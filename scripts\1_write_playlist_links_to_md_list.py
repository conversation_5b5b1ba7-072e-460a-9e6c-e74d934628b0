#!/usr/bin/env python3
"""
YouTube URL Extractor from HTML Clipboard
==========================================

This script extracts YouTube video links and titles from HTML content in the clipboard
and adds them to user-selected .md or .txt files. It supports section-based insertion
for markdown files and removes duplicates.

Features:
- Extracts YouTube video URLs and titles from HTML
- Supports both youtube.com/watch and youtu.be formats
- Allows browsing for target .md or .txt files
- Detects markdown headers and allows section selection
- Inserts URLs into specific sections or at end of file
- Removes duplicate URLs from the entire file
- Preserves existing URLs and file structure

Requirements:
- pyperclip: pip install pyperclip
- beautifulsoup4: pip install beautifulsoup4
- tkinter (usually comes with Python)

Author: Generated for YouTube Transcript Manager
"""

import os
import re
import sys
from pathlib import Path
from urllib.parse import parse_qs, urlparse
import tkinter as tk
from tkinter import filedialog, messagebox
from datetime import datetime, timedelta

try:
    import pyperclip
    from bs4 import BeautifulSoup
except ImportError as e:
    print(f"Missing required package: {e}")
    print("Please install required packages:")
    print("pip install pyperclip beautifulsoup4")
    sys.exit(1)


# Configuration (kept for backward compatibility, but not used in new file browsing mode)
HARDCODED_FOLDER_PATH = r"C:\Users\<USER>\Documents\HEW Notes\Video Transcripts MD"
INPUT_URL_FILENAME = "InputURL.md"


def extract_video_id_from_url(url):
    """Extract YouTube video ID from various URL formats."""
    # Handle different YouTube URL formats
    patterns = [
        r'(?:youtube\.com/watch\?v=|youtu\.be/)([a-zA-Z0-9_-]{11})',
        r'youtube\.com/embed/([a-zA-Z0-9_-]{11})',
        r'youtube\.com/v/([a-zA-Z0-9_-]{11})'
    ]
    
    for pattern in patterns:
        match = re.search(pattern, url)
        if match:
            return match.group(1)
    return None


def extract_date_from_playlist_video(playlist_video):
    """Extract date from playlist video renderer element."""
    current_date = datetime.now().strftime('%Y%m%d')
    
    if not playlist_video:
        return current_date
    
    # Look for date information in various elements
    date_elements = [
        playlist_video.find('span', {'class': lambda x: x and 'published' in str(x).lower()}),
        playlist_video.find('span', {'class': lambda x: x and 'metadata' in str(x).lower()}),
        playlist_video.find('span', {'aria-label': True}),
        playlist_video.find('div', {'id': 'metadata-line'}),
        playlist_video.find('ytd-video-meta-block'),
    ]
    
    # Also look for any span elements containing date-like text
    all_spans = playlist_video.find_all('span')
    date_elements.extend(all_spans)
    
    for element in date_elements:
        if element:
            # Check both text content and aria-label
            texts_to_check = [element.get_text(strip=True), element.get('aria-label', '')]
            
            for text in texts_to_check:
                if text:
                    parsed_date = parse_date_from_text(text)
                    if parsed_date:
                        return parsed_date
    
    return current_date


def parse_date_from_text(text):
    """Parse date from text using various patterns."""
    if not text:
        return None
        
    text = text.lower().strip()
    
    # Common date patterns
    patterns = [
        # "2 days ago", "1 week ago", "3 months ago", "1 year ago"
        (r'(\d+)\s+days?\s+ago', lambda m: days_ago_to_date(int(m.group(1)))),
        (r'(\d+)\s+weeks?\s+ago', lambda m: days_ago_to_date(int(m.group(1)) * 7)),
        (r'(\d+)\s+months?\s+ago', lambda m: months_ago_to_date(int(m.group(1)))),
        (r'(\d+)\s+years?\s+ago', lambda m: years_ago_to_date(int(m.group(1)))),
        # "Streamed 2 days ago", "Published 1 week ago"
        (r'(?:streamed|published)\s+(\d+)\s+days?\s+ago', lambda m: days_ago_to_date(int(m.group(1)))),
        (r'(?:streamed|published)\s+(\d+)\s+weeks?\s+ago', lambda m: days_ago_to_date(int(m.group(1)) * 7)),
        (r'(?:streamed|published)\s+(\d+)\s+months?\s+ago', lambda m: months_ago_to_date(int(m.group(1)))),
        (r'(?:streamed|published)\s+(\d+)\s+years?\s+ago', lambda m: years_ago_to_date(int(m.group(1)))),
        # Exact dates like "Jan 15, 2023", "2023-01-15"
        (r'(\w{3})\s+(\d{1,2}),\s+(\d{4})', lambda m: parse_month_day_year(m.group(1), m.group(2), m.group(3))),
        (r'(\d{4})-(\d{2})-(\d{2})', lambda m: f"{m.group(1)}{m.group(2)}{m.group(3)}")
    ]
    
    for pattern, converter in patterns:
        match = re.search(pattern, text)
        if match:
            try:
                return converter(match)
            except:
                continue
    
    return None


def days_ago_to_date(days):
    """Convert days ago to YYYYMMDD format."""
    date = datetime.now() - timedelta(days=days)
    return date.strftime('%Y%m%d')


def months_ago_to_date(months):
    """Convert months ago to YYYYMMDD format."""
    # Approximate months as 30 days
    date = datetime.now() - timedelta(days=months * 30)
    return date.strftime('%Y%m%d')


def years_ago_to_date(years):
    """Convert years ago to YYYYMMDD format."""
    # Approximate years as 365 days
    date = datetime.now() - timedelta(days=years * 365)
    return date.strftime('%Y%m%d')


def parse_month_day_year(month_str, day_str, year_str):
    """Parse month name, day, year to YYYYMMDD format."""
    month_map = {
        'jan': '01', 'feb': '02', 'mar': '03', 'apr': '04',
        'may': '05', 'jun': '06', 'jul': '07', 'aug': '08',
        'sep': '09', 'oct': '10', 'nov': '11', 'dec': '12'
    }
    
    month = month_map.get(month_str.lower()[:3])
    if month:
        day = day_str.zfill(2)
        return f"{year_str}{month}{day}"
    return None


def normalize_youtube_url(url):
    """Convert YouTube URL to standard format."""
    video_id = extract_video_id_from_url(url)
    if video_id:
        return f"https://www.youtube.com/watch?v={video_id}"
    return url


def extract_youtube_links_from_html(html_content):
    """Extract YouTube video links, titles, and dates from HTML content."""
    soup = BeautifulSoup(html_content, 'html.parser')
    video_data = []

    # Look specifically for playlist video renderers (the actual playlist items)
    playlist_videos = soup.find_all('ytd-playlist-video-renderer')

    for playlist_video in playlist_videos:
        # Find the video title link within the playlist video renderer
        title_link = playlist_video.find('a', {'id': 'video-title'})
        if title_link and title_link.get('href'):
            href = title_link.get('href')
            title = title_link.get_text(strip=True) or title_link.get('title', 'Untitled')

            # Convert relative URLs to absolute
            if href.startswith('/watch?'):
                full_url = f"https://www.youtube.com{href}"
            elif href.startswith('http'):
                full_url = href
            else:
                continue

            # Extract video ID and normalize URL
            video_id = extract_video_id_from_url(full_url)
            if video_id:
                normalized_url = normalize_youtube_url(full_url)
                
                # Extract date from the playlist video renderer
                date_str = extract_date_from_playlist_video(playlist_video)
                
                video_data.append({
                    'url': normalized_url,
                    'title': title,
                    'video_id': video_id,
                    'date': date_str
                })

    # If no playlist videos found, fall back to looking for div elements with id="meta"
    # but only from ytd-playlist-video-renderer containers
    if not video_data:
        meta_divs = soup.find_all('div', {'id': 'meta'})

        for meta_div in meta_divs:
            # Check if this meta div is within a playlist video renderer
            parent_playlist = meta_div.find_parent('ytd-playlist-video-renderer')
            if not parent_playlist:
                continue  # Skip if not in a playlist video renderer

            # Find the video title link within the meta div
            title_link = meta_div.find('a', {'id': 'video-title'})
            if title_link and title_link.get('href'):
                href = title_link.get('href')
                title = title_link.get_text(strip=True) or title_link.get('title', 'Untitled')

                # Convert relative URLs to absolute
                if href.startswith('/watch?'):
                    full_url = f"https://www.youtube.com{href}"
                elif href.startswith('http'):
                    full_url = href
                else:
                    continue

                # Extract video ID and normalize URL
                video_id = extract_video_id_from_url(full_url)
                if video_id:
                    normalized_url = normalize_youtube_url(full_url)
                    # Check if we already have this video
                    if not any(v['video_id'] == video_id for v in video_data):
                        # Extract date from the meta div's parent playlist video renderer
                        date_str = extract_date_from_playlist_video(parent_playlist)
                        
                        video_data.append({
                            'url': normalized_url,
                            'title': title,
                            'video_id': video_id,
                            'date': date_str
                        })

    # Sort video data by date (latest first)
    video_data.sort(key=lambda x: x['date'], reverse=True)
    return video_data


def get_target_file():
    """Get the target file path by browsing for .md or .txt files."""
    root = tk.Tk()
    root.withdraw()  # Hide the main window

    file_path = filedialog.askopenfilename(
        title="Select .md or .txt file to add URLs to",
        filetypes=[
            ("Markdown files", "*.md"),
            ("Text files", "*.txt"),
            ("All files", "*.*")
        ],
        initialdir=os.getcwd()
    )

    if not file_path:
        messagebox.showwarning("No File Selected", "No file was selected. Exiting.")
        return None

    return file_path


def detect_markdown_headers(file_path):
    """Detect markdown headers in the file and return a list of sections."""
    headers = []
    if os.path.exists(file_path):
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                lines = f.readlines()
                for i, line in enumerate(lines):
                    line = line.strip()
                    if line.startswith('#'):
                        # Count the number of '#' characters to determine header level
                        level = 0
                        for char in line:
                            if char == '#':
                                level += 1
                            else:
                                break
                        header_text = line[level:].strip()
                        headers.append({
                            'level': level,
                            'text': header_text,
                            'line_number': i + 1
                        })
        except Exception as e:
            print(f"Warning: Could not read file {file_path}: {e}")
    
    return headers


def read_existing_urls(file_path):
    """Read existing URLs from the target file."""
    existing_urls = set()
    if os.path.exists(file_path):
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                for line in f:
                    line = line.strip()
                    if line and not line.startswith('#'):  # Skip comments and empty lines
                        # Extract URL if line contains external markdown link format (with or without date prefix)
                        if line.startswith('- [') and '](' in line and line.endswith(')'):
                            url = line.split('](')[1][:-1]
                        elif line.startswith('- ') and ' - [' in line and '](' in line and line.endswith(')'):
                            # Handle format: "- YYYYMMDD - [Title](URL)"
                            url = line.split('](')[1][:-1]
                        elif line.startswith('[') and '](' in line and line.endswith(')'):
                            url = line.split('](')[1][:-1]
                        else:
                            url = line

                        # Normalize the URL
                        normalized = normalize_youtube_url(url)
                        existing_urls.add(normalized)
        except Exception as e:
            print(f"Warning: Could not read existing file {file_path}: {e}")

    return existing_urls


def select_section(headers):
    """Let user select which section to add URLs to."""
    if not headers:
        return None
    
    print("\nFound markdown headers in the file:")
    print("0. Add to end of file (no specific section)")
    
    for i, header in enumerate(headers):
        indent = "  " * (header['level'] - 1)
        print(f"{i + 1}. {indent}{header['text']}")
    
    while True:
        try:
            choice = input(f"\nSelect section (0-{len(headers)}): ").strip()
            choice_num = int(choice)
            if choice_num == 0:
                return None  # Add to end of file
            elif 1 <= choice_num <= len(headers):
                return headers[choice_num - 1]
            else:
                print(f"Please enter a number between 0 and {len(headers)}")
        except ValueError:
            print("Please enter a valid number")


def find_section_end(file_path, selected_header):
    """Find the line number where the selected section ends."""
    if not selected_header:
        return None  # Will append to end of file
    
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            lines = f.readlines()
        
        # Find the next header at the same or higher level
        for i in range(selected_header['line_number'], len(lines)):
            line = lines[i].strip()
            if line.startswith('#'):
                # Count header level
                level = 0
                for char in line:
                    if char == '#':
                        level += 1
                    else:
                        break
                
                # If we find a header at the same or higher level (lower number), this is where the section ends
                if level <= selected_header['level']:
                    return i
        
        # If no next header found, section goes to end of file
        return len(lines)
    
    except Exception as e:
        print(f"Error reading file: {e}")
        return None


def write_urls_to_file(file_path, video_data, existing_urls, selected_header=None):
    """Write URLs to the target file, optionally inserting into a specific section."""
    new_urls = []
    duplicate_count = 0

    # Filter out duplicates and skip "Untitled" videos (likely Shorts)
    for video in video_data:
        if video['url'] not in existing_urls:
            # Skip videos with generic "Untitled" titles (likely Shorts or hidden videos)
            if video['title'].strip().lower() in ['untitled', '']:
                print(f"Skipping untitled video: {video['url']}")
                continue
            new_urls.append(video)
        else:
            duplicate_count += 1

    if not new_urls:
        print(f"No new URLs to add. Found {duplicate_count} duplicates.")
        return 0

    try:
        # If no specific section selected, append to end of file
        if not selected_header:
            with open(file_path, 'a', encoding='utf-8') as f:
                if os.path.getsize(file_path) > 0:
                    f.write('\n')  # Add newline if file is not empty

                for video in new_urls:
                    # Write in template format: - YYYYMMDD - Video ID - [Video Title](Video URL)
                    f.write(f"- {video['date']} - {video['video_id']} - [{video['title']}]({video['url']})\n")
        else:
            # Insert URLs into the selected section
            with open(file_path, 'r', encoding='utf-8') as f:
                lines = f.readlines()
            
            # Find where to insert the URLs
            section_end = find_section_end(file_path, selected_header)
            if section_end is None:
                section_end = len(lines)
            
            # Prepare the new URLs to insert
            new_lines = []
            for video in new_urls:
                new_lines.append(f"- {video['date']} - {video['video_id']} - [{video['title']}]({video['url']})\n")
            
            # Add a blank line before the URLs if the previous line is not empty
            if section_end > 0 and lines[section_end - 1].strip():
                new_lines.insert(0, '\n')
            
            # Add a blank line after the URLs if the next line is not empty
            if section_end < len(lines) and lines[section_end].strip():
                new_lines.append('\n')
            
            # Insert the new lines
            lines[section_end:section_end] = new_lines
            
            # Write the modified content back to the file
            with open(file_path, 'w', encoding='utf-8') as f:
                f.writelines(lines)

        print(f"Successfully added {len(new_urls)} new URLs to {file_path}")
        if selected_header:
            print(f"URLs added to section: {selected_header['text']}")
        if duplicate_count > 0:
            print(f"Skipped {duplicate_count} duplicate URLs")
        return len(new_urls)

    except Exception as e:
        print(f"Error writing to file {file_path}: {e}")
        return 0


def remove_duplicates_from_file(file_path):
    """Remove duplicate URLs from the file and return the count of duplicates removed."""
    if not os.path.exists(file_path):
        return 0
    
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            lines = f.readlines()
        
        seen_urls = set()
        new_lines = []
        duplicates_removed = 0
        
        for line in lines:
            original_line = line
            line_stripped = line.strip()
            
            # Skip empty lines and headers
            if not line_stripped or line_stripped.startswith('#'):
                new_lines.append(original_line)
                continue
            
            # Extract URL from line
            url = None
            if line_stripped.startswith('- [') and '](' in line_stripped and line_stripped.endswith(')'):
                url = line_stripped.split('](')[1][:-1]
            elif line_stripped.startswith('- ') and ' - [' in line_stripped and '](' in line_stripped and line_stripped.endswith(')'):
                # Handle format: "- YYYYMMDD - [Title](URL)"
                url = line_stripped.split('](')[1][:-1]
            elif line_stripped.startswith('[') and '](' in line_stripped and line_stripped.endswith(')'):
                url = line_stripped.split('](')[1][:-1]
            elif line_stripped.startswith('http'):
                url = line_stripped
            
            if url:
                normalized_url = normalize_youtube_url(url)
                if normalized_url not in seen_urls:
                    seen_urls.add(normalized_url)
                    new_lines.append(original_line)
                else:
                    duplicates_removed += 1
            else:
                # Keep non-URL lines as is
                new_lines.append(original_line)
        
        # Write back to file if duplicates were found
        if duplicates_removed > 0:
            with open(file_path, 'w', encoding='utf-8') as f:
                f.writelines(new_lines)
        
        return duplicates_removed
        
    except Exception as e:
        print(f"Error removing duplicates from {file_path}: {e}")
        return 0


def main():
    """Main function to extract YouTube URLs from clipboard and save to file."""
    print("YouTube URL Extractor from HTML Clipboard")
    print("=" * 50)
    
    # Get HTML content from clipboard
    try:
        html_content = pyperclip.paste()
        if not html_content:
            print("Error: Clipboard is empty")
            return
        
        if '<html' not in html_content.lower() and '<div' not in html_content.lower():
            print("Warning: Clipboard content doesn't appear to be HTML")
            print("First 200 characters:")
            print(html_content[:200])
            proceed = input("\nProceed anyway? (y/n): ").lower().strip()
            if proceed != 'y':
                return
                
    except Exception as e:
        print(f"Error reading from clipboard: {e}")
        return
    
    # Extract YouTube links
    print("Extracting YouTube links from HTML...")
    video_data = extract_youtube_links_from_html(html_content)
    
    if not video_data:
        print("No YouTube video links found in the HTML content")
        return
    
    print(f"Found {len(video_data)} YouTube video(s):")
    for i, video in enumerate(video_data, 1):
        print(f"  {i}. {video['title'][:60]}{'...' if len(video['title']) > 60 else ''}")
        print(f"     {video['url']}")
    
    # Get target file
    file_path = get_target_file()
    if not file_path:
        return
    
    # Check for markdown headers and let user select section
    headers = detect_markdown_headers(file_path)
    selected_header = None
    if headers:
        selected_header = select_section(headers)
    
    # Read existing URLs
    existing_urls = read_existing_urls(file_path)
    print(f"\nFound {len(existing_urls)} existing URLs in {os.path.basename(file_path)}")
    
    # Write new URLs to file
    added_count = write_urls_to_file(file_path, video_data, existing_urls, selected_header)
    
    # Remove duplicates from the entire file
    duplicates_removed = remove_duplicates_from_file(file_path)
    
    if added_count > 0:
        print(f"\n✅ Successfully processed! Added {added_count} new URLs to:")
        print(f"   {file_path}")
    else:
        print(f"\n⚠️  No new URLs were added to {file_path}")
    
    if duplicates_removed > 0:
        print(f"🔄 Removed {duplicates_removed} duplicate URLs from the file")
    
    input("\nPress Enter to exit...")


if __name__ == "__main__":
    main()
