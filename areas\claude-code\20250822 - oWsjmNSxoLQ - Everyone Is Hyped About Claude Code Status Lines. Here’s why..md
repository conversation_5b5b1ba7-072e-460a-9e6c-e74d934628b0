---
title: Everyone Is Hyped About Claude Code Status Lines. Here’s why.
artist: AI Oriented Dev
date: 2025-08-22
url: https://www.youtube.com/watch?v=oWsjmNSxoLQ
---

(t: 0) Hey everyone, welcome back to another episode on how to get the most out of your Cloud Code workflows. One of the most interesting features that has been released by Cloud Code themselves (t: 10) has been the status line feature. So initially when I saw it, I didn't think too much about it, but over the past week, I've actually seen some really cool applications of how this status line can go beyond just providing information to actually enabling you to have a much more (t: 20) productive workflow as well. So if you actually take a look at my status line over here, you can see that I have not only some static information such as my current working directory and my (t: 30) current Git branch, but I also show things like the current model that I have, the version of Cloud Code I have, as well as which output style I'm using. If you saw my episode last week, (t: 40) this is one of the new features that has been released by Anthropic that's gaining a lot of attention, which I will be covering in a future video. But you can see I actually have recently (t: 50) added how much context you have remaining. So for those of you who are using the Gemini CLI, this would be very familiar to you. It's very useful because it helps you to prevent context fraud. And if you saw it last week, I actually have the time remaining, (t: 60) the progress bar in the window, as well as my usage in dollars and tokens. I've made my own status line completely open source. So stay tuned to the end where I show you how you can get the (t: 70) entire status line that I'm using over here with a single command. Now, a lot of people have been sharing all of their very cool looking status lines, and there's been some really crazy stuff (t: 80) out there. For example, somebody's created a <PERSON>aguchi, which lives within your terminal. However, I thought in today's tutorial, rather than utilizing what somebody else has provided, I want to show you how you can build a status line from scratch. My hope is that with (t: 90) that, you will go ahead and create some really cool looking and customized status lines tailored to your needs. Now, if you want to get hold of all the resources in today's tutorial, check out the (t: 100) link in my description below to join my AI-oriented Insiders Club. In addition to the materials from today's tutorial, I also include things from my other video tutorials, including full write-ups of (t: 110) the lesson plans and access to private GitHub repositories containing additional bonus material. And if you'd like to take things a step further, you may want to check out my Cloud Code Builder Pack. It is an all-in-one (t: 120) toolkit that contains working examples, source code, configurations that will help level up your Cloud Code experience. It's already helped some users get up to speed with using Cloud Code and (t: 130) be enabled to build applications a lot faster. So if you're interested, get it in the link in the description below. All right, without further ado, let's get started. The first thing you want (t: 140) to do is the status line. This is the new command from Cloud Code. It'll kick off a native sub agent, so I'm going to say id.cube, and it's going to look at your shell configuration and generate the (t: 150) default status line for you. Okay, so when this is completed, if you go to your home directory and the cloud folder within that, you will see that it's now created an entry for your status (t: 160) line over here. I'm actually just going to copy this entire settings file, delete this from the global file, because I want everything to be within my project for this demonstration. So if you see, (t: 170) if I open cloud right now, there should not be any status line showing below. Within my cloud folder, I'm just going to open my settings.json. I'm just going to open my settings.json, and I'm just going to open my settings.json. and paste everything that was in there. I'm just going to get rid of the schema as well as the (t: 180) feedback survey state and save it. Now you can see if I close this and run plot again, it's going to show me the current working directory as well as the model. This is just the very default thing (t: 190) at the end of running the status line command. Now next thing to note is that this is not a good way of doing things because putting code, especially bash scripts within a JSON file (t: 200) is terrible. Instead what we're going to do is that we're going to create a file called status line dot sh okay and we're actually going to put everything that was in this command over (t: 210) here and we're going to grab it, cut it and then we're going to put it within this file over here (t: 220) and just save it okay and now we're going to use plot code to help fix this for us. Now that we've removed it over here we can just do dot slash plot slash status line dot sh. Now before this will (t: 230) work there are two things you need to do. First of all you need to do is to create a status line dot sh and then you need to add a header editor to this to run this as a bash file. So let's just (t: 240) ask plot to fix this for us. So look at status line dot sh and make it executable and fix the (t: 250) formatting of per scripts. Okay so we're just going to let plot fix this up for me so that we (t: 260) can actually get to a point where we are running a status line dot sh. Now let's just go ahead and create a script that does the exact same functionality as we saw before. Now you can see the changes have been made. It's added the shebang editor and the code within it as well. So if we run (t: 270) plot again we should be able to see that the status line is the same as we saw before. All right so now we know that we are no longer shoving a script within here. Now we will be running a script (t: 280) that we have defined over here so that we can isolate and do the coding within this file without (t: 290) in the configuration file itself, which is honestly not the best idea. And if you hop over to how cloud code themselves has mentioned it, (t: 300) they said that it's better to run a bash command as opposed to putting it in there. And you can see the difference here is that we're storing it within our project folder (t: 310) rather than the whole folder. You might as well take the opportunity to follow the guidance and do it. Great. I would heavily suggest that before you do anything, you will want to grab the location (t: 320) of these docs and study and familiarize yourself with the docs on status lines. (t: 330) Let this run so that it understands how to make use of the status line configurations, how to configure things, knowing the input format, what to expect and the best practices related to (t: 340) writing status lines. Now I've actually created an extended version of this cloud.md file that is tailored specifically for how to make use of status lines, how to deal with input formats and (t: 350) how to do some of the classifying. So I'm going to go ahead and create a folder called cloud.md. And I'm going to create a folder called cloud.md. And I'm going to create a folder called cloud.md. So by having this cloud.md in your project folder, you will have a much better chance (t: 360) of generating status lines that work, and you'll be able to make use of the full set of features that you see. If you're interested in getting hold of this cloud.md file, you can access it in the builder pack in the link in the description below. So we're not going to really worry too much (t: 370) about this because my cloud.md file already has all of the information that it needs to know how to build with status lines. All right. Now the way this works is that every time this (t: 380) script is run, Cloud Code will pass in an entire input of information, which you can then access to choose what you want to display. So for example, you can see here, you have things like (t: 390) the model that you're using, which is already displaying, but you can have other things such as the version of Cloud Code you're running, the output style, which is one of the new features (t: 400) they've done. And they've also recently added some costing so that you can display these into your terminal as well. So the first thing that we're going to do is add some debug blogging. So we can see these requests coming in. I'm going to use the following. (t: 410) Prompt. Add a debug flag to status line dot sh that logs in input JSON with timestamps. (t: 420) This is a useful way of making sure that whenever you are working with status lines, you can see what's coming in because sometimes you may have some issues related to how the code is written. (t: 430) And this will help you immensely when you're writing your own status lines. When that's done, what you want to do is go into your settings file and just add dash dash debug. And we run (t: 440) Claude one more time. You can see the moment that this status line has come through, there has been a log file. So now you can actually see a working example and get all the information about the model, the display name, as well as everything related to the cost, the workspace and the (t: 450) different versions. Now that you know that your login is working fine, this will make your life a lot easier as you go along to build the other features. So now let's actually look at this, (t: 460) right? Let's actually just spiff this up a little bit. All right, I don't really like how it's showing the username and hostname. So let's actually fix that up first. (t: 470) Remove the user and hostname from the status line to make it cleaner. The second thing that I want to do is that I say for the model, highlight this background with a nice color and a hat and emoji. (t: 480) The third thing we want to do is add the version number and output style (t: 490) separated by bars and with emojis as well. Okay, so what this is going to do now is that it's actually going to show the user's name and the status line. So let's say I want to add a user at (t: 500) the top of my model, so I'm going to say here, my name is a lot nicer. I can see the user's name and the status line, and I'm going to be able to make use of the information it gets by the inputs. And this is already powerful enough to get you to (t: 510) a state situation where your status line looks a lot nicer. As you can see right here, it's immediately available. You can see now it has a highlighted background for my model, and it's (t: 520) added in the version of Cloud Code I'm running as well as the output style that I'm using here. And just just to show you how the output style works. If I change this output style to say, a learning here i'm going to switch back to the default one because that's what we're going to be using as we (t: 530) go along now let's take this one step further because the status line is actually a bash file so technically you can run any bash command within it so let's actually do the following (t: 540) this means that why not make use of the git commands to help us to know which branch we are on let's go ahead and ask clock to do this for us now add the current branch with an appropriate (t: 550) emoji and let's see what it does here all right you can see now it's added in the current branch that i am on in my git repository take a look at what it's doing here you can see that it's (t: 560) running a git command which shows the current branch and then it will do a bunch of other things to showcase that this is here so this showcases the power of the command line because (t: 570) now you can run any custom command that's available on your system and use it to display something within your status line as well let's take this a step further it's really good to get (t: 580) all of this kind of static information that doesn't really change right you wouldn't change out your current working directory you wouldn't change a branch that often but some things change (t: 590) very frequently for example you're always firing off several prompts into clock every now and then and if you are like me you actually have several clock instances running on different projects (t: 600) sometimes as you flip between them it's hard to remember what was the prompt you sent in so it might be really useful to add the prompt into the status line so you know what was the last thing (t: 610) you said if you actually look at the inputs to the status line you don't get anything related to your prompt but this is where the transcript path as well as your session id comes into play what you can do is actually ask the script to go and directly look (t: 620) into the transcript path and then identify what was the prompts that was made by you so let's go ahead and do this right now i'm going to say add a second line showing the last user prompt extract (t: 630) from the transcript file at whatever this is find the last user message display with this symbol and (t: 640) truncate to 300 characters if longer okay let's just give this a shot now i mentioned this a little bit earlier but if you go ahead and try to just tell quad to do this without first giving it the docs or using this updated plot.md file that i (t: 650) have you may find that it actually struggles to understand how to gather things out of these transcript files if you would like to be able to build your own status lines with fewer errors (t: 660) and a higher chance of getting it right you will want to get hold of this expanded plot.md file that i've made available the link to it is in the description below all right if you look at (t: 670) the status line right now you can see it's showing the last prompt that i said so this is actually very useful because if you open another terminal and you're doing something else with plot on the (t: 680) same project on a different project and you're switching around you can be saying like say hello back to me all right you can actually see and remember like oh this was actually the last (t: 690) prompt that i sent to claude this is something that i learned from another youtuber called (t: 700) indydeb then he did something like this and i saw how useful it was to have okay showing your last problem is quite good but how about we take this a step further what if we could provide a summary of everything that has been happening so far so that when you (t: 710) come back you not only remember what prompt you send but you actually remember the entire context of what is going on in the session you had previously now you might be thinking how do (t: 720) i do that wouldn't i need some kind of ai to do this to be able to do this summary do i need to connect to some api service to do this well the answer is no because you actually have this (t: 730) on-demand one-shot powerful tool that can provide summary in your terminal itself with cloud code the thing is actually called the cloud code sdk you can use the cloud command and it will be able to (t: 740) perform actions and give you a response in one line so if i'm going to open a terminal here you can do quad and then minus p for prompt and then say (t: 750) tell me a joke and what you will see here is that you will make use the cloud sdk to perform this action and return the result into it you means that you can pass in a prompt (t: 760) to say summarize the following transcript and then pass in the path to it it should be able to launch cloud code then take in the transcript and then look into it and then provide a summary as (t: 770) well and i just wanted to give credit to ian nuttle he was the one that first identified that you could actually use this in this manner so if you aren't already following ian i suggest you do (t: 780) so because he posts great tips and tricks about using cloud code and other agentic tools all the time so let me show you how we're going to do it over here okay if you're not already using the (t: 791) md file then you don't need to do this because it's already gotten that context as part of it okay (t: 800) so now that he understands that i'm going to say conversation summaries using quad minus p and i say extract the last five user assistant exchanges from the transcript so that it doesn't have to (t: 810) read the entire thing and so there's a little bit more optimized and i actually say to use the haiku model so that it's a little bit faster you don't end up wasting your precious opus or (t: 820) sonnet limits Now this is completed and I've been having a conversation with Claude as I debug an issue that I found. Now below the last prompt that I sent, it actually talks a little bit about the (t: 830) context about the conversation that we've been having, which is about status line generating the AI conversation summary with a timeout fallback. So this is just a nice way to demonstrate (t: 840) how you can actually use the Claude code SDK as an intelligent way of performing actions that would otherwise require you to write some very complex code or rely on the API itself. Hopefully (t: 850) this has been a useful way of showcasing to you how this is done. I think you have all the tools and knowledge to go and create really cool looking status lines. Now if after all of that, you just (t: 860) want to get a really nice looking status line, I actually have created and open sourced a status line generator on GitHub and you can actually make use of it very simply by running just this (t: 870) single command that you have over here. So if you see, I'm just going to copy and paste this and I'm going to do npx with this latest version of what I have. They will ask which features I want, (t: 880) I've turned on all of these features by default. I'm going to say yes to modern color schemes. I'm going to turn off debug logging so it doesn't spam my console. If I run Claude, you will get a status (t: 890) line that looks something like this. All right, we should take a look at it. It actually has a few more features that I haven't covered today. It shows you things like the context remaining so that you can actually get ahead of doing either a compact or saving your work. It shows you the (t: 900) progress and the time till the end of your window. And then finally, it shows you a couple more stats (t: 910) related to your usage. It makes it a little bit easier to use. So if you want to do a little bit of a more complex work, you can do that by clicking on the icon on the top right corner of your screen. And then if you want to do a little bit more complex work, you can do that by clicking on the icon on the top right corner of your screen. So if you want to get hold of this status line that has all of this information, you can run that single command that showed you and like I mentioned, it is completely open source. I actually already had a pull request that was made just yesterday, (t: 920) which has been merged in. So definitely contributions are welcome and any feedback is welcome as well. Okay, so that takes us to the end of today's tutorial. It actually provides a (t: 930) lot of opportunities, especially when you start to tie it in together with other features of Claude, such as the hooks, the ClaudeCore SDK, as well as utilizing the Cloud Cloud Cloud Cloud Cloud (t: 940) integrated built-in Cloud Cloud Cloud Cloud Cloud Cloud Cloud Cloud Cloud Cloud Cloud Cloud Cloud Cloud Cloud Cloud Cloud Cloud Cloud Cloud Cloud Cloud Cloud Cloud Cloud Cloud Cloud Cloud Cloud Cloud Cloud Cloud Cloud Cloud Cloud Cloud Cloud Cloud Cloud Cloud Cloud Cloud Cloud Cloud Cloud Cloud to run in the bash terminal itself. Hopefully with today's tutorial, you have all the tools and knowledge to build some great and fun looking status lines. Don't forget to give it a like, subscribe, (t: 950) turn on that notification bell so that you always be the first to know whenever new content like this drops. If you haven't already seen my last video, while I went through a whole bunch of (t: 960) other new features that cloud code has released in the past couple of weeks, from the community, as well as anthropic itself, including how you can start to access cloud code (t: 970) in your machine have a look at my beginning. You may want to download these and download this mobile phone you definitely want to check out this video right over here alright so till next time see

