import os
import tkinter as tk
from tkinter import filedialog

def create_context_list():
    # Create a simple GUI to select folder
    root = tk.Tk()
    root.withdraw()  # Hide the main window
    
    # Ask user to select a folder
    folder_path = filedialog.askdirectory(title="Select folder to create context list for")
    
    if not folder_path:
        print("No folder selected. Exiting.")
        return
    
    # Get all files in the selected folder
    files = []
    for item in os.listdir(folder_path):
        item_path = os.path.join(folder_path, item)
        if os.path.isfile(item_path):
            files.append(item)
    
    # Create context list with @ prefix and folder structure
    folder_name = os.path.basename(folder_path)
    context_list = []
    
    for file in sorted(files):
        context_path = f"@{folder_name}\\{file}"
        context_list.append(context_path)
    
    # Write to context-list.txt in the same folder
    output_file = os.path.join(folder_path, "context-list.txt")
    
    with open(output_file, 'w', encoding='utf-8') as f:
        for item in context_list:
            f.write(item + '\n')
    
    print(f"Context list created successfully!")
    print(f"Output file: {output_file}")
    print(f"Found {len(files)} files")

if __name__ == "__main__":
    create_context_list()