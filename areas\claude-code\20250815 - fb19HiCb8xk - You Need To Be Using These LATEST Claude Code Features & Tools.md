---
title: You Need To Be Using These LATEST Claude Code Features & Tools
artist: AI Oriented Dev
date: 2025-08-15
url: https://www.youtube.com/watch?v=fb19HiCb8xk
---

(t: 0) Hey everyone, welcome back to another episode on how to get the best out of Cloud Code. Now it's only been a week since my last video, but between that time, Authentic have already pushed out a ton of new features into Cloud Code. (t: 10) Now I don't know about you, but keeping up to date with these releases is hard enough in itself. But then, sometimes when you look at the release notes, while they state what the new feature is, it's not entirely clear how to use it properly. (t: 20) So in today's video, I'll be covering all the major new features that have been released, and I'll be showing you step-by-step how to best make use of them based on my own experiments. (t: 30) That said, it's not only the Anthropic team that have been hard at work. The entire Cloud Code community have been releasing some really cool new tools. For example, check out this one by <PERSON>, or otherwise known as <PERSON> on Twitter. (t: 40) He has actually managed to get a mobile version of Cloud Code working. You can actually access Cloud Code right on your phone, (t: 50) keeping track of every single conversation you're having across the different sessions, as well as the status of any tasks that you have running. This is just one of the many tools that's coming out of the community every single day. (t: 60) So in today's episode, I'll not only be covering the official Anthropic features, I'll be covering some of the really exciting projects that are coming out of the community as well. And definitely stick to them, because one of them is actually also developed by yours truly. (t: 70) Now you might be wondering, how do I keep up with all the news that's going on? And yes, you can definitely be on Twitter, but there's a lot of stuff to filter through. So I recently built a Telegram bot using N8n. (t: 80) It automatically watches for updates on the Cloud Code package site, and then it just sets the change log and then summarizes it and sends it straight into my Telegram messages. (t: 90) I found it's been extremely useful for me to be able to stay on top of what's been coming out. And this is available in my AI-oriented Insiders Club. It's completely free to join, and within it, you'll be able to have instant access to all of the notes that I've written up (t: 100) for every single video that I've released. I've also made this entire N8n workflow available for free as part of my Insiders Club. (t: 110) And if you want to take this a step further, I've created an all-in-one Cloud Code Builder Pack. This is a toolkit that contains everything you need to know about Cloud Code. This is a toolkit that contains everything you need to know about Cloud Code. This is a toolkit that contains everything you need to know about Cloud Code. This is a toolkit that contains everything you need to know about Cloud Code. Including the source code, write-ups, configurations from all the videos in this series, (t: 120) as well as bonus material that isn't included elsewhere. This will help you take your Cloud Code experience to an even greater level. The link to that Builder Pack is also in the description below, so definitely check it out. (t: 130) Now without further ado, let's get started. All right. So the first tool that we'll be covering is something called CC Usage. CC Usage is a really useful tool that gives you a good overview of how much you're using Cloud Code for. (t: 140) There are many different commands that you can use in it with your Cloud Code. There are many different commands that you can use in it with your Cloud Code. So you have CC Usage Daily, which just shows you how much you're using per day. (t: 150) So it's roughly 85 cents today, and I used about $12.20 yesterday using both Opus and Sonic. And you can actually do other things such as CC Usage Monthly. (t: 160) So you get a monthly breakdown. You can use it for different sessions that you have within a day. But I find the most useful one is CC Usage Blocks. Right now, Cloud Code uses five hour blocks in order to rate limit your usage. (t: 170) And it's going to move to a weekly block at the end of this month. But the main thing here is that because everything goes in five hour blocks, it's actually much more informative to know how much you're using within each of these blocks that you have. (t: 180) You can actually see how many tokens you've used, how much it costs, the amount of tokens you have remaining, or the amount of time left within the block. I find the most useful tool is to do CC Usage Blocks and then do Live. (t: 190) This will give you a live second by second updated view of your token usage. It shows you what time your session started, what time it's going to end, (t: 200) how much you've actually used within this block session, and how much you have remaining. It will look at your projected usage based on how much compute you're using now and tell you whether or not you should slow down a little bit because you might exit the limit. (t: 210) I usually like to open it up in a second window over here. Then I do CC Usage Blocks and then Live. You can see this will continue running while I also continue doing different things on the left. (t: 220) This is very useful for you to kind of regulate how you're using things and to also keep an eye out so that you don't get unexpectedly rate limited when you need Cloud Code the most. (t: 230) All right. So for the next thing, we're going to cover is actually one of the new features that has been released by Anthropic within Cloud Code. This was released in version 1.0.71. (t: 240) Now you have this custom status line. This is what it looks like on my machines here. I've customized it in the following way where I actually have my current working directory, (t: 250) which branch I'm on, which model I'm using, and it also shows me a progress bar of how long I have left before my block ends and what time it ends at. (t: 260) It also shows me my usage both in dollars as well as the number of tokens and it shows me the burn rate. Which is how much I'm actually using. So let me show you how you get started with using this. So the way that you do it is very simple. (t: 270) The first thing you can do is just to run status line and it'll give you a very default status line. You just have to run this command right here. All right. So I've just started a fresh instance of Cloud. You can see I have no status line over here. (t: 280) So what I'm going to do is type status line and this time it's actually going to utilize Cloud's inbuilt tool to create a default status line for you. This is a very good starting point so that you have something to work with. (t: 290) You'll try to look at any of your examples. You'll try to create existing configurations within bash or your terminal. So that's your bash RC or your ZSH RC. (t: 300) And then you'll try to create a default status line for you that matches your style. This is the first thing you want to do to get a status line working and then start to customize it yourself. All right. So you can see it says that it's successfully configured and it shows the directory name and the model name. (t: 310) And it matches whatever the terminal theme is. Let's give it a role. Control C out and I'm just going to run Cloud again and put up my session. (t: 320) And you can see here. Cloud Code Mastery, which is the name of my workspace. And then it shows me the model. If you go into your settings. Jason, you can see that it's now added in a status line configuration. (t: 330) It says command and then it's put in the actual command that it needs to run within the status line. Now, let me show you two ways to make this a little bit better. What you want to do is actually just replace this with a script. (t: 340) So you don't end up putting code within this JSON file over here. I like to call it dot plot slash status line dot sh. (t: 350) And then what we're going to do here is create a new file. And call it status line dot sh. So once you've created the status line dot sh script, you want to add this at the top of the header. (t: 360) What I did was I took this entire command and pasted it below. And then all I did was to say, fix the formatting in status line. (t: 370) So it's actually formatted it correctly. And if I restart it now, you can see that it's showing me as it was earlier. The problem is that this isn't actually that informative. So now I'm going to show you two ways that you can actually make this a little bit better. (t: 380) The first way is. You can have use CC usage. So if you recall, we were using CC usage to show us this live token usage monitor. One thing that would be helpful to know is that use CC usage has something called a status line command. (t: 390) All I'm going to do is that I'm just going to put this and replace this with CC usage status line. All right. So there you go. (t: 400) CC usage gives you a really cool looking status line that already shows you what model you're using. And how much you use for the session. How much you use today. How much you use for the block. How much trend you have left. (t: 410) As well as your burn rate. All right. So you want to make it look a lot more like mine with a lot more information. I have actually created an open source. The tool to get your status line to look like this is completely open source on GitHub. (t: 420) And the way that you install it is just a single command line. Click copy. Open a new terminal and paste it in like this. And after that, it will say, what would you like to display in your status line? (t: 430) You can actually press A to toggle all and press A again to disable the ones that you do not want. And then you can select whichever ones that you want. I'm just going to turn on everything and then press enter and then say, yes, (t: 440) I want colors and emojis. It is going to give you a preview of what your status line will look like. And then it's going to actually generate a status line for you right over here. (t: 450) All right. And I've launched for again. This is a full blown status line, including the nicer looking block representation, the progress bar and the different burn rates for you. Let me know if you find any issues about, or if you have any feature requests and I'll be happy to try to improve it. (t: 460) So hopefully this has helped to make your cloud code terminal look a lot cooler. All right. (t: 470) This next cloud code tool is going to blow your mind. This is called cloud code templates. Do not be fooled by the name because this is more than just templates for cloud. (t: 480) This is actually a full blown toolkit. It has so much functionality. It does have templates for agents that you can use different commands, different settings, hooks, mtp servers, and many other things. It's fully open source and it actually has several other cool things such as an analytics dashboard, (t: 490) as well as what I'll show you a mobile chat interface. And yes, this means that you can. (t: 500) Actually monitor cloud code from your mobile device. Let me show you a few things. As you can see, I have a bunch of agents here. I'm going to install one new agent that I can make use of. (t: 510) All right. So let me just pick this one for instance. This is the one I want. All right. So I'm just going to run this. And you can see it's actually installed the hackathon agent for me right here. (t: 520) The same thing applies to these commands over here. All right. So if I just say I want to have a changelog command, I'm just going to copy the command and then paste it in. And there we go. (t: 530) You can see I have now a add changelog command over here. And then if I launch cloud, I can see that I have an add changelog to this project. I run it. (t: 540) You will see it needs to add a changelog to my project. As you can see here, it actually does quite a bunch of things. It looks through my version control history, and then it actually then tries to create a good looking changelog based on that. (t: 550) So while this is running, I just want to just show you, you can do the same thing for your different settings, for your different hooks, for your different MCP servers. (t: 560) It's a very easy way to get full of different tools, commands, agents, settings, and hooks very easily into your project without having to manually configure things around. And all right, so you can see here, it's actually created a changelog for me, (t: 570) which I'm going to preview here so you can see it's done it in a really nice way. It talks about my insider's landing page. I'm actually revamping the insider's blood. So there's a lot more accessible and that I have a little bit more control over it. (t: 580) So stay tuned for news on that launch. It's definitely picked up everything just based on my commit history and the workspace, which is great. It actually also created a customs command here called changelog update, (t: 590) which I will use whenever I make more changes to this repository. But remember, the templates are just one aspect of this tool provide. (t: 600) The first thing I want to show you is the analytics dashboard. All right, and this is the analytics dashboard that they've launched. And this is really cool because it shows you the usage. (t: 610) This one shows you everything like the number of conversations you have, total number of sessions. It shows you really nice statistics around your token analytics, including which tools get run, which tools are used the most and your daily productivity trends. (t: 620) It's just really fun to look at. It allows you to get a very high level view of how your usage of cloud is. Now, the second thing I want to show you is something called the chat user interface. (t: 630) Now with this one, it gives you a mobile friendly interface of all the different chats that you have across your cloud sessions. If you open up this one over here, you can see that we were using the changelog command earlier. (t: 640) You can see that this is being reflected here in this chat window. Now, if you have a mobile friendly interface, it gives you a settingти screenado where the celebrity caregivers could filter their messages. (t: 650) So if you're actually using it for your moi account, I've added this transition message and that's really the info that I was using earlier. And it's also giving you the melhores ofuggle. And it also gives you be the English American for what you didn't expect. (t: 660) And it's結束 иalnya. And after all of that, you can also use cloud peoples to come up with the chat work responsibilities. Now that I'm using this chat mode to conveyed, (t: 670) you got all these links to серд utu. So, I'm just going to open up this chat and let it be to chat processes. This is where I got a short chat. Let's take a little glance at this. There you go. It's really cool. here we go. change lock setup complete and I successfully set up a comprehensive change lock system you can see (t: 680) everything that I have on my phone is exactly the same thing on screen this gives you a useful way (t: 690) to monitor your cloud code runs while you're using your phone I can't wait to see whether or not in the future you're going to be able to actually also send in commands as well because that will (t: 700) really make this extremely useful when you're not at your desk all right so then it's cloud code templates you should definitely give this repository a star and follow Daniel on twitter (t: 710) because he posts not only things related to cloud code templates but many other cool tips and tricks related to using cloud code as well all right this other feature is called background commands this says use ctrl b to run any bash command in the background so cloud can keep working but the (t: 720) thing here is that it's not the most intuitive to know how to actually use this and so I'm going to show you how to use this right now when you are in your web project you run np and run dev and this (t: 730) is usually where any errors or things that might be causing you to run npm run dev and this is where you're going to be causing problems with your website will get posted out here so I'm just (t: 740) going to open my website over here and as you can see this is a revamp of the insiders cloud that I'm working on the way that you do this within cloud is that now you say run np run dev then (t: 750) it's going to prompt you whether you want to run it in the background so you can press ctrl b now and it will tell you that it's monitoring this in the background I'm just going to break something over here I'm going to put react t and then it's going to throw this error up over here all right (t: 760) and what I'm going to do I'll be like check the terminal for errors and fix all right so you can see it's actually reading this shell output (t: 770) and it actually sees the errors automatically for you it knows that there's a typo and then it's just going to link the fix and then this is back to normal and this is the other thing that is not (t: 780) so obvious it's just say you want to end the process you can see one bash running at the bottom of your prompt right press down here press enter and you can see the running shell and then (t: 790) from here we can actually get a lot of visas about how long it's been running what the command was and the actual output is enter to jump back out or if you want to kill it you can actually press okay and this will kill (t: 800) the running process and then this page will no longer load up all right so that's actually how you make use of the background bash task because it's not super obvious I thought it's something to (t: 810) do with the bash mode and then you type ctrl b from there but that's not actually how you use it at all hopefully this tutorial has shown you how exactly you make use of the new background task command all right so this other change that has been added is this MCP with multiple configs (t: 820) and it may not be immediately obvious how you use this but it's actually a very useful feature that you can use to run your background tasks. But I found this to be extremely useful. So let me just show you how I'm knowing plot I'm just going to show (t: 830) you my MCP servers and you can see I have contact 7 and fire crawl as you can see in this m.mcp.json it contains both of this and this is stored in my project what I found is that sometimes I want to (t: 840) actually have more MCP servers but I don't want it to be loaded up for every cloud session I only want to load it up when I actually need it for example I have this super base MCP that I have (t: 850) over here one you can do is you can do club dash dash MCP config and then you can just pass in the that you had over there. And what you will see now is that for that session that you've run this, it (t: 860) will add that to your list of running MCP servers. And the change that they've done right now is that in the past, if you wanted to have multiple, you would have to still store them all into a single MCP JSON. (t: 870) So for example, I have two browser automations. I have a Playwright and a browser base. MCP server, I will then have to do browser automations and then I'll be able to get hold of the both of them. (t: 880) But with the recent change, you can separate out all your MCP servers into separate configurations and then find that this is a lot cleaner. All right. So now I have another MCP server at the end of it, and it will be able to (t: 890) concatenate and put all of them together. I feel that this is really useful because I think it's actually a lot cleaner to have different settings for the MCP servers laid out in a per file (t: 900) JSON, as opposed to starting it all into a single dot MCP dot JSON file. And this pattern has actually been useful because as for those of you who know, the super base MCP is read only, but then I have a separate write version. (t: 910) So I can actually run this in my. Super base write version. And if you actually look over here, I actually can see that I'm clearly using (t: 920) a write version of super base whenever I'm making database entries. So this is a fairly small change, but I think opens up a lot more possibilities of how to manage your MCP servers. (t: 930) And if you've been working with MCP servers of any kind, you know what a hater it can actually be, but I think this is one step in the right direction. All right. And this other tip and credit to Ian Renato for this tip, because it's (t: 940) actually undocumented, but he's managed to find this out. So there's this new. Command called agent output style setup. This allows you to create a custom style for clock code. Let me just show you how it's done because this is not documented at all. (t: 950) So you really want to pay attention. All right. The way that this works is that you just run plot as per usual. All right. You can see that there's this hidden agent called agent output style setup. (t: 960) That's not actually shown anywhere. It's got this inbuilt custom sub agent that allows you to create an output style. All right. So you can see here, your output style has been configured. (t: 970) You can see here. It's actually got a. This output style and it's actually popped this into my cloud configuration file. And it actually lays out how it wants to provide the responses. (t: 980) So in some cases it actually would prompt you what style you want, but if it doesn't, and it goes ahead and just creates this default one based on your usage pattern, you can say, I want a parody sounding response style and it's going to go ahead. (t: 990) And then he's going to create this pirate theme output style. All right. And then you can see it's already created this pirate style response, right? You have pirate theme output style, be ready for battle captain. (t: 1000) And. This is actually now start here in this pirate dev. And obviously this is a bit of fun, but you can imagine creating a style suited for say planning mode where you want it to be very high level. (t: 1010) And then you want it, maybe you want to have an implementation mode where it actually goes into a lot more detail. And here's the other secret thing. If you do style and then workflow expert, you switch the style back to workflow expert. (t: 1020) You can see here, you'll see I have a plan to workflow expert style to our conversation. Then if you do style and then pirate dev. Now if I do hello, you can see it switches back to a fully there captain. (t: 1030) I find credit again to Ian Nuttall for this pro tip and you should definitely follow him because he posts so many good tips related to plot and AI coding in general. (t: 1040) And that's how I found out about this hidden functionality as well. And just as I was recording the previous part of the video, they actually went ahead and explained what this new output style feature is supposed to be about. (t: 1050) So Anthropix has posted about how to use output style properly. (t: 1060) The idea is that it enables you. To the switch between different modes that change the way cloud responds to you. You can create styles such as an experiment, explanatory style or a learning style. (t: 1070) If you use the explanatory style, it will explain a lot more about what it's doing. But the really cool one is this learning style where it would, it says that it would occasionally pause and ask you to quote some of the tooling tasks. (t: 1080) It's like you having a pair programmer. So the way you do this is that you load it up within cloud and just earlier, I was telling you to use this agent. (t: 1090) So you have to do it a little bit earlier. And this is how we discovered this feature. As of version A1, you're actually gonna be able to do something called output style. (t: 1100) This one allows you to switch between the output styles that you have. And you can see it's already picked up the pirate dev output style that we created just a little bit earlier. And now if you switch in the style to learning, I'm going to do something very simple. (t: 1110) What I'm gonna do is propose the best practice for updating. Prop.MD. This is just a very simple task because I just want to show you how this feature can be used. (t: 1120) All right. (t: 1130) So check this out. Now it says it's going to analyze the cloud MD structure and gives you the insight. So this is the, this is the style that it has is that it's actually going to help you to learn how to actually perform this task. (t: 1140) Right. All right. So it's actually given you a much deeper insight into how we can do this. And you actually say, like, let's pair on updating. So now it's kicked off into this pair programming mode. (t: 1150) And you can see here it says, now let's collaborate on the updates. I'll prepare a section for you to contribute to. And now you can see it's gone into this learning by doing mode. (t: 1160) In this mode, it tries to get you to work together with it so that you can learn how you can actually update this file in a much better way if you wanted to do it manually. So he says, I've added a version history (t: 1170) section to maintain a clear record of changes over time. Your task is to fill in the version history section with two to three version histories. Note for to do human. So let's just do this right now. (t: 1180) I'm going to pull this up. I'm going to look for to do. You can see this actually come in here and it says right over here, I'm supposed to add in this and this one map and then include the major changes, (t: 1190) new features and changes. It even tells you that the guidance is to use cemented versioning and focus on the changes that affect how Cloud interacts with the code base. You can imagine like how this could actually be a very, very interesting way (t: 1200) of turning Cloud Code into just listening to instructions, to a more interactive back and forth. And you can actually create a new style by using this output style new command. (t: 1210) As the docs suggest, you say, I want a style that is extremely terse and curt and to the point no fluff. (t: 1220) All right. Rather than using the agent setup mode that we saw a little bit earlier. This is actually the document, the way for you to create a new style. So you probably want to use this, (t: 1230) but there's also the previous way where we actually use the agent itself, which is a very interesting observation that Anthropic themselves are starting to use the subagents feature even to develop their own new features. (t: 1240) So you can see that I created a new Terse output style. What you're going to do, you can do output style and then switch to Terse. You can see that you would pop this into your local settings over here. (t: 1250) You can see here output style is Terse. I'm going to say like, tell me about recent changes. All right. And you can see it's just telling you (t: 1260) recent changes and just listed it out, no fluff at all. So this is actually how you make use of this new feature that literally just came out on the same day as recording this video. I think we're going to start to see a lot (t: 1270) of very interesting things. Use cases for this. Props to Anthropic for rolling out yet another super cool feature in such a short span of time. All right, folks, that's it for today's tutorial. Hopefully you found it very useful. (t: 1280) As you can see, the ecosystem of things for Clockwork is just moving so rapidly. They launch several updates in a given week. Sometimes they are just minor updates, but sometimes they put in some really cool (t: 1290) features that you wouldn't know about if you weren't paying attention. I hope you found this content useful. If you'd like to keep up to date with more Clockwork tips, don't forget to give this video a like. (t: 1300) Hit that subscribe button and turn on notifications so that you always be the first to know whenever new content drops. And hey, I know that we've been using Clockwork for a lot of coding related tasks, but did you know that you can (t: 1310) actually use Clockwork for other things such as creating videos and video editing as well? If you'd love to learn more, I created this video which outlines how you can actually use Clockwork to create animated videos just using text prompts. (t: 1320) So if you're interested in learning how to do so, you should check out this other video I made, which teaches you how to use Clockwork to create animated videos as well. (t: 1330) All right. Till next time. See ya.

