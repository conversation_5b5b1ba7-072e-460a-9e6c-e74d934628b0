---
title: "Claude Code After 1 Month: The Patterns That Actually Stuck"
artist: <PERSON>
date: 2025-07-29
url: https://www.youtube.com/watch?v=IUi--nVU4yA
---

(t: 0) I've been using Claude Code for a month, solidly now. Like, really using it, not just testing it. This video is not about the features. I made that video, and I would really advise you go check it out. (t: 10) It's got some really interesting little patterns and little techniques that you might use. This one is actually about the patterns and techniques that really have stuck with me (t: 20) and make an enormous difference when I use them, and I think they'll make a difference for you as well. Hang in there. I'm going to go through seven big patterns that I use that honestly make a difference. (t: 30) Let's get right in. Okay, I have to jump in here. While editing, I noticed something pretty horrible, and I apologize for it. (t: 40) It's really embarrassing how many times I mention other videos in this video. You'll notice. But honestly, I also find it a little bit hilarious how many times this came out of my mouth. (t: 50) I noticed. I'm embarrassed. I'm sorry. I'll do better. Let's get back to it. Okay, I'm going to push very quickly through some of these, and certainly through these little bits at the beginning. (t: 60) The first one, which is little bits, is just little pieces that still actually are very, very useful and I enjoy quite a bit. One of them is I always run Claude code inside of Cursor or Visual Studio Code or something (t: 70) so that I have access to the files that I want to touch while Claude is touching them or changing them. (t: 80) That's actually really, really helpful to me. You can also drag them in from here to here. That's easy to do as well. Very worth it. Another one is hooks. So if I say hello here, maybe you heard the sounds go off. (t: 90) I use hooks in that way. If I look inside of my Claude settings file, you can do hooks with a slash command. Go check my previous video about this. (t: 100) But I'm just using AF play to play specific sound files when the thing returns to me so that I can tell the difference between my projects. All right, got to move on. All right, the next one is YOLO mode. (t: 110) So YOLO mode is running Claude with dangerously skipped permissions. This is something that, quite frankly, I always do. This is 100% of the time these days. (t: 120) I run this. I have had no problems with it. I have said this in the past. I don't advise you do it. Use it at your own trust level. So kind of keep an eye on things while you're using it in the beginning until you gain some level of confidence. (t: 130) But you'll see this down at the bottom that says bypassing permissions. That's when you know you're into that mode. All right, another one is the rapid updates by the Anthropic team. (t: 140) Can't say enough about this. They update this thing. Multiple times a day sometimes. And they're usually quite valuable updates that they're putting in. (t: 150) The end of the last week, they put in a way to load settings from a JSON file externally and the agents mechanism that they dropped, which is just kind of earthshaking. (t: 160) So these are just in two days. They dropped those two items, which really changed the way that you can use the product in substantial ways. All right. Sorry. Got to move on. Another one is screenshots. (t: 170) All right. If you take a screenshot, this is a little utility that has a screenshot. You can drag it in. You'll see when I'm in cursor, you'll get this hold shift to drop into editor. (t: 180) When I drop it in, it'll just do the whole path to the file itself. Or if you hit control and V, that's how you paste in images. I cannot tell you enough. (t: 190) Get a good system that you can take quick screenshots that go directly to your clipboard command V or control VM in. It's heaven. So you really need to take that one on. (t: 200) All right. Enough for these little bits. Go check the previous video if you want to see those. Let's move on to the next. OK. Number two is context. Context. Context, of course, is everything. (t: 210) So imagine trying to solve a jigsaw puzzle while people keep adding pieces from different jigsaw boxes. That's kind of what you're doing when you're letting your context grow too big. (t: 220) And that information keeps going in while you're trying to solve new parts of the problem. So we're going to work backwards here just for a second so that I can show you something. (t: 230) If you're using cloud code, there is a command to resume a conversation. When you select this, it will give you the different conversations that you've had. So if I open one up from six minutes ago, that will load the previous conversation into my context window. (t: 240) And now I have a lot more presumably in this context window. And really, you don't know what you necessarily have in here. (t: 250) So if I start asking for something new, I want a new button added or something else. All of the rest that's in this context is actually going with it. (t: 260) And so you're actually asking the LLM to be able to see the difference between what you're asking for. It is going to get confused and be less performant. So what we want to do is always clear our context. (t: 270) It's super easy. It's just a slash command that you can use clear. So this is the other half. First, you can resume at any time. If you fall out of cloud, don't feel like you've lost your conversation. (t: 280) Just use resume. That is a tip in and of itself. The other one is clear. Always clear. Whenever you get to a point that you feel like, OK, we're at a spot. I'm about to start talking about something new. (t: 290) You want to clear. You want your context to be smaller. It needs to be tighter. It's very important that you get it quite tight. All right. On top of this, if you can't clear and (t: 300) you're a little worried about it and you think I do want to move on to talking about the button, but some of the concerns that we have in this context are important, even though we've done a whole bunch of work (t: 310) working on many other things toward the end here. We started talking about the button and this context is so filled, I don't want to just lose it. It's hard for me to move on. You can ask the model, create a prompt for me to move on. (t: 320) I call this. Context handoff so you can ask it about the concerns that you're dealing with at that moment and say, write a detailed prompt with the nuances (t: 330) that are dealing with the button and the theming and the styling that we've recently been talking about so that I can start another conversation. That will still simplify things. You don't always have to do this. (t: 340) Don't worry too much about this. But the more frequently that you do this, the happier you'll be. It will really perform better in a lot of cases. All right. And the last one and this one is, I think, (t: 350) pretty obvious to a lot of people, so I'll go through it pretty quickly. There's this Claude MD file. Now they have a slash command that will get you there. It's in it. If you use the init command, what it's going to do is look through your entire application or what project (t: 360) you're in and try to understand all of the concerns and write things down in this Claude MD file. OK, not a bad idea. However, it's going to get humongous. (t: 370) What is this Claude MD file doing? Well, once I hit this clear. So this is another command that resets my context history that we're talking about. (t: 380) This is like hitting new window and some other tool. So or new chat. So here we are in a new chat. No context. This Claude MD file, everything in here actually flopped down into this context. (t: 390) So it might say no context, but this is the memory file essentially for Claude. And the way that it works is whenever you start a new chat, if you will, (t: 400) that goes and sneaks in there as a background. So it kind of wants to carry the memory along so that every conversation you have, everything, all LLMs understand what's going on inside of this project and some (t: 410) of the concerns you have. OK. So two things about that. This one is enormous. I would say this is a miss. This is my mistake. I probably did a slash init early on. (t: 420) And this is what they look like when you do slash init. It's just too much stuff. I'm talking about all the different ways, start scripts and in scripts and build scripts and everything else. I don't need that for almost all of the things that I do. (t: 430) Right. So this does not need to be in this memory file. I need to come in here and slim this down quite a bit. This is the place that you want to be able to tell Claude, hey, remember, do this. (t: 440) This way or stop doing this or my gosh, don't forget to write tests or you always have to write it in TypeScript, not in JavaScript. (t: 450) This is your file to do that kind of thing. There is a way inside of Claude code that if you use a hash, you'll see it says add to memory, this will actually just write a line to that file. (t: 460) But you can just go directly into this file. It's one of the reasons that I use a file editor with this like cursor or something else. I can just come in here, write another line and say, oh, don't forget to X, Y, Z or I (t: 470) prefer capitalization. X, Y, Z. So this is a really important file. It's really critical to your value of your project going forward. Take a look at it, learn about it. (t: 480) But I've got to move on. OK, number three is about voice. This is the voice revolution. So if you haven't seen this one or you haven't experienced this one yet (t: 490) yourself or you're reticent to it or your environment doesn't quite facilitate it, I highly advise you try to lean into it. The hard truth is, if you're still typing, you're almost certainly not giving enough (t: 500) context in many cases. If you're typing and you're tired of typing when you put things in, you might be reaching the right amount of context. (t: 510) So it's not a hard and fast rule, but voice is a bandwidth multiplier. That's all it is. It's not that you're going to be giving better information as voice, but you're going to be able to put so much more consideration and concerns onto (t: 520) the model of what you're trying to do that you absolutely will get better results. Honestly. So the interesting thing here is people that are very advanced. (t: 530) And people that are beginners both have problems with context. We just talked about context. Of course, context is everything. How much you put in is really representative of what you're going to get out. (t: 540) And people that know a lot tend to put in what they know in a very opinionated way without knowing that that's what they're doing. (t: 550) I'm looking at you, senior devs. You guys are putting in, I want you to do this, build it this way. Here's the model. This is what it should look like. Here's all the attributes. That's fine. If you really need that, if that's an actual hard requirement, (t: 560) rather than an opinion, then absolutely put it in. So I'm not saying it's a bad idea. However, I think very often it's because that's the place our minds go very easily (t: 570) and very quickly when we start thinking about the construction, the architecture of a problem. If you're brand new, you're probably saying, I have no idea what to ask for. (t: 580) I want a doghouse clock. Go. Well, that's not enough. You probably actually have more ideas of what you're asking for. You don't have to be technical. It's not that it's, well, I want a doghouse clock. Where do you want it? (t: 590) You want it as a watch application or phone application? You want it to run on the browser only? You want it to be huge or small? Does it look like Snoopy's doghouse or some other doghouse? (t: 600) Is it the front of the doghouse? All of these things would come out if you were talking about it for a second, but they will not come out if you're typing. Your voice is your key. (t: 610) All right. Number four. I'm going to have to try to push through this one. I apologize. Here is my promise to you. I will build another video that talks about building PRDs. They are really a big unlock for (t: 620) more advanced uses of something like Cloud Code or an agentic building system. If you build better PRDs, which is basically a product requirements (t: 630) document, it's just kind of what you'd like to see in a product. It's not all the technical stuff. It's really all the idea of what the application is, how it works, (t: 640) where it should run, how often it should reboot. Those kinds of things are all inside of a PRD. The better PRD, the better planning document here that way, the better results (t: 650) of your initial build. And if you plan frequently for the different things that you're going to build, you'll be better off. So it's it's pretty particular on how you might be able to get to a better PRD. (t: 660) And there is no real prescription to it. So I think another video actually works here. Let me know in the comments and subscribe if you want to see that, because I'm really hopeful that that's a video that I'll put together. (t: 670) But I will push into this planning section first, pretending that I just told you really critical is being able to put together really solid PRDs. (t: 680) I know that's a promise of a future video. I apologize. But really, what I'm saying is work back and forth with some model. You can do it here in Claude Code or you can do it somewhere else. (t: 690) I very frequently will do it somewhere else. And you can bring the plan in and have Claude Code build it there. So one of the neat things about Claude Code is it's part of your subscription (t: 700) with Anthropic. If you have a twenty dollar subscription, you have enough to really use Claude Code pretty meaningfully. Once you start pushing it, you'll hit your rate limiting a little bit more (t: 710) more frequently inside of those four hour windows. But if you're starting to drift into that spot, one of the things that you can offload is planning. Just go back to the Claude website or even (t: 720) OpenAI ChatGPT anywhere else and say, I need a good plan. Now, you'd like to use a good model to do this and you would like to do this iteratively. So you're talking through it, reading what it's giving you back (t: 730) and forth to make sure it's a plan that you like. And again, the plan should represent what you want from the product, not the technical. (t: 740) It's not all about the how it's not about the how it's all the other stuff. If you don't give all that other stuff, no matter how good the technical solution is, it's not going to match what you want. I know this all sounds trite, but it really is a very major feature (t: 750) inside of building correctly with these versus I threw in an idea. I just wanted a website that did X and it was good. (t: 760) Then I had to go back and forth with it a thousand more times to get what I really wanted. You don't have to do that anymore. These models are really sophisticated and these tools like Claude Code are (t: 770) massively sophisticated. So I would I would definitely advise using a different system if you want, if you're on that twenty dollar model. But if you're not hitting any of your rate (t: 780) limit and you can do what I have here, which is I just went into planning mode and asked for it to plan something so I can say, no, keep planning. It has this plan, which is a pretty reasonable PRD. (t: 790) You can see that it doesn't have a lot of technical stuff. It's much more the this is how this will work. This is what we might add. This is where it might be. And I might here be able to say, oh, this is where it might be. (t: 800) I might be able to say save this as a document in the plans folder and that's it. So now it's going to take this and put a new document in our plans folder that we can work from. And so this is something that I do pretty (t: 810) frequently is the plans that I'm generating. Let's see if we can actually get it to show up. Well, let's hope it's somewhere. I have to move quickly. I'm sorry. (t: 820) So basically the plans that we're generating here, you don't have to just leave within memory while it's building. You can say write a file, write a document. I very often will have a docs folder. (t: 830) All the plans that I'm working through will be listed up here so that I can go back later and kind of see what was planned, how it was going off the rails. Or if I had to stop halfway through, I can just say, hey, go take a look at that plan. (t: 840) Pick up wherever you were and move on. And at least I don't always have to worry about the resume feature. And how do I get back to the context? All right. (t: 850) In any case, this is planning. Planning is super important. I just want you to hear that. And there are ways to get better at it without having to know all of the technical gook that an engineer would know. (t: 860) You can still do a lot of the same lifting, but your plan has to be clean. You really have to say, I want it to have two buttons, not four buttons, or don't just make it any color. (t: 870) Here's the color schemes that I want. All right. Sorry. Sorry. OK, moving on to custom commands. But let me clarify one thing. This was cloud code being great, by the way. I am in planning mode. (t: 880) I asked it to save a file and it came back and said, hey, I know you want me to save something there. I'm in planning mode. If you want to leave planning mode and ask me to do that again, I will. That's pretty cool. (t: 890) Great message. It also didn't escape the rules of planning mode. I just have to give it props for that. All right. I want to talk about custom commands and agents and and some other things, (t: 900) but essentially these things that you'll find up in your cloud file. And these are really meaningful. The idea of kind of building your own tool set is critically important and really, (t: 910) to me, fundamental to making cloud code feel like not only my own meaningful per project, like I've said in the past, I have tons of projects. (t: 920) I create a lot of different projects and having specific commands for this project. For example, the the commands that I have here, I've written a build command so I can just do slash build. (t: 930) And in this project, the way the project is built because it's an electron project is very different from other projects. So I have a little bit inside of this build MD thing here. (t: 940) It's very simple. It's basically the command that needs to be run is this particular prod Mac version of my build command, whatever. Don't have to worry about it. And the nice thing is the next time I come in here or the next person that happens (t: 950) to be in here, they don't have to know it either. They can just look for the slash commands that we have and see, oh, here's one of the personal slash commands that we have. (t: 960) I can use that. So really, I would really advise anything that you do kind of frequently or semi frequently or you keep forgetting how to do just write a simple MD file, you can come up here in an MD file for everybody (t: 970) that doesn't know is just basically a text file. Feel free to just write a text file. In fact, my build command. Has no markdown. (t: 980) Well, has a tiny bit of markdown in it, but very little markdown in it. And it doesn't matter because intelligence is reading this and it can figure it out. These kinds of slash commands from cloud code are really, (t: 990) really valuable and end up being really meaningful across the board. So I have others that I've shared in previous videos. I have an iterator that can do in number of designs at once. (t: 1000) I have several git commands that I've put together that are really useful. So, for example, here I have some changes. If we look here, we have these changes here. (t: 1010) I can just say, oh, I'll do my g save command and that's going to do something that I've written previously that's a global so you can put things in your home directory. (t: 1020) And I know this might be getting too far. Once again, previous previous video. Sorry about that. That is in your home directory. So your slash username directory in the Mac world. And then there's a dot cloud file inside (t: 1030) of there that looks just like this cloud file. So it's nothing special. You just need to open it up and then you can edit those as well there. And I've had that one. It has a commands folder in it. (t: 1040) And this this git g save item that we're running right now is inside of there. And this g save one is pretty complicated. Actually, if we looked at it, it would say, oh, depend. (t: 1050) Go find out, run some diffs, figure out what kind of changes there are. Depending upon which kind of change it is, use this icon versus that icon to denote what kind of changes, blah, blah, blah. It's a lot of stuff in it that I don't want to have to obviously type every time. (t: 1060) And I would never remember to run every single time. But now all of my commits will look similar to one another. Because they're using one global command. (t: 1070) All right. So that's build command. Dang it. It's not build commands. Those are slash commands. They don't all have to be built. Don't make all your slash commands build commands. Sorry. OK, so that one finished. (t: 1080) We can go quickly take a look. This is what the different commit looks like. So this is the commit message built out the way that I have decided I want my commitments messages to look very nice as far as I'm concerned. (t: 1090) And if you recall, what do we do? We just learned this. Hey, since I've just checked everything in, I'm at one of these points that are save points. (t: 1100) I may as well clear my context. I'm in a good place. Do that. If you can, it really will make a difference. What's next? This one's a little upside down. You'll have to forgive me. Number six, checkpointing that thing we just talked about that I told you (t: 1110) that I told you about previously, but I didn't. So now I'm telling you about it. Pretend like I told you about it before I told you that last thing. It doesn't really matter. Here we go. (t: 1120) Checkpointing something that's missing in cloud code. You can find this in windsurf and VSC and and other cursor. Products, they will checkpoint whenever you make a big change, (t: 1130) whenever you start a big conversation or there's a new change that's begun. They kind of mark all the files and say, OK, everything looks like this right now. Come back and click this button if you want to pull everything back to this point. (t: 1140) Cloud code does not have something like that, but it is super easy to do this yourself. So I'm going to introduce you to something. Some of you all are going to yawn. I apologize. This one I'll go pretty quickly with Git. (t: 1150) I also think that I want to create a video around some concepts on how to use Git, not as a code kind of management version control system that I'm going to describe here, (t: 1160) but also some Git agents because cloud code works with your Git repository and can offline do some work for you, some really interesting things that I want to investigate. (t: 1170) So subscribe once again. I'm sorry if you want to see me work through Git solutions with cloud code as well. One of the things that I will cover is having a Git repository. (t: 1180) And what does that really mean? So there's GitHub and a lot of you that are that really don't know either of the. These terms certainly know the word GitHub more than likely. (t: 1190) Git is kind of the underlying system that manages version controlled files. Oh, boy, is that a lot. That's just basically saying it checkpoints everything and says, all right, (t: 1200) where everything sits right now, I'm going to go save all these files, checkpoint everything. This is where it is. Any changes that happen from here, I can come back to this moment once they commit again. (t: 1210) That's a special word and Git. Once they commit again, it's another checkpoint. So you'll have you can come back to the first point or the second point. All the stuff that happened in between, you can't see, but you can see these checkpoints. (t: 1220) All right. Think of it as a video game. You walk up on, you know, the campfire basically. And you'd see that lovely term that comes up across the top that says saving. And you think, oh, good. When the bear eats me next time, I can at least start here. (t: 1230) That's this is for when the bear eats you. That's all. And the bear does eat you. I'm sorry to keep this this analogy going. It does eat you with agentic coding, right? (t: 1240) Every now and these things, every now and then, these things will kind of go off the rails just a little bit, kind of have their own opinion, kind of push things too far in a certain direction. And you want to commit frequently enough or checkpoint frequently enough (t: 1250) that you can jump back to something recent. And that's why I have this slash command that we just used here. That is my G save. (t: 1260) And all G save is doing is running a git commit basically with that whole thing that we just talked about. And so if you can do something like that, build your own slash command (t: 1270) to be able to do commit or git commit or whatever else, that's fine. But you know what you can also do is just ask Claude code here. Can you commit if you just say that it will go forward? (t: 1280) If you don't have a git repository, it'll determine that it'll come back and say, well, this doesn't look like it's yet in a git repository. I was saying there's GitHub and git. They're really separate. (t: 1290) Don't worry about the GitHub stuff yet. If you're brand new to this and this all sounds fancy on your system itself, you can save these checkpoints and that's where you want to do it. You want to just have as many of these checkpoints as you can locally. (t: 1300) In fact, you can see this whole graph here. These are all of the changes that I have in this project that I can roll back. To just tell Claude code, hey, I want this and git. (t: 1310) And then once it is, every time you want to to save stuff, you can say, can you commit? That's it. All right. We've hit the last one. Number seven, going beyond code. All right. (t: 1320) I will say once you see this, you can't unsee it. This changes everything. Does take a little bit of a leap. You'll hear me say this. And if you're really not ready for it, you'll go, OK, I got it. Whatever. Confusing. (t: 1330) Can't even imagine what you're talking about. But the moment that it clicks and you, oh, I get you. It will change everything. Because I think what's happening in Claude code and other agentic systems like this, (t: 1340) these agentic editors that we're working in now, even though it's called Claude code, I think we're seeing something that would be akin to what I think is vibe editing. (t: 1350) So it doesn't have anything to do with code. I've brought you into a different project here. This is my video notes project. And this is where I keep the different notes for the videos that I create. (t: 1360) I do a lot of what I call meetings or walks. I walk around really frankly, right here in this room. And talk right into this microphone just out loud about what I think should be in a video (t: 1370) and let that go usually for an hour or so. And that's what you'll find in these different transcripts is these very long walk notes (t: 1380) that I have. And those walk notes, I then come back to Claude code and say, oh, I need you to take a look at those notes and create an outline from those notes. (t: 1390) And then we will start working forward. And so I have created some things up here in Claude that are agents. Agents. I just released. I just released a video about them. I think they're a game changer. (t: 1400) Basically you can think of them as slash commands for now. If you don't want to try to figure out what I mean by agents, they're very cool and they're very worth mentioning here, but I haven't been using them for a month. (t: 1410) So I don't want to talk about them here, but if, if I use this video outliner agent, you can see that it's just a big prompt on here that says you're an expert video content strategist (t: 1420) and a narrative architect specializing in crafting compelling outlines, et cetera, et cetera, et cetera. And I have a format that I asked. For it to come out in. And I say, here's the format that I want you to deliver my notes in. (t: 1430) And then we go back and forth. So it delivers one of these things and I'll even show you at the bottom of this prompt. And I did share this in the previous video. So sorry if I'm double tapping that I say, when you save the file, I want the file in (t: 1440) a special format. Take the year month day, because all of my notes for these videos are under a year, month (t: 1450) day format. And then the title of what I presume the video might be, or just some nomenclature that, you know, defines the video for me. I want those to persist. So that's, that's one of them. (t: 1460) And the video name is the other one. And I'm saying, I want you to create an outline file with those two constants, but what's really neat is a version number here. (t: 1470) And I'm telling it every time I want you to update that version number. And so if we look into the reports as it creates these different reports, it will create the version one, three, four, five, six, seven, eight, nine. (t: 1480) So as I make edits to it and say, oh no, I don't think that's quite right. It will work back and forth, make some changes that I've asked for, and then write another (t: 1490) version of the same file essentially. And if we go into this, this is very similar. In fact, it happens to be this one that we are working from. And I can even show you where we are in our notes. (t: 1500) Come all the way down to, let's see, where are we? Beyond code. It's one of the things we talked about, right? So we are in the vibe working revolution, if you will. (t: 1510) But what I'm doing here is working with just notes. These are just text files, markdown files, and I'm using Claude code to do it. (t: 1520) And there's something really elegant about having an LLM at your fingertips to change your own environment. We've been stuck in that chat interface web-based system for so long that we forgot that all (t: 1530) of these tools could do anything if we gave them a little bit of capability, if we just allowed them some kind of rights and privileges to our files and our systems. (t: 1540) And we will see more and more of this. I am not saying Claude code solved this, invented it. And it's the answer. I'm just saying this hints at a future I can very clearly see. (t: 1550) And we will be doing this for literally everything soon. In any case, I think we're in the middle of something really brand new. (t: 1560) And Anthropic is even, as I understand it, offering Claude code. A lot of the different people in their company are using Claude code for their own uses that (t: 1570) are not code related. So not just the engineering teams, but other teams are also using it for other purposes. And I think that's a good example of, look, this tool can do a lot for you. (t: 1580) You want it to write your Instagram posts or you want it to go and read the URL from the web page that you're trying to summarize and turn into a video. This can do it, right? (t: 1590) It's an agentic system that can reach out to the world, can reason about things and can write files. It is time to put it to work. So this is my biggest piece of advice. If you want to look into something really different, like I said, this one might miss (t: 1600) you. Cause it does take a moment to see that next level to go. Oh, okay. I see that I could use it for other things. If you're still stuck, just trying to figure out how to code with it. (t: 1610) That's perfectly fine too. Obviously it's excellent at that. And I haven't even scratched the surface of what it can really do in that space, but it really has legs in a place that maybe people weren't really expecting. (t: 1620) All right. This is getting really long. I apologize for it. Thanks for sticking around for this one. Let's jump out. Okay. So this was a good one. (t: 1630) I really wanted to hand off what my experience with working with Claude code for a month was like. And what my findings were like, did I stick with everything that I started with? (t: 1640) What things fell off? What things found their way on? I hope I showed you some of them. This is really the highlight list of how to really get the most out of it after a month of working with it. (t: 1650) And really what I'm talking about here is useful in almost all agentic coding environments or agentic editing environments might be a better way to describe these in the future. (t: 1660) So don't feel like I'm only talking about Claude code. If you use cursor, all of this. Or almost all of this completely works for cursor and all of the other systems as well. (t: 1670) Even the online ones. A lot of these rules will work if you're working in bolt or other things. A lot of this kind of tip idea pattern stuff that's being shared is, is really just general (t: 1680) stuff. So I hope this hits you a little bit with some ideas that you hadn't seen before or some aha moments with any luck. (t: 1690) A lot of these are aha moments for me when I put them all together. So. I hope you enjoyed this video. I hope you enjoyed the same. I really do appreciate you being here and I'll see you next time.

