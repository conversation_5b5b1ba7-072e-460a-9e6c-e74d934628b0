---
title: 3 GENIUS Hacks to REVOLUTIONIZE Your SaaS UI UX Experience
artist: Code With <PERSON>
date: 2025-08-23
url: https://www.youtube.com/watch?v=CDpm3DP7WeU
---

(t: 0) In this video, we'll build a complete navigation system for your SaaS using Cloud Code. Header navigation, collapsible sidebar, and smart breadcrumbs that automatically show users where they are. And I'll show you how to use MCP browser (t: 10) automation tools to let Cloud Code test and fix code automatically. And if you haven't seen the previous videos where I build out the core framework and user authentication, check those out too. Alright, so let's jump straight into (t: 20) building our navigation for logged in users because we already have our main navigation, which is what users can click on to get to the main page they want to. From there, we have the main nav, x, x, x. These are different main application (t: 30) pages that you can click on. Then we'll have a side navigation here where we can have the second level of navigation. And then inside that, we might have just a (t: 40) second level to each of these individual levels in case there are sub-navigation inside one of these pages. And then usually there is an x and y and z and (t: 50) maybe even sometimes a w. Breadcrumbs. And we probably also want to have a nice cool little icon, at least on the main (t: 60) navigation here. And then we will have space for the main page right there. Just going to correct this here. And then I'm also going to dictate my instructions to (t: 70) this application, Whisperflow, so that we can then copy and paste that into VS Code into our Cloud Code session here. And this will be the main page area. And (t: 80) again, the two exceptions that we need to make sure that we're doing this correctly are the one that we need to make sure of is that when we visit the home page as a logged (t: 90) in user, it goes to the main app page instead of the landing page. And two, using this sidebar (t: 100) should be configurable. Not every page has to have a sidebar or breadcrumb because there are going to be some pages that do not require a sidebar, maybe like a dashboard. So we'll (t: 110) need the option to disable the sidebar. All right, so let's dictate this out. I need to make some updates to my SaaS application. So I'm going to go ahead and do that. And then I'm going to go ahead and do that. And then I'm going to go ahead and do that. And you can see where I have the navigation. I want to build in some generic navigation (t: 120) using a sidebar and breadcrumbs so that anyone using this SaaS framework can easily roll out multiple pages and navigation for their SaaS without having to redesign and re-implement (t: 130) this for themselves. At the top of the page, we are going to have our main navigation and we already have that. That is in the header. Those are going to be the main pages. Anytime (t: 140) you click on one of those pages and it is a logged in page, we should be able to open up. sidebar, which will be our secondary navigation for that main page. Each one of those secondary (t: 150) navigations should have the ability to have an icon. And then we should also be allowed to have a second level of navigation under each item in the left-hand sidebar. So if we imagine all of (t: 160) the links in the header are going to be X, and then in the left-hand sidebar, the first level (t: 170) of navigation is Y. And then there's a second level of navigation under each main item in the sidebar, and we can call that Z. So we have X, Y, and Z. To the right of that, there should be a breadcrumb (t: 180) where we can also access X, Y, and Z, and potentially a fourth level of depth, W, but W should not be part (t: 190) of the left-hand navigation. Under the breadcrumb should be the main page area. Now there are two exceptions to this, or two special cases, I should say. First and foremost, when a user goes to slash (t: 200) or a user goes to slash, they're not going to be able to access the main page area. So they're not going to be able to access the main page area. So they're not going to be able to access the main page area. So (t: 210) that they are directed to if they are logged in. And there might be other main pages outside of that, but we should at least have that. Two, we should be able to disable the left-hand sidebar (t: 220) for some pages because it might not be necessary. So when a user creates a new page, we should be (t: 230) able to add configuration to that page to determine what the name of the left-hand sidebar item is. is, as well as detect which main page it's under. And I'm going to stop navigating here. Oh, it looks (t: 240) like I put it right here. That's fine. Normally it puts it somewhere else. We'll just move this off to the side here. But I just wanted to clarify one other thing before I kept going here is that (t: 250) when we actually look at the navigation here, it's going to be slash x slash y slash z and then slash w depending. So I'll just continue to dictate inside this box. And one other thing to mention (t: 260) is that in terms of the URL itself, that should follow the same methodology where it's slash x (t: 270) slash y and slash z and then potentially a slash w. If you're enjoying this video, make sure to like and subscribe. It tells me what type of content you want more of. So I'm going to go (t: 280) ahead and just stop here. It's quite a bit of information to absorb. I'm going to go ahead and take this over to Cloud Code and allow it to just come up with a plan. So I'm just going to come (t: 290) over here. We're going to do a shift tab and we'll go into plan mode. I'm going to copy this here and we'll let it digest all of that and come up with a comprehensive plan. Actually, one of the other thing I'm going to do is please make sure to keep this very simple (t: 300) and also evaluate any best practices online or the community before finalizing your plan. (t: 310) We'll go with that. And then once it comes back with the plan, I'm also going to make sure it (t: 320) creates a few sample pages to help us confirm. Each of the different possibilities is testable. All right. So here's the plan. It's going to create a flexible, configurable navigation system with sidebar with the (t: 330) hierarchy that we talked about key components. So now the first thing that I'm noticing is that it wants to create a single config file with all of the navigation. I was thinking that as you created each page, you would put the configuration there. (t: 340) So now there's a few things that I'm seeing here as I go that I want to clarify. So I went out of plan mode and I'm just going to dictate things as I as I read these things here. (t: 350) So here for Section one. I notice you're mentioning that we're going to create a single config file that defines the entire navigation structure. I assume that we would put the pages that we create somehow in that file. (t: 360) I was thinking that the configuration would go into each file itself, but I'm open to what you think about that, whether we should just have a single config file that allows people to construct the system and reference the pages that are inside of our system or whether the configuration should be stored in each page individually. (t: 370) So now let's go to number two sidebar components collapsible with icons. (t: 380) Okay. So the first one is the page level. So the page level is a little bit more like a solid page with only going to the second level, bread crumb dashboard layout, that looks fine. (t: 390) Routing structure. The root redirect looks fine. We'll go from slash to dashboard whenever they're authenticated, page level sidebar via configuration implementation, files to modify. (t: 400) So overall, that looks good. I just want to confirm on this one here. All right. So looking at the two options here, it's got the pros and cons, and it agreed with me on the per page configuration. (t: 410) the sidebar. So we'll go with the per page. Let's go with the per page. And also please put into the (t: 420) plan test pages X, Y, Z, and W so that we can confirm everything is working. All right. Single (t: 430) SAS navigation system plan. We got the whole plan here. Per page configurations. That was the one (t: 440) thing that we changed. And then it's creating these test pages, but I don't want it to use dashboard for the test pages because I want to leave the test pages behind and the person (t: 450) configuring the SAS framework. It would be confusing to have them attached to the dashboard. So I'm going to make that one change. Please don't use the dashboard as the X test. Please (t: 460) make a X page and add to the header so that. (t: 470) It's not connected to a real page. All right. Let's go ahead with that and see how it comes out. Now, while it's running, the one thing I do want to make sure of at the end is that it implemented (t: 480) these two exceptions here for the homepage. I know I've mentioned this one earlier, but I don't know that I saw that in the final plan. So we'll confirm that. So let's check in on the pages that it's (t: 490) creating. We have the source, the app, we have the test nav here. And if we come into one of the pages here, we can see the navigation that we would add to each page that we want to have in the navigation. (t: 500) And here's where we can see the navigation that we would add to each page that we want to have in the navigation. So we can enable the sidebar or not. And then here's how it builds out the breadcrumb. So every page is going to be able to configure this navigation system properly. All right. Now (t: 510) it's trying to start up the app. I do have it running over here already. So I'm going to go ahead and just start it myself. NPM run dev. Looks like it's working. Go back to the site. Let's do a (t: 520) refresh. I see the test nav is already here. We'll click that. All right. It looks like we've got an issue. So I'm going to copy this, jump back to the terminal. We see the issue here as well. I ran it (t: 530) myself. Some issues. You'll usually run into some issues, sometimes caching issues as well. So I'm just going to copy all of this here as much as I can. Just give it all the context it needs. Looks like it needs to (t: 540) install a component. And you can notice here that it found there was a cleaner approach. So it's automatically just reverting to that. So again, here it's noticing some complex logic and luckily (t: 550) fixing that for us because one of the risks with all of this is just that the AI creates too complex code that makes it hard to manage later. All right. So it says it's done. Let's check it out. (t: 560) Okay. So we're going to go ahead and start it up. And then we're going to go ahead and start it up. Still seeing an issue, not seeing the sidebar, seeing something pretty random here. Also seeing the issue here. We'll cut and paste this. Let's see how far it gets with this. So if I run into (t: 570) another issue, I'm going to take a pause, go into plan mode, analyze what it's built so far and see if it thinks it made something too complex and if it can be simplified, but we'll let it finish this (t: 580) out to see. I'm going to go into auto accept mode so I don't have to accept each change. All right. Let's take a look. Still another issue. So I'm going to go over to cloud code. I'm going to go (t: 590) to the cloud. I'm going to say that we have a very simple, simple and stable feature here. (t: 600) Before we go any further, I wanted to see if you feel we've designed a very simple and stable feature here. It feels like we might have made it overly complex. Please review and see if it needs (t: 620) simplified or do you think this is a good design? So now it's reverting back to the static (t: 630) configuration approach instead of the per page approach. So I'm just going to question it and I'm just doing that because it's taking a drastic turn here. It's not just trying to simplify what (t: 640) it has for this solution, it's trying to come up with something totally different. Is this actually complex? The core concept is simple and so you can see here this is why I question things back and forth a bit. I'm not saying we should redesign it and totally ditch what we're building but I'm (t: 650) wondering for the per page configuration have we built it too complex or do you feel pretty confident (t: 660) that we're pretty close to having all the issues fixed? All right so it thinks it's actually pretty close. All right let's go ahead and finish up if you think we're close. So now one thing that it's doing here is that it's restarting the application but I already have it running here. So you got to (t: 670) pay attention to small things like that. It's starting at 3001 instead of 3000 so when I test it again I'll need to go. That's not ideal but I don't want to stop it now so it's going to continue forward. So it turns out (t: 680) it was just a cache issue. So it's good to question it and go back and forth and really challenge it. It looks like it's trying to test it on its own. We'll go ahead and let it test it but since it's (t: 690) up I'm going to jump over there as well. Just need to change this to 3001. Noticing this little menu over here I'm not sure what that is. Still having an issue. Please kill the running instance you have. (t: 700) I'll restart it myself. Can you go ahead and clear the cache and rebuild it? Just to make sure it builds correctly before I start it myself. So I'm just going to have it (t: 710) clear the cache and rebuild everything to make sure it's working. I'm super curious what this menu is over here. Ah it's over here. So it's this. So it might have just been hidden there. (t: 720) Got it. I didn't ask for that so it was a little confusing. All right everything's building correctly. So I'll go ahead and run it. Refresh. Move it back to port 3000 now that I'm running it (t: 730) myself again. All right still popping up with the same error and the menu is no longer working. I'm going to go into plan mode. Working and we have an additional error and we have the same error. It looks like you're not (t: 740) really getting to the bottom of the issue. Please take a step back. Do any research online that you need to do related to this issue. If you feel like we can make this solution more simple in terms of (t: 750) the per page configuration not switching implementation styles completely. Please (t: 760) consider all that and come up with a plan that is going to help us resolve this. All right let's move forward. Let's create the new plan. So one of the things I told it to do was to do a search so that it could be more efficient. So I'm going to go ahead and do that. I can actually get some new information. So when you're in a situation like this the best (t: 770) thing to do is if you don't have the information tell it to do a search so that it can break out. I'm going to go ahead and confirm its plan and let it move forward. All right let's run it again. (t: 780) All right so now we don't have the error and the menu is working. And here are our sub pages. And there's our breadcrumb. So now we're in the analytics test navigation analytics. There should (t: 790) likely be an indentation here in the second level. And it looks like there's a third level here as well. And now we've got this here. interesting so this is the second level I gotcha analytics is just another page (t: 800) go to settings it's kind of interesting that these pages are showing up dynamically if I go back to home first I notice it's not redirecting to dashboard (t: 810) but if I go to test nav now they're showing up reports monthly reports reports view user reports so now our four levels deep so this looks like it's (t: 820) working but I'm really curious as to why those weren't showing up I'm gonna go ahead and stop this run it again go back to the main page start it again okay so (t: 830) there's the issue so it does look like it's working now however I noticed that in the left-hand navigation the navigation wasn't showing up until I (t: 840) actually went to one of the pages and then once I went to the page then it showed up in the left navigation but the left navigation should be complete and ready to go as soon as we see the menu the X level and Y level sub pages should (t: 850) already be there you on page load I'm gonna go into plan mode all right so the issue is that the sidebar only shows up after visiting the pages because we're using a (t: 860) registration based system where pages register themselves so now we're back to a single file so that's option one or option two pre-register all pages well (t: 870) to me option two should have been obvious from the get-go it doesn't make any sense so what I need to understand is can you fix this in the right way have them all pre-registered in a way that is efficient and (t: 880) scalable or do you need to rethink this a bit I'm asking you to come up with a plan you need to tell me what the best solution here is so it's basically (t: 890) asking me if I think this is the good solution but in fact it should be thinking about this all right so it looks like we've got a fix for this let's go ahead and move forward sticking with the per page configuration I will (t: 900) say one thing that is interesting about AI is that I've done this same project before in the past and it did it quickly and did it right the first time and then (t: 910) for whatever reason the way I approached it this time may be slightly different and it went down far more rabbit holes than it did the first time all right let's let it run a type check I'm gonna go ahead and stop the dev (t: 920) version while it's doing that notice the issue and it's fixing it noticing it's changing everything back gonna go back into plan mode so I had just noticed that it went from const to export now it's going from export back to const (t: 930) again and I just wanted to double check continue forward with the plan all right we'll let it do its own build and then I'll run it locally myself all right let's run the dev site again all right it loaded and it's back to the original (t: 940) version now it's back to the original version okay so I'll stop running the dev site and it's back to the original version okay so now it's back to the original version all right so that's working a couple of small things is that (t: 950) I'm noticing that the menu is way over here on the right side but the left nav is on the left side that doesn't make much sense I'm noticing that number one (t: 960) the menu icon to show the left-hand navigation on or off is way over on the right side and we should have something that's more in line with the left hand (t: 970) that is better suited than just saying menu. And also the menu should be enabled by default. I'm not actually even sure we need the ability to hide it, (t: 980) but go ahead and come up with a plan to move the menu hide to the left side with an icon and always have it enabled by default. All right, so it's presenting a plan. Let's go ahead and move forward. (t: 990) All right, it looks like it's done. Just finishing something up here. So now when we refresh, it is enabled. It looks like this is the icon that they're using now. That feels a little bit odd here (t: 1000) to have it with the breadcrumb. It should be more associated with the left-hand nav bar. The other thing that I'm noticing here too as well is just that the entire page here is full width. I'll go ahead and refresh here. (t: 1010) And now this is enabled by default. I see this new icon here. Doesn't seem like quite the right place for it. Seems like it should be over here somewhere. So by default, it is showing the left-hand sidebar (t: 1020) by default. The icon to hide and show it again is right next to the breadcrumb, which doesn't really make sense to me. What do you think is a good suggestion (t: 1030) for where that button should be? Or should we even consider not having it? All right, so it's coming up with a few different options. Option one, inside the sidebar header, on the sidebar border. (t: 1040) Option three, top left corner separated. Option four, main header left side. My recommendation, option one, hide sidebar header. All right, let's go ahead and go with option one. (t: 1050) All right, so it looks like it's done. All right, so now it looks like it's done. It's moved over, except now that when you collapse it, it just completely disappears. It looks quite a bit better there. However, when you collapse it, then it's gone completely (t: 1060) and there's no button. You don't want to bring it back. So there's gotta be a way around that. Yes, go ahead and implement that. To be honest with you, I don't really like that. I really don't like the floating show and hide icon (t: 1070) when you close it. I think this makes the most sense where it just moves to a thinned sidebar strip. We've got a few different options here, but I think option one, where it's just the sidebar strip makes the most sense. All right, it looks like it's ready. (t: 1080) Come back, refresh just in case. And now that looks a lot better. That makes a lot more sense. So I'll just walk through this just a little bit. Now, the only thing I'm noticing here (t: 1090) is that there's a big gap here that doesn't exist on the right side. All right, it looks like that's working. Now, the one final piece that I'd like to correct is that in the main page, there's a fairly large gap from the left-hand margin (t: 1100) to the left-hand sidebar. And then there's no gap on the right side of the page, the main page. (t: 1110) The breadcrumb is also above this main page and there's also a gap there as well. I feel like there's a better spacing that we could have with the left-hand navigation that is also correct. (t: 1120) Consistent with the right-hand margin. Please take a look at the code on how the main page and the breadcrumb are aligned with the page, the left-hand sidebar and the right-hand side and come up with a good amount of space, (t: 1130) an equal amount of space between the main page and the left-hand sidebar and the right side. One thing I wanna make sure is that it's applying this to all pages, (t: 1140) not just the main one. One thing I do wanna make sure of is that you're applying this to the main styling that will apply to all pages (t: 1150) and not just the dashboard. I noticed that you were looking at the dashboard layout. This should be the main page that all pages will inherit. Can you confirm? And then it was also mentioning something (t: 1160) about the breadcrumb. I wanna take a quick look here because the breadcrumb and the main page are aligned perfectly. All right, so that is the case, although the naming of that is a little odd to me. Would it be an easy fix to rename the dashboard layout (t: 1170) to main layout because we have an actual dashboard page slash dashboard or is that the same thing? (t: 1180) I'm just gonna double check myself how this is set up, dashboard. See, we have an actual dashboard page. Perfect observation, you're absolutely right. So you wanna be looking out for these things as you go. I've spent a little bit more time on this (t: 1190) than I would have liked, but it's an important core feature. So getting this right is good. So it's gonna rename that for us. We'll go ahead and confirm that we'll allow it to move the pages. (t: 1200) All right, can you also just clear the cache and rebuild just to make sure everything is working? Go ahead and rebuild. I'm gonna go ahead and restart it. That looks like a cache issue, but I think we cleared that out. (t: 1210) Cross your fingers. All right, really not seeing any difference on the widths and it doesn't have anything fixed over on this right side. So what I'm gonna do now is, since we've got everything else (t: 1220) looking like it's working pretty well, I'm going to jump into the terminal here and I'm going to install the Playwright MCP. And what that's gonna allow Cloud Code to do (t: 1230) is to communicate with the browser and check itself so that we don't have to go back and forth. So just with that command, it's gonna now be able to open its own browser and double check itself. Although I do believe (t: 1240) I'll have to reload, which is fine. I'm gonna go ahead and kill this Cloud Code. There was already quite a bit of context there, so starting fresh will be nice anyway. I'd like some help fixing the formatting (t: 1250) of the current left-hand navigation and the main page to the right. Actually, the left-hand navigation and the breadcrumb above the main page and the main page. (t: 1260) There's a large gap between the left-hand navigation and the main page and breadcrumb. And then there's no gap on the most right, most portion of the main page (t: 1270) and the left hand or in the right hand limit of the page. Go ahead and use the Playwright MCP to load up the site and see it for yourself. (t: 1280) You can go ahead and use the slash test nav page. Go ahead and look at it through the MCP so you can see yourself, then fix it. And then go ahead and make a fix (t: 1290) and then test it until it is right. I'm gonna go into plan mode and I'm just gonna fix this here. So we have the right URL and double check it, test nav. (t: 1300) So that's good. And I'm just gonna jump into Cloud real quick and just make sure the MCP is there now. So there we go. So we're gonna let it start it on its own this time (t: 1310) and then we'll let it use the MPC to navigate to the page and check it out. And we'll let it take a screenshot as well. And now this way, I won't have to continue to just talk to it. It'll actually be able to see the issue on its own. (t: 1320) All right, I'll go ahead and accept the plan. And now we should be able to just sit back, have it fix it, test it and loop through until it's fixed. It's gonna see that it didn't fix it. (t: 1330) It has not been fixed at all. The left-hand margin on the main page and the breadcrumb is significantly greater than the right-hand margin to the rightmost side of the page. (t: 1340) The left-hand margin or padding on the main page to the left-hand navigation and the right side should be equal. I noticed it did put some padding here (t: 1350) and maybe that's what it thought success was, but we really want this to be more equal. They're not balanced at all. The left-hand margin of the page bumping up against the left-hand navigation (t: 1360) is significantly greater than the right-hand margin to the rightmost side of the page. We at least see that it sees the issue now. And as we can see, it's not fixed. (t: 1370) It is absolutely not balanced. For a brief moment, when you were doing your fixes, I noticed the right-hand margin pulled in significantly and it now looked much better, (t: 1380) except the fact that there was way too much padding on both the left and right. So you were able to correct the issue for a brief moment before you went back and reverted, but there was way too much padding. (t: 1390) So there is something about the way you're going about this and even looking at the issue that you're not even seeing. So I need you to take a step back, come up with a plan here (t: 1400) and really make sure you know what you're doing. And not only that, but take more time analyzing this situation because you keep thinking it's balanced and it's not. I'm gonna go into plan mode. All right, let's go ahead and watch it as it goes. (t: 1410) This is the fourth time that you've checked and said it was balanced and it is not. There's something that you're not looking at that's not allowing you to see. The left-hand margin on the main page (t: 1420) in between the main page and the left-hand sidebar is huge. And the right-hand margin on the page to the right-hand side of the page is very small. (t: 1430) Focus on that left-hand margin. You haven't changed it one time. And just maybe we're talking about two different things. I'm talking about the left-hand margin on the page, (t: 1440) not the left-hand navigation. The left-hand navigation, there is a huge gap between the left-hand navigation and the main page. And you've never changed it a single time. (t: 1450) This is the fifth time you've tried to change it and nothing changed. Remember, the gap is in between the left-hand navigation and the left-hand margin of the main page. (t: 1460) There's a huge gap there. There we go. Now you are able to make an impact, but the gap between the left-hand navigation and the main page and the gap on the right side of the website is not equal. (t: 1470) The gap between the left-hand navigation and the main page and the gap on the right side, the page and the right-hand side of the page (t: 1480) should be more equal. So go ahead and bring in whatever you changed, make it a little bit tighter again. And when you analyze the screenshot, (t: 1490) really look at the margins, it's clearly not equal and you keep saying it is. There we go. All right, that's looking a lot better. Although I think on the right-hand side, there could be a little bit more margin there. (t: 1500) Take a look at the screenshot again that you just took and add enough padding to make it so they are equal. All right, there we go. All right, that's looking a lot better. Now, one other thing that I'm noticing is that the line underneath the breadcrumb (t: 1510) is going all the way to the right-hand side of the page. Whereas on the left-hand side, it does not bump up against the left-hand margin. (t: 1520) It should leave a little bit of a gap on the right-hand side, the same way the line has a little bit of a gap on the left-hand navigation. So I'm just correcting this little piece here. (t: 1530) There we go. Perfect. All right, so there we go. Now, if you're looking for an engaged group of vibe coders, make sure to jump into my new community. I'm here to help you personally and patiently work (t: 1540) through these types of issues, as well as everyone else here in the community. I will be dropping this entire SaaS framework into the classroom. We have support calls where you can get personalized one-on-one help (t: 1550) and networking calls on Friday. I'd love to see you inside the community either way. I hope you enjoyed this video and I'll see you on the next one.

