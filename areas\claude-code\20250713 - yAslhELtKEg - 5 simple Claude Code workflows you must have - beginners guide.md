---
title: "5 simple Claude Code workflows you must have - beginners guide"
artist: <PERSON>
date: 2025-07-13
url: https://www.youtube.com/watch?v=yAslhELtKEg
---

(t: 0) Here are five simple Cloud Code workflows you must have. My name is <PERSON> and I have hundreds of hours inside of Cloud Code. In this video, I'll show you how to use Cloud Code to build literally anything, (t: 10) even if you are a complete beginner. So the first workflow I'm going to show you is a multi-agent setup, how you can use three different Cloud Codes working together on a single project. (t: 20) So let's jump into Cursor. I'm going to open an empty project. So while the team of Cloud Codes will be running, there will be a Cursor agent just overseeing the operation and also helping us set this up correctly. (t: 30) Because multi-agent teams can be tricky. I even made a whole video on this, so make sure to watch that after this one. But anyways, let's switch to Cursor and we need to create a first file. (t: 40) So I'm going to say create a new coms.md file in the root of our project. Then I'm going to paste in the purpose of this file, which basically explains how the agents should work together. (t: 50) And let's use grog4 to make this happen. Now while this is running, we can actually manually create a new file called cloud.md. This is obviously the main file, the main system prompt file for all of our Cloud Code agents. (t: 60) Now since Cursor added the queued messages, we can already start typing our next prompt. Boom. And we have this message in queue. While Cursor is still doing the coms.md file, this message was ready. (t: 70) Now this action log is where the AI agents will write down what they are doing and what should be done next based on the goal. So I'm going to give Cursor a next prompt, basically explaining the workflow. (t: 80) Update both .md files clearly and explicitly explaining this. So basically, we're using grog4 as our chief prompt engineer, constructing the rules for this multi-agent system. (t: 90) I'm going to reject it here. I'm going to only accept it here. Okay, so I'm using Super Whisperer to transcribe my voice faster. (t: 100) Let's see. Action log, login protocol, agents are designated agent 1, 2. Yes, correct. Okay, this looks good. Let's accept. And now we're ready. So what we need to do is we need to set the goal. (t: 110) So I'm going to put it right here. Create interactive 3D mountain terrain game. It should be pretty simple. All right. So what I did is I opened three different terminals. Inside. Of course, right here. And what we need to do is launch cloud code in each terminal. (t: 120) So let's do that. Just type in cloud. That's it. But a pro tip every morning. What I do is I open my main terminal inside of my MacBook and I just type in cloud update. (t: 130) This will check if there are any new updates of cloud code globally on your entire machine, not just within a specific project. (t: 140) Anyways, I can probably close the cursor agent so that we have some more space on the screen. So top left, we have the cloud.md file and coms.md. This is where we're basically going to be managing the project. (t: 150) And if the agents are misbehaving or something, we can just add more instructions into cloud.md, which impacts the behavior of every single cloud code instance running in this project. (t: 160) All right. The next thing we need to do is switch each cloud code to the auto accept mode. So you can do that by pressing shift tab. So now all of them are in auto accept. (t: 170) Next, I'm going to do slash model and I'm going to switch each cloud code to Opus because I guess I don't like model. So I'm going to do slash model and I'm going to switch each cloud code to Opus because I guess I don't like model. By the way, for context, I do have the $200 a month subscription, which basically gives you unlimited cloud code. (t: 180) I'm also paying it for a bunch of my other developers. So that's how I'm spending over $800 a month just on cloud code between me and my team. (t: 190) Anyways, it's time for the first prompt. So the prompt is pretty simple. Read both MD files in our project to develop a full understanding of what our goal is, as well as how you need to work with other AI agents in this project. (t: 200) And I'm going to send this to every single instance of cloud code. Boom. And there it is. Watch. The cloud codes run. All right. So it's creating a list of to-dos. (t: 210) And I'm going to actually stop them because I forgot to assign which AI agents they are. So I'm going to say, you are AI agent 01. I understand I'm agent 2. (t: 220) I understand I'm agent 1. I'll wait for further instructions. Okay. So notice how each cloud code behaves differently. The real challenge with multi-agent systems is to tame them so that they work together and not against each other. (t: 230) So now that each agent understands their role and what needs to happen, we're going to launch them one by one. To launch the agents, I prepared. This prompt. Start by writing down the first task you're about to execute. (t: 240) Properly identifying yourself and clearly and concisely explaining what the next task is. Once you finish your task, read coms.md again and keep going. (t: 250) Do not stop working. The user will tell you when the project is complete. Send. Let's launch agent 1. Let's open coms.md to see the action log. We expect to see some stuff appear. (t: 260) There it is. Agent 1. So now we can launch agent 2 with the same prompt. So the next thing is to set up free JS library and initiate a 3D scene. Okay. We're waiting for agent 2. Agent 2 is working. (t: 270) Beautiful. Let's do agent 3. Man, this is amazing. This is so amazing to watch. We really are living in the future. I cannot imagine where this will be in three to six months. Let's see. Agent 3. Come on. Join the workflow. And there he is. (t: 280) All three agents are active and they're working together, communicating through the coms.md file. Let's see if agent 1 will continue working. It implemented a structure. Yes. (t: 290) Agent 1 implemented step and it works again on the next step that agent 3 assigned. This is so amazing, guys. And we're just getting started. This is only the first of five different Cloud Code workflows I'll show you in this video. (t: 300) So if you think this is amazing, you have no idea what's coming. Real quick, if you want to win free access to my Cloud Code Mastery Workshop, (t: 310) then make sure to follow me on Instagram. All you need to do is like and comment on the latest post and one person will win lifetime access to New Society, which includes access to all the exclusive content we have inside, including the Cloud Code Mastery Workshop. (t: 320) It's completely free to enter this giveaway. All you need to do is go to. Instagram, like and comment the latest post and within 48 hours, I'll choose the winner. (t: 330) The link to my Instagram is below the video. The second workflow I'm going to show you is how to use Cloud Code to review pull requests. So if you work in teams with other developers or you're just about to hire your first dev for your AI startup, (t: 340) you absolutely need to know how pull requests work and how to review them. Luckily, we can use Cloud Code to help us in this process. (t: 350) So I've reviewed dozens of different pull requests from many different developers and I've optimized this prompt, which is like over 90 lines of context engineering. (t: 360) And yeah, but you can find this in the New Society is inside of this module right here. Anyways, this prompt approaches pull request review in stages. So let me just show you how this works on an actual real PR for my own AI startup. (t: 370) So, for example, this 1926, let's copy that. So I just switched into my project directory for my startup. (t: 380) And what we need to do is paste in this prompt into Cloud Code and say, help me review. PR and then the number of the pull request 1926. (t: 390) And it already has all the instructions, right? How to review the principle engineer, what not to do, what to do, looking for solid principles and performing it in stages. So stage one is running these two terminal commands to understand what the PR is about. (t: 400) Start with stage one. So there it is. It ran the first GitHub CLI command to view the PR, read what it's about and really understand it, (t: 410) because that's the first step of reviewing a pull request. You need to know what changes it's making. Now, the reason why you have to understand how Git branches and pull requests work is (t: 420) because Cloud Code can do a lot of code changes very, very quickly. And if you do that on the master branch or on your main branch, that can be very risky in case you want to revert it or there needs to be more testing. (t: 430) Right. So when you create like a feature branch from the master branch, you can safely do commits there. You can absolutely yolo it with Cloud Code. You can run four different instances just like I did earlier and do that very safely, knowing that at the end you'll do a (t: 440) pull request into the master branch and you can actually review it. (t: 450) And if you use this protocol, you can ship way faster. You can be even more aggressive and also be more proper during the reviews. So you win on both sides. So first it ran these two terminal commands and then it understands that, OK, this PR adds a 1500 character limit for ideas in the app. (t: 460) Actually, what we can do very easily is just tell Kyrsten to start both servers. (t: 470) Start both servers in commands, right? Boom. And it can close this actually. And it should start both our front end and our back end by itself. (t: 480) There it is. I'm just going to move terminal into panel. That way it's not annoying us and close the terminal. If we go back to the browser, we should be able to reload. All right. There it is. (t: 490) There is our app. Let me hide this. We're focused on the ideas. So again, understanding this PR, right? What it did, it implemented a limit, 1500 character limit for ideas. So here I already was testing it. (t: 500) So before I was able to surpass it. So let's see if this PR did fix it. Right. Boom. Pasting. No, it cuts it off at 1500. OK, that's good. (t: 510) So I'm going to do test two. So we know we're here. Create this new idea and then let me try editing it past. No. OK, so my previous way to surpass it doesn't work anymore. (t: 520) I cannot edit it past and surpass the limit. That's good. So this PR is actually very simple. Let me go back. But so there's only five files changed. (t: 530) But that doesn't matter. We will still follow this step by step PR review because what's better than one pair of eyes, two pair of eyes, right? And especially if one of them is human and one is LLM. (t: 540) The beauty of using LLMs is that they're very different than humans, right? If you completely delegate all of your reviews and code to LLMs, you will have a very bad time. But if you supplement your own skill set (t: 550) with an LLM, you'll be able to find bugs way faster. Cloud code can spot bugs that me and you can't because it's a large language model. It works very differently than a human brain. (t: 560) However, me and you can spot logic bugs that Claude might think it's a perfectly fine feature. So that's where using code to aid you in a pull request review is absolutely essential. (t: 570) So it performs stage one. It performs stage two, which is just switching to the branch of that pull request. And then stage three, it also gave us the instructions how to do it. (t: 580) So we already did that by testing out that this limit is enforced correctly. So we can go to stage four, which is the most advanced by far in this problem. So I'm going to copy it again, paste it into Cloud Code and say. (t: 590) Start with stage. Four. Then inside of GitHub, it just copied the file names of the files changed with this (t: 600) pull request and just paste it in, boom, and close code will follow our stage four prompt for pull request review, explaining the changes in each file. (t: 610) Right. So this file has plus 18 minus two. So pretty simple. You can read it through. But when it's like plus 150 minus 70, this really, really saves time. So let's see what was changed in idea service. (t: 620) OK, added truncation. Remove note update. Simple fix. OK, looks good. Boom. And this is what I mean. (t: 630) You can review pull requests so much faster than people who don't use AI. So next file. Boom. I'm going to copy it next. Paste in the file location and Cloud will begin working on the next file. (t: 640) Trying to understand these changes. So obviously, again, this is a pretty small change. 17 minus four. So we have a new import and we have some small changes. Added from the validation, clean implementation. (t: 650) OK, looks good. I can mark it as viewed and you get the point. This becomes especially OP on complicated long pull requests that would take you multiple hours to understand the review. (t: 660) With AI, you can massively speed this up. You can also improve your own understanding because you can ask Cloud code questions about the pull request, about the changes it's trying to do, (t: 670) why it's doing those changes and stuff like that. So personally, as someone who runs a team of four different developers, this single Cloud code workflow saves me hours every single week. All right. (t: 680) So I have 9% battery left, actually 8%. That's crazy. So I need to go fast. I need to go faster through the remaining three workflows. So the next one is Claudia. This is an open source project. (t: 690) I'm going to link it below the video. And this is a way to use Cloud code with a very nice UI. So let me show you what that looks like. In case you don't like the terminal, this is the perfect solution, right? So you can manage multiple Cloud code agents running in parallel, (t: 700) just like we did earlier, but you don't have to use the terminal. You don't have to use an IDE. With Claudia, this is a much simpler UI. Honestly, I might make a whole video on it because (t: 710) a lot of people are interested in a UI solution. The issue is that Anthropic might soon release their own UI because Cloud code is becoming insanely popular, right? In the meantime, this is a great solution. (t: 720) The GitHub repo includes all of the setup instructions for this UI, how to set it up, how to use it and everything else you need to know. You do need to install Rust. (t: 730) So that is a bit of friction if you've never used it. But overall, it's pretty simple. And again, I'm going to link it below the video. So if you don't like using the terminal and you want to use Cloud code in a nice UI, (t: 740) this is a very interesting project. And it managed to reach over 8000 stars on GitHub in just three weeks, which is pretty damn impressive. Workflow number four involves using MCPs. So inside of Cloud code, you can do slash MCP to manage your MCP servers. (t: 750) Right now, I don't have any configured right here. However, any app that uses MCPs, such as NA10, such as Curriculum, Vectal, Linear, there's like countless, (t: 760) GitHub, so many apps have MCPs, right, official ones. And there is like thousands of third party ones. But I would only recommend you use official ones. (t: 770) For example, inside of Vectal, you can go to your settings on the bottom left, advanced settings. And then if you're a Vectal Pro user or above, Vectal Teams, Vectal Ultra, you can get access to API keys right here. (t: 780) So all you need to do is click on create key and then you set up your MCP config file anywhere you want. Like obviously you can use it inside of Cursor as well. But this is a video about Cloud code. (t: 790) So with Cloud code, you can use Vectal to manage your tasks. So let's say right here, boom, I have a bunch of tasks inside my Vectal, like weekly team review, develop MVP technical architecture, (t: 800) a bunch of other stuff I need to do. You can just have Cloud code run on all of these. So instead of using like Todoist, ClickUp, all of these outdated task management tools, just switch your task over to Vectal, go to the bottom left, (t: 810) click on advanced settings, click on API keys, generate your API key for MCP, scroll down and just follow this two step process. NPM install.gmcpremote and then copy over this JSON schema either to Cursor or any (t: 820) other MCP client, obviously Cloud code included. And that's how you can use Vectal with Cloud code. (t: 830) Again, if you want to see a full video on this, let me know in the comments below and I'll happily make one. Now, Cloud code workflow number five is refactoring large files. So this is something that no one wants to talk about. (t: 840) But if you're vibe coding, you're going to end up with some large, messy code. Right. Files that are 500, 700, 900 lines. We even had a few files that were like 1.5K, which is just disgusting. (t: 850) Right. And nobody understands what the file does. So I have this prompt that is very optimized for large refactoring. (t: 860) And actually it might be a bit too long. So what I'm going to do, I'm going to use the code to explain this Cloud code workflow prompt. So I'm going to paste in this prompt. I'm going to say, give me a TLDR explanation how this file refactor workflow works. (t: 870) Be concise. So when refactoring large files, this is where most developers, (t: 880) especially most junior developers and especially most vibe coders go wrong. Right. It's easy to keep adding lines of code, (t: 890) keep adding complexity to files, but it's very hard taking a super long file, understanding what it does and breaking it down into two, three, four more modular, smaller, better structured files. (t: 900) That is very hard. And it's like not fancy, right? Everybody likes to vibe code these MVPs, whether it's like a web app or a landing page or like a 3D game, nobody likes to talk about refactoring (t: 910) and making your code base more modular. But as you actually start building an AI startup, you'll quickly run into issues if you completely rely on vibe coding. (t: 920) That's why this workflow, even though it's the last one in this video, it might be the most important because if you really want to build an AI startup and scale it to 10K a month, (t: 930) 100K a month and beyond, you will need to learn how to refactor. You will need to learn how to make your code base readable, modular, scalable and maintainable. And this Cloud Code workflow is exactly for that. (t: 940) So let me explain to you. The core philosophy is treating large file refactoring like a surgery on a live patient. One wrong cut can kill the whole system. (t: 950) And this is true. What's the point of a refactor if you have a severe bug or if a feature stops working or if you add five more edge cases, right, we want to do refactors in a way that keeps all core functionality (t: 960) and doesn't introduce any new bugs. So we do this in a three phase approach. Number one, safety net. Before touching anything, you need to write tests for 100 percent or close to 100 percent of behavior coverage. (t: 970) Usually 100 percent isn't really like this is like what product managers like to do. Right. They like to aim for 100 percent, but it's usually not efficient. (t: 980) So if you're like 80, 90 percent, that's already great. The core idea with the safety net is to establish everything how it works beforehand. Right. You can check out the code to alternate branch. (t: 990) Well, hopefully you're making a refactor on a separate branch, not on the branch that's in production or in staging or development, none like that. Large refactors have to be on a separate branch or even they have to be even (t: 1000) staged on multiple branches, right, depending how big the refactor is. But the point of the first phase is to really realize, OK, what's the stage of this? (t: 1010) How fast are our area features? Not only do you want to perform the test, you want to also track the features, the functions, whatever you're refactoring, how fast is it, how reliable is it before the refactor so that you have some baseline. (t: 1020) The second stage is surgical planning, finding complexity hotspots. What is the most convoluted, most complex piece of the code that makes sense refactoring first? (t: 1030) This is super essential with these large risk refactors. Actually, properly planning is almost as important as executing the refactor. (t: 1040) If you choose the wrong part of the code to extract, you're just wasting your time. And you might make your code base even worse than it was before. And you should definitely order by risk. What is the lowest risk first, right? (t: 1050) Something I like to ask myself, and this is a prompt I tell Claude Code all the time, identify like 50 to 150 blocks of code that make the most sense extracting first, (t: 1060) that are the safest to refactor, the safest to extract with the lowest amount of risk. So maybe there's just a bunch of styling or animation or some, you know, UI code that is very (t: 1070) isolated from the rest of the file from imported business logic. Then you should definitely extract that first. And by the way, an added benefit of making shorter files is that (t: 1080) LLMs and AI agents like Claude Code can work with them a lot easier. So this is not just, you know, to make your code base more modular so that human developers can work on it. (t: 1090) No, it's also for the AI. If you have a well-structured code base, Claude Code will be able to develop much faster with less bugs and will be able to understand files way deeper than if your (t: 1100) code base is a mess. And then the third phase is incremental execution. Rather than trying to one-shot everything in a single prompt and, you know, (t: 1110) just trying to do one big change, you do it one step at a time, right? You stage these changes. You do it in steps. You extract 50 lines, 40 lines, 60 lines of code, and you do tests after each extraction, right? (t: 1120) Is everything working? Did something break? Like, this is how you need to do it. You need to approach it really surgically and seriously. (t: 1130) You cannot just do Claude Code refactor this whole file. It's getting too long. If you do that, you will run into so many issues that you'll probably quit development forever. (t: 1140) And again, this prompt, just like all of my other prompts, workflows, AI agents, will be available, is available in the new society. So if you're serious about AI and you want to build your own AI startup, (t: 1150) you want to get to the cutting edge of using Claude Code, Curriculum Codex, and you want to maximize your connectivity, make sure to join the new society. It includes tons of exclusive content that takes the concepts I teach in my YouTube videos (t: 1160) to the next level. Plus, if you join during the month of July, you will get a month of Vectal Ultra completely for free. That's right. (t: 1170) Everyone who joins the new society during July gets Vectal Ultra for free, which is valued at $100. So don't miss this opportunity and join today. The link to new society is going to be below the video. (t: 1180) With that being said, thank you guys for watching. These were five Claude Code workflows that you have to start using. And if you want me to make more videos on Claude Code, just comment below. (t: 1190) That being said, I wish you a wonderful, productive week. See ya.

