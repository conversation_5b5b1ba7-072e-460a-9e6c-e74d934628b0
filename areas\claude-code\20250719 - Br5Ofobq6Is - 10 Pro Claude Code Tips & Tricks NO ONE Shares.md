---
title: 10 <PERSON> <PERSON> Code Tips & Tricks NO ONE Shares
artist: Better Stack
date: 2025-07-19
url: https://www.youtube.com/watch?v=Br5Ofobq6Is
---

(t: 0) I've been using <PERSON>'s code for a while, made a ton of videos about it and have come up with some tips that I honestly haven't seen anywhere else, like how to run <PERSON>'s (t: 10) code on your phone and get notifications. So let's go through my top 10 tips for using <PERSON>'s code. And before we do, make sure you hit that subscribe button. (t: 20) The first tip is to use hooks for <PERSON> to get your attention. There are many times I've asked <PERSON> to do something, I move on to something else. Then when I go back, it's been sitting there waiting for my input. (t: 30) But you can create a hook to trigger at notification or stop, and this will run any script or multiple scripts. (t: 40) So in my case, it will play this audio and also send a notification to my phone for when I'm away from my computer. Check out the link in the description to learn more about hooks. (t: 50) The next tip is to not be afraid of the dangerously skip permissions flag to skip all permission checks from <PERSON>'s code. Now, this option does come with a warning, but for the most part, I haven't had any (t: 60) issues with it. What I like to do is assign it as an alias so it's easy to trigger. And if I really want to turn it off, then I could press shift tab and it's gone. (t: 70) Also, pressing shift tab a few more times will take you to plan mode, so <PERSON> won't make any changes to your code base. (t: 80) Now with the dangerously skip permissions flag, <PERSON> will not ask permission to access sensitive files. So the next tip is to not be afraid of the dangerously skip permissions flag. The next tip is to deny <PERSON> read access to these type of files. (t: 90) You can do this on a global level or per project. Just make sure you have something like this in place. Tip four is to use think, megathink, or ultrathink in a prompt if you want to increase Claude's (t: 100) thinking budgets for scenarios where you want to perform deep reasoning on multiple files. (t: 110) Think tells Claude to use 4,000 tokens, megathink 10,000, and ultrathink almost 32,000. For the next tip. You've most likely created a ClaudeMD file which gets automatically pulled into context (t: 120) for a conversation. But did you know you can use IMPORTANT in all caps to add emphasis? (t: 130) So Claude will pay special attention to this. Use the at symbol to link a file. And you can use the hashtag or pound sign to add data to Claude's memory, which can (t: 140) be updated in the file or in Claude code. Since we're on the topic of things about Claude code you may already know about, did you know that you could add multiple arguments to a custom slash command? (t: 150) You might have already seen arguments in a file, but this also lets you chain arguments. And then you can tell Claude what to do with each of them. (t: 160) You can also nest slash commands in folders and use this syntax to reference them. Tip seven. If you've accidentally closed a conversation, you can continue from where you left off using (t: 170) the continue flag. Or if you've closed multiple sessions, you can use the resume flag to select which one to continue. But did you know you can press the escape key twice to go back through your conversation (t: 180) history and continue on from that point. This is useful for when Claude goes in a loop when debugging and you want to go back before that happened. (t: 190) Now the next few tips focus on extending Claude's functionality with external plugins. Like ClaudeSquad which lets you create different sessions on different Git work trees automatically. (t: 200) So you can see what each agent is working on. But you can also do this manually with tmux. Or VibeTunnel. This is a great way to create a terminal to your browser so you can access Claude's code (t: 210) on the go. For me, the combination of phone notifications and VibeTunnel has been something I've wanted out of Claude's code for a while so I could use it on my phone to check on tasks and get (t: 220) notifications. Now I'm sure in the future Anthropic will release a feature that can do this exact same thing. But for now, this will have to do. The final tip is to do with cost savings because let's face it, Claude can get quite expensive. (t: 230) And one thing that helps save you money is that you can save money on your own. So you can save money on your own. And that's why I've been using the ClaudeSquad app. The best way to save money is to use the clear command whenever you want to talk to Claude (t: 240) about a new topic to free up some context. But what's even better is to use the KimiK2 model which has very impressive benchmarks, (t: 250) is 80% cheaper than Claude and can actually be used inside Claude code. Check out this video to learn all about KimiK2. (t: 260) And until next time, happy coding. Hey, it's me again. If you enjoyed this video, go and check out BetterStars.com. It's an amazing product that I use all the time to help me write better, more robust (t: 270) software. And it's got a free tier, so you've got no excuse not to check it out.

