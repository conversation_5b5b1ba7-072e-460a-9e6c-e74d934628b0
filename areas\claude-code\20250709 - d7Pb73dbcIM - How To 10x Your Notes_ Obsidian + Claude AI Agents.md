---
title: "How To 10x Your Notes: Obsidian + Claude AI Agents"
artist: <PERSON><PERSON><PERSON>
date: 2025-07-09
url: https://www.youtube.com/watch?v=d7Pb73dbcIM
---

(t: 0) Hi everybody, my name is <PERSON> and in this video I'm going to teach you how to 10x your notes with AI agents. So the goal of this video is we are going to teach you how to use Clod Code, which is a new AI coding tool from Anthropic, and we are going to extrapolate its coding capabilities (t: 10) so that we can use something that I like to refer to as Clod Agent, which is we are going to teach you how to build flexible agentic workflows that you can apply in all sorts of different tasks (t: 20) that you experience day to day. We're gonna have timestamps on everything. This is gonna be on X, this is gonna be on YouTube. I also have an accompanying blog post on my Substack, so if you like this video, share it with friends, subscribe to my YouTube, (t: 30) follow me on X, share the Substack post, it goes a long way to help me out. I'm just gonna cover a few four to five minutes of like intro. You may just want to skip that, which is totally cool, (t: 40) and then we'll actually dive into those 10 workflow examples, and then we're gonna close so that hopefully by the end of this you are completely agent-pilled and you feel like you (t: 50) know how to use the Clod agentic experience to build workflows that can help you do any thing you want to do. So I'm gonna go ahead and start this video off with a quick intro. The rest of this video assumes two things, so these are gonna be steps where you just need to pause the video, and you can either of course just watch me do everything and then do this, (t: 60) or you can follow along. If you want to follow along, you need to make sure you have Obsidian downloaded. This is a free download. You can go to obsidian.md and you can get your free desktop (t: 70) version of Obsidian. Very easy to get started, so make sure you do that if you want to follow along, and then you will also need to make sure that you have Clod code installed on your computer so that (t: 80) we can take advantage of all of the agentic workflows that we're about to demonstrate here. So you can go to docs.anthropic.com. It's very easy to get to the Clod code docs, and what you want to do is go to the Clod code tab here, and you just want to go to overview here, and you're going to (t: 90) find this command. You just need to run this command in a terminal on your computer. If you're somebody who's not technical, right, you might have seen this video, oh, taking notes, let me (t: 100) figure this out, and they'd be like, oh great, it's a developer telling me how to run terminal commands. Don't be intimidated. This is a very easy setup. You can use your AI chat of choice. (t: 110) If you, you know, if you're a chat GPT user, if you're a Clod user, if you're a Gemini user, just copy this entire page. Anthropic makes it very easy to do so. You just copy that page, paste that into the AI app that you like to use, and ask, how do I do this? It's very easy. You (t: 120) just need to open the terminal up on your computer, copy this command here, and install it, okay? Super, super easy. The one note, the only thing you will need to pay for, of course, (t: 130) is you do need either a Clod plan from Anthropic, or you need a Clod API key. Clod plans start for as low as $20 a month, so if you do want to actually follow along, (t: 140) get started, and for some reason you don't have a Clod plan, you will need to go to Anthropic and actually sign up up here on the top right. So the two things that you're going to need to do before following along, again, you're more than welcome to just watch this, but you need (t: 150) to get Obsidian downloaded, and you need to make sure you get Clod code installed with a corresponding Clod plan. Then you're going to be all set to follow along. Before we get onto our (t: 160) workflows, we're first going to create a new vault in Obsidian. So you can see, I just have a new window of Obsidian here, and you can see I have the create new vault. We're going to click create, and we're going to create a demo vault, because what I'm going to do is a first, the first kind (t: 170) of these workflows are going to be things that I recommend that you do when you get it set up, particularly this first one. So I'm just going to call this Clod code demo. I'm just going to put my (t: 180) vault on my desktop here, but you can put this anywhere. We're going to go ahead and create a new vault. We're going to see the Obsidian app open here. We're going to go full screen here, (t: 190) so we can kind of explore a little bit. So the core experience of Obsidian is text files. So you'll see over here on the left, we have our file explorer. So I could go ahead, right click, (t: 200) create a new note, McKay, right? We could say hi, everyone watching. Okay. So all of these files here are just text files. So if I actually just open up this McKay file in Finder here, you're (t: 210) going to see, this is just a Markdown file. Okay. So I can do whatever I want with this file. I can move this to another device. I can use this in a different app. I have complete ownership of this (t: 220) file because it is just a text file in Markdown form. You'll also notice on the right, we have the graph view. So some of you are going to be into this kind of thing. Some of you are not. I do (t: 230) think that knowledge is important. I think that it's important to have a lot of knowledge in your knowledge bases, get really fun over time, right? My core Obsidian vault that you saw in that preview pane a few seconds ago, that was all years and years and years in kind of the making. And my (t: 240) graph view is very structured. So I have this knowledge base with all sorts of connections. And the great news is with something like Cloud Code, you can actually automate quite a few of the linking and tagging files, which we'll get to in the workflow section. (t: 250) But one of the things that you're going to want to do as well is you're going to want to make sure, of course, that you did get Cloud Code installed. And one of the things that we're going to want to (t: 260) do here is you can see we have our vault in the bottom left. So it's really nice because I can quickly switch between different vaults. I pretty much just operate out of this main vault. But what (t: 270) we can do is we can actually right click this and reveal in Finder. And what we can do is we can actually right click again. I'm on Mac. Some of you, of course, are going to be on Windows or Linux, but I'm just kind of operating with Mac here within those constraints. So just do whatever (t: 280) is the corresponding function on your particular operating system. But I'm just going to click new terminal at folder. Okay, so I just right clicked over my vault, new terminal at folder. (t: 290) Let me just drag in my terminal here. Okay, so we're going to put our terminal over here on the left side of the screen. And we're going to keep Obsidian over on the right side. What we can do is we can just run Cloud on our vault. And now we have a full instance of Cloud Code working on our (t: 300) vault. Now I can say something like, Hi, what's in the McKay file? Just verify the cloud does in (t: 310) fact work. We click on our file explorer, we'll see we have a McKay file. You can see the Cloud Code is now intelligently searching. Okay, you can see it found the mckay.md file. And then we can (t: 320) see that it said Hi, everyone is watching. Alright, so at this point, we're all good to go. Now let's jump into the workflows. Workflow number one is going to be about clouds rule system. So (t: 330) the very first thing that you're going to want to do once you have cloud code running is you're going to want to type slash init and you're going to see this init command pop up, you're going to see that it initializes a new cloud.md file with code base documentation. So that's highlighted, (t: 340) we're just going to hit return. And what's going to happen here is cloud code is going to (t: 350) be called all caps.md. This is going to be the rules file that is essentially injected as the system prompt to every single request that you make with cloud code inside of your vault. Okay, (t: 360) so this pertains to every single folder, every single file, it's basically going to be the very, very top level prompt. So if you have anything, you can see cloud code is asking me for permission to (t: 370) do things here, I'm going to go ahead and say yes, you have permissions is another one of those things you'll start to learn over time is the agentic nature of cloud is very powerful, but it's (t: 380) called and you can see it's now asking me for permission to create a new file. So if I accept yes, here, you're gonna see I have three options. Yes, yes. And don't ask again, the session, no, (t: 390) until cloud what to do differently. So if I selected three, I would get an opportunity to kind of give it some additional text, kind of prompt it in a different direction. In this case, this looks pretty good. And we're just going to kind of roll with it just for demo purposes. In (t: 400) the case of your actual personal vault, you're going to want to create this file, you're going to want to kind of go in here. And you really do want to actually take the time to customize this (t: 410) to your liking. And you're going to want to go in here and you're going to want to go in here and start editing, because this file is going to have a significant, significant impact on the quality of responses, how cloud actually kind of uses your code, or your not your code base, I'm (t: 420) so used to working code, how it works with your notes inside of your vault. So you can make this as specific and personalized as you like here, we're just going to kind of roll with what it gave (t: 430) us out of the box here, we can kind of assume because it's a fresh vault, and we did in fact, tell cloud it was a fresh vault, these are probably going to be pretty good. And we'll come (t: 440) back to this more later. As we try some of the other workflows, because we'll show you how you can kind of use those workflows in conjunction with updating this rules file to get better for performance. But anyways, (t: 450) the thing that you want to do with the very start of your cloud code experience in obsidian, that you just want to make sure that you use that slash init command, you want to let cloud create (t: 460) this cloud.md file at the very base root of your project folder of your vault, so that these rules okay, so if I actually exit cloud code here, clear my terminal here. (t: 470) If we were to initiate a new session, what actually happens is now this prompt gets injected into this new session. So this now applies to every single session, which is pretty cool. workflow. Number (t: 480) two, here, we're going to boot up a new session of cloud code. Again, just as a reminder now, everything that is in our cloud.md file here is going to be injected inside of our prompt. (t: 490) So cloud is now going to follow these instructions, just want to keep one of the things you're going to notice as we go from workflow to workflow. So a lot of them build on top of each other. So you (t: 500) want to bring that up one more time here but we're working in what is called cloud codes interactive mode so this is very chat based you know you kind of saw me working with it in the last workflow section but this is a very natural kind of ux paradigm right text in text out you just ask the (t: 510) agent what you need and it does it right very simple and it just kind of operates this entire (t: 520) thing inside of the terminal which is just a little bit different you know than the web browser or the app chat gbt or cloud or gemini experience that you're already familiar with it's just this time we're doing it with cloud code as a command line tool inside of the terminal here so let's (t: 530) just go over a couple of kind of the core workflows in standard chat a little bit more of a boring section here but this stuff is important so if i just say hey pick a random topic (t: 540) right we're just chatting with cloud code naturally like we would in the chat gbt interface for example and in this case we're going to have chat or work on cloud code rather pick an (t: 550) interesting topic here the fibonacci sequence and now i can say awesome please create a new note about that what's going to happen here is cloud code is now going to write a new file to our (t: 560) obsidian vault here and it's actually going to create this file it's writing this you can see it's actually updating the tokens in real time so it is actually kind of writing this under the hood (t: 570) you're just not seeing it stream in like many of you are probably familiar with and then you can see is asking us for permission to create the file again the great thing is if i wanted to allow it (t: 580) to always create files i could go into my rules file and i could actually allow it to do that in a different way so we're just going to accept this manually and now you can see we have a (t: 590) fibonacci in nature file here right here inside of our vault so one of the great things about obsidian obsidian because it is all markdown files ai models are super super good and markdowns here (t: 600) always going to get really nicely formatted files it's always going to have a really good idea of how to kind of construct things if we go to read view we can kind of see it in a little bit a nicer (t: 610) preview here like you would normally then we can kind of go back to edit mode and edit this file but one of the things you'll notice like at the bottom of the screen here is that you can kind of see that the file is already in the format and you can see that the file is already in the format and you can see that the file is already in the format bottom here you'll see we have tags (t: 620) this is how you get interesting things like knowledge based automation so one of the things you'll notice in our cloud rules file file um cloud rules file here is we have things (t: 630) like tags so if we wanted it to never have tags we could do something like you know do not use tags right and now our rules file is updated and it wouldn't use tags anymore so that's like a (t: 640) really lightweight example of how you could customize this rules file to make sure that cloud code is writing notes in the style in which you actually wanted to write that we're going to undo that and then just go ahead and do that and that's kind of the general idea of how you could that for now because we do just kind of want to demo some of the different things you can do (t: 650) but now cloud code has kind of written almost this like basic Wikipedia entry style um article here inside of our obsidian vault here and we could do things like you know see this welcome (t: 660) file we actually don't need the welcome file anymore and what you'll notice is Claude is going to ask me for permission to remove it here you can see it's actually searching first to find (t: 670) the welcome file right you can see how Claude's search capabilities can get really interesting because imagine you know something like my own personal vault that has like thousands of files (t: 680) at this point it can actually just dynamically search that entire thing without you having to tag it here or you know have to kind of manually provide a file path you can see it asking for me (t: 690) for permission it found the file we're going to say yes and you're going to see that file disappear okay pretty cool stuff now if we wanted to tag a specific file say we wanted to go back into that (t: 700) Fibonacci we can actually just type an at we can kind of start typing we're going to see the Fibonacci here I can hit tab that's just going to auto complete that and we're going to say um can you add an interesting fact section at the (t: 710) bottom but above the tags and now we're going to see it actually go and edit the specific file that (t: 720) we tagged okay so we tagged that Fibonacci file but you'll see it will in fact ask us for permission for the edits again I'm going to keep repeating myself here for a little bit and then I'll stop (t: 730) but you are going to want to approve the file you can see all of these green lines here those are files that are being added if you ever see red here those files those lines those characters those are getting removed so we're going to accept that you can see we now (t: 740) have an interesting fact section that is right above the tags like we asked for now so if we wanted to clear our message history say we started working on a new task or something what we can do (t: 750) is we can just type slash clear I'm going to tab to auto complete that hit return and this is going to clear my session and boom I am now ready for a new chat okay so a few things okay cloud code can (t: 760) also create folders okay so if we wanted something like create a math folder and put the related files (t: 770) in it one of the things you'll notice is we actually cleared our message message history so it doesn't actually have any memory about the Fibonacci in nature okay so what's going to happen (t: 780) is it's actually going to realize oh Fibonacci sequence kind of related to mathematics here so what we're going to do is we're going to create a new folder and we're going to put that file in (t: 790) it and it's intelligent enough to understand that it needs to do that and not only is it intelligent enough to do that in this setting where we just have literally two files in here but it can do that as your vault grows so this is again the great thing about an agentic system is (t: 800) cloud code is really just an agent here that's really good at knowing when and how to use tools in this case it's searching for all sorts of different mathematically related operations here (t: 810) is doing this agentically we could actually hit control R and that will actually expand if it looks like I need to hit control E you can see a lot more insight into what's going on here we're (t: 820) just going to toggle that off with control R we're kind of now back to our familiar format here and you can see it's now trying to run make deer which is going to make directory okay it's going to create that math folder which we accept (t: 830) and then it's going to do one more operation here to put that file inside of that folder okay and boom now our Fibonacci in nature file is inside of that folder and we did that dynamically without (t: 840) having to specify that this is the file we wanted right cloud code is very smart it's kind of able to figure out what you need the goal is you just want to prompt as clearly uh in kind of directly (t: 850) as possible it's very cool workflow number three we really want to emphasize the importance of using a speech to text tool so (t: 880) this one's kind of optional but I promise this one is going to be life so this is the speech to text tool that I use it's called whisper flow the product specifically now is called flow you can see the URL up here but all you do here is you're just (t: 890) trying to increase basically the amount of tokens that you can output to the AI so this is just going to speed up your workflow you can speak tokens you can speak words about four times faster (t: 900) than you can type them so if you're somebody like me who's basically working with ais all day every day these days it's just much more efficient if you're using a speech to text tool I can just kind (t: 910) of just basically to the AI without having a hammer on a keyboard so we're going to skip installation here there's a little bit of a setup here you don't even necessarily have to use this (t: 920) tool in particular maybe maybe your native device has speech to text functionality maybe you have a different tool the importance is just that you use a speech to text to one I just wanted to kind (t: 930) of highlight the one that I use so the reason I use flow here is because a has a really nice Mac and a really nice iOS app so as kind of an Apple guy as you're probably catching on to here it's (t: 940) pretty easy for me to kind of sync my whispers or flows or whatever the I actually don't know what (t: 950) the unit is they officially call them here but one of the things that's common for me is like maybe I'll go on a walk I'll record the you know use the mobile app to record some of my thoughts then I'll (t: 960) come back on desktop and I can just like paste things into notes and then have the AI organize it and things like that but in this case what we'll do is we'll just jump back to our Obsidian on the right cloud code on the left set up here and what I'm going to do is I just hold down my (t: 970) function key on macOS again the reason I love flow is just because user experiences are going to be really great this isn't like sponsored or anything um uh if anybody from flows watching this maybe we'll get you guys some free credits or something um but I'm just going to hold down my function key (t: 980) and then I'm going to talk all right Claude I want you to create a new note it should be your (t: 990) three favorite dinosaurs and why okay we're just going to send this off and you can see that was very quick it was very instant all I had to do was hold down my function key and speak and this (t: 1000) isn't the greatest demo here this is particularly useful when you just kind of want to do something like a ramble for a while so you can see we just accept that we now have three favorite dinosaurs from Claude here but one of the things especially in like my coding workflows is I'll have these (t: 1010) sessions where I'm like rambling about a feature I want built for like three or four minutes and I can just hold my function key I don't have to type I don't have to really like think through I can (t: 1020) just dictate to the AI and Claude code is powered by smart enough models that kind of figure out what you need even if the speech is like a little messy or jumbled so very very nice way to just (t: 1030) see people get hooked or people try this they get hooked immediately this is a total game changer to (t: 1040) not just your workflow with this particular workflow but any time that you're actually interacting with an AI especially if you coders out there are working with Claude code in actual code format super unbelievable tool definitely recommend you try a speech to text tool to (t: 1050) increase the amount of tokens you can output workflow number four so you're probably wondering wait I thought we were using Claude code wise cursor here so we're going to talk about how (t: 1060) you can actually use cursor's tab feature which can be really really powerful here um if you want to kind of get a little crazy here and use a code editor as your notes editor so (t: 1070) let's go show you kind of how to do that and I'll explain how this can actually make sense in practice one thing that's worth noting is really quick before we do that is that this is probably the most optional of everything that we talk about here so I know the speech to text was also (t: 1080) optional I think speech text will totally change your life I think this one's a little bit more niche this is something that I do like to use especially when I'm very frequently iterating (t: 1090) on a specific file inside of my vault some of you who are non-coders may not want to work in a code editor which is totally understandable but nevertheless we kind of want to show you why cursor tab can be useful for something like (t: 1100) Obsidian so we're going to go back over to our Obsidian here what I'm going to do is I'm actually just going to right click on my vault we're going to reveal this in Finder now this is the important (t: 1110) thing about Obsidian right is all of these text files you own they're on your computer you can use these in any app you want so say I was working in this three favorite dinosaurs or something what (t: 1121) the world of Cursor is called though I'm actually going to drive that up inside of Curcer and work (t: 1130) like that or you can open the entire vault inside of Cursor in this case I'm just going to open up this specific file that I want to use so I'm actually just going to drag this on a cursor again I'm kind of assuming that you have a cursor open here so I'm just going to drag this file down (t: 1140) here you're going to see we have our three favorite dinosaurs MD okay again one of the great things about markdown files they translate very well to code editors so cursor does in fact (t: 1150) stopping you from doing that. I particularly find that cursor tab can be very useful. So cursor tab is cursor's native autocomplete feature. So one of the things you'll notice is if I change this to (t: 1160) four, this is going to start using cursor's tab model. So if we actually just do return, I don't even have to do return. This is how great cursor tab is. You can see it's actually just populating (t: 1170) a new dinosaur here. And now I can just hit tab, hence the name of the feature here. And that's just going to autocomplete my file. Now, if I save this, you'll see we have a little dot. If I save this and drag this window out, we go back into our obsidian. You'll notice that this file is saved. (t: 1180) Why is that? Because this is just a single text file. It doesn't matter where we have it open on our computer. You could even build your own user interface for all I care to open up markdown files. (t: 1190) The point is that every single one of these text files you own, you edit at one place, it gets edited the other place. Everything syncs well. So if you're somebody who wants to like do a lot of (t: 1200) editing in a file, right? Cursor tab can not only do creates, but it can do edits. It can do deletes. So if you're working on a really long note, right? Say we wanted, this to be bullet points, for example, instead of having to go and make every single one of these (t: 1210) a bullet point, it's smart enough to kind of figure out what I'm doing. And now we just can kind of like tab through this and boom, I didn't have to do that manually. Now I can save it. I (t: 1220) could of course, just ask the clot code to do that same thing for me. So if we drag this out and kind of open our terminal here back open, uh, we can say, please convert and we'll actually tag that (t: 1230) file. Okay. We're starting to tag. We're starting to kind of pile these features on top of each other, which starts to get more of the workflows get really interesting. So if I want to do a lot of things here, we can say, please convert the bullet points to numbers. And boom, we'll send (t: 1240) this off the clot code and it's going to be intelligent enough to go switch these bullet (t: 1250) points back to numbers. Uh, so this is just an example, really, really lightweight of using cursor tab. But I think if you start to use your imagination, you can see where that gets interesting. Again, very, very optional here. I know a lot of people are like, well, I don't want (t: 1260) to pay for all these different products. I want to kind of consolidate. So no need to do that. But a lot of people who are already using clot code also have to have cursor plans. If you're somebody like me, uh, you can even, if you want open your entire vault, open inside of cursor, (t: 1270) open up a terminal tab inside of cursor, use clot code inside of cursor while using tab, right? You can really get pretty wild with some of these workflows, but we just wanted to show (t: 1280) you a little bit of a simple version of that. So let's move on to the next one. And workflow number five, we're going to talk about plan mode. So plan mode is accessible inside of clot code. If you do shift tab, once shift tab is going to do auto accept edits on. So let's actually just (t: 1290) do a quick little side bonus tip here. Auto accept edits on accepts everything that clot does. So you've seen me kind of approve clods actions. If you don't want to manually prove that you can (t: 1300) turn auto accept edits on and just going to accept everything. Now, this can obviously be a little bit dangerous. Um, I recommend not turning this on until you've kind of coordinated the permission (t: 1310) system inside of clot on your own a little bit, or at least just gotten familiar with how it works so that you can kind of keep an eye on it. Uh, cause you know, you don't want it to do things (t: 1320) like editing your entire, uh, you know, obsidian vault with all these notes you've diligently kept, right? Uh, so you want to be a little bit careful with that one. So one of the nice things that you can do to help it stay a little bit more on track though, is if you hit shift tab one more time, (t: 1330) you're going to see plan mode turns on here, kind of in green here, at least with the theme that I have running here. What plan mode does is it actually make sure that clot code doesn't do (t: 1340) anything. It doesn't perform any actions. It's just going to take your prompt, create a plan, and then you have an opportunity to either approve that plan or reject that plan. Let's go ahead and (t: 1350) take advantage of our speech to text tool here and give it a little bit of a basic prompt. And then we can kind of see how plan mode works. I want you to go to the anthropic docs, and I want you to figure out how the cloud code SDK works. I'm interested in building an express (t: 1360) server to build, uh, automations with the cloud code SDK. Okay. So you can see that is now inside (t: 1370) of my prompt here. I send this off to cloud code and because we're in plan mode, cloud is going to kind of think through this and reason throughout things. One thing that you'll notice too here is (t: 1380) we actually have access to web search. So cloud can search the web, which is kind of a nice bonus little workflow tip here. Uh, as we're going to actually take advantage of number two here. So anytime now cloud will not ask permission for going to (t: 1390) the docs on anthropic.com here. You can see we get a nice URL preview. So now, because I've selected (t: 1400) that option, it can just autonomously do that without having to ask me for permission every time, which is particularly useful in this research case, because it's probably going to go through a different, a few different URLs here to figure out what's kind of going on with this SDK. (t: 1410) So a couple of bonus tips here already, right? We've gotten the auto accept mode. Uh, and then we've got the auto accept mode. So we can just click on that and it'll take us to the (t: 1420) website and it'll take us to the web search abilities. So cloud code does have quite a bit of a feature gap to it. If you really, really want to get crazy here, but we can see it's just (t: 1430) trying to figure out what's going on. It's not writing any files, right? It's just doing research. So note taking of course is a very broad discipline. I kind of batch research in with note taking. You know, one of the things that I do when I'm writing code is I do a lot (t: 1440) of research. I like to take it a lot of notes in my projects. So inside of my vaults, you know, I have a folder for my projects. Or something, figure out how like a certain API works or a certain software library works. (t: 1450) I can just hand that right off the cloud code can go do that research task autonomously while working on something else. I can have plan mode on so that I can kind of come back and make sure that all of the things that it writes directly to my vault, I kind of (t: 1460) approve all that sort of stuff. So let me just kind of sit here for a second and I'll speed this up once it's done with its research task. And we'll take a look at plan mode. (t: 1470) Looks like cloud code is done with my plan. I can kind of scroll up. You can see it's boxed in here, which is sort of the user interface inside of the terminal. Yeah. Yeah. It's really done a quite a good job with trying to make the user interface inside of (t: 1480) the terminal code, despite it just being a terminal based program here. So you can see we're given two options. Yes, which this is just going to accept the plan and no, which is keep planning. And what I can actually do is I can give it a little bit more feedback (t: 1490) here. So what's going to happen here is we should just kind of read through this. So what we're going to do is we can see, okay, cloud code SDK allows you to run cloud code as a sub process. Okay. Everything here looks pretty good. So what I'm going to do is I'm (t: 1500) going to hit no, keep planning. What we're actually going to do is we're going to say, note is documented in a cloud code SDK note. So we're just going to make sure it knows, (t: 1510) Hey, we actually want to take all the research that you just did. We actually want to add it to our obsidian vault. Of course, it's not going to add that yet because plan mode (t: 1520) is still on as indicated below the text input here. And you can see we would now like it to proceed. So let's go ahead and hit yes. And one of the things you'll notice is it actually turns auto accept mode on by default. So we can actually shift to tab back to regular (t: 1530) here. You can see cloud code is actually tracking its own internal to do system, which is kind of cool. So what's going to happen is it's probably (t: 1540) going to ask me for permission to create the note. I'm actually just going to go back to auto accept edits on just to show you how that works here. Cause it's kind of one of the little bonus steps we're going to do in this workflow number five here. So we can (t: 1550) just kind of let this thing work for a little bit here. I might speed this up just a little bit. So you guys aren't watching forever. So as you can see here, cloud code is now auto accepting the file creation here. It wrote to that file. It's kind of tracking (t: 1560) its internal to do system. We can see over here in our vault, if I click on cloud code SDK, we now have this entire documented kind of explanation of how to use the cloud code (t: 1570) SDK with an express server. You can see, we even get things like code blocks again, cloud code, very, very good at using Markdown. Like all AI models are these days, you can see (t: 1580) it is now done with the task. We were able to kind of figure out what we needed from it. You can do this with much more complex tasks, of course. So if you want to use plan (t: 1590) mode for something that has like a dozen plus steps, totally can. You can just continue to iterate on the plan back and forth with cloud code and tell it, you see it's going to configure to your liking, hit that yes, accept plan button. And then you can either (t: 1600) auto accept everything it does, or you can kind of improve it manually one step at a time. Obviously in this case, we did auto accept edits, but that's plan mode. That is a way to kind of plan complex tasks really comes in handy with things like researching (t: 1610) things in your vault, like organizational tasks where you really want to make sure cloud is going to be doing the right thing. A ton of opportunity here. Again, one of the things (t: 1620) that's great about cloud code is just how flexible it is, right? Obviously it's powerful, but it's also very malleable and flexible. You can pretty much get it to do whatever you want. And one of the best ways to get it to do whatever you want is to use plan (t: 1630) mode. Workflow number six, we're going to talk about custom commands. So this is very, very powerful. A custom command inside of cloud code is basically like a reusable prompt that can be very detailed and it can kind of outline to cloud code what task you want (t: 1640) done. So if you have things that you're doing very often inside of your notes, this can be something that's very powerful. So for example, in my own workflow, one of the things (t: 1650) that I do is I have kind of a daily template. Then one of the things that I do now with cloud code is setting, having to go like copy my daily template. So I can kind of do a daily template and like paste it in and configure it with some kind of starter information based (t: 1660) on the day. I just use the daily dash template, which of course isn't here because it's not in this vault. And then what I'll do is I'll just kind of use my speech to text tool, talk about some of my goals, like, Hey, I want to get X, Y, Z done today, right? Use speech (t: 1670) to text. And now I kind of combine those two things into a reusable command, this daily template command. And that goes, it creates my new daily template. I have some instructions (t: 1680) on like where I want that organized, how I want that formatted, all that kind of stuff. So what we're going to do in this section is we're going to show you how you can actually create one of those on your own. This is going to get a little bit weird. And I apologize (t: 1690) for that just because of a little bit of a jank in how obsidian works. So if I actually drag in my finder here, again, the way that you can access your obsidian vault folder, (t: 1700) kind of at the very parent is you can just click the right click rather than the bottom left of obsidian here and just reveal and finder. That's going to pop up in your vault. (t: 1710) So you just want to make sure you go in your vault. And one of the things that cloud does is it actually creates a dot cloud. Okay. So I'm going to go ahead and create a dot cloud. And I'm going to go ahead and create a dot cloud folder. So if I do a shift command dot here on Mac, this is going (t: 1720) to show hidden folders, the convention, at least on Mac, I'm actually not sure how it is on windows, but if you have folders that are kind of prefixed with a dot, it will actually (t: 1730) hide them in the finder by default. So this can get a little bit tricky for things like get for things like a dot GitHub folders, which is very popular. If you're a coder obsidian in this case has a dot obsidian folder that hides with a bunch of different configuration (t: 1740) stuff. And then cloud, of course, has a dot obsidian folder that hides with a bunch of different configuration stuff. And then cloud has a dot cloud folder that just has the dot cloud folder. So you're actually going to need to edit this outside of obsidian, which (t: 1750) I know is like a little bit janky, not the best workflow, but Hey, what can you do? Because in obsidian and the file Explorer, it hides all these dot folders, just like Mac does by default, but there's no way to show them. There's a little bit of an interesting design (t: 1760) decision, but that's a conversation for another day. So what we want to do inside of the dot cloud folder is we're actually going to create a commands folder. So what we want to do here (t: 1770) is we're going to create a new folder called commands. And I actually just need to drag that inside of dot cloud here. So what your file structure should look like is you should (t: 1780) have your vault case. We have the entire vault here. We should have the dot cloud folder, which at this point you should now have a cloud should have created this for you by now, but if you don't have it, you need to create a dot cloud folder. And then inside (t: 1790) of that dot cloud folder, you need a commands folder. So this is where we're going to be creating our new command. So what I'm actually going to do is I'm actually just going to drag this folder open inside of cursor. We're going to be using cursor as our text editor. (t: 1800) So you can use, again, you can use any text editor that you want. You can see, we just have this commands folder open. So we are just going to be creating new text files and (t: 1810) all commands are also Markdown files. So if we do something like, um, get time, get dash (t: 1820) time that Markdown, we could just create a heading, get time. And we'll just say, please, please get me the current time. Right. And then what we could do here, you'll see if (t: 1830) we go back. To our, uh, finder here, you'll see, we have that file of course. And now what we can do (t: 1840) is we can do slash, but wait, we actually have to restart our cloud session. So this is something I see that people do all the time is they'll create a new command and you actually have to exit out of your session, start a new one. And then if we do slash, (t: 1850) we actually now get all of our custom commands of slash commands. So now you'll see that that get time command is in fact loaded. So what we can do is we can just start typing get music. It's already highlighted. So we can just tab auto-complete that and send this (t: 1860) off and it's going to go ahead and get the command. So you can see it's Monday, July 7th. Um, I'm trying to get a couple extra, uh, sneak, (t: 1870) sneak a couple extra workflow videos in here before I post this tomorrow. Um, but, uh, so you can see that's like the most very basic version of a command. That's obviously not particularly interesting. So what I'll show you is I'll show you something that I use, (t: 1880) right. Which is like that daily template command. So I can do daily dash template dot markdown. Again, all of these have to be marked down files inside of the commands folder, which (t: 1890) is inside of that dot cloud folder. And the reason that we're having to edit. This inside of cursor, again, you can use any app you want is because obsidian hides those. So we can't actually directly, uh, do that in obsidian. So for daily template, (t: 1900) I would do like daily template and, you know, maybe we want, uh, this, um, little, you know, we'll just accept whatever cursor tabs, uh, going to give us today. And what I can do (t: 1910) here is I can also at the very start, say you are given the following context. This is something I do all the time. Claude actually has a way to give the context that you give (t: 1920) to a command after you use the slash. So if I say hi there, right, if we want this high there injected into this command, what we actually have to do is we actually have to (t: 1930) use this dollar sign arguments and all caps here. We want to make sure we spell that correctly of course. And what's going to happen is that (t: 1940) high there would get injected right here. So it was very common in my own personal commands, right? As I'll give it a heading of whatever the name is. And then I'll say you were given the ball in context. I'll include that dollar sign arguments keyword here. And then I'll (t: 1950) actually go about what I need to do. So you can see that I've got a lot of commands here. I need. So you can do all sorts of things in here, right? You can say, you know, one of the things I could do is I could say like, please create this in the, and we'll call (t: 1960) this, you know, daily dash updates folder. We could save this and then we could have (t: 1970) this like basic template. So what's going to happen now, of course, because I created a new command, we do need to reset our Claude session here. And now I can do slash daily template and now I can type here. I can speak here with my speech to text tool. And I could (t: 1980) say, Hey, I'm super blocked by the last few lessons of my Claude code obsidian workflow. (t: 1990) And what's going to happen is we can send this off. This is going to actually get injected into my prompt here, right? You can just think of commands as prompts. And what's going to (t: 2000) happen is it's going to create a new file inside of daily updates. And you'll even notice our bolt doesn't have daily updates. That's totally fine because Claude can do that all (t: 2010) in one operation. And now we have this new daily updates folder here. Uh, and I'm going to go ahead and type this in here. I'm going to type this in here. And, and let's say, you know, we wanted to go look at that. You can see those blockers are now injected right here. We got the title. Uh, we got kind of the daily update for January (t: 2020) 8th and we could be as specific as we want, right? You'll see like, this isn't a very clean date format. So one of the things that we would do inside of our command, right. As we would just specify, Hey, this is how we want the date. This is how we want things (t: 2030) formatted. This is where we want that file created, right? Maybe, you know, we haven't talked about MCP yet, but maybe we have some MCP connections and we can say like, Hey, you know, pull from this MCP tool or use this tool inside of that connection, right? You (t: 2040) can get super crazy. With commands, commands are incredibly composable and powerful. I'm just showing you the very, very, you know, we're just scratching the surface here. So commands are, think of them (t: 2050) as like reusable prompts, the kind of recipes that you can give to cloud code for things that you're doing all the time, right? In my own personal coding workflow, right? I (t: 2060) have commands for things like generating plans, things for using get operations, all sorts of stuff. So that's one of kind of the most powerful workflows you can really go crazy on. And I highly recommend you experiment with it. In workflow seven here, we're going (t: 2070) to get into things like automated tags, linking. And organization. So this is a really, really powerful one, especially for those of you who are super into things like knowledge bases. If you really like things like Wiki style (t: 2080) links and tags and your notes, if you want to kind of make more connections across your different files, especially as your vault grows. I know that's something I really like (t: 2090) because you know, as your vault grows and grows and grows, it can be difficult to kind of maintain that knowledge graph, which, you know, I'm somebody who can definitely get a little bit obsessive over my knowledge graph. I just think it's the compounding effects (t: 2100) of it over years and years. It's really, really cool. But one of the things that you can use cloud code for is to help you do a lot of that more automatically. So I'm going to show you an example of this for tags, but you can also (t: 2110) extrapolate this for Wiki style links and for file organization in general. So one of the things that we're going to do, and I highly recommend that you do this because (t: 2120) this kind of sets the foundation of your system is you want to instruct cloud code to create a tags file for your tagging system. So what we're going to do here is we're going to create (t: 2130) a little bit of a prompt to cloud code. We're just going to kind of work through what a really, really basic V1 version of that would look like. So I'm just going to say, please create a, and I do this in all caps tags.md file at (t: 2140) the root. This just means at the very base level of the vault in the file, you need to define rules for our tagging system with hashtags inside of our obsidian vault. (t: 2150) Okay. And what's going to happen here is cloud at the very base of our vault. (t: 2160) It's going to create this tags file. This tags file is very nice because now any time that we want cloud code to autonomously (t: 2170) handle tagging in any of our files, in any of our folders at any time, we can always tell it to kind of reference our tags.md file. And this is something that we want to keep updated. (t: 2180) Not only can we keep it updated, but cloud code can keep it updated. So we're just going to kind of create this right away. And you can see, we now have this tags file. (t: 2190) So when you are creating this for your own system, you're really going to want to make it a little bit simpler. Okay. So let's go ahead and do this. So let's go ahead and do this. So let's go ahead and do this. So let's go ahead and do this. So let's go ahead and do this. So let's go ahead and do this. So let's go ahead and do this. We're going to create this as a file. And you're going to want to put in the time to actually curate this, of course, according to how you like tags done in this case, we're just going to kind of accept the defaults, (t: 2200) but this is very similar to the kind of cloud rules in that tags is almost kind of the grounding layer of our tagging system. So you can see it came up with a bunch of different things. (t: 2210) Um, you obviously, again, customize this to your liking, but one of the things that you want to do is you want to now tell cloud code that in its kind of cloud file, like, Hey, anytime you go about doing tagging, make sure you reference this tag file. (t: 2220) this tags file because this tags file is kind of the source of truth. So I'm going to use speech text here and just do this autonomously with cloud code. Okay. (t: 2230) Now we need to make sure to update our cloud.md file here. That's the rules for your system prompts. We just need to make sure that anytime you go about tagging, you reference tags. (t: 2240) We want this to be the source of truth for our tagging system. And we want to make sure that you also keep this updated over time. I'm just going to send that to cloud code. (t: 2250) And what's going to happen now is it's going to actually dynamically alter this cloud.md file here. So you can see it's creating a very basic little to do system here. It's going to ask us permission to edit the file. (t: 2260) It's going to add somewhere in here, some notes on how it should actually go about using the tags file. Again, in your own workflows, you're going to want to make sure (t: 2270) that it does that autonomously here. So you can see here, we got one update here, which is this tags line right here. You can see see tagging system section below, and now it's actually creating the tagging system. (t: 2280) So we're going to accept that. And now down here, you can see important, always reference tags.md for tagging rules. So this is where clods in its system prompt now, right? This is the, basically the rules for cloud code is it's going to reference that it's going to (t: 2290) see this every single time we run any cloud code session. So anytime it updates our tags, it's now going to go about that. So let's actually ask us for a demonstration. (t: 2300) Please give us a quick demonstration to make sure that this works. And then we should hopefully see it, whether it edits an existing file or, (t: 2310) or creates a new one. So it looks like it's going to check tags.md for appropriate tags and then create a sample note here. Again, this, this makes a lot more sense once you have your own personal vault with many, many different files here. Worth noting too, (t: 2320) you can actually just create vaults for different things. I know people who will go about it that way too. If you want to kind of silo things and have, you know, certain pieces of your workflow be more separate. (t: 2330) You don't necessarily have to give cloud code root access to every single thing you can kind of divvy it up, which is kind of cool. So we have sample meeting notes here. Just go ahead and click sample meeting notes. And presumably you can see there's actually an error with how it must do numbers (t: 2340) and tags. So we would maybe have to go update our tag rules to account for something like that. Kind of teach the AI how to work, but overall got a few things done here, (t: 2350) which is nice. Presumably this follows the system rules here. We're not going to like dive into that again. I think you get the idea here. This is, this is where you want to define your rules for your tagging system. (t: 2360) And you just want to make sure inside of your main rules file, your cloud.md file that you kind of mentioned the tagging system. So that at the very minimum, just knows to reference that, that file and to keep things up to date over time. (t: 2370) So that's kind of a really nice way you can create an automated tagging system. You can do things like combining this with commands, right? So say I wanted to create a command for tagging a file. (t: 2380) Maybe I have a command called like tag dash file, right? And maybe in the side of that command, I say like, Hey, you know, go inside of whatever file I've tagged and update it with some tags. (t: 2390) You know, when you have some instructions there, maybe we do tag folder. Maybe this is for specifically tagging folders, right? You can kind of start to chain commands. So with dynamic organization and things like that. (t: 2400) And then of course you can do things like creating a Wiki link, a Wiki, Wiki links. I always see a little bit of a tongue twister that right. (t: 2410) To do something like this. If you wanted Wiki style links, which is very popular in knowledge-based applications. So you could do something like that where you, you know, create a new note called like Wiki links. (t: 2420) Dot MD. Obviously you don't do the dot MD in the title of you get what I mean, right? If you want things for organization, this is another one I use in my own personal vault organization. So you can do that. You can create a new note, (t: 2430) you can do things like that. So you can create a new note that you can do things like that. And then you can do things like that. So you can do things like that. You can create a new note, you can do things like that. So there's a couple of different ways you can build automated systems around organizational features, (t: 2440) but in particular tags, very core one. That's very nice to do the Wiki links one. Very nice to do. And then organization of like files and folders. Another really nice one to do. So highly recommend you take advantage of that in conjunction with things (t: 2450) like slash commands, workflow number eight. So this is going to be a quick one surprises me. A lot of beginners in particular, don't know about this, but you can actually ask, Claude code to use sub agents. (t: 2460) So we'll actually be able to do things in parallel. So you don't have to wait for one task after another to be done. This is particularly useful for things like researching tasks, (t: 2470) but you can kind of use your imagination. So really quickly, I just want to show you we're going to do sub agents. And then we're also going to do a bonus of showing you how to use deep (t: 2480) thinking, which is kind of another little bonus tip here, but to use sub agents and cloud cloud code, you just ask for them. So one of the things that we want to do is let's just say, we, we want let's actually just dictate this to the AI and we'll show (t: 2490) you what's going on here. I want you to spin three sub agents up for a new research task. We are going to create a new note that is called AI model pricing. (t: 2500) I want you to look up the pricing of open AI models, cloud code models, and Google models. And I want you to put those in a table inside of that note. (t: 2510) And I want you to price everything per million tokens. (t: 2513) Okay. So now we can send this off again, very speedy. If you're using speech, you can see that it's going to create a sub agent. (t: 2520) So you can see that it's going to create a sub agent. So if you're using text software, you can now see it's going to create three sub agents, just like we said, it's creating our to-do system. So presumably what's going to happen is you're going to see task pop up for these three things. And these are all going to be happening at the same time under the (t: 2530) hood. So this is also going to take advantage of things like web search under the hood. Very cool. So we're starting to chain things. One of the things that I'll do in a lot of different versions of my projects is I'll have a slash command for sub agents where I kind of (t: 2540) have different sub agent tasks, things like research for things like writing notes, all sorts of different stuff. And then I'll have a slash command for web search, which is going to be running on a web search tool. (t: 2550) So I'm going to run a web search tool. And what we're going to do here is we're going to let each one of these run in parallel. Okay. So instead of having to wait for this first task and then asking for the next one, we're just doing this all at the same time. Okay. (t: 2560) So sub agents are very cool. You can see it's actually using web search for each one of these. So I'm going to go ahead and speed this up and show you the end result here. It looks like these tasks just finished up. We should now be getting onto the note creation point. (t: 2570) All I had to do in some of these tasks was just accept a few web search tool requests here. Obviously all of that could be configured to run autonomously, but it's going to do a little bit different than what it should do for the actual generation of the file here so that we don't (t: 2580) have to accept that. So in just a few seconds here, we can hopefully see that table. Okay. You'll remember we did specifically ask for a table so we can see how well the AI does again, because it knows how to use Markdown. (t: 2590) It's going to be very reliable. A really nice little use case here. So obviously kind of a silly little example here, but you can see how this can get kind of nice. It can, you can see here it's now writing to AI model pricing. (t: 2600) It looks like we do in fact have that new note and it did create the table. It's a little squished here because of my window size. But if we actually go ahead and click on that, we can see that it's now writing to AI model pricing. ago. It's a review here. And let's actually just go full screen. You can see we have the OpenAI, (t: 2610) Anthropic, and Google models. You can see they're all priced out. It dynamically found this based on its own web search. So nice. Oh, it looks like it even did some key insights and pricing notes, (t: 2620) which is kind of cool. Some notes on prompt caching, batch API. Okay, very useful stuff. So one of the ways that I use this in my workflows, you know, I'll do a lot of research on things like code libraries. I apologize. Some of this is a little bit code heavy on the examples (t: 2630) here. Probably some of you more casual people who aren't coders are like, oh, that's kind of annoying. But again, it's personal to me. You can personalize your cloud code experience to you. (t: 2640) This is very flexible. But that's an example of how you can use subagents. You just ask cloud coder for the subagents, and it's able to kind of spin up these parallelized tasks to do a bunch (t: 2650) of different things at the same time. Workflow number nine, we're going to show you how to use MCP servers with your note-taking experience using cloud code. So this is where things can get particularly powerful. And if you actually read my corresponding blog post, you can find (t: 2660) the links for that below. I wrote a post called Cloud Agent, where I'm going to show you how to use MCP servers with your note-taking experience using cloud code. And I'm going to show you how to use MCP servers with your note-taking experience One of my big points and reasons for doing this video is that cloud code is so much more than (t: 2670) just cloud for code. It's really just an agentic system that's like super good at knowing how and when to use tools, which is why I kind of like to call it Cloud Agent, because you can build (t: 2680) agentic workflows for any type of task, any type of job, especially with things like MCP servers, which if you're not familiar with MCP, this stands for Model Context Protocol. This is kind of a new (t: 2690) way in which people are building tools and integrations that can very easily fit within AI models and other things. So if you're not familiar with MCP, this is kind of a new way in which people are building tools and integrations that can very easily fit within some of the most popular AI tools that you know and love. So one example that I'm going to be (t: 2700) showing you here is something called Context 7. This is something that I particularly use in a lot of my research tasks. Again, I mentioned earlier in this video, and you guys are going (t: 2710) to love it, another code adjacent example here. Hence the life of a developer for you here. But one of the things that this MCP server is really useful for is getting up-to-date (t: 2720) information about software libraries and documentation. So you can see if we go to context7.com. As a word of noting, guys, you can use MCP servers for all sorts of stuff. There's (t: 2730) MCP servers for things like Google Drive, right? So say I wanted to pull info in from my Google Drive into my own personal vault, I could actually just connect to the Google Drive MCP server, get that set up, and then boom, I would be able to do slash and you would actually (t: 2740) see the MCP server for like Google Drive, right? And then you'd be like, hey, can you actually pull this file instead of my Google Drive? It would go find that, create a new note for it, (t: 2750) right? Maybe you want to pull four or five different files. But the point is like all schools, right? Maybe you're somebody who's running a business and you want to use the Stripe MCP to query customer data or something. Maybe you have like a customer call coming up. (t: 2760) Maybe you need like the billing information. I don't know. Just coming up with random things on the fly here for you. Point is MCP is very extensible. There's all sorts of MCP servers (t: 2770) popping up these days. I'm just showing you one very particular example. It's something that I actually use most days. So you can see they have this MCP tab at the top. We're going to go ahead (t: 2780) and click that. We're going to scroll down and we're going to get some instructions on how to figure this. So if we scroll down, you can see they have a little bit of installation section here in the readme. And one of the things you'll notice is they have install and cloud code. So in (t: 2790) our case here, we're going to use the remote server connection. It's worth noting, right? If you just want to search a popular tool that you use in Google, right? You know, XYZ MCP, it's going (t: 2800) to be pretty easy to find the documentation for whatever server you're looking for. There's so, so many of these days. And side note, not only can you use pre-configured ones, pre-built ones by (t: 2810) existing companies and people all over the world, but you can also use pre-configured ones by existing companies and people all over the world. You can also create your own, which is how you can get really, really crazy with cloud code, especially when you're taking notes, you can kind (t: 2820) of build your own workflows. A very quick example I'll give of this is I actually have in my own vault, I have a custom built MCP server where I can input a YouTube link. I can use a custom (t: 2830) prompt command and it will download the transcript of that YouTube link, take notes on it and put it inside of my vault. In case you can get pretty crazy with this, I'm just showing you a very (t: 2840) quick example. But in this case, we're going to copy this command. And then what we need to do is we need to run this in our terminal, not inside of cloud code. So one thing you can actually do in (t: 2850) cloud code is you can turn bash mode on with this exclamation point here. You can see exclamation point for bash mode. This is now going to effectively run as a terminal inside of the text (t: 2860) input of cloud code. So I don't have to exit out of my session to run terminal commands here. And so I'm going to paste this in. This is going to add this MCP server here. So what I can do now (t: 2870) is I can do slash MCP. And you can see we are now connected to the context seven MCP server. So you'll see we have the second MCP server here that we're connected to. This is actually the deep wiki MCP server from Devon. This is from Cognition Labs. This is another tool that I use (t: 2880) their MCP server for a ton for coding research when I'm working on different projects. But in (t: 2890) this case, we're going to go ahead and use context seven. We're going to reset our chat here. We're going to ask a question where I say use context seven to find how to do generate object with (t: 2900) tools inside of the Vercel AISDK. So this is actually going to ask us for permission first. (t: 2910) This is very similar to how cloud uses its other tools. You can see resolve library. So the first thing that context seven is going to do is actually going to pick which documentation to pull from. (t: 2920) Okay, so we're going to prove that it's going to search for Vercel AISDK. And then it's actually going to go ahead and dynamically find that inside of its libraries. If we go back to context seven here, you can see Vercel AISDK. If we search for on their site, presumably it's pulling from this (t: 2930) you can see we got a little terminal pop up. That means our message is ready inside of cloud code. And so now it's actually going to dynamically search for it. Instead of me, I'm going to go in (t: 2940) the UI, and I can just offload that task to cloud code. So we're going to approve that here. You can see there are some parameters here. So what it's going to do is it's basically going to pull the (t: 2950) cached docs that context seven has so that I can really, really quickly get an answer to my question here. Okay, so it looks like we got a little bit of documentation here on how to use that library. And again, we did this via the context seven MCP server, which is really cool. (t: 2960) Now I can say awesome, create a new note in code slash ideas slash AI chat bot. What this is gonna (t: 2970) do is we're gonna create a code folder, this will create an idea folder, and then this will create an AI chat bot folder here. So what we can do is we can actually just turn auto accept on with (t: 2980) shift tab, this is going to go ahead and allow cloud code to autonomously handle the creation of this without us needing to approve this, which is very cool. So you're starting to see the pieces of (t: 2990) how all of these things are starting to come together and form these really powerful workflows. So you can see there's the code folder, there's the idea folder, there's the AI chat bot, looks like it did create that as a folder. So we'd maybe want to be like, hey, actually create (t: 3000) that as a note, it's probably going to put a note in here anyway. So I'm not going to worry about it. That's something you could very easily iterate on with cloud code. But again, MCP servers, (t: 3010) super powerful, you can build your own. There's so many on the internet for all sorts of things, right? Say you're somebody who, you know, I'm also somebody who uses notion a little bit for (t: 3020) forming personal wikis that I want to be a little bit more presentable and maybe share with friends, I could connect to the notion. So I'm going to go ahead and create a new notion. And I'm going to go ahead and create a new notion MCP server. And then I could pull things from my notion into here, or I could actually pull things out from my personal vault. And maybe I want to create a (t: 3030) new notion page with info that's from my obsidian vault, right? So not only can you kind of read information and bring stuff in, but you can also take information from your obsidian vault. And (t: 3040) you can use that with outside sources. Okay, so this thing is very cool. MCP is a very, very deep rabbit hole that I highly recommend you dive down when pairing it with taking notes (t: 3050) to get super crazy. One of the last things I'm just going to ramble on for like 20 seconds, then I promise we'll move on to the next thing is you can start to really build these agentic (t: 3060) workflows. Again, this is why I call it cloud agent and not cloud code. Because say you're somebody doing sales, right? You hook this up to a few MCP servers. And suddenly you're almost like doing your job in conjunction with cloud code instead of the terminal. And maybe you know, (t: 3070) maybe you're not even using obsidian, maybe of your own, like I said earlier, like a custom UI built out, there's just so many possibilities here. We're really just scratching the surface (t: 3080) of what AI agents can do. And all this is just so so exciting to me. And of course, the use case, the best way I'm trying to use to demonstrate all this is taking notes. This is going to be the last workflow demonstration workflow number 10. This one's going to get a little bit weird. And (t: 3090) this is really meant to just kind of demonstrate something more broadly, which is that you can actually deploy cloud code in the cloud and have it do things autonomously. When you're on the go, (t: 3100) you can do things like building WhatsApp integrations, you can build your own custom app to manage this, all sorts of things to do the easiest way to demonstrate this and to get started (t: 3110) with at least just experimenting with this type of workflow is going to be with clouds, custom, get up, action. What I'm going to do here is I'm actually going to use cloud code itself to create me a new git repository. And what we're going to do is we're going to set up cloud codes, GitHub action (t: 3120) on our GitHub repository. So GitHub also an interesting thing to integrate with an obsidian vault because it also gives you version control on your vault. So this is something that I'll (t: 3130) experiment with from time to time, I kind of test vaults actually don't necessarily do this workflow with GitHub on my main vault, I actually have my own infrastructure built out to handle (t: 3140) that these days. But we're just kind of demonstrate this. So you can get a feel for it and just see kind of the magic of a workflow like this. So what we'll do (t: 3150) is we'll say, I need you to initialize a new GitHub repo and push it to my GitHub as a private repository. Okay, so I can actually just tell cloud code to do this cloud code is intelligent (t: 3160) enough to figure out how to use the GitHub CLI in conjunction with initializing a new git repository on my machine here for this vault. So I'm probably losing some of you non technical people if you're (t: 3170) not like a software engineer or developer or something like that, it's gonna be a little bit weird for you. You may just want to sit tight and watch this one. But it is pretty cool. I promise the payoff on this (t: 3180) is cool. We're going to have cloud code run git init, this is going to initialize a new empty git repository here. And what we're going to do is it's going to commit our files to GitHub and create a new repository with the GitHub CLI tool. Okay, so a lot of different things happening, you can see (t: 3190) those of you who are developers can see it's adding all files, and then it's committing with initial commit message. This just means that all the files that I have in my vault are getting added (t: 3200) to a new repository that we are about to push to GitHub. So I'm going to go ahead and create a new repository called cloud code vault on my GitHub here, going to push this again, (t: 3210) it gh is just the command for GitHub CLI, right, just like cloud code is a CLI, GitHub also has a CLI. And this is what (t: 3220) it's using. So we're going to go ahead and proceed. I'm going to let that create and then I'm going to go open that up (t: 3230) inside of GitHub. You can see we're on GitHub.com. This is in my new vault that I have on Git. Okay, so the idea here is you do a couple things. If you want to use a version control in your vault, very cool thing to do, you can kind of get a running history of everything in your vault. (t: 3240) Obviously, if we were configuring this for real, we would do things like adding DSTORE to git ignore and all that kind of stuff. But we're just kind of, we're just kind of going through the motions here. Now, the important thing is inside of cloud code, what we can now do is we can do slash install GitHub app, you can see this is a predetermined, or kind of a pre existing command inside of cloud code. This isn't like a command I came up with. This is on everybody's instance of cloud code. And what we do is we just tap that, and then we're going to hit return. And this is actually going to use the repository that we just set up, and it's going to install the GitHub app on our repository. So we're going to hit return and we're going to see that it's actually going to install the repository. And then once we do that, we're going to see it's going to be able to run the repository. (t: 3270) turn and this is actually going to use the repository that we just set up and it's going to install the GitHub app on a repository. So in my case, on my account, Cloud is allowed to install the GitHub app on any repository. You're going to need to install that for the first time or (t: 3280) configure what repositories this particular GitHub app has access to. I can kind of just skip that (t: 3290) because it already has access. So I can just kind of close that. And now you're going to see if we go back to Cloud Code, we just need to initiate this kind of initial thing here. So you can see (t: 3300) we can now select GitHub workflows to install. Both of these are checked. We're actually going to just accept both of these. So we'll just hit enter. And then you can see create a long-lived (t: 3310) token with your Cloud subscription. So you can either use your own API key or your own Cloud subscription token. We're just going to go ahead and use our existing Cloud token here. So we can (t: 3320) just sign in directly with Anthropic. Again, we've gone over this, but to use Cloud Code, you do that by clicking on the link in the description. So we're going to go ahead and either an API key or a Cloud subscription. In my case, I'm just using the Cloud Mac subscription, (t: 3330) the $200 one, because I'm such a power user. You can see we now have a very introductory PR. This has some information about how this particular integration works. We're just kind of speeding (t: 3340) through this because you actually have to approve this before the app will work. You actually have to create that and then merge it, and then you're going to be all set. Okay. So that's merged. We (t: 3350) can delete that branch. Everything is ready to go. So now how does this get interesting? Let's switch over to the phone. I apologize, guys. The audio is going to get a little bit weird here. You can see I am now on the GitHub app on my phone. Okay. So let's imagine for a second that (t: 3360) I'm on the go. Let's say I'm on the walk and I actually want Cloud to do some research for me in the background, in the Cloud. And then when I come back to my computer, what I can do is I can (t: 3370) actually go see it's research inside of my vault. So what I'm going to do is I'm going to go into issues here. And what I'm going to do is I'm going to create a new one in the top right here. And I'm just going to say test integration, which you can do something really basic here. And I'm just going (t: 3380) to say, please, please, please, please, please, please, please, please, please, please, please, please, please, make a new file in the vault in the slash test folder called hello world and write a funny joke, (t: 3390) a funny joke for my viewers. Okay. So what we're going to do is we're going to submit this. This (t: 3400) is going to create a new issue inside of my GitHub repository here. And what I can do is I can add a (t: 3410) comment. You'll see this comment button at the bottom. And because we've installed the Cloud app, I can now actually test it. And I can see that I've got a comment. And I can see that I've got a comment. And I can see that I've got a comment. So if I go back to my GitHub repository, I can see, let's say, tag Cloud, you don't need to see anything there. And what we can do is we can say, please handle this. Thanks. Do a smiley face. We'll go ahead and leave that comment. And what's (t: 3420) going to happen is this is actually going to kick off our GitHub actions. So anytime that we tag (t: 3430) Cloud Code inside of issues, inside of PRs, it's actually going to go autonomously handle this, which is super, super cool. So we actually have to wait just a little bit to get set up. We're (t: 3440) now going to switch back over to the computer here so that I can show you how this is going to work. this works. So you guys can see this is now triggered from my phone. Again, I apologize for the phone audio. When we're back to the computer audio, you can see theoretically, and I know I'm (t: 3450) not literally doing this right now, but theoretically I could have just been completely on the go, not having to be around my computer at all. And this cloud integration is now just running in (t: 3460) the cloud and it's actually running this, you know, without me needing to accept anything, all sorts of stuff. So there's a lot of ways you can configure this to be a little bit more (t: 3470) personalized to your vault. And again, at this point in my own personal vault, I have kind of my own server that's running where I can trigger these things. But I think the GitHub action is a really, really quick way to at least get started and start playing around with this type of (t: 3480) workflow. So what's going to happen is cloud is basically going to look through all the comments in this issue. You can see it's going to check if a folder exists. You can see it's going to create a hello world. You can also go in your GitHub, there's the actions here, right? You can (t: 3490) see my issues actually running is out of this tab. You can see it actually just finished up. So if we go to issues here, click test integration, (t: 3500) you can see it actually did in fact create that new file, hello world. So what we can do now is we can go back to our vault. And because this was actually created, it was actually committed. So (t: 3510) we go to code here. You can see we're going to have presumably probably a new branch here. Okay, awesome. So this is a new branch. So what we want to do is probably just grab (t: 3520) this branch name here and then actually ask cloud code to merge it. So what we'll do is we'll go back to cloud code here. And what we're going to do is we're going to say, awesome. We just had, well, let's actually use speech to text here. By the way, you saw me clear (t: 3530) that text really fast. If you hit escape twice really quickly, it will clear your input. Okay, (t: 3540) we just ran an automation. There's a new branch. We need that to get merged into the master branch. And then we actually need you to pull. So we get the latest version of our notes. So again, cloud code is very intelligent. It's going to know how to use the Git CLI, (t: 3550) the GitHub CLI rather, to go ahead and accomplish this task. So it's going to fetch all the remote branches. It's going to see the existing branches. So we're going to go ahead and accomplish this task. So we're going to fetch all the remote branches. It's going to see the existing branches. It will ask us for permission because we haven't configured permissions here. You can see (t: 3560) that cloud issue two right there. It's going to go ahead and get merged into main. Okay, so it looks like it's going to pull order master and merge it with this branch. Awesome. Now it's (t: 3570) going to go ahead and allow us to see inside of test and inside of hello world. We now have a joke. Why don't scientists trust atoms? Because they make up everything. So the workflow that (t: 3580) we basically simulated here is that if I was on a walk, if I was on my phone, I could go ahead and create a new issue on GitHub. I could use cloud codes, GitHub, integration, and basically kick one of these things off in the cloud, come back to my computer (t: 3590) later, pull that research. So again, silly little example here, but the goal is to get the wheels turning in your brain that you take the simplicity of what I just demonstrated. And you actually (t: 3600) apply those concepts into practical real world workflow examples into your own vault. Hopefully those 10 workflow tips will help you 10X your note taking with AI agents in particular cloud code. (t: 3610) The point of making this video is that cloud code is so much more than just a coding tool. It is a multipurpose agent. It's very flexible. It's very malleable. So if you found this video helpful, (t: 3620) helps me a lot. If you guys subscribe to my YouTube channel or you follow me on X, or if you are reading on Substack, go check out the accompanying blog post that we have, (t: 3630) or that I have rather called cloud agent, give that a read. But if there's any other things you guys would like to see me do, if you want to see like an advanced version, maybe I use my own (t: 3640) personal vault a little bit more. Let me know. I'm super open to doing more types of videos like this. Obviously I do a lot of videos in the realm of the coding world and things like that. But definitely open to more general purpose agentic workflow videos. If this is something you go (t: 3650) like. One quick note to close on too, is we do on takeoff, we have our cloud code lessons. So we have 23 lessons, which are two hours and 22 minutes. We have a couple more coming up, just a few more (t: 3660) days on some project stuff that we've been working on. This is a really great way to get up to speed on all of the features of cloud code, everything in its toolbox. So if you are somebody inclined (t: 3670) who is like, wow, cloud code seems really cool. I can learn more about this. Really, really excellent resource we put together so that you guys can go learn how to use it. This cloud code. So thank you for watching again. Share this with your friends. If you found this (t: 3680) helpful, it goes a lot to help me out. Thanks for watching guys. We'll be back soon with more videos.

