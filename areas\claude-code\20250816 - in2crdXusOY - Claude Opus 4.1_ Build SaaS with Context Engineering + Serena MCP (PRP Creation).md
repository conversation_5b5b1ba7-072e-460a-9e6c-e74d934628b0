---
title: "Claude Opus 4.1: Build SaaS with Context Engineering + <PERSON>P (PRP Creation)"
artist: AI Business Lab
date: 2025-08-16
url: https://www.youtube.com/watch?v=in2crdXusOY
---

(t: 0) If you are trying to build your own website or start your own business using AI, you've probably been getting into some of this where you're thinking like, hey, I have the idea. I just need to get a website down, test it with some clients, see if people (t: 10) are interested. Or you're thinking, hey, I think this idea is good because I've seen it somewhere else. I want to get it going. So in terms of building something that's a little more robust (t: 20) and has a couple more steps involved and interlinking facets and assets, the context engineering seems to be king here. So what I'm going to do for you and me, because I think this (t: 30) is really fun. I just love doing this stuff. This channel. We're going to build one and I'm going to (t: 40) walk you through step by step. It's going to be pretty simple. It's going to be kind of back and forth. And yeah, I'll ask you a couple of questions and then, you know, put your comments, put your answers in the comments and let me know what you think. So with that said, I want to show you kind (t: 50) of a little bit about what I found. I found this. It's a cool website. And I think I could build it and I think I could do it locally in my city. So it's construction (t: 60) data solution. So it's like it just gathers all the requests for proposals from your city. (t: 70) And then it charges people 100 bucks a month to have those emailed directly to them. It filters for whatever they specifically want for their subscription. Right. So, for example, (t: 80) let's say someone is building a website for their city. And they're going to be able to do it like a new hospital or they're building. Yeah, let's say someone's building a new hospital. Well, (t: 90) if you are a construction company that does painting or that does, you know, flooring or (t: 100) high quality flooring, you know, if that comes out and that's been awarded to someone or there's a (t: 110) proposal for it, you're now a company that could build that and you could win that. So to spend, you know, twelve hundred bucks a year, being the first person who has access to reach out to these people, I mean, (t: 120) you could make that back in half of one. You know, they like some of these contracts go for (t: 130) sixty, seventy thousand dollars. The profit on those could be much more than that. So, yeah, sky's the limit. I found a business. And so what we're going to do is we're going to do (t: 140) some context engineering. We're going to walk through it. We're going to kind of strip this thing apart and we're going to build it for ourselves using a cloud desktop. Sounds fun. All right. So. To start off, we are going to be using cloud desktop. So here is the thing I started a new. (t: 150) I started a new folder and I'm just going to go cloud dangerously. Oh, wait, before we do that, (t: 160) what I'm going to do is I'm going to get clone the context engineering. So this will be on my (t: 170) git as well. Moving up. So I will get that for you right now. One sec. The first thing we're going to do and you can find all of this in the link in the description. So this is a lot of stuff. So you're going to go to the description is we're going to clone my git, which is for context engineering (t: 180) into the project itself. So we're going to go git clone, paste it, and we're going to set that in (t: 190) there. That's good. Now, the next thing we're going to do is we are going to add Serena MCP, (t: 200) which is my favorite. So (t: 202) let's grab that information, which is this is our repository. Sorry. This is from another thing that we obviously have in. (t: 220) Yeah. Okay. Serena's added and now, okay. So there's all of this will be in the description. So we have it, we have it set up. We've added the GitHub repo. Now that Serena's added, (t: 230) I'm going to do cloud dangerously, skip permissions, which is one of my favorite because it saves a heck of a lot of time. Let's kickstart cloud. (t: 244) Okay. And we're in, and then I'm going to say let's let's get Serena to go through this first and just make sure it understands what we're up to. (t: 250) So where is that? We're going to go MCP Serena initial instructions. (t: 264) Good to kind of just like do a little kick through. Check out everything, make sure it's cooking the way they want it to. (t: 272) Yeah. (t: 280) Okay. We don't have this set up yet, but empty directory. okay. Yeah, this, isn't so we've (t: 290) started Serena a little early. That's fine. I just wanted to make sure it was in there. Serena is good at going through things, organizing them, saving you a little bit of time and money. when there's already something there. (t: 300) Unfortunately, we don't have something there right now. So we're going to switch it up a little bit. Now, I know what the goal is here. (t: 310) So what I want to do is I want to... We're going to be following instructions on this in terms of how to get everything set up. (t: 320) So let's get started on that. So I want to open up... So I'm going to ask it to open up Visual Studio Code. (t: 330) You should ask it to open up Visual Studio Code. And what that's going to do is it's going to help us have a look at everything that's going on. So we open the project in VS Code, please, (t: 340) because I'm Canadian, I guess, and that's kind of how we do things. I'm going to make sure that we have VS Code on here. (t: 350) And then all of these instructions, again, will be linked to... But I'm doing this with you right off the hop, (t: 360) and it's going to be super fun because we're going to build this entire thing together. And I'm sure it'll be less fun once we go into the actual headaches of the whole thing. (t: 370) But, I mean, hey, it is what it is. (t: 373) Okay, so now I'm going to open up VS Code. Okay, so now we have this open in the code. (t: 380) And let's go to the next step. So our next step is going to be... Have a look at the entire project, (t: 390) especially the context engineering portion. (t: 400) We are going to be doing some research and then changing all of these forms (t: 410) so that we... And build our own project. (t: 420) Cool. We're just going to get to do a little bit of work and have a look through it so that way we don't have to, like, walk it through the whole thing. (t: 430) I think that's going to be the win. It's just like, hey, dig through this, have a look, explore and understand the framework. Once it knows the framework, which it should know from just reading through it, (t: 440) because we're going to be building something which is a little more drastic, we're going to use a lot of research. And this video might just be about getting it started (t: 450) and researching. We'll see, because I do, you know, take into consideration how long these things go. And I really, you know, I want to maintain your attention too, right? (t: 460) So if it's getting boring or whatever, if I can't, like, skip enough, but I wouldn't mind getting down to something that actually just looks good so that way you guys can, like, you know, (t: 470) take something to the next step. Okay, framework. Multi-agent approach. Spin up. Six parallel research agents. Great. Okay, so, okay. (t: 480) Now that you understand this, I want to direct you and chat about what our project is going to be. (t: 490) Then I want to update. (t: 500) Then I want to follow the instructions. I want to update the... Context engineering documentation. (t: 512) To be optimized for our own project. (t: 520) And then I want to launch. Then I want to build that project. (t: 530) Do you understand? Have I explained this well? Do you have any other questions? (t: 541) I really just want to make sure that I'm giving this thing everything it needs to be successful because that's the whole idea around, like, the context engineering. (t: 550) It is, like, just... You're not just kind of giving it everything. You are kind of giving it everything at once, but you need to make sure everything's there. (t: 560) So, okay, text-to-text. Do you have any preferences? Frameworks databases. Okay, so, one. Define your project. Okay, so, text-to-text. (t: 570) Do you have any preferences? I think that we should. So, okay, we're going to go one. (t: 576) Off the build. (t: 580) By building everything with HTML and CSS and JS. (t: 590) So that we can edit the build. Easily. At least the surface. (t: 600) At least the front end. End of it. (t: 603) I would prefer to use... (t: 610) What is it? MySQL. Just... I think it's MySQL. (t: 620) Node.js. (t: 630) And... Python. We may also need... (t: 640) M8n. To help with some of the automations. I would like to use the... (t: 650) MagicLink... Style. And... hospitalityoder.com Okay, there is a password call... (t: 660) For... Signups And... (t: 666) Registrations... (t: 670) We will be doing... Payments... Through... Stripe... (t: 680) In the future. Okay... Two... app it's going to be web app automation tool and data pipeline mostly but let's (t: 700) talk further about that and then it wants the Gina so Gina is really easy to get (t: 710) but I can't really show you guys too much about it because you open it up in a (t: 720) open it up in a incognito window and it will give you a API key (t: 733) just like that talk about that further (t: 740) we that for start simple but the let's get (t: 760) let's get as much as we can your available tokens oh they change it changed it (t: 770) out of Gina API keys so we're not doing (t: 790) g api so I'm just going to go we're out of gna pi he's yeah (t: 800) yeah yeah just use agents where they work for do (t: 810) as much as we can I would like to do this in one shot like to do this all at once if possible (t: 820) there's still a lot to share with you though you should chat about it (t: 830) okay so it hasn't really asked me too much about exactly what we're building yet but it's about to find out it's kind of getting the the basics down of what (t: 840) we're doing here now I do have um some research that I did and that I'm happily (t: 850) share quite often with everybody like I have an SEO strategy guide here um sort of big picture what's the core idea of this app um I want to set up a (t: 860) subscription-based email service that links um RFPs and projects done by (t: 874) government in my area to subscribers via email here is uh here's a another business in a (t: 880) different area that does the same thing (t: 890) I want you to review it and (t: 900) strip it for best practices and let's grab that old let's grab this thing here (t: 910) which is here foreign and the the the the the (t: 920) the the this there's a need in me right now to update my social media and we're gonna we're going to let it like do a little bit of work on this now (t: 930) so let's build this out it's going to go through and grab a little (t: 940) bit of everything which is great you can get yourą can get a little more product bet on that can get a little more placement what's really cool is that if you do a giveaway that says un deliberate buy or't (t: 950) likely at least it's time ت bald I'm a opened book icon tish I don't think they have a lot of like (t: 971) yeah they don't have a lot of information that's public (t: 980) I just know a bit about it excuse me (t: 1010) perplexity is not working either I think I'm just going to do some other work on this through perplexity (t: 1020) so (t: 1040) here (t: 1060) okay so I'm just going to say (t: 1070) I'm just going to say I'm just going to say I'm just going to say RF keys oh okay guys we're back it's finished up a bunch of the work that we have assigned to it (t: 1080) and we're moving on to the next step which is pretty cool now let's have a look at the research you completed I've successfully researched all technologies and comprehensive documentation so front end framework (t: 1090) fast API N8N Stripe and Magic Links AWS SES email service okay key decisions the front end the back end (t: 1100) the email the email the email the email the email the email I'm not using I'm not going to use any of that for email unless it's absolutely necessary authentication (t: 1110) would you like me to do okay so I think I'm just going to say for the email I want to (t: 1120) set up a free version where N8N sends something (t: 1130) from my email from a gmail account and I also want you to use (t: 1140) the meta agent to create specialized agents that can work on (t: 1150) each of these categories at a high level right (t: 1160) so I saw there that it's like AWS or SES is going to be this and I'm like I don't think so I'm sure that we could set it up with N8N where it'll just (t: 1170) create an email and send it super easy thing to do with just like a login flow or something like that so let's get that going (t: 1180) so that way it knows what we want it to do and then from there it should be kind of easy to go easy to work out so that way it knows what we wanted to do and then from there it should be kind of easy to go easy to work out (t: 1190) and then from there it should be kind of easy to go easy to work out and then from there it should be kind of easy to go easy to work out and if we can work that out I mean that'd be great I would use it in some of my other projects too because I have other emails (t: 1200) and websites that need an email solution which I'd happily do through N8N even the free version does quite a bit so I'd happily just set that up (t: 1210) and like run that on the daily or something just like let it go but let's see what it does (t: 1220) yeah it's going to do its own work on here yeah and now it's going to set up meta agents (t: 1230) which is kind of a new trendy thing but it's going to have agents that are specialized (t: 1240) within each of these features which is kind of what you want right it's just a lot easier like that (t: 1250) oh yeah see next.js SEO agent (t: 1260) Python scraper agent awesome these are all agents that we're going to be able to use not only for this project (t: 1270) but for probably other ones too which is perfect because after we're done this I'm going to be building another business which is going to be even more powerful (t: 1280) because that's the point here that's kind of what we're up to that's what I like doing I want to build a business see it through see if it can make some money and then you know get that multiple streams of income (t: 1290) kind of rolling like I'm not one for the hardcore like 12 streams of income whatever I just want one really big one (t: 1300) you know but I do feel like learning to execute these things is what can get you to that next level you're not going to figure it out on the first try I mean some people do (t: 1310) and you're not going to figure it out on the first try and that's awesome but like statistically it's not it's not worth banking on right like one of my favorite books ever it was called The Millionaire (t: 1320) The Millionaire Fastlane I can't suggest it enough and as cheesy as it sounds I mean I think it's I think it's one of the best books I've ever read (t: 1330) and it's just about you know it's not even like woo woo like crazy like hype yourself up whatever it's just like (t: 1340) hey man like , if you want to make some real money like it's not a four hour work week that's going to get you there you know you have to work and you're and if you work (t: 1350) and you keep trying and you make something new and make something new and make something new he says in there something that I think about often is that like (t: 1360) every time you make a new business it's like a it's like a gumball machine you know like you have these different gumballs you're in a gumball machine and you're every time you make a business (t: 1370) you put a coin in it you put a quarter in it and you turn it and there's some gold gumballs in there and those gold gumballs are retirement essentially right if you hit something (t: 1380) if you invent the spork you retire you know what I mean like it's there and yeah I'm happy to chat about that further but like man yeah I can't say enough good things about it (t: 1390) so okay so we have uh oh awesome awesome awesome (t: 1400) yeah that's fine yeah let's make now yeah so start number one (t: 1430) so this is going to be a big like almost a one shot I mean I guess it's not technically a one shot because there's so much going on here like (t: 1440) how many prompts have we done already right like we started you know we did a where's the first prompt (t: 1450) VS code have a look at the project misunderstanding a little bit more (t: 1460) now it's built in plan and now we're all the way down here so it's it's definitely not a one shot thing but I mean we're cooking like this is cooking guys like I could have this done (t: 1470) or at least up and online maybe by tonight but what I want to do here is I just want to get something I want to get this portion complete (t: 1480) the PRP complete and I'm going to call the video there because we're already at 40 minutes and um or maybe we're at 25 minutes I've had to do this in a couple of videos and (t: 1490) cut out some of, like, the boring stuff, because I really respect your time, super grateful for you guys for joining me on this, like, you guys are so motivating to me, I can't believe, like, we did 200, we did 200 subscribers, which is just so wild, it's so wild that you guys are just, like, (t: 1500) hanging out with me for this, yeah, like, so thank you so much, if you watch till now, thank you, (t: 1510) um, yeah, I think the big thing that I'm thinking about right now is, I am gonna get this first step done, and then I think when it comes to (t: 1520) actually creating it, and setting up the first one, and getting the design done, I'll do that (t: 1530) in a separate video, where it's, uh, I'll call that, like, launching the PRP, I guess, so this is, like, setting up context engineering, the next one will be launching PRP, anyways, if you found value in (t: 1540) this video, it's coming to an end, build it, and I'll see you guys in the next one, bye! Build the, like, it's, this is it, this is the whole thing, I don't know if it's gonna continue, but Jesus, it's, like, cooking on tokens right now, um, if you found value in this, if you, like, (t: 1550) hanging out with me, um, thank you, thank you for hanging out with me, um, yeah, like, subscribe, (t: 1560) like, thank you for watching this, um, I want you to make as much money as you deserve, and I think you know if you deserve it, I think you know if you're putting in the work, (t: 1570) and I think you know if you're putting in the work, and I think you know if you're putting in the work, I hope this helps you, I hope that if you're just, like, sitting at home, and you're kind of debating (t: 1580) between playing, you know, a video game, which I play a lot of video games, but it's usually, like, between 10 and 12 p.m., where actually I can bring myself to do that, because I feel so bad if I don't (t: 1590) get something done in the day, um, thank you for putting it off, and just getting one more thing (t: 1600) done in your day, I mean, I hope I'm the guy who kind of, like, pushes you to do that, but just to show you where we've gotten to, we have the PRP, (t: 1610) master implementation blueprint is locked in, the PRP, use the five phases of 15 days, foundation, core features, content and SEO, monetization, and production, key components, (t: 1620) complete database schema with indexes, file structure for entire project, validation loops (t: 1630) at four, and then, you know, you can do that, and I think it's really, really, really, really, really, really, really, really, really, really, really, really, really, really, really, really, really, really, really, really, really, really, really, really, really, really, really, really, really, really, really, level success metrics, risk mitigation, anti-patterns to avoid, (t: 1640) specialized agent assignments, each phase clearly identifies which agent to use, front-end task builder, government scraper expert, and fast API architect, and then workflow (t: 1650) orchestrator, ready to build, so this is, this has been getting you from launching your very first, (t: 1660) um, launching your very first, uh, like, context engineering to getting the PRP done, and what I'm (t: 1670) going to do is I am going to, I think I'm just going to add this whole thing to my git, like, this is going to be it, I'm going to add this as its own git, and if you want to build this for your (t: 1680) city, then I think you should, I don't know what's going to happen from here, it's really not done, (t: 1690) um, but, yeah, if it works, like, let it work, I don't know, if you can launch this in New York, or Houston, or, or, you know, (t: 1700) Mississauga, or Italy, or, you know, somewhere in Japan, like, God bless you, man, like, you can make it happen, you know what I mean, like, the world's your oyster, and I want you to (t: 1710) shuck it up, baby, anyways, hope you had an excellent time hanging out with me, thank you so much, if you stayed till this late, you're an absolute legend, and I hope to see you on the (t: 1720) flip side. Bye.

