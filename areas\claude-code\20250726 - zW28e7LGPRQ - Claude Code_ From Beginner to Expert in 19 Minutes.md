---
title: "<PERSON> Code: From <PERSON><PERSON><PERSON> to <PERSON><PERSON> in 19 Minutes"
artist: <PERSON>
date: 2025-07-26
url: https://www.youtube.com/watch?v=zW28e7LGPRQ
---

(t: 0) Cloud code is the best coding agent by far and in this video I'll teach you how to use it. And no, you don't have to be a professional developer. I've used cloud code for well over 200 hours myself. (t: 10) Plus, I read the entire cloud code bible. This is an article written by <PERSON>, the man who created cloud code. There is a reason why this is the fastest growing AI tool in the world. (t: 20) But you will not get it unless you use it yourself. So if you want to actually learn how to use cloud code to massively boost your coding ability, (t: 30) make sure to watch until the end. The first step of the cloud code bible is customize your setup. And there are four main things you need to do. The most basic step is to create a cloud.md file. This is the main system prompt that decides how your cloud code behaves. (t: 40) So in mine I have things like always prioritize writing clean, simple, modular code. Write lots of comments in your code. UI design principles. Basically, this is the main prompt that decides how your cloud code behaves. (t: 50) Now you can actually auto-generate your cloud.md file by typing slash init. So when you are inside of cloud code, (t: 60) just type in slash and then you see all of the commands possible. And if you type in init, you'll see that there is a command that can initialize a new cloud.md file with codebase documentation. So what this will do, if you already have a codebase, (t: 70) it will analyze all of your files, learn how your codebase is structured, and turn that into a set of high-density instructions in your cloud.md file. The next essential step in customizing your cloud code setup (t: 80) is curating a list of allowed tools. This is done in the cloud-settings.json file. So let me show you. So right here in my project, I have the .cloud folder. And inside, I have settings.json. (t: 90) This is the file that decides the allowed list of commands that cloud code can automatically run, as well as the deny list of commands which it can never run. (t: 100) So by curating this list of allowed tools and commands, you can greatly speed up how fast your cloud code works because it doesn't have to ask you for permission every single time. And the fourth step in the cloud code setup is to install the GitHub CLI. (t: 110) And this is very simple. If you're on macOS, just type in brew install gh. And on Windows, type in this command. This will allow... to use all of the GitHub CLI commands (t: 120) to see details of pull requests, create new pull requests, get GitHub issues, and anything else that you might do manually inside of GitHub. The second part of the cloud code bible is (t: 130) giving cloud code access to even more tools. So cloud already has access to your shell environment. However, you can make it even more powerful by giving it custom MCPs. (t: 140) Now, cloud code can function both as an MCP server and a client. And as a client, it can connect to any number of MCP servers. The one MCP that all of you absolutely must add into your cloud code right now is the Playwright MCP. (t: 150) So all you need to do is open up the terminal and simply paste in this command. Hit enter. And then when you jump into cloud code and type in slash MCP, (t: 160) you should see the Playwright MCP in there. This will allow it to interact with the browser, take screenshots, click on buttons, and debug the frontend like a professional frontend engineer would. (t: 170) Just by running this single command, you will already make your cloud code more powerful than everyone else's. But we're just getting started. The next step is to add custom slash commands into your cloud code. (t: 180) When you jump into cloud code, obviously there are the commands, right? We already covered in it. There's also slash clear, slash compact. You probably are familiar with the default ones. (t: 190) However, what many people don't realize is that you can actually create your own commands. So as you can see, inside of my .cloud folder, I have this subfolder named commands. And in there, I have a bunch of custom commands that I created myself. (t: 200) For example, create PR. This is a set of instructions that tells cloud code how to create a new pull request. And then inside of cloud, I can do slash create PR. (t: 210) And this is a custom command that saves me so much time because I don't have to retype all of these instructions every time. I don't have to tag any files or explain the process. It's just a custom command, which I can run. (t: 220) And cloud code already knows exactly what to do. So let me actually show you how to create one of these commands. And I'm going to use the example from the cloud code Bible, which showcases the command for fixing GitHub issues. (t: 230) So here is the instruction. And again, I'm going to link the whole article below the video. And what we need to do is we need to go into our .cloud slash command folder and create this file. So let's do that. Right click, new file, fix GitHub issue.md. (t: 240) Boom, empty file. And then I'm going to copy these instructions written by Anthropic. And I'm going to paste them in. Now notice at the top is dollar sign arguments. (t: 250) This allows you to pass in custom arguments while using the custom command. So remember when I said slash PR, you can see that after I type the command in, I can still type some words, right? (t: 260) And this is exactly the argument. In this case, I set it up that this will be the branch, right? Create a pull request against this branch. And then the argument will be the branch that I want the pull request to go into. Now in this new command, fix GitHub issue that we just created. (t: 270) This argument is going to represent the GitHub issue that we want Cloud Code to analyze and fix. So all I have to do is save this file, launch a new instance of Cloud Code, and then I should be able to do slash fix GitHub issue. (t: 280) And there we go. This is our new command that we just created. And then I can type in the issue number, like, I don't know, 1526. And now it will try to fix it. (t: 290) So what this means is that I can launch an entire workflow of eight, 10, 12 different actions that Cloud Code has to do just by typing a single slash command. This is how developers are becoming 20 times more productive (t: 300) by having a set of different workflows turned into single commands, which you can just type into Cloud Code and it will execute the entire workflow by itself. (t: 310) And you just saw that creating new commands is super easy, literally creating a single markdown file and writing the instructions inside. The next stage is trying common workflows. (t: 320) So even though Cloud Code can be used in any way you want, there are some general good practices, right? And these are the four recommended workflows by Anthropic. So number one, the explore plan code commit workflow. (t: 330) What this means is that first you tell Cloud Code to explore the code base, find all of the files relevant to this issue. Then you switch to plan mode and you can do that simply by pressing shift tab. (t: 340) This is one of the main keyboard shortcuts inside of Cloud and allows you to cycle between the three different modes that are in, right? The default mode, the auto accept mode and the plan mode. (t: 350) The plan mode is great before you start working. In this mode, Cloud Code is way more exploratory. It analyzes files. It thinks about the best practices and just tries to create the best practices. It makes the optimal plan for implementing what you gave it. (t: 360) After it finishes planning, the next step is code, right? You actually tell it to write the code for the feature that you requested or to fix the bug that you wanted to fix. And lastly, you commit to GitHub. (t: 370) Now Anthropic recommends that during the planning stage, you should consider using sub-agents. What that means is that inside of Cloud Code, you just tell it launch a sub-agent to figure out how we do the open router contact (t: 380) in the backend. And then it will launch a dedicated research sub-agent to find out about that part and report back to the main agent. And then it will send you a sub-agent (t: 390) to the main agent with the findings. And you can see that it's using sub-agents if it does this task thing. If you see task at the end, you know that it delegated it to a sub-agent (t: 400) with this instruction right here. Another great tip that you have to use is using the specific phrases such as think, think hard, think harder, and ultra think. These are actually mapped directly (t: 410) to increasing levels of thinking budget, AKA how many reasoning tokens Cloud Code will dedicate before answering you. So this isn't just some weird problem engineering trick. This is a very simple way to get into Cloud Code (t: 420) by the entropic engineers. So if you're having trouble with some bug, just tell Cloud Codes to ultra think before fixing it and you'll get much better results. (t: 430) The second Cloud Code workflow which you have to start using is test driven development, AKA write tests, commit, code, iterate, commit. So how this differs from standard development (t: 440) is that you first write the test, AKA first you tell it how you want this function or this feature to behave and the expected outputs. Then you write the test, that will fail on purpose (t: 450) because you haven't written any actual code yet. Then you will commit the tests once you're happy with them. Then and only then you tell Cloud Code, hey, write the code for the feature. But you have to tell it that you're doing test driven development. (t: 460) That way it's not confused what is happening. Then you iterate over the code until you're happy with it. And finally you commit the complete feature. Now test driven development is actually one of entropic's favorite workflow. (t: 470) But entropic is the company that created Cloud Code. And they say that this is especially good because there are a lot of applications that are easily verifiable with unit integration or end to end tests. (t: 480) So here are a few examples of when test driven development is better and when it is not ideal. So for example, when you're coding your backend APIs or when you're building business critical logic (t: 490) where bugs have a high cost or when you're doing library or SDK development where there's clear expected behavior. Now test driven development is not ideal in UI heavy apps, right? (t: 500) Apps with lots of user interface, lots of visual stuff or when you're rapid prototyping. If you're building quick and dirty apps, test driven development is probably going to slow you down too much. The third workflow you should use is (t: 510) write code, screenshot results, iterate. So when you have Cloud Code open, you can just drag in screenshots and if you hold shift, you can drag them in straight into the input field. (t: 520) So maybe you like this design style of these images, right? You can just screenshot them, drag them into Cloud Code and tell them build my front end in the same style. And it will do that. (t: 530) Now to make this even easier, just give it Puppeteer MCP or Playwright MCP like I told you earlier and it can take the screenshots by itself, saving you even more time. Now the reason why Anthropic recommends this workflow (t: 540) is that like humans, Cloud's outputs tend to improve significantly with iteration. So while the first version might be good, after two to three iterations, it will typically look much better. (t: 550) And workflow number four, which I also use myself, is to use Cloud to do all of your Git operations, right? So as you can see, I have create PR command that creates pull requests. I've also explained pull requests (t: 560) that is super useful for reviewing PRs from other developers. It starts by running GitHub PR view, GitHub PR div, then it checks out to that branch and gives me testing instructions (t: 570) how I can see if this PR works. I also have this command for reviewing specific files inside of a pull request and another GitHub command that lets me do pushes to GitHub (t: 580) just by typing slash push to GitHub instead of having to stage all the files, write the comment message myself, check if I'm on the right branch and then push to that branch, right? This instruction, (t: 590) which is just 30 lines of prompting takes care of all of that. Saving me tons of time every single day. And even the engineers at Anthropic use Cloud for over 90% of their Git interactions. (t: 600) But if you think these four workflows are OP, just wait until you see what's coming. Now, just like Cloud Code is the most agentic coding tool out there, (t: 610) Vectl is the most agentic task management tool. This is an AI tool I built for myself and my team that will massively boost your productivity. So the way Vectl works is that you give it a bit of context about yourself, (t: 620) your word description, short term results, long term focus, long term goals, and then it can use that context to, for example, prioritize your tasks, right? So here I say, please prioritize all of the tasks on my list, (t: 630) updating their importance to be more relevant to my goals. It delegates that to the task agent. And now you can see the task would be reorganized to be more relevant to my goals. And you can go through them one by one, (t: 640) just like you can go through the changes inside of cursor. You can also use Vectl to generate more ideas for you. Boom, there we go. Four ideas generated. Or do automatic daily research on a specific topic (t: 650) such as AI advancements, new prompting techniques, the best open source models, or whatever you want. It can also manage all of your projects, whether that's personal or your team projects. Plus, if you click on the stats button, (t: 660) you can see how productive you are compared to other Vectl users. Oh, and by the way, we also have an MCP integrations so that you can connect Vectl into Cloud Code, into Cursor, into N810, and use it from there. (t: 670) So if you want to be on the cutting edge of AI and use the most agentic productivity tool in the world, go to Vectl.ai and try it yourself. You can get started completely for free. (t: 680) The next step in the Cloud Code Bible is to optimize your workflow. So first off, you want to be specific in your instructions. The success rate of Cloud Code improves significantly with more specific instructions, (t: 690) especially on first attempts. So let me show you a few examples of the difference between bad prompts and good prompts. So on the left, you can see what bad prompts look like, such as add tests for file name (t: 700) or why does Execution Factory have such a weird API? These are vague. These are not specific. They are too short. A much better version would be look through Execution Factory (t: 710) and summarize how its API came to be. This is way more instructive. It gives it a direction and it will likely result in a much better outcome. The next step, how to optimize your Cloud Code workflow (t: 720) is to directly mention files you want Cloud to look at. So let's say I want Cloud to update the design of our sidebar on the left. What I would do is look at left sidebar (t: 730) and tell me how it's structured. Boom. And now Cloud knows exactly what file I'm talking about and what I want to be changed. And there we go. (t: 740) Here is how the file is structured. Header section, quick action buttons, main content area, footer, key features. Oh, and by the way, this is also how you can learn a new code base super, super fast. (t: 750) Another tip is to give Cloud Code specific URLs because it can actually browse the web. So let's say I want to learn this tool calling for OpenRouter and how to do it, what it can do, stuff like that. (t: 760) Sure, I could probably copy this, put it into Markdown file and tag it, but that's kind of slow. All I need to do is paste in the URL into Cloud Code and say, implement this tool calling in our backend. (t: 770) And Cloud Code will use the fetch tool to browse the website and read all of it itself. Another pro tip is to course correct early and often. So if you notice, I'm using the escape button quite a lot, right? (t: 780) And that's when it says interrupted by user. So anytime you need to course correct or interrupt Cloud, maybe you see it going in the wrong direction, just press escape and you will stop running it. (t: 790) That way you can prevent it from going down the wrong path. Also make sure to use slash clear and slash compress commands. These are built-in commands into Cloud Code that handle the context engineering part of Cloud. (t: 800) So slash clear will completely reset the conversation history. It will delete all of the context and start from zero. Slash compact, on the other hand, will not completely get rid of everything. (t: 810) It will just heavily summarize it for the most important information. That way you can keep working on the same feature or the same bug. The fifth part of the Cloud Code Bible is (t: 820) to use headless mode to automate your infra. So not many people know that Cloud Code actually includes a headless mode for non-interactive contexts like CI, pre-commit hooks, build scripts, and automation. (t: 830) All you need to do is use the dash P flag. So when you run Cloud Code, let me open a new terminal, you do Cloud, this is the default command to activate Cloud Code, (t: 840) but to trigger headless mode, you need to do the dash P flag and then your prompt right here, whatever it is. And if you wanted to stream JSON output, you can do dash dash output format stream JSON. (t: 850) Now headless mode is actually kind of OP because it can power automations triggered by GitHub events. For example, what we do in our team at Vectl is that on every single event, every single pull request, (t: 860) so here's a PR I created yesterday, Cloud will run and it will analyze the pull request, right? So here it actually found a critical issue which I missed during the refactor. So I addressed it and in the next review, (t: 870) you can see that everything was good and it was safe to merge. So this is yet another way you can reduce the amount of bugs in your code and save yourself even more time. Oh, and one essential thing if you're going to do this (t: 880) with GitHub Actions is please make sure to customize this prompt. Otherwise, you're not going to have nice and concise reviews like I do right now because by default, (t: 890) Cloud code with GitHub Actions is super verbose. It will literally spam your pull request review with like paragraphs and like endless pages of feedback that nobody reads. (t: 900) So what you need to do is you need to find the YAML file right here, cloud.yaml it's going to be in your GitHub workflows if you use GitHub Actions and this is super essential. (t: 910) Adding these custom instructions. Without this, it will be super, it will be yapping forever and nobody will read it. So make sure you give it this system prompt. And the last thing you need to do (t: 920) is to use the headless mode is to use Cloud as a linter, right? It can be a more advanced way of linting such as looking for typos, stale commits, misleading functions or variable names. (t: 930) Stuff that a normal linter will never catch. Now the sixth part of the Cloud Code Bible is to uplevel with multi-Cloud workflows, right? So actually some of the most powerful applications involve running multiple (t: 940) Cloud instances in parallel. So for example, you can have one Cloud to write the code and another one to verify. So here is the five step workflow that I have for you to do. (t: 950) First, use Cloud to write the code. Then second, use slash clear to reset the context window. Then you have the second Cloud review the first Cloud's work, right? (t: 960) So just like a developer on your team would review another developer's work because they have separate context windows so it's not the same AI agent. Step number four, you start another Cloud (t: 970) or do slash clear again to read both the feedback and the original implementation. And then you have this third Cloud agent edit the code based on the feedback emotional attachment to the feedback (t: 980) because it didn't write it itself. Another multi-Cloud workflow you can do is not waiting for your main Cloud Code to finish just start another one in a 50-50 screen. So this is what I do all the time (t: 990) inside of Cursor. I have one Cloud Code on the left then I launch another one on the right and usually one of them has the Opus model so you can do slash model to choose which AI model it uses, right? (t: 1000) So Opus is obviously the more powerful but slower. Sonnet is the faster one so typing in slash model lets you select which LLM Cloud Code uses Sonnet honestly is great. It's super fast (t: 1010) it can do most tasks so I would definitely recommend you default to Sonnet but sometimes if you want to do something more advanced, you know, big feature, big refactor and you want to squeeze all of the (t: 1020) intelligence that Anthropic has to offer going for Opus just for the planning part and then switching to Sonnet for the building and execution part, that's definitely the way to go. So this 50-50 screen (t: 1030) breakdown is honestly how most of my day looks like. I have two Cloud Codes in these windows and the best part is you don't even have to look at the code because you can see the diffs inside of the terminal (t: 1040) as Cloud Code is changing the files you can see the changes that it's making. So most of the time you don't even have to switch and read the code yourself because you can see the diff in the terminal Another multi-Cloud workflow that (t: 1050) Anthropic recommends is the Git Worktree method. So what you would do is create Git Worktrees this is a way to see multiple Git branches at once so you just type in git worktree (t: 1060) add and then the path. Then you launch Cloud in each of the Worktree by cd'ing into the path and running the Cloud command to launch the Cloud Code and after that you can just create additional (t: 1070) Worktrees as needed and repeat steps 1 and 2 in the new terminal tabs. And another tip which is a flag you can do is //verbose. So when you're launching a new Cloud Code you can do cloud //verbose (t: 1080) and this will show you what it's doing in way more detail. So only use this for debugging because it's not really practical. Now if you want to take your knowledge of Cloud Code even further I've made an entire (t: 1090) workshop named Cloud Code Mastery this is accessible in the new society inside of the classroom when you click on Code with AI. There's the ultimate code index guide but right below it is the Cloud Code (t: 1100) Mastery. This is a step by step training that will take you to the top 1% of Cloud Code users plus you get access to all of the resources such as my full Cloud MD file, my own (t: 1110) settings.local.json file, my protocol.md and all the other prompts and resources and commands I use inside of Cloud Code. So if you want access to the Cloud Code Mastery workshop make sure to join (t: 1120) the new society. It's going to be linked below the video. With that being said thank you guys for watching and I wish you a wonderful productive week. See ya.

