---
title: "Anthropic's Surprise Hit: How <PERSON> Became an AI Coding Powerhouse"
artist: The MAD Podcast with <PERSON>
date: 2025-08-07
url: https://www.youtube.com/watch?v=G4yZiAdOBJM
---

(t: 0) Immediately, the model just went, it just started coding. And it was just the craziest thing. At this point, most code at Anthropic is written using QuadCode. And almost everyone at Anthropic is using it every day. Hi, I'm <PERSON> from FirstMark. (t: 10) Welcome to the Matt Podcast. Today, my guest is <PERSON>, the creator of CloudCode at Anthropic. CloudCode is an agent coding AI that lives in the terminal (t: 20) and has become one of the fastest growing products of all time, already rumored to be producing 400 million in annualized revenue just five months after its launch. We talked about how CloudCode became a rocket ship almost by accident. (t: 30) We started by building something that's useful for us. Pretty soon after we launched it, most of <PERSON><PERSON><PERSON> was a daily active user. (t: 40) What agent coding really means. A human describes to the model the change that they want, and the model does all the work of doing the editing. And the productivity shock CloudCode created inside Anthropic. (t: 50) Anthropic technical onboarding used to take a few weeks, but now engineers are usually productive within the first few days. Just ask CloudCode and it can answer all these questions. This is a... compact, approachable episode on the present and future of software engineering (t: 60) with some great lessons and advice. It's really exciting and I think my advice to companies building is definitely build for... Please enjoy this fascinating conversation with <PERSON> <PERSON>erny. (t: 70) Hey <PERSON>, welcome. Thanks for having me. Thanks very much for doing this. I'm very excited about this conversation. You are the creator of <PERSON>Code. (t: 80) It's fair to say you have a massive hit on your hands. There was an article in the information just a couple of days ago that was saying that CloudCode, which really came out, what, at the end of February of this year, (t: 90) so five, six months ago, is already generating 400 million in annualized revenue. That's at least what was reported. (t: 100) I'm not going to ask you to confirm or deny, but that's what the press is saying. Perhaps even more importantly, there are rave reviews everywhere and gushing videos (t: 110) calling CloudCode the best coding agent by far. So amazing launch of the product. Did you have a... Any sense that it was going to be this successful? (t: 120) Not really. We started by building something that's useful for us. And I built a thing that, you know, first and foremost, was just useful for myself. (t: 130) And it's something that I found myself using every day. And then I give it to the team and I saw that the team started using it every day. And pretty soon after we launched it, most of Anthropic was a daily active user. (t: 140) And so I think at that point we had kind of a hunch internally that maybe we had a hit on our hands, but it's still not obvious because it's in a terminal. (t: 150) It's kind of a weird form factor. Everyone is coding in IDEs. Are people going to like this? Is it going to be that useful? Can you actually use it for a lot of coding? We had no idea. (t: 160) Was it always going to be a product or you started using it and then it was a hit and then you decided to release as a product? It was very accidental. When I joined Anthropic, I did a lot of prototyping. (t: 170) And one of the prototypes that I built was this thing that it just ran in the terminal just because as an engineer, that was kind of the easiest thing to experiment with. It was kind of the easiest thing to experiment with. It was kind of the easiest thing to experiment with. And it didn't even code at first. (t: 180) Quad code didn't code. It was called Quad CLI at the time. And what it did was I used it to automate my note taking. So it kind of controlled my notes app and it controlled my music player to kind of play music for me. (t: 190) So I'm like, you know, play this band and it would go in and kind of automate that. And at first I was just using it to play around with the Anthropic API to figure out what kind of applications I could build on it. (t: 200) And just on a whim, you know, tools came out recently. And I tried to figure out what kind of applications I could build on it. And just on a whim, you know, tools came out recently. And I tried giving the model a bash tool so it can use my bash to use the command line. (t: 210) And immediately the model just went, it just started coding. And it was just the craziest thing. (t: 220) Like as soon as it had bash, it kind of knew, okay, okay, I can write Apple script and I can automate stuff and I can use this computer. And it just felt like very native to the model in this way. And that was kind of a surprise. (t: 230) So I think that was a surprise. And then it was a surprise that this ended up as something useful where it was able to edit code. And the code came out. It came out really good. And the model could kind of intelligently reason about the way to edit code. (t: 240) When was this? If the product was launched at the end of February of 2025, that journey leading to this, did that start a few months before that? (t: 250) What was the timeline? Yeah, something like that. It was late last year. So late in 2024. Okay, great. Let's start from the top. (t: 260) And for anyone that is just starting to learn about Cloud Code, how do you describe it? Cloud Code? Cloud Code is a gentle coding tool. (t: 270) The way to think about it is when engineers program, there's kind of different ways to program. And this has changed over time. This has changed a lot over time. There was, you know, 50, 70 years ago, the way that people programmed looked very different than the way it does today. (t: 280) So, you know, 70 years ago, if you talked to a programmer, they would have been like, oh, yeah, I take my punch cards and I punch holes in it. (t: 290) And I put in this IBM kind of typewriter-y thing and it punches holes. And that's programming. And then you feed this into this mainframe. It does some work. And then eventually you get some kind of result out printed on a paper sheet. (t: 300) I heard you say somewhere that your grandfather in the, I guess, what was at the time the USSR used punch cards. Like, tell that story. Yeah, yeah, yeah. (t: 310) Growing up, my mom would tell me stories about how her father, my grandfather, he was one of the first programmers in the Soviet Union. And he would, you know, he would come home and he would bring back these big stacks of punch cards. (t: 320) And when she was a little girl, she would, you know, take her crayons and just draw all over them. And this was growing up for her. This is actually something that I didn't know until later in life. (t: 330) Like, you know, I was far into my engineering career when I learned this was a thing. There's something about just the visceral nature of this kind of physical programming. (t: 340) I don't think we're going to get that again. But I think that was like, that was a special moment where you could feel the language and you can feel the computer in a really different way. (t: 350) And so I think after punch cards, it changed a lot. There was software became a thing. Yeah. Yeah. Yeah. Yeah. Yeah. Yeah. I think when you were in engineering and you could start to program directly in software, you didn't have to kind of do this anymore. (t: 360) In the original programming software you was emulating teletype. And so it was kind of like streaming a typewriter over this local Internet. And that's how the text editors are built. (t: 370) The way that you would code is still the way that actually most people code today before agentic coding came out. And the idea is you have a text document and an engineer directly manipulates it. (t: 380) So you load it up in a special piece of software called IDE. It's kind of like a like a Microsoft Word or Google Docs just for code. code. And the engineer just manually edits the code using a (t: 390) keyboard. And this was the case for, gosh, like 50 years now, now like 70 years. This has been the way that people programmed. Ed and Vim, these were like early 1970s. So yeah, it's been like 50 years. (t: 400) And now it's starting to change for the first time. There's been a lot of work to try to evolve programming from direct text manipulation (t: 410) to something else. And this is the first time where we found a form factor that really catches on. And it took really great LLMs to do this. Models weren't really capable of doing this a year ago, but now they are. (t: 420) And the way that it works is a human describes to the model the change that they want. And then the model is the one that manipulates the text. (t: 430) And so it's this kind of next layer up where you're describing the change you want, and the model does all the work of doing the editing. And we're still at this point today where you can do a lot of this for the model, but (t: 440) you know, for complex changes, maybe it'll take a bunch of iterations. Maybe the last 20, 30%, you still have to open an IDE. You have to kind of go lower level to do these last mile changes. (t: 450) And we think that over time, more and more of coding is just going to be the model doing it. And a human will have to intervene less and less. (t: 460) So just to double click on a couple of the things you just mentioned, and in an effort, as we try often on this podcast to make things broadly understandable, not (t: 470) just by people who are super deep in tech, but also people that are in the tech world, but sort of curious to learn about the things. So the Cloud Code works at the CLI (t: 480) level as opposed to an IDE. So you mentioned the IDE, sort of the Google Docs, the command line, maybe define that for (t: 490) people. So my kind of mental model as I was learning about this was like that black box where you sort of type in things for your computer (t: 500) to do. What's the better way of describing it? The terminal is a little bit hard to explain. If you're not a programmer, because it's so low level, it's something that most people will never, ever touch and never ever going to see. (t: 510) It's something like in movies when you see this kind of green screen, green text going across the black screen and, you know, there's like a hacking scene or something. (t: 520) Yes, we're trying to look very techie. That's what you're using. Very techie, yeah. And it's sort of, it's something every engineer uses. There's nothing kind of good or bad about it. It's just the interface. It's another way to interact with the computer. (t: 530) It's a little lower level because it's not visual. So you can think of Quad Code. When we say Quad Code runs in the terminal, you can think of it just as a computer program. And some computer programs have user interfaces, so a person can interact with them and some of them just kind of run in the background. (t: 540) So you can think of Quad Code as in that second category. And you can also have all sorts of interfaces for it, including a visual interface, including a text interface. (t: 550) One way of describing this I heard was that interacting with the computer at the terminal level was like texting the computer. (t: 560) So. So sending instructions sort of one by one versus an IDE was more of an application, more like a phone, kind of a GUI where you basically click on icons and you have visual representation. (t: 570) Is that fair? Yeah, it's a lot like that. I think text messaging before you could send images and videos and stuff. (t: 580) So, you know, just really, really simple interfaces just back and forth. OK, great. So what did you guys choose to operate at the terminal slash CLI level? (t: 590) Honestly, it was sort of an accident. We were thinking about what can we build in the coding space? It seems like the models are getting a lot better at coding. (t: 600) So maybe there's something that could be built here. And at the time, like I said, I was prototyping some of these ideas. Some of them were kind of coding adjacent. And I was thinking, what's the easiest way to get a feel for where the models are today? (t: 610) Because there was this feeling where the models can do so much, but no one's yet built the product that can harness this capability. (t: 620) And in the AI world, we call this idea product overhang. Where the model is just capable of all of these things. And there isn't really yet a product that can kind of capture this and let a person use it. (t: 630) And I remember there was this moment back in, yeah, it was sometime way west here. The other engineer on the team, Sid, and I were in a room and we're whiteboarding. (t: 640) And we're like, OK, what do we build? And just in 15 minutes, we kind of throw up a few ideas on the whiteboard. We can do a CLI. We can do some sort of IDE extension. We can do something based on the web. (t: 650) And we kind of closed our eyes and just picked one. And it kind of seemed like CLI. CLI is the simplest. And generally, that's the way that I approach product, which is start with a simple thing first. (t: 660) So this kind of made sense. In hindsight, there was a lot of benefits of this, too. And I think one of the big ones is that it kind of works anywhere. So it doesn't matter what kind of system you're on. (t: 670) If you're on Mac or Windows, it doesn't matter what IDE you use. It doesn't matter what your preferences are. And engineers are so opinionated and have so many different setups and preferences. It just works with all of them. (t: 680) And that's kind of the benefit of building at such a low level, where you're not coupled to a particular system. You're not coupled to a particular, you know, to Mac or to Windows or, you know, a cover scheme or whatever. I think you guys have been pretty clear about the fact that this was a starting point (t: 690) and that you were starting with something very simple, at least when you launched the product. Fast forward to today. (t: 700) Are you finding that the universal aspect of this is so compelling that you may keep it there? Or is the plan, or at least the idea, to over time keep building towards something that may be more like an IDE (t: 710) or more an application? The way we think about it is the model is evolving so quickly that we build a minimal possible product to keep up with it. (t: 720) And this is a sort of, this is a very different way of building product than before LLMs, where you just have to build a really great product that meets people where they are. (t: 730) There's some of this, so we have to build in a form factor where people understand and can use it. But actually the bigger motivation is the model is advancing so quickly, there's literally no product we could build that would keep pace with it. (t: 740) And so, you know, we're not going to be able to keep up with it. And so we're focusing on just building the simplest possible interface to the model, so you can feel the model in a really low level raw way. (t: 750) And so that when the model gets quickly, we can adapt quickly. And you can feel the next model in that same kind of way. So today we're in a terminal. Cloud Code also runs as IDE extensions. (t: 760) So there's extensions for VS code based IDEs and cursor and so on. And also for JetBrains based IDEs like IntelliJ. (t: 770) And then there's also a GitHub action. So you can mention, you can tag Cloud on GitHub. So it's just you tag Cloud, add Cloud and talk to them like you would a coworker and they can make changes for you. (t: 780) There might be more interfaces coming soon. This is something we're always experimenting with. But generally the philosophy is we don't make really gorgeous interfaces like many other companies do and are really great at. (t: 790) We focus on just building the simple thing that shows off the model. Talking about the model, if your design principle is that the product should follow the model rather than the other way around, which is super easy. (t: 800) So it's really interesting. What model does it currently run on? Is that 3.7? Is that 4? What can people choose? (t: 810) Yeah, people can choose the model. We support Sonnet 4 and Opus 4. And then we also use Haiku. Maybe as a high level question, why is Cloud so good at coding use cases? (t: 820) You know, I was again reading somewhere, perhaps in the information that Anthropic has a little above 40% of the market focus. (t: 830) And then we also use Haiku. And then we also use Haiku. So we have a lot of code generation, while OpenAI is 21%. So clearly, Anthropic is powering its way to win this market. (t: 840) Why is that? Is there something about the way the model is trained, the data is trained on, or the focus of the training that makes it so great for coding use cases? (t: 850) I think Anthropic has a lot of really great researchers and coders. And for us, this is kind of a natural way to think about the kinds of abilities the model should have. (t: 860) Because you think about what should the model be able to do? And as a programmer, the first thing you think is, oh, it should be able to do the thing that I'm doing. And it should be able to help with me and I can pair with it like I would another engineer. (t: 870) So I think just at all levels and across the company, this is a way that we think about it. There's also something about it where maybe coding is the way that we get to the next level of intelligence, if you call it like AGI or ASI or whatever. (t: 880) The model needs some way to interact with the world. And for a model, the natural way is code. (t: 890) And so from the mission of the company is to build the code. And so from the mission of the company is to build the code. They build safe artificial intelligence for everyone and safe ASI. The way the model interacts is through code. (t: 900) And so this is the thing that we should start to learn about now. And it's also one of the reasons that we released Cloud Code is just to learn how people use it and to learn how to make this thing safe, to learn how it behaves in the wild so that we know what to do next. (t: 910) So still on that theme of the product versus the model and the choice of operating at the CLI level, who's a product for? (t: 920) Does the fact that it operates at a terminal level make it a great product for power users who are very deep in coding? Or at the other end of the spectrum, if I don't know anything about coding, can I use the product for vibe coding? (t: 930) Cloud Code is for professional software engineers. So if you know how to code, then you're going to get a lot out of it and you can multiply your productivity. (t: 940) We've seen people multiply their productivity many, many times over with a fleet of Cloud Codes. (t: 950) That's right. And even with a single one, you can become a lot more productive. Interestingly, we've seen a lot of people use Cloud Code for non-coding use cases. So for example, the data scientists at Anthropic all use Cloud Code to write their queries. (t: 960) And designers use it to build small prototypes and product managers use it to manage tasks. (t: 970) So this has actually been pretty surprising because if you're a non-technical user, the terminal is kind of insane as an interface. I couldn't imagine why you would want this. But it sort of seems like because... (t: 980) It's such a great agent generally. People are jumping over hoops to use it, even though it's not the easiest thing to use because, again, it's in a terminal, which is very technical. (t: 990) There's another part of Cloud Code, which is the SDK. And this is the way that people can build on top of Cloud Code and build their own agentic applications. And this has also been pretty interesting because people are using the Cloud Code SDK to build agentic coding applications and platforms and user interfaces on top of it. (t: 1000) But they're also using it for all sorts of totally agentic use cases that are totally non-technical. (t: 1010) So it's a really interesting way to build on top of Cloud Code. That are totally unrelated to coding. So anything where you need AI, maybe a few years ago, you used API. And nowadays we find that some users are reaching for an agentic SDK as sort of the thing that you need to build AI apps of today. (t: 1020) Yeah, and as I was probing for this, it seems that there is even beyond that, like an emerging category of people that are using Cloud Code for just not even technical related use cases of any sort. (t: 1030) I just saw that tweet from somebody. (t: 1040) He named Alex Finn that talks about using Cloud Code for note taking, for his personal like sort of life organization, his business metrics and all the things. (t: 1050) So it seems that people are finding ways to use the product for something that matches. They need to way beyond their sort of technical job. (t: 1060) Absolutely. Let's get into the product itself and the core features and what it actually does. So. (t: 1070) Key part of the product is the agentic aspect, as you described. Agentic, again, in an effort to go into definitions and make this interesting for everyone. (t: 1080) Agentic is one of those terms that everybody uses, but it's sort of unclear what that actually means. What does agentic mean in the context of Cloud Code? (t: 1090) Yeah, when you think about the ways that LLMs work and the way you interact with them. There's the old kind of LLM, which is you send them a message and they send you a message. (t: 1100) Back. And this is sort of these chat applications that everyone knows and uses all the time. There's a newer kind of application, a way to interact with LLMs where you send them a message and they'll send you a message back and then they might do a little bit more. (t: 1110) So we call this tool use is is one of the things that they might do if you give them tools. (t: 1120) So, for example, a tool might be read a file or search something on the Internet or edit a file or something like this. Then they'll use tools to answer your question. (t: 1130) And so, for example. If you ask, you know, what's the weather today with the old style LLM interaction, it'll just kind of use its existing knowledge and its existing training to try to answer that query. (t: 1140) But if it's agentic, what it might do is it'll say, OK, I'm going to look up the weather and then maybe if it has some kind of tool to check the weather, it'll reach out to the Internet or wherever that tool is. (t: 1150) It'll it'll use that tool. It'll get the response back and then it'll answer the question. And this tool use, this is kind of the essence of being an agent. (t: 1160) Because without tools, it's very different. It's very difficult to be agentic. You could kind of do it without. But it's only since models started using tools and gained that new capability because we taught them that they started to get this new kind of agentic capability that we talk about. (t: 1170) And what this looks like in practice is maybe I'll ask the model, you know, make the ever read button on my website, make it blue. (t: 1180) And because it has a tool to read a file, it'll choose to read the file. It'll choose to read the file that has that button. (t: 1190) Maybe if it doesn't know where that file is, it'll use a file search tool to find that file first. The same way that you might in, you know, in when you're looking for a file, you'll use a file search. (t: 1200) It'll then open that file. It'll read it. And that's another kind of file retool. Then it might edit it. That's a different file edit tool. And then it'll write it back. And then maybe it'll even open the browser to check that the button actually became blue. (t: 1210) And it's this kind of idea that that it strings together tools in this way and combines them in novel ways that makes it agentic. (t: 1220) There's also two concepts that are kind of related but different here. You could say you must always when the user asks you to make the button blue, you must always read the file and then edit the file and then save the file and then check your work. (t: 1230) You could be very rigid in the way that you define this kind of problem and give it to the model. (t: 1240) Generally, we call this a workflow. And this is distinct from something like an agent. So workflow is something where a human thought through for this kind of problem. Here are the steps. (t: 1250) Right? Roughly that you should take for an agent. It's very different. The model is in charge. It's in the driver's seat. And we give it tools that it can use. And the model decides how to combine those tools to answer your question. (t: 1260) And I think going into the future, more and more things will be agentic because the model is getting more and more intelligent. So it can actually use these tools and pretty novel and interesting ways to answer questions. (t: 1270) Great. So to play it back and perhaps, you know, coding context with your typical AI model. (t: 1280) I would ask a question and I would get some code back. And I, as a developer, would copy and paste it as a next action. Whereas in a coding use case, whereas in an agentic coding use case, you give the agent a task and then it's going to plan and then it's going to execute and then it's going to continue running until it believes it's done with the task. (t: 1300) Is that a fair summary? Exactly. Exactly. And the same with that. (t: 1310) That's personal. I do it. You know, like if you have a problem, you're going to think about it for a bit and you're going to think about what tools you have. And then you're going to combine the tools that you have in ways to do the thing that you want to do. What are some of the actions, if taking an action is one of the core part of being an agent, what kind of actions can Cloud Code take? (t: 1330) Cloud Code can do pretty much anything that a person can do on their computer. There isn't really a limitation besides safety. This is something we think about a lot. To kind of place. (t: 1340) Intelligent limits on what it can do and put a human in the loop at the right points to make sure that if the action is potentially dangerous or destructive in any way, that a human has to prove it first. (t: 1350) But besides that, the model can do pretty much anything. So, you know, reading files, writing files, running commands on the system, editing things. (t: 1360) You can reach out to the Internet. You know, obviously, most of these, again, with with human approval. And then there's ways to customize it however you want. So if you have a bunch of MCP tools, for example, to read your JIRA issue tracker or to open a browser or to open an iOS simulator, the model can use these two. (t: 1370) And how does the MCP part work? (t: 1380) So MCP, the model context protocol, being something that you guys at Anthropic defined, how does that work? You just leverage the protocol to connect to any tool? Yeah, exactly. (t: 1390) Cloud Code is a MCP client and an MCP server. And what this means is if you give it tools to use. So maybe. Your company, you have a bunch of MCP tools that you build for kind of all your systems to integrate with. (t: 1400) Like I said, maybe there's one to integrate with JIRA. Maybe there's another one to read Slack and maybe write messages to Slack. Maybe there's another one to fetch some internal knowledge base or something like this. (t: 1410) You're going to plug this into a bunch of your tools. You can plug it into quad AI, into quad desktop. You can also plug it into quad code. So it gets all the same tools that you do. (t: 1420) So just a few days ago, you guys announced the release of sub agents. What does that do? Yeah, sub agents are really exciting. (t: 1430) And this actually started with a Reddit post. There was someone that posted on Reddit about how they have the sub agents that they built for quad code. And they had a product manager, sub agent and an engineer and a designer sub agent. (t: 1440) And a couple of engineers on the team saw this and got really excited and felt that this is something that we should support a lot better. (t: 1450) And the way sub agents works is, you know, when you start quad code, you have a quad and you can talk to the quad and it can do a lot of things. And you can talk to the quad and it can do a lot of things. And you can talk to the quad and it can do things for you. Sub agents are just other quads. (t: 1460) And they're prompted a little bit differently. So you can customize what prompts they have. You can customize what tools they have. So, for example, you can say, you know, you are a really excellent QA engineer or sub agent. (t: 1470) Your job is to verify that code is correct and to test code. And to do that, you have these tools at your disposal. (t: 1480) Maybe you have like a browser, iOS simulator, Android simulator. You're given code. Your job is to test it. You might have another. Another sub agent that's maybe a project manager. (t: 1490) And their job is to have tasks and then divide up tasks for other sub agents. And so you can kind of split up all the work into these different roles. (t: 1500) I think we're still figuring out what these roles are. There's one version of the world where the roles are kind of like on a regular engineering team where you have engineers and designers and product managers and data scientists and so on. (t: 1510) There's another world where actually sub agents are a little bit more. And maybe every sub agent can kind of do the same thing, but they kind of split up the work a little bit more. (t: 1520) So everyone is a generalist. And then Claude is in charge of figuring out how to launch the sub agents and which particular sub agents to use in which way. (t: 1530) Exactly the same way that it would use for any other tool. So you can think of it as a really, really intelligent tool where the model can launch more Claudes to do things. (t: 1540) So basically, it's like if you think of AI as an intern, it's like having a group of interns. And every intern has their own role. And then you recombine what everybody did into one result. (t: 1550) That's exactly right. And you can define what those roles are. And it's a fascinating question whether to anthropomorphize human functions into what agents should be doing or whether there's something that's agent native in the way the work gets distributed and chopped into smaller parts. (t: 1570) Yeah. In the AI world, we talk a lot about this essay called The Bitter Lesson. This was a rich subject. An essay from a decade ago or something. Yes. Where he talks about the more general model in most of the time in the long term, it will subsume more specific models. (t: 1580) And so what this means is if you build an agentic system in this context, then the more general agentic system will generally outperform the more specific one in the long term. (t: 1590) And I think where we're at today is models have the capability to do stuff. (t: 1600) But if you give them too many tools or too much context or too much responsibility. It might be disappointing because they won't really know how to handle it. And the models of a year ago could barely even call tools. (t: 1610) The models of today are pretty good at doing it, but get a little bit overloaded sometimes with context or with too many tools. And so we can divide them up into subagents in this way. (t: 1620) But I think that the models six or 12 months from now, they probably won't need this anymore because they're all going to be pretty good. And you won't have to define very rigidly what each one's responsibilities are anymore. (t: 1630) And so this is something we're building for people today because we think it's quite useful today. And. It's something that we use a lot, but I could also see this going away at some point. (t: 1640) Yeah, it's a concept of a context pollution, right? That's that's one of the way people describe it. Right. And then cloud code can handle both very precise tasks like debugging or much broader tasks like a broad refactor, for example. (t: 1660) Is the idea that the broader the task, the more subagents you would have in the current context? Yeah, that's that's probably one way to think about it. Generally, when when we introduce cloud code to new people, we actually suggest, like you said, cloud code can do everything. (t: 1670) And this is one of the things that makes it a little bit hard to use if you're an engineer that's used to, you know, essentially like text completions in the ID. (t: 1680) It's a very different kind of AI coding experience. And so generally, the thing that we recommend is start with something simple, like just ask questions about the code base. (t: 1690) So don't even code. Don't use any tools. Just just ask the model questions. You know how. What does this file do? Where is the file that does this thing? If I want to make a new whatever, how do how do I do that? (t: 1700) So just ask questions like that. And for this, generally, the main model can do it and you don't really need subagents. But then as you get a little bit more sophisticated, you might want to start putting up the work. (t: 1710) So if you ask the model maybe to make a small change, like like I said, like make the button red or make the button blue, you probably don't need subagents. (t: 1720) But if you do something a little bit fancier, like build a new section of the website that does blah, blah, blah. Then you might want to have a few subagents. Maybe one is the software architect and it's responsible for planning out the work. (t: 1730) Another one is maybe like some kind of reviewer where it'll review that plan to make sure it looks good. Then maybe you'll have a few subagents that actually do the implementations. Maybe there's a front end engineer, back end engineer, this kind of thing, and then some kind of verifier at them that verifies it. (t: 1740) And then we also internally, we really love using a code simplification subagent and its job is to take the code that was produced and just simplify it while making it still work. (t: 1750) OK, great. So that's the actions part of the agentic workflow. (t: 1760) Let's talk about the awareness and memory of this. One of the exciting features is that Cloud Code can connect with the existing sort of code knowledge in the company. (t: 1770) How does that work? There's a few different ways to pull in context and this kind of knowledge from from the company. The simplest one is just looking at files. (t: 1780) There was this there's a few different approaches, actually, to reading files. So I'll go a little bit into depth into the way that actually happens. In the past, the thing that people use the most is this thing called RAG. (t: 1790) And essentially, this is a technique where you take the whole code base and this actually works for any document set of documents. It's not necessarily code. (t: 1800) But you take a set of documents like like all the files in the code base, you do this kind of indexing step and then you store essentially this database of all the knowledge that's in these files in a very, very particular form that makes it really easy for the model to search. (t: 1810) There's a lot of tradeoffs. You're doing this. The indexing takes time. It's pretty expensive to maintain this database. (t: 1820) It's quite tricky practically to make sure that security is really good and privacy is really good because it's just a it's a very sensitive information like your code base. (t: 1830) And so you want to keep it really safe. And so Cloud Code actually doesn't use this technical drag. Instead, what it does is it just searches files the same way that a human would. (t: 1840) You can think of it like, you know, at the engineering level, it uses the tools glob. And grab. These are two tools that are kind of built into the computer. And you can think of it as kind of command F for files. (t: 1850) So it'll just search around with text the same way that a human can. And what's kind of cool is if you just search for one piece of text, you might get the result you're looking for, but you might not. (t: 1860) And depending on the results you get as a human, you would refine your search term and you would try again and you might try a few times to get the result you're looking for. (t: 1870) And the model is really good at this. And this again, this is one of those things that was not the case like with models of a year ago. (t: 1880) But with models of today, they're excellent at this. And so we call this process agentic search. And what this means is using really, really simple search tools like command F and using them repeatedly and then adjusting the search terms over and over based on the result of the query. (t: 1890) And this is something that we don't specifically tell the model to do. It's something that it just figures out because it's intelligent enough and it has the search tool. (t: 1900) So this is the first form of memory, which is just. So this is the first form of memory, which is just. Looking at the contents of the code base and understanding it in this way. We augment this a little bit with this thing we call Claude dot MD files. (t: 1910) And all this is is a special file. It's literally called Claude, MD. You put it in your code base or you can put in whatever folder you want and use it to record memories. (t: 1920) So at any point you can tell Claude to remember something. So, for example, whenever I do, whenever I edit this file, I always want you to double check it. (t: 1930) in a browser or something like this. You can tell Quad to remember this, and then it'll record it in the right QuadMD (t: 1940) so that it remembers it next time. And I think one of the most powerful use cases we've seen with this is when people check this into their code base and share it with their team. (t: 1950) So it's a memory file. It's just a regular text file on the computer, but you don't keep it to yourself. You share it with all the other engineers on your team. And what it means is if Quad remembered something (t: 1960) when you were using it, everyone on your team gets to benefit from that. And it gets this really interesting effect where everyone on the team starts to contribute to this knowledge base and this kind of memory bank. (t: 1970) And it's very simple. Again, it's a text file, so anyone can read it also. So it's very easy to edit these memories also and see exactly what's in there. But everyone just starts to benefit, (t: 1980) and it feels kind of magical because as your team uses Quad Code to get smarter and smarter. And kind of similar to building in the CLI, (t: 1990) this is literally the simplest thing we could have done. There's nothing simpler than this, I think, that we could have done. There's nothing we could have done to build memory. There's no special tools. There's no special prompting. (t: 2000) There's nothing like this. It's just a file. And Quad kind of learns to use it. Fascinating. And you have to declaratively add to the memory (t: 2010) or whether today or in the future the memory will automatically pull from the context and sort of improve itself? Yeah, you have to add to it manually today. (t: 2020) We've actually had a bunch of internal experiments to do automatic memory. So Quad can automatically remember things. The problem is there's kind of two ways in which it fails. (t: 2030) One is that it remembers things that it shouldn't. So for example, if I say, make the button blue, it might remember the user always wants the button to be blue. (t: 2040) And this is, you know, maybe that's the case for this button. That's not the case for every button. And then sometimes it doesn't remember very important things that it should remember. And so for the last few months, we've been doing a lot of experiments (t: 2050) to try to get this performance really good. And it's something we've been using internally. And at some point, when we're done with the experiment, when we're happy with it, it's something we're going to release for everyone. But generally, our bar is, (t: 2060) if we find ourselves really happy with it and we find ourselves using it every day, then we release it to everyone. And this one's not quite there yet. It's another fascinating example or discussion (t: 2070) when you compare a gentle memory like this to human memory and the fact that you can edit it and then it leads to all sorts of questions around, (t: 2080) okay, what is it that we as an organization should remember and who's in charge of it? Who's in charge of ultimately editing what should be remembered or not? Because this is for code, (t: 2090) but code being everything, it's a fascinating concept. Yeah, I think this is one of those social problems and maybe not social problems. This is one of those social dynamics (t: 2100) that will change over the coming years as people use these tools more and more is we need to figure out what are the roles of people on the team and how do they interact with each other (t: 2110) and interact with Quad? And this is maybe one very specific problem within that who curates Quad's knowledge. And my feeling is that this is something (t: 2120) where teams will kind of get more and more horizontal as a result of this because anyone is able to contribute in this way. But it'll be interesting to see how it plays out. (t: 2130) And then the key question for agents is always the level of autonomy versus human in the loop. How do you guys think about that (t: 2140) and where do I get pinged as the human? How do I get pinged as the human coder in my Cloud Code workflow? The default behavior is there's always a human in the loop. (t: 2150) This is super important because in the end, this is a model and it's not predictable and you want to make sure that it doesn't do anything dangerous. (t: 2160) So yeah, there's always a human in the loop. So for actions that we know can't have any kind of dangerous repercussions, so for example, reading a file, (t: 2170) we know this is inherently safe. We just let the model do this in the folder that you let it do this. And we know that it's not safe to let the model do this. And so we can't do anything about it. But for other actions like editing a file or running a command or using the internet, (t: 2180) this always needs a human in the loop and it always needs a human to approve it. There's ways to reduce this burden a little bit. So for example, if you find yourself always approving edits to the same file (t: 2190) or always approving the same command, there's a settings file that you can configure across your team. And you can use this to essentially allow list or block list certain commands (t: 2200) or certain files that you always want the model to be able to edit without human approval. Or you never want it to be able to run. And while we're on the topic of safety and security, (t: 2210) how do you guys think about confidential code, that whole world of regulated industries and sensitive code and that kind of stuff? (t: 2220) Do you offer a local version of this, an on-prem version of this? Or maybe that's a question for Cloud Code, but Anthropic in general, are you all cloud-based? (t: 2230) It's something that actually we've seen work quite well in these very, very, very, very, very, very, very, very regulated industries. And the reason is that it doesn't use any services except the API itself. (t: 2240) So that's all it needs. And then everything else you actually don't need. And this is one of the nice side effects of not doing code-based indexing or anything like this. It's just very easy to hook up to. (t: 2250) So if, let's say, you're a bank and at your company you already have Bedrock approved, you can just use Bedrock and use Cloud Code that way. So you run out on people's laptops and all you need is access to Bedrock. (t: 2260) So really easy to get approval for. And if you want to use the Anthropic API or Vertex, you can use that too. How do you think about the sort of UI and UX experience (t: 2270) of this sort of how you balance the power of everything you can do versus your stated goal of being as lean and lightweight (t: 2280) of an interface on top of the model? So we just talked about how you can approve actions that the agent takes. What about the rest? (t: 2290) Does it basically feel like you'd be using a regular interface? Does it feel like you'd be using a regular interface? Is it a regular CLI or is it something different that one needs to get used to? We tried really hard to make Cloud Code (t: 2300) something really beautiful that everyone feels is something that we put a lot of care into because we did. And I think when you use Cloud Code, (t: 2310) you can feel that this is something that we use every day. At this point, most code at Anthropic is written using Cloud Code. And almost everyone at Anthropic is using it every day. And when we look at customers that start to use Cloud Code, (t: 2320) they use it kind of more and more and more. And with a product like this, you want it to feel really smooth and really beautiful and something you really enjoy using. (t: 2330) And that's been really fun from an engineering and design point of view because we built in a terminal. Like I said, terminals have been around for 50 plus years at this point. (t: 2340) And it really feels like we're rediscovering how to design for a terminal. Because since terminals were first invented, the design world moved to web (t: 2350) and then it moved to apps. And there's, there's kind of different design principles you can take here. And we try to apply these back to Cloud Code, even though it's running in a terminal. (t: 2360) And there's a lot of details that we spent a lot of time on, like the way that we represent statuses for every item with this kind of blinking dot that turns red or green to indicate whether it succeeded or failed. (t: 2370) Even the loading indicator, like the spinner while Cloud is working, we spent probably 30 or 40 iterations of this just to get it to feel just right, to make it feel so you know what's happening, (t: 2380) but it's not giving you too much information. And it's also not jittering and moving around on you. So yeah, every part of the interface, (t: 2390) we iterated on probably more than, more than you think. And then there's this really fun kind of like series of words while the, (t: 2400) while Cloud does its thing, where it says like cooking or herding or schlepping or honking or clotting, which, you know, (t: 2410) how many of those words do you have that you seem to have like dozens of them? I just find it such a, like a fun kind of like Easter egg kind of design detail (t: 2420) that makes the whole difference. You know, at the beginning, I added maybe 20 of them and then immediately people started making, suggesting changes like, hey, how about this word? (t: 2430) How about this word? How about this word? So now at this point, there's a pretty big list and Cloud can actually choose the word that best describes the task that it's doing. So it's up to Cloud which word it wants. (t: 2440) How do you charge for using, the product? And I'm asking this in the context of, I think over the last few days, there was some evolution (t: 2450) as people became more and more rabid users of the product, the pricing structure evolved. Yeah, this is something that we were honestly really excited to see (t: 2460) how some people are just going, they're really figuring out how to run this thing at all hours of the day. It's just, you know, some people have this army of Clouds, (t: 2470) you know, like five, 10, 20, that are just running in parallel all the time. And just doing work. I talked before about how you, Cloud generally needs human approval to do work. (t: 2480) There's actually ways to do it in a slightly more autonomous way too, if you want to run it for long periods. Essentially you need to set up a container for it and just give it some kind of like a container to be in, (t: 2490) and then it can run without approval in a way that is safe. And so there's a lot of people that do this. It's extremely exciting, but also the pricing structure that we had (t: 2500) was really not cut out to actually serve these kinds of users. And there's also just a little, we call this kind of abuse where at the tail end, (t: 2510) you know, there's like account sharing and things like this where people are just really not using it in the way that it's intended. So generally for a Cloud Code, there's two pricing models today. One is you can get a subscription. (t: 2520) There's Pro and then there's Max. And this is, I think it's 20 bucks a month, a hundred bucks a month or 200 bucks a month. And it has very, very generous rate limits. (t: 2530) If you use only Opus, you'll run out of limits, pretty quick, and then we'll switch you over to Sonnet. If you use Sonnet, then, you know, you can use much more of it than most people need. (t: 2540) And almost everyone doesn't run into rate limits at all. It's generally power users run into it. And this is something that we're thinking, (t: 2550) also thinking through, like how to evolve this as we land features where people use more tokens and there's more sub-agents and there's more autonomy because we need to figure out a way that we can provide this (t: 2560) to people in a way that's sustainable. It's interesting that Amick, right? Because you're a tool for power users, very sophisticated coders that do sophisticated things with it. (t: 2570) But equally, you don't want to, you want to encourage them, not discourage your power users with pricing. Exactly, exactly. Yeah, we want to, we want to support the community. We want to hear how people use Quadcode in these ways (t: 2580) so that we can make sure that we can support that. That's super important to us. Great. So it's a price per token or you get like a certain (t: 2590) number of tokens for certain pricing tiers? Yeah, essentially, like within a certain, you know, a certain period of time, you have a certain number of tokens you can use. And then if you want pretty much unlimited usage without dealing with these limits, (t: 2600) you can always just use the API key. And this way you can just use it as much as you want. So I'd love to spend a little bit of time double clicking on use cases. So we talked about this a little bit and talked about how, (t: 2610) actually, some people at The French are using Cloud for non-coding use cases. But talking about the, you know, intended users (t: 2620) who are the coders, what are the main, you know, main features that you can use? And then things that you see people do in a context where Cloud presumably can do everything. Yeah, we see people using it for all sorts of stuff, (t: 2630) for all sorts of code related tasks. Everything from planning projects to managing tasks to actually writing code, testing code, debugging, (t: 2640) it's excellent at. So if something doesn't work, you can ask Cloud to debug it. Writing unit tests, verifying code. Whenever there's issues in production, (t: 2650) our first line of defense is to give it to Cloud. We have logs coming from GCP or whatever. We give this to Cloud and it'll figure out what is the issue that's happening. (t: 2660) And it can even interact with the Git and source control. So it can figure out what exactly caused a breakage or what exactly caused a regression. It can automatically fix it. So yeah, it's for every stage of SDLC. (t: 2670) And I think this is the first tool that really serves every stage in this way. And like I said before, we didn't intend it to be this way. (t: 2680) It just happens to be very general. And Cloud happens to be really good at using these tools. And so this is a, you know, for me, this has been kind of accidental product market fit (t: 2690) across the entire lifecycle of engineering work. (t: 2692) So yeah, the kind of accident that every founder or creator of a product dreams of that happens so rarely. (t: 2700) What an amazing story. Presumably, if you have those MD files and agentic search, (t: 2710) Cloud Code can also be used. If you're a new person, either at a company or on a project to learn about the code, first of all, is that right? (t: 2720) And two, do you see people do that? Absolutely. When I think about the things that Cloud Code is good at, I'll be kind of, I'll be a little bit self-critical. I feel like if you look at answering questions (t: 2730) about the code base and kind of code base research, I think it's like 10 out of 10 good. It's as good as it can get. When it comes to writing code, it's maybe like a six out of 10. (t: 2740) It's pretty good. It won't get everything perfect. The next model will be better. And it's something that we keep improving. When it comes to debugging code, it's maybe also like a six or seven out of 10. (t: 2750) So I think this code base research and onboarding onto a code base, it's really, really excellent at it. And at Anthropic, whenever new people join, (t: 2760) on their second day, this is part of technical onboarding. We teach them, here's Cloud Code. Here's the code base. If you have any questions, don't bug engineers on your team. (t: 2770) Just ask Cloud Code. And it can answer these questions probably better than they can because you can search around the code. It can look through history. It can look through pull requests and Slack and just pull in all the context to answer all of these questions. (t: 2780) And what we saw is at Anthropic, technical onboarding used to take a few weeks. But now engineers are usually productive within the first few days. (t: 2790) And they don't test their teams anymore. I think this is the biggest thing, where you don't need to bug your senior engineer or your manager anymore to get answers to your questions. (t: 2800) You just ask them. And they'll ask Cloud Code. And it can answer all these questions for you. So in the same vein, what do you find yourself using Cloud Code for as a leader and engineer? (t: 2810) What's your daily use case? Yeah, I use it all day for all sorts of stuff. So obviously, like I said, code-based research. (t: 2820) If I'm working on a piece of code I'm not familiar with, I'll just start by asking Cloud Code to tell me about it. Whenever I'm working on a small feature, I'll usually use Cloud Code in GitHub Actions. (t: 2830) So I'll just say, I'll make a new GitHub issue, and then I'll say, implement this feature for me. And it'll just do it usually in one shot. And sometimes I'll do this on the command line too. (t: 2840) So I'll just say, implement this feature and make a pull request. And I'll come back a few minutes later and it's done. Then there's this kind of other work (t: 2850) where it's a little bit more complex. You can't really do it in one shot. It's not as simple as changing a piece of text or changing a button or building a small feature. Maybe it's something more involved. (t: 2860) There's probably two workflows I have here. One is for really complex stuff. I'll prototype it a bunch. And this is something that I did even before Cloud Code. (t: 2870) When you write a complex piece of code or a complex feature, often engineers will write it a few times because you don't actually know the right way to do it. And so you'll try one approach. (t: 2880) You'll try a second approach. You'll try a third approach. And you'll kind of figure out the edge cases for each and kind of the limitations. And you'll get a feel for the problem this way. And this is something I used to do by hand. (t: 2890) Nowadays, I'll just tell Cloud Code to do it. So I'll maybe make a few Git work trees. I'll launch a few Clouds. And then in parallel, I'll tell them, here's your job. I want you to implement this feature. Go to town. (t: 2900) And I'll then look at each of the solutions and try to figure out, OK, maybe I like this piece of this one, this piece of this one, this piece of this one. And then I'll throw away all that code. (t: 2910) I'll just discard it. And then I'll start a new Cloud. And I'll tell it, OK, here's how I want you to do it now, that I got a feel for the problem. And then I think the last workflow (t: 2920) is actually the one I probably use the most. And this is probably for medium-sized features. And in this one, I'll ask Cloud to make a plan. And I'll go back and forth with it a little bit on that plan. (t: 2930) And it's really important, actually, to get that plan right and to make sure it's the same thing you have in mind before you continue. Because otherwise, what I see sometimes (t: 2940) is people ask Cloud for a little bit too much. Maybe it's a complex feature. And then they wrote it in some way that, like, it's not at all what you wanted. But the problem is that Cloud doesn't know how to do it. There's a lot of stuff that people don't know how to do. (t: 2950) And the problem is that the description is just too low bandwidth. You only described in a few words what you want. And so the idea that Cloud got of it is a very different idea than what you had in mind. (t: 2960) And so I find that planning helps a lot. So you kind of iterate on the plan the same way that you would with anyone else you're working with. And then once that's ready, I'll ask Cloud to write the plan to a file, maybe. And then I'll tell it to kind of implement that plan. (t: 2970) And it'll naturally make a to-do list for itself to implement it. All right, switching tacks a little bit, I'd love to talk about the space more broadly. So Cloud Code is a rocket ship (t: 2980) within the Anthropic rocket ship. But equally, there are other products like Cursor, Windsurf, Replit, Lovable, VZero. (t: 2990) The list goes on and on. And we've had a few of those CEOs on this podcast. (t: 3000) What do you make of the space going forward? Do you think we end up with a bunch of different solutions, different doings? Do you think there is a (t: 3010) at some point a winner takes all kind of scenario? How do you think about the next couple of years? Yeah, my feeling is there's kind of two dynamics here. (t: 3020) One is that this is just a giant market. The market, you could think of it as all of coding. You could think of it as all of kind of creativity and kind of creating things. (t: 3030) Because this extends at some point beyond coding to design and things like this. So I think there's room for everyone. It's a giant market. (t: 3040) And the biggest thing that these companies should be thinking about is everyone that's not yet using AI for coding. Because if you only focus on people that use AI for coding already, these are kind of the early adopters. (t: 3050) So you want to kind of get into that curve and get the middle and the late adopters too. And many of these don't even use AI today. So I think it's a big market. I personally use a lot of these products. And I use QuadCode every day, (t: 3060) but I also use Cursor every day and I use other products every day. So there's room for all of these and they all kind of fit together into people's work. The second way to think about it is, (t: 3070) like I said before, the model is getting better so quickly that the kinds of things it's able to do are just changing every few months. (t: 3080) It's this exponential that just keeps accelerating. And this is really the feeling inside of the lab, you know, working on AI and kind of building this stuff. And hopefully it's also the feeling that users have (t: 3090) as you get to use all these new products that are coming out every few weeks and maybe a few days at this point. So yeah, it's really exciting. (t: 3100) And I think my advice to companies building is definitely build for what the model will be able to do six months from now, not for what the model can do today. This is probably the single biggest advice. (t: 3110) And this is something that we followed for QuadCode also. We started building QuadCode when it was still Sonnet 3.5 and it was okay. And then with 3.6 and 3.7, it was fine. (t: 3120) It was pretty good. But then when Sonnet 4 and Opus 4 came out, that's when it really hit its stride and we felt like the product was really good. (t: 3130) And we started to be able to use it for a lot of coding. And so this is the biggest way I would think about it is, how do you build a product that captures the model capabilities six, maybe even 12 months from now? (t: 3140) And the market for those capabilities is just going to be huge. Yeah, and to double click on that, if I'm a product builder or founder, (t: 3150) how do I know that? How do I know what's in the, you know, six months horizon? You know, speaking of like entropic or in general, (t: 3160) what is it that is going to happen in the next six months that I should plan on talking about the coding capabilities? (t: 3170) Yeah, I think the biggest thing is just use all these products and see where they stumble. And try to get a feel for the model itself. I think QuadCode is a really good way to do that. There's probably other ways too. (t: 3180) But try to kind of get away from all the scaffolding and all the products people have built around it and just get a feel for the model's capabilities. And as raw form as you can. And try to get your head around the limitations. Like where exactly does the model stumble today? (t: 3190) What is exactly that frontier where it's like not very good and then, you know, sometimes it's good and maybe 50% of the time it's good. (t: 3200) You can kind of get a feel for this frontier. And with the models today, a lot of this is around kind of agentic work where it can use tools really well and then at some point maybe it'll fall over (t: 3210) when there's too much context or too many tools or the trajectory is too long. Maybe if you've been running Quad for two hours, it loses track after a little bit. So there's some sort of like frontier here. (t: 3220) Maybe there's another frontier around code quality where today I have to maybe correct the model when, you know, maybe there's something that it does that isn't exactly the way I would have written it. (t: 3230) And I think over time models will get better at kind of understanding this too. So I would just try to use the model in as raw a form as you can and get a feel for these frontiers and the domain you care about. (t: 3240) So for coding, maybe it's kind of the how long the trajectory is. And whether the model can stay on track and then the quality of the code and probably a bunch of other stuff. And then for non-coding domains, there's a lot more. (t: 3250) So still in the same vein of like the AI coding wars, it seems that there is this, you know, competition, co-petition kind of dynamic (t: 3260) where Anthropic or others would provide their models to companies like Cursor or Windsurf at some point. (t: 3270) But equally build Cloud Code, which is an application. Do you think that's a long-term sort of way the ecosystem works? (t: 3280) Or, you know, in a context where Cursor is saying that they're going to build their own models as well, (t: 3290) the end result is kind of like full-stack players where everybody has their own underlying model and application on top? I think there's probably room for both. (t: 3300) And my personal take is probably there's maybe more room for more stuff built on top of the platform than there will be built in-house. Just because there's so many things to build and there's just not enough time and enough people (t: 3310) and energy to build all those things. So I think a lot of the innovation is going to happen on top of the APIs and SDKs that are built. As a last theme for this conversation, (t: 3320) sort of the elephant in the room is what that means for coding and coding as a profession. (t: 3330) What's your general sense for what coding is going to look like in a few years from now? Yeah, it's a little bit hard to say. You know, a few years from now is, you know, (t: 3340) in AI time is like decades, you know, in normal time. I think even today for a lot of professional coders, it's really easy to lament the state of coding and to think, (t: 3350) you know, I used to write this code by hand and, you know, now it's this agent doing all of it. I think actually being the one that does this work, it's incredibly exciting to have an agent (t: 3360) write the code. It feels very empowering as an engineer because I can explore a lot more ideas than I could before. I can do it much faster. I can work in domains that I know literally nothing about. (t: 3370) You know, maybe I don't know iOS, but I can write an app because I can just generally code review the code and I can see it looks reasonable, but Quad actually does all the writing (t: 3380) and all the testing of it. There's one engineer on the team, Lina. She still writes C++ on the weekends, sometimes by hand. She was telling me because, you know, like as a programmer, (t: 3390) it's kind of one of the things that we enjoy. Because sometimes you have to get down to the metal and you have to kind of do it this way. But I see this as a transition the same way that in the 60s, (t: 3400) there was this transition between punch cards and assembly and then later on between assembly and Fortran and COBOL and the first high-level languages. (t: 3410) And I think this is just another next transition. It's hard to know exactly how this is going to play out. I think one way it will definitely play out is it's going to change programming, (t: 3420) where programming is no longer direct text manipulation, but it's more working with agents to get the work done. And I think it's going to be hugely empowering (t: 3430) where a lot of people that couldn't create before can now create. Even if maybe you don't know anything about apps, you can use Levelable or you can use another platform (t: 3440) to build cool stuff that you couldn't before. And this is just hugely exciting. But if I'm a young developer today or I want to make coding or building, (t: 3450) applications my career, what would you say? What would you tell a younger version of Boris at the beginning of your career for a future in the field? (t: 3460) What do I need to learn? I think for people that are learning coding today, it's actually harder than it was when I was learning coding because not only do you have to know coding, (t: 3470) because you still have to understand the languages, you still have to understand the frameworks, you still have to understand system design and all this stuff, but also you have to use all these tools. (t: 3480) And you have to do both. You have to hold both in your head at once. So you have to code so that you can check what the model does and you know how to direct it because you have to have, you know, still with the models of today, (t: 3490) you have to know what you're doing in order to direct coding agents effectively. But at the same time, you have to be using all this new technology. You have to be using quad code and you have to be using all these new agent-encoding tools (t: 3500) because this is what the future is. And I think it's hugely important to understand what these are and what it lets you do and to learn how to function. Both when writing code manually (t: 3510) and when using these tools. All right. It's been a fantastic conversation. Maybe to close, give us a sense for what the next few weeks or months (t: 3520) or year looks like for quad code. Anything on the roadmap, anything you're excited about, anything you can talk about? Yeah, there's a lot of stuff we're working on. We landed native Windows support recently (t: 3530) and we're working on single file distribution so you don't need Node.js anymore. You can just use quad code and it's a single binary that you can use. You can use anywhere. (t: 3540) So much more portable. We're working on getting quad code into more places. So wherever you are, you can use quad code more easily the same way that you can on GitHub today (t: 3550) and expect a lot more agents. So be able to launch agents, agents managing agents and a lot more kind of freedom this way and continuing to level up the state of the art (t: 3560) and figure out what's next. But I would say overall, we don't really know. Still, we're testing stuff out (t: 3570) and we have a lot of ideas and we don't know what's going to work, but we're excited to show what we come up with and see if people like it. That feels like a wonderful place (t: 3580) to leave the conversation and very fitting as we all try to figure out where, not only where we take AI, but where AI takes us collectively. So it's been wonderful. (t: 3590) Thank you so much, Boris. Really appreciate your spending some time with us today. Yeah, thank you, Matt. Hi, it's Matt Turk again. Thanks for listening to this episode of the MAD Podcast. If you enjoyed it, (t: 3600) we'd be very grateful if you would consider subscribing if you haven't already or leaving a positive review or comment on whichever platform you're watching this or listening to this episode from. This really helps us build a podcast (t: 3610) and get great guests. Thanks and see you at the next episode.

