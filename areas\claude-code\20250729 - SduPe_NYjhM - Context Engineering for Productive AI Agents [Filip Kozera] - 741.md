---
title: Context Engineering for Productive AI Agents [<PERSON><PERSON>] - 741
artist: The TWIML AI Podcast with <PERSON>
date: 2025-07-29
url: https://www.youtube.com/watch?v=SduPe_NYjhM
---

(t: 0) I think a lot of people think that more powerful agent means more autonomous agent. I actually think that's false. What you end up needing to do is incorporate human feedback even inside of these reflection loops. (t: 10) So basically the job becomes how to make sure that the agent knows what it doesn't know and bringing the human at the right time into these reflection loops. (t: 20) So when we think about how the future of work will look, it's exactly that. It's the data that the agent cannot find. (t: 30) The taste or creativity that it cannot come up with on its own surface to human as work. (t: 50) All right, everyone. Welcome to another episode of the TwiML AI podcast. I am your host, <PERSON>. Today, I'm joined by... <PERSON>. <PERSON> is founder and CEO at WordWare. (t: 60) Before we get going, be sure to take a moment to hit that subscribe button wherever you're listening to today's show. <PERSON>, welcome to the podcast. (t: 70) Hey, Sam. Pleasure to be here. I'm excited to have you on the show, and I'm looking forward to digging into a really interesting conversation, both about kind of what you are building at WordWare (t: 80) and the way you have approached the problem of building AI agents for users, but also we've talked about a bunch of interesting kind of topics around context engineering, (t: 90) the future of work, the fight happening at the application layer. All of these things will be interesting to dig into together. (t: 100) But let's get started by having you share a little bit about your background and introduce yourself to our audience. Sure. (t: 110) So very long story short, I actually was pretty lucky with my choice of research. I did research into... essentially LSTMs back in 2016, (t: 120) which were the precursors to the Transformers architecture. In 2018, I started my first company trying to augment human memory (t: 130) with always-on listening devices based on GPT-2 and BERT. And I must say, I was a little bit before my time. It felt like banging my head against the wall sometimes with GPT-2, you know. (t: 140) Sure, I didn't promise, but not really being there. Again, long story short, ended up exiting that company, took a year off. (t: 150) We don't talk about these moments enough in Silicon Valley. It's always seems to be grind, grind, grind. But I sailed the Atlantic, I climbed a couple of peaks in Nepal, and I was back at it. (t: 160) And this time around, we essentially approached WordWare as the new software (t: 170) where the words are actually the code. Hence, kind of the new take on the natural language programming. In the beginning, we were... We were a much more developer-focused platform. (t: 180) We found some faults in our hypotheses. And right now, we're building... WordWare is essentially the companion that helps you build other background agents. (t: 190) And we can get into, you know, what are background agents in a second. That's super interesting. And right now, there's a big risk that this suddenly becomes a sailing podcast (t: 200) if we dig into that topic, but I'm going to resist that. And talk a little bit more about the agentic side of things. (t: 210) You know, when I looked at what you guys are doing, it touched on some themes that I found super interesting. I've built a bunch of agentic workflows with tools like N8n and Zapier and Make and the like. (t: 220) And your proposition to users is that you allow them to build these kinds of workflows or agents (t: 230) with... Natural language as opposed to dragging boxes around and doing a lot of pointy clicky, (t: 240) which sounds really interesting. Talk a little bit more about the philosophy that led you down that path. (t: 250) It sounds like there was maybe a pivot from a developer-oriented approach to something that's more user-focused or end-user focused. (t: 260) Yeah. So I think the core of all of this is that the thing that matters the most right now is the natural language. (t: 270) It is essentially the assembly code of LLMs. And putting that abstraction front and center is very important. (t: 280) And, you know, at the beginning, we were much more focused on kind of developers and then very technical users where essentially you are sending particular snippets of text to different LLMs and (t: 290) kind of really embracing the chain of thought technique. Yeah. So that helps to press the through button of what you think is an181 (t: 300) by enabling you to also call different models. Right now, we've changed it a little bit and I can go into reasons. I think we took quite a lot of inspiration from where the software kind of vibe-coding market is. (t: 310) I can get into like, why is that important? (t: 320) But we essentially want to simplify the entry point to work where the engine stays the same, but we kind of want to make sure that human beings, as well as they whereas human beings with different materials outside of the app, and worlds of different kinds of networks and data are designed to work withfs antid wilderness, for instance. So in terms of Europeans, Humans know how to express the idea and the assignment for the agent. (t: 330) And as long as they can write it in a document format, it can be executed. (t: 340) And the questions there is that obviously at the beginning, the latency and the cost of it is much higher. Because you're essentially using a React agent behind all of it, trying to make sense of it and not make explicit tool calls. (t: 350) But rather have the agent decide on what to call, what code to write, etc. (t: 360) Help me kind of get a mental model for the next level of detail. The user describes their problem and what they're trying to do. (t: 370) And is the next step kind of a compilation step in a sense of you're taking what they're trying to do, you're parsing it into what agents might need to be involved, what tools might need to be involved? (t: 380) Right. Right. Right. Right. Right. Right. Right. Right. Right. Right. Right. Right. Right. (t: 385) Right. Right. Right. Right. It's a reflection loop. (t: 390) And so when we're parsing, we call it a dossier, which includes all the context needed to execute the leopard. (t: 400) Right. And I also mentioned about implantation route, hummingbird route, which has less gaps. Right. When we were distributing anything manually, we'reuseum container supports thatavanham. block or something in the use case. (t: 410) an assignment, you pass different data into it. You ideally truncate it to be manageable inside of the context window, which we know, you (t: 420) know, all the time we hit the limitation of the 200,000 tokens on Opus or a million on (t: 430) Gemini, et cetera. And the assignment is the function, the resources, the context, let's say all of your data from (t: 440) Slack is the X in the FX, you know, and the assignment can be kind of, you know, taken as an algorithm, which, you know, uses LLMs, obviously, very, very heavily. (t: 450) And once it starts on the assignment, it uses a React agent. (t: 460) That's a very famous paper I recommend reading and basically calls different tools in their reflection loop. And the agent itself is actually deciding on what tools to use out of its whole repository, (t: 470) which ideally you are kind of passing in the dossier. So the execution agent doesn't get too, doesn't get too confused. (t: 480) We have seen like a big, big drop in performance when presenting like even Opus 4 with more (t: 490) than 15 tools. Are you pre-processing that dossier to explicitly identify tools or are you allowing the agent to do that? The agent will have to choose from, you know, a set of tools, but we definitely limit the (t: 500) number. You know, we have right now in our repository around like 3,000 different tools. (t: 510) And based on the assignments and the resources, you kind of want to limit that. There is like two different approaches. (t: 520) You could have a architecture where there is a continuous agent, which depending on the execution of a particular tool, you can limit the number of tools. And you can limit the number of tools. And you can limit the number of tools. So if you have a particular loop iteration is actually choosing the right tools for (t: 530) that iteration. Or if you are very sure about how to execute this particular assignment, you can just kind (t: 540) of hard code it into the repository that the execution agent can call. Talk a little bit about how MCPs come into play in your architecture. (t: 550) First of all, MCPs are just tools. The only difference between them is that we agreed that. Let's say you're running. A team and each person, the team has some particular function. (t: 560) Okay. And they know how to do one thing. And here we are kind of assuming everyone is a single task kind of persona. (t: 570) And all the MCPs, the innovation of MCPs is that we standardize a sticky note that you put on the forehead of that particular tool. (t: 580) So when you come into the room, you don't have to know them, know them, but you kind of know if you are getting a task from your. Client, you as the CEO of all of these tools know to whom to give that task. (t: 590) And, you know, we've kind of discovered quite a lot of limitations to do with MCPs. (t: 600) Um, that sticky note lacks things. So it lacks a couple of different, um, in the end, it's still a description. It's just, it's, we should force people to describe it a little bit more. (t: 610) So things that we are. I think you know, it is only as good as the description that people are giving to the underlying tools and what they can. Do. Right. So ideally, I would like to bring a world where we have, uh, even a stricter format of that sticky note. (t: 620) And some of the things that could be added is, you know, um, required context. (t: 630) Uh, because if you just end up calling the tool and not giving it the right context to, um, uh, kind of move forward with that task, that's useless. (t: 640) Uh, it should have a, uh, feedback, uh, in some way. So if you already called that tool and that tool. Returned an error, you kind of want to call it again from the main agent and give it feedback. (t: 650) Authority is an important one. So basically there should be some kind of way when we are taking action in people's lives, we should know whether that authority has been given, uh, to that particular tool. (t: 660) And if, if not, then what's missing. (t: 670) Um, and there's a couple more, I think those four kind of describe it well enough. Uh, but we basically always throw a JSON. Into that description that compiles all of these things. (t: 680) And most of our tools are actually agentic. So they run some kind of fellow land on that, uh, on, you know, before they actually process and just use an API, right. (t: 690) It's the, in the end, it's a API route for natural natural language. When you say you throw a JSON into the mix there, are you saying that you are adding all of those things beyond what is available via the MCP itself? (t: 700) In other words, you're onboarding new. Yeah. APIs or MCPs and you're taking on the burden of, you know, filling out that metadata where it might be deficient. (t: 710) Yes. So essentially, uh, it's still, we're still using exactly, you know, the, the right, um, the, the same place in MCP as the description, which is populating that description with a, with a lot more things. (t: 730) So, you know, the context, the task, the, uh, the authority. Yeah. Yeah. As long as you then do the right things with it inside of the tool, then it's, you know, it's all good. (t: 740) So, you know, that's a little bit of a problem with kind of tools that are not managed by you. (t: 750) And for us, you know, a good tool is essentially still has an API somewhere in there, which just learns how to parse everything into the API. (t: 760) Uh, but now with, or it has an actual first party MCP inside. Yeah. But you can see inside of it. So then it basically, you lose a little bit of, uh, you lose out on latency, but you're a little bit more sure that what's happening inside of that tool actually makes sense and it is what you want to do. (t: 780) So tools can be agentic, tools can be hey, walk out my dog, which then, you know, calls, goes to Rover website and does browser, uh, like browser use there and so on. (t: 790) That can be a tool as well. And there is like a, you know, there is a little bit of a spec. between what is a full powerful agent and what is a basic tool, which is deterministic. (t: 800) And so when you hear folks talking about these agent, agentic protocols like A to A or ACP, (t: 810) is that, how do you think about that in the context of what you've learned about, you know, using tools and the agentic, the more agentic tools? (t: 820) All of this is a schema of how to communicate between tools, or if you want them to be agentic, you can call them agents. (t: 830) Or software systems. Exactly. So regardless of, you know, the exact schema, (t: 840) I think what you'll end up doing in your own architecture is to use something that like works just for you. So we don't actually utilize the A to A. (t: 850) We, you know, we utilize. We have some MCPs, but. But providing the additional context kind of closes the gap between MCP and A to A. (t: 860) Yeah. So, so, so that's like, I don't know the ins and outs of A to A. I just know that like, basically you need to find yourself on the right side of the wave. (t: 870) So it seems right now that an MCP is a wave, which essentially means that people are putting, you know, (t: 880) for, for the last 20 years, we've been putting a GUI on top of databases and that's called essentially a UX for humans. (t: 890) And what we are trying to do right now is we're trying to define what is the best UX for agents to interact with that particular database, (t: 900) which, you know, both SAS is a database with some manipulation and a graphical interface. And it's an interesting problem. (t: 910) I think it, again, it's a problem. I think the game is, is, is far from being over is just people are catching on that. If you are linear, you need to actually put out a way for agents to interact with all of that data that you've, (t: 920) you know, beautifully presented to humans, but have not found a way to present to AI. (t: 930) So I'm really wanting to ask you a direct question and that is, does it work? And the context for that question is in my experience, (t: 940) building out agents, with like, again, an eight N for example, um, you know, I get, I often will go through this experience where, (t: 950) you know, I'll build this fully agentic thing and leave a lot to the LLM and, you know, it kind of works kind of doesn't work. (t: 960) Uh, so I'll pull more and more out of the LLM and do more pre prep processing and apply, you know, my own knowledge about the problem to kind of structure it more and more for the LLM. (t: 970) Uh, what you're telling me is you've got this one system, you feed it a prompt and it's going to, uh, just work without that kind of explicit, (t: 980) you know, business knowledge. And, uh, I don't question that it, you know, will work at some point, you know, given a certain level of capability of LLMs. (t: 990) Uh, but I want to, you know, that's a very valid question. Yep. Um, I think, um, (t: 1000) graceful recovery, it's extremely important in these systems. And in that way, I think a lot of people think that more powerful agent mere mean more autonomous agent. (t: 1010) I actually think that's false. And what you end up needing to do is incorporate human feedback, even inside of these, (t: 1020) uh, you know, reflection loops. And if the agent, so basically the job becomes how to make sure that the agent knows what it doesn't know. (t: 1030) Uh, and, and bringing the human at the right time into these, uh, into these reflection loops. So when we think about, you know, how the future of work will look like, (t: 1040) it's exactly that it's the data that the agent cannot find the, uh, taste or creativity that it cannot, uh, you know, (t: 1050) come up with on its own surface to human as work. And, you know, that's on the that's on the reflection part of things. So how to figure out what tool to use, (t: 1060) and when. But there is also a part where it just lacks the right tools. It doesn't have the authentication or it doesn't have many things. So the first question for the human in the loop (t: 1070) could be, do you have an API key for this? Can you authenticate via web? But actually, what we found out is that it's good to have a graceful ability to fail in that aspect as well. (t: 1080) And you almost need to manage a to-do list for the human. So everything where the agent cannot (t: 1090) figure the shit out, it will be like, hey, I added it to your to-do. Good luck. (t: 1100) Got it. And is that happening? Does WordWare connect to my Slack and the agent is pinging me, asking me for things? Or is it some agentic inbox on your system? (t: 1110) Yeah. So we're currently in a closed beta. We're the kind of AIOS product. And we basically have a macOS app, a web app. We're basically an (t: 1120) agent, which is a companion, which is serving as that kind of manager and the last line of defense (t: 1130) between you and the fucked out of agents that are trying to get some shit done. And there's an agent, there's that companion, which kind of protects you. But the rest of it is actually like there (t: 1140) is a to-do. There is... There's a bunch of human in the loop tasks, which is slightly different. We haven't really figured (t: 1150) out the glossaries for all of these. But a human in the loop is kind of an intervention by an agent, which stops for approval, rejection, modifying of the modification of logical steps where it got (t: 1160) itself in some kind of dead end, or just kind of editing the final output. So those are the kind of (t: 1170) things an agent can ask you to do. And there is also things where the agent just gets up and says, (t: 1180) Hey, I cannot do that. And then you add it to your own to do. So this is kind of reflecting on how (t: 1190) I see humans working in, like, 2028 now. It's a lot about getting outside of this conc Όam, of chat interfaces, (t: 1200) Remove saluting your own Layman from the chat You're using chat interfaces to do your own Нав armastron Maybe you have two agents doing something for you You're mostly monitoring it You're in the loop close with you for the rest of your show, And you know, I'm probably six value for this day ahead, you're essentially micromanaging your agents. You know, one way to kind of look at this is look at what's happening in (t: 1210) agentic coding tools and like project that into general work. And so, you know, we've seen the rise of, you know, these agentic swarm coding tools and like (t: 1220) GrokHeavy is maybe an example of this as well, where you start a task and this task spins up a bunch of sub-agents that run off and, you know, do your coding task or, you know, your research (t: 1230) task. And your job becomes, you know, the agent becomes less of an augment, you know, (t: 1240) a way to augment you as a developer, but a way to augment you as a developer manager kind of thing. And I've actually, yeah, there's a couple of very important ideas here. One, it's just a (t: 1250) companion. So you still have to take responsibility of it for its work, which works better than like a top to bottom push from the manager, from the management. (t: 1260) Of using some particular tool, because then nobody ends up taking responsibility for its outputs. And that's one part of our thing that like we are better than AIs. We have a, we are (t: 1270) a legal entity that has responsibilities, can get fired and we give a shit. And so when I look at (t: 1280) the current, I'm Polish originally. And when I come back to Poland, I see this (t: 1290) human pride in implementation of very particular technical solutions. And these people often say, AI doesn't work. It doesn't do the thing. (t: 1300) And when I look at the best engineers right now at my company, I think about the people who (t: 1310) have had experience managing interns, have taste and are extremely good technically, but know which parts to delegate. And they work with tools like, (t: 1320) Codex, Devon, and like with interns, a lot of the work, you just need to throw it out. You cannot make it like get to the code base. And in the particular parts where you as an engineer (t: 1330) understand that there is not much training data for that particular problem, then you end up (t: 1340) solving it yourself. And this is very important to kind of understand for which problem there is a lot of training data and kind of develop that intuition. (t: 1350) To be like, hey, actually, if I would have scraped all of Stack Overflow, there was probably 400 instances of this question being asked. That's perfect. (t: 1360) And this actually also influenced our architectural decisions where something could be slightly suboptimal in the kind of implementation. But we know that there is a lot of training data about (t: 1370) this particular implementation, which ends up allowing us to manage our code base with AI much (t: 1380) better. So it is optimal. It's an interesting way of like decomposing a problem in that you're explicitly thinking about what's likely to be, you know, within the training data, which I guess is another way of explicitly (t: 1390) thinking about what the LLM is going to be good at and not, which is kind of table stakes. But thinking about it from a training data perspective maybe helps you do that. (t: 1400) And it's crazy because like we're basically choosing like our suboptimal, you know, (t: 1410) like our... architectural decisions based on the employees we have available, right? You're not going to write your stack in Java if you only have like, you know, Python developers, (t: 1420) you know? It's like you end up having to choose. And yeah, it's very interesting to me that we are already making, like we're already changing our (t: 1430) lives to let AI help us easier. Along that specific point, I've thought a lot about how AI changes the (t: 1440) future of like developing new programming paradigms and frameworks and the like. (t: 1450) And the example that comes to mind and illustrates this for me is. Agents are really good at like creating React applications, like that's the canonical things kicked (t: 1460) out the VPC's platforms to make a spot in attributes, SQL enfants, three of these mental Google語 or other things that I might want to do that are somewhat less worth represented in the (t: 1470) training data. or, you know, God forbid. forbid some new thing that I want to get out there and, you know, promote for developers to adopt. Like, how do I get them to do that? Well, I've got to make it really easy for agents to (t: 1480) kind of take all the, you know, documentation, for example, you know, becomes really key, but not necessarily for the humans, but for the AIs that will need to consume it (t: 1490) so that they can help people write code with it. And it's very interesting of like, when are you again against the wave versus kind of being able to (t: 1500) stay on the wave? And I think, you know, V1 of this company has been really focused on developers, which then created agents, which were somewhat deterministic and then exposed it as an API and (t: 1510) plugged it into their code. And, you know, we got very quickly to some decent revenue. And this (t: 1520) only speaks to how crazy the AI market is because, you know, we've raised $30 million based on that biggest round out of YC. (t: 1530) And then soon after we realized that we're going against vibe coding. A year ago, vibe coding wasn't really that big of a thing, you know? And it's just such a crazy industry. Like, I don't know if you've been following what's (t: 1540) happening with Windsurf, but my God, it's just shit is like, I think my main lesson there is like, (t: 1550) don't get burned. If you want to invest in real time development like NO artificial intelligence that way, you have a better chance of managing work as, let's say, a very sophisticated open source (t: 1560) device. Well, I'll let northeast, t Isabella, grab a collaboration from me, like you practice teaches how to run a service the best way. (t: 1570) Not the most natural way to it. But click on the program want to gain more This is another very similar question to the (t: 1580) other, you know, kind of V1 and you know, in, Manos had 20 million of ARR and they pivoted away. (t: 1590) And it's just crazy how just the bar has gotten so high. There are a lot of lessons and takeaways in the whole Windsurf thing. (t: 1600) And that's probably a podcast on its own. But, you know, there was another thing in the news about Salesforce and Slack blocking API access for Gleam. (t: 1610) Which I think has a lot of implications for a company like yours that depends on getting access to your customers' data in these silos that that data lives in. (t: 1630) This has come up for me in discussions as well with just agents that scrape the web. And we've already seen companies starting to put up barriers to prevent agents. I think Cloudflare came out with an offering that, you know, (t: 1640) is going to allow publishers or site owners to charge agents to come and scrape their content. (t: 1650) You know, how do you think about that as someone whose company is about, you know, in some way kind of, you know, (t: 1660) liberating access to this data that's in these silos or at least allowing, providing another way for organizations to leverage information in silos? (t: 1670) So it's interesting. The it's this is a much more difficult. Topic than in software engineering, because in software engineering, you own your whole code base. (t: 1680) And, you know, we are trying to automate the menial, the knowledge worker tasks and make sure that humans spend much more time on the creative tasks. (t: 1690) And, you know, essentially automating all of these tasks requires the access to all of that data. If you don't have like access to, you know, your Google Sheets and your Notion, you cannot respond or draft a response. (t: 1700) Right. Right. Creative web requirements are in a lot of these systems. So how do we utilize it? (t: 1710) I think in the early, early 20s, we just started creating, um, apps, guy like when I joined Overhauls Aunque title, that is some new things around there, um, well, I work in marketing systems, remove tracking Серge, shameless, um, but I've done, uh, a lot of those products, but again, just just applying only a few things as I went (t: 1720) forward, um, so I, for some reason here, um, I only usedалогibrabied as I wanted, but it's because, um, I think windows just started being a tool for around 1932 that, um, (t: 1730) when Google stepped up, all the ones that wanted that, it's cool, you can find a box with, I don't know, something really great that, they're looking for Yahoo next fueracstage business selling, uh, you know, holding a particular flash, how is it called, not flashpoint, (t: 1740) like holding all of your Slack data. And this essentially means companies like Glean cannot really kind of, you know, (t: 1750) answer precisely about these things. And it might be that everyone becomes really greedy and everyone who has that data (t: 1760) will try to become their own AI agent. So you'll have an AI agent with Notion, with Slack, and they will charge you for each call. (t: 1770) I think that would be suboptimal. And it would mean that you need kind of like a personal assistant to chat with all of these agents. (t: 1780) And there's a completely different world where, you know, there's so much benefit from chatting with your index data on Notion via Cloud that Notion just cannot close it. (t: 1790) And then if we would be in Europe, if ChatGPT and Cloud have access to the Notion, if there's a lot of data to it, the small startups would as well. I have no clue how it would pan out in America where, you know, there's not such strong anti-competition, (t: 1800) sorry, such strong like proactivity into kind of making sure that competition is healthy. (t: 1810) So those are the two ways. I see different companies who are actually, and in the end of the day, trying to build very powerful AI agents to try to own one particular channel. (t: 1820) So, a great example of this is Granola, who, you know, like I can see what they're doing. Like I can see that they want to become an, (t: 1830) you know, universal AI agent for everything that gets said. And, you know, they basically approach it and, you know, I know the founder, (t: 1840) but I have no data from him at all. It's just my guesses. That, for example, they recently raised a round, which, you know, like it wasn't like great, (t: 1850) but I think they might just be losing so much money on transcription. And just being okay with it because they are getting like, you know, (t: 1860) so much data and they hold it. Like I asked them to give me like access to transcript to the stuff that I say, (t: 1870) because like I'm not, like I end up processing my transcripts in a different way. And I just, I just can't get it. And I'm like, oh shit. (t: 1880) So you guys are like, you guys are hiding this from me. And it's an interesting, like, you know, way in, you know, how will this look like? Yeah. How this look like when we, (t: 1890) when we have the IO Johnny Ive stuff that like that, uh, open the eyes paying 6.5 billion for, and it just listens to everything. (t: 1900) And it's an additional hardware that's where I don't know. This brings up a bunch of different things for me, I think. And then it kind of goes back to a point you made earlier, (t: 1910) which is a lot of the apps we use, are a UX on a database and the value, and maybe that's an oversimplification. Maybe it's a UX on some business logic on a database. (t: 1920) Um, but you know, for things like CRM and things like ERP systems and many of the tools, particularly an enterprise, (t: 1930) like the business logic is relatively thin. Uh, and the value is in, you know, the data that, you know, these, the users have put into this database. (t: 1940) And if this, this front end, the front door to that is shifting from a UX to something else to an agent, uh, (t: 1950) you know, you know, Salesforce and, you know, others who have all this data are, you know, they don't want to necessarily see that front door experience to Claude, (t: 1960) right. By like publishing an MCP and letting users bypass, you know, that Salesforce experience. And certainly, um, you know, they don't want to lose, (t: 1970) lose the value of that data by giving it away, uh, easily. Uh, Salesforce in particular has always frustrated me in this way. I remember as a, (t: 1980) you know, as a small Salesforce user, it's like, you know, you could be paying $50, $60 a user per month, but to get access to the API, you needed to spend another $10,000 a year. (t: 1990) It's like, it's my data. Like what the hell? Um, and so it's very interesting. Yeah. Where will regulations step in? (t: 2000) Uh, you know, based on GDPR, CCPA, you can just request all of your data and they are obliged to give it to you. But that would mean that, (t: 2010) you know, as a startup, you might have to ask about it. Like we will ask on behalf of you every 24 hours. Uh, (t: 2020) you know, like, and also like if they end up charging for it, you know, that's, that's like, and they might try to block it in some way unless like, (t: 2030) well, GDPR is pretty, like, airtight. Like it's pretty good. You own your data. I'm not so sure about CCPA. It doesn't specify that they give it to you in a way that's useful or easy, (t: 2040) right? Yeah. Yeah. But then they are steps in and, uh, Hey, like the, uh, the, the user experience that needs to be beautiful for humans can be just as like (t: 2050) freaking SQL database and you'll have all the information very easily accessible. One thing that AI is great is writing SQL queries. (t: 2060) So, uh, you know, then, yeah, so that's the magical part, right? The whole UX ends up being a prompt with an ability to write code being in this case, (t: 2070) SQL and the data. Suddenly that's out cold is like almost like an agent experience, right? And how quick that is, (t: 2080) how, uh, you know, you basically don't need any of the blocks. So the data problem is very high on my mind and basically how to make sure there's like startups right now, (t: 2090) which, now I'm trying to get to open applications, I think there's more time, uh. All right, so는데요 is essentially basically like how you create an application that's big enough to actually build your Datacore stuff. Okay, cool. (t: 2100) I love it. And, I also love like your biggest connection便 Isom andere h (t: 2120) Like they have added every feature under the sun to this thing, like whiteboards and this and that. And, you know, like they probably do have a Slack like thing. (t: 2130) And when I think about that with an old world lens, I think that like, you know, their core (t: 2140) value proposition is like highly commoditized and they need to throw more and more stuff in to justify their license. But there's another lens that you're kind of speaking to here, which is, you know, they (t: 2150) need to or they may end up being the beneficiary of just having all the data. (t: 2160) Like the data is what is key and important in this world that we're moving into. And the dynamics are slightly different. Yeah. Likewise, when I think about a business like yours, like in the old world, you know, if (t: 2170) I look at a Zapier, I think probably the most. Complex thing in that business is managing all these connectors and, you know, building (t: 2180) relationships with these partners and getting them over the hurdle of like, you know, one connector at a time to build out this catalog. (t: 2190) Whereas, you know, it's kind of, I suspect it's slightly different world for you. Like it's still like, there's still an effort that's proportional to the number of connectors, (t: 2200) but like you got MCPs, even where there are no MCPs, you can probably use an AI to slurp in an API and like. Make it accessible to your system. (t: 2210) Uh, but then you still, you know, in this world, this one of the parallel universes that you project, you're like, oh, event thing is like building relationships with these companies (t: 2220) where you're asking for their data dumps every day, which doesn't sound easy either. Yeah. And that would not be pleasant, especially that you wouldn't be building a relationship. (t: 2230) You would just have a lawyer on staff, which, which is chasing everyone who is not like, you know, adhering to you. Owning. Your actual data. Uh, so it's, it's the opposite of building a relationship. (t: 2240) It's building a litigation. Uh, uh, I completely agree. I, I, you know, I think this will essentially, which parts of your resources in that dossier (t: 2250) that we've mentioned before ends up being, uh, something that you brought a UX for the (t: 2260) users to add data into, um, is a question mark, you know, you can think about like granola in the end of the day is, you know, it's, it's a folder of, of, of transcripts on which (t: 2270) there is one prompt written and the main, I think they were very, very lucky that they hit the right timing after COVID where yeah, that like essentially crisp used to be something (t: 2280) that reduces the noise before the, all the big platforms had it. (t: 2290) And they basically created a, um, a, uh, virtual microphone and virtual speaker that, uh, wrote, uh, virtual speakers. Yeah. Uh, wrote like basically send data to both your headphones and their system. (t: 2300) So they basically built granola for granola as a thing, uh, just for the, because of a coincidence that they already built this way of capturing it without having to add a, uh, bot into your meeting, which I think is the main innovation of granola is that you don't have to have this awkward third thing. (t: 2310) And I don't know if you're running down granola. (t: 2320) I, I, uh, randomly right, right now. And I don't even know about this. Uh, and. Uh, other people have tried it. They just nailed time. (t: 2330) And, you know, the product is, it's not that complex, but it's just their way to market with it is genius. And then, uh, you know, how they're going to progress and grow on that top of that data. (t: 2340) That's, that's a, that's a real question mark. But if you could imagine that you have a folder somewhere on your computer with all of the transcripts, you could imagine that you just find ways to add to it. (t: 2350) It doesn't matter if it's, you know, I'm exploring all different potential things. It's not like, I can't, uh, end up, you know, compiling, like computing your data. (t: 2360) This is a, quite a sophisticated microphone, which just gets stuck into my, uh, into my, uh, iPhone. (t: 2370) I just connected like this and this is a sophisticated microphone capturing stuff for me. And then later on, I can just kind of, you know, run it through transcription, which is not really, um, which is a commodity. (t: 2380) Right. Interesting. Interesting. I think it speaks to. Uh, this context engineering and the importance of gathering and having access to, uh, this context. (t: 2390) Um, you know, because that's what, you know, without that, like the LLM isn't all that useful. (t: 2400) Like you've got your prompt, you can tell it what you want it to do, but, uh, you're telling it how you want it to operate on some set of, you know, data that you, you know, own or control or has access to, or that it can find out on the world itself. (t: 2410) But, um, I think, you know, certainly, you know, your preexisting thoughts about the thing through your calls and transcripts is going to be, you know, more interesting than, uh, you know, what it finds out, you know, the, the way for you in this. (t: 2430) Just one last point. Um, for you wrap up this, this, this, uh, topic. Um, it's an interesting approach to say for now, all of the UXs are actually that the GUIs are actually the best way to get that data. (t: 2440) And, um, for example, Manas recently, uh, built in a persistent authentication into, so they basically hold a little instance of a logged in version of whatever you have going on. (t: 2450) Uh, and they keep it live. So they don't have to re-log you in into everything. (t: 2460) And, you know, for example, for me, that means like I could, you know, we, I actually run, have a background agent which scrapes a bunch of websites. (t: 2470) We don't have to do that. But I actually have a website which I pay for access to about kite surfing conditions. And, uh, I, I get a notification, uh, through our system actually, which tells me today's a great day. (t: 2480) Take a 11 meter kite, not the 13. It's going to be blasting from 2 to 3 PM. I have just put a meeting in your calendar regarding this. (t: 2492) And that is an integration or something that you've kind of built with Manas connecting it to your system? (t: 2500) We, we, no, we actually. We just built the whole thing. So, uh, essentially our companion built an assignment, the assignment you iterate on this assignment, just in a document format where you explain exactly what needs to happen. (t: 2510) And, you know, there's like little things like I'm 90 kilograms and I'm to make sure that I take the right kite and that I choose the right height and add in San Francisco, have flow and add and add helps you go up, uh, uh, upwind, which is great. (t: 2520) And I want the perfect combination of it. (t: 2530) But like ads and flows, they are not appearing at the same time. So they actually do not run a 24 hour schedule, which means that it's actually like complicated algorithm to know when to go to kite surfing. (t: 2543) Interesting. I mean, that brings up a, a very tactical question that I had, uh, in looking at word where I thought I saw something in an FAQ that suggested that you weren't enabling like always on agents that like. (t: 2560) Just like do like a regular-to-week program. Running 24 hours in the background. And like completely do a task for a user, um, but that the user had to launch the agent or something like that. But you're describing something that. Can you explain it like something I'm sorry, I have to try. Yep. Well, let's previous topic. Oh. it's nice that. That's (t: 2570) based on what you, something topic, like I, I recommend everyone to start a website using (t: 2580) w mindfulness, um, So Однако (t: 2590) ボ forest.ora But until you see this now on vi President of the 28th of December. but we might have to cut out at this version, but I would recommend every go to S to everyone go to Suсти, the day I like a finisher sauna, and uh. And that's our companion. It helps you reflect. It helps you be the better version of yourself and helps you be the companion that builds all of these background agents for you (t: 2600) and sign up for a waiting list because the stuff we're cooking is pretty powerful. I probably have like, I don't know, 30, 40 different background agents (t: 2610) doing everything from prioritizing my email, catching me up on, you know, beautifully voiced and transcribed, but beautifully, sorry, (t: 2620) beautifully transcribed and voiced to me a morning update. It searches for every big old document and puts it through a simple prompt of like, (t: 2630) hey, anything weird? Or it searches through all of my transcripts, applies the books that I'm actually trying to become a better leader. (t: 2640) So 15 rules of conscious leadership and the great CEO event and tells me about how I can do better. It, you know, collates, all of my previous one-on-one transcripts (t: 2650) and checks whether somebody is performing or not in the team. There's just so much stuff or checks through, (t: 2660) monthly checks through all of my Slack and gives me a report on the company sentiment and where potential sources of conflict. Checks through all linear tickets (t: 2670) in order to see whether we're on track and is anyone polluting our linear board with some random bullshit. And there's, there's a lot that's going on (t: 2680) when I'm not working. I love it. I go to, I go somewhere and then I get a bunch of notifications and I respond to them and I get work done. (t: 2690) Yeah, that's very cool. Very cool. Awesome. Well, Philip, it was great to meet you and catch up with you and learn a little bit about what you are up to. (t: 2700) I'll definitely be keeping an eye on Wordware and checking out the tool. Perfect. Thank you so much, Tom. I've had a little fun. Thank you. (t: 2710) Thank you. Thank you. Thank you.

