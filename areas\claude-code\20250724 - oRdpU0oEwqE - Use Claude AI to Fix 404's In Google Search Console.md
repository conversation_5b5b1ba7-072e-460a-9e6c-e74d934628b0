---
title: "Use Claude AI to Fix 404's In Google Search Console"
artist: AI Business Lab
date: 2025-07-24
url: https://www.youtube.com/watch?v=oRdpU0oEwqE
---

(t: 0) Hey guys, what's going on? It's your boy, it's <PERSON>. Thank you for clicking on this link, and if you've clicked here, then you're here for one specific reason, and that reason is that you have too many redirects on your website, and you want to fix them all at once, (t: 10) and you probably want to use AI to do it. Without further ado, I'm going to show you how to fix this in five minutes or less. Ready? Let's go. This is how I fixed 2,459 broken links (t: 20) in five minutes or less. Now, what happens is you'll see here, broken links to fix. (t: 30) Every now and then when you're on your website, you'll do something, you'll change a category, you change a page, maybe you change your homepage link or a secondary page link, (t: 40) and then that has a cascading effect where it just goes through and it starts breaking a ton of other things on your website. What you can do very easily is you can use AI to create (t: 50) a plugin, which you can... Upload to your website, and that plugin will go through and correct all of your redirect links (t: 60) in every blog post, on every page, every redirect you have, because every redirect you have takes energy from that SEO juice that should be pumping your website full of new people and new customers, (t: 70) and it's not. It's not. I'm going to break this down for you a little bit right here. Multiple redirects. This can look a couple of different ways. (t: 80) Maybe a redirect is a page is missing and multiple pages were linking to it or it was linking to other (t: 90) pages. So pages come in, this page is linking to the main page, but it's broken. So now you've got (t: 100) a broken link here. Or maybe this page was linking to here. Or maybe this page was linking to here (t: 110) through here. All broken. Broken, broken, broken. Another thing is that if there's too many redirects, what can happen is maybe there was a page and it disappeared, (t: 120) and then you changed the redirect link on it and your rank math sitemap or whatever. So now this page is linking to this page and this page is linking to this page. And it goes back and forth and back and forth and back and forth. (t: 130) Guess what? You're losing SEO. (t: 140) So this is the strategy. And I'm going to do this quick version for everyone who doesn't have a lot of time. I'm going to do a long version if you want to stay and hang out. Quick version. Collect all of the redirects. (t: 150) Then load them into AI. OK. Then create a plug in to correct all the issues. Add the plug in to your website and run it. That's it. What do you need to do this? (t: 160) Collect all the redirects. You can use rank math. You can use Screaming Frog. Screaming Frog looks like this. I'll walk through in a minute. (t: 170) Load them into AI. What do you need for AI? Cloud code, cloud desktop, even ChatGPT can do this. Create a plug in. How do you do that? You ask AI to create a plug in. (t: 180) How do you add the plug in to the website? You save it as a text edit file dot PHP. You zip that and then you can drop it right in as a plug in. (t: 190) All right. That's all you needed. And you just needed some simple instructions. Thank you for clicking on this. For the rest of you want to hang out. Let's talk about it. OK. Now. Broken links are a real thing. (t: 200) OK. Now. Broken links are a real thing. OK. Now. Broken links are a real thing. where you can get them is Screaming Frog. Now, if you go to Screaming Frog, you'll see here, I ran a free, this is a free one. (t: 210) Maybe not, I don't know. And it gave me all of these. So I went into here, I clicked response codes. (t: 220) I clicked overview. I clicked response codes. I clicked overview and then I clicked client error for XX. (t: 230) And then that gave me all of these. I grabbed these and I dragged over here and I clicked copy. And then I came into here and I literally just said, (t: 240) I wanna create a plugin so I can fix all of these issues. And then I told it what I wanted it to do (t: 250) and it pumped this out. And then I said, hey, give me step-by-step instructions because I don't know what I'm doing half the time. Said fine. And then from there, I came in, I went to the backend (t: 260) and then I'm gonna go to Okanagan and I get paid from this transformative startaidonal improperly. So I click delete and then I click latest tool (t: 270) and then I get to see this bug fixer collection from Okanagan. ** années battery approaching about half an hour ago Nothing different but my account was broken and it hated it. I håme guns all the way out of this stream because security department said to please play home videos I have taken a phone response to my phone (t: 280) and we've got not a single thing changed so if I know what I need to fix. I'm in 검찰 I need to be able to move redo anyway. (t: 290) So I dozen said, okay, drop you in the boss room to wennt for more RAV4ED features. I see a few林kos have dumped the base. was grab this and they gave me really specific instructions. I copied this, I did this, I did text edit, (t: 300) opened up a new page, it came here, I pasted it. And then I came to format and it said, make plain text. (t: 310) I made a plain text and then I saved it with the .php. Then I zipped it and I'll show you, (t: 320) I just went over it here in downloads and I zipped it. You'll see here, it's in downloads. (t: 330) I just click compress. And then once compress was done, I came to plugins (t: 336) and then in plugins, I just drag and drop. (t: 340) I clicked add plugin, I clicked upload plugin and I dragged it right onto there and I clicked install now. (t: 350) And then it ran all its own stuff. And now all of the issues that I had with the redirects everywhere, cause I had a lot of redirects. Let's see if some of them are still here. (t: 360) They're all gone. Cause the app actually put them in the trash for me. The app deleted it cause I total hate. (t: 370) And you can see it here. (t: 372) It says here, delete Rank Math redirects after fixing. And it's just a button that did automatically. (t: 380) And this you can do if you have a bunch of redirects, and this corrected for me, (t: 384) 24, 259 broken links were fixed with this. (t: 390) So you could do this yourself. If you're interested, I'm going to have a link below and I'm gonna have this as a plugin that you can download (t: 400) and you can use it yourself. And yeah, it's a new SaaS project for the day. So anyways, if you found some value in this, click the link, click the bell icon, drop a like, (t: 410) drop a subscribe. Drop a comment. Give me the link to your website. If you threw this on your website, I'd love to check it out. I'm happy to review websites that I find (t: 420) that you guys send me. And yeah, if you watched the end, you're an absolute legend. Thank you so much for spending today with me. You're the greatest. Ciao.

