---
title: Build an AI Army With <PERSON>’s New Sub-Agents
artist: <PERSON>
date: 2025-08-02
url: https://www.youtube.com/watch?v=9i3ic1sVhlI
---

(t: 0) Cloud Code is the most powerful coding agent and general agent in the entire world. In my opinion, it is the closest thing that we have to AGI. And <PERSON><PERSON><PERSON> just released a new feature to Cloud Code that brings us closer to AGI, (t: 10) and that is sub-agents. I have not yet tested sub-agents, and that's what we're going to do today. (t: 20) We are going to create a master agent who has a team of smaller agents beneath it. So I don't want to waste any more time. Let's dive into the video. (t: 30) I created this kind of visual diagram, and this is kind of how I understand what's happening. So basically, when you ask Cloud or ChatGPT, which are now agents, right? (t: 40) They have access to tools like browsing the internet. But now you can create agents that have a team of sub-agents. (t: 50) And so the user asks the primary agent. The primary agent delegates to sub-agents, and the sub-agents respond to the primary agent. (t: 60) That is the key distinction. The sub-agents don't respond back to you as a user. Primary agent delegates to sub-agents, and the sub-agents report back to the primary agent. And the primary agent synthesizes that information into a response that is cited that shows attributions. (t: 70) It's very clear where the information came from, which sub-agent. (t: 80) And this is built directly into Cloud Code, which we're going to set up in just a second. Another cool fact about these sub-agents. After the user enters something in or a query, the primary agent not only delegates them like this. (t: 90) They're actually delegated in parallel. So they can happen at the same time, right? (t: 100) It would be a lot more annoying, you know, if they went one, two, then three, then four, right? This would be a very annoying process to basically work your way through linearly. (t: 110) And that's how a lot of AIs have worked so far that have access to tools. It's been quite linear, and that's why it takes so long. (t: 120) So this primary agent can actually plan for these agents to go off in parallel to do tasks. And then it gets its progress report gets sent back to the primary agent, which can synthesize it so that the user knows what's happening. (t: 130) Each sub-agent has a system prompt, a tool set, and a context window. (t: 140) And so many people have understood the concept of context window in terms of chat. Chat. You might have heard headlines about how Gemini's new million token context window. (t: 150) And basically there's input tokens, which are how much you basically type into the AI. (t: 160) And then there's output tokens, which is how much the AI types back to you. So like you have these chats going on. You have your main chat. (t: 170) Let's call this your main chat. And then you have this agent that has a better understanding of your main chat. And that's your primary agent. If you think of this as a max capacity or max tokens that fit into a chat, you have these sub-agents that can process things without clouding your massive context window because the role of your primary agent is to synthesize this information. (t: 190) It's distilling it down to the important information. And that's what lives in the main chat here. (t: 200) And so that's basically what's happening with context window. Another thing that's really important is your primary agent will have access to tools. And it'll do things in a loop until it decides that it's done. (t: 210) Then it will get back to the user once it's decided that it's done. And it can have access to external tools. If you think of a tool like Cursor, within Cursor, if we were to open some app here, I don't know what this is. (t: 220) But if I were to type into this side panel right here, this is an agent right here. (t: 230) But it has access to tools. It can search my code base. It can search the web. Search the web and make. A change. (t: 240) Right? This is just a popular vibe coding tool. I'm showing you an example. And so right now it's planning. So it's using the thinking model, which all of these models. And so now it's probably it's going to read. (t: 250) This is a tool. It's reading another file. That is the same tool, but doing a different thing. Now it's planning again. And so it'll run all these things. Now it's doing a fire crawl search. (t: 260) That was another tool. Now it's calling brave search. So these are different tools that it can use. And it's using these tools in a loop. Until it decides that it's done. (t: 270) And that's kind of the way that I conceptualize it. And that's how you give it agency. Right? And automation is like kind of fixed. Where no matter what, it's going through once. (t: 280) With this. Right? You can give it different tool sets. But one thing. Right? With a primary agent. The more tools you add to an agent. (t: 290) The less consistent it becomes. Let's say we had a master content agent. Which I am actually currently building right now. (t: 300) That's probably for a future episode. If you gave it access to. It could post on X. It could post on Insta. It could respond to DMs. (t: 310) If it had access to all of these different tools. It would get very chaotic. If you had like 50 tools it could have access to. Because remember. It decides when to use these different tools. (t: 320) That's what makes it an agent. Right? Your agent. When you give it a query. Right? Ideally you want to give it a good prompt. It will actually. Decide which of these tools to use. (t: 330) And the more you give it. The harder it is for it to be correct. In which tool to use. So the way. That Claude. (t: 340) Or Anthropic. Has decided to solve this problem. Is to allow you to create these sub-agents. Where the primary agent knows the function. Of the sub-agent. But it doesn't have to have like. Detailed knowledge. (t: 350) Of all of the tools it has access to. That's up to the sub-agent to know. Because the sub-agent doesn't even know. What the primary agent knows. The primary agent knows. (t: 360) What the sub-agent needs to know. And so that way. Everything's faster. There is a higher probability. That the sub-agent will succeed. Because you can test the sub-agent. (t: 370) Independent of the whole workflow. So that you want to basically maximize. How frequently the sub-agent succeeds. And so the less tools you give. (t: 380) The higher that percentage will be. And then you just need to give primary agent. Really good instructions. On how to use these sub-agents. And what data to send to those sub-agents. That's what's really important. (t: 390) And so these tools might be. For one sub-agent. So sub-agent one. Might have access to these tools. And sub-agent two. Might have access to these two tools. (t: 400) And that is kind of. What the tool set means. A different set of abilities. These are functions. Like calculators, APIs. (t: 410) Executors. That type of thing. And then of course. All of these have their own system prompt. So the primary agent has a system prompt. (t: 420) For those of you who have used chat GBT. Maybe you've created a custom project. On chat GBT. Or Claude. Or you've created your own custom space. (t: 430) In Perplexity. You create a custom system prompt. For that project. So that anytime you ask something. It has the necessary context. (t: 440) And every single one of these. Has its own system prompt. Which would include information. About these tools. (t: 450) You don't have to write one massive system prompt. Instead. You'll write a system prompt for your primary agent. And that system prompt. Will basically give. (t: 460) Context of what sub-agents. That it has. This is just a good way to conceptualize it. But basically your job. Is to tell the primary agent. (t: 470) What sub-agents. It can delegate to. And then with the sub-agent. Your job. Is to make sure. That it uses. (t: 480) The tools effectively. That it follows the instructions. Of this primary agent effectively. Each one of these sub-agents. Has their own system instructions. That it uses every single time. (t: 490) And it's very very important. If this is an agent you want to use over and over again. Or use in a way that's fully automated. Then you should spend tons of time. (t: 500) Writing these system instructions. And making sure that it uses these tools. And follows instructions effectively. So that you can just let it run autonomously. And if it's a profitable activity. (t: 510) You can just turn it up. You can give it more tokens. And if it is something that benefits a company. You could run it forever. And that's one thing that makes agents. More effective than human employees. (t: 520) If you find something that works. It's a lot easier to turn it up. Than to turn up the activities. A good employee does. Because you would need to duplicate them. Which is not possible. (t: 530) And that is why I'm so excited. So now what we're going to do. Is we are going to test this. And to test. You need to download Node.js. Then what you need to do. (t: 540) Is you need to copy this. Right here. And what we're going to do. Is we are going to. Open up terminal. Because remember. (t: 550) This runs in the terminal. And so I'm going to go to a new desktop. And what I'm going to do. Is I have my terminal open. Right here. (t: 560) And in order to access cloud code. All you have to do is type cloud. But if you haven't yet. Downloaded cloud code. You need to paste this in right here. And press enter to download the latest version. (t: 570) And that will allow you to download cloud code. Since I've already done that. I'm not going to do that. All I need to do is type in cloud. So now we can talk to cloud code. (t: 580) And remember. If you watch my last video. You know that cloud code. Controls files on my computer. It is a command line interface. And it is a lot easier. (t: 590) Once you start using it. Once you realize that it can just create any type of file. You kind of realize the power of this. Software. Right. Let's go ahead and go into our documents folder. (t: 600) And create. A folder. In my documents. Folder. Called testing sub agents. And so this is going to create a file. (t: 610) In the documents. There we go. Testing sub agents. And we see that. We can go into this folder. Now. I want to use. (t: 620) The new. Cloud code. Sub agents feature. Search the web. Tell me about. This. New feature. (t: 630) And put it. In a markdown. Simple. Explanations. And simple. Bullets. But first. (t: 640) Please make sure. I have the latest. Version. Of cloud code. Now. It should. Give it a list of to do's. (t: 650) And remember. This is just. The main agent. We have not yet used. Sub agents. And so sometimes. You need to give it. Permissions. Right. It'll be like. (t: 660) Do you want to proceed. I just always press. Yes. But. We're just going to go ahead. And we are running. Cloud code here. Do you wish to create. This markdown file. (t: 670) And I'm going to hit. Yes. And there we go. Look at this. Cloud code. Sub agents. Now we have this clean. Markdown file. So sub agents. Are specialized. (t: 680) AI assistance. In cloud code. That handles specific. Types of tasks. They work. Like having. Different experts. For different jobs. Each with their own focus. (t: 690) And tools. Very. Very interesting. Very cool. Now what I want to do. Is. I. Want. To. Move to an IDE. (t: 700) For those of you. Who. Have never written any code. This part. Might get. A little bit weird. Because we're opening up. A coding tool. Called cursor. We're going to open up. (t: 710) A new window. And we. Are going. To. Open project. And we're going to. Open the project. At the same location. That we just created. The project. Which was testing. (t: 720) Subagents. Open. And an IDE. Allows you to see the files. Because remember. Coding is just. Files on your computer. Just like. You know. Microsoft. Word doc. And so instead of. You looking at this view. (t: 730) We're going to be looking at this view. Because it's a lot easier. To just very quickly. See. The different things. And so. What we're going to do. Is we're going to click. Model. And I'm going to switch. To. (t: 740) Opus. This one is a lot. More expensive. So if you want to save money. Don't use Opus. But what I'm going to do. Is I. Want. You. To. Search. (t: 750) The internet. More. Find. Someone's. GitHub. Repo. Template. For. Inspiration. And GitHub. They. (t: 760) People create these agents. And by the way. These agents are created. With files. Right. These are just files on your computer. And you can create these agents. Just by creating markdown files. On your computer. (t: 770) And that's what I'm getting to. We are literally. Just going to create files. And remember. This might look scary. But all this is. Is just markdown. And markdown. Is just a special way of text. It just turns these. (t: 780) Hashtags. Into headings. It's nothing. Crazy. Nothing. Special. They're just little files. And we're going to create these agents. By creating these files. And what we're going to do here. Is we are looking for. (t: 790) Someone's existing. Filebase. Or codebase. On GitHub. Who set up subagents. And so we're going to look at it. For inspiration. Then I want you to. (t: 800) Guide me. To setting. Up. My first. Subagent. On. Claude code. Right now. Using. The agent. (t: 810) Command. Please. Tell me. How to do it. And. Put it. In a. Markdown file. In this. Folder. I was going to have it created. (t: 820) But I actually want to use. The new agent. Because there's. There should be. A new agent. So yeah. You can create. You can. Set up agents. But I actually want. Opus to do some research. (t: 830) And then. I want to. Set up the agent. Manually. Right here. And. We're going to have. AI. Teach us how to do it. And. What's. Really cool. Is we're actually going to be able to. Set up different colors. (t: 840) So it'll actually show them. In the terminal. Here is different colors. When you create. Subagents. But for now. Let's just wait for it to. Be done. Getting research. A few. Moments later. Okay. (t: 850) So here. We are setting up your first. Claude code. Subagent. And. So now we can actually type in. Slash tag. Or slash. Agents. And we're going to do this. Within the project. (t: 860) So you can either create this. This is more globally. Or within the project. I'm going to do. Project. And. I. Am going to do. Generate with Claude. And now describe what you want. (t: 870) This agent. To be able to do. So. I want. This. Agent. To. Be able. To. Post. On. X. Using. (t: 880) The. X. API. Please. Put. Placeholders. In. For. The. Keys. That. I need. I'll. (t: 890) Give. Them. To. You. In. Just. One. Second. And so. I. Want. One of these tools. To be able to. Post. On. X. And. (t: 900) This. Is. Just. One. Tool. That. Will. Give. To. One. Of. The. Sub. Agents. And. You. Could. Just. Make. This. A. Tool. For. One. Of. The. Agents. But. I. Think. We. Should. Keep. This. Twitter. Agent. Separate. And. (t: 910) So. It. Is. Now. (t: 920) Agent. CMO. Agent. What. I. Want. To. Do. Is. I. Want. To. Create. Over. Time. We're. Going. To. Start. With. Very. Simple. (t: 930) Tasks. And. In. This. Video. We're. Creating. Just. Like. The. First. Part. Maybe. I'll. Make. This. A. Multi-part. Series. But. We're. Creating. The. CMO. Agent. And. We're. Giving. Him. Access. To. (t: 950) Tools. Which. Part. Of. The. Tools. That. It. Has. Are. Posting. On. The. API. It'll. Generate. System. Instructions. And. It. Will. Also. (t: 960) Have. Its. Own. Context. Window. And. So. That. Is. Kind. Of. How. This. Is. Being. Organized. And. This. Twitter. Agent. That's. Where. It. Fits. In. So. Now. What. (t: 970) We're. Going. To. (t: 980) Just. Call. It. X. API. Poster. This. Will. Be. The. Name. Of. The. Agent. You. Are. An. Expert. X. Formally. Twitter. API. Integration. (t: 990) Specialist. You. Handle. All. Of. The. Posting. Content. To. X. And. Your. Core. Responsibilities. Etc. Etc. Okay. What's. This. Created. The. Key. Steps. To. Create. Your. First. Sub. Agent. Okay. (t: 1000) Create. New. Agent. Now. We. Should. Have. A. Image. Generator. Agent. That. Uses. The. F. A. L. API. I. (t: 1010) Want. To. Be. Able. To. Use. The. Most. Popular. Image. And. Video. Generators. Pick. One. (t: 1020) Image. Gen. And. One. Video. Gen. And. Put. Them. As. Tools. In. The. And. We. Can. Name. It. I. Know. I'm. Typing. Into. The. Ether. Here. (t: 1030) But. Bear. With. Me. We'll. Call. This. Image. Gen. Agent. Okay. So. We. Have. This. Twitter. Agent. And. Part. Of. The. Tools. One. Of. These. Tools. Is. The. X. The. X. API. And. So. This. (t: 1040) Tool. Right. Here. Requires. And. Any. API. Requires. An. API. Key. And. Then. Sometimes. Like. Other. Tokens. Like. User. ID. In. X. (t: 1050) API's. Case. It's. Kind. Of. Many. Different. Keys. Actually. I. Think. It's. Like. Four. Keys. That. You. Need. Access. To. Which. It's. Kind. Of. Annoying. But. In. The. Second. Agent. Here. (t: 1060) And. We're. Actually. Going. To. Hit. Continue. And. We. Will. Make. This. One. Green. And. This. Is. The. Image. Gen. Agent. Which. (t: 1070) Is. Green. We'll. Have. Access. To. Tools. And. These. Tools. Will. Be. FAL. Which. Is. A. Provider. That. Gives. You. Access. To. Many. (t: 1080) Different. Image. Gen. Or. Video. Gen. And. We're. Going. To. Give. It. An. FAL. API. Key. That's. What. We're. Going. To. Need. To. Give. It. And. These. Are. All. Keys. That. Are. Specific. But. Right. The. Twitter. (t: 1090) Agent. Won't. Have. Any. Knowledge. Of. The. API. Keys. And. The. Master. Agent. Won't. Have. That. In. Its. Context. Either. That. Is. Just. For. These. Sub. Agents. To. Know. Okay. Three. (t: 1100) Agents. Created. Okay. Create. New. I. Don't. Want. To. Create. New. Agent. I. Just. Let's. Hit. Enter. View. Agent. Okay. Great. Now. Okay. Put. The. Agents. In. The. (t: 1110) Code. Base. Files. In. Testing. Sub. Agents. And. So. Now. It. Is. Copying. Those. Folders. Into. Here. So. What. I'm. Going. To. Do. Is. I'm. (t: 1120) About. To. Give. It. Access. To. All. Of. My. API. Keys. And. Actually. I. Want. To. Actually. Create. One. More. Agent. And. I'm. Going. To. Make. This. Really. Quickly. Create. (t: 1130) New. Agent. We're. Going. To. Put. It. In. The. Project. We're. Going. To. Generate. With. Cloud. Research. Agent. That. Researches. The. Web. Quickly. About. Any. Topic. (t: 1140) This. Is. Just. (t: 1150) Okay. Now. We're. Just. Going. To. Hit. Continue. And. Let's. Just. Make. This. Cyan. Okay. Now. Put. This. Agent. In. This. Project. And. (t: 1160) Run. This. Agent. To. Search. Please. Do. This. Correctly. Like. I've. Never. Done. This. Before. (t: 1170) I. Want. To. Be. Able. To. Use. These. Agents. So. Set. Up. The. Project. Of. Testing. Sub. Agents. (t: 1180) So. That. This. Works. As. Anthropic. Wants. It. To. Work. Please. Set. Up. The. Code. Base. Correctly. (t: 1190) Based. On. Best. Practices. Please. Okay. So. I. Wanted. To. Put. The. Agent. Information. In. Here. So. It's. Like. Copy. The. Web. Research. (t: 1200) Specialist. Alright. So. I'm. Just. Going. To. Very. Quickly. Add. That. To. Our. Little. Thing. Here. We. Have. This. Web. Research. Specialist. As. A. Sub. Agent. That. Will. Be. The. CMO. (t: 1210) Agent. Because. We. Need. To. Create. The. Master. Agent. As. Well. And. That. Is. What. We. We. Need. To. Do. So. It. Just. Added. Something. Now. It. Has. A. Dot. Claude. With. An. Agents. Folder. (t: 1220) Within. It. So. As. Dot. Claude. And. It. Has. Agents. In. It. Okay. So. I. Think. It's. Going. To. Put. The. Agents. In. This. Folder. Right. Here. So. Put. The. Web. Research. Specialist. In. There. I. Hope. It. Puts. (t: 1230) The. Other. Two. In. There. As. Well. Okay. So. Name. Rebs. Researcher. Specialist. The. Description. Is. Use. This. Agent. When. So. In. The. Sub. Agent. You. (t: 1240) Put. A. Description. Of. The. Agent. In. Here. And. That. Will. Tell. The. Main. Agent. When. To. Use. That. So. That's. Good. To. Know. What. Are. The. Latest. Developments. Okay. So. These. Are. Examples. That's. (t: 1250) Very. Interesting. Commentary. Since. The. User. Is. Asking. For. The. Latest. Development. Which. Require. Up-To-Date. Information. Use. The. Web. Research. Specialist. Agent. To. Research. This. Topic. Okay. Cool. Amazing. So. That. Makes. Sense. And. (t: 1260) Then. Here. Is. You. Are. An. Expert. Research. Specialist. With. Exceptional. Skills. At. Finding. Evaluating. And. Synthesizing. Information. And. There. We. Go. We've. Created. This. Agent. That. We. Can. Use. Okay. (t: 1270) So. Now. It's. Created. This. Cloud. MD. File. And. So. This. Project. Demonstrates. The. Proper. Setup. And. Usage. Of. Cloud. Code. Subagents. Following. Anthropic. Specs. Practices. And. Here. It. (t: 1280) Has. The. Project. Structure. So. This. Cloud. MD. Is. Whenever. I. Ask. Something. To. Cloud. This. Is. Kind. Of. This. Main. Agent. That. Now. Fully. Understands. The. (t: 1290) Code. Base. And. Here. We. Can. See. The. Web. Research. Specialist. Which. Is. Highlighted. I. Think. It's. Doing. A. Test. Let. Me. Test. The. Web. Research. Specialist. Agent. With. Real. Research. (t: 1300) To. Ensure. Everything. Is. Working. Correctly. That's. Very. Cool. So. Now. We. Have. This. Web. Research. Specialist. And. It's. Highlighted. So. In. This. Interface. The. CLI. Interface. We. Can. See. (t: 1310) Which. Agent. Is. Working. And. It. Is. Searching. For. AI. Agent. 2025. Companies. Leaders. Blah. Blah. Blah. And. That's. Very. Cool. And. So. While. It's. Working. What. We. (t: 1330) Outputs. In. The. Outputs. Folder. And. Yeah. That's. What. We. Can. Do. I. Just. Made. A. New. Folder. Called. Sensitive. Keys. So. I. Just. (t: 1340) Created. These. Sensitive. Keys. I. Do. Not. Want. These. I. Do. Not. Want. To. Show. You. This. Folder. Because. They. Are. Indeed. Sensitive. Keys. So. What. I'm. Going. To. Do. Right. Now. Is. I'm. (t: 1350) Going. To. Go. To. Cloud. Code. And. Remember. These. Keys. Are. Right. Here. Right. The. X. A. P. I. Key. And. Then. The. F. A. L. A. P. I. Key. This. These. Are. Tools. Right. So. Okay. I. (t: 1360) Want. To. Finish. Setting. Up. The. A. P. I. Keys. I. Want. To. Finish. Setting. Up. The. A. P. I. Keys. For. X. And. (t: 1370) For. F. A. L. Please. Search. Both. Use. The. Normal. Research. Tool. Not. The. Web. Research. Specialist. (t: 1380) For. Whatever. Reason. That. Wants. To. Research. For. A. Very. Long. Time. And. I. Think. By. Looking. At. The. Prompt. I. Understand. Why. Just. Find. The. Docs. Get. It. Set. Up. So. The. X. (t: 1390) A. P. I. Agent. Can. Post. And. The. Image. Generator. I. Agent. Can. Generate. Images. And. (t: 1400) Videos. Um. When. It. Needs. To. Please. Set. Up. These. Tools. Properly. Oh. Wait. I. Never. Told. It. About. The. Sensitive. Keys. (t: 1410) I. Want. To. Stop. It. And. Say. I. Put. All. The. Keys. For. Everything. In. The. Sensitive. Keys. Dot. MD. File. (t: 1420) This. File. Has. All. The. Keys. Now. I. Configure. The. Keys. Let. Me. Update. The. Agent. Configuration. It. Configured. The. API. Keys. Now. It's. Creating. Example. Scripts. (t: 1430) Showing. How. To. Use. The. Agents. Okay. Your. Agents. Are. (t: 1440) Going. To. Be. Using. Vibe. Mo. Vibe. Mo. Is. My. Automated. AI. Agent. CMO. Vibe. Agent. That. That's. What. These. Keys. Are. (t: 1450) Set. Up. To. So. This. Is. The. Account. That. It. Will. Likely. Be. Tweeting. To. I'm. Going. To. Test. Please. Delegate. To. The. Ex. Poster. Agent. To. Post. Hi. There. This. (t: 1460) Is. The. First. Time. I'm. Using. Sub. Agents. Okay. So. Ideally. It. Should. Use. The. Correct. Agent. The. Agent. Is. Blue. Remember. We. (t: 1470) Selected. The. Blue. Color. So. We. Can. See. The. Ex. API. Poster. That. Is. Very. Cool. Okay. Your. Tweet. Has. Been. Posted. The. Ex. API. Poster. Is. Successfully. Posted. The. Image. (t: 1480) Hi. There. This. Is. The. First. Time. I'm. Using. Sub. Agents. Let's. See. If. This. Is. True. And. By. The. Way. This. Is. Where. I. Am. I'm. On. The. Developer. Portal. This. Is. Where. You'll. Find. (t: 1490) All. Of. The. Information. That. You. Need. And. They're. In. The. Keys. And. Tokens. So. That's. What's. Going. On. Here. But. Right. Now. I'm. Going. To. Twitter. I'm. Going. To. See. If. It. Actually. Tweeted. (t: 1500) Hi. There. This. Is. The. First. Time. I'm. Using. Sub. Agents. Okay. So. Now. What. We. Need. To. Do. Here. Is. We. Need. To. Verify. And. It. Even. Said. That. Like. It. Even. Put. The. Status. (t: 1510) Here. Right. You. Can. Actually. See. This. What. I. Want. To. Do. Now. Is. I. Want. To. Go. Back. To. This. Right. Here. So. Let's. Just. Keep. Little. Notes. (t: 1520) On. Here. So. We. Got. This. Working. The. Web. Research. One. Takes. A. Long. Time. But. I. Do. Think. It's. Working. But. We. (t: 1530) Generate. (t: 1540) An. Image. Of. A. Turtle. Flying. Through. Space. With. An. Advanced. Image. (t: 1550) Model. And. Please. Put. It. In. The. Outputs. Folder. After. You. Do. That. Make. Sure. To. Add. Instructions. In. (t: 1560) All. Necessary. Places. To. Make. Sure. That. All. Of. The. Image. Outputs. End. Up. In. The. Outputs. Fold Folder. So. You. See. Here. These. Are. All. Files. And. We're. Just. Creating. These. (t: 1570) Files. For. Agents. And. Giving. These. Files. Access. To. Tools. And. By. Giving. Access. To. These. Tools. It. Can. Take. Actions. Which. Generates. More. Files. And. Puts. Them. (t: 1580) And. Keeps. Them. Organized. And. If. You. Have. Good. Instructions. As. You. Generate. More. Things. Generate. More. Tweets. Right. You. Can. Actually. Go. Out. On. The. Internet. And. Generate. New. Things. Hi. This. Is. The. First. Time. I'm. (t: 1590) Using. Sub. Agents. That's. Pretty. Cool. All. Right. So. It. Is. Generating. A. Turtle. In. Space. Hopefully. It's. Just. Generating. An. Image. Test. Image. Video. Generator. Agent. With. A. Turtle. In. Space. Prompt. (t: 1600) Update. Agent. Configuration. To. Save. Outputs. Folder. Okay. So. For. This. We. Are. Using. FALs. API. They. Have. A. Bunch. Of. Different. Models. So. We. Can. Actually. Hit. (t: 1610) Explore. And. We. Are. Going. To. Do. Text. To. Image. Bria. Text. To. Image. Right. Here. Let's. See. If. What. Happens. When. We. Run. This. (t: 1620) Let's. See. What. It's. Doing. Oh. Outputs. Is. There. An. Output. In. Here. Oh. There's. A. Space. Turtle. Output. Okay. Look. At. That. We. Have. This. Space. Turtle. Very. Cool. Now. What. I. Want. To. Do. Is. (t: 1630) Update. The. Agent. Configuration. To. Save. Outputs. To. The. Folder. Okay. Now. Please. Update. The. Image. Video. Generator. (t: 1640) Based. On. What. You. Learned. Right. Because. It. Failed. At. The. Beginning. I. Don't. Know. Why. It. Failed. At. The. Beginning. But. Then. It. Ended. Up. Successfully. Generating. It. With. What. You. Learned. The. (t: 1650) Space. Oh. I. Don't. Think. We. Can. At. Code. Files. The. Space. Turtle. Image. Was. Successful. I. Want. It. To. Be. Successful. (t: 1660) Every. Time. Oh. Update. The. Documentation. With. Folder. Instructions. Okay. Yeah. That's. Just. That. Part. Then. I. Want. You. To. Generate. A. (t: 1670) New. Image. Of. A. Similar. Style. Except. Make. It. A. Panda. Because. The. Um. (t: 1680) Vymo. Is. A. Panda. Of. A. Panda. Flying. Through. Space. And. Please. Make. A. Two. Sentence. Tweet. Along. With. (t: 1690) This. Image. Like. Post. The. Image. On. Twitter. And. Make. It. About. How. You. Delegated. To. These. Subagents. And. (t: 1700) Describe. What. Tools. You. Used. Okay. That's. A. Really. Confusing. Front. But. It's. Opus. So. It's. Very. Powerful. We. Should. Probably. Switch. Back. To. Sonnet. (t: 1710) And. We. Should. Probably. Condense. Our. Context. Or. Switch. To. A. New. Chat. So. This. Is. What. It's. Going. To. Do. It's. Going. To. Update. The. Image. Video. Generator. So. The. The. The. Cloud. Code. Is. Going. Through. Your. (t: 1720) Files. The. Same. Way. You know. The. Cursor. Agent. Is. But. Now. We. Can. Delegate. To. Any. Of. These. Agents. That. Are. In. This. Dot. Cloud. Slash. Agents. Folder. And. We. Can. See. Which. Agent. (t: 1730) Is. Working. Based. On. What. Color. Is. Showing. Up. Here. And. So. The. Plan. Is. To. Update. The. Implementation. Details. I. Wanted. It. To. Update. This. Based. On. What. It. Learned. Then. (t: 1740) It's. Going. To. Generate. An. Image. Of. A. Panda. It's. Going. To. Put. It. In. The. Outputs. Folder. Hopefully. Just. Like. It. Did. With. The. Space. Chartle. And. Then. It's. Going. To. Create. A. Tweet. With. The. Sub. Agent. Describing. What. Tools. It. Used. To. Create. The. (t: 1750) Tweet. And. Post. The. Tweet. With. The. Image. To. X. Let's. See. So. Right. Now. It's. Generating. The. Image. We. Can. See. Which. Sub. Agent. Is. Working. Okay. It. Generated. The. Space. Panda. There. We. Go. We. (t: 1760) Have. This. Space. Panda. It's. Been. Generated. I. Don't. Know. Which. Model. That. It's. Using. But. Now. It's. Going. To. Post. It. On. X. And. Ideally. (t: 1770) Okay. Generate. The. Space. Panda. Okay. Create. The. Tweet. About. Sub. Agents. Because. Remember. I. Asked. It. To. Describe. The. Flow. And. Then. It. Should. Post. It. On. X. Now. (t: 1780) It's. Initializing. The. X. API. Poster. And. It. Is. Hopefully. Able. To. Post. It. With. The. Image. We. May. Run. Into. Problems. With. The. Image. I'm. Not. Sure. If. It's. (t: 1790) Going. To. Be. Able. To. Convert. It. Into. The. Right. Format. To. Get. It. Posted. But. We. Will. See. Let's. See. Okay. It. Claims. That. It's. Posted. It. On. X. Let. Me. See. If. It. Gives. Us. A. Tweet. Okay. Your. Tweet. (t: 1800) Is. Live. Let's. See. If. This. Works. So. I'm. Going. To. Refresh. It. Here. Wow. There. We. Go. Just. Delegated. Image. Generation. To. My. Image. To. (t: 1810) Video. Generator. Subagent. Using. Claude's. Claude. Code's. Task. Tool. Which. Called. The. F.A.L. Flux. Model. A.P.I. Created. Whimsical. Say. Subagent. To. Handle. (t: 1820) Specialized. Tax. Independently. Making. Complex. Workflows. Simple. And. Efficient. All. Right. This. Is. Going. To. Get. Fun. Amazing. (t: 1830) So. Vibe. Mo. My. Automated. Chief. Marketing. Officer. Now. Has. Tools. Right. It. Has. Three. Tools. Image. Video. Generator. Web. Research. (t: 1840) Specialist. (t: 1840) This. And. Then. Posted. On. X. This. Is. A. Very. Simple. Example. This. Is. Not. That. Complicated. (t: 1850) Of. An. Agent. Right. And. We. Did. Finish. This. Right. Here. We. Have. This. Chief. Marketing. Officer. Agent. That. Has. Access. To. Sub. Agents. And. These. Agents. Aren't. That. Hard. You. Could. Have. Had. One. Singular. (t: 1860) Agent. To. Do. All. Of. This. But. If. We. Had. Access. To. All. Of. My. Social. Media. Accounts. And. My. Notion. And. My. Buffer. Which. Is. The. Place. Where. I. Manage. Manually. Manage. All. Of. My. (t: 1870) Social. Media. Accounts. And. It. Could. Post. Thumbnail. Ideas. For. My. Video. As. Well. As. Research. For. My. Podcast. As. Well. As. Everything. Right. This. Will. Get. Bigger. And. Bigger. And. So. Vibemo. (t: 1880) Is. The. First. Iteration. Of. This. Agent. Right. This. Is. My. Chief. Marketing. Officer. And. I. Started. This. About. Two. Week. Two. Days. Ago. Where. It's. Claude. Code. Running. 24. (t: 1890) Hours. A. Day. And. So. It. (t: 1900) Is. A. Chief. Marketing. Officer. As. A. Co-Founder. I. Want. To. Spend. More. Time. Creating. Content. Because. I. Think. That. Is. (t: 1910) A. Higher. Leverage. Thing. For. Me. To. Be. Working. On. Instead. Of. Being. The. Chief. Marketing. Officer. And. Looking. At. Analytics. All. The. Time. I. Think. It's. A. Waste. Of. Time. At. The. Stage. Of. A. Startup. I. Just. Think. You. Can. (t: 1920) Get. A. I. To. Do. It. And. So. That's. What. We're. Going. To. Attempt. To. Do. So. Eventually. Vymo. Is. Going. To. Have. Hundreds. Of. Tools. But. Not. In. Its. Main. Agent. It's. Going. To. Have. Sub. Agents. That. It. Can. Delegate. (t: 1930) To. (t: 1940) Little. Short. Overview. Slash. (t: 1950) Entertaining. Video. To. Kind. Of. Get. Your. Juices. Flowing. Around. Agents. Right. How. Can. You. Get. An. Agent. To. Delegate. To. Smaller. Versions. Of. Itself. To. Accomplish. More. (t: 1960) And. When. You. Start. Thinking. About. Your. Business. Or. Your. Life. In. This. Way. Especially. Once. You. Get. It. On. (t: 1970) The. More. Time. You. Put. Up. Front. And. (t: 1980) The. More. Time. You. Spend. Paying. Really. Close. Attention. To. The. Tools. The. System. Instructions. And. The. Context. Window. Of. Your. Sub. Agents. As. Well. As. Your. Master. Agent. The. More. Leverage. You. Can. Get. From. These. (t: 1990) Activities. And. I. Didn't. Really. Go. Into. Like. (t: 2000) Practices. For. System. Instructions. (t: 2010) For. Both. Your. Master. Agent. And. Your. Sub. Agents. And. I'm. Really. Excited. To. Make. Future. Videos. And. Make. Vibe. Mo. The. Chief. Marketing. Officer. Better. And. So. That's. What. We're. Going. To. Do. (t: 2020) Make. Sure. To. Subscribe. And. Like. This. Video. And. I'll. See. You. Here. For. The.

