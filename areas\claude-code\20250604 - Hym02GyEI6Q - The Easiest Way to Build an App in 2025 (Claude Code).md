---
title: The Easiest Way to Build an App in 2025 (<PERSON>)
artist: The AI Advantage
date: 2025-06-04
url: https://www.youtube.com/watch?v=Hym02GyEI6Q
---

(t: 0) I just replaced my $30 a month invoicing software with something I literally built under 10 minutes without writing a single line of code. And at the end of this video, I'm confident that you will be able to do the same thing regardless of your technical experience. (t: 10) And I did all of this with the Gentic software Cloud Code that runs inside of a terminal window on my Mac. Now, if you find terminals scary, fair enough, but this tutorial is designed to (t: 20) lead you through this step by step so you too can turn your computer into a senior developer that listens to your commands in natural language, just like a developer you hire would. (t: 30) Let me show you how. This is free software from Anthropic and it's called Cloud Code. It runs inside of your terminal, which comes pre-installed on every Mac and Linux machine. You do not need a subscription just to use the service, although you can get one. And that makes (t: 40) it super easy to get started because you pay per usage. And this entire app cost me $2.97 to create. (t: 50) And unlike many tools on the internet, this comes without any ads and you own everything it creates. We're going to be using the example. of an invoice generator for the purpose of this tutorial. And that invoice generator is mine. (t: 60) I have a folder with it on my machine. I can customize it. I never have to pay anybody and I can use it forever. And I realized that the fact that this only works inside of a terminal might (t: 70) seem a little too scary to some people, but everybody who I taught this to was surprised by how simple it actually is in practice. But first for anybody not familiar, let me explain to you what the command line, also known as a terminal on a Mac actually is. It's a pre-installed (t: 80) application that looks like this. And you can think of it as this black window into which you can type commands instead of clicking buttons. You could really think of this as texting your (t: 90) computer instead of using a mouse. Developers use this all the time. And now you will too. At this point, I want to say a big thank you to Amphropic for partnering with me on this video. But if you (t: 100) follow the channel or the community, you will know that I've been using this tool since the day of release. And I also want to add that every prompt and result that you see in this video is always the first run. This hasn't been edited to fake the results. Just try my prompts and you should get (t: 110) the same results. Okay, but now let's get into this. So here's a simple setup in four steps. First of all, you need to install something. That's called Node.js on your machine. You can think of this as the engine behind cloud code, (t: 120) and you don't even need to understand what this really does. All you need to do is go into this link that as all others I'll put into this video's description. And then this automatically (t: 130) selects what system you're on. And all you need to do is download the installer down here and go through this step by step installation. Step one, complete. Step two, you're going to open up the (t: 140) terminal by looking for the terminal on a Mac. And then you're going to copy this exact command from this video's description. And then you're going to copy this exact command from this video's description. You just paste it in here with right click and paste or command V. And then all you do is (t: 150) hit enter once and it will install cloud code on your computer. Now once we have that you will be typing the most complicated command of today's video. You ready, you'll say cloud and then (t: 160) configure with a space in between the two. You can also copy that from the description if you want to. I'll hit enter again, and then it will ask you to link your Amphropic API key. As I said, this (t: 170) software is free to use but you do have to pay for every request that you make. So you can do that in the next step. So if you want to pay for every request that you make, you can do that in the next step. So if you want to pay for every request that you make, you can do that in the next step. So you can do that in the next step. And then you can also add a new folder to the folder that you send to the Amphropic API. In (t: 180) other words, you're going to be paying per usage. Now if you just want a subscription plan and Amphropic does have cloud Mac subscriptions that include cloud code. So it's really up to you want to pay per request or want to pay a fixed amount per month. So let me close up the terminal and just (t: 190) open up a finder window over here, you probably want the folder and what you will keep your projects. And this one I just created one called tutorial. And inside of that folder, the simplest (t: 200) workflow is for each project, you just want to create a new folder, I'll call this one invoice generator. And then when you right click that folder, you can go to services new terminal at folder. And in here, all you need to type to activate cloud code is the word cloud, hit enter. (t: 210) And now you're all set up and ready to build new projects from scratch without writing any code. (t: 220) And now I can tell it something like build me an invoice generator. When I hit enter cloud code is going to start by creating a plan of what we need for invoice generator, you can see it right here, these are the two do's. But I don't have to do (t: 230) anything with the exception of when cloud wants to create or edit new files, it does ask me for permission, do you want to create index.html, the file that I'll be opening with Google Chrome, (t: 240) I could just say yes, or I could press tool to say yes. And don't ask me again this session. So it just works by itself. I prefer doing that because then it becomes smoother. And there you go, cloud built the application on the four minutes. Here you can see all the details on what it did. (t: 250) So this is the to do list it completed. And here's some of the features it decided to include. And it even exactly tells me how to try this out. The application is now (t: 260) open in your browser. Okay, let me check. Yeah, it actually opened this window in my browser. And this is the first version of my custom invoice generator. If I just fill this out with test data (t: 270) and say download PDF, you'll see an invoice PDF that looks like this. Now this is obviously just a starting point for my project. So let me show you how to improve it according to your needs. Let (t: 280) me show you my favorite feature. But that is the fact that you can open it up again. And let's say I want to customize the app. And maybe in this case, I want to change the look of the invoice because I might already have one. Well, what you can do is (t: 290) give images to cloud code and it will work with them. So in this case, I just have a little screenshot of a mock invoice over here. I just screenshotted this off the internet because I like the simplicity of it. And all I will do is drag and drop it into the terminal here you can see image number one and (t: 300) I will say make the invoice look exactly like this and hit enter and now it will analyze the image (t: 310) and make all the changes that might be necessary. And there you go. Less than three minutes later, we have an updated version of the invoice generator. And I'll just fill (t: 320) this out with test data. And interestingly, it even created a signature field. And I see it even pre selected the tax at 10% because that's what the invoice here showed. So let's just download the PDF and let's review the result. And sure enough, that's pretty damn close to the screenshot (t: 330) I gave it even including a little preview here at the bottom. And that's really all there is to it. (t: 340) Now you could definitely extend this with more customizations. I think I personally would want to save my company name and save a few presets for invoices that I create regularly. And all I would need to do is just (t: 350) tell Claude code as I would tell a developer via email. Now to round out the video, I have a few more tips for you. The first one is that I found this to work best if you add one feature at a time. Additionally, it can really help to ask Claude codes to create a plan before you start building (t: 360) the app, especially if you're trying to create something larger. Another tip would be is that you could duplicate this invoice generator instead of second terminal windows say Claude and work on (t: 370) two versions of the feature at once and then you just go with the one that works better for you. And finally, I want to add that this also really works well if you have some old (t: 380) projects that you want Claude code to explain to you or if you work as a developer and you need to get familiar with brand new project, well, you just open up Claude code and ask it to guide you for the entire project. And it does that really, really well. But yeah, overall, if you ever had a software (t: 390) idea, it has never been easier to create an MVP of it. Or alternatively, you could take some online apps that you see or some spreadsheets that you have laying around, just give a screenshot of (t: 400) those to Claude code and tell it to recreate it. Outside of that, I just want to show you that if you type slash in the terminal, you have various commands here that might be helpful. My most (t: 410) used one is slash compact, which shrinks the conversation if it ever gets too long. But Claude code will warn you if that happens and even recommend this command. And then just to round everything out, let's type slash cost and see how much it's actually cost me this invoice generator (t: 420) took six minutes to generate and cost me $2.97. Impressive if you ask me, and that's all it takes (t: 430) to build your own bespoke applications. In the description of this video, you'll find a few more prompts for starter projects like this that you could try yourself right away. So go and try out (t: 440) Claude code and see how it works. And if you want to see how it works, you can go to the link in the description below. And if you enjoyed sign up for the Claude Mac subscription, which allows you to build many applications like this without you having to worry about the cost every time you add a new feature. That's everything I have for today. In case you want to see me build out the (t: 450) larger project with Claude code, you can check out this video and I hope you have a wonderful day.

