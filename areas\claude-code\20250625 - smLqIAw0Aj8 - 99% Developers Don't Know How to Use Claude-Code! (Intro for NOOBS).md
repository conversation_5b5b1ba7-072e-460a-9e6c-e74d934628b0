---
title: "99% Developers Don't Know How to Use Claude-Code! (Intro for NOOBS)"
artist: <PERSON> in USA
date: 2025-06-25
url: https://www.youtube.com/watch?v=smLqIAw0Aj8
---

(t: 0) Today as a software developer, iOS developer, Android developer or even web developer, I have achieved something with my skills with agentic abilities which I never thought I would (t: 10) be able to achieve. After Claude, 4 has been released by Anthropic. It has changed coding forever. Agents like Cursor, Windsor, VS Code, they cannot compete with this anymore. This is (t: 20) something which has to be mainstream and used by all of you as soon as possible so that you don't (t: 30) lag behind. If you look at the second wave of layoffs coming at Microsoft, Google, Meta, you need to be ready and you need to combine your skills with AI agentic flows like the ones I'm (t: 40) going to show you right now because this is the app that I have made. Today is not the day when I'm going to show you just these examples of games like Mario being made with <PERSON> or these AI (t: 50) agents. Today is the day. day when I show you an enterprise level shipped app which is going to make you money. This is (t: 60) a paid app I'm going to show you right now it's a screenshot app in Mac called Shorter. So let's say I take a screenshot of this with one click it shows this layout I can add gradient I can change colors customizable I can draw on it and this is $12 but you will be blown away that this (t: 70) app can be created in just a few minutes at your fingertips thanks to the abilities powered by (t: 80) cloud 4 opus and cloud 4 sonnet. So let me show you what I've already made I'm going to first show you the app I'm going to run it on my xcode I'm going to show you the same app on my computer that (t: 90) I made just now in five minutes so here's the app that I made I can I can click on full screen or windows screenshot let's say click on full screen and then I can click on enhance I can get the same (t: 100) features rounded corners drop shadow and even a gradient effect I can customize the gradient (t: 110) and have whatever colors I want to and make it beautiful and voila the same app is ready within five minutes of course (t: 120) you can add more features and make it right now so so any paid app that you want for your macbook (t: 130) can be created within few minutes that's what I want to show you today now first of all what is cloud code why so many people are switching from like agents like cursor vs code to cloud code the (t: 140) best way to do this is by using a lot of software like openai openai is a software that actually can do work independently and it is going to be always always always better than vs code cursor do you (t: 150) know why because those ids compress and then send data to llms like cloud or like any other (t: 160) competitive uh llms but cloud is best in software engineering okay look at the benchmarks it is best in coding as compared to gemini openai is 4.1 and don't get confused they have two models sonnet 4 (t: 170) they have a lot of different models they have different types of data maybe bigger context window maybe it's more information but for most of your tasks with cloud code sonnet 4 is enough (t: 180) and that's what i'm going to use today with the cloud 4 subscription you get sonnet 4 included in that 20 subscription so we know that it is the best model out there for coding and this is the (t: 190) reason i have switched to cloud code as well for the last few months i've just been using cloud code no other id and cloud 4 is like having a world-class consultant with you at all times i'm (t: 200) going to show you all the tricks you really need to know and finally an ai capable of tackling complex projects that you have been avoiding of course there have been talks that oh you can make (t: 210) games with ai but there haven't been talks that an ai which can build test ship and do all in one and even push it to code with mcb i'll show you all of the powerful features of this agent asap (t: 220) to get started you need to just run this command for that make sure you have node running in your (t: 230) terminal so i'm gonna start with the code and then i'm going to show you how to do it in the next video so let's start with this command open terminal uh i have cloud already so what i'm gonna do is first sign out sign log out oh it suggests so log out tab enter so i have logged out and all you have (t: 240) to do to get started is run this command i have already installed it so it won't do anything (t: 250) and i have npm packages installed and now once you have installed using this command you can just do cloud and it will show up this window and we can set it up (t: 260) by choosing dark mode light mode i think this high contrast mode is my favorite enter and now you have two options number one with api number two cloud pro at twenty dollars per month (t: 270) or hundred dollars cloud mac so with the max plan you get opus cloud opus but for most of the people the limits that you have with cloud pro is enough for beginners but of course you have (t: 280) lot of tons of tons of programming to be done you may need opus as well and with the max subscription (t: 290) you can use api based billing for that and this api cost that people were complaining that it's very expensive you know this was more expensive than other agents do you know why because other agents compress as you mentioned they do not give you best outputs because they are mastered at (t: 300) compressing you know tons of files or coding files you share and this it doesn't give you a best answer in one go but this is the best agent i found in the first attempt sometimes it codes the (t: 310) best possible app alright so i'm gonna choose my subscription open the browser i'm gonna authorize i (t: 320) Pro so now let's build something great and login was successful and enter and now you want to use recommended settings I always choose recommended settings and here comes the magic now before (t: 330) starting a lot of people feel overwhelmed with this terminal because they are not comfortable using terminal I will say become friend with terminal but if you don't like terminal you can (t: 340) also open like windsurf cursor and open terminal there so let's open terminal here here is terminal (t: 350) and you can enter cloud here when you close that window you will always see cloud code handy on top right corner which you can just access instead of opening terminal you can just open cloud code (t: 360) immediately right there and windsurf cursor vs code whatever you're using but I'm not going to be using it because I want you to be comfortable with cloud code directly now let's get started (t: 370) with coding the app we're going to build right now once again it's called shorter you can read about it it's a screenshot app and with more features like the gradient effect I showed you (t: 380) so let's get started with coding the app we're going to build right now once again it's called shorter you can read about it it's called shorter you can read about it it's called so I'm going to make the paid version of it which is for twelve dollars as you can see so right now to get started I recommend first of all you should create a hello world workable project and of (t: 390) course ask it make a hello world ios app and create and test it but I will say for ios development (t: 400) at least the best output you get is by opening xcode create a new project so because I'm making ios app you can create web app whatever you want to file new (t: 410) project and then we're going to create a mac app which is cross platform and then we can put screenshot app video the one i make in the video so i'm going to click that enter and voila so this (t: 420) app is hello world ready so i can click the run button and then you will see hello world shown (t: 430) on the screen so basically hello world is ready now what you can do is before running cloud code (t: 440) what you can do is go to cd into developer so let's exit this window and now open the folder where you want to be in so i'm going to go to my developer folder i keep all my projects coding (t: 450) projects in the developer folder this folder now what we're going to do is open cloud window here (t: 460) by entering cloud and enter so here is the bubblezel that i have created and i'm going to show you how it works so go to we will of course trust the project and this window is ready for cloud now i have a prompt (t: 470) i have created not myself but using ai so what you can do is i create prompt using ai so i say cloud write a good prompt for cloud code to build screenshot app like this is actually called (t: 490) reverse engineering then engineering and re-engineering that's how i code so i don't even write prompts from scratch so right and then uh with features like gradient background so i'm (t: 500) just mentioning one feature but it will figure out and fetch information from this screenshot app (t: 510) called shorter oh i forgot to mention shorter here so let me ask shorter as well app (t: 520) like shorter with robust features so now it will give me the prompt we will edit it here is a (t: 530) robust prompt and we're going to do changes into it of course so let's copy it and open notes i'm open notes and in my notes right here i'm going to modify this prompt so create a professional (t: 540) screenshot ios app (t: 550) and and editing application with the following features uh in xcode we need to tell what it's using in xcode using swift ui or the current project structure if you don't know if the project is using swift ui (t: 560) whatever it is you can just type current project structure and features would be capture screenshots (t: 570) and then uh and we can say using a button for now for simplicity we will (t: 580) it as a shortcut do it with button instead of a shortcut and keyboard shortcuts we can skip for now we will add it later we have to to make make it simple so as simple as possible Autosave let's skip that feature and editor we (t: 590) will just put gradient,ff rounded corners one ch backdrop shadow border padding these features are good these are the features i showed you in the intro as (t: 600) well and arrow lines we can add it let's skip these for now and then export options yeah in Png Jpg is good so this is a solid environment as we can really make things easier if we need it (t: 610) to manga So this is a solid prompt we created using AI. This is basically reverse engineering. Now we will engineer. Now as I have copied this prompt, it is ready. (t: 620) I'm going to paste it in my cloud code and hit enter. Now this will do the magic. It will code independently. (t: 630) By the way, you can do shift tab to auto accept edits, shift tab again, plan mode and shift tab again into normal mode. So basically if you want it to not ask you that, Oh, is it okay to create this file? (t: 640) Is it okay to make this change? You can do shift tab tab and or shift tab once and choose what setting you like. (t: 650) Right now it has created the structure and it is asking me yes or no. So I can, I can, you know, put shift tab and make it not ask me. So here is the plan. (t: 660) It is going to update app permissions, code architecture. It has planned editing state, UI screenshot capture. It has planned everything into five pieces and you can edit these pieces if you want (t: 670) to, but I approve it. I'm going to hit enter. Here are the six to do's it has created and I think all are good and it has already created (t: 680) the first file. It is spinning first done second task. Now let me create the screenshot capture service. (t: 690) It's going to create a service. It is just mind blowing. I love reading it. This is what people call reasoning. This is called extended thinking. This is called cloud or you can call it chain of thought, whatever you want to call it. (t: 700) Basically whatever this AI agent is thinking, it is telling you so that you can actually plan like a senior developer as well. (t: 710) Your goal is not to just say yes, yes, read what is doing, understand. And that's why I never enable like, you know, Yolo mode. So you can, you can do shift tab and twice to have like, you know, Yolo mode, which is (t: 720) like, it'll accept everything. I have not done that because I want to read. I love reading. I can be faster, but I'm okay to compromise on speed. (t: 730) Okay. This is the file and I'm going to say yes. I could also do yes and do not ask for this session. Let me just do that for now. But your goal is to actually, your goal is actually to learn and see how a senior developer (t: 740) is thinking here. Okay. Three steps done. Now it's going to go for the fourth one grad gradient background with view presets. (t: 750) It's so exciting. And here you can see the number of tokens it is using for philosophizing. Okay. 20,000, actually 11,000 tokens being used here. (t: 760) 81 seconds. Of course, reading apps is a big deal, but it is still really fast within five to 10 minutes. (t: 770) We are going to be done while this agent is running. I want to remind you why every developer need to switch to this cloud code. Number one reason is speed to market. Every startup will tell you that they have an advantage because they go fast as compared (t: 780) to any big tech. Same with you having agents. You can be faster than any of your competition. Any of your competitor developer, any of your friends who are working on projects, (t: 790) which are better than you with AI agents, you can actually have projects like these, put them in resume and mention that I am making apps, which make revenue like thousands of (t: 800) dollars made by this shorter app. You're making apps, which make money in few minutes shipping fast. And this is something that makes you unique. (t: 810) Number two is quality at scale. Quality scale is not just fast code. It is actually production ready. Enterprise quality. Okay. So you can make a lot of quality code, which you get with extended thinking. (t: 820) No other, you know, no other platform I've used. I'll be honest. I even use cloud for translating. I have subtitles on many of my videos, which are just AI generated. (t: 830) I copy the Hindi ones because cloud is not just good at coding. It's good at everything related to writing, writing code, writing English. And it's so creative. (t: 840) One of the most creative piece of writing, including code I've seen with cloud. And number three reason is RDI. Cloud has introduced artifacts. Since cloud has introduced artifacts, I don't, you know, send like Excel sheet word documents (t: 850) to my friends. I make website and send to them. Look at this website. I showed you in my previous video, I created it for my China trip, whether I should go (t: 860) from one place to another, through train or flight from one city to another, it figures out everything, puts all the options in a cool website and shows it to me beautifully, (t: 870) which I can send it to my friend. And then I can send it to my friends. He shares the feedback. I can write the prompt and tell it that, Oh, I think we should have another city as a choice. (t: 880) And it can show it in the whole map. It is so beautiful and so powerful. I was blown away. (t: 890) And number four, my favorite, I have this email. I'm going to show you my newsletter email. So in this email, I get a lot of newsletters. I follow wall street journal, the information, New York times. (t: 900) I don't have times to read emails. Sometimes I have like these, this clutter. What I do is I have a lot of emails. What I do is I go to my cloud and then what I do is I can just click on connect apps. (t: 910) My Gmail is connected here and with Gmail connected, I can just type it and it will (t: 920) summarize all the emails heading by heading, make a website out of it. And I can catch up with all my hundreds of emails, which are unread. (t: 930) This is example for newsletter, whether you have emails from, you know, your class, your friends, your friends, your friends, your friends, your friends, your friends, your friends, your friends, your friends, your friends, your friends, your friends, your friends, your friends, your friends, your friends, your friends, your friends, my friends, you friends, your friends tools. Sometimes you can automate and see everything in one go, sometimes you have 200 emails. When you are on vacation, you can see all of that in one click on a button. (t: 940) The, this makes Claude the best LLM and the only subscription I pay for. Iアーс now (t: 950) let's get back to coding because I said yes to not ask me again. It is only asking me. Yes. To build the project. So since it is anSON Rouge project, it is going to build using Xcode build command. (t: 960) It is going to build it in terminal. I don't even have to hit the done button and pick it up, my 300? My treatments. MyJust going to make sure. Since it is an Oncomer Air北, My Just. if it is working. It's going to build it and then actually go from there. So let's (t: 970) we hit yes it'll see errors and fix them for me as well. So it is calling this term unfurling it saw some errors okay now it's gonna go ahead decide how to (t: 980) fix the errors and fix them. It uses it used the deprecated window accidentally let me fix that. So beautiful. Another failure it is common I can actually next (t: 990) time if there are multiple failures I can just use auto accept everything. So I told you right shift tab tab you can auto accept all the changes and just go (t: 1000) drink coffee and come back but I wanted to learn that's why I am showing you everything. Now here is a quick overview complete screenshot editing application (t: 1010) core features delivered it's actually writing just like a software developer (t: 1020) should write to a product manager. You know it's so beautiful and successfully compiles with only one minor deprecation warning. If you want you can even ask it to fix that warning. Now let's open Xcode (t: 1030) and now let's run the app replace. Wow it looks different than the previous app I (t: 1040) had made. Remember that this is something I've made from scratch without touching (t: 1050) a single line of code on Xcode. Just remember I can call it a single line of code and it will just copy it and paste it into the code. So I can just click on full screen window selection. Let's do window this time take screenshot. I need to give permissions last time I had already given. Okay now it has taken (t: 1060) a screenshot of this Xcode window. It is enlarged there are some changes to be done. There are so many gradient options. I can add padding. Let's make it smaller. (t: 1070) Let's add some colors. Okay fire theme whatever theme (t: 1080) you like you can add and then export as JPG, PNG. Of course it has it has really big size. (t: 1090) So what we have to do is what I will do is I'll go to cloud code say that the screenshot looks too big (t: 1101) when I edit. So fix that. So yeah it'll fix that and (t: 1110) we will be able to customize and it'll work just like the app I showed you before. Now this is not (t: 1120) it by the way. This is not how you should end coding. You can even automate further go one more step by connecting a MCB. I'm going to show you this magical MCB. What I did was I search on (t: 1130) the AI. I search on cloud how to connect MCB on this terminal and now when I have MCB connected (t: 1140) all I can do is push this repo. Thanks to MCB connected all I can do is commit and push with a readme file just that. Now it'll (t: 1150) create a readme file commit with a good commit message and push it for me as well and I'll show you the change while it is doing it. It is doing these four steps. I can show you my repo github.com (t: 1160) I can show you that this app right now has no readme. Okay now it is coding (t: 1170) and within few seconds you will see readme. It's mind-blowing without writing git commit git push and (t: 1180) I didn't do in like you know automatic mode so I have to say yes and I can do yes and don't ask me this question again and it should do all of it. Now it has done the step one create readme (t: 1190) commit changes it is working on it. Okay git commit message it has typed and (t: 1200) yes again okay there was a merge conflict by the way it is automatically resolving that (t: 1210) merge conflict as well just imagine it is pushing to my screenshot app it has some errors now let's (t: 1220) go refresh oh wow it is here within like within few seconds it was added and you can see contributors are Hanoor and Claude and we have a readme in few seconds that's the power of MCB. (t: 1230) And not just this you can hear compact this conversation it will summarize what we have discussed and it will clear the conversation so I'm using slash compact since we have compacted (t: 1240) the information seen the summary as well this command slash compact is very useful to see the summary what you have done you can also do something like shift tab and plan mode in (t: 1250) this plan mode you can ask Claude what new features should I add it's just like a chat it's not coding (t: 1260) chatting with the LLM and taking suggestions or explanations here and there this is also very very cool and now it is go through the repo and tell me what you know new features I can add (t: 1270) so these are the suggestions and some features it would like me to have to make this app better (t: 1280) than the competitor OCR QR code detection screenshot comparison tool automatic duplicate detection and it can code all of these as well the default way you should code is (t: 1290) make this change and push it to GitHub or commit immediately instead of typing this so that you (t: 1300) don't have to say push in every single command what you can do is you can do in it in it the repo and it'll create Claude.md in the Claude.md file you can find examples online you can mention (t: 1310) that every single time a prompt is asked it commits and pushes so what happens is when you're using AI to code if code breaks if there's a time the code is messy you can go (t: 1320) back to the previous comment and use AI to code again that is the best way to handle because as you use AI intensively you know going back becomes hard the easier way is to you know go back four (t: 1330) commits delete the rest and code again so that's why committing and saving your progress on GitHub (t: 1340) is important now here's the harsh truth you really need to know you have two options right now either to adapt this or get left behind because those who are adopting in their workflow are going to (t: 1351) be exhaustive or you have got to adapt this Sendメール and Korh start saving storage to continue developing your content remember that this is 24 hour kat **** and although (t: 1360) it's not even complicated to Opt kalau avation and you can ears can easily be revealed to you the information you're telling you we talked about the 100K now software engineers (t: 1370) life it can be only survival for you all you switch. Color code. Only kommenом again Mi Mongke sol law garcktek logo digital maap neem volt cro gati one character acumalar Tata malauka

