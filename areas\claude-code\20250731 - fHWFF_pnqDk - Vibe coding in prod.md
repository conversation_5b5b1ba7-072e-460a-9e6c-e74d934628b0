---
title: Vibe coding in prod
artist: Anthropic
date: 2025-07-31
url: https://www.youtube.com/watch?v=fHWFF_pnqDk
---

(t: 0) Hey everyone, welcome. I'm here to talk about everyone's favorite subject, vibe coding, and somewhat controversially, (t: 10) how to vibe code in prod responsibly. So let's talk about vibe coding and what this even is. (t: 20) So first of all, I'm <PERSON>. I'm a researcher at Anthropic focused on coding agents. I was the author along with <PERSON> of building effective agents where we outlined (t: 30) for all of you our best science and best practices for creating agents no matter what the application (t: 40) is. This is a subject that's near and dear to my heart. Last year I actually broke my hand while biking to work and was in a cast for two months and (t: 50) <PERSON> wrote all of my code for those two months. And so figuring out how to make this happen effectively was really important to me and I was luckily able to figure it out. I was able to figure that out well and sort of help bring that into a lot of Anthropic's (t: 60) other products and in our models through my research. So let's first start talking about what is vibe coding. (t: 70) A lot of people really conflate vibe coding with just extensive use of AI to generate your code. But I think this isn't quite true. (t: 80) A lot of people, you know, they're using cursor, they're using copilot, it's a lot of AI and a lot of the code is coming from the AI rather than them. But I think when you are still in a tight feedback loop with a model like that, that (t: 90) isn't truly vibe coding. When I say vibe coding, I think we need to go to Andrej Karpathy's definition where vibe (t: 100) coding is where you fully give in to the vibes, embrace exponentials, and forget that the code even exists. (t: 110) I think the key part here is forget the code even exists. And now the reason this is important is that vibe coding was when the code was not even there. So if you're using a code that's not even there, you're not even using the code that's there. So that's the key part. And it was really important for me to think about this. (t: 120) I think it's really important to think about this. And I think it's really important for people outside of the engineering industry really started getting excited about code generation. Copilot and cursor were great, but only sort of for engineers. But someone that didn't know how to code, suddenly with vibe coding, they could find (t: 130) themselves coding an entire app by themselves. And this was a really exciting thing and a big unlock to a lot of people. (t: 140) Now, of course, there were a lot of downsides of this. And you had people coding for the first time. And really. Without knowing what they were doing at all. (t: 150) And you said, hey, random things are happening. Maxed out usage on my API keys. People are bypassing the subscription, creating random shit on the DB. (t: 160) And so this is kind of the downside of vibe coding of what started happening. And the positive sides of vibe coding that you'd see were all things that were really (t: 170) kind of low stakes. It was people building video games, building sort of fun side projects. Things where... Yeah. It's okay if there is a bug. (t: 180) So why do we even care about vibe coding if it seems like something where the stakes are really high if you do it for a real product and the most successful cases of it are kind (t: 190) of these toy examples or fun things where the stakes are very low? And my answer for why we should care about vibe coding is because of the exponential. (t: 200) The length of tasks that AI can do is doubling every seven months. Right now, we're at about an hour. (t: 210) And that's fine. You don't need to vibe code. You can have cursor work for you. You can have Claude code write a feature that would take an hour. (t: 220) And you can review all that code and you can still be intimately involved as the AI is writing a lot of your code. But what happens next year? (t: 230) What happens the year after that? When the AI is powerful enough that it can be generating an entire day's worth of work for you at a time? Yeah. Or an entire week's worth of work. (t: 240) There is no way that we're going to be able to keep up with that if we still need to move in lock step. And that means that if we want to take advantage of this exponential, we are going to have to find a way (t: 250) to responsibly give into this and find some way to leverage this task. I think my favorite analogy here is like compilers. (t: 260) I'm sure in the early day of compilers, a lot of developers, you know, really didn't trust them. They might use a compiler, but they'd still read the assembly that it would output to make sure it looks, you know, how they would write the assembly. (t: 270) But that just doesn't scale. You know, at a certain point, you start needing to work on systems that are big enough that you just have to trust the system. (t: 280) The question, though, is how do you do that responsibly? And I think sort of my challenge to the whole software industry over the next few years is how will we vibe code in prod and do it safely? (t: 290) And my answer to that is that we're going to have to do it. So I think part of the reason that I think it's important for us to do this, that we're going to be a little bit more flexible, (t: 300) is that we will forget that the code exists, but not that the product exists. (t: 310) Thinking again to that compiler analogy, you know, we all still know that there is assembly under the hood, but hopefully most of us don't need to really think about what the assembly actually is. (t: 320) But we still, you know, are able to build good software without understanding that assembly under the hood. And I think that we will get to that same level with software. And one thing I really want to emphasize is that this is not a new technology, because it's not a new technology. It's a new technology. It's a new technology. problem. How does a CTO manage an expert in a domain where the CTO is not themselves an (t: 330) expert? How does a PM review an engineering feature when they themselves can't read all (t: 340) the code that went into it? Or how does a CEO check the accountant's work when they themselves are not an expert in financial accounting? And these are all, you know, problems (t: 350) that have existed for hundreds or thousands of years, and we have solutions to them. A CTO can still write acceptance tests for an expert that works for them, even if they don't understand (t: 360) the implementation under the hood. They can see that these acceptance tests pass and that the work is high quality. A product manager can use the product that their engineering team built (t: 370) and make sure that it works the way they expected, even if they're not writing the code. And a CEO can spot check, (t: 380) can you see that the product is working? And if they're not writing the code, they can spot check, can you see that the product is working? And if they're not writing the code, can you see that the product is working? And if they're not writing the code, can you spot check, can you see that product is not writing the code, can you see that the product is working? And if a CEO is not writing the code, can you see that the product is not working, can you see that the product is working, can you see that the product is not working, can you see that the product is working, can you use this or that? Can you see that the product is working, can you use the version you want to put into (t: 390) the product, can you use this or that? What might you do? What because if you are just an engineer, then you're Macs, you know how to handle a EMJинг or a filernge and how to use theaturc default mechanism. (t: 400) Don't know how that works. But basically we are managing the data, as old as civilization. And every manager in the world is actually (t: 410) already dealing with this. Just we as software engineers are not used to this. We are used to being purely individual contributors, where we understand the full depth down to the stack. (t: 420) But that's something that in order to become most productive, we are going to need to let go of in the way that every manager, in order to be most productive, (t: 430) is going to need to let go of some details. And just like us as software engineers, we let go of some of the details of understanding the assembly itself that's happening under the hood. (t: 440) And the way that you do this while still being safe and being responsible is to find an abstraction layer (t: 450) that you can verify, even without knowing the implementation underneath it. Now, I have one caveat to that today, which is tech debt. (t: 460) So right now, there is not a good way to measure or validate tech debt without reading the code yourself. (t: 470) Most other systems in life, like the accountant example, the PM, you have ways to verify the things you care about without knowing the implementation. (t: 480) Tech debt, I think, is one of those rare things where there really isn't a good way to validate it other than being an expert in the implementation itself. So that is the one thing that right now we do not (t: 490) have a good way to validate. However, we are going to need to do this. However, that doesn't mean that we can't do this at all. It just means we need to be very smart and targeted of where (t: 500) we can take advantage of IVE coding. My answer to this is to focus on leaf nodes in our code base. And what I mean by that is parts of the code (t: 510) and parts of our system that nothing depends on them. They are kind of the end feature. They're the end bell or whistle, rather than things (t: 520) that are the branch or trunks beneath them. Like here in white. Here the orange dots are all these leaf nodes where, honestly, if you have a system like this, (t: 530) it's kind of OK if there is tech debt in these leaf nodes because nothing else depends on them. They're unlikely to change. They're unlikely to have further things built on them. (t: 540) Versus the things that are in white here, the trunks and the underlying branches of your system, that is the core architecture that we as engineers still (t: 550) need to deeply understand because that's what's going to change. That's what other things are going to be built on. And it's very important that we protect those and make sure that they stay extensible and understandable and flexible. (t: 560) Now the one thing I will say here is that the models are getting better all the time. And so we might get to a world where this gets further and further down, where (t: 570) we trust the models more and more to write code that is extensible and doesn't have tech debt. Using the Claude 4 models over the last week or two (t: 580) within Anthropic has been a really exciting thing. And I've given them much more trust than I did with 3.7. So I think that this is going to change and more and more (t: 590) of the stack we will be able to work with in this way. So let's talk about how to succeed at vibe coding. And my main advice here is ask not what Claude can do for you, (t: 600) but what you can do for Claude. I think when you're vibe coding, you are basically acting as a product manager for Claude. (t: 610) So you need to think like a product manager. What guidance or context would a new employee on your team need to succeed at this task? I think a lot of times we're too used to doing sort (t: 620) of a very quick back and forth chat with AI of make this feature, fix this bug. But a human, if it was their first day on the job and you just said, hey, implement this feature, (t: 630) there's no way you'd expect them to actually succeed at that. You need to give them a tour of the code base. You need to tell them what are the actual requirements (t: 640) and specifications and constraints that they need to understand. And I think that as we vibe code, that becomes our responsibility to feed that information into Claude to make sure that it has all of that same context (t: 650) and is set up to succeed. When I'm working on features with Claude, I often spend 15 or 20 minutes collecting guidance (t: 660) into a single prompt and then let Claude cook after that. And that 15 or 20 minutes isn't just me writing the prompt by hand. (t: 670) This is often a separate conversation where I'm talking back and forth with Claude. It's exploring the code base. It's looking for files. We're building a plan together that captures the essence of what I want, (t: 680) what files are going to be changed, what patterns in the code base should it follow. And once I have that artifact, all of that information, (t: 690) then I give it to Claude, either in a new context or say, hey, let's go execute this plan. And I've typically seen once I put that effort into collecting all that information, (t: 700) Claude has a very, very high success rate. And he's a very good example of being able to complete something in a very good way. And the other thing I'll say here is that you need to be able to ask the right questions. (t: 710) And despite the title of my talk, I don't think that vibe coding and prod is for everybody. I don't think that people that are fully non-technical (t: 720) should go and try to build a business fully from scratch. I think that is dangerous because they're not able to ask the right questions. They're not able to be an effective product (t: 730) manager for Claude when they do that. And so they're not going to succeed. We recently merged a 22,000 line change (t: 740) to our production reinforcement learning code base that was written heavily by Claude. So how on earth did we do this responsibly? And yes, this is the actual screenshot of the diff (t: 750) from GitHub for the PR. The first thing is we asked what we could do for Claude. This wasn't just a single prompt that we then merged. (t: 760) There was still days. There were days of human work that went into this, of coming up with the requirements, guiding Claude, and figuring out what the system should be. (t: 770) And we really, really embraced our roles as the product manager for Claude in this feature. The change was largely concentrated in leaf nodes in our code base, where (t: 780) we knew it was OK for there to be some tech debt, because we didn't expect these parts of the code base to need to change in the near future. And the parts of it that we did think were important that would (t: 790) need to be extensible, we did heavy human review of those parts. And lastly, we carefully designed stress tests (t: 800) for stability. And we designed the whole system so that it would have very easily human verifiable inputs and outputs. (t: 810) And what that let us do, these last two pieces, is it let us create these verifiable checkpoints so that we could make sure that this was correct, even without understanding or reading (t: 820) the full underlying implementation. Our biggest concern was stability. And we were able to measure that, even without reading the code, by creating these stress tests (t: 830) and running them for long durations. And we were able to verify correctness based on the input and outputs of the system that we designed it to have. So basically, we designed this system (t: 840) to be understandable and verifiable, even without us reading all the code. And so ultimately, by combining those things, (t: 850) we were able to become just as confident, as confident in this change as any other change that we made to our code base, but deliver it in sort of a tiny fraction of the time and effort that it would have taken to write this entire thing (t: 860) from hand by hand and review sort of every line of it. And I think one of the really exciting things about this is not just that this saved us a week's worth of human time, (t: 870) but knowing that we could do this, it made us think differently about our engineering, (t: 880) about what we could do. And now suddenly, when something costs one day of time instead of two weeks, you realize that you can go and make (t: 890) much bigger features and much bigger changes. Sort of like the marginal cost of software is lower, and it lets you consume and build more software. (t: 900) So I think that was the really exciting thing about this, is not just saving the time, but now kind of feeling like, oh, things that are going to take two weeks, let's just do them. (t: 910) It's only going to take a day. It's going to take a day. It's going to take a day. It's going to take a day. It's going to take a day. It's going to take a day. It's going to take a day. It's going to take a day. It's going to take a day. And that's kind of the exciting thing here. So to leave you with the closing thoughts about how to vibe code in prod responsibly, be Claude's PM. (t: 920) Ask not what Claude can do for you, but what you can do for Claude. Focus your vibe coding on the leaf nodes, not the core architecture and underlying systems, (t: 930) so that if there is tech debt, it's contained and it's not in important areas. Think about verifiability and how you can know, (t: 940) whether this change is correct without needing to go read the code yourself. And finally, remember the exponential. It's OK today if you don't vibe code. (t: 950) But in a year or two, it's going to be a huge, huge disadvantage if you yourself are demanding that you read every single line (t: 960) of code or write every single line of code. You're going to not be able to take advantage of the newest wave of models that are able to produce very, very large chunks of work for you. And you are going to become the bottleneck. (t: 970) If we don't get good at this. So overall, that is vibe coding and prod responsibly. And I think this is going to become one of the biggest challenges for the software engineering industry (t: 980) over the next few years. Thank you. And I have plenty of time for questions. (t: 990) In the past, we spent a lot of time dealing with syntax problems or libraries or connections amongst components of the code. And that was how we learned, by coding like that. All right. (t: 1000) But how do we learn now? How do we become better vibe coders? How do we know more to become better product managers of the agent KI? (t: 1010) Yeah. So I think this is a really interesting question. And I think there are reasons to be very worried about this and also reasons to be very optimistic about this. (t: 1020) I think the reason to be worried, like you mentioned, is that we are not going to be there in the struggle and the grind. I think that that is a challenge. (t: 1030) I think that that is a challenge. I think that that is a challenge. I think that that is a challenge. I think that that is a challenge. I think that that is a challenge. And so, like, the question is, how do we make things more effective, how do we make things more efficient, more efficient, and more efficient? (t: 1040) And that is a really good question. I think that the problem is actually OK. I've met some of my professors in college would say, like, oh, man, like, coders today aren't as good (t: 1050) because they never had to write their assembly by hand. They don't really feel the pain of how to make something run really fast. I think the positive side of this (t: 1060) Tell me about it. Like what is it? Why did you choose it over another and having sort of that always there pair programmer? Like again, I think what what's gonna change is that people that are lazy are not gonna learn (t: 1070) They're just gonna glide by but if you take the time and you want to learn There's all these amazing resources and like Claude will help you understand what it vibe coded for you (t: 1080) The other thing I will say is that for learning some of these higher level things about what makes a project go well (t: 1090) What is a feature that gets you product market fit versus flops? We're gonna be able to take so many more shots on goal I feel like especially sort of like system engineers or architects over it takes (t: 1100) you know oftentimes like two years to like make a big change in a code base and really kind of come to terms with was that a good architecture decision or not and (t: 1110) If we can collapse that time down to six months I think engineers that are investing in their own time and trying to learn they're gonna be able to (t: 1120) You know learn from four times as many lessons in the same amount of calendar time as long as they're putting in the effort To try yeah going back to your pre planning process (t: 1130) What's the balance between giving it too much information and too little are you giving it a full product requirement document? Is there kind of a standardized template that you put together before you actually move into vibe coding? (t: 1140) Yeah, I think it depends a lot on what you care about I would say that if it ranges for there's four things where (t: 1150) I don't really care how it does it I won't talk at all about the implementation details I'll just say these are my requirements like this is what I want at the end There's other times where I know the code base well, and I will go into much more depth of like hey (t: 1160) These are the classes you should use to implement this logic look at this example of a similar feature (t: 1170) I'd say it all comes down to sort of what you care about at the end of the day I would say though that like our models do best when you don't over constrain them so (t: 1180) You know if you I wouldn't put too much effort into creating sort of a very rigorous You know format or anything. I would just you know think about it as like a junior engineer (t: 1190) What you would give them in order to succeed? (t: 1193) So sorry if I'm too loud How did you balance (t: 1200) effectiveness and Cybersecurity like there were reports a couple months back of like the top 10 vibe coded apps being (t: 1210) Vulnerable and a lot of important information was released well Not released but proven to be released and the person who did it wasn't even like Like a pro hacker and stuff and so like there's that how did you? (t: 1220) Balance being able to keep things secure even at a leaf node level And then also being effective because something can be effective but not secure yeah, that's a great question (t: 1230) and I think that all comes down to this first point here of like being Claude's PM and and understanding enough about the context (t: 1240) to basically know what is dangerous, know what's safe, and know where you should be careful. And I think, yeah, the things that get a lot of press (t: 1250) about vibe coding are people that have no business coding at all doing these. And that's fine, that's great for games, that's great for creativity and having people be able to create. (t: 1260) But I think for production systems, you need to know enough about what questions to ask to guide Claude in the right direction. And for our internal case of this example, (t: 1270) it was something that's fully offline. And so we knew there weren't any, there were, we were very, very confident that there was no security problems (t: 1280) that could happen into this. In our case, it's run in something that's fully offline. (t: 1284) So this is more about people you're mentioning (t: 1290) as have no business. And maybe I shouldn't have said it like that. No business vibe coding in production for an important system. I will say that. Yeah. But if we look at the numbers, right, (t: 1300) less than 0.5% of the world's population are software developers. And software is an amazing way to scale ideas. (t: 1310) So how do you think the products need to change to make it easier for people to vibe code and build software (t: 1320) while also avoiding some of the things that we run into with people leaking API keys and things like that? That's a really great question. And I would be super excited to see more products and frameworks (t: 1330) emerge that are kind of like provably correct. And maybe what I mean by that is I'm sure people could build some back end systems (t: 1340) that the important off parts, the payment parts, are built for you. And all you have to do is sort of fill in the UI layer. (t: 1350) And you can vibe code that. And it basically gives you some nice fill in the blank sandbox is where to put your code. So I think that's a really great question. And I feel like there's tons of things like that (t: 1360) that could exist. And maybe the simplest example is like Cloud Artifacts, where Cloud can help you write code that gets hosted right (t: 1370) there in Cloud AI to display. And of course, that is safe because it is very limited. There is no auth. There is no payments. It's front end only. But maybe that's a good product idea that someone should do here (t: 1380) is build some way to make a provably correct hosting system that can have a back end. And that's a back end that you know is safe, no matter what shenanigans happens on the front end. (t: 1390) But yeah, I hope people build good tools that are compliments to vibe coding. Erin. Hi. So for test-driven development, do you have any tips? (t: 1400) Because I often see that Cloud just spits out the entire implementation and then writes test cases. Sometimes they fail. And then I just want to try to prompt it (t: 1410) to write the test cases first. But I also don't want to verify them by myself, because I haven't seen them. I haven't seen the implementation yet. So do you have an iteratable approach that, (t: 1420) have you ever tried it for test-driven development? Yeah, yeah. Test-driven development is very, very useful in vibe coding, (t: 1430) as long as you can understand what the test cases are. Even without that, it helps Cloud be a little bit more self-consistent, even if you yourself don't look at the tests. (t: 1440) But a lot of times, I'd say it's easy for Cloud to go down a rabbit hole of writing tests that are too implementation specific. When I'm trying to do this, a lot of times, I will encourage, (t: 1450) I will give Cloud examples of, hey, just write three end-to-end tests and do the happy path and error case (t: 1460) and this other error case. And I'm very prescriptive about that I want the test to be general and end-to-end. (t: 1470) And I think that helps make sure it's something that I can understand. And it's something that I'm going to be able to do. Yeah. It's something that Cloud can do without getting too in the weeds. (t: 1480) I'll also say a lot of times when I'm vibe coding, the only part of the code, or at least the first part of the code that I'll read is the tests to make sure that, (t: 1490) if I agree with the tests and the tests pass, then I feel pretty good about the code. That works best if you can encourage Cloud to write very minimalist end-to-end tests. Thank you for the very fascinating talk. (t: 1500) I also appreciate that you've done what a lot of people haven't done and tried to interpret one of the more peculiar lines in Karpathy's original post, (t: 1510) embrace exponentials. So I wonder if I could pin you down a little more and say, how would I know if I've embraced the exponentials? (t: 1520) What precisely means following that advice? And to maybe put down a little more in what I think it intends to mean. It sort of maybe alludes to this, the models will get better. (t: 1530) Do you think there's some legitimacy in saying just the fact that the models will get better? It doesn't mean they'll get better at every conceivable dimension we might be imagining (t: 1540) we hope they'll be in. So yeah, how do I embrace an exponential, sir? Yeah, absolutely. I think you got close with the quote of, (t: 1550) keep assuming the models are going to get better. But it's a step beyond that. The idea of the exponential is not just that they're going to keep getting better, (t: 1560) but they're going to get better faster than we can possibly imagine. And that's kind of like when you can kind of see the shape of the dots here. (t: 1570) It's not just that it's getting steadily better. It's that it's getting better, and then it goes wild. I think the other funny quote I heard from this, I think in Dario and Mike Krieger's talk, (t: 1580) is machines of loving grace is not science fiction. It's a product roadmap. Even though it sounds like something that's very far out, when you are on an exponential, things (t: 1590) get wild very, very fast, and faster than you expect. And I think if you talk to someone that was doing computers in the 90s, it's like, OK, great. (t: 1600) We have a couple kilobytes of RAM. We have a couple more kilobytes of RAM. But if you fast forward to where we are now, it's like we have terabytes. And it's not just that it got twice as good. (t: 1610) It's that things got millions of times better. And that's what happens with exponentials over a course of 20 years. So we shouldn't think about 20 years from now (t: 1620) as like, what happens if these models are twice as good? We should think about what happens if these models are a million times smarter and faster than they are today, which is wild. Like, we can't even think about what that means. (t: 1630) In the same way that someone working on computers in the 90s, I don't think they could think about what would happen to society if a computer was a million times faster than what (t: 1640) they were working with. But that's what happened. And so that's what we mean by the exponential, is it's going to go bonkers. Yes? I got a couple. Well, I got one question, but it's kind of two parts. (t: 1650) The first part. So I use a Vibe Code, and I have like two different workflows. I have one where I'm in my terminal, (t: 1660) and then I have one when I'm in VS Code or Cursor. Which workflow do you use? And if you're using Cloud Code in a terminal, (t: 1670) how often do you compact? Because what I find is my functions will get a new name as the longer I Vibe Code, or just things kind of go off the rails the longer I go. (t: 1680) And if I compact, it still has to be a little bit more complicated. And that's what happens. If I create a document to kind of guide it, I still have to get it back on track. (t: 1690) Yeah, great question. I do both. I often code with Cloud Code open in my terminal in VS Code. And I'd say that Cloud Code is doing most of the editing, (t: 1700) and I'm kind of reviewing the code as I go in VS Code, which is not true Vibe Coding in the sense here. (t: 1710) Or maybe I'm reviewing just the tests from it. I like to compare and match. I like to compare and match. So I'm going to go into the (t: 1730) And then I'll say, OK, write all this into a document. (t: 1740) And then I'll compact. And that gets rid of 100k tokens that it took to create that plan and find all these files, and boils it down to a few thousand tokens. (t: 1751) So one question is following up his previous question, which is, have you used other tools along with Cloud Code (t: 1760) to increase your speed a little bit more, like running multiple Cloud Codes together, using Git Worktrees, and then sort of merging a few things, (t: 1770) or stack PRs or something like that? Or is that something that you personally follow or would advise to? Second question is, how do you very structurally (t: 1780) and in a very nice engineering way approach a part of the code base that you're not (t: 1790) very familiar with, but you want to ship a PR in it really fast, and you want to do it in a really nice way and not Vibe Coded? So yeah, what are your ways of using Cloud Code (t: 1800) to help do both these things? Yeah. So I definitely use Cloud Code as well as Cursor. And I'd say typically, I'll start things with Cloud Code, (t: 1810) and then I'll use Cursor to fix things up. Or if I have very specific changes, I know exactly the change that I want to do to this file, (t: 1820) I'll just do it myself with Cursor and sort of target the exact lines that I know need to change. The second part of your question was, oh yeah, (t: 1830) how to get spun up on a new part of the code base. And I think that's a really good question. Before I start trying to write the feature, I use Cloud Code to help me explore the code base. (t: 1840) So I might say, tell me where in this code base off happens, or where in this code base something happens. Tell me similar features to this, and have it tell me (t: 1850) the file names, have it tell me the classes that I should look at. And then kind of use that to try to build up a mental picture to make sure that I can do this and not Vibe Code, make sure (t: 1860) I can still get a good set. I want to have a good sense of what's happening. And then I go work on the feature with Claude. Thank you so much. I'll be around and can answer other questions.

