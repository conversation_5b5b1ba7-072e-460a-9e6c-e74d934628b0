---
title: "Claude Code Just Got A HUGE Upgrade: Claude Flow"
artist: Income stream surfers
date: 2025-08-16
url: https://www.youtube.com/watch?v=-8DLxknZkkY
---

(t: 0) hey guys welcome to this video where i'm finally going to be testing out claude flow for claude code i'm going to show you how to install it etc etc so what i do is ctrl a ctrl c inside the github (t: 10) just go on google type in claude um flow it'll be the first one then i'm going to start a new conversation here so mkd claude flow 3 cd claude flow 3 claude i'm going to say help me install (t: 20) claude flow completely and utterly don't miss any steps and then paste it like that (t: 30) take a while because it's quite a lot of text to paste there we go let's just checking i have all the prerequisites so for code npm etc etc should just quickly have (t: 40) to check oh wait now somewhere um don't do that okay so let's see if this works so version should (t: 50) work okay so let's just make sure help should also work okay now let's test the memory status (t: 60) beautiful (t: 70) now it's just going to test the hive mind stuff so let's see if this works no active forms found (t: 80) and then it starts the hive mind in it here this is the thing i didn't do last time i'm pretty sure i'm just going to take a specific claude md file let's do a simple test okay sure (t: 93) okay so press escape here and we're just going to grab this stuff this is from the context (t: 100) engineering stuff and say make me a command for this project with all agents okay so this is the command i want here to just grab this (t: 112) and control c and then i should be able to exit out here and do that (t: 122) oh damn right so actually creates agents is that what's happening right now oh that's actually (t: 130) pretty op mobile dev create custom front end that is actually crazy that is actually really needed as well damn so is this is this what it does it creates agents right custom agents (t: 140) yeah look agents auto spawned for task wow okay this might be a game changer guys i'm not sure (t: 150) i'm not sure yet but this might actually be a game changer holy what is all that wow okay (t: 160) ticd engineer okay wow and then each one is doing different tasks wow so i'll just quickly (t: 170) quit out here and i'll just say claude dash dash danger to you skip the mission so (t: 180) okay so it's gonna have to do that again but it's fine okay guys so i mean it says it's done this (t: 190) will be very very interesting to see um let's say make the project run now i think a really interesting thing about this is the the agentic system that it uses now if you look if you add (t: 200) up all of these tokens it's easily able to run the agentic system and it's very easy to run it above 200 000 tokens right but it did everything in that amount of time you can see 15 20 minutes (t: 210) but it did so many things at once and it used separate um agents for each one which have separate context lengths which is actually a huge game changer to be honest with you i'm believe i'm (t: 220) using opus i will check that but okay guys so this is what has been built very very interesting (t: 230) let's see if this actually works works for this time. Yeah, I didn't think it would work. Okay, damn. So there are some pages (t: 240) here. Look at this. Holy shit. Okay, so there is definitely something to this method. It's not (t: 250) quite perfect. There's definitely a little bit more work that needs to be done here. However, this is one of the most interesting projects I've actually seen. This was built extremely quickly (t: 260) as well. Oh shit, guys. I think there is something here. This looks like it might actually be working. I've never had something where it's been basically first time with no issues whatsoever. (t: 270) There was a slight CORS cause issue because obviously we have to change local host to local (t: 280) host 3002 because I have so many projects running. But other than that, this was built in one shot and it has pretty much everything I asked for. There are a few things that (t: 290) need to be fixed. But like overall, this is yeah, very, very interesting. This was built (t: 300) more quickly than context engineering. It was built better than context engineering. And it just this seems like an absolute win, I would say. Okay, so there's a problem here where (t: 310) it hasn't used the internet. Obviously, this would be something to do with the first initial prompt. And also just generally, there does need to be a little bit more action here. (t: 320) There's a few things missing. But overall, this is a very, very, very impressive result. So if I go here, this is the directory for all of the pages. Let's go here, for example, (t: 330) and I go here. It built all of these pages. Look at this one by one. It does the SEO for you guys. (t: 340) This is a game changer. I'm going to be making a lot more content about this. This is actually fucking really, really overpowered. Look at this. What? Insane, guys. I'm going to leave the video (t: 350) there. Thanks so much for watching. If you're watching all the way to the end of the video, you are an absolute legend. Make sure you check out SEO Grove and the school community and I'll see you (t: 360) very soon with some more content. Peace out.

