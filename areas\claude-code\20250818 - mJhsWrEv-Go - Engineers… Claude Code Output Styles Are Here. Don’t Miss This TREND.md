---
title: Engineers… <PERSON> Code Output Styles Are Here. Don’t Miss This TREND
artist: IndyDevDan
date: 2025-08-18
url: https://www.youtube.com/watch?v=mJhsWrEv-Go
---

(t: 0) Engineers, here we have six Cloud Code instances. In each instance, we have six unique output styles. Default, table format, (t: 10) YAML format, ultra-concise format, text-to-speech summary, and most importantly, HTML format. (t: 20) For every agent with every style, we have one prompt. Honestly, when I first saw the output styles feature drop, (t: 30) my first thought was, this is useless. But I never let my initial reaction prevent me from doing the real work and understanding the feature. (t: 40) After a quick Dev session, I learned just how useful these output styles are. Never bet on your first initial reaction without doing the work. (t: 50) Dan, I've explained how to add new hooks to your Cloud Code setup with examples and configuration steps. You can hear our text-to-speech format summarize the exact work that was completed. (t: 60) We can see our results starting to flow in here. We're going to break down every one of these output formats. Believe it or not, this feature release marks the beginning of viable generative UI. (t: 70) The Cloud Code team is on a generational hot streak, shipping valuable features back to back to back. (t: 80) But there are two features I'm not a fan of because, there are two features I'm not a fan of because, there are two features I'm not a fan of because, there are two features I'm not a fan of because, there are two features I'm not a fan of because, they could take the tool in the complete wrong direction. (t: 90) We'll talk about that in this video. Right next to the output styles drop, we also have the status line feature that lets you customize your Cloud Code interface. (t: 100) And I can guarantee you, you're not using this feature to its full potential yet. (t: 110) Default, table, YAML, ultra concise, text to speech, HTML. (t: 120) New features are only valuable in relation to existing features. In our first Cloud Code instance, you can see the default response for the following prompt. (t: 130) We asked, slash question, how can I add a new hook? If we look at this response format, you can see it's the default Cloud Code response format. (t: 140) But now we have full control over the output style of Cloud Code. If we switch to our table, we can see that we have a new response. If we switch to our table, we can see that we have a new response. If we switch to our table, we can see that we have a new response. You can see something very different. We have clean formatted tables helping us speed up the flow of information between us and our agents. (t: 150) You can see this is a valuable format for organizing information. (t: 160) Now we get to my second favorite output format, YAML. At first, this format might seem kind of weird and kind of out of place. (t: 170) But the YAML output format actually has a really interesting way of organizing information in a highly structured way. I found that this specific Cloud Code instance typically outperforms the rest. (t: 180) Why might that be? If I just copy all this out here, clean up the output format a little bit here. So you can see the task here, analyze the hook system, provide guidance on adding new hooks, (t: 190) status complete, there's some details. The YAML format provides a highly structured informational way to get responses out of your Cloud Code instances. (t: 200) So next up, we have the ultra concise format. So next up, we have the ultra concise format. This format is the most similar to the default Cloud Code response, (t: 210) but it's going to be concise and just give you enough information. I've also found that the ultra concise response format reduces the number of input and output tokens that Cloud Code consumes. (t: 220) Next, the text to speech format. In the beginning, you heard after our agent completed its work, it communicated exactly what it's done. (t: 230) I'll just run this again. Say again. All right, so we can just hear that again. I've explained how to add new hooks to your Cloud Codes. So here's the second step. You need to run your Cloud Code setup with examples and configuration steps. (t: 240) Audio summary again for Dan about Cloud Code Hook's setup and configuration. Okay. So in the system prompt, it has the instructions. (t: 250) Whatever command you just ran, summarize it. So I asked it to run that audio summary again. It ran it. And then it ran an audio summary on the audio summary. (t: 260) Okay. So that's fantastic. But there is one output format that is more important than all of them. format. This is the most important output format of them all. Let me explain why. (t: 270) This is the one output format to rule them all. At first, you might be thinking, damn, (t: 280) this is stupid. This is super hard to read. There's no way I'm going to actually get information by reading HTML. We're talking 10 to hundreds of responses per day as we're doing (t: 290) real engineering work with cloud code, with agentic coding tools. Let's slow down and really think about what's happening here. We have dynamic, accurate HTML generation our agent is generating (t: 300) on the fly. What you're looking at here is the first useful application of generative UI. With (t: 310) a few tweaks to this prompt, we can create a new output style that enhances the information rate between us and our agent. Let me show you. (t: 320) Exactly what I mean. Let's fire up a brand new cloud code instance slash output style to update the style. Then we're going to hit gen UI. What you just saw was format eight. This is our HTML (t: 330) structure. This prompt builds on top of the HTML layer. Let's fire this off. Let's run that exact same prompt that we ran before. Slash question, how can I add a new hook? What you're going to (t: 340) see here is nothing short of extraordinary. With output styles, with (t: 350) cloud code, we're going to hit gen UI. Let's fire up a brand new cloud code instance slash output style to update the style. Let's fire up a brand new cloud code instance slash output style. Let's fire up a brand new cloud code instance slash output style. Our agent can build UI on the fly. Now I'll create a comprehensive HTML guide explaining how to add a new hook to cloud code. (t: 360) On every response, our agent is going to generate UI and respond to us in this rich way. Check this out. (t: 370) How to add a new cloud code hook. Let's see exactly what happened there. It wrote to a temporary file. It opened it at the end of the response. (t: 380) It generated an HTML guide for us. This is generative UI. This is not a gimmick. This is yet another powerful agent decoding tool, a powerful agent decoding pattern you now have in your toolbox. (t: 390) With every request, we can now have our agent generate UI for us. This is generative UI. (t: 400) You can see here we got a great breakdown on exactly how to add this. It's got consistent, simple themes and style. (t: 410) This is insane and we have project structure it's really breaking down how we can add a cloud code hook. We even have some next steps. We have files referenced. Here we have UI that was generated on the fly for us. (t: 420) This is HTML. It's the language of UI and of course, Sonnet, Opus, they can generate this for us with no problem. (t: 430) It does take a little more time to generate the response but you can see how powerful this can be. This is not a gimmick. (t: 440) Let's run some more of these output style, Gen UI, output style, Gen UI. Here we're going to generate a TypeScript interface. (t: 450) Here we're going to do some research with fire crawl. We want to see the top five hacker news posts, break down the sentiment. I'm going to update the model here, switch to Opus. (t: 460) And this is going to be in that YAML format, create a Pydantic class for cloud code, hook output structure, blah, blah, blah. All right. So we're going to run that. We have multiple agents running on our behalf, getting work done for us. (t: 470) All right. So check this out. Here's our hacker news sentiment analysis. We can quickly validate this. The future of large files and Git is Git. If we just search this, you can see there's that 453 points. (t: 480) That looks right. This is quite fascinating. Okay. This is a new paradigm to operate in. You can see the points on the side there. We are getting the top five posts on hacker news right now of this date. (t: 490) We have a temp file getting generated for us on the fly. Here's another one. We have that type script interface get generated. (t: 500) Here's a summary of the work done, task completed. It created this new types file for us based on the JSON structure in this file. Clean, simple write up, right? Detailing exactly what was done. (t: 510) Preconfigured variables. You can see here's the interface hierarchy. Just giving us a breakdown of what this looks like. We can of course open up this code base, just search for types. (t: 520) There it is. If we look at .mcp, JSON sample, this exact type right here. It looks like it's typed it out nicely for us here based on the other mcp servers it (t: 530) has in its system prompt. Here's our last request here, wrapping up CC generate cloud code hook models. Based on all the output types from our hooks, look at this incredible UI. (t: 540) Every time it runs, it's generating UI for us. You can imagine pushing this further, adding a layer on top of this where we can interact (t: 550) back and forth. You can imagine a layer where we can interact back and forth. You can imagine a layer where cloud code itself, the terminal interface is actually (t: 560) presenting us with generated terminal user interfaces and letting us interact with that. There are really no limits to this capability. We have truly the first useful application of generative UI. (t: 570) Us interacting with our agents, our agents giving us specific responses. You can see here it's breaking down all the hooks. (t: 580) This is all based on our output style system prompt. Okay. This feature is ultra, ultra powerful. I'm almost like embarrassed to say that I thought this was not useful, right? (t: 590) But this is ultra powerful. And I think that probably even the cloud code engineers haven't really realized this capability, (t: 600) right? Or where this could go. They have now because they're watching this video. What's up, anthropic engineers, cloud code engineers. Thank you. You guys are doing incredible work here. (t: 610) But this is really extraordinary, right? This is the power of the primitive. Okay. This is the power of the cloud code. This is the power of stacking up the right feature set, putting together the right tools, (t: 620) and most importantly, understanding the fundamentals of AI coding and what's actually happening under the hood of the agent, right? All this does is it updates the prompt, right? (t: 630) The output style, they say it here, right? I'm really happy that they're being really clear about the fundamental unit that this is updating. The output styles update the system prompt. (t: 640) Okay. As viewers of the channel know, there are only three key elements. Okay. I'm going to set these the right ways for you to define this. (t: 650) One of the key elements is code mechanics and code mechanics starts again with the program defining what code mechanics and code mechanics replace system functions and Ramadan, which try to define how (t: 660) all scenarios with around their different cadences really PF is. (t: 670) When I actuallyambition error. You really realize this. Land on fully, right? Yeah. Yeah. The συ жест. bros or some blog, use cloud code output styles to generate UI. Remember where you saw this first, (t: 680) okay? Remember how many things you've seen first here, right? It's so obvious after you've seen it. (t: 690) I've seen so much of what we've done here on the channel getting repurposed without giving credit where it's due. Obviously, I come here every single week to share these ideas with you. I'm not gloating. (t: 700) I'm just stating the facts of all the incredible work that we do here on the channel week after week after week. If this is your first time watching an Indie Dev Dan video, welcome to the (t: 710) channel. It is funny to think that some new subscribers think I'm a YouTuber. (t: 720) I'm not. I'm an agentic engineer and many longtime followers of the channel are transforming into this role as well, one week at a time, one concept at a time, just like cloud. (t: 730) code even on a hot streak focusing on the signal of what's going on in the space surrounding generative ai prompt engineering ai coding and now agentic coding make sure you subscribe so you don't miss (t: 740) the signal of what's going on there's so much noise stay focused on the signal stay focused (t: 750) on valuable information like this okay our agent is responding to us in user interface okay this is extraordinary make sure you subscribe and make sure you take principled ai coding so you (t: 760) understand the fundamentals that agentic engineering is built on do this before the phase (t: 770) two engineering course hits i'm working on that day and night to ship that on september 29th mark the date the countdown has started okay this is the phase one course everything we do moving (t: 780) forward is based on the key principles of the phase one course and i'm going to show you how to master the basics of ai coding big idea here you need to master the big three this is going to be (t: 790) one of the last times i'm mentioning this course after this it's all in on phase two this is risk (t: 800) free by the way no questions asked refund before you start lesson four so hop in here master the basics so you can master the compositional ideas right the bigger ideas i've been able to move so (t: 810) fast and ingest these features so quickly because i understand the fundamentals okay and i built all the fundamentals you need to master ai (t: 820) coding and now agentic coding in this course check this out so just to mention it by the way a question prompt like this is ultra valuable for speeding up the information flow between you and (t: 830) your agent this helps prime your agent while giving you information you need to make engineering decisions it's also great for helping your engineering team get up to speed as well if you're (t: 840) a lead on your team or you want to add some value to your team so you can use this question prompt okay so before we move on to how output styles work let's talk (t: 850) about status lines (t: 857) as you can see here i have a pretty bare minimum status line a lot of the status lines you've (t: 860) probably seen so far are like vibe coding it's the lowest hanging fruit when you combine hooks (t: 870) and a simple state management system your status lines can become tremendously more valuable let me show you exactly what i mean so i'm going to get rid of some instances here let's clear out and (t: 880) just focus on a single instance here we have a simple minimal status line model current working directory git branch plus the changes on committed our cloud code version this is simple mostly (t: 890) static information let's update our status line to use a new format i'll open up cursor go to my settings here we'll search for status line if we open up our folder structure here i have a (t: 900) dedicated status lines directory with this status line in there so i'm going to go ahead and start this and just add a couple more status lines in here so if i go to my settings here i'll see my status with four versions, giving us different status line outputs. (t: 910) Let's update to v2. And let's see exactly what this looks like. I'll close Claude reopen and check this out. Now we have model and no recent prompt. (t: 920) So you can see exactly where this is going, right? I'll say hi, we now have our most recent prompt in the status line. So who cares? Well, why is this important, right? (t: 930) Isn't this kind of kind of stupid? If you follow this channel, you probably are running more than one Claude code instance at any time, right? Just like AI coding is not enough. (t: 940) One agent is not enough. As soon as you start using powerful agentic coding tools like Claude code, especially Claude code, there are only two directions to go better agents or more agents. (t: 950) All right, output formats helps us with better agents. Status lines helps us with more agents. Okay, this is really, really simple to portray here, right? (t: 960) Open. Open up a new terminal window, buy or up Claude code. Let's go yellow mode in Opus. Let's update our output style just for fun. We'll use Texas speech summary. (t: 970) This is a great format when you have multiple agents and you're working in parallel, doing a lot of work, long running tasks, you can put on Texas speech and when your agent finishes, (t: 980) it'll respond back to you. So this is great for more and better agents. Here's the kicker. This says, hi, I can say who's investing the most in AI data centers. (t: 990) I've seen in Google, right? Just any prompt, right? I'm just firing off whatever. And you can see here we have a brand new last executed prompt now on its own. (t: 1000) Not very useful. Right? But when you're switching instances all the time tell me about this code base. This becomes very useful. Okay? So we have one instance here. (t: 1010) We have another instance here, right? If you're really pushing this tool, you've likely opened up multiple and I'm talking 3510 instances of this tool in your prompting. (t: 1020) All right. the time here's another prompt okay and so it becomes very very helpful to have this last prompt status line to remind you what is going on right so now i can just quickly look at this (t: 1030) cloud code instance i can read the status line and i can know okay yes that's what i was prompting that's what i was working on in this specific instance okay and so when you combine just a (t: 1040) little bit of state with cloud code hooks with i'm ready to help but need you to share the complete prompt or question you mentioned with the status line you get a really really powerful capability (t: 1050) here that builds on your agent decoding tool so you can see here our web search firing off and you (t: 1060) know when i look at this i can just immediately look at the status line right we have emojis based on if we're asking a question if we're creating new code right let's go ahead and do that here in a new instance right so claude um create a compressed readme based on readme like this (t: 1070) right we have the create keyword here right an information dense keyword and now our recent prompt there we are we have (t: 1080) that light bulb right because that's creating or updating something new okay and so you know we're updating just a few things right we're using the tools i've analyzed your cloud code (t: 1090) hooks mastery code base it's an advanced tool kit for extending cloud code with hooks subagents (t: 1100) and custom behaviors wonderful so we've got that text-to-speech response again this is useful for interacting with multiple agents this kind of replaces our stop hook text-to-speech response that we had that we've been using in previous videos now with the (t: 1110) output styles we can have our agent run a tool every time it finishes in this case the tool (t: 1120) happens to be 11 labs text-to-speech you know i hope you're really paying attention to what you can do with this tool right output styles in combination with the status lines what does this (t: 1130) do what's the fundamental change that's happening here we are increasing the information rate between ourselves and our agent agents, right? And when you push this further with something like... (t: 1140) I created a compressed readme that cuts 650 lines down to 150 while keeping all essential information. Wonderful. And so when you push this even further, what do we get, right? We get (t: 1150) generative UI, okay? Right? This is kind of, you know, where this is all going. I hope you guys (t: 1160) can kind of see a larger picture here of how far you can push this tool, all right? And again, if you're not subscribed, join the journey. We focus on these big ideas every single week. I'm (t: 1170) not waiting for the future to show up. I'm running toward it and I'm running toward it and sharing the ideas, sharing how you can also be using these tools. We have generative UI, right? We (t: 1180) have powerful status lines to help us keep track of what the heck is going on. So here's another generative UI response. For that question I asked about data center investments, Cloud Code did some (t: 1190) research and created a breakdown for us. Clear winner here for AI data center, open-ended data center. I'm going to give you a little bit of a breakdown of what's going on here. So here's another generative UI response. For that question I asked about data center investments, Open AI stargate, 500 billion over four years, pretty gnarly. A great breakdown again, (t: 1200) generative UI. My agent just put this together for me. Here's a little timeline and you know, always just a starting point, right? Every response, every prompt you write, (t: 1210) every piece of context that gets added to your agent. This is just another tool, another leverage point for your agentic coding. All right. So fantastic, really cool stuff here. (t: 1220) Let's refocus back on the status line. Now, you know, I have these four windows open, right i'm doing a lot of work in parallel i can cycle through these and know exactly what was (t: 1230) going on right so literally nothing was happening here just demo prompt but here who's investing the most okay now we can follow up on that what else we have right create a compressed readme okay fantastic we also got that text speech summary 650 down to this open that file of course we're (t: 1240) going to get that i've opened the compressed readme file for you to review text to speech (t: 1250) response fantastic stuff let's go ahead and close out our instances here let's focus that was just two status lines right let's go to status line v3 and then i'll leave v4 for you to experiment (t: 1260) with here let's fire up cloud once again we'll run an opus with permissions you can see here a very similar format until we start prompting so the new flow here for me is set my output (t: 1270) format let's go ahead and set a yaml structured format here and i'll say at we'll search for that new compressed readme what does this code base do read only this file (t: 1280) so just going to get a quick understanding and you're going to see something really incredible here our status line updated here we have a name for our agent kind of a similar format right (t: 1290) opus is a little bit better at formatting yaml we have a great yaml based response format (t: 1300) again there's something about this response format that improves this agent's performance definitely play around with this right we said what does this do read only this file (t: 1310) task completed overview core functionality right eight life cycles for controlling cloud code yep sub agents okay meta agent very good covered that in the previous video custom features (t: 1320) status line output formats very good technical stack love that astral uv that's right shout out astral use cases key innovations great stuff right and our status line has the last prompt (t: 1330) and an agent name another tool to scale your agents and another example to showcase how you can use (t: 1340) state that's getting updated throughout your session to guide your agent to the next step and to help you interact with your agents faster and better right more agents better agents first (t: 1350) you make your agents better then you add more agents okay now read uh read me update your understanding okay something like that and in this status line v3 we now have trailing prompts (t: 1360) right so historic trailing prompts here's the most up-to-date and here's the second to last (t: 1370) prompt that we ran okay so really powerful stuff here i think i think you get the idea as we're interacting with agents more and more as engineers the key (t: 1380) idea the key value proposition is in increasing the information rate between you and your agent right between you and the work that you're trying to do these are big drops i did mention in the (t: 1390) beginning there are a couple features i'm not as happy about coming out of the cloud code team let's just quickly talk about those (t: 1400) what are the features that cloud code is not happy about? there are a couple features that cloud code is not happy about i want to talk about that um i think might be pushing cloud code in the wrong direction my (t: 1410) opinions but i want to share this with you i want to get your thoughts on these as well model opus plan mode when you submit a prompt cloud code is going to plan with opus and then (t: 1420) build with sonnet if you've been with the channel for a while this feature might make you think of aeder the original ai coding tool aeder was one of the first ai coding tools to really leverage (t: 1430) language models this is a very common tool in the cloud code team so if you're using a lot of cloud code teams you're going to want to make sure that you're using a lot of cloud code teams is traditional ai coding it's not agentic coding there are no tool calls happening in aeder that pr (t: 1440) that patch just hasn't gotten out aeder was one of the first tools to build out a architect mode we can see this great blog put back out in 2024. this was innovative because they used a prompt chain (t: 1450) right two prompts with two models aeder separated thinking and building okay this was really big we (t: 1460) had already talked about prompt chaining on the channel around this time why do i have an issue with this tool aeder boa talk to something Google ljinx endpoint connection setup solve that (t: 1470) i don't know but aeder mount writing logic was not standard in aeder because we needed to update aeder to help at some point an other would get debuggington for an aeder so i did (t: 1480) extracted the code that call code poca and the cost this concept was a bit less expensive and gave a feeling i wasn't agressive about lost control at the time of my start i just kept (t: 1490) banking on pinching the loggin to doär build but then i decided i'm gonna do whatever they said models, how they're used, the planning, the building, and things like background task management, right? (t: 1500) Bash controls. You know, I can simplify everything I'm trying to say here a little further and just say, you know, don't tell me how to use the models. Don't tell me how to manage my bash commands in my (t: 1510) background processes, okay? This is something I always watch out for with every tool, right? Especially the leading tool. I have a guiding question that kind of helps me determine if a new (t: 1520) feature is valuable or if it should even exist, right? In the generative AI age, we always have to keep track of the context model and prompt every step of the way. The leading question here (t: 1530) is, do I know exactly what the context model and prompt are at every step of the process (t: 1540) in whatever tool I'm using, okay? Any feature that obscures this or inserts an opinion about how you or I should manage the big three, it throws up a huge yellow, orange, red flag. (t: 1550) Now, to be clear, both of these features are useful, right? Opus plan mode, where you have (t: 1560) opus plan and sonic build, quite useful feature. I'm not saying it's not a valuable feature. You can build a great feature that is directionally incorrect for your product, for your mission, (t: 1570) for your stated engineering philosophy, okay? So maybe this is just me. Let me know what you think about this. Do you think that opus plan mode and background commands are a slight overreach from (t: 1580) cloud code, right? From the background commands? Do you think that opus plan mode and background commands are a slight overreach from cloud code, right? From the background commands? Do you think that opus plan mode and background commands are a slight overreach from cloud code, right? From the team in cloud code, is this the right, you know, set of features that they should be building out? I think this is the first sign of things going a little bit off course, but maybe I'm overanalyzing (t: 1590) being too detail oriented. Let me know in the comments if you like this direction from cloud code, separate from the individual features themselves. Again, I'm not saying background (t: 1600) bash commands and opus plan aren't valuable features. So with that aside, you can see here (t: 1610) that output styles and the status line increase the information rate between you and your agent. (t: 1620) So let's quickly understand how this works. These are very simple features, which I absolutely love. The cloud code team is keeping it simple. If we open up dot cloud, open up output styles, (t: 1630) you can see we have all of these output styles detailed here. We have a bullet points format that I just completely glossed over, right? We have our HTML structure. We have our markdown. (t: 1640) And of course we have the gen, the UI. This code base is going to be available to you. Link in the description. By the way, if you made it to this point in the video and you tune in every single week and you're still (t: 1650) not subscribed for some reason, what are you doing, right? All of this work, all the ideas we talk about every single week, and you can't press one button to show your appreciation. Come (t: 1660) on, subscribe, join the journey. Don't be weird about it. Okay. You're in good company. You know, I'm here delivering value for free for you every single week. So hop on the train. Things are only (t: 1670) going to continue to accelerate. So that's the end of this video. If you enjoyed it, please subscribe and hit the bell. And if you want to see more of this, check out the link in the description. You can see how this works and you can tweak it to make it your own. You can see we have that key workflow step for these agentic workflows where we want our agent to take actions back (t: 1680) to back to back. This is a great place to get a high level breakdown of what the prompt does exactly. So that's that. HTML structure, TTS, it's all there. If we close that, we can focus (t: 1690) on our status lines. And I've created a dedicated folder for this. Output styles is a anthropic specific directory. Status lines is something that I created just for organizational purposes. (t: 1700) Just like the data directory here, we'll talk about in a second. You can see in the status line directory, the only thing you need to focus on is main, right? We read in the line, we generate (t: 1710) whatever status line you want using whatever data you want. But most importantly, we're just printing it. That's it. That's how status lines work. And to run this specific status line, (t: 1720) as you saw, all you need to do is add this to your dot cloud settings file. You can see here, we have that status line, the three. And if we open up V three, (t: 1730) you can see something really cool in combination with our clock code hook. If we look for user prompt, right, and we open up these files side by side, we are managing session data. So every (t: 1740) cloud code prompt makes a contribution to our agent specific data, right? So we have this new (t: 1750) dot cloud. If we open up data, you can see here, I have a bunch of sessions. And if we open up one of the sessions, you can see here, we have information about that particular session (t: 1760) that we can update. So if we open up one of the sessions, you can see here, we have information about that particular session that we can update, write to and read from. Okay. So as you would expect, session ID, agent name prompts. This is how we're populating our agent name and our prompt (t: 1770) history, right? And we can keep prompting. Fantastic. Looks great. Just a random prompt, just to showcase that, you know, we're tracking the three most recent prompts with this status (t: 1780) line, right? And this is all because we have a simple state management system. Again, just JSON objects, super readable, very understandable. And that's that, right? And the other thing that we (t: 1790) can do is we can do a lot of things. So if we open up a new user prompt, submit hook, right here, we have that cloud code hook. We are combining these two features to get more value out of them. Prompts (t: 1800) append. If we don't have an agent name, we're doing something really awesome. If you remember from our GPT-5 nano agents video, we were running GPT OSS 20 billion. Here, we're doing that exact (t: 1810) same thing. If we open up olama.py, we're having a powerful on-device model right on my MacBook generate unique names for our agent. This is a simple way to do that. So if we open up a new agent (t: 1820) file, we're going to see that we have a super simple, great use case for these agents. You can see here, generate agent name. This file is going to be here for you to check out. It's super (t: 1830) simple. We're generating a one word name, right? This is prompt engineering 101. That's it. The agent has five seconds to generate this or it falls back. And this is how we update state for (t: 1840) our status lines. What you're seeing here, right? All these features that you've seen here in this video, these are essential ideas of what you can do with output styles. You can take this further (t: 1850) and combine, merge, and add. You can add conditional branches directly in your output style system prompt to direct which output type your agent uses based on the prompt at hand. Okay. So if you want to take this even further, (t: 1860) that's a great direction. And this is what makes Cloud Code so great. It's a new primitive of engineering. It's the engineer's agent that's increasingly customizable and so far so good, (t: 1870) very unopinionated. This is key. It's not a dedicated solution. This is what we as engineers (t: 1880) need to solve. Any problem we face. And this is why, again, I feel like the plan mode and the bash command is starting to look like a slight overreach of this principle of being an unopinionated, simple engineering primitive. Tell me what you think about that. Drop a comment down below. This code base is available to you. Link in the description. No matter what, stay focused and keep building.

