---
title: "I Tested EVERY AI Coding Tool for $500+ - Only ONE Survived"
artist: AIpreneur-J
date: 2025-06-07
url: https://www.youtube.com/watch?v=ifKX_YyN5T0
---

(t: 0) I spend over $500 testing every AI coding assistant, cursor, insert, code code, and you name it. (t: 10) And after three months, I'm using only one. Here's why code code with Opus 4 destroyed the competitions plus eight tips that will make you unstoppable. (t: 20) If you're new here, I'm Jun building multi-sense products in AI agency as a solo founder. (t: 30) I use AI to replace what used to take entire teams, and I have shared everything I learned so you can do the same. Please subscribe our channel and our newsletter via link in the description. (t: 40) And now without further ado, let's dive into it. So let me compare top three AI assistants right now, cursor, insert, code code. (t: 50) The first thing is context window. As you can see, code code. (t: 60) Can capitalize the max context window you are allowed to. On the other hand, cursor and windsor. (t: 70) And didn't give you full context as you want, even though you pay $15, $20, you will be limited to this around, but you can use maximum, but it's your credit faster. (t: 80) Like 1.20, 1.25. I wonder why this happened. (t: 90) So I found this one from the Reddit. There is a guy from client team and he said, quote, code and client have different approaches than cursor and Windsor. (t: 100) They take context forward, meaning that it takes as many as context to finish their word. So get things done for you without messing you up. (t: 110) And the second thing, and a lot of people hate the paper use here. Okay. So I'm going to show you how to use here from the code code. (t: 120) But from recent updates, if you do take max plan, you don't have to pay. Cloud code per API anymore. (t: 130) You can use cloud desktop and cloud code, but still expensive because it's, I think 180. But I always think this is ROI. (t: 140) If it saves your five hours more per week, I think it's a hundred percent worth investment. But if you don't know how to use cloud code well, or if you don't understand your code base, your product roadmap, well, it may be waste of your money. (t: 150) So I'm just letting you know here. (t: 160) And the second part is multi agent. So cloud code, interestingly, introduce multi agents, meaning that you can spawn multiple cloud code instances and run at the same times. (t: 170) They even mentioned in here. So you can run multiple cloud code instances. Okay. So you can do one work on the main thing, planning thing. (t: 180) And the second instance is a preview the following code for you. Or in my opinion, I tried this one a lot. (t: 190) It helps when you work on the main thing with extreme focus and then ask them to work on minor bug fixes or minimal changes in styles. (t: 200) That helps. And the last thing I love the most. Many people ask me. So many times about what is the best cursor roles? (t: 210) What is the best roles for running Windsor perfectly? And the answer was always, it depends. It's really hard to give you the best one, even though you can have a basic project structure with cloud MD. (t: 220) You don't have to work on those rules. You just need to in it. Cloud that MD and the cloud MD will return your repository and try to get the gist of it. (t: 230) Those information will be always based on that. And the last thing I love is the ability to take into your prompt by default. (t: 240) That's why it has reduced a lot of errors that I could I found with cursor in Windsor. (t: 250) So I love that. So far, I've compared three different AI assistance for you. And now let's move on how you can use cloud code better with these eight tips. (t: 264) First tip actually is to read through this anthropic engineering cloud code, the best practices for agent code. (t: 270) Surprisingly, they compile all the resources, tips that you can apply to make your cloud code workflow smooth. (t: 280) So next thing is to create cloud that MD file. (t: 290) As I mentioned previously. Cloud MD file is a special file that cloud automatically pulls into context when starting any conversations. (t: 300) This makes an ideal place for documenting your batch that comments or file utility functions, style guidance, testing instructions, repository guidelines like that. (t: 310) So to do so, you can do slash in it. (t: 320) And this will initiate. New cloud that MD with your current code based documentation, it can take a little bit of time. (t: 330) But this gives you a jumpstart. And from there, I recommend to update this cloud that MD file whenever you have a major changes or value fixes or you want to update change logs. (t: 340) The way I'll do it is just to update. (t: 350) Not like you go to each file manually and update it. I will just ask at the end of your session, your coding session. (t: 360) I'll do update. Docs. Or add a little more context in this will update your cloud MD file. (t: 370) And for example, mine, my new product here as I'm working on my architecture documentation here. And then it shows overview. (t: 380) The stack. Structure core components. Flow. And. All of it. And then whenever I have improved each part of it, each part of content, I update these files too. (t: 390) And cloud will bake this information for you for the next prompt. (t: 400) From my experiment, this has reduced a lot of misleading behavior that I encounter with windsurf or cursor. (t: 410) Next one is to use slash commands for repeated workflows. Such as. Debugging. Or git. Or document. You can store templates in markdown files within .cloud-commands directory. (t: 420) And these become available through the slash commands menu when you type slash. (t: 430) So. Here. When I slash, I have now. Bubfix. Document API. (t: 440) Git commit. Create a PR. Like these. That I saved in these commands. And. Then I can save this in my command directory. So. (t: 450) If I show you quickly. Here. I just. This is all. Native LLM. I said. Use git status. And use git diff. And create descriptive and conventional commit messages. (t: 460) Following these rules. And then commit. So this helped me a lot. And I also like the optimize plan. Or review. (t: 470) Optima. If I go back here. And. Optimize it, I said focus on efficiency, memory usage, computations, such that. (t: 480) And I always ask suggestion how I can improve my code base. So these slash commands are very useful for me. (t: 490) The next one is utilize escape. (t: 500) As you see, Cloud Code is going on the wrong direction. Many times you are waiting for AI to finish their work, and if we dislike it, we start (t: 510) undoing it. Now Cloud Code offers escape button. So if you don't like any movement, you just stop it there and then redirect them. (t: 520) And you can do by, for example. And it will create, it will start creating content for me in this product. (t: 540) And now Cloud is working for creating new content form. And now it has components content form here. (t: 550) And if you don't like it, try escaping it. And I'll say undo what you did. And it will create for creating content form. (t: 560) And then it will reverse back to the previous version. See, it will remove the content form correctly. (t: 570) So if you do yes. (t: 580) And let's see, get status again. Now it's clean. So I highly recommend using escaping. Don't get scared of it. It's fine. (t: 590) So the next one is utilize screenshots. Cloud also can read images from your screenshots. (t: 600) So whenever you have broken UI, try to screenshot that and upload it here. (t: 610) Like this. Like this. And add a screenshot. And then you can see that it's working. And then you can see that it's working. And add a simple prompt. From my own experiments, Cloud is really good at reading those images so you don't have to describe a lot. (t: 620) Instead, you direct them. You ask them how you can fix them. (t: 630) So fix this with adding more pegs, for example. (t: 640) And it will fix very well. So now, I'm going to show you how to fix this. So next one is to use MCP for connecting tools for your own advantage. (t: 650) So Cloud, you can add like Cursor or WinServe. You can connect many MCP servers. (t: 660) And from my own experience, I like these five. Superbase, Browser MCP, File System, Brave Search, and Fetch. (t: 670) So use these MCPs to plain. Design and debug your codebase. And these are just my favorites. But if you want to have more MCPs, there is a site. (t: 680) The website is mcpservers.org. (t: 690) You can search many MCP servers that might help your productivity. And you can also see most popular ones here. (t: 700) Brave Search, Fetch, Git, Google Drive, Postgres, Puppeteers, and things like that. (t: 710) So check this out. So next one is to how you ask Cloud. So they said, we recommend here using the word think to trigger extended thinking mode, (t: 720) which gives Cloud additional computation time to evaluate alternatives more thoroughly. (t: 730) If you have difficult code architecture or complex designs, I highly recommend to use think. (t: 740) And interestingly, think, think hard, think harder, and ultra think in this way, that will extend further. So utilize this one if you are tackling down a difficult problem. (t: 750) I've used this one and surprisingly, it worked really well. Remember this one? (t: 760) So final one is to utilize multi-agent instances. So I briefly mentioned in the beginning of our video, (t: 770) and you can spawn multiple instances like here. And I can do a lot of Cloud. (t: 780) So while I'm working on the main one, you can also ask the second one, third one to do different tasks. So you can increase your productivity. (t: 790) But here's the thing. I highly recommend to not approve the right mode by default, like YOLO mode. (t: 800) So I like to focus on the main one, especially difficult one. But if I have minor bugs that doesn't interfere the main workflow of your current one, (t: 810) or if you have basic design changes, I recommend trying this mode. And I like to use this mode to try and use multi instances. (t: 820) So while I'm working on this, let's fix this, and I go to here, and let's retouch the dashboard for that mode. (t: 830) Then while it's working for this one, I will be very focused on the first one, (t: 840) which is the core problem I'm currently solving. (t: 846) So this way you can increase the productivity. (t: 850) But I'm not sure yet. I'm not that experienced to make these instances under my control. (t: 860) But I'm just letting you know. So today I cover the difference between Cursor, WeServe and Cloud Code, and why Cloud Code can be your perfect AI pair programmers (t: 870) that understand your entire project and follow your instruction without much effort. I think Cloud Code with Office 4 is at another level. (t: 880) And try these eight tips for just one week. I guarantee you will never get back to other tools. (t: 890) And thanks for watching this video. And make sure to subscribe to our channel and my newsletter that I share my own journey building multi-sense in AI agency with AI only. (t: 900) See you in the next video.

