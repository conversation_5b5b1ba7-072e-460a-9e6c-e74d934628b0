---
title: 5 Agent PATTERNS to SIMPLIFY Your Agentic Coding (<PERSON>, Gemini <PERSON>, MCP)
artist: IndyD<PERSON>Dan
date: 2025-08-25
url: https://www.youtube.com/watch?v=XojxD7hfaD4
---

(t: 0) If you're an engineer using the best tool for the job, you know this as fact. Agents are not the future. They're already on our desks, in our repos, in our workflows. It's clear that (t: 10) agents are already here. But here's what's not so clear. It's not obvious how to get the most (t: 20) out of them. Should you lean on prompts, MCP servers? How do you integrate them with existing APIs, CLIs, or even other agents? The answer to this question can be the difference between (t: 30) shipping in an hour or shipping in days. You've seen this. You know what you can do with the right (t: 40) context model, prompt, and tools in the right agent. That's why today we're breaking down the top five agent interaction patterns so you know exactly when to use each pattern and how to turn (t: 50) your agents into your unfair advantage. Here's the setup. You're a cracked Gen AI engineer. But as your abilities have grown, you've taken on more (t: 70) responsibilities. The small, lean AI team narrative has taken hold in your company, and that means more (t: 80) responsibility. With responsibility, of course, comes opportunity. Right now, you're working on a big presentation. You're working on a big presentation. You're working on a big presentation. You're working on a big presentation. Everyone on your team is depending on you. You're almost finished, but there's (t: 90) one big missing piece. Tons of high-quality images. You know that you're in the age of agents. Instead of using Sora, MidJourney, Gemini, or going through some image generation service, you've been watching (t: 100) any DevDan videos, and you know that agents equal exponential leverage. You also know, of course, (t: 110) that cloud code equals the best agent. Okay? So you start designing a system. You'll use cloud code to generate (t: 120) tons of images. You'll go through the Replicate MCP server, or you'll hit the API directly. You're not sure yet. This is great because you have access to any image model you need. You'll even use the brand new (t: 130) Quinn image edit model for tweaks to get your images just right. Then a massive problem hits you. Are you building a custom slash command? (t: 140) Or a cloud code sub-agent? Or a custom MCP server that calls the Replicate API? Can't cloud code just use the Replicate API directly? Is this just a ad-hoc one-off prompt? Or maybe you should build a custom MCP (t: 150) wrapper server to hit the API directly, to hit other services you want to embed. Or maybe you should combine (t: 160) some of these approaches, right? This is the agent pattern problem. You realize you have no idea what the (t: 170) optimal agent interaction powers. You're not sure what the optimal agent interaction pattern is. We have all these nodes, all these resources. We have more compute available to us than ever. Agents, reasonable prompts, sub-agents, MCP servers, existing APIs, CLIs, applications. And of course, it's all about the outcome for you, your users, your company, your customers. (t: 190) But how do we put it all together? Okay? We're making a bet here, right? We have a big bet on this channel. We bet on this every single day over and over and over. (t: 200) We're betting that a majority of the economic market will be the same as the market. And that the economic value of AI will come from AI agents. If you believe this is true, we can start asking important questions, right? We can start asking the winning question. (t: 210) What's the best interaction pattern you can use to quickly scale your engineering impact with agents like Cloud Code, Gemini, Codex, and the next big agent, right? (t: 220) Whatever's coming next. What are the best interaction patterns? Let's talk about the top five agent interaction patterns with Cloud Code. (t: 230) Let's talk about the top five agent interaction patterns with Cloud Code. Let's talk about the top five agent interaction patterns with Cloud Code. Let's talk about the top five agent interaction patterns with Cloud Code. And this is a common agentic tooling. Let's start from zero with the first pattern. This is what we all do every single day. Iterative, hill, iterative, human in the loop. (t: 240) You have your agent that calls some service. Let's keep rolling with this image generation example, right? You're working on this presentation. (t: 250) Your team's depending on you to get this finished, right? You're going to integrate with the Replicate MCP server. And this will generate the outcome you're looking for. So this is the simplest version, right? (t: 260) Iterative, human in the loop. back and forth with your agent, connecting to some service, to some MCP server, and this generates your outcomes, okay? Every approach has pros and cons, right? (t: 270) The pros here, you have direct oversight. This is simple, you can quickly review, high accuracy, high control. On the con side, you are stuck in the loop, right? (t: 280) Human in the loop, you are quite literally stuck in the loop, the scalability here is terrible. You are in fact the bottleneck. So this is the lowest hanging fruit. This is a great place to start. (t: 290) This is a terrible place to end, all right? Let's scale this up. What tools do we have? What interaction patterns do we have with our agents to do more with less? (t: 300) We of course have reusable prompts. This is a big pattern. This is the 80-20 of agent interaction patterns, okay? So here we have Gemini CLI, we have Cloud Code. (t: 310) Unfortunately, as far as I know, as far as I can tell, Codec CLI does not support reusable prompts. They need to build that in, otherwise it is not a viable solution. (t: 320) It's not a viable agentic coding tool, full stop, because this enables you to reuse your agentic prompts, right? In the Cloud world, it's .clouds slash command slash name.md. (t: 330) If you've been watching the channel, you're very familiar with this. In the Gemini world, you use these .toml files in a very similar pattern, okay? (t: 340) These are just reusable prompts. So this is an entirely separate interaction pattern, right? What we've done here is we've taken prompts and made them reusable, okay? (t: 350) Fantastic. This is where 80% of all the value is. If you wanna be Pareto efficient, you want maximum value for your time, you just build a reusable prompt, okay? You write it once, reuse all the time. (t: 360) Version control, you can iterate and improve at light speed. So reusable prompts, very powerful. You're already aware of this pattern. This is where you should go right after you're sick of running this prompt yourself. (t: 370) You wanna get out of the loop and move to reusability ASAP. Three times marks a pattern. If you need reusability, build a reusable prompt next. All right? (t: 380) So we have initial overhead setup. You now need to manage these commands somewhere, right? You need to keep track of these. I'm working in five to 10 code bases over the course of a week, (t: 390) and I lose track of which commands are where all the time. Okay? And this is an additional abstraction layer. It is thin, but it is there. Let's scale it up. (t: 400) How can we interact? How can we have our agents do more? What's the next layer? We of course have the sub agent pattern. With Cloud Code, right? No one else supports this right now, but with Cloud Code, (t: 410) as your primary agent, you can now spin up sub agents. So this is a great way for your agent to interact with outside services, because now you can create dedicated specialized agents. (t: 420) So in our image example, we can use a create image agent that we can scale up, right? We can parallelize. We can generate 20 images in no time or more, right? (t: 430) Depending on what's in our sub agents system prompt. We can also spin up edit image agents. Okay? This is a great agent interaction pattern. (t: 440) Because it allows us to interact with these outside services in a more scaled, specialized way. Okay? Right? Once we use with Cloud Code, parallelize and specialize. (t: 450) Okay? These are the big advantages of the sub agent interaction pattern, right? When you're thinking about having your agent interact with existing tools, services, endpoints, (t: 460) sub agents give you a massive edge because you can parallelize and specialize. Okay? But there are cons here. The sub agent pattern is not a free lunch at all. You can isolate the context window. (t: 470) You can do a lot of cool things inside of your sub agents, but there are problems. Okay? Right now, one of the biggest problems, I have my eye on this all the time. Cloud Code lock-in. A lot of these other patterns, right? (t: 480) If we back up here, right? Reusable prompts. This is not super unique feature. Cloud Code is definitely doing it the best, but you know, Gemini CLI has reasonable prompts. (t: 490) Okay? No one's doing sub agents right now. Cloud Code is the best agent decoding tool, full stop. We talked about this on the channel over and over and over, but there is lock-in here. Right? There's model lock-in and there's feature lock-in. (t: 500) We then of course have the gray box problem. It's not a black box, but looking into your sub agent, analyzing it, debugging it, it's not exactly simple, (t: 510) but most of all, and the biggest issue I think with the sub agent pattern is that you have to be really careful about how these prompts are coming in to your sub agents (t: 520) from your primary agent and how they're coming out of your sub agent back to your primary agent. As you know, if you've been watching the channel, you know that your primary agent prompts your sub agent (t: 530) and then your sub agent responds to your primary agent. We created a video on this very idea. Make sure you check that out. (t: 540) That was our big sub agents video. That one absolutely exploded. And for good reason, you don't want to make the mistakes that a lot of engineers are going to make when they're building sub agents. Okay? The flow of information (t: 550) in these multi-agent orchestration systems matters more than ever. And so this is another way you can scale up. Your compute. Notice how we're going from minimal to complex. (t: 560) We're going from low compute to high compute. All right? So what's next? You can, of course, write a prompt that fires sub agents. (t: 570) Going back to that image generation example, right? You need to generate a ton of images for your presentation. You want multiple versions, so you can pick the best of n, right? (t: 580) You can pick the best images, the best ideas that your agents generate. So prompt to sub agent is another way you can have your agents interact with outside services. (t: 590) Here you have cloud code. You have your agent. Call a prompt. And this prompt contains a bunch of details, right? This is your workflow definition, which then calls an entire suite, right? (t: 600) An entire team of agents to solve a specific workflow very well. Okay? And so here we are once again, scaling up our compute. (t: 610) I'm just using the replicate MCP server here as an example. Of course, not sponsored. I'm a third party actor. No sponsors at all. This is an important example. This could be replicate MCP. This could be your internal services. (t: 620) It could be other custom MCP servers. It can be API, CLI, anything. Okay? Think of this as just the external layer and your agents are interacting with the external layer and then you ultimately drive some outcome. (t: 630) What are the pros here? This is, of course, great because we create workflow reusability as code slash prompts. Really as prompts. (t: 640) The ability to create a reusable workflow that spawns specialized agents to do specific work is big. Okay? This is absolutely big. In our next agent interaction pattern, you can actually scale this even further. (t: 650) This is another huge value point. You can, in fact, pass in the number of agents you want to spin up. You could have three image generation images spin up, (t: 660) and then you could have your edit agents review the image that was generated against the original prompt, and you can have them do that end times to improve the image, (t: 670) right, to make it more like you want it in your original prompt. There are tons of ways to use. The prompt to sub agent pattern and the whole idea here is to create more reusability right (t: 680) now. You have an entire workflow with sub agents. You have more compute. Okay. And of course, speaking of compute, this is very token intensive. (t: 690) It's complex. It's hard to debug, right? You have agents doing work all over the place. Who knows if it's going to be right? This requires a lot of prompt engineering, a lot of context engineering, a lot of awareness (t: 700) of the problem that you're trying to solve and the compute that you can use along the way. The better and the more you know how your models work, the more successful you're going to be (t: 710) when building out these more complex workflows. Okay. But we can scale this even further, right? What's the next version of this? We start getting into the deterministic land where we start combining code with agents. (t: 720) How do we do that? We build a wrapper MCP server. Okay. What does that look like? Cloud code, Gemini codex. (t: 730) They're all calling your wrapper MCP server. It's not just reusable prompts or sub agents. Okay. Okay. Right. Those are all just prompts at the end of the day. System prompts or user prompts. (t: 740) What we're doing here is building out a dedicated wrapper MCP server that then calls the APIs directly. They call your custom APIs, your custom services, so on and so forth. (t: 750) And this is a dead giveaway. We'll talk about when to use each one of these patterns in just a moment. But this is a big giveaway for when you should use MCPs, right? Do you need a specific functionality? (t: 760) Do you need to add deterministic code? Do you have proprietary services that you're trying to build and do you need a concrete agent layer? And then of course, this all drives our outcome. (t: 770) All right. And so tons of pros here, right? MCP servers are ultra powerful, mostly because they create the interface layer. Really, that's what this is, right? (t: 780) What is the MCP server? It's an interface layer to give your agents the ability to solve the problem at hand with various services. And to be super clear, of course, you know, you can build out reasonable prompts that (t: 790) just call multiple MCP servers, whatever tools. You have right CLI commands. They can run bash commands directly. (t: 800) But when you want to start building out a encapsulated, reasonable solution that serves a domain set of problems very well, the wrapper MCP server is the place to go. (t: 810) Why is that? It's because you have a single integration point for all agents. So continuing with our image generation example, you know, you'll now have generate image and (t: 820) edit image as tools or and prompts inside of your dedicated wrapper MCP server. And this is great because you get full control. It's controllable and it's customizable. (t: 830) Okay. And a huge, huge, huge call out here, something that is still super underrated. I don't see enough engineers. I don't see enough MCP servers written out there with custom slash commands. (t: 840) Okay. Everyone's so focused on tools. Resources are decent, but the real value of the MCP server is in the prompts that represent (t: 850) the reusable workflow of specific tool calls. And so this. This. Is the big advantage of creating a dedicated wrapper MCP server. (t: 860) You get one place to call a set of tools, prompts and resources that you define, and you get to expose just those pieces for your agent. (t: 870) Right. And so, you know, for example, right, let's hop back to, um, Internet of human in the loop, right, right here. You're just calling the replicate MCP server. Now you have to call the replicate MCP server based on whatever tools they have defined, (t: 880) right? Whatever prompts, whatever resources they have defined. You. Have to do it their way or your agents have to do it their way. (t: 890) You have to understand their tools and their prompts. When you build out your MCP server, right? You get to do it your way. Okay. So when you build your MCP server, you can create a generate image tool, and then you (t: 900) can create a reasonable prompt, right? A custom slash command inside your MCP server. That is generate prompt batch or generate, then improve, or, you know, something like (t: 910) generate in this style, right? Generate in Ghibli style. You can define exactly what things look like. (t: 920) Okay. Highly customizable and controllable. So of course there are cons, right? You have to maintain this MCP server. And if you're not embedding an agent inside of your MCP server, which is a, another advanced (t: 930) pattern we'll talk about, uh, in just a moment, you have to build these integrations by hand, right? So now you need to grok the replicate MCP server. (t: 940) And you know, by hand, I, of course, mean you have to have your agent do it and you have to know what to prompt and what to instruct. Your agent to do, right? You basically have to build these integrations, you know, by quotes hand, you have to integrate (t: 950) with the, with the services APIs, the exact way you want it. This of course is a good thing. You're being more specific and explicit with what you want done, but there is still a cost (t: 960) to that, right? Control cost time, control, cost, effort, control, cost, your engineering resources. Okay. This is of course built for agents, not humans. (t: 970) This is an MCP server. It's not a UI. It's not a CLI. It's not an API. It's an MCP. P server, right? This is built for agents. All right. And so that leads us to the highest level, the application pattern. (t: 980) If you need the big guns, this is where you end up, right? You end up basically creating a super interface layer for, I forgot codec CLI here. (t: 990) Whoops. You, you, you end up with a super interface layer here where you have a, an entire application layer. Okay. (t: 1000) Where you have your own CLI MCP server, UI API, and just whatever you want. Okay. This is a full on. Application, right? Of course, here you get full control over what this calls, right? (t: 1010) You can just do anything. It's a full on application. Now the trick here is that in order to make this useful for your agents, right? In order for your agent to run your application and to do things with your application, you (t: 1020) either need to expose some specific CLI methods. You can expose a MCP server for your application. This is a big, big idea. (t: 1030) We're going to be discussing on the channel. Make sure you're subscribed. And then of course you have UI and your API. And your agent can operate on all of these dimensions to call specialized sub agents (t: 1040) and application code and really anything under the sun. When you get to this level, right? You're just going basically full out guns blazing. Okay. And the pros and cons are exactly what you would imagine. (t: 1050) Full control, infinitely extensible, multiple access patterns, right? Multiple access layers, CLI, MCP, UI API. But of course the cost is just through the roof to go back to our image generation example. (t: 1060) You need to build a presentation. You need to generate image assets. This is extreme overkill for that use case, right? So, you know, that brings us to our decision making framework. (t: 1070) How do we choose between all of these levels? How much compute do you really need to solve the problem at hand? I think this is a great place. (t: 1080) It's a great problem to bring back a fundamental engineering principle, right? Keep it simple scale when needed, basically solve the problem first. And if you can't solve it with that level of operation, with that much compute, with (t: 1090) the simple mechanism. Right? With the simple agent interaction pattern, then you scale it up, right? (t: 1100) Scale when needed. So everything should start with ad hoc prompt. This is your first encounter. When do you use this? When you're encountering a new problem for image generation problem, where you're building (t: 1110) out this presentation, you need a ton of images. We're talking 10, 50, hundreds of images generated very quickly. You don't want to go through the UI, right? And in fact, more and more, you want to be doing things the agentic way. (t: 1120) Let your agents do it. Find the interface and build the interface for your agent. Why? Because your agent can use more compute. And if you're using compute, you're doing more digital work. (t: 1130) We're in the age of agents, not the age of LLMs, not the age of prompts, not the age of chats, right? This is, it's all about the agent right now. (t: 1140) Okay. But you still want to start at the lowest level. And this is also helpful. There's that saying do things that don't scale. This is important so that you understand how to actually solve the problem yourself with (t: 1150) your agent. Okay. Then we get to reasonable prompts. You want to tap into this. Okay. When you've spotted a pattern, I like the rule of three. Once you've done something three times as an engineer, automate it, stop doing it. (t: 1160) Stop wasting time three times marks a pattern, codify it, create a custom slash command, AKA every usable prompt. All right. (t: 1170) What about sub agents? Now this is a, this is kind of still tricky, but I I've come to a concrete answer here for when you should use sub agents only use sub agents. (t: 1180) We need specialization and parallelization. Okay. If you don't need to paralyze and you don't. Need to specialize, just create every usable prompt, right? Keep it in your primary agent, because as soon as you start messing with sub agents, (t: 1190) you now have to manage multiple context windows. Even if you don't think you do, there is another context model prompt that you need to manage. (t: 1200) Okay. You want to do the least amount to get the greatest value. This is all about finding that sweet spot. Understanding your agent interaction patterns is how you decide where that sweet spot is (t: 1210) for the problem. You're trying to solve. Right. You want to keep it simple scale when needed. Again, let's go back to our image generation example. (t: 1220) We definitely don't want to stay at ad hoc prompts, right? Because we need to generate 10, 50, hundreds of images and we might need to edit them. Okay. So already we're talking about repetition and we're talking about specialization. (t: 1230) So we're talking about reusable prompts or sub agents. We could stop at two. We can stop at three. Do we need four or five? (t: 1240) Okay. So what is four? Four is the MCP wrapper. I am combining. Okay. Prompt workflows that call sub agents. That's still all just the sub agent pattern. All right. (t: 1250) MCP servers, right? So when do you use this? You need to expose your agent to services. You know, calling a CLI tool is not good enough. If hitting an API with curl is not good enough, right? (t: 1260) If you need to be more specific, then you build an MCP server, right? Also, if you're starting to increase the number of services you're interacting with, you probably don't want to be managing all that in a reasonable prompt or in sub agents. (t: 1270) You probably want something that pulls it all together. And then re-exposes it through a simple tool or prompt via your MCP server, right? (t: 1280) And I'm talking about MCP server tools and MCP server prompts. Prompts are insanely underrated, insanely underused. (t: 1290) If you're building an MCP server, you should build MCP prompts that exposes the common workflows you will use with the tools. Okay. (t: 1300) Or of course, if you have some unique asset, right? Some, some unique internal service that you want. Your agents to be exposed to. Okay. So this is when you upgrade to an MCP server wrappers. Now, the question is, do you need to upgrade from a sub agent or from reusable prompts (t: 1310) up to MCP servers? And I think the answer here is unless you know, you need to integrate with some personal (t: 1320) information for this presentation, right? You have some types of personal sales assets, maybe for this client, you have some information on them, or maybe you're bringing these images for multiple presentations each for a different (t: 1330) client that you're working with. Or working for, then you might want to create an MCP wrapper that puts together the replicate image functionality with your internal services. (t: 1340) And then you expose it through a single or a set of generate image tool calls and prompts. I think that's the big differentiation here. (t: 1350) Only if you need to integrate with multiple services, multiple assets, would you upgrade from sub agents and reasonable prompts to the MCP server? And then of course we have the final level, the full application. (t: 1360) Okay. Complete integration. You have full control. And you have a long-term vision, right? Basically you're building a product. Okay. If you're building a image generation product, or if you see a opportunity here to solve (t: 1370) this problem for good in your organization, for your team, then you would build a full (t: 1380) application. Okay. And why is that really the full application now is just a way to expose many ways to do something right. And what do I mean by many ways I'm talking about the interface layer. (t: 1390) What's the difference between a customer facing application. And a developer tool. It's the interface layer. If we want to move fast, if we want to get something done, we go through the terminal. (t: 1400) But if a customer wants to do something, it's all UI that hits an API. So if you need full control, if you need multiple interface layers, and you have a long-term vision for a solution for generating tons of images and editing them right with a big (t: 1410) UI, you want the full application. Now it's important to note that throughout each level of the agent interaction pattern, (t: 1420) you are increasing compute. You're increasing complexity. And you're increasing the time you need to spend to deliver the solution. Very obviously, the time it takes to create a reasonable prompt is much less than the (t: 1430) time it takes to create an MCP wrapper or a full application. Okay. Even if you have complete meta prompts for these problems, okay, which if you've been (t: 1440) watching the channel, you probably have meta prompts for one, two, and three. Stay tuned for four and five. All right. So this is great, right? (t: 1450) This is a simple pattern decision-making framework that we can use to figure out how to do this better. And we can figure out when we need what level of compute, what level of solution for a (t: 1460) specific problem. You want to move from left to right. The big idea here is start simple, right? Don't make the classic engineering mistake. Don't over-engineer. Let the patterns emerge naturally from usage. (t: 1470) If you skip step one and two, there are going to be things that you don't know about the problem and therefore that you don't know about the solution because you didn't do it (t: 1480) by hand. Okay. And you know, I should be really clear when I say by hand. Now, because I think that engineering ability is kind of all over the place right now. There's a huge gap. There's a growing gap. (t: 1490) When I say by hand, I mean with your agent. Okay. By hand should almost always mean you're having a high composition level agent do the work (t: 1500) on your behalf. Okay. So that's what I mean by hand. I should probably stop saying that. But anyway, so I really like this line, right? Complexity should be earned, not assumed. Don't assume your problem is hard, right? (t: 1510) You should never assume. That your problem is hard. You're actually causing yourself pain by assuming that you need more compute or a bigger (t: 1520) interface layer or more interface layers or, you know, a complete solution. Okay. Complexity should be earned, not assumed. And there's also a great idea embedded here. (t: 1530) You know, larger working systems are almost always built from simple working systems. So if you start here, level one, you can generate images through the replicate MCP server, (t: 1540) through. Whatever server you want to write by hand with your agent prompting back and forth, you know that the solution can be done right. (t: 1550) And right after you do this a couple times, you can immediately throw it in a reusable prompt, right? Because you've solved the problem. You know, the steps you need to go through, right? (t: 1560) For instance, for the replicate MCP server, you need to get the model name. You need to know the model name. You need to know your provider. Then you actually make the prediction and then you pull you effectively pull back to (t: 1570) the API to wait for the image to generate. And then you download it. Okay. And then you move it, right? Generating an image, right? It's almost a workflow in itself, right? (t: 1580) It's almost a reusable prompt. Okay. And then we can push it further. We can go for the sub agent pattern. Why would we do that? Because we need specialization and more specifically, we need scale, right? (t: 1590) We need parallel execution. Now you can definitely accomplish this with a reusable prompt as well, right? You can say in your prompt, you can say generate and images, but you can also do this with (t: 1600) the sub agent pattern for this specific problem of generating images for a project. For a presentation, I would stop at two. If I didn't have a lot of time, I would go to three. If I knew I needed to generate a hundred plus images and I would even put the reusable prompt (t: 1610) that generates in agents as a sub agent, right? So I would call five of my sub agents. (t: 1620) I'd say generate 10 images each. Here's, you know, five different prompts for the images, right? And then, you know, five sub agents would do that work, right? And then you can build out specialized edit image agents as well. (t: 1630) Okay. And then. Okay. Okay. Okay. Okay. Okay. Okay. Okay. If I needed to involve other services, right. Or if I wanted this to be a repeat solution with a simple interface, right. (t: 1640) I want to create image interface all embed, which model to use, all embed the aspect ratio, blah, blah, blah, right. Details. Okay. (t: 1650) And then you only go to a full application. If you have some long-term vision or you need to expose this tool to your team, to your team, I mean to your non-engineering team, the ideas are simple, right? Solve the immediate problem, recognize repeat pattern, scale incrementally. (t: 1660) Okay. What you don't want to do is premature optimization build before you validate complex solutions. First, if you don't need the compute, if you don't need the interfaces, don't build them. (t: 1670) Being an engineer is all about solving the problem. Okay. I feel like this has gotten lost somewhere. Being an engineer is all about solving the problem. (t: 1680) Gen AI gives us brand new technology to solve the problem better than ever, but we're now faced with this really interesting problem of how do we have our agent interact with new and (t: 1690) existing systems, CLIs, applications, APIs, and now the new MCP server prompts, sub-agents, (t: 1700) and whatever's coming next. Okay. I think this general framework, this is how I've been thinking about this. Keep it simple scale when needed and always go from left to right. (t: 1710) Never right to left. Right now I'm building a ton of reusable prompts. I'm building fewer sub-agents than I was before, unless I need specialization and parallelization. (t: 1720) And then every once in a while, I'm going to build a new agent. And every once in a while, I do build out a dedicated MCP server, usually to pull together multiple services or expose repeat functionality. (t: 1730) And then of course I have a couple applications that I'm building out to solve problems, right? These are just full on full scale applications where you have a long-term vision for, right? (t: 1740) So for instance, I have every single layer here built in to agenticengineer.com. This is where I host principal AI coding. And of course the next phase two agentic coding course. (t: 1750) Okay. So the countdown is on. I'm really excited to release that at the end of September. Stay tuned, stay locked in. We are in the age of agents and all the winning engineers are mastering the agent architecture (t: 1760) and agent interaction patterns. Comment down below. Let me know which agent interaction pattern you're using the most. (t: 1770) No matter what, stay focused and keep building.

