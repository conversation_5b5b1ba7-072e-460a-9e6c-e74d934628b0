---
title: The BEST Way To Use Claude Code Anywhere (Thanks <PERSON>)
artist: Better Stack
date: 2025-08-21
url: https://www.youtube.com/watch?v=N434yhINnHo
---

(t: 0) AI coding assistants in the terminal are blowing up right now. It feels like everyone is using them on their desktop machines and not on their phones, because they don't work that well on phones, right? (t: 10) But what if I told you, thanks to this tweet from Levels.io, I now know how you can vibe code anywhere with just three tools and maybe one of these? (t: 20) Do you know what? Let me just show you. And before we get started, make sure you hit that subscribe button. Okay, so we usually install coding assistants on desktop machines for easy access, (t: 30) and so that they can run in the background while we do other important tasks. But because phones are less powerful and go to sleep more often, (t: 40) it's better to install the assistant, in this case, Claude Code on a remote server than connect to it through a terminal running on your phone via SSH. (t: 50) Which means if your phone runs out of battery or gets lost, your project doesn't go along with it. To set that up, you could use an EC2 instance or a DigitalOcean Droplet. (t: 60) But personally, I'm a huge fan of <PERSON><PERSON><PERSON>, because you can't beat four bucks a month for a server that's always on. So once you've created an SSH key locally, (t: 70) added it to Hetzner and then created your server, connect to it, update the packages, install Node with NVM, which is a bit more straightforward, then install Claude Code and connect it to your Anthropic account. (t: 80) If you have any custom hooks or subagents you use regularly, make sure to bring them over at this stage, because this is super fiddly to do on a phone. (t: 90) Let's also add a project from GitHub and double check that Claude Code is working. The next step is to get a terminal app for your phone. (t: 100) If you want just an SSH client, then get Terminus, which works on both iOS and Android. But if you want the full terminal experience on your phone, then use Termux, which is Android only. (t: 110) Let's go through how to set up both of them. For Terminus, you can set up a new SSH key by either generating one or copying over the private key of an existing one, (t: 120) which it will use to generate a public key. Then we need to create a new host with an alias and the IP address of the remote server with a username, we'll use Roots for now, but this should usually be your own user. (t: 130) Then add your SSH key and you should be able to interact with your server. Please note, if you've generated a new SSH key, (t: 140) you'll have to go into the authorized keys file in your Hetzner server and add it manually before you can get access. Now let's move on to Terminus, which is a bit harder. (t: 150) After updating the packages, install OpenSSH. You can also install the fish shell for nice colors and a visual autocomplete. Then generate a new SSH key, add that to Hetzner, connect with this command (t: 160) and then you should have access to cloud code from your phone, which is really cool, but it could be better. (t: 170) Right now we're connecting over regular SSH, which is fine for desktops, but not ideal for phones since it doesn't support roaming. (t: 180) So moving from a Wi-Fi to a cellular connection. So to make things better, we could use MOSH, the mobile shell, which addresses these issues because it uses UDP instead of TCP for the connection, (t: 190) which is better for handling network interruptions. And it's also able to keep the session alive if the connection is lost and automatically reconnects. (t: 200) MOSH uses its own client and server to open UDP connections. So it first needs to be installed on the Hetzner server and also in Termux. (t: 210) Then we can update our SSH command from this to this, which works as expected. But what about Terminus? Well, MOSH is actually built in. (t: 220) Just edit the connection and turn on MOSH. Now, I know a lot of you out there have home servers like Asynology, which could also be used instead of a Hetzner server. (t: 230) You'll have to enable SSH firewall rules if you have it turned on. And in my opinion, I would have Clawed Code installed on a separate (t: 240) Docker container so it's isolated from the rest of the system. But if you're interested, I can go through all that in more detail in a separate video. And that, my friends, is how I would connect to Clawed Code using my mobile phone. (t: 250) Now, I know there are tools like Vibe Tunnel that can make the whole process much easier, but I prefer having access (t: 260) to an actual terminal on my phone instead of using a terminal through a browser. Speaking of, mobile terminals, I'm going to go off on a tangent and say if you want Termux to (t: 270) look as good as Terminus, there is a separate app called Termux Style that costs a bit of money but allows you to change fonts, themes and a few other things. (t: 280) There's also an app called Kisuke, hope I'm pronouncing that correctly, that isn't yet released but does look promising when it comes to connecting to something like Clawed Code on your phone. (t: 290) Anyway, I've given you enough information to get started. Hope you enjoy Vibe Coding while your girlfriend is shopping. Add any questions to the comments. Don't forget to subscribe. And until next time, happy coding.

