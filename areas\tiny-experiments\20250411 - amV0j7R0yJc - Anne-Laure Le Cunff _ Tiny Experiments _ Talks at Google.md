---
title: <PERSON><PERSON><PERSON><PERSON> | Tiny Experiments | Talks at Google
artist: Talks at Google
date: 2025-04-11
url: https://www.youtube.com/watch?v=amV0j7R0yJc
---

- [00:00:00](https://www.youtube.com/watch?v=amV0j7R0yJc&t=0) - <PERSON>, Google School for Leaders, Google's internal center of excellence for all things manager and leadership development. Welcome to Talks at Google. I'm <PERSON>. I have the privilege of heading up the Google School for Leaders, which is Google's internal center of excellence for all things manager and leadership development. Today, I am delighted to welcome <PERSON><PERSON><PERSON>, who is a an award-winning neuroscientist and entrepreneur. She founded Nest Labs, a platform supporting healthier ways to work and learn,

- [00:00:30](https://www.youtube.com/watch?v=amV0j7R0yJc&t=30) - an award-winning neuroscientist and entrepreneur. She founded Nest Labs, a platform supporting healthier ways to work and learn, and her newsletter is currently read by more than 100,000 knowledge workers. Her research at King's College London focuses on the neuroscience of lifelong learning and curiosity, which I'm sure we're all very fascinated to learn more about. Her book, Tiny Experiment, is a transformative guide for living a more experimental life, turning uncertainty into curiosity, and carving a path of self-discovery. Previously, she worked at Google as an executive on digital health projects,

- [00:01:00](https://www.youtube.com/watch?v=amV0j7R0yJc&t=60) - is a transformative guide for living a more experimental life, turning uncertainty into curiosity, and carving a path of self-discovery. Previously, she worked at Google as an executive on digital health projects, such as Wear OS apps and Google Fit. Her work has been featured in Wired, Forbes, and the Financial Times. Please join me in welcoming Anne-Laura to Google. Thank you. I am so happy to be here today.

- [00:01:30](https://www.youtube.com/watch?v=amV0j7R0yJc&t=90) - Thank you. I am so happy to be here today. I actually started my career right here in this office. So being back and being able to share some of my work and research since I left Google feels particularly special. So thank you for having me. I'm going to start by sharing a photo I have never, ever shared publicly before. So as I mentioned, I used to work at Google. I started in London, and then I moved to San Francisco. And it was my dream job.

- [00:02:00](https://www.youtube.com/watch?v=amV0j7R0yJc&t=120) - I have never, ever shared publicly before. So as I mentioned, I used to work at Google. I started in London, and then I moved to San Francisco. And it was my dream job. I also was constantly worried that someone would figure out that they had made a hiring mistake, that I didn't belong there with all of these smart people. And as a result, I responded to that uncertainty by saying yes to absolutely everything and by desperately seeking a sense of control. So my task list looked something like that. I was also trying to time box every single gap in my calendar. I was over planning, over committing, and I was the yes girl in the office.

- [00:02:30](https://www.youtube.com/watch?v=amV0j7R0yJc&t=150) - and by desperately seeking a sense of control. So my task list looked something like that. I was also trying to time box every single gap in my calendar. I was over planning, over committing, and I was the yes girl in the office. Anything you needed me to do, I would say yes to. I was dangerously dancing with burnout. But I loved my job. I loved my team. And I loved our mission. So I kept on pushing through. Until one day, I was in front of the mirror, brushing my teeth, getting ready for work. And I noticed that my entire arm had turned purple. So I went to the Google infirmary in Mountain View.

- [00:03:00](https://www.youtube.com/watch?v=amV0j7R0yJc&t=180) - So I kept on pushing through. Until one day, I was in front of the mirror, brushing my teeth, getting ready for work. And I noticed that my entire arm had turned purple. So I went to the Google infirmary in Mountain View. And the nurse had one look at my arm and said, you need to go to the hospital right now. I went to the hospital, and there the doctor said, we need to perform surgery as quickly as possible. You have a blood clot in your arm that's threatening to travel to your lungs. And what did I do in that moment in front of the doctors? I said, one second. I need to check my calendar. And right there in the doctor's office, I opened my calendar,

- [00:03:30](https://www.youtube.com/watch?v=amV0j7R0yJc&t=210) - And what did I do in that moment in front of the doctors? I said, one second. I need to check my calendar. And right there in the doctor's office, I opened my calendar, and I proceeded to thoroughly check that the moment we would schedule that surgery would not conflict with any of the product launches I was working on. So as you can see, my arm is fine. It healed. We actually took that photo right after the surgery. But this moment stayed with me. It made me reconsider our entire relationship to work. It made me ask, why is it that so many of us push ourselves to the edge

- [00:04:00](https://www.youtube.com/watch?v=amV0j7R0yJc&t=240) - But this moment stayed with me. It made me reconsider our entire relationship to work. It made me ask, why is it that so many of us push ourselves to the edge in the name of productivity? And it made me question our relationship to uncertainty in our personal and professional lives. This is actually a really important question. It's a really interesting paradox. As knowledge workers, we are hired for our capacity to solve problems, to think creatively, to deal with complexity. But somehow, when we're faced with uncertainty,

- [00:04:30](https://www.youtube.com/watch?v=amV0j7R0yJc&t=270) - As knowledge workers, we are hired for our capacity to solve problems, to think creatively, to deal with complexity. But somehow, when we're faced with uncertainty, we have this tendency to want to feel in control, to seek certainty. And because of that, we sometimes push ourselves to extremes, such as checking your calendar when someone is asking you to go to work and schedule your surgery. To understand that, we need to go back to the very definition of success. So success is something we all want. We praise it. We admire it. We want it. We also spend a lot of time trying to measure it. We have KPIs at Google, OKRs, performance reviews.

- [00:05:00](https://www.youtube.com/watch?v=amV0j7R0yJc&t=300) - So success is something we all want. We praise it. We admire it. We want it. We also spend a lot of time trying to measure it. We have KPIs at Google, OKRs, performance reviews. We also very often measure our own success based on the success of others. It's as if we were all looking at a giant leaderboard, constantly asking, who's doing better, bigger, faster work? So what is the success that we're all so ardently chasing? If you open a dictionary, the most common definition of success you will find is something like this, reaching a desired outcome.

- [00:05:30](https://www.youtube.com/watch?v=amV0j7R0yJc&t=330) - So what is the success that we're all so ardently chasing? If you open a dictionary, the most common definition of success you will find is something like this, reaching a desired outcome. Now, please indulge me while I break down this definition, and we try to understand what is this definition we've all agreed on as a society. Reaching, there's the idea of movement. Progressing towards something. Towards what? Something we desire. Something we feel like is positive. And that thing is an outcome, which implies that in order to be successful, you need to reach a specific destination. This might seem like the only obvious definition of success, but it's not.

- [00:06:00](https://www.youtube.com/watch?v=amV0j7R0yJc&t=360) - And that thing is an outcome, which implies that in order to be successful, you need to reach a specific destination. This might seem like the only obvious definition of success, but it's not. This definition of success is actually based on something called linear goals. Linear goals are based on the idea that in order to be successful, you need to reach a specific goal. And that goal is based on a clear vision and a clear plan. And if you start looking around you, you'll notice that those linear goals are everywhere in our life and in our work. So we have four-year university degrees, followed by a five-year career plan, followed by a 30-year mortgage. And even in the way we manage our work on a daily basis, we might say,

- [00:06:30](https://www.youtube.com/watch?v=amV0j7R0yJc&t=390) - are everywhere in our life and in our work. So we have four-year university degrees, followed by a five-year career plan, followed by a 30-year mortgage. And even in the way we manage our work on a daily basis, we might say, here are the features we're going to launch in the fall, and here are our sales targets, and here are the marketing activities that we'll put into place in order to reach that goal. And it feels really good to have this sense of certainty. But there's only one problem with linear goals. It's that we don't live in a linear world. We live in a non-linear world. You have market trends that keep on shifting, new technologies that can disrupt your industry, and as we've seen recently, global events that can change everything overnight.

- [00:07:00](https://www.youtube.com/watch?v=amV0j7R0yJc&t=420) - We live in a non-linear world. You have market trends that keep on shifting, new technologies that can disrupt your industry, and as we've seen recently, global events that can change everything overnight. And so things rarely go to plan. And so we have to be very careful about what we're doing. Instead of going from point A to point B in a very neat way, we find ourselves navigating this complex web of twists and turns with unproductivity at each crossroads. And what do we do when we can't achieve our goals? We blame ourselves. And very often, we might even want to hide our failures from others.

- [00:07:30](https://www.youtube.com/watch?v=amV0j7R0yJc&t=450) - with unproductivity at each crossroads. And what do we do when we can't achieve our goals? We blame ourselves. And very often, we might even want to hide our failures from others. In today's world, in this non-linear world, clinging to linear goals can often be a problem. It can only lead to frustration, to overwhelm, and very often, to burnout. So you know who has a completely different definition of success? Scientists. For a scientist, success is not reaching a specific destination. Success is learning something new.

- [00:08:00](https://www.youtube.com/watch?v=amV0j7R0yJc&t=480) - So you know who has a completely different definition of success? Scientists. For a scientist, success is not reaching a specific destination. Success is learning something new. Whatever the outcome, whatever the results, they're able to look at it without self-blame or self-judgment. And today, I want to encourage you to think about this. I want to convince you to start treating your work, and maybe your entire life, like a laboratory. I want to convince you that being curious is much more powerful than feeling certain. I want you to start imagining what it would look like if we approached any challenge as an opportunity for experimentation.

- [00:08:30](https://www.youtube.com/watch?v=amV0j7R0yJc&t=510) - I want to convince you that being curious is much more powerful than feeling certain. I want you to start imagining what it would look like if we approached any challenge as an opportunity for experimentation. And to do that, we're going to study how to develop an experimental mindset. So the first thing is, how do scientists react when they get an unexpected result? When a scientist doesn't get what they expected, they don't go like, shame, shame, shame, I'm such a bad scientist. No, they look at it and they ask themselves, huh, what's going on here? What can we learn from this? And this is because they understand that we need failure to learn.

- [00:09:00](https://www.youtube.com/watch?v=amV0j7R0yJc&t=540) - shame, shame, shame, I'm such a bad scientist. No, they look at it and they ask themselves, huh, what's going on here? What can we learn from this? And this is because they understand that we need failure to learn. Failure is an inherent part of learning. What's interesting is that this kind of thinking, knowing that failure is a part of learning, is actually aligned with the way your brain works. Or, should I say, the way your brain would like to work if you didn't force it to follow linear goals. So the way your brain works is based on something neuroscientists call the perception-action cycle. It's fairly simple.

- [00:09:30](https://www.youtube.com/watch?v=amV0j7R0yJc&t=570) - the way your brain would like to work if you didn't force it to follow linear goals. So the way your brain works is based on something neuroscientists call the perception-action cycle. It's fairly simple. So first, you're going to perceive some information in the environment, some data. Based on that data, your brain is going to formulate, a hypothesis, a prediction. Sometimes, that prediction is correct. And it's great. Sometimes, the prediction is wrong. And that's okay too. As long as you're still alive and you're not dead, because of that wrong prediction, your brain is going to use that new data, that new information, and make a new prediction.

- [00:10:00](https://www.youtube.com/watch?v=amV0j7R0yJc&t=600) - And that's okay too. As long as you're still alive and you're not dead, because of that wrong prediction, your brain is going to use that new data, that new information, and make a new prediction. And this is how you learn through experimentation. Where it gets a little bit more complicated is that your brain is also optimized. It's optimized for survival. So it's trying to reduce uncertainty as quickly as possible. And that makes sense from an evolutionary perspective. If you think back on our ancestors and the environment in which they were evolving, the more information you had, whether it's, where are the resources? What's that weird noise in the bushes over there?

- [00:10:30](https://www.youtube.com/watch?v=amV0j7R0yJc&t=630) - If you think back on our ancestors and the environment in which they were evolving, the more information you had, whether it's, where are the resources? What's that weird noise in the bushes over there? The more you knew, the more certainty you had, the more likely you were to survive. But I think we all agree that, whether it comes to our work, our relationships, our health, us modern humans want more than just surviving. We want to thrive. And in order to do this, we need to replace this desperate need for certainty with curiosity instead. You're still using your perception action cycle,

- [00:11:00](https://www.youtube.com/watch?v=amV0j7R0yJc&t=660) - We want to thrive. And in order to do this, we need to replace this desperate need for certainty with curiosity instead. You're still using your perception action cycle, but instead of trying to resolving uncertainty as quickly as possible, you're using that uncertainty as an opportunity to learn and to grow. What's amazing is that there is research showing that when you approach challenges with this curious way, in a more experimental way, not only you're going to find solutions faster, but you're also going to experience less anxiety and stress in the process, which is pretty neat.

- [00:11:30](https://www.youtube.com/watch?v=amV0j7R0yJc&t=690) - in a more experimental way, not only you're going to find solutions faster, but you're also going to experience less anxiety and stress in the process, which is pretty neat. The reason why scientists are so good at this, at having this experimental mindset, is not because they're smarter, than all of us. It's because they've been trained to do so. So for anyone who had studied science at school, you probably remember this experimental cycle. And again, it's quite simple. You start with observation, where you ask, what is the current situation? Then based on that current situation, you formulate a hypothesis. You ask, what could be different? Then you start data collection.

- [00:12:00](https://www.youtube.com/watch?v=amV0j7R0yJc&t=720) - You start with observation, where you ask, what is the current situation? Then based on that current situation, you formulate a hypothesis. You ask, what could be different? Then you start data collection. You test that hypothesis. And finally, you analyze the data. And based on those results, you update your observation, so you can design your next experiment. And this is the experimental cycle. It can only work when you pair action with reflection. So you need to do something, look at the result, and then change the way you behave based on that information that you just collected. When you use them, all three of them together, these are sorts of like equations

- [00:12:30](https://www.youtube.com/watch?v=amV0j7R0yJc&t=750) - look at the result, and then change the way you behave based on that information that you just collected. When you use them, all three of them together, these are sorts of like equations that form an operating system for how to develop an experimental mindset. So first, knowing that failure is an inherent part of learning. Believing that curiosity always, always beats certainty. And always pairing action with reflection. Reflection with action. When you use this operating system, not only are you going to be able to navigate uncertainty in a smoother way,

- [00:13:00](https://www.youtube.com/watch?v=amV0j7R0yJc&t=780) - Reflection with action. When you use this operating system, not only are you going to be able to navigate uncertainty in a smoother way, but again, you're going to be able to keep your sanity in the process and not feel as stressed and lost. And you're going to be able to keep your sanity in the process and not feel as stressed and lost. And you're going to be able to keep your sanity in the process and not feel as stressed and lost. So, the next thing that I want to show you is a very important part of the experiment. Because it's so important, it's not just about the experiment. It's about the practice. And I'm going to show you how you can actually apply this

- [00:13:30](https://www.youtube.com/watch?v=amV0j7R0yJc&t=810) - is a very important part of the experiment. Because it's so important, it's not just about the experiment. It's about the practice. And I'm going to show you how you can actually apply this to any kind of experiment. So, this is a very, very simple method. It's called the I know that the kind of people who would be interested in this talk are the kind of people who are probably problem solvers, who like getting things done, who are quite creative. And I know it's exciting to execute on something, but not so fast. When you want to design an experiment, it always, always starts with observation.

- [00:14:00](https://www.youtube.com/watch?v=amV0j7R0yJc&t=840) - who are quite creative. And I know it's exciting to execute on something, but not so fast. When you want to design an experiment, it always, always starts with observation. And I like to call this self-anthropology. Because just like an anthropologist goes and studies a new culture with no preconceptions whatsoever, you can actually study the way you think, the way you live, the way you work, and pretend that you don't know anything about the way things are done. And really taking notes and asking yourself, why are we doing things the way we are? This is really an exercise in paying attention.

- [00:14:30](https://www.youtube.com/watch?v=amV0j7R0yJc&t=870) - and pretend that you don't know anything about the way things are done. And really taking notes and asking yourself, why are we doing things the way we are? This is really an exercise in paying attention. Paying attention to how we are. And how things are, so you can start imagining how they could be. And this is what will help you plant the seed of a hypothesis. So you start with observation, which then allows you to imagine something that you might want to try. Then you're ready to create your mini protocol for experimentation. When you want to experiment, you don't have to have a full, complicated experiment like a scientist would in the lab. You only need two ingredients.

- [00:15:00](https://www.youtube.com/watch?v=amV0j7R0yJc&t=900) - Then you're ready to create your mini protocol for experimentation. When you want to experiment, you don't have to have a full, complicated experiment like a scientist would in the lab. You only need two ingredients. First, you need to know what you're going to test. And second, you need to know the number of trials. So you need to know the action and the duration. And that's it. This is your protocol for experimentation. I call this a pact because it's a commitment to curiosity. It's a commitment to collecting the data and withhold judgment until you have the results. You say, I'm going to commit to trying this thing for this duration. And it's very important to commit to the duration

- [00:15:30](https://www.youtube.com/watch?v=amV0j7R0yJc&t=930) - It's a commitment to collecting the data and withhold judgment until you have the results. You say, I'm going to commit to trying this thing for this duration. And it's very important to commit to the duration before you get started. First, you need to know if something is working or not. If not, it might be just a coincidence. Second, if you don't commit to the duration in advance, you might be tempted to stop the experiment in the middle if you're not seeing what you want to see. This is why scientists decide the number of trials in advance. This way you avoid confirmation bias, finding the result that you actually want to find, and they withhold judgment until the end

- [00:16:00](https://www.youtube.com/watch?v=amV0j7R0yJc&t=960) - This is why scientists decide the number of trials in advance. This way you avoid confirmation bias, finding the result that you actually want to find, and they withhold judgment until the end when they can actually look at it and analyze all of the data and put it together. I want to show you how flexible this approach is. You can use tiny experiments for literally anything. You can experiment with acquiring new skills, with trying new tools, with doing new research, with connecting with new people. Here I focused on work-related examples, but you can actually use tiny experiments in literally anything. I've seen people run tiny experiments

- [00:16:30](https://www.youtube.com/watch?v=amV0j7R0yJc&t=990) - with doing new research, with connecting with new people. Here I focused on work-related examples, but you can actually use tiny experiments in literally anything. I've seen people run tiny experiments to experiment with their health, with their health, with meditation, with creative hobbies, and even with dating. So it works with literally anything. The last step once you're done collecting your data is to reflect on the results. This is the moment where you take some notes, where you might want to discuss it with other people, and where you share with others what you learned.

- [00:17:00](https://www.youtube.com/watch?v=amV0j7R0yJc&t=1020) - This is the moment where you take some notes, where you might want to discuss it with other people, and where you share with others what you learned. This is the last part of the experimental cycle. And this part is extremely important to learn from the experiment and to close this loop so you can implement whatever you learned into the next cycle of experimentation. This is what is allowing you to not just keep going in circles, but really grow through the cycles. And this is what people call growth loops. This is where you grow through each loop that you close, implementing the data into the next experiment. So how do you embody

- [00:17:30](https://www.youtube.com/watch?v=amV0j7R0yJc&t=1050) - And this is what people call growth loops. This is where you grow through each loop that you close, implementing the data into the next experiment. So how do you embody this experimental mindset? How can you actually learn and lead like a scientist? The great thing about tiny experiments is that you actually don't need to get any kind of buy-in from anyone. You just need to notice that maybe something might be worth trying, and that's enough to design a tiny experiment. That being said, we can actually grow and learn better and much faster when we experiment together. And to do this, we need to reimagine

- [00:18:00](https://www.youtube.com/watch?v=amV0j7R0yJc&t=1080) - and that's enough to design a tiny experiment. That being said, we can actually grow and learn better and much faster when we experiment together. And to do this, we need to reimagine some of the ways that we envision leadership. So whether you're leading a project or a team, you might be putting a lot of pressure on yourself to look like you know where you're going, to look like you're the expert, to look like you have all of the answers. But one of the most powerful things that you can do as a leader when faced with uncertainty is saying, I don't know, but let's figure it out together.

- [00:18:30](https://www.youtube.com/watch?v=amV0j7R0yJc&t=1110) - But one of the most powerful things that you can do as a leader when faced with uncertainty is saying, I don't know, but let's figure it out together. This is how you open a space for experimentation. And for learning in public, where everybody is learning together, including from our failures. Even better, you can encourage people around you, actively encourage them to design their own tiny experiments. And this way, you can unlock social flow, where that information is flowing in between team members, and everybody can grow together.

- [00:19:00](https://www.youtube.com/watch?v=amV0j7R0yJc&t=1140) - their own tiny experiments. And this way, you can unlock social flow, where that information is flowing in between team members, and everybody can grow together. So for instance, what might it look like if, with your team, you had a small, lengthy curiosity circle where everybody would share their experiments? What worked and what didn't? This is a great way to learn together and to create that safe space for experimentation as a team. For all of this to work, though, we need to redefine success. Not as a fixed destination that is based on linear goals, but as something that we learn together.

- [00:19:30](https://www.youtube.com/watch?v=amV0j7R0yJc&t=1170) - For all of this to work, though, we need to redefine success. Not as a fixed destination that is based on linear goals, but as something that we learn together. Success is learning something new. Thank you. Ultimately, this shift in mindset is all about defaulting to curiosity. It's about learning to fall in love again with problems. It's about letting go of the fear of failure, the imposter syndrome, the analysis paralysis. It's about internalizing the belief that if you approach it with curiosity, any challenge

- [00:20:00](https://www.youtube.com/watch?v=amV0j7R0yJc&t=1200) - the imposter syndrome, the analysis paralysis. It's about internalizing the belief that if you approach it with curiosity, any challenge can be an opportunity for growth and discovery. Tiny experiments can lead to big changes. Imagine a culture where it's completely normal to walk around and to ask people, what have you been experimenting with? What did you learn? What was your latest failure? Not only would this be the kind of culture where there's more space for innovation and imagination, but that would also be

- [00:20:30](https://www.youtube.com/watch?v=amV0j7R0yJc&t=1230) - What was your latest failure? Not only would this be the kind of culture where there's more space for innovation and imagination, but that would also be the kind of culture where we value people, not based on the quantity of answers they provide, but based on the quantity of the questions they ask. And this, this is the power of an experimental mindset. Thank you. Thank you for such a bold

- [00:21:00](https://www.youtube.com/watch?v=amV0j7R0yJc&t=1260) - Thank you for such a bold invitation to consider how we are operating and what we might need from ourselves in a world that is consumed with certainty and knowing and specific goals. I see such possibility and opportunity in the ideas that you share and so I'm really excited that we have this opportunity to be able to discuss them together. So let's start with this notion of certainty.

- [00:21:30](https://www.youtube.com/watch?v=amV0j7R0yJc&t=1290) - and opportunity in the ideas that you share and so I'm really excited that we have this opportunity to be able to discuss them together. So let's start with this notion of certainty. You talk about how we are wired for certainty, and I think that's one of the things that we see. What are ways in which we can become more comfortable with uncertainty? Experimentation is obviously one route, but how should we be thinking about that? And just embracing that discomfort. I think the first step is to acknowledge the fact that it's completely normal to feel anxiety when we're faced with uncertainty. Again, that's what our brains are designed for,

- [00:22:00](https://www.youtube.com/watch?v=amV0j7R0yJc&t=1320) - that discomfort. I think the first step is to acknowledge the fact that it's completely normal to feel anxiety when we're faced with uncertainty. Again, that's what our brains are designed for, to reduce that uncertainty. And so whenever we're faced with a situation where we're not quite sure what's going on, we're not quite sure what the threats are, what the risks are, who the other players are, our brain wants to reduce that uncertainty as quickly as possible. And so I think there is sometimes a lot of self-blame around the uncertainty where you feel like, how come everybody looks so comfortable and I'm so scared? So I think that's the first step, knowing that that's normal and it's probably the case

- [00:22:30](https://www.youtube.com/watch?v=amV0j7R0yJc&t=1350) - self-blame around the uncertainty where you feel like, how come everybody looks so comfortable and I'm so scared? So I think that's the first step, knowing that that's normal and it's probably the case that other people around you are just a bit better at hiding it, but we're all feeling scared when we're uncertain. So I think that's step number one. And number two, this is why the book is called Tiny Experiments. I think we don't have to necessarily go for something really big and scary straight away. We can start with something very small. And this is how you start building that muscle of playing with uncertainty and having this relationship with it which is more similar to a scientist when they see something they don't understand,

- [00:23:00](https://www.youtube.com/watch?v=amV0j7R0yJc&t=1380) - building that muscle of playing with uncertainty and having this relationship with it which is more similar to a scientist when they see something they don't understand, they actually light up. They actually feel like, ooh, juicy, there's something interesting here. And you start tiny, you start by looking at little things you don't understand and the more you do this and the more you experiment, the more you're going to find yourself in situations where you're so out of your depth but somehow you feel like that's exciting. So I think one of the things you talk about there is the importance of observation as you talk about

- [00:23:30](https://www.youtube.com/watch?v=amV0j7R0yJc&t=1410) - where you're so out of your depth but somehow you feel like that's exciting. So I think one of the things you talk about there is the importance of observation as you talk about and just seeing different things and how you're reacting to them. And how can we improve our skill of observation and do it more frequently? There's a uniquely human capability that's called metacognition and scientists love jargon but it really just means thinking about thinking. So we know that most mammals are able to think. Anyone who has a pet here would know that you look at a cat or a dog you know they can think, right?

- [00:24:00](https://www.youtube.com/watch?v=amV0j7R0yJc&t=1440) - So we know that most mammals are able to think. Anyone who has a pet here would know that you look at a cat or a dog you know they can think, right? But us humans, we're able to observe our own thoughts, observe our own emotions, our own behaviors. And I think this is a great way to start with observation by turning that eye towards yourself, that attention towards yourself. We're usually better at observing the outside world. We're happy to observe what's going on, take a few notes, share them with the team. But it's a bit more uncomfortable to just observe how we're feeling. The tension, the uncertainty, the anxiety that we can have when we're navigating

- [00:24:30](https://www.youtube.com/watch?v=amV0j7R0yJc&t=1470) - We're happy to observe what's going on, take a few notes, share them with the team. But it's a bit more uncomfortable to just observe how we're feeling. The tension, the uncertainty, the anxiety that we can have when we're navigating challenging moments at work and in our personal lives. So what everyone can do is just taking a little bit of time every day to write a few notes and just observe. How was today? How did you feel? Not just the external measures of how things went but how did it feel internally? And by practicing doing this you'll become a lot better at just by default observing how things are around you before making any decision. So journaling and reflection is that best done individually

- [00:25:00](https://www.youtube.com/watch?v=amV0j7R0yJc&t=1500) - And by practicing doing this you'll become a lot better at just by default observing how things are around you before making any decision. So journaling and reflection is that best done individually or can I do it with other people? Is there a better way? The better way is the way you actually do. I know a lot of people journaling is great. There's so much research showing how good it is for your mental health and your creativity but the fact is lots of people don't like it. That's why I tell people if that's not working for you there are lots of other ways to engage with active observation in this way. If for you it's finding either a friend or a colleague you feel quite close to

- [00:25:30](https://www.youtube.com/watch?v=amV0j7R0yJc&t=1530) - That's why I tell people if that's not working for you there are lots of other ways to engage with active observation in this way. If for you it's finding either a friend or a colleague you feel quite close to and say, hey, once every couple of weeks let's grab coffee together and this is just to share how we're doing. And that's it. And we can talk about work we can talk about our mental health about creative projects we're exploring but that forces you when you verbalize what is going on in your mind, with your emotions it really forces you to understand them and articulate them. Whether you do this through writing in a journal or through talking with someone it doesn't really matter as long as you do it. Got it.

- [00:26:00](https://www.youtube.com/watch?v=amV0j7R0yJc&t=1560) - it really forces you to understand them and articulate them. Whether you do this through writing in a journal or through talking with someone it doesn't really matter as long as you do it. Got it. I can imagine I can imagine I can imagine people wondering in a world that's already feeling very full and feeling somewhat overwhelmed I can imagine people saying I just don't have time for that and I just can't find the time and I'm not really sure why that's going to be beneficial what would you say to them in that context? I would say just experiment with it that's why

- [00:26:30](https://www.youtube.com/watch?v=amV0j7R0yJc&t=1590) - can't find the time and I'm not really sure why that's going to be beneficial what would you say to them in that context? I would say just experiment with it that's why my book is actually not that prescriptive in terms of how you implement these things because I think it might look different for a lot of people the idea here is to just experiment with different ways for you to pay attention to how you feel your productivity, the way you work with other people the way you communicate with other people the way you lead and relate to other people and then running tiny experiments so you can see what works and what doesn't and adjusting your approach adapting instead of sticking to the same rigid approach over and over again

- [00:27:00](https://www.youtube.com/watch?v=amV0j7R0yJc&t=1620) - the way you lead and relate to other people and then running tiny experiments so you can see what works and what doesn't and adjusting your approach adapting instead of sticking to the same rigid approach over and over again and for some people that might look like taking only two minutes a week to reflect on the important things that came up in the past week so I don't believe that nobody has time for it we also know that we spend a lot of time doing other things that are not so good for us if you took five minutes out of scrolling on your phone and used that for self reflection you would probably benefit a lot from it so it's really a matter of not having enough time

- [00:27:30](https://www.youtube.com/watch?v=amV0j7R0yJc&t=1650) - if you took five minutes out of scrolling on your phone and used that for self reflection you would probably benefit a lot from it so it's really a matter of not having enough time it's more a matter of not having found the right way for you to do this and to find this way you can't find that through just reading a book you actually need to experiment and see what works for you the key is the word tiny that it really can be small yes at least when you start it can grow bigger if something you like is working really well for you so an example of tiny experiments that I actually

- [00:28:00](https://www.youtube.com/watch?v=amV0j7R0yJc&t=1680) - yes at least when you start it can grow bigger if something you like is working really well for you so an example of tiny experiments that I actually run myself I used to be terrified of public speaking I'm talking terrified as in stomach cramps, nightmares for weeks before I had any kind of presentation and so I asked myself what is a tiny tiny tiny the tiniest of experiment I could run around this and so I said for the next ten days I'm going to record myself with my phone for one minute and I'm going to post it on Instagram unedited one minute

- [00:28:30](https://www.youtube.com/watch?v=amV0j7R0yJc&t=1710) - and so I said for the next ten days I'm going to record myself with my phone for one minute and I'm going to post it on Instagram unedited one minute it was absolutely terrifying but after a few days I could already feel like opening my phone and starting recording myself was less and less scary after that I finished that experiment that duration for this pact and I said okay actually I think I kind of liked it towards the end I started liking it so what is a slightly more ambitious version of this experiment and so for this one I said every month I'm going to find an online workshop that I can present

- [00:29:00](https://www.youtube.com/watch?v=amV0j7R0yJc&t=1740) - towards the end I started liking it so what is a slightly more ambitious version of this experiment and so for this one I said every month I'm going to find an online workshop that I can present not ready to go on stage or anything like that yet but from the comfort of my home I can say hey pyjamas I can do this and I did that once I completed that duration I said and I'm currently doing this one where I said once a quarter I need to find something quite big and scary where I need to be on stage in person and that's my second one and I'm already starting to feel a little bit less anxious so you can keep them tiny and you should certainly start tiny but if in the process of experimenting

- [00:29:30](https://www.youtube.com/watch?v=amV0j7R0yJc&t=1770) - in person and that's my second one and I'm already starting to feel a little bit less anxious so you can keep them tiny and you should certainly start tiny but if in the process of experimenting you discover that something is quite interesting and you might want to grow through this and experiment more you can also do that and make them a little bit bigger I appreciate the fact that you've experimented in that way because by doing that you've shared with us a gift of your knowledge and we would never have that had you not been able to have done that so thank you one of the things I find fascinating when I looked at how you were describing experiments was this

- [00:30:00](https://www.youtube.com/watch?v=amV0j7R0yJc&t=1800) - been able to have done that so thank you one of the things I find fascinating when I looked at how you were describing experiments was this the fact that there isn't actually a hypothesis there so it says I'm going to do x by x or for x period of time but there isn't a statement of what I expect to learn so can you talk more about that lack of the thing that I might learn and what that opens up yeah so I actually I didn't want to put the entire book in the presentation so I skipped over that part a little bit but there is a hypothesis not exactly as a scientist would actually write a hypothesis

- [00:30:30](https://www.youtube.com/watch?v=amV0j7R0yJc&t=1830) - I actually I didn't want to put the entire book in the presentation so I skipped over that part a little bit but there is a hypothesis not exactly as a scientist would actually write a hypothesis but there is a hypothesis that there is a hypothesis when you run an experiment and usually the hypothesis is more along the lines of this is going to work or this is not going to work so for example for me I was very scared of public speaking and I had the hypothesis that maybe starting by recording those little videos might help but you can also run experiments with the hypothesis that this is not going to work and another example for this one

- [00:31:00](https://www.youtube.com/watch?v=amV0j7R0yJc&t=1860) - starting by recording those little videos might help but you can also run experiments with the hypothesis that this is not going to work and another example for this one so that's not a work one more of a personal one is when you find experiments is when you hear yourself saying something that sounds like you have a fixed mindset around something so I was talking with someone and we were talking about meditation and I said I'm so bad at meditation it doesn't work for me I tried and you know we all know these apps that has the onboarding the 10 session onboarding for 10 days I had never managed to go over 3 of them

- [00:31:30](https://www.youtube.com/watch?v=amV0j7R0yJc&t=1890) - it doesn't work for me I tried and you know we all know these apps that has the onboarding the 10 session onboarding for 10 days I had never managed to go over 3 of them so that's how bad it was when I heard myself say this I was like oh wait a minute that's interesting fixed mindset here so how can I be more experimental with this and so I designed an experiment for this one and I started fully convinced that this would not work but I wanted to experiment with it and I wanted to collect the data and see if it worked or not and so I said I'm going to meditate every morning for 15 days and I decided to actually run this experiment in public

- [00:32:00](https://www.youtube.com/watch?v=amV0j7R0yJc&t=1920) - but I wanted to experiment with it and I wanted to collect the data and see if it worked or not and so I said I'm going to meditate every morning for 15 days and I decided to actually run this experiment in public I had a public google doc where every day after I read I meditated I put some notes in there and I shared that online and I had a lot of people leave comments and give me advice and tell me when I wrote why is it itchy everywhere why can't I stay still people said that's completely normal here are some techniques and not only I completed the experiment but I actually enjoyed it so I ended up being wrong and now I don't meditate every day this is not that kind of transformational story

- [00:32:30](https://www.youtube.com/watch?v=amV0j7R0yJc&t=1950) - here are some techniques and not only I completed the experiment but I actually enjoyed it so I ended up being wrong and now I don't meditate every day this is not that kind of transformational story but it's part of my toolkit now I feel particularly anxious this is something I'll do I'll just sit for 15 minutes and meditate which was something I could have not imagined before so you can have a hypothesis and it could be that this is going to help or this is not going to work but I still want to try it in that example that idea of public accountability sounds like it was powerful what role does accountability play with experimentation

- [00:33:00](https://www.youtube.com/watch?v=amV0j7R0yJc&t=1980) - but I still want to try it in that example that idea of public accountability sounds like it was powerful what role does accountability play with experimentation you can run your experiments on your own you don't have to share them with anyone but it can really help to add this layer of learning in public especially if it's something where you have quite a bit of resistance around it and maybe you've tried it before maybe that was a habit you tried to build in the past and you couldn't do it which by the way with habits I find it completely crazy that we pick a new habit and we say I'm going to commit to this for the rest of my life and we've never tried it before

- [00:33:30](https://www.youtube.com/watch?v=amV0j7R0yJc&t=2010) - build in the past and you couldn't do it which by the way with habits I find it completely crazy that we pick a new habit and we say I'm going to commit to this for the rest of my life and we've never tried it before so you can actually run a tiny experiment first see if it works and if it does then turn it into a habit so learning in public and having this accountability can be helpful to actually stick to it and collect the data I highly recommend that if you're running an experiment where you feel like oh I'm going to be very tempted to not finish it do it in public that's going to be helpful and second just in the spirit of shared knowledge and generosity if it's an experiment where you feel like

- [00:34:00](https://www.youtube.com/watch?v=amV0j7R0yJc&t=2040) - to not finish it do it in public that's going to be helpful and second just in the spirit of shared knowledge and generosity if it's an experiment where you feel like your friends, your family, your team whatever the context is you can take it from you can share it with others what's really important is to remember that it's not about sharing all of the experiments that worked but also the ones where you got an unexpected result there is a lot of value in saying hey I tried this thing it doesn't work don't do it you're saving people a lot of time and energy by doing this and it can be an amazing contribution so yes learning in public is completely optional

- [00:34:30](https://www.youtube.com/watch?v=amV0j7R0yJc&t=2070) - I tried this thing it doesn't work don't do it you're saving people a lot of time and energy by doing this and it can be an amazing contribution so yes learning in public is completely optional but can be really helpful to you so as a leader of a team how would you encourage me to try this with a team I would actually ask your team each person on your team to design a tiny experiment you could pick a theme a product, a challenge that you're facing as a team and say for the next month let's all run an experiment and let's all report back at the end of the month everybody can share what worked and what didn't

- [00:35:00](https://www.youtube.com/watch?v=amV0j7R0yJc&t=2100) - a product, a challenge that you're facing as a team and say for the next month let's all run an experiment and let's all report back at the end of the month everybody can share what worked and what didn't when you have this kind of scaffolding for experimentation what's great is that you're really creating a sandbox, a playground for experimentation where it's okay encouraged even to share the unexpected results and so instead of saying this is success, this is where we want to go you can start from more of a research question again a hypothesis saying we think this might work we don't know, let's try it let's collect the data and let's regroup and learn from each other

- [00:35:30](https://www.youtube.com/watch?v=amV0j7R0yJc&t=2130) - you can start from more of a research question again a hypothesis saying we think this might work we don't know, let's try it let's collect the data and let's regroup and learn from each other so I think this is a very simple and very tactical way to do it and at a more strategic level this is not just with experimentation in general leading by example really showing that what I mentioned during the presentation being okay saying I don't know, but not just that I don't know let's actually figure it out together is there an experiment we can design to figure out the answer together and I think this is how leaders can truly encourage

- [00:36:00](https://www.youtube.com/watch?v=amV0j7R0yJc&t=2160) - I don't know, but not just that I don't know let's actually figure it out together is there an experiment we can design to figure out the answer together and I think this is how leaders can truly encourage their teams to develop this experimental mindset yeah and I think that comes along with a lot of courage in terms of being able to say you know what I don't know and that's certainly one of the things that we work with our leaders a lot on here one of the topics I found fascinating in your book was the idea of cognitive scripts I'm wondering if you might be able to share a few thoughts around cognitive scripts this is a

- [00:36:30](https://www.youtube.com/watch?v=amV0j7R0yJc&t=2190) - one of the topics I found fascinating in your book was the idea of cognitive scripts I'm wondering if you might be able to share a few thoughts around cognitive scripts this is a fascinating study that's from the late 70s, 1979 where scientists it's a very simple elegant study they basically ask people if you're put in that specific situation, how do you act, how do you behave, what do you do and what they found is that most people when placed in the same situation with the same scenario end up all acting in exactly the same way that is useful in lots of scenarios, so for example if you go to

- [00:37:00](https://www.youtube.com/watch?v=amV0j7R0yJc&t=2220) - people when placed in the same situation with the same scenario end up all acting in exactly the same way that is useful in lots of scenarios, so for example if you go to the doctor, you know that you're supposed to wait in the waiting room and then they're going to call your name and then you go in the doctor's office and they might ask you to get undressed and check what's wrong if the doctor comes out of their office and comes in the waiting room and asks you to get undressed and get naked in front of everybody you might feel a little bit uncomfortable and that's because they've gone off script, there's a script we've all agreed on and this doctor is not following the script

- [00:37:30](https://www.youtube.com/watch?v=amV0j7R0yJc&t=2250) - and get naked in front of everybody you might feel a little bit uncomfortable and that's because they've gone off script, there's a script we've all agreed on and this doctor is not following the script so we have all of these scripts that are very useful to function as a society we have scripts for going to the doctor for going to the restaurant for all sorts of interactions the problem is that scientists discovered that we also follow these cognitive scripts in lots of other areas in our lives how we choose our jobs our careers, how we dress, the way we talk, the different studies that we do and in the book I share three of the big ones, the big cognitive

- [00:38:00](https://www.youtube.com/watch?v=amV0j7R0yJc&t=2280) - how we choose our jobs our careers, how we dress, the way we talk, the different studies that we do and in the book I share three of the big ones, the big cognitive scripts, they're more like buckets of scripts that I think are useful to think about them and maybe notice if you follow one of these in your life or in your work so the first one is the sequel script, that's the one where you make decisions based on the decisions you made in the past you feel like whatever you decide to do today needs to make sense based on what you did yesterday this is why a lot of people when they finish university they only look at jobs that align with

- [00:38:30](https://www.youtube.com/watch?v=amV0j7R0yJc&t=2310) - you feel like whatever you decide to do today needs to make sense based on what you did yesterday this is why a lot of people when they finish university they only look at jobs that align with their studies instead of considering a lot of other options and that's also why we actually all rewrite our CVs when we're applying for new jobs because it needs to look like we knew what we were doing there's a nice narrative so that's the sequel script the second script is the crowd pleaser script and that's the one where you make decisions based on what you think will make people around you happy those people could be your team members could be your friends, your family, your spouse and you make decisions because you want them to react like saying

- [00:39:00](https://www.youtube.com/watch?v=amV0j7R0yJc&t=2340) - where you make decisions based on what you think will make people around you happy those people could be your team members could be your friends, your family, your spouse and you make decisions because you want them to react like saying I can't believe you did that, that's amazing or this is so cool or you're working on this project, you made this decision and sometimes we limit the scope of our decisions because we only go for things that are going to be praised or that are going to lead to admiration and recognition from other people and the last one is the epic script so that's inspired by the Hollywood script, that's the idea that whatever you do, it needs to be big it needs to be impactful it needs to save the world

- [00:39:30](https://www.youtube.com/watch?v=amV0j7R0yJc&t=2370) - and the last one is the epic script so that's inspired by the Hollywood script, that's the idea that whatever you do, it needs to be big it needs to be impactful it needs to save the world and anything less than that is failure and that one is particularly insidious because as a society we've decided that this is a script we should all follow follow your passion, follow your dream change the world because of that a lot of people are feeling miserable because they feel like they haven't found their passion yet why has everybody around me figured it out and I haven't another problem with this script is that a lot of people put their eggs in the same basket and so when that thing doesn't work out

- [00:40:00](https://www.youtube.com/watch?v=amV0j7R0yJc&t=2400) - found their passion yet why has everybody around me figured it out and I haven't another problem with this script is that a lot of people put their eggs in the same basket and so when that thing doesn't work out their entire sense of self worth and identity collapses and you see that a lot with startup founders when their startup doesn't work out and then they end up being depressed for months and years sometimes so that's the epic script and I highly encourage anyone to just think about these scripts and think about maybe different areas of your life where you might be following them at a subconscious level and again using that inner sense of observation asking yourself is there a way I could do things

- [00:40:30](https://www.youtube.com/watch?v=amV0j7R0yJc&t=2430) - and think about maybe different areas of your life where you might be following them at a subconscious level and again using that inner sense of observation asking yourself is there a way I could do things a little bit differently, is there a way I could experiment with another approach that is a little bit off script yeah it's I think we've got a lot of homework to do when we get back in terms of thinking about how some of these things have shown up I can see a lot of those patterns actually if I just think about my own stories in a moment we're going to move to Q&A so if anybody has any questions if you want to go and begin to line up behind the microphone but whilst you're doing that I will ask

- [00:41:00](https://www.youtube.com/watch?v=amV0j7R0yJc&t=2460) - think about my own stories in a moment we're going to move to Q&A so if anybody has any questions if you want to go and begin to line up behind the microphone but whilst you're doing that I will ask you one final question so I am excited to leave here this afternoon and begin a tiny experiment what advice do you have I'll go back to the advice of starting with observation so that exercise of self anthropology I recommend doing for 24 hours so the way it works is very simple you choose a day during your week that's a pretty typical day so don't do that on a Saturday where

- [00:41:30](https://www.youtube.com/watch?v=amV0j7R0yJc&t=2490) - I recommend doing for 24 hours so the way it works is very simple you choose a day during your week that's a pretty typical day so don't do that on a Saturday where you go to your crazy festival do that on a day during the week that's pretty normal and again just like an anthropologist start taking little notes throughout the day and you can do that in between meetings in between tasks and ask yourself what is giving me energy what is draining my energy when do I feel particularly curious and excited you'll very quickly notice that maybe you just finished a meeting about something and you were particularly excited and you just want to

- [00:42:00](https://www.youtube.com/watch?v=amV0j7R0yJc&t=2520) - when do I feel particularly curious and excited you'll very quickly notice that maybe you just finished a meeting about something and you were particularly excited and you just want to spend more time on this equally maybe you just had a very long conversation with someone and all you wanted was to go to bed and disappear so make little notes of these moments the good the bad the challenging you'll very quickly see that when you start doing this you'll notice some patterns and this can be the seed of a hypothesis how can I do things differently maybe you've been running your meetings exactly in the same way forever without ever questioning how you do it maybe you've been working on the exact same

- [00:42:30](https://www.youtube.com/watch?v=amV0j7R0yJc&t=2550) - and this can be the seed of a hypothesis how can I do things differently maybe you've been running your meetings exactly in the same way forever without ever questioning how you do it maybe you've been working on the exact same project for a few years without questioning if maybe there might be another project you might be interested in maybe you've been approaching your time management your productivity and your calendar in general in a certain way and you can experiment with that so that's the advice I would give to anyone who wants to start designing their first tiny experiment pick one day 24 hours of self anthropology of observation and then choose one tiny experiment with one action one duration keep

- [00:43:00](https://www.youtube.com/watch?v=amV0j7R0yJc&t=2580) - experiment pick one day 24 hours of self anthropology of observation and then choose one tiny experiment with one action one duration keep it tiny don't go for something really big at first and see what you learn I love that thank you lovely well we have our first we have our first question so please Hi I'm Chancey I heard you were in APMM when you were here so I was too I graduated

- [00:43:30](https://www.youtube.com/watch?v=amV0j7R0yJc&t=2610) - question so please Hi I'm Chancey I heard you were in APMM when you were here so I was too I graduated recently so it's really cool to see how like how far you come what cool stuff you're doing I'm actually going through a big life stage at the moment where I think this book is like literally like the holy grail like my mind is blown but I had one question around neurodivergence how do you see these experiments kind of affecting those who might move through the world with their brain a little bit differently this experimental mindset is actually perfect for neurodivergent people

- [00:44:00](https://www.youtube.com/watch?v=amV0j7R0yJc&t=2640) - see these experiments kind of affecting those who might move through the world with their brain a little bit differently this experimental mindset is actually perfect for neurodivergent people my job at King's College London is actually at the ADHD research lab so although this book is not about neurodiversity that was something that was always on the back of my mind while writing it and something you find with neurodivergent people is that they actually have a more non-linear way of thinking and it can actually feel quite constraining and uncomfortable for them to follow the more curriculum like approach where you have step by steps kind of steps that you need to take so this is actually

- [00:44:30](https://www.youtube.com/watch?v=amV0j7R0yJc&t=2670) - and it can actually feel quite constraining and uncomfortable for them to follow the more curriculum like approach where you have step by steps kind of steps that you need to take so this is actually perfect in the sense that you start with curiosity which is also something that we know is very high in neurodivergent people and you leverage it in order to explore experiment and figure out what works and what doesn't amazing thank you so much thank you we have a couple of questions coming in on Dory but we'll take one more in the room and then I'll go to one of these hey Anne-Laure lovely to see you I used to work with Anne-Laure many many moons ago so wonderful to see you again so actually I was thinking of a very similar question

- [00:45:00](https://www.youtube.com/watch?v=amV0j7R0yJc&t=2700) - but we'll take one more in the room and then I'll go to one of these hey Anne-Laure lovely to see you I used to work with Anne-Laure many many moons ago so wonderful to see you again so actually I was thinking of a very similar question so you've just answered that so I'll ask a different question I'd love to know what have been some of your own personal favourite tiny experiments you've done and have there been any surprising learnings along the way yeah I love the meditation one I actually love the experiments where I start thinking that this is not going to work and when I'm proven wrong this is the best and as a scientist in the lab and outside of the lab this is the best feeling another one that I did recently that's going to sound very simple but

- [00:45:30](https://www.youtube.com/watch?v=amV0j7R0yJc&t=2730) - that this is not going to work and when I'm proven wrong this is the best and as a scientist in the lab and outside of the lab this is the best feeling another one that I did recently that's going to sound very simple but that was extremely good for me so while I was on my book tour for this book I had to record a lot of podcasts and at some point I realised that there were days where I was inside from 8am to 8pm back to back meetings without taking any breaks and so I said maybe taking some walks is going to help and there's the commitment again that is really helpful with the tiny experiment format so I said I'm going to take a 20 minute walk every day for the next

- [00:46:00](https://www.youtube.com/watch?v=amV0j7R0yJc&t=2760) - I said maybe taking some walks is going to help and there's the commitment again that is really helpful with the tiny experiment format so I said I'm going to take a 20 minute walk every day for the next 20 days and we're going to see if that helps or not so I did that for the last 20 days of my book tour and this time I was not proven wrong it really really really helped and even if it was just 20 minutes a day that was really good for my mental health and this is just to show you how they don't need to be very complicated it doesn't need to be something that's groundbreaking you don't need to reinvent the wheel it can be something really really simple thank you so one of the

- [00:46:30](https://www.youtube.com/watch?v=amV0j7R0yJc&t=2790) - they don't need to be very complicated it doesn't need to be something that's groundbreaking you don't need to reinvent the wheel it can be something really really simple thank you so one of the questions on Dory do you recommend specific practical tools like journaling specific apps or mental models for tracking managing and reflecting on experiments there's a tool that I really like that is actually in the book that's very simple that is very helpful to track your experiments but also to reflect on them and to decide what you're going to implement in your next experiments I maybe if I had named it at Google the name would be better but I did that on my own it's called

- [00:47:00](https://www.youtube.com/watch?v=amV0j7R0yJc&t=2820) - your experiments but also to reflect on them and to decide what you're going to implement in your next experiments I maybe if I had named it at Google the name would be better but I did that on my own it's called plus minus next and it really describes what it is it has three columns in the first column plus you write everything that went well second column everything that didn't go so well and in the last column next with a little arrow what you want to tweak next what you want to implement next what you learn and that you want to use in the next cycle I usually use that as a weekly review for my experiments so I do that on Sundays or Monday morning if

- [00:47:30](https://www.youtube.com/watch?v=amV0j7R0yJc&t=2850) - next what you learn and that you want to use in the next cycle I usually use that as a weekly review for my experiments so I do that on Sundays or Monday morning if you do a short daily experiment that's quite intense you can also do it every day because it's so short it's just a few bullet points in each column the nice thing too for anyone who likes to do some form of annual review is that once you have these at the end of the year you can look back on them and see all of the experiments that you run over the past 12 months that can be really nice to reflect on as well love that thank you our next question in the room hi thanks a lot it's Joffrey here so

- [00:48:00](https://www.youtube.com/watch?v=amV0j7R0yJc&t=2880) - experiments that you run over the past 12 months that can be really nice to reflect on as well love that thank you our next question in the room hi thanks a lot it's Joffrey here so my question is I feel we are at the time when I think everyone knows that the company and the department went through a lot of changes and I feel like anxiety around the job security is at a certain level so I think my question is do you have any tips on how to influence maybe leadership or the culture in general to make it more failure friendly because I do think the scripting now is also stronger than ever and maybe it's just me but I think sometimes

- [00:48:30](https://www.youtube.com/watch?v=amV0j7R0yJc&t=2910) - maybe leadership or the culture in general to make it more failure friendly because I do think the scripting now is also stronger than ever and maybe it's just me but I think sometimes we are told to not report on certain numbers if it doesn't look that good and like make the whole narrative looking a little bit better and I do feel it would be really nice to have to influence the culture in a way where we can actually learn from each other and getting failures would be like a good thing yeah absolutely I think this is why it's helpful for any project that has a lot of uncertainty to frame it as an experiment the issue is when we have something that's very uncertain and we link it to a

- [00:49:00](https://www.youtube.com/watch?v=amV0j7R0yJc&t=2940) - yeah absolutely I think this is why it's helpful for any project that has a lot of uncertainty to frame it as an experiment the issue is when we have something that's very uncertain and we link it to a linear goal a specific destination when that doesn't work out we don't get to that destination what you just described happens we try to create a post hoc narrative saying why that didn't work and why nobody's to blame and how we would do things differently next time but we're not really learning in the process because we're just trying to hide the failure make it look like it didn't really fail and so the incentives are kind of skewed where this is not really designed for learning

- [00:49:30](https://www.youtube.com/watch?v=amV0j7R0yJc&t=2970) - but we're not really learning in the process because we're just trying to hide the failure make it look like it didn't really fail and so the incentives are kind of skewed where this is not really designed for learning if you start from the very beginning by saying this is just an experiment this is just an experiment here are the parameters and I think this is going to work but I'm actually not quite sure and I'm going to report after this duration and I'm going to tell you what worked and what didn't so you really completely transform your definition of success from this binary definition where either it worked and it didn't and if it didn't you better hide it to something

- [00:50:00](https://www.youtube.com/watch?v=amV0j7R0yJc&t=3000) - what worked and what didn't so you really completely transform your definition of success from this binary definition where either it worked and it didn't and if it didn't you better hide it to something where the only goal is to learn something together Thank you Please Hi, so my question is how to like is the complexity of internalizing the experimental mindset and since that we all have a propensity so it's more of a curiosity question that we have a genetic propensity to be anxious or crave certainty which might be different across different people, you might have childhood experiences which change that or change your associations and then you have this cognitive

- [00:50:30](https://www.youtube.com/watch?v=amV0j7R0yJc&t=3030) - so it's more of a curiosity question that we have a genetic propensity to be anxious or crave certainty which might be different across different people, you might have childhood experiences which change that or change your associations and then you have this cognitive prefrontal context which is saying we can reason about this and change this and I guess maybe it's a curiosity question valuable, and I guess you have to practice, right? I mean, that's definitely true. But do we accept that certain times, this is my limit? I mean, I do worry about uncertainty. Or you can keep practicing and kind of just go all the way and be fully experimental? This is a great question, because I think a misconception when I talk about the experimental mindset is that it's about getting rid of the uncertainty and getting rid of the anxiety.

- [00:51:00](https://www.youtube.com/watch?v=amV0j7R0yJc&t=3060) - and be fully experimental? This is a great question, because I think a misconception when I talk about the experimental mindset is that it's about getting rid of the uncertainty and getting rid of the anxiety. And it's not. This is a completely natural reaction. And it's very, very hard to get rid of it. So what you want to do instead is noticing it, accepting it, knowing that this is perfectly normal, and then design an experiment around whatever the challenge is, knowing that by actually turning yourself from this paralysis to action, you are going to reduce the uncertainty. So you're never going to get rid of that fear of anxiety, that fear of uncertainty completely.

- [00:51:30](https://www.youtube.com/watch?v=amV0j7R0yJc&t=3090) - the challenge is, knowing that by actually turning yourself from this paralysis to action, you are going to reduce the uncertainty. So you're never going to get rid of that fear of anxiety, that fear of uncertainty completely. That's not possible. But experimenting is going to just give you more of a sense of agency, where you can feel like, OK, this is scary. I actually don't know what's going on. But I have agency. I can actually experiment. I can figure out my own answers. Love that. Thank you. I think we have time for one last question. Hi, I'm Lowe. Thank you. I have a question. I think just a few minutes before you started talking, I was reading the chapter, a deeper sense of time, I think.

- [00:52:00](https://www.youtube.com/watch?v=amV0j7R0yJc&t=3120) - I think we have time for one last question. Hi, I'm Lowe. Thank you. I have a question. I think just a few minutes before you started talking, I was reading the chapter, a deeper sense of time, I think. And I totally agree with your views about the fact that we sometimes tend to be more like, oh, I'm going to do this. I'm going to do this. And I think that's a good thing. But I also think that we tend to be overproductive. Not overproductive, but we take the productivity hacks a bit too far. And I wonder, tiny experiments and trying to get good at things and structuring it this way, do you ever think that it could be interpreted as a productivity hack, too? And is there an upper limit to this? Is there a limit by which you're probably

- [00:52:30](https://www.youtube.com/watch?v=amV0j7R0yJc&t=3150) - to get good at things and structuring it this way, do you ever think that it could be interpreted as a productivity hack, too? And is there an upper limit to this? Is there a limit by which you're probably doing so many experiments? So yeah, I wondered how those two ideas sit with each other, where it's like, we shouldn't obsess so much about productivity. I mean, we should be more productive, but we shouldn't obsess about it. And then we should design tiny experiments. But this also feels like a productivity hack. So it's like, do you think that it's the same thing? And if it's not, is there an upper limit? And where is that upper limit?

- [00:53:00](https://www.youtube.com/watch?v=amV0j7R0yJc&t=3180) - But this also feels like a productivity hack. So it's like, do you think that it's the same thing? And if it's not, is there an upper limit? And where is that upper limit? How does this arrange itself in your head? Yeah. So first, I don't see tiny experiments as a productivity hack, but I do think tiny experiments can help you figure out ways to be more productive without sacrificing your mental health. It's really about questioning the way you work and figuring out if there's maybe a gentler way that will give you the same result. So that's one part. The second part about whether there is such a thing as running too many experiments. Yes, absolutely, there is. So I highly recommend running only one experiment at a time.

- [00:53:30](https://www.youtube.com/watch?v=amV0j7R0yJc&t=3210) - and figuring out if there's maybe a gentler way that will give you the same result. So that's one part. The second part about whether there is such a thing as running too many experiments. Yes, absolutely, there is. So I highly recommend running only one experiment at a time. And there are several reasons for this. The first one is that you actually want to complete the experiment. And so if you run five, six, seven different experiments at the same time, it's very unlikely you're going to be able to stick to it and finish collecting the data. So it's better to just have one experiment. And the other reason is that if you're changing everything the way you work and you live, it's going to be really hard to know which experiment is actually having a positive impact, which one is not working.

- [00:54:00](https://www.youtube.com/watch?v=amV0j7R0yJc&t=3240) - So it's better to just have one experiment. And the other reason is that if you're changing everything the way you work and you live, it's going to be really hard to know which experiment is actually having a positive impact, which one is not working. The only exception to this I would give is if you really want to run two at the same time, is to run them in very separate areas of your life. So you could have an experiment at work with maybe the way you manage your time, your meetings, or whatever project, and maybe something around your health, your diet, your relationships, and something that's quite separate. But if you can, just stick to one experiment at a time. Well, thank you. Thank you for joining us.

- [00:54:30](https://www.youtube.com/watch?v=amV0j7R0yJc&t=3270) - your relationships, and something that's quite separate. But if you can, just stick to one experiment at a time. Well, thank you. Thank you for joining us. I am really excited, as I said, to go away and try this. I will invoke a power of observation over the next 24 hours and look forward to seeing what we learn. So thank you. And thank you for the invitation to all of us to think about how to experiment more and become more comfortable with uncertainty. Thank you for having me.

- [00:55:00](https://www.youtube.com/watch?v=amV0j7R0yJc&t=3300) - more comfortable with uncertainty. Thank you for having me. Thank you.

