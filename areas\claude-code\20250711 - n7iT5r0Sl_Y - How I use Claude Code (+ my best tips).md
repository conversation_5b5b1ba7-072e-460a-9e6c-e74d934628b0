---
title: How I use Claude Code (+ my best tips)
artist: <PERSON> (Builder.io)
date: 2025-07-11
url: https://www.youtube.com/watch?v=n7iT5r0Sl_Y
---

(t: 0) I have now for the last several weeks switched over from <PERSON>ursor's agents to Clod Code and I'm not looking back at all. Here's how I use Clod Code and my best tips. First I install the extension which works with VS Code, Cursor and (t: 10) probably other forks like windsurf. It doesn't do a lot but it makes it really easy to launch Clod Code right in your IDE. I still use cursor by default because every once a while it's nice to use command K and their tab completions (t: 20) but the only time I've touched the agent sidebar is when <PERSON><PERSON><PERSON> was down. What the extension does do is it makes it really easy to open Clod Code. I often have it (t: 30) in multiple panes at a time so I can run things in parallel as long as they're working on different parts of the code base and different files and if I have a file open it'll automatically pull that into the context. Now Clod uses a (t: 40) terminal UI which I was very hesitant about at first but they actually do a really good job with it. You can tag files easily and choose what you want to include. They have slash commands which are awesome. Speaking of I use the model (t: 50) command a lot and usually work with Opus unless Opus is having issues which happens. I use Clod to run my own commands and then switch to Sonnet. A lot of people (t: 60) should probably just use the defaults. It'll use Opus until you're at 50% of your usage limits and then switch to Sonnet which is more cost efficient. I found Opus isn't slow like 3.5 used to be compared to Sonnet at least not (t: 70) noticeably and both models are very good but Opus is just a little bit better. Other commands I use a lot I use clear a lot. In my opinion every time you're doing something new clear you don't need all that chat history in your tokens (t: 80) every time and you don't need Clod always trying to compact it either because compaction basically runs another call to output a bunch of tokens which takes time to summarize the conversation (t: 90) history just clear clear every time you're doing something new the up arrow key will go back to past chats including chats from prior sessions so if you close out of Clod and open it again for instance another day you can still go (t: 100) back to prior sessions speaking of opening Clod one thing it does it's really annoying is after you type a prompt it'll start working agents take a (t: 110) while so I'll go about my business I'll check slack I'll check email I might code something manually but then here's the problem. I come back and I see it's asking me can I edit this file it's really annoying yes you could edit files (t: 120) it's the point to be an agent like edit the files and there's no way I've found to globally say just edit files it's fine and then you'll go about your business it come back and it's asking if it could run a basic bash command. Can I (t: 130) run LINT? YES! oh my god yes! So here's what I actually do every time I open Clod code I actually quickly hit cmd C and then I run Clod dangerously (t: 140) skip permissions and enter. It's not necessarily as dangerous as you think it is but I need to assure you yesterday that I didn't want to run that germination type there's way beacsomething it's not just any type of click here if you're kind of moisture can you get it it's a T settings and you end up looking for the best and you're literally swimming at the end of the sea. and enter. It's not necessarily as dangerous as it sounds. It's akin to what Cursor used to call YOLO mode. And while it runs a minor risk that a rogue agent could run a command you didn't expect (t: 150) that's destructive, I've never seen that happen in my life. So up to you if you want to take the risk. I have for weeks and weeks, and I've never run into a problem whatsoever. Now speaking of slash (t: 160) commands, Cloud has a lot. One really cool one is installing the GitHub app. This makes it so when you submit a PR, Cloud will automatically do a code review. This is pretty awesome because as (t: 170) you use more AI tools, your volume of pull requests might increase. And I found in certain cases, the AI models are better at finding bugs than humans, because they frankly put more effort (t: 180) into it in some ways. While I've seen humans are really common to nitpick at, oh, this could be named differently and stuff like that. I've seen Cloud actually find real bugs that are humans missed in a good chunk of cases. The main tip I have for this is Cloud will add a cloud code (t: 190) review.yaml. It'll have a prompt in it already. Here's the prompt I use. The original issue we (t: 200) found with this tool is that it's not going to be able to find bugs. So I'm going to add a prompt in here, and it'll come up. And I'm going to add a command to this. So this is a really cool tool. It's not going to be able to find bugs. It's going to be able to find bugs. It's going to comment on all kinds of like nuanced, unimportant things and write a whole SE on every PR. What we really care about most for the AI to review is bugs and potential vulnerabilities. So we tell it, (t: 210) look for bugs and security issues, only report on bugs and potential vulnerabilities and be concise. The cool part is when you run this command and edit that one line, you have a pretty (t: 220) awesome new addition to your workflow. There's a lot of other really cool stuff it can do, like pull comments from a GitHub pull request and address them, review a pull request, (t: 230) and do things like, because out of the box, shift enter will not work for adding new lines, but we can just hit enter and tell it to do it for us. And there we go. Shift enter adds new lines. Beautiful. Speaking (t: 240) of quirks with using a terminal interface with cloud code, you might be surprised, but you actually can drag files in though in tools like cursor and probably VS code, it opens it in a new (t: 250) tab. If you drag in and hold shifts, it'll actually pop it in and reference it like you need. Now, one thing that doesn't work is it doesn't paste images from your clipboard. So if I do the Mac (t: 260) thing where I take a screenshot of a clipboard, I can actually paste images from a clipboard. And if I do the command V, nothing happens. A special trick for this is control V actually will work there. It pasted the image. That one took me a long time to figure out. Another thing (t: 270) that took me way too long to figure out because this is not a normal UI interface is when cloud is working. I always thought to hit control C to tell it to stop. And that doesn't do it. Hitting control C twice, just exit entirely. Oops. To actually stop cloud, you need to escape. (t: 280) And if you want to jump to any previous message, you can hit escape twice and see a list of previous messages and pop back to them. There's a lot of invisible features like this in cloud (t: 290) code. If you want to be hardcore, cloud also has a Vim mode, but I'm not a Vim user, so I do not use it. Now let's talk a little bit more about why cloud code is so good. In builder, we have one (t: 300) React component that is so large, I can barely even scroll to the bottom. It is 18,000 lines of code. There's never been an AI agent that can reliably update this file and tell cloud code. (t: 310) When using cursor, I have still found a lot of little hiccups. It has trouble resolving patches and has to rewrite files often. It really struggles to update extremely large files. (t: 320) Cloud code has no issue updating this file, like not even remotely. Cloud code works great with large code bases and complex tasks, and I find it just getting stuck incredibly rarely. I'm not even (t: 330) sure if I've noticed that at all. Whereas with cursor, I feel like I have to babysit it more, and when it gets stuck, stop it and realize maybe this was just not a good task to ask. (t: 340) Cloud is also exceptionally good at navigating a large code base, searching for patterns, understanding relationships between different parts of the code, components, shared states, (t: 350) stuff like that. It's honestly kind of incredible. If you think about it, cursor built a product that has a general purpose agent intended to support a variety of models. In order to do this, you have a whole additional team involved. They trained custom models as well, and there's just (t: 360) more layers and requirements and things going on here. As well as, cursor doesn't build or control the core models that do the core AI. Compare that to Anthropic. They definitively make the (t: 370) best coding models. And so they make cloud code the best at using the model. And when they hit challenges with cloud code, they go and make the model better. They only care about the (t: 380) support of their model. They know everything about how the model works, how it's trained, and how to use it in depth. And they continue to train the model to work well with what they need for cloud code. It also means that Anthropic can give you the most possible value for the least (t: 390) possible price, because you only have to worry about paying them. So they could compete on giving you maximum access to models like Opus without situations like cursor has, where cursor has to (t: 400) make money too. So cursor needs to make a margin and Anthropic, which fundamentally means you need to pay some degree more with cursor, or they need to lose some money. So they need to make a margin (t: 410) and lose more money, which isn't sustainable. So I commend the Anthropic team for making such a good tool with cloud code, because they can give you the maximum performance at the lowest price directly (t: 420) from the experts and make their models and products better altogether. It's really smart. And it's where I am betting my time and money right now today, given the incredible results I've been (t: 430) seeing. Now, speaking of pricing, I pay for the max mode. Now, if you're used to cloud code, previously being based on API pricing, cloud code now supports the standard pricing plans. Max mode, (t: 440) in my opinion, is an absolute steal. If you feel like a shockingly intelligent coder working for you 24-7 is not worth $100 a month, you need to look hard at what a human costs per hour for engineering, regardless of where (t: 450) you look in the world, and it's orders of magnitude more than that. So if you're having trouble justifying it for yourself or from your work, always remember the gains this has given you (t: 460) compared to actual manual human work. One other feature of cloud code that I absolutely swear by is queuing. So we could type a message, say add more comments. This is a thread about adding (t: 470) comments to some code. And often I think about, oh, the next thing I want to do. What I used to do is create a notepad and start drafting other prompts that I want to do. And then when I see one is done, I'll go and (t: 480) paste the next one and enter. That's what I did with the cursor, which is really annoying because again, I'll usually go about my day and answer Slack messages, answer email, do something else, and come back and see the agent's been idle for who knows how long. Type the next prompt, go away. (t: 490) And it was not very time efficient. But now with cloud, you can just queue more messages. So if I think of another one, actually also add comments to, (t: 500) the main chunks of the JSX, I can queue them into the computed values. And what's great is cloud is really smart about knowing when it should actually run those things. (t: 510) If it needs feedback from you, it's not going to automatically run the queued messages. It's a pretty smart system, but when it's wrapped up something, it'll start addressing them when it makes sense. So you could queue up a lot, go about your day. And in a lot of cases, (t: 520) just come back to a ton of work done in a good and smart way. But again, check it from time to time, because it might need your input. A couple other cool power features you could do with cloud. (t: 530) Is add custom hooks and custom slash commands. And the coolest part is you can have cloud build those for you. In this case, I asked for cloud to add a couple of default hooks, commands, and settings and created a settings file that I can easily edit. It added a cloud MD, (t: 540) which gives a bit of project overview and some key commands that it should know about. This prevents it from having to figure that out each time and scan the code base for, (t: 550) is there a build command or a lint command? It always has awareness of that. And it added some hooks for what code should run before edits are accepted, such as run prettier on a specific file (t: 560) or after edits are accepted. And it adds some hooks for what code should run before edits are accepted, such as run prettier on a specific file or after edits are accepted, such as run prettier on a specific file or after edits are accepted, like run a type check on a specific file to make sure that it only accepts good and correct files. Another really cool one you could do is add custom slash commands. Like if I want to output a test, (t: 570) I can describe my tests. To add commands, just add a dot cloud slash commands folder. Add its name and then dot MD. You just write these in natural language and you can use this arguments string (t: 580) to place an argument into the prompt. And the cloud will do exactly what you asked just by typing slash and whatever it is. You can even have sub folders and those we can access. (t: 590) Like this, like builder colon plugin matches the builder folder plugin dot MD. And that's how we can create a new builder plugin super easily. Another cool one is we can use the pound sign and add memory super fast. Like always use Mui components for new stuff. And then it'll automatically save (t: 600) that to the most relevant file. Cloud dot MD files can be hierarchical. So you can have one project (t: 610) level and you can have one in nested directories. It'll look at them all and prioritize the most specific, the most nested when relevant. You could also save this to global user memory, (t: 620) preferences you want to apply everywhere or local project memory. That's memory specific to you and gets get ignored, add it to any of these files and then it'll write it for you. Now, the main problem I sometimes feel, and I see people point out the most is sometimes you really (t: 630) just want a normal UI. It is kind of annoying that typing out long responses have these weird escape keys. I can't just click on what I want to edit and highlight and edit. It's still very clear (t: 640) you're working with a terminal and that comes with some inherent limitations. For there's times where you want a UI or you prefer UI interface, but you still want the mechanics of cloud code. (t: 650) One trick is to use the builder.io vs code cursor, winter extension, any point you can just launch into a visual UI from your IDE sidebar. What you get is a much more visual interface (t: 660) with the typical chat dynamics that you're used to and a live preview. And under the hood, we built the system to match almost exactly how cloud code works. This tool uses effectively the (t: 670) same agent and approach as cloud code down to the tiniest details. We reverse engineered it as closely as humanly possible. And then you could preview changes quickly. Like I actually just moved items from here. And then you could preview your changes quickly. Like I actually just moved items from here to here, to here, to here, to here, to here, to here, to here, to here, to here, to here, (t: 680) to right here. Perfect. And what's kind of cool is that anytime we can switch to design mode, and we get a Figma style interface, where we can change up any of the styling we want and edit it just like a design tool. It's kind of a cool way to bridge the worlds of visual editing (t: 690) and code and get a bit of the best of them both at the same time, we can explore different UI patterns quickly and make precision edits. And they just apply our changes to the code. (t: 700) You can do all this stuff from a browser interface too, which can be really cool for letting people on your team create prototypes right with your design system. And then you can (t: 710) do all this stuff from a browser interface too, which can be really cool for letting people on your team create prototypes right with your design system. And then you can do all this stuff from a browser interface too, which can be really cool for letting people on your team create prototypes. And then when you're happy with your changes and you're ready, you can just fire off a pull request. Here's our PR with the title and description. We can look at files changed. Looks like it's using (t: 720) our design system correctly as hoped. And I can leave a comment anywhere and say, hey, build a bot, move this to be in its own component in a new file. In this role now we're effectively just (t: 730) communicating with cloud code, but through the PR directly. The agent will then reply and push up commits addressing the feedback. And here we go. The builder bot address the feedback and describe the feedback. And here we go. The builder bot address the feedback and describe the feedback. (t: 740) And here we go. The builder bot address the feedback and describe the changes. Now it looks like we have our customer's table and its own file and we are using it as expected in the original. We can merge away. So whether you want to use cloud code in a visual way or in a terminal, I hope these tips were useful (t: 750) for you to be able to build awesome stuff like never before. Check out my full list of tips over on the builder.io blog. If you want to try the visual interface for cloud code, go to fusion.builder.io (t: 760) and let me know your feedback.

