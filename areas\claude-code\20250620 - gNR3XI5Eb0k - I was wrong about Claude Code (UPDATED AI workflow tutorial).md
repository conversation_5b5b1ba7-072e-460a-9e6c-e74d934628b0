---
title: I was wrong about <PERSON> (UPDATED AI workflow tutorial)
artist: <PERSON>
date: 2025-06-20
url: https://www.youtube.com/watch?v=gNR3XI5Eb0k
---

(t: 0) So I wasn't sure if I should make this video because I recently did a video two months ago about my AI coding workflow. So much has changed in that time that I felt like I had to do it. If you're new here, welcome to the video. My name is <PERSON> and I build productivity apps. (t: 10) And today I'm going to share my updated AI coding workflow. If you saw my workflow video, you know that I use Cursor and Cursor Agents to do the AI coding, which most developers are currently using right now. (t: 20) But I've recently switched to Cloud Code and I basically haven't used Cursor's AI features like Agents in over a week. In this video, we're going to cover why I switched to Cloud Code, my workflow and practical tips to get the most out of Cloud Code, (t: 30) and then some general thoughts on where I think AI coding is heading in general. Okay, so what happened? Why did I change to Cloud Code? So I've been using Cursor just like everyone else for over a year now. (t: 40) There's absolutely nothing wrong with Cursor, but about a week ago, <PERSON><PERSON><PERSON> introduced some new pricing to Cloud Code. If you're not familiar, it is basically a coding agent. So it actually lives in the terminal. So it's not a code editor like <PERSON><PERSON>or. (t: 50) It just lives in the terminal and you can put it into any code base and just ask it to do anything. You can ask it questions, you can ask it to add features. And I have been genuinely surprised by the results that I've been getting. (t: 60) A lot of these things were things that Cursor, even with Cloud Opus for <PERSON>, which is again, one of the best models for coding, was really struggling to help me with. (t: 70) Cloud Code was able to come up with solutions to these issues really quickly. It seemed to think about complex problems way better than Cursor's agent. And the solutions it came up with were genuinely better every time I compared the two. (t: 80) So how does this stuff actually work? The way it works is you go to any project in your terminal and then you just run the Cloud command. And when you run this command, you can start chatting with it. You can ask it questions. You can tell it to work on features exactly like Cursor agent. (t: 90) And my workflow barely deviates from how I use Cursor agent. I'm just constantly asking the agent to do things. I'm checking the code. Technically, I am still using Cursor, but I'm not really using any of the AI stuff. (t: 100) I just use Cursor or Xcode as my editor. And then I have Cloud Code in a terminal window, usually on the right side. (t: 110) First thing is Cloud Code does have something called plan mode. So if you hit shift tab on your computer, you'll see that it'll change to plan. And what this is going to do is it's only going to think through the problem (t: 120) and it's not going to actually generate or modify any code. So the way that I use Cloud Code is I always first use plan mode. I ask it to make a change. (t: 130) So if I ask it something like, can you go modify this? I make sure I'm in plan mode. I hit enter and then it's going to think through for a pretty long time usually. And then it's going to spit out its game plan. (t: 140) I review this game plan very thoroughly. And then if I'm happy with it, I tell it, okay, go ahead. Go try to execute the plan. If it doesn't work, I'm going to go ahead and do it again. If it doesn't work, I'm going to go ahead and do it again. If it doesn't work, I'm going to go ahead and do it again. If not, I hit no and then I revise the plan. (t: 150) And so that's tip number one and step one in my workflow is I always use plan mode first. So number two is I always generate a ClaudeMD file. So this is a file that's basically the brain and the memory of Claude when it's working on your project. (t: 160) You can kind of think of it like cursor rules. Basically even Claude rules, but it is very important and Claude really follows this thoroughly. If you hit slash init in any code base, it's actually going to automatically generate this file for you. (t: 170) And then you can go ahead and make modifications or you can even tell Claude code itself like hey, can you remember to do this next time and it'll go ahead and add to the ClaudeMD file. (t: 180) So step number two is generating this and making sure that it's accurate and up-to-date and exactly how I want it to be. So step number three when I'm about to start actually using it and modifying code is I always make sure to commit frequently (t: 190) and use git as almost a checkpoint system. So if you're familiar with cursor, they have this really nice restore feature in the chat. (t: 200) You can go anywhere back in your chat hit the restore button and it'll basically go back to that point in time of the chat. It's really great when cursor agent is going down the wrong path or you made a mistake (t: 210) and I was constantly using that feature Claude code has nothing like this. So the way I'm getting around this is by using git almost as my checkpoint system when I'm happy with the changes. (t: 220) I make a commit and if I don't like the changes, I just discard or revert the commit. It's kind of a hacky system and I haven't found a better way to do this, but it gets the job done. It gives me the ability to undo changes. (t: 230) If Claude does something that I'm not happy with number four is using screenshots. So you can actually drag screenshots into Claude code. So that way it has context very similar to cursor agent. (t: 240) You can drag images into the chat do this with errors. I do this with design screenshots, but I'm constantly dragging in images number five is similar to images. I usually also drag an entire folders and I'm not talking about folders in your code base. (t: 250) I'm talking about other folders and other code bases. So something I'm frequently doing for Ellie, for example, is I'm working on the Ellie front end. I like to drag in the folder for the back end and tell it hey, by the way, this is what the back end looks like. (t: 260) I found it is very helpful to give it additional context on something How does the back end work can actually make changes to the folders if you give it permission. (t: 270) So sometimes if I'm asking it to build something on the front end can go ahead and make changes for the back end for me. I heard that working with multiple code base is not officially supported, but this is a way to get around that number six is giving it URLs. (t: 280) So something a lot of people don't know is that Claude code actually has access to a web browser. So you can just paste in the link to documentation kind of similar to what you can do with cursor. (t: 290) But when you give it the link to documentation, it will go to the website read the documentation and get whatever context that. It needs can also run Google searches and go find documentation. (t: 300) So sometimes I just tell it things like make sure to use the latest Google Calendar API and it'll actually go do a web search and go find the documentation. So I don't even have to paste the link in I'm constantly doing that especially when I'm working with newer APIs number seven is sometimes I use sub agents. (t: 310) Claude code has the ability to spin up sub agents. So these are instances of Claude code with their own context that will go off in parallel. (t: 320) So if I'm doing a task that's pretty massive like trying to port an entire Ellie iOS app. To Android, I told it for the sake of time. Can you actually break this problem down and run sub agents where necessary actually spun up like 10 agents that all ran in parallel at the same time. (t: 330) If I ran this without sub agents, this probably would have taken over an hour to run. (t: 340) But since I ran it with sub agents in parallel, they all ran at the same time and it was able to finish much faster number eight is that I actually ask it to double check its work. So when it's done, (t: 350) I often asked it. Hey, can you make sure that it didn't break anything else or can you try to find some edge cases and just confirm that everything is working? I've been surprised that sometimes it actually does find things that I've originally missed and it gives me a little bit more peace of mind about the code that it generated number nine, (t: 360) which really isn't a tip. This is what I do is I always review the code that it generates. This thing is so good that I can easily see people just blindly accepting whatever it's producing. (t: 370) But my advice and for my workflow, I always review the code that it produces almost as if it was another developer and I'm basically just kind of reviewing a pull request and reviewing their changes. (t: 380) If you're using any of these AI tools, you should be doing that anyway, but I think it's worth saying because especially with Claude code, this can get really tempting to just blindly accept things because it is really good at generating some of this stuff. (t: 393) I wanted to share some real examples of things that I was able to build with Claude code that I wasn't able to do with cursors. (t: 400) Specifically. I've been using cursor with Claude sonnet 4 as the model and sometimes I even use Claude opus 4 Max, which is one of the most expensive and powerful models on cursor and it still wasn't able to get some of these things. (t: 410) So one example was very custom drag-and-drop animations in the LE iOS app. So this is where you can hold down and reorder list items. (t: 420) This isn't using the default Swift UI drag-and-drop. It's a completely custom drag-and-drop experience, which cursor did seem to be struggling with after a couple hours. But the minute I switched to Claude code, it was able to get it in like 30 minutes. (t: 430) Second example is a feature in LE where you can take an external calendar event like a Google calendar event and convert it into a task. I have been struggling with this feature for over a year now because it is extremely complex. (t: 440) It touches three different calendar integrations. It touches recurring tasks. It's just an overall very complicated feature the way that it's built into LE. (t: 450) Because of the complexity cursor had a really hard time helping me with this. Every time it changed something and something was fixed another thing ended up breaking. But Claude code was able to deal with all of that complexity and I was able to successfully ship this in less than one hour. (t: 460) So the last example is a very extreme one. I actually started the process of porting over the LE iOS app to Android and I had attempted to do this with cursor in the past, (t: 470) but it kind of struggled to do this because this is a pretty big migration. Claude code actually made substantial progress and I was thoroughly surprised by the results I got with this. (t: 480) If you don't believe me on the timeline, I was live tweeting the entire thing. So you can go check that out if you want some proof, but these were three concrete examples, but I had five or six other features that I used as benchmarks to test cursor versus Claude code. (t: 490) And every single time Claude code gave me better results and much much faster than cursor could. Claude code has been around for a few months now and I've heard really good things. But the reason I was hesitant to try it is it was only available through API based pricing, (t: 500) which means I had to provide. I had to provide my own API key and I had to pay based on the amount of tokens that I was using with Claude code. (t: 510) And as someone who does a ton of AI coding that scared me because I'd rack up a huge bill if I use this the same way I was using cursor. So I've always stayed away from it until they introduced their new $200 Claude Max plan, (t: 520) which allows you to have borderline unlimited usage and not have to worry about the token based pricing. Obviously the big caveat is to use it the way I'm using it. (t: 530) It costs about $200 a month. They do have some cheaper plans, but in my experience they're way too limited. I use Claude code on their $20. A month plan and I hit the limit in like 10 minutes. I use it on the hundred dollar plan and I hit the limit in an hour. (t: 540) Realistically. My recommendation is to use the $200 plan. So when you subscribe to Claude code, you can choose between using the Claude sonnet for model or the Claude Opus for model, (t: 550) or you can have it Auto select and try to use the best one for the task. Personally, I just have everything set to Opus because I'm paying for the Max plan and even using Opus for almost every request. (t: 560) I rarely hit the limit on the Max plan, but I have heard people say that sonnet for is actually good. And sometimes even better than Opus in some cases. (t: 570) Why is this thing performing better than cursor agent? If cursor agent is using the exact same model. So that was the first question I had and I did a little bit of research. I have no definitive proof here. (t: 580) So this is just my opinion. My hypothesis is that cursor is super optimized from a token usage standpoint that it does a lot of things like compression and not using the full context window to try to save cost which makes sense (t: 590) because at the scale that cursors operating at, they have to do whatever they can to try to get the token usage down. So that way as much as possible can fit in the $20 a month plan. (t: 600) I think that Claude code is not doing optimizations like this. I think it is eating a ton of money. Someone wrote a program. You can run to actually see how much tokens you consumed how much it would have cost. (t: 610) If you were on the API based pricing. Here's what my usage look like in a little over a week. If this is accurate, I have used over $3,000 and over a billion tokens in a little over the week, (t: 620) which is absolutely crazy. If that's accurate, I think Claude code is losing anthropic a ton of money because I'm just paying a flat $200. (t: 630) I think it's not as optimized as cursor and it's just consuming tokens. And what that does is it probably allows for better output. This makes me really question how anthropic is doing this. They're probably doing this to try to take more market share and tropic has probably 1 100th the number of users on Claude code than cursor does. (t: 640) So I think that they can sustain this a little bit longer. But if developers start picking up on this and start using Claude code who knows how long this is going to last. (t: 650) I probably just accelerated that timeline by making this video. But I think everyone's going to figure it out eventually. So I think it's worth sharing with you guys. (t: 660) Okay. So what are the downsides to it? Number one, the cost is extremely high. I think to use this effectively. You have to be on the $200 a month plan, which is just not affordable to most developers. (t: 670) But if you can afford it, I highly recommend trying it. Maybe I'll do a whole separate video about coding tools at different price points. But the price point is probably the biggest downside. Second downside, (t: 680) which I already mentioned is that there is no checkpoint functionality like cursor agent where you can just restore to a specific point in the chat. You have to do that manual workaround with git that I'm doing which kind of sucks, (t: 690) but I'm kind of used to it. So it's not even that big of a deal-breaker anymore. Number three, it takes a very long time to run some of the actions that I take have taken over 30 minutes to run which really can be disruptive to flow. (t: 700) But again, since you can in theory run sub agents and run things in parallel, that is a way to cut back the time. You can also just open up multiple terminal windows and use multiple instances of Claude code at the same time, (t: 710) which I am frequently doing. (t: 713) So who is this for? I think that if you are a developer who does a lot of coding and if you make apps and you make money from your app, (t: 720) I think this is a no-brainer at $200 a month because I am getting substantially more value than $200 a month at least in my case. So I think if you do this stuff as a professional and you make money from software development, (t: 730) this is a really good investment. I think if coding is just a hobby or you're just getting started. I actually recommend just sticking with cursor and using that instead. Again, there's nothing wrong with cursor. (t: 740) It is still an incredible tool. It's just that Claude code has been better in my experience. So, that's why I've switched to it right now. Please share your experience below. And if you have any tips on using Claude code or you found something even better, (t: 750) please leave a comment down below. I'm always looking for new tools and ways to improve my workflow, but I hope you guys found this interesting. If you like this kind of content, check out my Instagram and TikTok. I post almost every other day about building productivity apps. (t: 760) And obviously if you like this content, don't forget to subscribe. Thank you guys so much for watching and I'll see you guys in the next video.

