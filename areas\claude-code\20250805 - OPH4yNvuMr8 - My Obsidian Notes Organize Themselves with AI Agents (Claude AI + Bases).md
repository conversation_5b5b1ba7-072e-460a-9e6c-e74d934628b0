---
title: My Obsidian Notes Organize Themselves with AI Agents (Claude AI + Bases)
artist: <PERSON><PERSON>
date: 2025-08-05
url: https://www.youtube.com/watch?v=OPH4yNvuMr8
---

(t: 0) Hi everybody, my name is <PERSON><PERSON>. In this video I'm going to teach you how to build a self-organizing Obsidian Vault that lets you spend 90% of your time thinking and only 10% organizing. (t: 10) We are going to do this by turning our Obsidian into a true AI assistant. We'll be moving beyond (t: 20) simple summarization into the world of agentic workflows. We're going to build an agent that's going to route our ideas into a proper Obsidian base. Next we're going to build a powerful command (t: 30) that's going to process the whole YouTube video, extract the transcript and create a structured (t: 40) entry in our Obsidian base. Then it's going to perform a semantics search across our Obsidian Vault and link to our existing nodes such that we can prepare context for ourselves and be ready (t: 50) to go. So let's get started. To interact and engage with the material. And finally we're going to build the orchestrator (t: 60) command which is going to be a single capture command and depending on our input to this command (t: 70) the agent is going to decide and orchestrate where to put this. If that's idea it's going to put it into basis idea or if that's a YouTube node it's going to put into a materials base. That's a deep (t: 80) dive so there are going to be a timestamp. So let's get started. So let's get started. As the first few minutes are set up if you're familiar with Obsidian and Cloud Code feel free to skip ahead and by the end of the video you have built a very first functional AI agent and I've put every prompt and code snippet into a description. You can just go ahead and jump to a GitHub and download those snippets and start using them right away. Let's get started. (t: 110) Alright, first what we're going to need is Obsidian and this is a free download. download from Obsidian.md and if you're watching this video you probably already have it. (t: 120) Second you need the Cloud Code installed as this is agent decoding tool from Entropic and go ahead to docs.entropic.com and you'll find the Cloud Code section and run the installation command in your (t: 130) terminal not the command and if a terminal is something sounds intimidating don't worry (t: 140) you can copy this page into a into your clipboard. And go ahead and to your LLM of interest, chat GPT, cloud whatever use Gemini and just paste it there and and if something is not clear start asking. (t: 150) I'm sure you're going to succeed. (t: 160) The only thing that cost money is a subscription to a cloud.ai. So they have different plans and the cloud code is available starting from this pro plan for $17.99. (t: 170) $17 a month and that's what I'm using. (t: 180) Okay let's start building and every complex system starts with a simple and reliable action. For us it's going to be a solving the problem of capturing fleeting thoughts. (t: 190) To solve this problem we're going to build an AI agent that that's going to fix it right now. We're going to create a simple and reliable idea command and this command is a custom command for (t: 200) cloud code. You can think of this command as a workflow definition for our agent which has all (t: 210) of the context that agent needs to know and other steps which agent gonna need to achieve the goal. How it works is the agent gonna read this prompt file this custom slash command file then given our (t: 220) instructions we're gonna create a new node in our Obsidian world and then the agent can access some (t: 230) tools. The agent going to run this command in the cloud code. The agent going to run this command in the cloud code. The agent going to run this command in the cloud code. place the note into the inbox base. I'm going to be using a code editor to create this prompt for (t: 240) our agent. Let's first create a folder called dot cloud. And this folder going to contain another sub folder with the comments. And since this folder, we're going to put our custom slash (t: 250) command, let's call it idea dot MD, it should be a markdown file, and the agent going to be reading (t: 260) that in this file, we need to provide instructions for our agent on what it should do. I've already pre filled an example custom slash command on how it might look, I'm going to explain and walk you (t: 270) through the first convention is to have a YAML front matter and define their list of tools, (t: 280) which agent can use. And depending on the goal of the agent, we need to provide access to different tools. In our case, the agent going to create a file on our file system. (t: 290) We need to allow access for a right tool. Next tool is a bash command. And here we specify that the agent is allowed to run this command to get the current date which we're going to be using in (t: 300) the context of our note. And here is a description just concise description of what command is doing. (t: 310) That's optional. A good practice on how to actually structure the prompt for the agent is to first provide the context for this simple command, I'm just providing the (t: 320) current date. And the agent going to be able to execute this command and it's going to be in the context of the agent and the agent going to know what is the current date. Next, we define a task (t: 330) was the agent actually need to do. I'm defining step by step first generate a file name for the (t: 340) idea. Next, create a file in the following format here we use the date which we executed previously. And the file is going to be created in a root directory. (t: 350) And then we ask agent to clean up the raw idea input to remove filler words, or make it more concise. And then that's exact file which we ask agent to create it has a YAML (t: 360) front matter. And here we have also ID attack, which is going to be used later in our obscene (t: 370) base and the current date. Here we provide the header and actual content of the cleaned up idea. All right, let's get started with the code code and run (t: 380) it. So let's run it. And here we have our code code in our obscene repo. We are prompted with the interactive interface where we can access different commands. Here we see our idea command. (t: 390) Let's try it out and see what's going to happen. Let me come up with some idea. I'm going to be (t: 400) using the voice input. Okay, the idea may be for the next video, I have idea to perform a weekly (t: 410) use with a cloud code. Based on my journal inputs, I wonder how it will work out. Let's actually open our obscene there, bring it up here and observe what's going to happen. Now we see a date has two tools allowed. (t: 420) It first gets the current date, and then writes to a file, the agent creates this file and other (t: 430) property tag at the date. And it actually cleaned up my input and it gives a concise name also the (t: 440) here we run the full control, the agent us permission to create a file They can us we can just say we can decline if agent did something wrong, (t: 450) or we can accept it. This sounds very good. Let's accept that. Indeed us load a new note in our (t: 460) obscene, it has a proper tag. Date has a proper file name. What you've just seen is very cool because you can customize custom slash command have captcha with you will automatically commit toAM.ph traffic data in lot in a problem sheet and for that you are required to save some time. (t: 470) Justomest comes firstly to what we call melhor. And that was actually previously was the web when I was preparing to program in, this custom slash command however you want. You can ask not to clean up idea, you can ask to to give a different template. The killer feature of those custom slash commands is reliability. (t: 480) Now you just created a workflow which is going to be predictable and you know what to expect and (t: 490) you can use to add new ideas into your Obsidian. And now I want to show the integration with Obsidian bases and how it works all together where you don't need to remember where you put (t: 500) your nodes. You just have a tag for idea and then you can display all of your ideas in Obsidian base. (t: 510) We can open command palette and look for create a new base. We created a new base and by default (t: 520) what bases do is they just parse all the files in our Obsidian. And the way Obsidian bases work we can have a filter to filter only the required files. (t: 530) And let's build a filter to filter ideas. For us it's a tag. We can see we can we can filter (t: 540) where the tag is exactly idea. Now we are having this filter out by idea. We can also mention (t: 550) we can also provide properties for example the date which it was created. We can click there and get back to our idea. That's a new way to organize information and to see that it all works (t: 560) let's record another idea using the same slash command and see how it appears in our Obsidian base. (t: 570) All right another idea which I have is to perform a morning check-ins with a plot code where plot code asks me a set of questions on what is my intent for the day what is the most I can type? (t: 580) an excellent idea Mengge Zhang, a security manager, writes her own FAQs for you please keep going. most important thing I can do today. And it also pulls the calendar events from my Apple calendar (t: 590) and adds it into my daily note. Let's process that the agent is working and it's asking to (t: 600) create a file. That sounds good. Let's review that. We see that another file has appeared. And this is the one. And that's cool. That's very cool. That's been successful demonstration on (t: 610) how you can use a system together with bases. The workflow is as follows. The agent creates files (t: 620) according to the template, we create a file with a tag called idea. And then we can filter in our base by this tag. The key here is that we are decoupling the organization from location. It (t: 630) doesn't matter where the file leaves. All that matters is properties. It's a YAML front matter (t: 640) which file has. And this is the one. And this is the one. This gives agent to create the files anywhere, knowing our system of bases and automatically (t: 650) organize them. And this principle will let you never touch folders ever. All right, in the next workflow, I want to explore a more complex command, how you can capture information without (t: 660) friction on example of YouTube video. We're going to use agent to prepare a context for us to be (t: 670) ready to engage with the material. We're going to create a slash command called YouTube note. And it's going to accept a YouTube (t: 680) video link. Then the agent going to pull transcript of the video and analyze the contents of the video and create a structured note in our obsidian vault. The agent going to be able to pull the (t: 690) thumbnail for our YouTube video and we're going to have a beautiful obsidian base with all of our (t: 700) videos. As a next step, we're going to perform a semantic search. We're going to try to see are there any connections for this video with our current nodes. (t: 710) Alright, let's get back to our code editor and take a look at this command. I already prepared a new file in the YouTubeNode.md in the comments folder along with our previous (t: 720) idea command. Here we have a familiar front matter, a defined description, allowed tools and also argument (t: 730) hints. When we're going to run this command, we're going to have a hint of what should be the input if we forget. Next we provide the context for the agent as before, today's date. (t: 740) And here we have a new context we provide as the YouTube URL. (t: 750) Next we define a task and the task is a two-step workflow. We start with a high-level description on what we want to do. (t: 760) And on the first step, we extract video ID. And we extract video ID. And we extract video ID from the link. And fetch the transcript. And we provide the exact command for the agent to run. (t: 770) The agent first parses the video ID link. Basically we want to extract this video ID. And then we use a Python package called YouTubeTranscriptAPI and provide this video ID link as an argument. (t: 780) And we use UV package manager. And this command just creates an isolated runtime template. (t: 790) environment. On the next step we ask agent to create a video entry in our Obsidian world. We define a file name and then we define a template for the file. (t: 800) We add the title, tags, we also add the cover which we're going to use to create this beautiful cards view for our base. And then we have a template. Remember (t: 810) that this is fully customizable and you can ask to perform any analysis you want (t: 820) based on the transcript. You can ask for a key insights, you can ask for a summary, you can do literally whatever you want. Let's get back to the terminal and (t: 830) restart our cloud to refresh the commands. Remember that each time you add a new command into commands folder you need to restart the cloud session to (t: 840) refresh the context. Let's start the cloud. I'm just going to be grabbing some YouTube (t: 850) URL which I prepared. Okay. I like this video a lot. And let's try to run this workflow and see what happens. A youtube note and we provide the youtube URL. It's running in real (t: 860) time and the agent understood the task. Any create a list of tasks on what it (t: 870) needs to do according to the workflow. We also can take a look at the transcript. We see we have a full transcript for the video here, the agent successfully (t: 880) grew it. I just give 100% credit to this. So I click ok. So they give a credit to this video and they give the links back to us. I lucky to be on YouTube now. Here we also have the link to the video but it should be in Start Google Data Center so the links are sometimes loadedhem and we'll have a link to the link to a front of the page and we'll not get grabbed it. Now it's at the stage of generating the file name. Okay, the agent is forking. It's created a new file and it finished the task. That looks great. It created a cover, grabbed the cover (t: 890) and it provides a description, learning objectives and provide a section for us to follow along. (t: 900) You can interact with that and study. And remember that this workflow is fully customizable. You're in full control of what agent is going to be doing. And you can just ask to format it in a (t: 910) different way. If you don't like this, like learning objective sections, you can just delete that (t: 920) from your comment. And on the next iteration, it's not going to appear if you rerun this command once again. Let's create a new base to capture all of our YouTube videos, create a new base, (t: 930) called video. Let's filter in our view, tags, and add a new one. And here we have a new video. And we can see that the image that we want to (t: 940) capture is exactly video. Okay, for some reason we don't have it there. We need to perform that (t: 950) contains video. Great. Let's configure this view, create a card called card view, layout cards, (t: 960) and set the image property to be cover. Nice. And here is our YouTube video. We can also change the title here, for example, and remove the file name. That looks beautiful. So it's ready for you. (t: 970) You can just click and start learning and interacting with this material and follow along. (t: 980) All right, let's get back to our outline and see where we are. Okay, so we right now here at the (t: 990) workflow three, and we successfully implemented this command called YouTube node. And the next step is to get an agent to perform a semantic search within our Obsidian world and find the (t: 1000) relevant nodes to this YouTube video. Okay, to get this working, I created this command called (t: 1010) semantic search. The semantic search command instructs the agent how to perform semantic search using the local rest API and smart connections plugin. Essentially, that's just a (t: 1020) single query which agent is going to perform. extract the relevant concepts within our Obsidian Vault based on the semantic information, based on (t: 1030) the semantic overlap. To get this up and running, I've already set up everything. And on a detailed (t: 1040) setup, you can look at my previous video. I quickly walk you through what I've just done. I've installed those three plugins. The first plugin is Smart Connections. This plugin enables (t: 1050) the agent to understand a different semantic relevance between our nodes. The next plugin is (t: 1060) MCP Tools. MCP Tools plugin enables the agent to query this information from Smart Connections. It creates this Search Smart API point, which we query. We also installed the Local REST API plugin (t: 1070) to programmatically extract this information. What you need to do is copy this API key. (t: 1080) I've already done that. Go to your Obsidian Vault and create a .env file. And this file you define your Local REST API key (t: 1090) and just insert it here. And this API key is used here in this query to authorize and perform this (t: 1100) extraction of semantic information between the nodes to actually perform a search. And to make it all work, I'm going to create a few nodes which are relevant to Ray Dalio. I want to grab (t: 1110) the principles of Ray Dalio. Let's say I'm going to just copy those. And for each of the principles, (t: 1120) I'm going to create a separate node. And I'm going to ask Claude to do that. Create a separate node for each of the principles in our Obsidian Vault. All right, the agent is creating the principles. (t: 1130) It's creating many, many more principles. Okay, and that's actually quite good for us. I think (t: 1140) I'll just stop the agent. And those principles appeared here. Okay, and then let's perform a semantic search. (t: 1150) Let's actually clear our session and perform a semantic search on relevant concept to our video. (t: 1160) I want you to analyze this YouTube video and perform a semantic search, find a relevant concept in my Obsidian Vault and add those concepts in a new section. (t: 1170) Within this video in the relevant node section. That's a complex task. I don't know what's going (t: 1180) to happen here. But let's hope for the best. That's the results of the search. Actually, the agent is smart. Based on what we asked it to do, it performs a query where it puts (t: 1190) keywords which might be relevant to the query. And then it finds a relevant file within our (t: 1200) Obsidian Vault. Here is the relevant score for each. I created a slash code to this multiple depends and got 10 바�lites of 10 Barbara artic FPS (t: 1210) since we do not have (t: 1220) any développing server. By default, each variable is already considered valid. So it's about how many verdeck words now I need to get it out of the phrase. (t: 1230) And that's the main example where we added a group of citizenship words to our Snapbox. Let's check which page is right for us. We can also look at the graph view, open local graph view, and we see all of the connections (t: 1240) which the agent have made. And I believe that this is a very powerful workflow, where we just given the YouTube (t: 1250) video link, we can create this whole file with all of the context for us how we want and be ready to engage with the material. (t: 1260) We also find the relevant nodes within our Obsidian Vault and connecting them automatically. And I think that's a great starting point for you to explore how you can use this system (t: 1270) in your workflow and how can you adopt those concepts. And with that, let's go to the final workflow, building an orchestrator agent. (t: 1280) What we just created is different commands for various cases. When you have those many commands. (t: 1290) And it's a very simple process. It really becomes a pain to remember all of them. And you need to decide which one to use. I want to create a single custom slash command called capture, where we instruct the agent (t: 1300) to execute either the idea workflow or YouTube node based on the input we're going to provide. (t: 1310) And this approach is very scalable. You can imagine that having here some additional connections, some additional routes on how (t: 1320) the agent can proceed. With that input, you can have like other task workflow and then have a task base. Really, it's just the limit is your creativity and what you can think of using the system for. (t: 1330) Let's get started. This command is very simple. Again, we start with the context. (t: 1340) We provide the path to an idea command and a YouTube command. And this path is exactly a path to those files. We instruct the agent to execute. (t: 1350) We have a few more commands. We have a command to automatically route into appropriate obsidian base. And the task for the agent is to automatically decide which command to run based on the input (t: 1360) we provide. We have two modes of operation, the idea capture and the another one is YouTube capture. All right, let's test this command. (t: 1370) I'm going to create a new cloud instance. I'm going to split pane. I'm going to create another cloud instance and see how (t: 1380) the agent runs this side by side. I'm going to grab a different video right now. Here is the link capture and that's the video link on another instance of cloud. (t: 1390) I'm going to dictate an idea. Okay. I'm a bit out of creativity today. I'm just going to tell you can use. I wonder if you can use a cloud code to create events in your calendar and automatically (t: 1400) pull it into your daily note. That's the video I recorded a couple of weeks ago. (t: 1410) You can also watch that. That's the idea which you're going to record. Great. Now let's run those two cloud instances together and see how the agent routes those two tasks. (t: 1420) And that's the YouTube video. Okay. That's really exciting. (t: 1430) Uh-huh. It find that it's indeed idea and it understand that this is a YouTube URL. And it's a good idea. And it's a good idea. And it's a good idea. And it's a good idea. And it's a good idea. It should use a YouTube note command template. (t: 1440) It reads the YouTube note command and here it reads idea command. And you can just like auto approve that. Let's go with that. (t: 1450) Oh, sorry. Here I made a mistake. Cancel it. Continue. It created a file already. It's finished. Okay. The agent is analyzing video. (t: 1460) Yes. It's a bit takes a bit longer time because a bit more context to handle. You need to download transcripts. Transcript is very, very long. But we'll hope to get results soon. (t: 1470) If we can see it in real time appearing here, that would be really cool. We see that another video appears here. (t: 1480) For some reason, it did not render it properly. That's interesting. But we see that we have this idea in our base captured. (t: 1490) And we have this YouTube video processed. The video captured in the same format as before, meaning that this workflow is reproducible. As well as the idea workflow. (t: 1500) Ah, the cover is somehow not properly rendered. Let's ask Claude to fix that. I want you to fix the YouTube thumbnail. (t: 1510) Right now it's not rendered properly. Can you grab a different size of a thumbnail? Okay. Yes, it got it right and fixed the thumbnail. (t: 1520) I wonder if it fixed it also in the video. Here it did not do it. Oh, but you need to ask permission. Now. It also done it here. (t: 1530) I believe that's been a very successful demonstration. And we just built step by step on orchestrator from ground up. And this orchestrator can handle different inputs. (t: 1540) And you can add much more different workflows, for example, for adding tasks, books, articles, (t: 1550) blog posts, and much more. And I think this brings us to an interesting point. I think that those tools, such as Claude code, completely change the way we interact with (t: 1560) our nodes. For me personally, I start thinking about nodes with a goal and intent in mind. (t: 1570) I start asking questions. Why do I take this node? And I envision the process of what's going to happen with this node. So I start thinking in terms of workflows. (t: 1580) I just started with a simple idea command. And now I have commands for literal code. I have a lot of commands for literal code. I have a lot of commands for literal code. Well, that's little, a few commands, but probably (t: 1590) 30 worden nodes and future ones. So basically, I come to this myself. (t: 1600) I'm basically in Settings from My Church. And I like Everything channel to do the same thing. In this channel here, as you can see, I came with five code tools. (t: 1610) These two are all interesting and useful, but I was planning to even turn these related things into floors. Quick review. Big move. Soon. Coming up. more. Now it's your turn. I want to hear from you what you found valuable in this video. What kind (t: 1620) of ideas have you had while watching this video. This feedback really helps me to understand what (t: 1630) to build next. And of course to get you started right now I'm gonna providing the list all of the prompts and commands which are used in this video. You can just follow the github link down below in (t: 1640) the description. My goal here is to get you up to speed and actually start using those tools in your (t: 1650) note-taking workflows. The key here is that you can't learn those tools without actually using (t: 1660) them. I think you just need to start download cloud code and start tinkering around with that and see what are the capabilities, what are the limitations and what is the (t: 1670) value of the tools. If you want to learn more about cloud code, you can go to my website at cloud.com. And I'll leave the link in the description. And if you want to learn more about cloud code, you can go to my website at cloud.com. And I'll leave the link in the description. And I'll leave the link in the description. And I'll leave the link in the description. And I'll leave the link in the description. Thank you so much for watching share this video with a colleague who you (t: 1680) think might find this video useful and this goes a very long way and now go and build something amazing I'll see you in the next (t: 1690) one

