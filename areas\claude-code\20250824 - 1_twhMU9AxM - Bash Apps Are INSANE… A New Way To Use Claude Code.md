---
title: Bash Apps Are INSANE… A New Way To Use Claude Code
artist: AI LABS
date: 2025-08-24
url: https://www.youtube.com/watch?v=1_twhMU9AxM
---

(t: 0) Tools like Cloud Code and Cursor have changed how we write code with AI. We've gone from generating simple code snippets to building full applications, and this shift happened because we moved from basic AI models to AI agents. (t: 10) Now these agents work with tools. They're given access to these tools and can call them to perform real tasks. One of the most important tools here is the Bash tool, (t: 20) because it allows these AI agents to run commands directly in the terminal and therefore control any tool or even your entire system. This opens up an entirely new type of application that's built on top of these (t: 30) powerful coding agents that become part of your daily workflow. Now just to explain what I mean in a simpler way, I'm going to give you two examples. (t: 40) These examples are really basic and you might not see much use in them right away. But later on, I'll show you an actual tool I've built using this new approach, and you'll see how it's been incredibly useful to me, (t: 50) how I've combined Cloud Code with it to create a real agentic use case that helps me in my daily life. Take this first tool. GalleryDL. (t: 60) It's a command line program that runs in the terminal and downloads images from public sites. If I scroll down and show you the usage example, you can see that we simply call the tool and then give it the link. (t: 70) That's how you use it in the terminal. Now you don't have to manually do this anymore. Cloud Code can handle it for you. All you need to do is provide the usage examples, the commands and how they're used, (t: 80) descriptions, and a good system prompt explaining how to use them. Cloud Code essentially becomes an agent with full control over this tool. Then I just... have to give it a link and tell it what I need to download. (t: 90) And if there are complex commands or flags that need to be added, I don't need to memorize them. That instance of Cloud Code becomes specifically dedicated image agent for me. (t: 100) Another example is this downloader called ARIA2, which again is a terminal program. Just like before, you have usage examples where you run the command name followed by a link, (t: 110) and it downloads the file. The point is, I don't need a separate dedicated program for this anymore. I can just give links to Cloud Code and it'll download them for me. Things run nicely. It runs much faster in the terminal, especially downloads. (t: 120) And interestingly, many tools that have graphical interfaces also have terminal versions. By using their examples in the cloud.md file and giving that context to Cloud Code, (t: 130) you can create a specific agent dedicated to that tool and actually use it in your daily life. Think of the CLI commands as the new tools of the agent, where you're giving it the proper context to work with. (t: 140) But again, these are simple examples where you're just delegating the work. You're not using it in the thinking process yet. Well, you can do that as well. (t: 150) Now, a quick break to tell you about today's sponsor, ScaleKit. ScaleKit is the authentication and access management layer built specifically for AI applications. If you're building in the AI space, it helps you manage identity and access for users, agents, (t: 160) and the external systems they connect to. Say your setup involves MCP servers for agents to access your product, (t: 170) or your AI agents need to talk to tools like Slack, Notion, or Google. Getting OAuth 2.1 setup, you can do that in just a few minutes. Getting OAuth 2.1 setup, you can do that in just a few minutes. Getting OAuth 2.1 setup, you can do that in just a few minutes. can be, well, painful too. (t: 180) That's where ScaleKit shines. It takes care of the hard stuff. OAuth 2.1, user-scoped tokens, even built-in tools calling for over 100 plus apps. All without you reinventing the wheel. (t: 190) And here's the best part, you don't have to replace your existing OAuth provider. You can use ScaleKit just for the parts you need. Or go all in with a full stack setup. (t: 200) It's modular approach is what really sets it apart. Hit the link below and try ScaleKit today. Before I show you the tool, let me explain the use case behind it. If you've been following the channel, you know that most videos feature open source projects. (t: 210) These projects contribute significantly to the AI coding community, enabling us to build better with (t: 220) AI. To properly explain these repositories, I need to examine them thoroughly. Normally, this means going through the README and any documentation folders. However, I've streamlined this process using a site called Git Ingest. This tool converts entire repositories (t: 230) or selected parts into LLM readable text. It organizes the content in a format that's (t: 240) optimized for language models to process and understand. For smaller repositories, I convert the entire codebase. For larger ones exceeding 200k tokens, I focus on the README and (t: 250) documentation folders. Once I have this formatted text, I bring it into Claude Desktop. I paste the converted content and ask Claude to explain the repository. This gives me comprehensive insights (t: 260) and all the information I need. Take the BMAD method as an example. I've created a new repository for the codebase. I've created a new repository for the codebase. I've created a new repository for the codebase. I've created a new repository for the codebase. I've created a new repository for the codebase. I've created a new repository for the codebase. I've created a new repository for the codebase. I've created a new repository for the codebase. I've created a new repository for the codebase. I'm be favouring the particular codebase that I need. I wanted to get all the currently existing data in the right context while 제 (t: 290) what the hell is this yellow technique doing a fully functional CLI tool. This means I can run its commands directly in the terminal. I simply (t: 300) provide a GitHub repository link and the CLI command converts the entire repository into LLM readable text, exactly as I demonstrated earlier. However, I frequently encounter a workflow (t: 310) challenge. I need to first check each GitHub repository's token size. When it exceeds the limit, I have to manually extract just the readme and documentation folders, then paste them (t: 320) separately into Cloud Desktop. This creates unnecessary friction in the workflow. I realized all of this could be streamlined by integrating it directly with Cloud Code. So that's exactly (t: 330) what I built. I took the CLI tool and created a complete Cloud Code framework around it. This is GitAgent, the tool I've built. I'll give you a general overview first, (t: 340) then dive into the details and explain how you can build similar tools yourself. The key innovation is that this tool eliminates the need for Cloud Desktop. (t: 350) Cloud Code becomes the actual agent, and instead of just maintaining context, it stores everything as structured data within the repository. The structure works like this. We have a data folder containing the original (t: 360) repository extractions. Then we have an analyze folder where specific analysis are stored, whether that's a full installation report, a workflow guide, or any other documentation. (t: 370) Simple queries don't require storage. The tool automatically checks repository size, eliminating manual checking. When a repository exceeds 200k tokens, (t: 380) it examines the repository tree structure and stores it for reference. You can see this stored examples here. Cloud Code then intelligently determines what content is needed based on (t: 390) your analysis request. If you need documentation, it fetches that specific content. If you need implementation details, it retrieves the relevant code files. Building this was surprisingly (t: 400) straightforward. It's a compact tool, and I'll explain the specific framework I used. For the Cloud Code integration, I created a GitAgentPRD document containing all my requirements. Cloud Code you can find out if you have been using the code, if you have not, you can use the code to determine the content you're using. agent PRD document containing all my requirements. (t: 410) Cloud Code used this PRD to build the application, write the tests, and validate everything by executing commands sequentially to ensure proper functionality. (t: 420) Let me walk you through the workflow. The entire system is defined in the cloud.md file, which serves as guidance for Cloud Code on how to use the Git Agent tool. (t: 430) At its core, Git Agent centers around the CLI-PY file. This contains commands from Git ingest combined with additional custom code. The cloud.md file instructs Cloud Code how to respond when given a GitHub link. (t: 440) Every time I provide a repository URL, it executes the complete baked-in workflow automatically. The process works like this. (t: 450) It extracts the URL, performs the extraction, and runs a checking function to count tokens. Then it generates analysis templates based on what needs to be analyzed. (t: 460) Cloud Code's LLM takes over from there. Analyzing the content and production. This is the process I am going to show you. The CLI-PY commands are documented in cloud.md. Git Agent serves as the parent command with various subcommands built in. (t: 470) Cloud Code simply runs the Git Agent command and knows exactly when and where to execute each step. When I include cloud.md in any Cloud Code instance, it automatically picks up this context (t: 480) and behaves accordingly. Here's how it works in practice. When I provide a GitHub link, it immediately runs the check-size tool through terminal (t: 490) commands, executing step-by-step with full-time work. This will allow the command to存 Religion and (t: 500) other new text afterwards The 00undepoint function automatically records this configuration through 7ax (t: 510) от almighty content prompttźsrU mentorship.txt. (t: 520) it illustrates some of the infrastructure. So, the node design is built in a API environment still needs some refinement. After retrieving the tree and the documentation, I need another check to ensure the combined content doesn't be removed by the command. doesn't exceed the limit again. Currently, it can overshoot. These are the small bugs you discover through testing, and they're easily fixed with prompt adjustments. (t: 530) When I request installation steps, it extracts that specific section. I can optionally store this in the Analyze folder for future reference. The system provides installation details, (t: 540) core workflows, and explains how everything functions, including user flows. This entire workflow integration with Cloud Code took about 2 hours to build. Most of that time (t: 550) went into designing the workflow logic and waiting for Cloud Code to test the commands it wrote. For 2 hours of work, this level of automation is remarkable. This tool becomes (t: 560) especially valuable when working with complex coding repositories. When I need to code with an online repository using Cloud Code, I can extract the most relevant documentation directly (t: 570) without switching tools. On the architectural level, here's how it works and how you can build your own. First, choose your CLI tool. In my case, that's Git Ingest. (t: 580) Since you'll need to build your own CLI tool, you can use the Git Ingest tool to build your own CLI tool. If you need to build a CLI framework that combines special commands with additional code from Git Ingest, you'll use Qlik. Qlik allows you to define new commands and run (t: 590) them seamlessly. When writing your PRD, simply tell Cloud Code you're using this framework. It understands Qlik and handles it efficiently. That's exactly the approach I took. Most of (t: 600) your effort will go into refining the cloud.md file. Prompting agents properly requires significant tweaking. You'll need to test the workflow manually, as Cloud won't be able to fully (t: 610) test everything on its own. During testing, you'll discover various breaking points where parts of the workflow aren't followed correctly. These refinements take the most time. The technical implementation uses a Qlik group. (t: 620) This groups together commands from Git Ingest with additional code that handles file saving, adds necessary flags, and defines everything under the git agent command. When we run git agent, (t: 630) it's essentially calling the Git Ingest CLI underneath. That's the core mechanism. So now, just find a CLI tool that you think you could use in your (t: 640) daily life and ask Cloud Code to implement it using Qlik. Then give it a PRD of the workflow you want to build with that tool and Cloud Code will go ahead and create an amazing system for you (t: 650) that you can actually use every day. It's that simple. Take any command line tool that solves a problem you have, describe the workflow you envision, and Cloud Code will turn it into your (t: 660) own personal automated assistant. That brings us to the end of this video. If you'd like to support the channel and help us keep making videos like this, you can do so by using the link in the description. (t: 670) If you'd like to support the channel and help us keep making videos like this, you can do so by using the link in the description. If you'd like to support the channel and help us keep making videos like this, you can do so by using the link in the description. As always, thank you for watching and I'll see you in the next one.

