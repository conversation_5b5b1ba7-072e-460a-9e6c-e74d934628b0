---
title: "SUB-AGENTS: Build Your AI Engineering Team"
artist: <PERSON>
date: 2025-07-26
url: https://www.youtube.com/watch?v=8w_HY0mkn94
---

(t: 0) Instead of one overworked AI, imagine having a whole team of specialized agents working together. Dedicated code reviewers, designers, test writers, documentation agents, all vibing together. (t: 10) So check this out. Join me as we are going to explore Anthropiq's new sub-agent workflows that have just been announced. (t: 20) And we can learn how to build an AI engineering team that transforms the way that you code. So instead of relying on a single AI agent for everything, we're just going to go ahead and discover how to orchestrate these multiple agents that work together seamlessly. (t: 30) Just like a real development team. So this is actually perfect for those who are beginners or those who are experienced developers as well. (t: 40) If you're feeling like overwhelmed and you want to make an AI handle like your entire workflow, we'll be building everything from scratch together here live. (t: 50) So this is lock in and hopefully you can walk away with some practical experience that we basically embrace. So we're just going to go ahead and get started with the setup of how we can implement immediately. (t: 60) In this stream, we're just going to go over like the setup of how to configure these agents, like maybe the different engineering tasks that we can go through. Right now on YouTube, I have a little poll up of what agent you want me to create first. (t: 70) So is it going to be a designer? Is it going to be a code reviewer? Or is it going to be an order me a pizza agent? So if you're on YouTube right now, just go ahead and click the poll and go ahead and vote your favorite design review agent that we'll do. (t: 80) We're also going to create like some, maybe some automated testing or documentation. This is something I use a lot in my workflow. You've been seeing me on my live streams. I always ask to create documentation and do those types of (t: 90) things. So now I can probably delegate that work to a sub-agent. I'm a solo engineer and I'm trying (t: 100) to use AI and I'm always not in the code all the time. So I think this is going to be a really great way to delegate some of these types of tasks because I don't want to confuse you too much, (t: 110) but maybe I'll go over that a little bit later. I'm so excited here. This is a really crazy one. I've been waiting for this for a long time. And so drop a fire in the chat. If you're totally locked in right now, we're going to get right into it because we need to transform basically my (t: 120) single person workflow into like a whole design team and engineering team, crack designers, cracked engineers. And I don't know about you, but I'm totally hyped for this whole thing. (t: 130) So really quickly, just to get set up on this, what we need here is this is actually available on Anthropics guidelines and docs. So let me just go ahead and drop this up in here so that we can (t: 140) check this out. So it's really fun to have the community here with me live. This is kind of one of my favorite things to do live. This just gets announced and we're just going to like work through it together. (t: 150) Pretty sure other people are trying to figure this out as well. And so this is our chance to kind of really interact with each other. Also, if you want to get in on the discord, I have some (t: 160) information that's going to be linked to join my community membership. It's just right now, 699 until I get my first hundred members. And then you'll be able to join the Ray Fernando community before we double the price when we get our first hundred members. So big shout out to (t: 170) everyone. So just a quick start here on their website. I just dropped the link here and it's (t: 180) going to say you'll see this link in the actual chat. You can click that and this will take you to the documentation. I'm pretty much going to be following this guide to get us started. (t: 190) And then once we get cooking, I'm going to see if maybe like Anthropic and Claude code can maybe generate some of these types of things. I have lots of these rule files and I've been documenting (t: 200) a lot of stuff in my code. And I'm hoping that we can kind of leverage some of that. So I'm going to have it just read through some of my documentation and workflows and some of these optimization (t: 210) principles that I have and see if it can generate some sub agents for that. Just as a like from what I've been able to see right now is that like these sub agents can be either called if you tell them (t: 220) like specifically in the chat or Anthropic as it's going through things, it can figure out that saying, oh, I should use a sub agent for this. Here's one that Ray already has for code review, (t: 230) or here's one that Ray already has for design. So I'm going to have to do a little bit of a review. That's kind of really interesting about like what this type of thing can do for us. The other positive is that it actually has its own (t: 240) context window. So what that means is if you have like 200K tokens for a context window, once you (t: 250) reach about 50%, you're start to get a lot of degradation in your actual outputs. So you want to always either start new chats or start new conversations or try to like separate your (t: 260) concerns. Why? This is magical. This is really important for the entire movement is because now we can separate our concerns over (t: 270) these different sub agents like a code reviewer, someone who's going to do a design review. Someone's going to order me a pizza. I see you out there. (t: 280) Make sure it's gluten free, by the way. And those types of things are going to have their own context window to basically think about like a big old room. You can be able to stretch out and you're going to be able to do more things. (t: 290) And that's basically what they're going to be able to do on our behalf. And that's why I'm really excited about this. And there are some other platforms that have been able to do this. And one of them is called AMP. (t: 300) AMP code from Sourcegraph. They have some real they've been experimenting with this for a little while and we're going to get into a little more details about that in the future. (t: 310) I met these guys and since like February, they've been doing this type of stuff and they have their own coding agent that does their own thing, which is super awesome. (t: 320) And that's like this is done without describing sub agents. So we're going to explore a little bit that like maybe towards the end of the stream. But I want to take a just kind of give you guys a heads up that this is kind of been a thing. (t: 330) They have a thing called the Oracle, which is really, really cool. You know, I love learning more about these types of agents, but I don't want to distract us any further from here. So getting into the anthropic agents thing here, it's going to say create a new agent. (t: 340) So we're just going to open up cloud code and I'm just going to create a new session here. I'm going to close this one out. As you can see what I was doing for in my project. (t: 350) Was I was trying to see if I can have Claude generate these abandonment emails. So I have this workflow where if a new user signs up, but they don't actually like check out, they do the stripe checkout. (t: 360) I can see that they've created an account, but they never actually completed the checkout. I want to be able to send them an email and then say, hey, do some things. (t: 370) And in this conversation with Claude code, what I ended up doing was like, can you help me actually write those emails? Because Claude is really good at writing the emails and understanding these workflows. (t: 380) And because it's already in my code base. I had already described and figured out that there was a problem here. So yeah. So let's go ahead and see here. Let's see. (t: 390) I think someone's going crazy in the chat. I'm going to go ahead and ban them. Yeah. And so let's see here. I just abandoned. (t: 400) Okay. So then what ended up happening here was basically there was like a whole lifecycle of email templates that got generated here. And this is like this. (t: 410) These are types of things that I think about. Like, hmm. If I ever have something in my workflow, I want to try to be able to reproduce it again. And so like some type of things like this are usually ripe for like documentation. (t: 420) And so I basically had it create this little templates for my emails here. And in the future. (t: 430) I don't know. I think like there's an opportunity here, but there's out of all the other opportunities, people have been talking a lot about my design workflow. So I wrote one up here. (t: 440) And these are the actual design guidelines. And so these ones go over like it's almost like a code review type of thing. So let's go ahead and see if we can kind of get this code review design things bootstrapped for us. (t: 450) So we opened up cloud code just by clicking this little thing. I found out I probably installed the cloud code plug in. And this is actually an extension. (t: 460) So if you type in cloud code, this is actually people were asking how I got this little star thing up here. And that's actually how you do it. You just type in cloud. (t: 470) And in the extension. You'll actually see the cloud code for VS code show up. And that's actually how you can get that little cool thing up there, by the way. And so that's why it kind of like becomes its own life inside of the inside of your little VS code. (t: 480) Or in my case, I'm using cursor. That's how you get it to show up there. So. All right, cool. (t: 490) So let's go ahead and see if I type in slash agent, we should be able to see manage or configure agents. This is really cool. So it's going to allow us to now create a new agent. (t: 500) I'm going to see if I can make this a little bit bigger for people to see. Okay. And then I'm going to get rid of this at the bottom. All right. And I'll probably stretch this over a little bit more. So it's a little bit more in the view. (t: 510) Okay. So design. Okay. So go ahead. Agents. No agents found. Create a specialized subagents that cloud can delegate to. (t: 520) Each agent has its own context window, custom system prompt, specific details. So try creating a code reviewer, a code simplifier, tech lead or UX reviewer. (t: 530) So we're going to do one that's basically like a UX reviewer based off my guidelines here. And let's go ahead and do that. Okay. So I'm going to say create new agent. Okay. (t: 540) And so we have a couple of options. Looks like you can choose either to have it in the project or for personal. I'm going to go ahead and select project. I like having maybe project specific agents, but like the design one is something that I use in all of my projects. (t: 550) So for now, I just want to basically. Create it in my project just in case I mess something up. (t: 560) I can, you know, like totally back out the changes and so forth. You can, you can see where it puts it. The only difference is in your project that's going to live here versus in your like global repository and stuff. (t: 570) Let's see new agent creation methods. So generate with cloud recommended or manual configuration. Ah, so we can either modify in the actual documentation. (t: 580) Claude says that if you want to create your own agent, do you want to create your own agent? I would actually kind of follow this file format so you can easily give this to Claude code and have it generate more sub agents. (t: 590) And this is probably something that we're going to do a little bit later in the stream. So that way we can kind of go through more details on like taking a look because I have a lot of documentation in my code about how I do certain things. (t: 600) Like I have an optimizations principle because these agents like to overwrite the code and they don't actually reuse a lot of my code. (t: 610) So we're going to definitely do a sub agent for that. So if you're just tuning in, make sure you stay tuned. And we're going to do that workflow of like, how do we create a new agent? How do we create our own custom one? (t: 620) But for now, let's just go ahead and follow through how Claude code generated and then go from here. So we'll do that. Describe what the agent should be do when it should be used. (t: 630) Be comprehensive for the best results. So here it's going to say expert software engineer that helps review my code not based on best practices. One of the things that I say a lot is to like don't repeat yourself or maybe like optimization principles. (t: 640) And so I think what I should do. Here is probably describe like all of this stuff. (t: 650) And let's see. I think what I could do is just maybe describe. (t: 660) I'm just going to do like a really quick primer here, just like something off the top of my head. So I want this to be an expert designer that's going to go through and use some design principles. (t: 670) And some of the design principles will involve like color rules, typography systems, clean visual structure, and try to make sure that whatever code was generated follows our foundation of the tailwind v4 integration. (t: 690) I think that's it. Yeah. Okay, cool. So I'm using something called Whisperflow. (t: 700) And it already kind of did this type of like markdown stuff for me already. Okay. Cool. It might work some other way. Some more path. Maybe I can. (t: 710) Oh, it's called fk2. Yeah. Okay. So I can actually create a file here and then I can insert another one in. And then I can see that it's like a pretty good build. And then I can do a little default, which is pretty crazy. I was not actually expecting this at all. So I'm just going to do this for now. I just want to see what file it creates in my repo and then I'm just going to go ahead and replace it with my own rules. So I'm just curious to see how Clawd is going to generate this type of description for us and see if it actually expands upon what I said here. (t: 720) Because that would be really interesting, right? Because a lot of the times, it's generating agent configuration. here. Because that'd be really interesting, right? Because a lot of the times he's generating agent (t: 730) configuration. So I think it actually is using some type of model, probably Opus or probably, you know, Sonnet to generate like an agent based off of these instructions. So I'm a really, (t: 740) really big fan of having AI models write instructions for themselves. And I think (t: 750) that's what it's doing here. And it's really, really cool to see. So let's just go ahead and pop back in here. I'm trying to see, did it actually write a file? I don't see anything yet. (t: 760) Yeah. Okay. So a new agent selects tools. So we can say, if we hit the down arrow, you have to hit the space bar to toggle or enter to toggle the selection. So if I hit enter, it'll say, (t: 770) it'll toggle them on and off. So, and then up and down the navigate and escape to go back. So I'll say continue, right? Because I want everything to show individual tools. That's cool. (t: 780) So, I want all the tools. I want access for the agent to do MCP. Yeah. Yeah. Yeah. Okay. Cool. But yeah. (t: 790) Ooh, background color. Okay. Okay. I like this. I like this. I could dig this. This is going to be really interesting. So let's go ahead and see. For a designer, (t: 800) I feel like designers are purple. Yeah. Y'all are purple out there. Purple because like Steve Jobs' favorite color like that. There's a whole thing behind this whole purple thing. Okay. So the name (t: 810) is the design system. I'm going to go ahead and type in the name of the design system. And then I'm going to type in the name of the design system. And then I'm going to type in the name of the system enforcer. Okay. Okay. Watch out. (t: 820) All I would, that that's not my first name, but yeah, I'll roll with that. Yeah. Use this agent when you need to review or refactor UI code to ensure it adheres to the established design (t: 830) principles, ensuring color harmony, typography, consistency, and the clean visual structure. Yo, I'm not going to lie. This is very hot for a couple of reasons. One, I sort of described in (t: 840) English what I was thinking. About, but then it kind of took that and added it a little bit further. And I'm wanting to learn (t: 850) here and see how these like sub agents are created from Anthropic. And maybe we can kind of make my (t: 860) own custom blend of rules using my existing principles, because I don't know if you know this, but remember how we're talking about context windows, those rules files take up some part of (t: 870) that context window. And if the agent has to go through and grab code and fill up the context, you can do that. And if the agent has to go through and grab code and fill up the context, window, we can slam 50% just on the first request. And every single chat that you add on after that (t: 880) will just keep adding to that context window. And that's where things kind of get out of hand. And so I'm really curious to see, I'm pretty sure they've tightened everything up (t: 890) and made it more efficient here. And we can learn a lot from this. I'm learning a lot from this. If you're learning a lot from this, drop the fire emoji in the chat for sure. (t: 900) Yeah. Big shout outs to tool use, the AI conversations dropping about that a hundred percent purple. Definitely a lot of people are going to be wanting to use that tool. Definitely appreciate everyone who's been able to participate. Also, Alberta, who's a member, a sub agent with its own context window is fire or sure. Yeah, yeah, yeah, (t: 910) man. Y'all. Wow. Okay, cool. All right. All right. Also big shout outs to dig bros to finally (t: 920) able to make it to the live stream. I definitely appreciate that. All right. So let's go ahead and see. This keeps going. It says it's a very warning. Description is very long, over 400 characters. (t: 930) Agent has access to all the tools. Okay. Okay. So hit enter, press S slash enter to save and or E to edit in your editor. (t: 940) Oh, so if I hit E, it'll pop into, uh, cause I'm using cursor. It's just going to leave it (t: 950) inside of cursor. Y'all want to see y'all, y'all want to see what they put out? I mean, that's kind of cool. I feel like if I save it though, I'll still be able to see it because (t: 960) it's going to be in this type of folder here and then I can edit that later. Right? So let's just go ahead and, um, dang, I'm just, I just hit enter and you can see it already created a slash agents (t: 970) folder with the actual doc. And this is the documentation. Bruh. Way, way, way, way, way. (t: 980) Did it know? Hold up. Hold up. How does it know about this? It, it knows about like the typography (t: 990) system. So, uh, I don't know. I don't know. I don't know. I don't know. I don't know. I don't know. I don't know. I don't know. I don't know. I don't know. I don't know. So somehow it had got context in my code base about these rules. Cause literally these were in my design format and it put them in here. (t: 1000) I don't know what type of drugs you're, you're smoking anthropic, but damn, you're good. (t: 1010) Like it went through my code base and somehow probably found that file about my design reviews and these key terms. And it slid them right into its own agent. Be like, (t: 1020) Yo, I got you, fam. I didn't even have to copy and paste it. Remember how we were thinking about doing that? Oh, man, these AIs are getting real scary. (t: 1030) They're getting crazy smart. (t: 1034) I love it, though. I love it, though. This is what I live for. This is how I'm going to be a solo engineer with a team of agents. (t: 1040) Oh, my God, this is too good, man. (t: 1045) Probably asked it at the beginning. If you want this project to be a project or personal agent. (t: 1050) Yeah, I said project specific. So it probably has access to the scope of that. (t: 1060) So, yeah, this was basically my request. And even has the chat commentary from what the agent was thinking inside of its little design description thing. (t: 1073) Okay, I think we can really cook with this. Yes. Boy, this tool. (t: 1080) This tool. Wow. Wow, wow, wow. Look at the way it describes even like the type system and like the visual structure. I didn't give it the eight point grid system. (t: 1090) I think I was going to say something about eight point grid system. I don't remember if I said it or not. Oh, my God. Okay, so I'm still blown away. So, okay, now it has two agents. It has a design system enforcer and then the built in general agents, which are always available. (t: 1100) And then now we can go through and create a new agent. If we want to go ahead, we can go back or enter to escape. Man, this is crazy. (t: 1110) Okay, okay, okay. So now that I have this enforcer, I'm just going to go ahead and check this into my code. I have a separate branch set up for this because I wasn't really sure if this thing was going to totally bonkers our whole config. (t: 1120) So I created a branch and we're just going to keep rolling this way. So I made a little type of thing here. I'm going to publish it. So it's available. (t: 1130) Oh, my God. This is really, really great. Okay. So I want to see if maybe we can even fix some bugs and see if we can fix some bugs. Okay. So I'm going to go ahead and see if the agent either it should kick off. (t: 1140) It should just basically figure out the design system. And I'm just going to try to see if I can I can call it from there. So, yeah. So this basically configured itself much better than this would have. (t: 1150) And yeah, I think that's it. I mean, so these agents can also call MCPs and they can also call specific tools, as we can see there. (t: 1160) And even has file management so you can create a project subagent. So. Oh, this is for like actually creating the agent and so forth. Explicit invocation. (t: 1170) So to request a specific subagent by mentioning it in your command. So use test runner subagent to fix the failing tasks. Oh, so I can say, hey, use our cut. (t: 1180) This name is horrible. Design system enforcer. Oh, my gosh. I miss. (t: 1190) I'm kind of behind. So every time I wouldn't hit it like a like or on the YouTube algorithm, I have to like do this little dance. So like I have 11 of them to do. I think that's one, two, three, four, five, six, seven, eight, nine, ten, eleven. (t: 1200) Okay, cool. We're good. All right. But whenever anyone does a live, I do this little dance. If you see me dancing like this while I'm talking, I literally saw like a little like come across the screen at the dance like this is so funny. (t: 1210) But whatever. It's my thing. Anyways. Design system enforcer. Okay. (t: 1220) Dang. Have the code reviewer subagent look at my. So they have to say the name of the agent. Plus. Okay. The subagent like keyword right next to it so that it can kind of pick it up. (t: 1230) So this is this is what they call it an explicit invocation. If I just give it some yellow task, eventually it may come around to using that subagent. (t: 1240) So and we should see that new purple color as well. By the way, ask the debugger subagent to investigate this error. Oh. (t: 1250) Okay. Hold on. We got to do one on optimization principles. Oh, my gosh. Oh, my goodness. Oh, wow. I don't know. Are you guys hyped about this? I am so hyped. (t: 1260) Let's just get this going. This is so crazy. All right. All right. Oh, okay. I'll come back to this. We got to get this going. I want to see this. I want to see this cook right now. I do want to see this cook. (t: 1270) All right. So let me show you real quick on my app. I'm not really proud of this moment, but it is what it is. I think I was valuing shipping over anything. So in here, let's see. I'm going to go to my app. I'm going to go to my app. I'm going to go to my app. I'm going to go to my app. I'm going to go to my app. (t: 1280) I'm going to go to my app. I'm going to click on the store now. So I'm done with that. I'm going to go to my app. I'm going to go to my app. I'm going to go in my screens. I'm going to go to my other app, but it is what it is. I think I was valuing shipping over anything. So in here, let's see, BunnX. Convex Dev. (t: 1290) And actually this is the wrong chat. Your BunnX convex dev. I'm just getting my database stuff. Make sure everything is all synced up. I pushed the change last night. I would like the middle of the night half asleep. (t: 1300) Closed my laptop. It went live in production. So for like six hours, people were trying to load my app and it was crashing. And I forgot to push this command in production to push my migrations. (t: 1310) And once I did that, everything was fine. I was like, what's going on? So I missed on some signups and things, but it's okay. (t: 1320) Let's see, Bundev. Let's run this thing. I'm pretty sure it's happened to you, right? I'm a solo engineer. (t: 1330) I wanted to make sure my changes landed and I was just really excited. To just kind of also sort of get it done with, you know what I mean? All right, cool, cool. So this is now loading up. (t: 1340) Am I still transcribing something? Oh, I have a bug I need to fix. I need to clear the state because I did this migration. So in my database, what I'm going to go ahead and do is in my transcription job, (t: 1350) I'm just going to clear out the state because it's totally borked right now. (t: 1360) And this may happen to you as you're going through like breaking changes in the database. What's nice about it is that theoretically, yeah, all of the states should clear up. Great. (t: 1370) Everything's being pulled from convex. So all I have to do is basically just describe, you know, like write the hook for the front end and just going to pull the data for the back end. So every transcription gets pulled up this way. (t: 1380) And so in here, I want to fix like some of this layout. So there are these types of things. I'm just going to give it a screenshot. (t: 1390) And when I... And so... Somebody said that I can hit Control-V to paste it in. It says no image found in clipboard. (t: 1400) Use Command-Control. The Command-Control-V, is it? No, it doesn't do that. Okay. So like, I guess when you get the screenshot, it should also show up in my... (t: 1410) Yeah. For whatever reason, my screenshots aren't going into my clipboard and I have to drag (t: 1420) them in from Finder. And that's kind of... That's a problem I had. And I have to probably figure this out later. And I'm not going to debug this on the stream. But basically, this is a screenshot of our app. (t: 1430) And then I'm going to see if I can use the design system enforcer. Hello, Mr. Cloud. I'm going to use you. And see if I can have it do some design system enforcing. (t: 1440) And I'll describe that. Please use the design system enforcer to help me fix the UI here when I hit all transcriptions. (t: 1450) This also needs to work. This also needs to work for mobile as well as desktop view. So that's... We will have the appropriate design system for viewing all of my transcriptions. (t: 1460) Boom. All right. So I just said design system enforcer without the dashes and stuff because this is just (t: 1470) what I do for dictation. Let's see if it picks it up. (t: 1480) Right? Like, yeah. All right. So like, let's see. I love all the participation in the chat. You guys are the real MVP right now. I like the fact that everyone can just drop in and just drop some sauce on me helps me (t: 1490) so much. So I really appreciate that. Command control shift four. Then it will go to your... I did it all wrong. (t: 1500) Oh my God. Command. So I have to hit the command control shift four. Okay. Because I normally do command. I have this. I have a memory. (t: 1510) I don't even remember what the actual keys are. I just go like this. And I know when my hand lands on the keyboard that this thing always happens. And I go like this. If I wanted to do a full screen, I would do like this. (t: 1520) So I have to look down on the keys like, what's... Oh, okay. So I have to now change my little like... Add my little thing like this. I'm like, oh, claw. (t: 1530) So if I do that, then it goes in the clipboard. G Costa, you're a real G. You are the MVP of the chat today. By the way, if you're new to a Mac or an experienced engineer like me, (t: 1540) who used to work at Apple for like, I don't know, 13 years. I do remember there's some more way in the back, but I never used that command. (t: 1550) Because I don't know why. I would always get the screenshot and I would drag it in. But on a live stream, because of my software, it doesn't make it easy for me to just get the screenshot and drag it in and do this dance. But yes, you are the real MVP for this. (t: 1560) So command key, control key. They're kind of right next to each other. And then you got the shift key here with a little like this, this finger there. (t: 1570) And then four. So I have to kind of do this. All right, let's go. Do you want to proceed? Yes. (t: 1580) Let's go. Drop some sauce. Whoa. Y'all know about that. If you're cooking, you know what's going on with the sauce. (t: 1590) And if you're from the hood, you know what's going on with the sauce. Yeah, yeah. It's a double meaning or triple meaning, which is kind of cool. Actually, it's really these many meanings and accents. (t: 1600) And I think that's what's going on. I'll be able to what you guys been up to. I love to hear from my members and stuff. Star. Yes, I know. (t: 1610) Right. Like just those little secrets are so worth it, dude. Like, yeah, dude. G Costa, MVP of the chat right now. (t: 1617) Happy Friday, y'all. (t: 1620) Dude, we are cooking right now. And Claude is clouding right now. And it's basically going to be using our new design system enforcer. But it doesn't appear to be using it right. (t: 1630) We'll see. It's currently reading the image, but I was hoping it was going to kick off a sub agent for it. (t: 1640) I don't see the sauce right now, but I'm going to give it some patience and a little wait for it here. Let's see what happens here. Come on, Claude. (t: 1650) Is it me? Oh, yo, we got it. We got in the chat. Let's go. Let's go. Let's go review and fix you. (t: 1660) I just. Yes. Yes. Okay. So now what we're seeing is it's kicking off my design system enforcer, which is all of this prompting stuff, like 73 lines, which is cool because that's my regular design system prompt is like several hundred lines. (t: 1670) And it's going to have its own context window, has its own dedicated agent with these specific instructions. (t: 1680) And so I'm curious to see what code changes start rolling through so that I can go ahead and review them and see if it actually makes a difference. Because we're just trying to fix this like janky UI that I just tried to ship as fast as possible. (t: 1690) And we're also trying to see if it can actually work on mobile a lot better because you can see on mobile yet to do the sideways scroll thing. And bro, I am not having that right now. (t: 1700) But hey, I shipped. So that's really cool. So for. For. Yeah. You can just basically click here and I should be able to like grab like a short or something and then just generate the transcripts. (t: 1710) And so it basically will do some processing on device. And I figured out some secret sauce about web workers where you can have these threads. (t: 1720) So I'm basically trying to. Bring mobile development forward to the web. And I don't know all the APIs and everything. So I'm kind of learning as I go. But basically this is an app that I would probably would have normally done like on iOS with Swift UI, probably with less lines of code. (t: 1730) But like I'm trying to learn web development and I really want to really push myself to learn something new. (t: 1740) And so this has been a really fun experiment to experiment with databases on the web and play around with these different types of things to do. You know, learning about all these different protocols and stuff that I have no clue about. (t: 1750) But we're learning together and I'm learning how to use cloud code to be my designer. And hopefully we can kind of fix some of this. This jankiness. (t: 1760) It's still going. Dang. So it. I don't know if it's a. OK, so I wonder if there's some limitations that are going on right now with the API and maybe it's a little bit slower. (t: 1770) So we'll see. Cloud agents are pretty great. I've been waiting for this feature for a while now. I've made like 30 agent system for RU code, bro. (t: 1780) Bro. Do they kick off automatically or do you have to then tell them like I want to use this agent or I want to use this agent? (t: 1790) Just really curious. Insanity. There's so many use cases for this. Dude, I'm telling you. I mean, did you see my poll? Like if you haven't voted in the poll yet, make sure you go ahead, check it out on YouTube. (t: 1800) You can actually vote for which is your top agent. I need to actually log in and see if I can actually see how many people have voted on the poll. (t: 1810) So let me go ahead and see if I could do that. On YouTube. So let's go ahead and see. I wish YouTube had like a stream manager for the studio. (t: 1820) And yeah, I wish YouTube had a stream manager for. This stuff, so that that way I can kind of see everything here and like actually manage the poll and stuff. (t: 1830) So studio that YouTube. Let's see, stream manager. All right, cool. So it's come back and it says it wants to do stuff. (t: 1840) All right. So you want to make AI edits. Let me kind of get rid of this terminal for now. All right, cool. (t: 1850) Yes, these are all the code changes to the components. So in this view file, I have my font increased a lot bigger. (t: 1860) Maybe I'll just make a little bit smaller. So it's easier to see. I know it's gonna be easier to see for me. All right, cool. So what I'm doing now is. (t: 1870) I'm. I'm just going to go ahead and edit this. It looks like it. Oh, OK, so this is the file that it generated and it's asking me to do the edit. I'm just going to do a quick cursory glance at this and it is doing the whole divisible by the eight point grid system because previously you could see P Y four P Y three and now it's doing four and six and it's actually adjusting for the actual mobile views and stuff as well. (t: 1890) OK, OK, OK, OK. Let's go. (t: 1900) I'll say yes and don't ask for the session. OK. I like what it's doing here. Let's see. So now we have one of the code changes landing and let's just see if I can actually see this live right now. (t: 1910) OK, a lot better. A lot better. OK, let's see if I can make this mobile view. OK, it's improving itself. (t: 1920) Oh, let me refresh it because it has to redraw the view. OK. (t: 1930) And then I maybe have to get another screenshot for what I'm doing. OK. Let's see what it looks like on mobile. Yeah, but you see all of these icons are now like the right size for previously like one would be smaller. (t: 1940) And then if the file name was too long, it basically would like not do the right word wrapping. Oh, oh, we're seeing this change in real time, which is pretty fun. (t: 1950) OK. Ooh. Let me just refresh here. Wow. (t: 1960) Yeah. Oh, hold up. Hold up. Oh, my God. Oh, I know what happened. It linted like all of my files. That's nasty. (t: 1970) Oh, Claude. How do I? Oh, my goodness. OK. Let's see. (t: 1980) All right. So we go back here. So it's getting better for the mobile view because you can see it actually takes out some of the stuff. And that's probably the second iteration that I'm going to do, which is nice. (t: 1990) You know, it just gives you the information you need kind of wrap some of the things here. And that's probably has not accounted for that. But for the desktop view, it's kind of nice. (t: 2000) It's very reactive. And I like this a lot. You can kind of see it kind of adjusting itself. That's pretty cool. Actually, I like this. (t: 2010) It's nice because I don't have to do this extra prompting loop. I could just say it. And, you know, once it's done something, things, once you're done, you're done. Coding this way, you can maybe add a cloud code hook to say, just do this type of task afterwards or something or make sure you always do a design review, which would be really, really cool. (t: 2020) So, however, I noticed the issue from the screenshot that it needs to be fixed and the table needs better horizontal scrolling and column width management to prevent file names from being cut off. (t: 2030) So what's amazing right now is that remember how we saw one design is like, oh, this is better. (t: 2040) And then when we were just live that that quick switch up. So what's happening right now is Claude is actually going through and doing a secondary pass on the output and reviewing. (t: 2050) Am I done yet? Is everything solved from what the user was originally asking? (t: 2060) And then it's thinking by itself and say, oh, if this is changed now, this needs to change. Let me go ahead and see if I could find it in the code. (t: 2070) And that's kind of the advantage of using some of these subagents because now it has more token windows because it only use 50K tokens to continue through. And that's a good thing. And then actually kind of making a better pass at it. (t: 2080) And that, to me, saves me prompting time as a solo engineer. This is super duper helpful and super duper resourceful. And I'm glad it's kind of doing this on its own without me really telling it to do what it needs to do. (t: 2090) So now it's actually checking to see if it can run the server. And it did a second pass. And now it's basically kind of fixed that. So it's like it fixed the table layout view. (t: 2100) It fixed the file name display and remove the truncation in favor of break words to show the full file names. Nice. Okay. So we should now see. (t: 2110) See? In that couple of iterations, like Claude code by itself figured that out. That's my favorite thing about Claude code right now compared to a lot of other agents is that fact that it's using that output. (t: 2120) It's thinking it's coming back out. I'm starting to see that appear right now with cursor agents as well. And their amp agent also does something like this as well. (t: 2130) So those are a couple of ones that I'm kind of seeing right now in the pipeline. But this is really, really cool to see. Wow. Man, that's a big improvement so far. (t: 2140) You know, and if I'm in here, if I do like refresh, let's see if I can refresh it to my server. Oh, I need to refresh my server. I made a whole bunch of changes. Oh, my gosh. (t: 2150) Iran linting on everything. No. Okay. That's a different problem for a different day. This is why I created a different branch. Okay. Let's go back here. And we're basically on the mobile size. (t: 2160) So we should be able to see everything. And I'm going to go to all transcriptions. Cool. So in here, can we get that same treatment for mobile? So I'm just going to do this. (t: 2170) Oh, my bad. See, my habit is command shift four for the screenshot. And I'm like doing this type of thing. But check this out. I have to do command control shift and four. (t: 2180) And then apparently that should put it into the clipboard, which then I can copy and paste right into cloud code. Right. (t: 2190) So if I do this type of thing here and I get this and I'll see it. Okay. So I'm going to do this and I'll say I just go back in here. Command V or control V. (t: 2200) Yeah. Control V. Okay. So and this is a lot of different things. So it's funny because I'm a longtime Mac user and I'm still learning things every single day. (t: 2210) So big shout outs to everyone who's been in the chat and all the MVPs in the chat for doing this type of thing. And that's G Costa. So G Costa is the one that came up with this thing. And it basically says you have to do control command shift four. (t: 2220) Select the stuff you want to copy. It puts it into the clipboard. And then you can't just command V like you normally do on the Mac for this specific command. (t: 2230) You have to do control V and that'll grab it from the clipboard and put your screenshot that you just selected in there. I can't tell you how much of a lifesaver that is. (t: 2240) And it's going to be probably a lifesaver for a lot of other people who are in a similar workflow. So you can just kind of move really fast. But these little micro things do help you a lot. (t: 2250) So I'm going to say. Please do a second iteration. This is working really great so far. And I want you to do the iteration on the mobile view right now. (t: 2260) Because in the mobile view we need that same type of treatment for the titles. Because as you can see they're very long as well. (t: 2270) And so what I can also tell it is. Shift enter a couple times. I notice you ran linting. And it ran linting on files that we didn't use. (t: 2280) And it didn't touch in the code base. I want you to go ahead and review and discard the changes for the files that don't require linting right now. (t: 2290) I don't want to check in all that code. I just want to check in code that we have specifically modified. Because all of these files were touched when it ran the linting command. (t: 2300) And then eventually I want to tell it some rules to go ahead and fix it itself. So go ahead and hit enter. And let it do its thing here. So yeah. (t: 2310) Hertzfeld Labs. How's it going man? Big ups. Yeah. Good to see you on YouTube man. I really appreciate that. I built a stock analyst. So many use cases. For sure. Fire sub-agents using Cloud Hooks. (t: 2320) Dude. Cloud Hooks. So this is where it's all going right? There's all these different ways of kind of stemming them together. I would always say start simple. (t: 2330) Start with your own workflow. Start with something you use a lot. Because you're going to see the most impact. It's really tempting to go into these YouTube videos and see these people create like 50 agents. (t: 2340) All kick off. Do this cool thing. It's kind of like fun to see. But you're number one burning through your whole usage. Right? Like is it actually helping you generate better code? (t: 2350) Usually no. Because simpler is better. And context window is a big deal. So yeah. Just keep it simple y'all. I'm a little old school. But it is what it is. It's so weird being like a really young person saying I'm old. (t: 2360) But I'm an old soul. That's the way I feel. I've gone through a lot of experiences. All right. Cool. (t: 2370) So this is going to undo those changes. Yes. Please. So these are the files I basically did not touch. And I'm glad it's kind of doing that. And so I think yes. (t: 2380) We're basically back to that just one file that we changed in our code that had the changes for us. So perfect. Has the changes. Let me run a linter on just this file to make sure it's clean. (t: 2390) And it's doing some command. It failed. And it's going to try to see if it can do some cool stuff here. I'm going to try to stage this change so that way I don't lose it. All right. (t: 2400) This is cool. Yeah. This makes more sense. And well, look at that. On mobile, it's already fixed. Wow. This is clean. I like this. Yo, not going to lie. This is noise. (t: 2410) So if I go the... Sheesh. Let's go. Man, this is now looking like a professional app. One day at a time, right? This is all I have time to do. It's like one day at a time. I'll work on one feature. But setting these types of subagents are so helpful because now we're going to start to leverage the AI. And I'm going to go ahead and do that. (t: 2420) I'm going to go ahead and do that. I'm going to go ahead and do that. I'm going to go ahead and do that. I'm going to go ahead and do that. I'm going to go ahead and do that. And I'm going to go edit it. I'm going to go edit it. I'm going to go edit that. I'm going to go edit that. I'm going to. (t: 2430) And then it looks like the set up. You see the layout. Okay. All right. All right. We're good for the day. So I'm going to. You know it now? You know I'm going to hit this block and this missions list, and then this. It's going to go to sdc. (t: 2440) All right. Thank you. Okay. So I added that. Let me make sure. So the task is to direct your tool. Right. If I click on appeal. It will worry you. GitHub issues on the web. So when you tell it, I want you to go do this. I want you to fix my (t: 2450) design, this exact prompt that I had to go fix my design prompt. I don't, maybe I don't even have to (t: 2460) give it a screenshot. Sometimes I could just say, go fix it. It's going to run that agent in a container and it's going to kick off some of these sub agents as well to help you solve that issue. (t: 2470) So by preparing yourself to use these in your app, just as you do as like a human being, eventually you're going to automate that away so that later on you're now sitting in GitHub issues. (t: 2480) You're sitting in a Slack command. You're sitting in somewhere else that will go write the issue in and then Claude will kick off and start solving those problems. And this is the workflow that I'm (t: 2490) trying to get into that the Claude code team currently does. And all the folks at Anthropic, I think it's like over 80% of their code is written by AI and more specifically by Claude (t: 2500) code. So by leveraging that and their workflows, we get to bring that into our thing. I'm a solar developer. So can you actually be solo and be a designer, be an (t: 2510) engineer, be a project manager, be a person who orders pizza for Ray Fernando, make sure it's gluten-free. Thank you very much. Yes, you can. Right. Because we start to define them slowly. (t: 2520) Right. And this is how you can leverage those workflows to do that type of thing. Holy smokes. This is really cool. And we only got one of these up. Right. So (t: 2530) man, that's, that's beautiful. Okay. So it did the linting thing. It did this thing. I'm going to do this thing. I'm going to check in this code. This is fire. This is like, it totally banged it out. (t: 2540) Like almost in one prompt, right? Like I really just said, do this. Here's the screenshot. Okay. (t: 2550) Make sure we do the same treatment for mobile. Cooked. Absolutely cooked. Look at that. Damn. Let's go. This is gorgeous. You know, I probably would like, this is a whole nother bug to fix (t: 2560) and we could probably do that as well. I have lots of bugs to fix. It's great. But shipping is better. Like shipping is better than perfect. I guess it's probably my new term here. (t: 2570) So shipping my app is probably better than me waiting for it to be this perfect. Is a user going to care that much? No, they just want the transcriptions. They came in here for a job. (t: 2580) They're going to get this stuff and get out real fast. And so my app has gone a long ways from the very beginnings from just being a super simple vibe coded app to like actually having accounts (t: 2590) and having like this history and then being able to check this out. This is something that I added, which I'm super cool. But basically it's like a multi-threading system. So in here I can take like (t: 2600) a live stream. I can start to generate the transcripts. And what's nice about it is now (t: 2610) kicks off its own process. So I can review the other transcripts. I can come back to it and it still has its progress. I can go into here, view the other transcripts and do other types of work. (t: 2620) So it starts to actually feel like a solid web app versus like, you know, just a straight up static page. And so this is something that, yeah, I have to tell like there's some keywords like called web workers and some other things that (t: 2630) I've been playing around with, which allow the task to actually run in the background. And I probably don't show it here. Oh, yeah. So, yeah, I have a web worker that's running in the (t: 2640) background. So wherever I'm navigating, it's still navigating the background. Another thing that you may want to do whenever you have these web workers is you want to not block the main thread. So the (t: 2650) main thread is basically where everything is happening in the user interfaces and any types of instructions. A lot of times when you're coding these types of things, you're not going to be able to do that. So you're going to have to do a lot of processing. And so if you're doing a lot of (t: 2660) processing like this going on, it's eating up tons of memory in the actual browser and the system is only going to allocate so much for you to do those types of things. So what ends up happening is that (t: 2670) then you start competing with all of the user interface elements. And so if a user wants to tap a button or scroll around or do something, you'll see it really laggy, really slow while (t: 2680) this job is being processed. And that's everything that's just happening on the main thread. And think of the main thread as just like the main highway of things. So if the highway only has one lane and you start to just have like an on-ramp that you put on (t: 2690) and you're shoving a bunch of stuff into the on-ramp, all the cars that are coming are going to start slowing down and be super slow. So you want to get that stuff off the main thread and (t: 2700) you can have Cloud Code help you figure that out. And they can set up these workers that can run in parallel threads and then be more efficient about kicking off those threads and coming back (t: 2710) and closing them. So that way everyone, like now that you have an on-ramp, now you have maybe two or three of those extra cars and resources that are coming through, you still keep the main ramp, (t: 2720) main like highway clear. And the main highway is all of your user interface elements. Anything that users are going to interact with, they should just feel like it's working while the other stuff is (t: 2730) processing and happening in the background. So just a hint, FYI, something that I just learned recently about how to apply this on the web and kind of what that looks like on the web. And so, (t: 2740) yeah, you never want to block the main thread. If you take anything away, don't block the main thread. You can ask those types of questions in Cloud Code to help you dig through your code or help me debug (t: 2750) and see, like, I'm having a performance issue with my interface. It's really slow. Can you explain how this is currently rendering or can you explain what's happening on the main threads or whatever's (t: 2760) happening? What type of processing am I doing? Just start asking questions right in the chat window. You'll start to get very far very fast with these types of things. So, damn, damn, we cook. Yeah. (t: 2770) FYI, if you want to go, oh, McKay, what's up, man? If you want to. Screenshot without having to do the claw defaults, right? Calm Apple screen capture target clipboard (t: 2780) in Mac terminal and it'll save you a control step. This is also a straight up MVP hack as well. So (t: 2790) Apple has these defaults that you can write and these are settings. Whenever you go and toggle something, you say, I want this preference to be safe. Apple writes them to these defaults and you (t: 2800) can actually modify them in the terminal. And so in the terminal, you just literally write that exact command. You know, you take out the little ticks and you just write the defaults. And then you go and drop your defaults. And you say, oh, I'm going to add this right. Defaults right. And you (t: 2810) target that basically reverse global domain space. And my camera should not be frozen anymore. Once you do that, then you should be able to have this as a default for your thing. And so you can do (t: 2820) defaults, delete. And then that calm that Apple that screen capture. And that will basically delete that specific, you know, that default that you just said. So if you're curious, like, OK, I said (t: 2830) this, but I don't like it anymore. You can say default, delete and then calm that Apple screen And that'll basically delete that default from you. So you can basically enable it by writing it (t: 2840) and then disable it by that way. So that's a really great pro tip, McKay. That's really, really great. WebDevCody, 125 views on the live stream. Loving your growth, man. (t: 2850) Dude. Oh, Cody, I love your content. I've learned so much about web development from WebDevCody. If you don't know, WebDevCody is a legend. And right now, what I like about what he's doing (t: 2860) is that he's actually teaching you how to write software. And that's super important for this AI age because you want to learn how the software is made. And I wouldn't be able to get this far (t: 2870) as far as trying to like sort of even prompt my way through if I didn't have some of this baseline understanding. And I would highly recommend you check out WebDevCody if you're (t: 2880) not subscribed. Probably one of the most popular subscribers here on YouTube or one of the most popular content creators on YouTube, especially for writing code. He likes to try (t: 2890) all sorts of different things. He likes to try all sorts of different things. He likes to try all sorts of different things. He likes to try all sorts of different things. He likes to try all sorts of different things. And it's definitely a must as well. Yeah, I've been live streaming every single day for my summer of streaming. And I've been really delighted and surprised by how many people (t: 2900) keep showing up every single day to hang out with me and learn these concepts live. So if this is something you like, definitely hang out some more. Subscribe. Definitely share with some friends (t: 2910) because it definitely goes a long way to get more awareness out about what we're doing here. And I really feel like this community is really great that we're building together, you know, just kind (t: 2920) of hanging out. So I'm really excited to be able to share with you guys. And I'll see you guys soon. Bye. (t: 2950) Bye. (t: 3010) Bye. Bye. (t: 3040) Bye. (t: 3060) Bye. (t: 3130) Haha. (t: 3152) Bye. Bye. Bye. Bye. Bye. Bye. Bye. Bye. Bye. Bye. (t: 3160) Bye. Bye. Bye. Bye. Bye. Bye. Bye. It's like, yeah, this is not that live stream, but maybe in the future, right? (t: 3166) Dude, drop the pizza emoji in the chat if you're all about the pizza right now. (t: 3170) I feel like that's my energy right now. All right, let's go continue. All right, let's continue. Yes. (t: 3180) And then in this one, all right, we got to have the red pizza, the pizza order specialists, because we're cooking with the sauce boy. I thought you knew about this. And so this is my pizza order specialist. (t: 3190) Use this agent when you need to order pizza online for pickup, especially when there are specific dietary requirements like gluten-free options. The agent will research local pizzerias, check their online ordering capabilities, verify (t: 3200) gluten-free. In the middle of my coding, I want to see if Clot Code can know that I'm hungry. (t: 3210) It's like, yo, it's 12 o'clock. Ray has been coding like crazy. For the last couple of hours. (t: 3220) And I think he's a little hungry because like he's starting to yell at me and be a little bit more impatient. He's usually really nice, but then maybe he's hungry. I'm going to go ahead and get a sub agent on that and come right back. (t: 3230) Ladies and gentlemen, that's how you know that we have artificial general intelligence. When these things start to really start to read our mind and really kind of read between the lines. (t: 3240) I don't know, man. This is I don't know, man. This is this. I don't know if I'm ready to hit enter on this right now. (t: 3250) Drop the pizza emoji in the chat if we should hit enter and cross the chasm of us actually seeing if this AI is going to be smart enough to like really take us there. (t: 3260) That's how we know they have really taken over. Because a couple of weeks ago in our live streams, you remember this? (t: 3270) The Clod Code hooks one where we had Clod Code write the hooks. And the hooks were too aggressive. So it self rewrote its own rule set using its own model and said, hey, you can't be. (t: 3280) I can't. I don't have access to that. Let me rewrite the rules so it's a little bit more chill so I can go ahead and do what I need to do. (t: 3290) I don't know about all that. I don't know about all that. (t: 3300) Yeah, I don't know. Alberta with the pizza. Jeremy with the pizza. I mean, yeah. Okay. (t: 3306) I don't know what. Okay. My credit card. (t: 3310) It doesn't have my credit card details. But if it uses the Stripe MCP that's already built in, I'm kind of regretting that I have the Stripe MCP enabled. But it can use the Stripe MCP and use that money in that Stripe account. (t: 3320) So all $10 that's in there is just going to be wiped out. (t: 3324) Oh, my God. Web Dev Cody with the pizza. Oh, you had a brush with AGI, my man. (t: 3330) Man. Pizza plus plus. Yes. Bro, I'm not ready for this. Bro, what are we cooking? (t: 3340) Bro, we're about to order like 10 million pizzas and I don't know what's going to happen right now. So let's go ahead and go. Let's go. All right. So the pizza order specialist is here. (t: 3350) We got the pizza. (t: 3354) Let's take a look at these instructions. I'm really curious. (t: 3360) I'm like super duper curious. Let's see. I don't know if I'm ready to read this. But let's. Let's see. Bro, what does it know about me as well? (t: 3370) Okay. Use this agent to order gluten free pizza. Like if it writes code to like spin up an 11 labs voice and it starts to talk to the thing and starts to do its own feedback loop. (t: 3380) I don't know what to say. I don't know what to say. I swear to God. You're a pizza order specialist, an expert at navigating online ordering. (t: 3390) You will. Low key. I want to run it. You will prioritize restaurants with good reviews and gluten free options. (t: 3400) Bro, this spec is fire. Handling edge cases of close locations unclear request specific address. (t: 3410) Okay. I think I think we're unleashing a monster right now. Oh, let's go. Let's go. (t: 3420) Yo, I'm starting to get really hungry. And I think I low key need a pizza. (t: 3425) Let's just see. Let's just see. I don't know if I'm ready for this. (t: 3430) I don't know if I'm ready for this right now. I don't know if I'm ready for this. (t: 3433) Oh, wow. (t: 3440) Wow, bro. If the FBI shows up in my chat, I can't right now. I can't. (t: 3450) Oh, burger. Heck yes. Oh, burgers right now. Oh, burger. Sounds so good. Check cursor over VSC root code. What do 20 bucks a month? I don't know if you know this, but the 20 bucks a month plans are over, bro. (t: 3460) No one's doing 20 bucks a month anymore. You have to come in with this high key $200 a month plans if you're going to be agent encoding. But all jokes aside, 20 bucks a month plan cursor VSC. (t: 3470) I don't know. Like, I think I think the conversation should be more about. (t: 3480) Oh, dang. I'm going to give it access. Yes. Go ahead and go search the web. Ruben, I think our conversation here is like. (t: 3490) I kind of had a live stream about this, but like, are the 20 bucks a month plan codes dead? Yeah. And it's now about two different camps. (t: 3500) Are you a tab to code person, which you're hitting tab to code and you're kind of going along? You need some assistance. Or are you an agent encoder person? (t: 3510) So I feel like if there's those stats on, like, you know, you're going to have to go to the website. You know, Ray Fernando, like engineer status, like tab to code would be like very, very little, like minimal. (t: 3520) And then like agent encoding would be like to the max, like breaking the threshold and expanding beyond. And so if you're going to be doing that, you're going to require a lot more compute and you (t: 3530) have to pay for it. So these days, basically what all these companies are saying is like. Now, you can't you can't get that compute. It's going to cost you 200 bucks. It's going to cost you $100. (t: 3540) It's going to cost you $69, you know, from all these different platforms. Because you're going to be spinning out doing lots of tokens. So think of it that way. You know, if you're a tab to code type of guy, you want to remain more in the code. (t: 3550) There are really good options. And cursor is really great along with these other ones, right? Just to tab. But I think cursor has the best tab model, to be honest. And it's not a bad place to be right now. If you want to actually just get away from the AI coding cursor, 20 bucks a month. (t: 3560) Huge deal. If you just need tab, you want to look up documentation. That's going to teach you a lot more about debugging and doing all this stuff versus (t: 3570) being straight up 100% agent encoding. So I would look at it now. And those two, like the word, the paradigm has all changed. Like this is probably, this is the last month. This is kind of for the last couple of months. (t: 3580) This is kind of where it's going to now. So just, I just expect that. So we're, we're basically ordering a pizza and it found blue line pizza. That's like here, right in town, bro. (t: 3590) What? Oh my gosh. (t: 3595) I found a great option in Los Altos spot, a pizza place as close as near to, (t: 3600) right? While state of mind offers Detroit style plant pants, a blue line pizza is nearby mountain view and it's the strictest gluten-free protocols. (t: 3610) If you need that, would you like me to help navigation? Oh yeah. Let's go from blue line. Cause they have the strict protocols and I need that because I have celiac disease. (t: 3620) Oh my goodness, bro. This is too crazy right now. This is crazy. (t: 3630) So not only is it going to help us with code. (t: 3631) Oh my God. This is so dumb. But it was so good though. It's not dumb. It's kind of smart, but dumb at the same time. (t: 3640) It's dumb though. It's not. I think I'm dumb for asking it to do this, but because I have the Claude max plan, I can literally max out. (t: 3650) Oh man. So, oh, oh, so, oh man, I, I'm yeah, we're doing this right now. Let's go. (t: 3660) Let's go. Who's got. five out of it in the chat. Drop the five. Oh, man. Tell Claude he's got to cover the pizza and (t: 3670) then you'll get him on the next one. Hey, that's what I'm talking about. You're that type of friend, right? Oh, man. I don't know if I want to be your friend, DW, if you're like that. (t: 3680) Get that drone delivery option. That's what's up, man. That's what's up. Yeah. Oh, man. That's so cool. YOLO mode with Puppeteer. Bro, we're so close for this. That's why I'm still asking to (t: 3690) approve. I'm not saying yes for everything. I'm just saying yes as we go. So I'm so impressed at (t: 3700) how good it is. And like all jokes aside, though, for real, I really do think there's a good workflow here. And here's what I'm thinking. Our vibe coding friends who are in marketing, (t: 3710) who are not into code right now, are going to have a field day with this. And they don't even know it. Is that a good hook or what? The reason why they're going to have a (t: 3720) field day with this is because like these marketing bros can hook up their MCPs to like their finance stuff. They can hook up their MCPs to their Notion notebook. The MCPs can talk to the sub (t: 3730) agents. And so the sub agents can now have instructions for like YouTube script writing. (t: 3740) I have a whole workflow that I have in Cloud as a project. So I don't need Cloud projects anymore. I have Cloud code. Each of these sub agents are technically a Cloud project for a very specific (t: 3750) amount of work. So I can say, go to my MCP server and go to my Notion notebook and always look for (t: 3760) this database, which all of my like, you know, transcripts are for all of my different videos. And then I want you to use those transcripts to scrub through them and start to like come up with (t: 3770) some cool hook ideas for these types of things. And then I want you to take some of my other notes over here for today's trending topics and kind of scrape through them. As well, this is how we're getting started. And this is like sub agents feature, not only related (t: 3780) to code, but how it's expanding on these different things right now. What we're actually realizing is (t: 3790) that jokes aside, this thing is actually has the task to order a pizza that's gluten-free because I have celiac disease. It asked me about that. It's like, how important is the gluten-free stuff (t: 3800) for you? I was like, bro, it's very important. It's taking these next turns and actually searching. It's about to place the... Order. Oh my God, bro. I can't. I have to stop it. I have to stop because the next step (t: 3810) is going to go hit that stripe endpoint. And I'm about to... Oh no. Yeah. I'm a marketing (t: 3820) advertising bro. I see you, bro. I see you out there. Yeah. Well, clock code is MCP. Could you sub agents? I love what you're doing. This is exactly what I've been looking for. LOL. To stop (t: 3830) the order. That's why I did not YOLO approve, dangerously approve because I always want to be involved for now because we... I know from my previous live stream with the hooks live stream that this guy goes up in the chat and (t: 3840) then it can rewrite its own instructions. When it starts to do that, I know this thing has mega superpowers that I swear to goodness, this is going to do... Oh, wow. Okay. This has been a wild, (t: 3850) wild, wild ride. Damn, damn, damn, damn. Okay. So we got a whole bunch of stuff that's been going (t: 3860) on here and I just have a lot of stuff to catch up on the chat because there's just been so much going on. Yeah. We're going to go ahead and check out this one. So let's see if we can get out of (t: 3870) here with this guy. I mean, yeah, this is far better than GPT agent so far. It's low key because it's kind of more in the background type of thing. Obviously, we're engineers and we like to tinker (t: 3880) with this stuff, but I'm pretty sure no one is like ordering a pizza right now. Like this has just come out in the last like 12 hours and I don't think anyone's thought about doing this. (t: 3890) Oh, man, dude. This is, this is really, really cool. All right. So let's kind of rewind this a little bit. right. So let's kind of rewind this a little bit. Let's kind of calm down a little bit. I need to get back in the frame. Let's go. Oh, I need to escape this chat because this thing is about to (t: 3900) get so dangerous right now. We're at the point where we're able to use a pizza order specialist (t: 3910) as a subagent, search for gluten-free pizza. It's gone as far as like going to toast tab and actually place the order and it listened to all my instructions and it will do that. (t: 3920) I need to probably go ahead and just remove this specific agent specialist that I had cloud code generate because in the future, if it thinks I need a pizza, it's just going to go. (t: 3932) Oh man, it's a good thing I didn't give it extra crazy permissions and stuff like that. So (t: 3940) a cloud coding for you and ordering a pizza in parallel, we're approaching the next level. I feel like that's the screenshot. Like I need to have like the actual pizza box and everything and like do the whole viral thing where like, you know, (t: 3950) I was like, I need to go and order a pizza in parallel. I need to go and order a pizza in parallel. I was like, I need to go and order a pizza in parallel. I need to go and order a pizza with cloud code today. And it ordered me a pizza by accident. It just didn't order me one. It ordered me 500. It's like, uh, yeah, you need a pizza, right? Yeah. So I don't know if you know, (t: 3960) but today's Friday and I need to go get a pizza. So yeah, a nice big pepper pepperoni. Yeah. I like, I like a combo. I think combo pepperoni, all of those stuff's really good. Um, with the gluten (t: 3970) free stuff, there are a few places like state of mind is really, really good here in Los Altos. There's also, yeah, like the blue line is delicious. Super duper good. (t: 3980) Uh, in mountain view. So, all right. So we we've got covered a lot of stuff and I think we can have enough to go ahead and cook with our agents. I know we didn't get into crazy details, but (t: 3990) one of the things I'm really excited about is that you can just tell it in plain English, what you want, right? I didn't have to go and generate these crazy long documents. (t: 4000) I didn't have to go and like figure out these other things just in plain English. I can actually describe what I want this agent to do. And it writes its own instructions and it's really, (t: 4010) really easy. And it's really, really easy. And it's really, really easy. And it's really, really, really good at following them, which I'm really impressed by. So don't be afraid. All you have to do is hit slash agents, start the conversation, and then you can put it into your (t: 4020) project. And that'll be a good way so that in the future you can kick them off with a GitHub workflow that we can probably get into a little bit later. So this is a multi-step series. And (t: 4030) if you're not a member, feel free to join to help cover the cost of my gluten-free pizza, because I'm about to pay bank for this whole thing. And you can do that by going to youtube.com (t: 4040) slash at Ray Fernando. And in there, you'll actually see the ability to join my membership. And so my membership, all it is, is just $6.99 a month right now. And I'm trying to aim for my (t: 4050) first hundred subscribers. I'm basically right around like 73 or so. And once we reach our first hundred, we're going to double the price for folks to get into the Discord. So right now, (t: 4060) it's pretty much you're the earliest people who have discovered me. And I want to give you a hat tip for discovering me. And it has come a long way. I've literally started streaming every single day, (t: 4070) starting July 1st, and we've grown quite a bit. And just getting this information out there, even if you can't afford it, just to share for free to other friends is also very helpful, (t: 4080) because it helps find other people who want to learn more about this information. So yeah, if you're into AI coding, if you're into just trying to make some apps, I'm a solo developer. And right now, as a content creator, I'm making tools for myself as I go. (t: 4090) And so one of the recent discoveries I had was the fact that transcripts can actually be a huge SEO boost. And so I'm trying to make sure that I'm making a lot of content for myself. And (t: 4100) I'm trying to make sure that I'm making a lot of content for myself. And so I'm trying to make a lot of So today, everyone's going to be searching for subagents. And my entire transcript today is filled with all these subagent keywords that these basically search engines love. (t: 4110) And so after the live stream, I'm basically just going to log in, transcribe the whole thing. And because I have some additional tooling inside of my app (t: 4120) to have these custom words and dictionaries, I'm going to be able to pick up on all these extra keywords that are normally actually misspelled. So in the custom dictionary, (t: 4130) all of these keywords are webpages. And so I'm going to be able to pick up on all these people are actually typing into Google Bing and so forth. And if you type in chat GPT, you'll actually see like, you know, my stuff, sometimes like some of my stuff shows up. (t: 4140) But other people's keywords, if you're ever scrolling on TikTok, I'll say chat GB, because they'll say some people pronounce it as a B or grok with a K or grok with a Q, (t: 4150) or, you know, it'll just be all kinds of misspelled. It just doesn't know a lot of different things. So a lot of these types of stuff actually help it in its whole thing. And as you can kind of see, (t: 4160) this is like a tool. I built for myself and have been building over time. As I've been building this tool, it's been helping me share back knowledge with you. And this is something that I do for free here as part of this type of thing. And if you want to take it to the next level, (t: 4170) the community is a great place to do that right now with all the members that have been joining in. I can chat with other folks who have joined our community who are doing that. And, you know, (t: 4180) I want to do more of those types of things. Also, let me know, like if you're from the Bay Area or you're from Hawaii and would love to do some meetups here in the Bay Area or Hawaii so we can (t: 4190) all hang out. And share some knowledge and cross pollinate, which would be really, really cool. Also, I want to see if I can travel across the U.S., which would be kind of fun to like if there's (t: 4200) a bunch of folks who are in certain places like New York or something, head on out, maybe do some type of meetup or something like that, which would be super duper cool. So, yeah, this is the app that I'm working on. And it's been it's been really fun to kind of learn more about like web development (t: 4210) and how this stuff kind of all comes together, if you know what I mean. And of course, I'm using Convex for the database because it's so freaking amazing. And, you know, (t: 4220) I'm actually looking for sponsorships, by the way. So this is I'm going to be reaching out to a bunch of my favorite companies I use. Like I use V0 a lot. I use Convex. I use Vercel. (t: 4230) I use some Cloudflare stuff as well. And so what their sponsorship is going to do is going to allow like my channel to really grow even more. And I'm looking to see if I can, you know, because (t: 4240) there's no way that like I can get enough membership to have like a $10,000 Mac covered or like a $40,000 robot, you know, completely covered. (t: 4250) With everything. So these sponsorships can help cover a lot of these types of costs for things that I really want to do, because I really like had so much fun programming a robot with my friend (t: 4260) Butoshi that like I want one of those types of things. But like those are like start at 40k and they go up even crazier prices. So having a sponsor really helps cover these types of costs (t: 4270) where I would normally have to have like millions of members or millions of people subscribe to the channel. And I think I think it's going to be really cool just to share in real time. (t: 4280) Speaking of tinkering, some of the tinkering toys that I have with me right now are this cool like this display thing. So I'm going to be taking a look. I don't have my stuff on, but I'm going to try to see if I can get as far as I can to show you what the board looks like. (t: 4290) And in this board, we're going to try to see how we can programming it using some Python and things. (t: 4300) And in there you can I don't know if you can kind of see I'll have like some secondary cameras like looking down, but you can see how we're going to kind of set up our own little board. That's going to like connect to some NCPs and show some displays. (t: 4310) And stuff like that. And I have a little battery pack and stuff. I'm going to get you through like configuring some Python environments because Python is typically really challenging to set up consistently. (t: 4320) But I have a pretty good consistent workflow at Ray Fernando dot A.I. And so these are kind of some of the fun projects, like even doing a project like this where I do look at mini workshop in person would be kind of fun in the Bay Area or like in Hawaii and stuff. (t: 4330) And so definitely appreciate everyone who's been able to to do these types of things and show up for for for us and also participate. (t: 4340) Right. You can also join me on Twitter by helping us here in the community. So I just want to kind of close out with some of the members chats and any questions if you have any questions, just drop them right now. (t: 4350) This is your time to kind of chat with me and kind of catch up. I am doing a member's only live stream tomorrow. So tomorrow right around eight o'clock Pacific Daylight Time, I'm basically just going to do like a one to one with anyone who's a member. (t: 4360) You can just hop on in. It'll just be a member's only stream. And so there we just kind of have our own free time to chat. So this is a good time to become a member if you haven't become one yet so that you can get into the conversation. (t: 4370) So if you haven't become one yet so that you can kind of join in on that live stream, just ask some more questions and answering your stuff one to one. Cloud Code destroyed my pizza by ordering this one. Why? Weird topping. (t: 4380) How about meetups in Europe or Thailand? I'm so down for Europe. I have not been to Thailand, so that'd be really cool. I would love to know more about that. I'm a computer science student and I always see Cloud Code on my for you page. (t: 4390) I ask people about and they say that it's expensive. Is cloud good for people who have a tight budget? If you have a tight budget, if you're just getting started, you can also try like Google Gemini Coder. (t: 4400) Like it's free. I think you get so many thousands or almost like a million tokens per day or something like that. (t: 4410) So that's something to get started on. It's I mean, cloud code is probably like the best model right now. But I mean, I feel like if you're just getting started, you're probably better learning off some of the basics of the coding stuff and using AI to kind of teach you some of the fundamentals so you can kind of leverage that, you know, to go further. (t: 4420) And wherever you get stuck. You know, all of these agents are really good at debugging. (t: 4430) So you have a great heads up there. Cloud code in the background connected with OMI device. Can we can cloud code in the bench drinking a mojito? (t: 4440) So what you're saying is if you have he's talking about this wearable that you have and because it's listening in all the time, you can set up an MCP that can go kick off a sub agent. (t: 4450) So it's listening into your conversations because you're wearing it all the time. So imagine like you're saying, I'm hungry. I'm thinking about ordering a burger. And like it here. Here's burger keywords. And then start to like figure out some stuff for you and can display it on your phone or something like that. (t: 4460) That's kind of cool. I feel like that's where the future of some of these devices could go. They're very niche. (t: 4470) That's not my main experience because I'm always more intentional than like having an AI like I don't need to delegate that task. But yeah, I mean, it could be helpful for research for me, especially with celiac disease. (t: 4480) When I go to a new place like New York or I go to like a new place like in Southern California, I don't know the restaurants. I don't know the lay of the land. And my problem is I like I have to talk to the chef and say, do you fry these French fries or do you fry some type of, you know, whatever the gluten free item is with other stuff that has flour or wheat. (t: 4500) And like I can't tell you how many restaurants I have to walk away from because they don't have that type of safety. Right. So having an agent that can do that for me is actually very helpful. (t: 4510) So, yeah, such a sweet last. I'm going to try out the cloud code today. Oh, thank you. She does. She's also building. She actually went from zero to like seven Apple rejections to get her first app in the app store. (t: 4520) So I think she launched an app that you should go ahead and check out on Product Hunt. It may have the link down below. So that'd be really cool. Oh, you're in the Bay Area, too. Cool. Yeah. (t: 4530) I got to do a meetup. I also have to do this little like funny like dance. I'm in New York. Obviously, let's do a meetup when you are in the area. Let's go, New York. Hit them up, New York. Let's go. (t: 4540) Who's who's in NY? That'd be so cool. I just want to fly out there just to go see people. Put screenshot to your clip. Or you have to press control key after the selection. Yes. So normally I do command shift four and I have to do control command shift four. (t: 4550) But also McKay Wrigley drop some defaults so you can do a defaults right so that you just do your normal screenshot combo and it'll automatically put it to the keyboard. (t: 4560) So you don't always have to do the song and dance. The other part of it is when you're pasting it back into cloud code, you have to hit control V instead of command V, which which is what I'm used to. (t: 4570) So, yeah. I don't know the keys. I just have been doing it so long. I just know the like, you know what the what the fingers are. (t: 4580) So if you see me talk like this, that's kind of how I'm talking. That's what that's what I'm thinking in my mind. Are you going to make any more agents today? A one last one would be fine as soon as I can get to the live broadcast. (t: 4590) Oh, yeah. If you're rewatching the broadcast, even though it's live, you can actually rewind it back. It won't have the timestamps, but probably shortly after this video ends, I'm going to run it through my processor and it's going to just generate the timestamps. (t: 4600) For you. And that way you can kind of go back and see the exact spaces. But I need to get some lunch, literally some gluten free pizza. (t: 4610) I got to be careful what I say around here because this cloud code agent is listening to me and I swear. I swear. (t: 4620) Yeah, that's isn't that the weirdest thing that you have to be careful what you say, because these agents will start to listen. Like if all of them are listening all the time. OK, I'm not really trying to incite fear, but I'm just trying to be funny. (t: 4630) But yeah, like that's not what I mean, though. Yeah. I analyze 50 socks. It only costs 20 cents. I want to do some stock analysis because it takes me a lot of time and I still do it by hand. (t: 4640) But this is something I do all the time. Like at least every quarter I take a look at all the you know, what's the trends, what's going on, what's going on in the bond market, what's going on in all these different categories to try to get a sense of like, oh, should I be, you know, reallocating some things and kind of seeing what's going on? (t: 4650) So that'd be a fun exercise. Is there a Kiro? (t: 4660) There's also Kiro, which is free. Cloud sign up for. Oh, cool. Yeah. I don't know how much free it is, but that'd be really interesting. And the Gemini context windows one million. So great to start with. Yeah. (t: 4670) I'm curious to see if anyone's had a lot of luck filling up that context window for Gemini. Right now I fill up my live streams with it. Like the most I've had is 150 K tokens and it does pretty well at finding timestamps and stuff like that. (t: 4680) So, yeah. Oh, Portugal. I was just there. Great for vacations. Yeah, I was in port. I went all over Portugal and that was that was Portugal. (t: 4690) And then the food is really good from an American perspective. Everything is a lot cheaper. So I was really surprised to be paying like literally like, I don't know, like four or five bucks for like some amazing food. So that was really, really cool. (t: 4700) So awesome stuff. I can help with this. We can help with the Seliac stuff for sure. Yeah. Cool. If you can go into the tab to code versus the more agentic direction in the previous live stream. (t: 4710) Yeah. Let me launch into that right now. So I don't know if you know this, but if you're trying to use these agents to code like this, like this is a great way to do it. (t: 4720) You just basically vibe coding is what most people call it. It's going to cost you quite a bit of money to do these types of tasks now. So a lot of companies are now like cursor has a $60 a month plus plan that's not really on their main website. (t: 4730) You have, you know, I think GitHub Copilot has a now plus feature that can generate code in this agentic window. (t: 4740) Lovable. All these ones have like usually it's like $40 to $60 to kind of get started just with agentic coding. And if you're doing 20 bucks a month, they'll be like, oh, I'm going to do this. (t: 4750) And then if you're doing 20 bucks a month, they'll have these features where you can either use an older model or you can like tab to code across your way into, you know, using into writing software. (t: 4760) And so traditionally before AI, you would just write every line by hand. And then GitHub Copilot came across with their type of thing, which would basically, you know, fill in the code for the next line, which is super magical. (t: 4770) And then that cursor came across and they came up with their own model and it became really fast. And then you used to be able to just put stuff in a chat window and slam it in. Yeah. (t: 4780) And then you would just put stuff in a chat window and slam everything in. Now, what these models like Cloud Sonnet 4, they become so expensive to use, these companies are charging larger dollar amounts. And to do the same tasks that you used to do for $20 now, like to be an agentic coder basically just means that you're going to be paying, you know, at least 200 bucks a month at least. (t: 4800) Right. Typically, if you're paying out of pocket beforehand, people were paying thousands of dollars a month and you could still do that. And there's some products out there that are really good at that. But you're going to be like looking at 200 bucks a month. (t: 4810) So you have to kind of depend on like what your sliding scale is. If you're an engineer and you're like paying like $5 for a coffee every single day, these types of tools help you so much. (t: 4820) And you're paying 200 bucks a month because you already have a high paying salary can actually be very helpful for your different types of workflows. (t: 4830) Right. So you just have to figure out, like, where are you in your life and what's your profession? How much are you going to benefit from it? A lot of these plans let you get started for free or they have a generous two week trial or they even even just starting out at the 22 bucks a month and going up as you need it is pretty cool. (t: 4850) And so you want to see how you can incorporate this into your workflow. And for me, I've been gravitating a lot towards cloud code, mostly because of these types of little feature drops that I can enable me to be a single person engineer, but then have some of these other stuff delegated. (t: 4860) Right. You know, doing a design review, doing a code review, doing a code review. Right. Doing a debugging review, doing a research task. (t: 4870) So, like, I want to make one that's going to do a research task. Technically, we did a research task here, but for ordering a pizza and you saw the details in which how good it was at just going out for that task. (t: 4880) It didn't involve my code base, but it's really good at searching the web in multiple steps and reasoning. So imagine if you had that for your code where you would start to grab snippets of code, see what you have, start planning features. (t: 4890) That'll be really cool. We can make another sub agent to do planning. You know, just literally start planning. That would go do research, kick off the sub agents for research, come back with plans and do all these types of things. (t: 4900) I mean, the possibilities are endless, but don't go too crazy. Just start with something basic and then add on saying, I do this a lot. (t: 4910) Let me see if I can turn this into a sub agent. And we turned that into a sub agent in like probably. I mean, obviously, I had to walk you through it, but you can probably do this in like less than 90 seconds. (t: 4920) Just next, next, next. Describe what your thing is. Use some type of transcription thing saying, you know, I want to add this. I want to add this to my agent to do X, Y, Z, review documentation, do some research and then just let it do its thing and just start using it and then tweak it as you go. (t: 4930) Because now you have the markdown file. You could tell it to improve the file saying this is the output that I got. (t: 4940) I want this output to be better. How can we make it better? This is something that I prefer that you like to do or something like that. And they'll just rewrite that document for you. The possibilities are endless and you can spend hours and hours, but I would just say keep it simple and kind of go from there. (t: 4950) Yeah. That's what's up. Ray Fernando went through seven. (t: 4960) I really appreciate everyone for joining in. We've had a lot of people on the stream live. This has been really fun to cook with y'all. I love doing live stream, so I will be on tomorrow for my members only at eight o'clock Pacific Daylight Time. (t: 4970) If you're not a member, you can just feel free to sign up on the YouTube at Ray Fernando one, two, three, seven. Hit that little join button. I'll have a link in the description as well. So that way you can join in from there. (t: 4980) And I will see you guys tomorrow. Hopefully I will have like 500 pizzas to get rid of. .

