---
title: Wait.. <PERSON> Code is MADE Slow on Purpose? Heres How to Fix It
artist: AI LABS
date: 2025-07-22
url: https://www.youtube.com/watch?v=wYWyJNs1HVk
---

(t: 0) What if I told you that your Claude code is probably working at 30% of its true potential right now? Every time you ask <PERSON> to edit a component or fix a bug, it's drowning in (t: 10) unnecessary information. Reading through thousands of lines of code, it doesn't need processing files that have nothing to do with your request. This isn't just slowing <PERSON> down, it's actively making it less accurate. When the context window is cluttered with irrelevant code, (t: 20) <PERSON> has to work harder to find what actually matters, and that leads to mistakes, missed connections, and suboptimal solutions. But here's the thing, there's a completely free (t: 30) way to make <PERSON> laser focused on exactly what it needs, and I'm about to show you how. Before I tell you how I'm going to optimize Claude code, let me give you a quick intro (t: 40) on how Claude code actually works, and the problem we're going to solve with this tool I'm about to show you. As you can see, I'm initializing <PERSON>, and I've already made a prototype here, an HTML prototype. When I tell it that I want to make the HTML prototype better, (t: 50) it needs to find ways to improve the design, and make it look nicer. When I give it this message, since this is a new session, (t: 60) it has no context of what's already in the session, so it's going to read through everything. By doing that, it's not only using up our tokens, but it's also filling the context window with (t: 70) potentially irrelevant information. When the context window gets cluttered with unnecessary data, it directly impacts Claude's performance. It has to process all that information even when (t: 80) most of it isn't relevant to the current task. This isn't just about hitting usage limits, it's about efficiency and accuracy. Another way Claude doesn't have to read through all of this, is if it already knows the context (t: 90) of the codebase, what's inside the code, and it only targets the files that actually contain the code it needs to edit. This is a simple example because right now, there's only a (t: 100) single HTML file that it's editing, but imagine if it were a whole Next.js project, and I asked it to edit a login component. It would have to list the directory, read different (t: 110) files, and figure out where the component was that needed editing. Even that component example is simple. What if there were a random error, and it had to check every single file because it (t: 120) didn't know how the codebase was structured, what context it had, or what was inside it? Not only would this consume tokens, but it would also degrade performance as Claude tries (t: 130) to maintain context of all these files simultaneously. You might say that we could improve this by using the Claude.md, which is basically the codebase documentation. And yes, you'd be right. If you wrote out everything about the (t: 140) codebase inside the Claude.md, then Claude wouldn't have to read everything, and you'd save something. But still, it has to read through the entire markdown file, and that's still filling up (t: 150) the context window with potentially unnecessary information. There are different types of searches. One is textual search, where a model like Claude (t: 160) searches through the text it's been given, like the text in the Claude.md file. Then there's semantic search, which is much faster, more accurate, and most importantly, only returns the relevant pieces of information. (t: 170) The Context 7 MCP uses semantic search, and it gives you up-to-date documentation for all the libraries and the files. You can also use the text in the text file, and you can see right here that the text is not being read through the text window, and it's not being read through the text window. The reason tools like Context 7 are so effective is because they use this semantic search approach. (t: 180) For example, if I tell it that I need to implement a feature from Next.js, it first finds the Next.js library. Then, when it needs to get the library docs, whatever issue I'm facing (t: 190) with Next.js, it doesn't search through the entire documentation. It uses semantic search to get only the relevant pieces of information that it needs. (t: 200) This becomes much faster and more accurate. The agent only gets the context it truly needs. It doesn't have to wade through irrelevant information to figure out what you're trying (t: 210) to accomplish. This focused context window dramatically improves Claude's performance and accuracy. This is called RAG, and this can be applied to your codebase as well. Think of it this way. What if Claude already knew everything about your codebase and could (t: 220) use semantic search instead of textual search? Whether your codebase is 100,000 lines or larger, what if Claude could automatically find exactly what it needs and pull only the (t: 230) most relevant pieces of information into the codebase? The answer is no. You can use the semantic search to get the most relevant pieces of information. The context window. That is what the Serena MCP does. It knows everything about your code (t: 240) and uses semantic search. So it's much faster and more performant. By keeping the context window lean and relevant, Claude can work more efficiently and provide better results. (t: 250) It's honestly been a game changer. Since it's an MCP server, it's not only constrained to Claude code. It can also be used with other MCP clients like Cursor or Windsurf. (t: 260) Personally, I like Claude code because the models there aren't restricted to a limited token window. The code is just as easy to use as the code in the context window. The code is just as easy to use as the code in the context window. They get their full context windows inside Claude code. This is opposed to Cursor where it's limited to 120,000 instead (t: 270) of the full 200,000. Even if you do use Cursor, this is still an amazing tool for you. The reason is because of Cursor's switch to their new pricing model, where they now give you (t: 280) a set usage of the model. After you've used up your credits, you switch to a pay as you go model and start paying instead of using their included credits. (t: 290) Installing it is pretty easy. You just have to scroll down until you reach the Claude Code section, and in there, you're going to find these two commands. You'll copy the (t: 300) second one and go back into your terminal. You need to install it in whichever directory you want to use this MCP server because it's specific to the directory. If you initialize a new directory with Claude code inside it, the MCP server won't be present there, so (t: 310) you have to install it in every directory where you want to use Claude code. After you do that, you just paste the command and this will automatically add the MCP server to Claude (t: 320) code settings for you. Then, when you fire up Claude code, you'll see that the MCP server is now installed. You can now install the new directory in the new directory. You can also install the new directory in the new directory. You can also install the new directory in the new directory and also install the new directory in the new directory. After installing Claude code and navigate to the MCP section, you're going to see that now the Serena MCP is connected (t: 330) with a checkmark that says the connection is valid. Another thing that the author has provided with Serena is this dashboard. The dashboard provides logs for the MCP server. (t: 340) In addition to logs, a feature the author personally likes is the ability to shut down the server for proper cleanup. And the functionality there is pretty great too. For example, when (t: 350) you're done with the MCP server, you can just go to this web UI and press the shutdown button and it'll shut it down. If I navigate back, you can see that now the Serena MCP appears with a (t: 360) cross mark, which means the connection is now cut off. To initialize it again, I'll just exit Cloud Code and reinitialize it in the same directory. Whenever I launch Cloud Code again (t: 370) after exiting, it'll automatically launch Serena and bring up its dashboard as well, and I'll be able to view it. Before we go further into this MCP, I want to show you another tool (t: 380) that I've been using with Cloud Code. This tool has also helped me optimize my Cloud Code usage in a really nice way. The tool I'm talking about is the Cloud Code Usage Monitor. It lets you track (t: 390) your Cloud Code usage. As you can see, I'm on the Pro plan right now. The Pro plan works in five-hour windows, so your limit resets every five hours, and the time remaining in this reset is two (t: 400) hours and 53 minutes. We also have other trackers, cost usage, token usage, and message usage. As you can see here, I'm using Cloud Code right now, which is why the message usage is actively increasing. (t: 410) There's also model distribution. Currently, there are only two models listed in the Cloud Code usage monitor. The first one is the Cloud Code usage monitor, and since I'm on the Pro plan and not on the Max plan, it's 100% the Sonnet model. This helps me (t: 420) track my Cloud Code usage. It helps me optimize how I use it by showing the reset timer and alerting me when I'm getting close to my message limit. That way, I adjust my usage depending on (t: 430) how many messages I have left. Another important point is that it's right here in my terminal, in another tab. I don't have to use those UIs built on top of Cloud Code, like Claudia or (t: 440) the Cloud Code Web UI, which are good, but I prefer using it in my Cloud Code usage monitor. I'm using the Cloud Code UI terminal. It just suits me better. Installing it is pretty easy as well. I'll leave the GitHub link down below, but it's just one command. You need to have that installed (t: 450) on your system, and after that, you just copy and paste the command. It'll install it. In my case, it's already installed, and I can just launch it with this command, and the Cloud Code monitor will (t: 460) appear right here. I've found that this usage monitor is far better than others. For example, there's CC usage as well. I've tried it, but it wasn't tracking my messages and usage correctly. (t: 470) But ever since I switched over to this Cloud Code usage monitor, it's been really nice. Over on the AI Labs Discord community, (t: 480) we're hosting our first ever hackathon from July 22nd to July 28th. Submit your most interesting builds and projects, and the top five submissions will be featured in one of our YouTube videos. (t: 490) You can join by clicking the link in the pinned comment below. And if you're enjoying the content so far, make sure to hit that subscribe button so you don't miss what's coming next. (t: 500) Now, coming back to the tool, the first thing you're going to do is actually exit Cloud Code, so you can initialize the internet. So, for that, you basically have two commands. We're going to use the first command because we (t: 510) installed the MCP server using UV. We're not running it locally. So, just copy that command, head back into the folder where you've installed the MCP server, and run it. You'll see that it (t: 520) indexes the project. It also gives an error saying that this command is deprecated and suggests using another one instead. But this one still works. It's only a matter of time before they update (t: 530) the readme. You can find this command in the GitHub documentation too. I'll link it down below. Another important thing you need to know is that indexing works only for certain programming (t: 540) languages. So, if you only have HTML in your project, it probably won't work. And honestly, you won't even need it. But for other applications, like Next.js using TypeScript, or the one I've (t: 550) initialized in this folder, which is just a demo Python app I created to test indexing, it will work with those. Once it's been indexed, you can just go ahead and type out Claude. And before (t: 560) actually continuing with Claude, you'll need to give it some instructions. So, let's start with the demo Python app. So, let's go ahead and type out Claude. And before actually continuing with Claude, you'll need to give it some instructions. Basically, telling it how to properly use the MCP. This gives Claude code the context it needs to (t: 570) know how to interact with the tool. And as you can see, it's read the instructions, and now it knows what tools are available and how to use them. It even picked up on some key principles on how to (t: 580) apply them. So now, whenever I'm requesting edits, like in this task I gave it, which involves both the Python files and the UI, it's no longer going to explore the entire code base blindly. It's (t: 590) actually going to use the tool, fetch the appropriate data, and only bring the specific parts it needs into the context window. This focused approach significantly improves performance (t: 600) because Claude is working with exactly what's relevant, not wasting processing power on unnecessary files. This way, you'll not only save tokens, but also get much better performance from (t: 610) Claude. The cleaner context window means faster, more accurate responses. And from my experience, you can easily stay within your message limit during your five-hour window while getting much (t: 620) more done. That brings us to the end of this video. If you'd like to support the channel and help us keep making videos like this, you can do so by using the super thanks button below. (t: 630) As always, thank you for watching, and I'll see you in the next one.

