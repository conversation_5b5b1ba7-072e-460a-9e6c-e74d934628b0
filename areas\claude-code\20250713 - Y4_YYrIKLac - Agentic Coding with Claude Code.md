---
title: <PERSON><PERSON>ding with <PERSON>
artist: <PERSON><PERSON>
date: 2025-07-13
url: https://www.youtube.com/watch?v=Y4_YYrIKLac
---

(t: 0) I think this should be live. (t: 2) Post. (t: 10) Thing should be live. (t: 20) Okay. (t: 23) I have no idea if this works. This is an experiment. If you are, (t: 30) if you can hear me or something, write something into the stream chat. (t: 40) Cause I don't know. (t: 42) Actually, let me open the stream. (t: 50) There can be some sort of live updates on what I'm doing. So like some sort of feedback. So yeah, I've created this one little test project here (t: 60) just to maybe show a little bit how I'm doing this. (t: 65) Okay. So what I'm usually doing is I'm using plot code. (t: 70) There are a bunch of other options too. There's open code, there is AMP and a few others. (t: 80) And what all of these have in common is that they currently primarily run on the command line. So it has become very use specific to where per user (t: 90) and a lot of the malicious!!! This is what I love aboutactic were used in short. So how I'm doing, so this is why I make these plans is, of course, more (t: 110) Vocational Kotlin ongoing at the moment. Because I although I don't like it, I do like, yeah. I like this oneStex work, I like it. All right. setup. So what am I doing for agents to work at all, right? So the most important part here (t: 120) is to have a CloudMD file. This is actually auto-generated with slash init, and just I did (t: 130) some minor modifications to it. For almost every single project that I'm doing, I use a make file. (t: 140) In fact, I would say for 100% of projects I'm doing, I have a make file. And the make file acts as the main entry point for the agent to run things. So the agent, broad code in particular, (t: 150) will use bash, it will use Python, it will write some code, it will run the code, and it can dig itself out of a bunch of nasty situations with this way. But I want to steer (t: 160) it in certain directions, right? And so what I usually have is a project overview. It just tells (t: 170) it what it is. I don't actually know if it's necessary. It will read the readme tool. But it gives you some context. And then I give it immediately next the commands that it should use. (t: 180) The most important command here is make dev. And the reason this is so important is because I actually don't really want it to run this command. I usually run this command myself. (t: 190) This brings up all the services. So this particular project has two services, has a front-end service and has a back-end service. (t: 201) I tell it here that this brings up the server. It starts both the front-end and the back-end. (t: 210) I also tell it that it auto-reloads and it auto-compiles. And I also tell it it should never stop the server. And that's actually one of the first important things here is that because it can do anything, (t: 220) and in particular because I run it in YOLO mode and it will just do anything, I also wanted to encourage it never to run in the wrong direction. (t: 230) So, it's kind of annoying to explain, but (t: 240) it can, for instance, stop the server and restart the server. And I really don't want it to do that. So if the audio doesn't quite work, let me see if I can fix something here. (t: 253) I might have overdone this. Let me see. (t: 262) Let me maybe restart. Let me move it here. (t: 266) Let me know if the audio fixes itself a little bit. (t: 270) If this is better. (t: 272) Okay, I did the change here. Let me see if it works. But basically I wanted to stay on the path of most success. (t: 280) And for this to work, (t: 290) I have to basically juggle me and the agent. So what I want to do is, I want the dev environment to be in my, to the front. (t: 300) I always want to see it, right? So I have Clorup here doing stuff and I always have the dev server running things and I can see what it's doing. Right? So if I, for instance, go now to this website and I load it, (t: 310) well, so far nothing has happened because it doesn't log requests. But for instance, if I were to hit the backend service, let's say, (t: 320) I'll go to slash API health. I can see that a request was made, right? So I want to see how the server runs. The server is always there for me. (t: 330) So that's the first most important thing. I want the server to run in my terminal. I don't want Clorup to run it in the background. (t: 340) The next thing is that I want to have a consistent log of all the things that are happening. I've talked about this a couple of times, but this basically allows me to see front-end and backend requests simultaneously, (t: 350) even though they're from different services, right? One is the VEED server. The other one is the server. The third one is my Go server. But I also want to give this visibility to the agent, right? (t: 360) So one of the tools in here is this mgtailog command. So if I run this here, (t: 367) basically I see the same thing as here. (t: 371) Why is this important? Well, it is important because I want to get the agent to always understand how it establishes the context. (t: 380) Like if I'm talking about a bug with the agent, I want the agent to understand how it establishes the context. I want the agent to understand what's going on, right? So I will show in a minute how I'm doing this, but this is one of the reasons why this tailog command is here. (t: 390) And then the other commands there, as you would expect, how to run the linter, how to run format, how to run clean. (t: 400) It's just, that's the entry point of the tools that it should use. And then here really is the most important part about this. I'm telling it where the log files are. (t: 410) So I want to prevent it from guessing a bunch of other ways. So I want to prevent it from guessing a bunch of other ways. And establish the context that it needs. Again, I reaffirm that tat-this is the command l should read, use to reattail the log file. (t: 420) And I also tell it to never stop the server. One of things that I always tell it that this server order compiles and order re-loads, right? (t: 430) That that's the most important part. So how do I have the order-reload setup, the order compile setup? Well, there is a plugin called foreman. (t: 440) I'm not using that. (t: 442) Yeah. Okay. I have a problem with formAN, and I have to hide本当という pagíto. I have a problematic mange. Yeah, I think the problem is I probably don't have the right setup here for the webcam. (t: 450) So this might be confusing because I noticed earlier that the audio desyncs a little bit. (t: 460) I need to get the setup working better, but I hope it's not distracting. Otherwise, I can just disable the webcam. Maybe this makes it less awkward. I'll just turn it off for now because I think the webcam is trailing a little bit here. (t: 470) Okay, so I'm using a fork of Foreman or a re-implementation of Foreman called Shoreman. (t: 480) You can find this on the internet. Foreman clone Foreman. (t: 490) It's this one here. And the reason I'm using this is because this is basically a very small shell script, which does the same thing as Foreman. But I had to make some changes to it. (t: 500) It was easier for me to do the change in Foreman. So what does it do? It does the same thing. So I have a file here called proc file. And it says this is the command to run for the frontend and this is the command to run for the backend. (t: 510) And the frontend is a basic Vite application, which auto reloads all the time. (t: 520) Right. So as I'm making changes to the frontend, it automatically appears because it recompiles. And I have set up something very similar for the backend. I basically told it to use this watch exec tool. (t: 530) This is. This one here. All that this does is it watches these files, specifically Go and SQL files. (t: 540) And it watches recursively this entire folder. And then it runs this, this Go run command to compile this. (t: 550) And so when I make a change here on my server, let's say, I think this is my actually unused at the moment. (t: 560) So let me. Completely delete this file. I don't want to use it. Right. It's gone. The server has recompiled. (t: 570) Right. That's, that's, that's all that is. (t: 573) Then. (t: 580) Go back to this. So, so then I have this sort of a basic setup where at least these things log in into this project. (t: 590) What in this project is not set up yet. Is that. The server also the front end also logs into it. So if I go here and I issue console log, whatever. (t: 603) I don't see this here. Right. So that's actually one of the changes that I want to do. And I want to show why this is useful. (t: 610) But basically that's the next thing that I want to set up is also gets this to, to, to log into the server. (t: 620) I want to say one last thing about the, the make command here. So I give it instructions in the cloud file, right? I'm telling it. (t: 630) These are the commands that you should run. There's some extra stuff down here, but how I wanted the structure of the project to be, but this is not alone. (t: 640) This alone is not enough to actually get it to work reliably. Um, to work correctly. Right. So the reason I made changes to shore man is because I actually discovered that this is not, doesn't take a long to discover this. (t: 650) That. Um, the tools work better if they are more descriptive in their error messages about, um, what, what would, what actually happened? (t: 660) Let's put this way. Right. So one of the things that I changed into shore man is that when shore man runs, it writes this file called shore man pit. (t: 670) And this is basically the, um, the, the, this master process running as a shell script. (t: 680) And if I run it a second time. Um, shore man checks if it's already running and then errors, but many tools do that, right? (t: 690) Many tools error when they're running. What this, what I changed here is that I error in a way where if the agent reads the error, it is more likely to understand what happened. (t: 700) Right. So every once in a while, say the agent tries to do an HTTP request, but it makes the HTTP requests right at the moment where the server restarts. (t: 710) Right. And then it might see. Oh, the server is not running. Right. And it then it comes to gets the idea to run make that. But the reality is that the server was actually running. (t: 720) Right. And so when the agent now goes in and intentionally tries to start the server, it gets a slightly different error message and would otherwise get right. It now gets the error message. (t: 730) Service is already running. That's good. We auto reload. No need to do anything. Right. So the reinforces to the agent that it doesn't, it shouldn't stop the server. (t: 740) It shouldn't kill a bunch of processes, right? Because the agent will, it will start killing a bunch of stuff. And restart it. And I don't want it. Right. I, I want this service here to be very reliable. (t: 750) And when it tries to start it again, I wanted to get exactly the error message that it should get to not go off the beaten path. Right. It should, if it does accidentally run it now, it will realize, oh, it's actually running. (t: 760) Right. Um, so, and you can see this, right? So if I told, tell the agent, um, I want you to start the dev services, right. (t: 770) It will run, make dev. It gets an error, but it says no action needed. (t: 780) Right. So this is, this is, this works better than if it just errors out and says, um, show me already running or whatever the default is. (t: 790) Right. So getting better error messages specifically for the agentic loop in is, is one key part here. The second thing is of course, that I changed shore man to write these, these log files correctly. (t: 800) So if we look into shore man. Yeah. Yeah. Yeah. Yeah. Yeah. Yeah. Yeah. Yeah. Yeah. Yeah. While I can actually show this a different way. Right. So, um, there's, um, there's a dev log. (t: 810) This is the one that is right. And as you can see, it's only contains the messages from the current run. So if I restart this here, it starts out fresh. (t: 820) Um, so this is one of the changes that I've found to work really, really well is, um, hiding away information that it will not need, (t: 830) because otherwise, if I have this ever growing log file. then sometimes it picks up new work and it sees unrelated changes from yesterday, for instance. (t: 840) So that's one of the other changes that I landed into Shorman. (t: 850) The other thing is, since there was already a question of Docker Compose, I do not actually use Docker Compose. I don't use any Docker here. (t: 860) This is all running on my machine. If I were to put anything into Docker, then maybe I would care about Docker Compose, but this is just the most basic setup that I can have. Okay, so let's make a change, right? (t: 870) Let's put the console forward plugin in. The reason I want this plugin is because it makes just iteration generally much easier. (t: 880) I just haven't set it up here yet. So that is this plugin. So I want it to set this up. (t: 890) So let's say, please set up this plugin by our NPM. (t: 894) And my English just sucks. It doesn't read me. (t: 900) Okay, let's see if it can do that, right? So it should hopefully read this. (t: 910) I can optimize a lot of these things, right? This probably could have done it manually. I just want to see if it works. One of the things, for instance, (t: 920) that slows down Cloud a lot right now is that it actually, from scratch, always tries to figure out what we're using here. So this might be one of the things we should put into this project (t: 930) is that we're using NPM. So I didn't write this yet. So we can do this here. We always use NPM. (t: 940) Right, this in theory should prevent it from using PNPM or something else that it might have. (t: 946) It will only pick up on that (t: 950) when we start to use it. So we can start from scratch, but for future iterations, maybe it will improve this slightly. (t: 960) Another thing, it probably read the instructions incorrectly. I noticed the other day that I'm not documenting this correctly. (t: 970) I'm actually importing. Maybe I do it correctly, but I think I've done this once before and it always imports this incorrectly. So I will manually fix this now (t: 980) because this should actually be like this. But in theory, let's go back to the plugin. This should work now. (t: 990) I noticed this before, that it always gets this wrong. (t: 1000) But in theory now, what should happen is that if we log an error here, (t: 1007) we see it in the log. (t: 1010) Right? That's the error. That's what we want to accomplish here. And (t: 1017) the whole point of this is that (t: 1020) future iterations where we're coming, where we're running into issues on the frontend will also show up in the same log. So now we have at least this running. (t: 1031) And let's see, there's some changes here. (t: 1036) Let's do this. (t: 1040) Update. Let's do, what do I want to say here? (t: 1045) Set up console forward plugin and remove old pagination. (t: 1050) I got very lazy and used a lot of dictation now. Okay. So, let's try to set up some code here, right? (t: 1060) That's really what we're here for. How can we make some changes to this? (t: 1065) I actually don't find Adjantic from the start to work particularly well. (t: 1070) So, I did actually bootstrap this with Cloud Code, but I did already make some changes so that it has an infrastructure that I like. So in particular, for instance, (t: 1080) at the very least, I picked my web framework or the router that I want to use. I set up some utilities in here to respond with errors. (t: 1090) It's just the most basic kind of infrastructure that I wanted to use for building our API. The second thing that I did is I created a plan. (t: 1100) And this is basically all the things that I want to implement here, right? I want to build a small bulletin board that's modeled after PHPV mostly, but also 4chan, so I don't want to have user authentication. (t: 1110) The idea is just that I'm using what's called trip codes to authenticate admin skin-clean boards. So basically, I have this whole plan here (t: 1120) that I wanted to implement. And I will not tell it to do this in one go. Okay. But what I want to do is I want to have it look at the plan (t: 1130) and tell me if it needs something else. So I created a plan in plan.md. I want you to ultra think about it (t: 1140) and see if there are omissions in the plan that we need to fill in. So... (t: 1150) So it will now read this file. And... Ultra think is basically a hard-coded value in Claude (t: 1160) that also extends the thinking context window. So it will use more tokens to reason. One question came up is what dictation tool I'm using. I'm using two different ones. (t: 1170) I'm trying flow. The other one I'm using is called voice ink. I use them both for different things. I'm just trying to see if I can use them both. (t: 1180) And I'm just trying to see if I can use them both. Okay. So I'm just trying different things. I'm just trying different things right now. (t: 1190) Yeah. That's basically the answer to that. Um... The reason I wanted to read through my plan is that it's actually quite good at telling me (t: 1200) if there are omissions that will help it later. I don't usually use this with Claude. (t: 1210) Instead, what I usually do is I copy-paste this entire thing into O3. So let's do this here. I have a plan here. I want you to think hard about this plan (t: 1220) and tell me if there are omissions to this plan that we should look into before we implement it. Let's put the plan and see what O3 is doing. (t: 1230) Um... So let's see what it came up with. Several omissions, admin authentication. (t: 1240) So how admin privileges granted verify. That's actually a good point. We didn't mention that. So let's do... off here. (t: 1250) I think we already have a section up here. So, um... Admin scan of all ports. Admin permissions are hard-coded. (t: 1260) And nflr. Okay. We don't really have authentication. Maybe we just use very, very basic... (t: 1270) HTTP basic office. Well, we don't have that. We'll see. We'll see. Um... So most of this we will not actually do. (t: 1280) Um... This doesn't really matter. The indexes, I think... (t: 1290) Like a lot of this stuff, we'll figure out along the way anyways. Mostly I want to see if there is some... some very clear omission that we have (t: 1300) that we should clarify. Okay. Now, so far this looks good. Let's see if... if this came up with something. (t: 1310) Thank you for leaving me too. Do I have to pick one? Just want to quickly look through. Okay. (t: 1320) This one tells me that there are no deletions in it. That's a good point. Um... We will not do this for now. Okay. (t: 1330) So... So far, if we go to this bulletin board, there's nothing, right? There's... Um... Uh... (t: 1340) We haven't set up anything yet. Um... I get a warning here that the... We're kind of using outdated packages (t: 1350) for the dev tools. I will leave this for now. I just don't want to spend too much time on the stream on the wrong things. But... Um... Yeah. The idea is basically that we are going (t: 1360) to implement the feature now. So... The only endpoint that I have right now is actually the health check endpoint. So we don't have much. We don't have any database code yet. (t: 1370) Other than setting up the database once in the server. I think it's here somewhere. Um... So let's see. So we'll like one API endpoint. (t: 1380) And I think we're best to start with listing all the boards that exist. And because you cannot create a board right now because there's no admin panel, (t: 1390) we're just going to hard code a bunch of boards in the database. Um... So here we have migrations. So we ask it to make a new migration (t: 1400) with two boards that it can just make up. And then we're going to list the boards. I want you to make an API endpoint that lists all the boards that exist. (t: 1410) Because we do not yet have an API to create the boards, I want you to make a migrations and create two default boards. One called general and one called water cooler. (t: 1420) Okay. So we're going to make a new migration. Um... Let's see if this is good enough. Um... (t: 1430) I think I already wrote what the response for APIs largely should be. I think this is all... (t: 1440) Um... Yeah. So let's do this. Each board in the response should pose... It could contain the most recent topic (t: 1450) and the most recent post in addition to just the title and description. (t: 1454) Okay. Let's see what it does. (t: 1460) So let's see what the questions are in the meantime. (t: 1464) Have I tried using Cloud to generate a file mapping (t: 1470) in user with voice in KI post processing for prompt generation? Um... I have tried that. So far I haven't... A lot of the things I'm doing at the moment, are basically based on (t: 1480) does it actually make anything work better? And I know that a lot of these AI tools can do quite impressive things, but very often it doesn't make it any more productive. So I don't really like using voice ink (t: 1490) or something like this to generate prompts to then have another prompt. So I might rather have command set up. Um... (t: 1500) But yeah, I haven't... I haven't tried that so much. So it kind of... It came up with a new function. It came up with a migration here, (t: 1510) uh... For defaults. So, um... It will run this. One of the things you will notice is that in this project, and in fact all of the code I'm writing now, I'm asking it to write a custom SQL. (t: 1520) I do not use an ORM. This is really because I always liked writing SQL manually. In fact, I really just like SQL. (t: 1530) It's not that I enjoy SQL, but I like having as little of an indirection between me and the database. The main reason I don't do it (t: 1540) when I don't use a chat decoding as much is because it is annoying to write SQL. But now that I have a machine write it for me, this beats to me having, um... (t: 1550) Like, another indirection in place. So let's see what it does here. Um... I think it already does some things I don't like. But let's see. (t: 1560) Um... Most likely what it's going to spit out is code I don't like. And then rather than it making... more code like this, I just want to stick with the initial one. (t: 1570) I want to fix it up. Because the more code exists that looks like what I want, the more likely it is that future API generations will kind of fit into this, right? (t: 1580) That's sort of the idea. Um... Yeah, and so, as you know, I basically... I gave it all the permissions. I just let it write. (t: 1590) I... I don't... I don't do anything here, right? It's like, I just let it go. Yeah. It has all the permissions to do everything on the system, which in parts could be a terrible idea. (t: 1600) But seemingly, Cloud Code does really well. Um... Right? So it managed to run the API. (t: 1610) It sees that there is a... There's a response coming back from the API. So it is working. Uh... We can also go to the browser now (t: 1620) and sort of test this. I think we called it boards, right? And so we see... We see that there is a board and it actually has test posts in it. (t: 1630) I'm assuming it has test posts in it because it just went to the database and created some. This is my guess. I didn't actually see where it did it. But this might be an interesting moment (t: 1640) to look into the database. So we have a database here called miniDB. This was empty when we started earlier. And it has created some posts here. (t: 1650) Um... I wonder when it created them. I didn't look. So when did it create them? (t: 1660) Did it make me a test? So let's do this. Let's check quickly which files we have here. So it must have created these manually through... (t: 1670) At which point did it create them? So it created some handlers. Um... When did it create... This is one of the things I'm not sure about. (t: 1680) I'm not sure if it's a good idea to do this. This is one of the reasons why the terminal interface is not very great because I don't have a search here. I have to quickly go through this and see. (t: 1690) Um... (t: 1693) I don't actually know when it made the posts. But it clearly created some content in the database here. (t: 1700) We'll just leave it now. This works good enough. Let's check the changes, right? So now we can see sort of how I do that. So I know that it changed these files, right? (t: 1710) Because they're all modified. So we have a new route here. Um... Boards. This is okay. It's fine. (t: 1720) I have a list boards. And so all of this is new, right? We only had the health check before. And now we have... Uh... this. (t: 1730) So it calls this get boards, which is down here. Um... I really don't like this, right? It should not do this. All the database code should go into a separate module. (t: 1740) So let's start with this here, right? Um... We need some changes. So let's see. All the database queries should go into models slash boards dot go. (t: 1750) Actually, we'll do boards dot go. Um... (t: 1760) So that's the first that we want. So we want this to go somewhere else. And... This is okay. So the boards response is okay. (t: 1770) So it will be list of boards. Each board will be a database model. But this kind of thing here will be kind of weird. Because I want the model to represent a singular row only. So... (t: 1780) The model should only represent a singular row. Not any... Uh... joint records. Uh... (t: 1790) So we need to figure out how to best... Um... Query this board then to have this in two. Um... (t: 1800) What is it doing here anyways? It is... It's running another query. So this is an n plus one query anyways. That's probably good enough for now. (t: 1810) Um... So let's just say that it should move this over there. Um... The... (t: 1820) Topic... Should go into models topic dot go. And then we have the post... Dot go. (t: 1830) Post should go... Models... Post should go... Um... Let's just see if it... If it manages to refactor this a little bit. (t: 1840) Um... And then we see from there. What we need to do. Does Cloud Code Visual Extension work if you're outside of Visual School Terminal? Um... (t: 1850) Yeah. So if the... If the... If the integration is set up correctly. It works even if it's running on the site. Right? I can also start Cloud in here. But I don't really like it. (t: 1860) I prefer this terminal on the outside. Um... But yeah. It... These changes they still show up. Um... Although I think that this comes actually from the Git plugin. Um... (t: 1870) But we'll see. What other questions there? Um... So maybe I should explain this because I didn't do this. (t: 1880) But... Cloud YOLO... Write this here. It's just an alias for this. So... So... So... This is an alias for this impossible to pronounce argument called Dangerous Skipper Missions. (t: 1890) Right? Basically, I run this all the time. Is it a good idea? (t: 1900) I don't know. I'm not strongly advocating for it. But I can tell you that I'm using it this way all the time. Um... So, that's why it doesn't ask me for anything. It just, it... (t: 1910) It just edits. Um... What are my thoughts on Gemini CLI? I will rewind and start talking about it in the next few minutes. But wait. I will re-evaluate it. Last time I was using it. (t: 1920) The problem basically is that any model other than the entropic family of models is not overly amazing at tool usage. (t: 1930) So I want to see that these agentic loops work. So that's why I'm playing with it. I have most success with Claude. I also think that (t: 1940) Claude is the cheapest option because the $100 a month package in Sonnet-only mode is enough. (t: 1950) And it's kind of hard to beat for the price right now. And I don't know how long this price is going to stick here. But that's really why I'm not (t: 1960) trying Gemini much. I have Gemini on the system. I sometimes give Claude access to Gemini to read through a codebase. But it's... (t: 1970) I'm wanting to get this working first. Working well. And then I will try other tools again. I also tried AMP. I tried a bunch of other ones. But this is the one that is just... (t: 1980) I think it has the highest chance of sticking around also in part because the people that write the tool are also the people that write the model or create the model. (t: 1990) And so they go hand in hand. (t: 1992) So now we have a board.go. Get all boards. This looks quite a bit better. We don't need pagination here (t: 2000) because we don't expect that many boards. So that will be quite good. Now it uses the scan to feed this. And we have a board by ID. (t: 2010) This is also quite okay. And the board by slug. I am quite okay with all of these. (t: 2020) One of the consequences now is that all of these methods can return null. (t: 2030) Or board. So if the board doesn't exist, it returns null. Or nil. Do I like this? I don't know. (t: 2040) So we have this. Most recent post by board ID. (t: 2044) Okay. So one of the things for sure (t: 2050) that is not amazing is that it looks like the board doesn't have a pointer to the most recent topic. But the topic has a post to the most recent topic. (t: 2060) So it looks like the board doesn't have a pointer to the most recent topic. So it looks like the board doesn't have a pointer to the most recent post. So maybe this is okay. I will not judge the database structure too much right now. (t: 2070) Okay. So I think we can stick with this. In theory, if we now go to here, it should look more or less the same. So we have a most recent topic, a most recent post. (t: 2081) Let's actually remove the most recent post from the API because (t: 2090) it's not going to be a post. I don't think we need it. I wonder what is not the author is here. Okay. (t: 2100) Let's leave it for now. Let's leave it for now. But I think we will throw it away. So what I usually do when I program with this is I create myself a to-do file. (t: 2110) Where I basically keep track of all the stuff that I still need to do. So one is nits. nits. (t: 2120) We should remove the most recent post from the API. Next we will put the one thing alsot to add, (t: 2125) which is on the list board and (t: 2130) that should berif from the board listing. Okay. So we will think of this later. So let's have a look at how the API response so far looks like. So we have a list board route (t: 2140) which is hooked up to the router (t: 2150) and it grades this boards response and then responds with說. And then it gets the most recent topic and post. (t: 2160) Yeah, not overly amazing, but kind of okay. But one of the things I do not like is this part here, right? This http.error. (t: 2170) And we have this utility here called internal server error. So we'll actually use this. So we'll call utils.internal-server-error w and error. (t: 2180) Then we remove the other one. So we want to do this. (t: 2190) And hopefully going forward, we will actually start using this utility instead. Why do I want to use this utility? Well, for the one hand, because it logs the error (t: 2200) and it returns with a standardized message. So that's why I want this. And um um and then this is okay. (t: 2210) And so board with recent is an extended struct that has the board in it plus the extra things here. So this is okay. Um (t: 2220) So let's say we commit this. We'll leave this for later. (t: 2224) So let's say edit basic board API response. (t: 2230) So the next thing we want to do is we want to hook up the end, the front end, right? So if we go to the front end, we want to hook up the front end. So if we go to the front end, we want to hook up the front end, right? So if we go to the front end, we want to hook up the front end, right? So if we go here, we don't see anything. (t: 2240) So let's say we want to have the board show up. I want you to change the front end to show all the boards. Um For now, I want you to make sure that we create components for each row on the listing. (t: 2250) So that we can reuse this later. These rows should be reused for topics in a board as well as for the board listing overall. (t: 2260) So that's an example. We might need a parameter to change the... (t: 2270) Actually, I don't want this. Let me do this differently. I want you to now show all the boards the most recent topic in the overall... (t: 2280) In the index page. On the index page. (t: 2285) And let's ignore the... (t: 2292) I want to know. This might... So the problem with whenever it creates a frontend from nothing, (t: 2300) it turns into a mess. Since there's basically no real frontend, this might be incredibly messy. And I'm a little bit afraid that it doesn't even manage to set up the router. (t: 2310) So we'll see what it does. We can watch it. In the meantime, I can look at some questions here. (t: 2320) Yeah, so for how to put the browser logs in a terminal, I used a beat plugin that I wrote. You can also do this yourself from API endpoint. The beat plugin was this one here. (t: 2330) And this is what it does. (t: 2335) One other question is what the font. The font I'm using is MonoLisa. (t: 2340) I think all the time. This one here. (t: 2346) That's the font. (t: 2350) What other question? (t: 2350) Yeah, so one question is, if you manually edit the code like that, do you have the problem that the model is not working? If you manually edit the code like that, does the model has unedited versions in the context? (t: 2360) And yes, this is a problem. One of the problems with this is that it will recall things that you have already thrown away. This is actually a pretty big problem. This is one of the reasons (t: 2370) why I clear the context all the time. The same problem, by the way, also comes up if you do code formatting. It's quite often that the linter and the formatter edit the file in a certain way. (t: 2380) And sometimes they just get back and forth. I don't have a good solution for this, but it is a problem. I can't really recommend anything here (t: 2390) other than I do want to do these commits. Then I want to clear the context. Sometimes I maintain a to-do list. (t: 2400) So before I run out of context, for instance, I tell the agent to summarize everything that it did into a file. And I can look at this file later and then continue from there. (t: 2410) So let's see what it did. It probably has created something here. So this is actually an interesting thing. It has not managed to run this, right? (t: 2420) And so now we can probably see that our tooling comes in helpful, hopefully. When I navigate to the page, I get a bunch of errors. (t: 2430) Please check the log and see what's going on. (t: 2432) Right? So it should now read the log, which it does. (t: 2440) And hopefully see what it broke. Okay, cool. (t: 2450) So it managed. Probably it wouldn't have needed the log, but having the log now means that it was just able to go back there and figure this out. (t: 2460) And at least we have something now, right? So not that I like how this looks at the moment. You can't even click on it or anything, but yeah, we see something. (t: 2470) Let's make two changes here. I want these to be rows. So one, below the other, not next to each other. (t: 2480) And I also want to not show the most recent post. I only want to show the most recent topic. So I just want to make this change and then we're going to figure out how to make it less crappy (t: 2490) because it probably doesn't look very nice. (t: 2493) The way I do front-end code at the moment is I let it write a whole bunch of stuff (t: 2500) and then I ask it to extract components. That usually sort of works. But front-end code, unfortunately, turns out to be very sloppy very quickly. (t: 2510) Okay. So, okay, this at least is getting somewhere. (t: 2520) So let's see what it wrote. (t: 2522) So it created an index route. (t: 2530) So this is already, we're already sort of down. If I, so, please lint everything. (t: 2540) This is already going to be annoying because it clearly left a bunch of nonsense behind. And so the linter will immediately complain, hopefully, that there's unused stuff. (t: 2550) So let's see. (t: 2554) By the way, in this project, I'm not using any hooks. I do use some hooks in other ones, (t: 2560) but I want to start with the basics here. Okay. So we got rid of some unused stuff. So we got rid of some unused stuff. I don't know what page is this. (t: 2570) We're throwing this away for now. And then it created this api.ts. And this is already messy. This is already too big. (t: 2580) So the API client I'm actually okay with, it can leave that. But I don't like the types on the same file. So let's do this. Move the types from api.ts into a separate file. (t: 2590) So, api.ts. Yes, in the separate file. (t: 2600) Actually, other than the API client itself. (t: 2610) Let's see. (t: 2612) Just kind of want to move this out. (t: 2616) So the types are here now. (t: 2620) The API is here. One of the most important things is to make sure that the files don't grow too large. The larger the files, the harder it is for the, for the system to work with it. (t: 2630) So this is, this is, this is okay for now. So we're going to just have, not the nicest thing here. We're going to manually remove this welcome thing, (t: 2640) which I think, where do we have this? (t: 2644) Where is this? It's here. Throw all of this away. (t: 2650) So we have only the boards. Okay. So we have a starting point. The front end so far, (t: 2660) probably a little bit messy, but, initial display of the boards in the front end. (t: 2670) The problem immediately here now is going to be that, we don't have the router, we have the router set up, (t: 2680) but we don't have query set up. So I think it uses, no, it does use query. Okay. It does use query, which is, that's good. (t: 2690) Then it uses this get boards function. It might be okay. Oh, well, we'll see. We'll see how messy it gets as we continue. (t: 2700) Okay. So what should we do next? I think next we're going to show each individual board. So the next thing we need to do is we need to create these boards. (t: 2710) So let's do this. (t: 2712) I wonder if I should continue the session or not. (t: 2720) Maybe we'll continue the session. Might be a bad idea, but might actually help. Now, please add an endpoint to show all the topics in one board. (t: 2730) We will also actually, I will do it from scratch here because I want to set up pagination. Now we need an API endpoint to list all the topics in a board. (t: 2740) Note for this, we will need a pagination helper. We want to use cursor-based pagination. So we need to use cursor-based pagination. That means not offset, but to continue from a specific starting point. (t: 2750) And we want to take the cursor to continue to the next page from (t: 2760) the URL parameter because it's going to be a get request. (t: 2763) It's actually going to be a shitty user experience (t: 2770) because it means you can't jump to a specific page. So we will not use cursors here. We want to use offset-based pagination. (t: 2780) We want to take the page and per page parameter from the URL. (t: 2790) Because it's going to be a get request. (t: 2800) Default per page 250. Also return the total number of pages. And then we will add the page. So we will add the page. And then we will add the page. And then we will add the page. And then we will add the page. And then we will add the page. And then we will add the page. (t: 2810) Don't hook this up to the front end yet. Don't hook this up to the front end yet. Okay. In the meantime, I have more questions. (t: 2816) I do not use compact at all. (t: 2820) Never ever use compact. If you run out of context, compact is basically a command that just screws up everything. (t: 2827) I don't know what happens if you compact. (t: 2830) It's going to be a gamble. It's already random enough what happens out of the box. But, I never, never, never run compact. Instead, if I notice that I'm running out of context, (t: 2840) I'm asking Claude to summarize what it did into a markdown file. I reviewed a markdown file, start a new session, (t: 2850) and then read back from the markdown file. Because then at the very least, I know what it pulls into context. Compact, I have no idea what it does. (t: 2860) I don't think the tool even shows you what it did after compacting. So it's a gamble. It's a pure gamble. I'm not going to do that. Never do that. Basically. Auto compact is, (t: 2870) I'd rather have Claude stop than auto compact. Because it's just so, so random. (t: 2880) It's absolute random. I also only use Sonnet. I kind of wish I could use Opus more. But even a $200 subscription, I run out of Opus. (t: 2890) So I just stick myself to Sonnet. And, I use O3 for planning a lot. (t: 2900) Where I basically go on, I take what I'm working on, copy paste that into just JetGPT, pick O3, and have a conversation about my architecture there. (t: 2910) And I can maybe show this later. I kind of want to hook up the trip codes to post something. And then we'll see how well this works. (t: 2920) So what I should have received here now in theory is, what did we get here? We got the pagination. So, (t: 2930) this is the parameters for the pagination. We get an offset. Interesting. Why do we have an offset? (t: 2937) Okay. (t: 2940) So we're pulling this from the query of page and per page. The offset is calculated. (t: 2944) And then this pagination meta (t: 2950) is probably used in the API response. So what did it do? So it lists the topics. And this is a list of topics and the pagination meta. (t: 2963) Do I like this? (t: 2967) Do I like this? (t: 2970) I don't know if I like this. (t: 2971) Do I like this? I don't know if I like this. We'll figure this out if I like this. I think I might want to rename this to pagination probably. (t: 2981) But maybe this is good enough. So let's see. So in theory, there should be an API now for me to hit. API boards. (t: 2990) Which one? Test. This has stuff in it. (t: 2994) Letterslash? No. What's the API? (t: 3000) Board. What's a board? Board ID? Is it a board ID or is it a board slug? Let's try. (t: 3010) What was the other API? Okay. Slash boards. Board ID 1 is a test board. Did we get something here? (t: 3020) No, we don't get anything. Oh, slash topics. Put this. Okay. Board not found. So we need a test probably. (t: 3030) Okay. (t: 3032) So we get this. Total 1. Total pages 1 per page 10. So if we do page equals 2, (t: 3040) we get an empty list. 3 empty list. Okay. This is okay. I do want to change the meta to pagination though. So I think we're just going to do it manually. (t: 3050) I'm going to call this pagination. So heads up. I'm going to do it manually. So this is a little bit more complicated, but I think we're going to do this. So I'm going to call this. Let's go to the page. What's this? I don't know what it is. So. (t: 3060) What's this? What's this? This is a page. What's that? How do you use it? What is it? What's this? What's this? What's this? What's this? (t: 3070) Is there something here? What's this? What's this? Heads up, I changed meta to pagination. (t: 3076) So I just give it this context immediately interrupted (t: 3080) just so that hopefully it doesn't get confused later through a manual edit. (t: 3090) There are some questions, I will quickly go to them. Have I used context seven? Yes, I don't have good experiences with it. I don't use any MCP servers other than Playwright and I try to not use Playwright either. (t: 3101) So I'm not sure if I'm using it. I'm not sure if I'm using it. Then the other question. Yeah, in general, I don't like it looking up docs. (t: 3110) I much rather give it the docs myself if it needs them. I'm very conservative on context usage. I don't like any tools that pull anything in automatically. (t: 3120) I optimize everything for low context usage. (t: 3122) I want you to now register a URL for the board. So if a user goes to slash B slash slug, (t: 3130) then we will show the most recent topics there and add a pagination for previous and next page (t: 3140) and a basic overview of how many pages exist and a quick jump to a particular page. (t: 3145) Did it manage? (t: 3150) The like slash B, what do we have here? Slash, slash test. Maybe just do, yeah, let's do slash B slash slug. (t: 3160) I like this. And show the topics there. (t: 3170) Most recent first. (t: 3173) And this is good. Note, we can always rely on monotonic increasing (t: 3180) primary key integers for board order. (t: 3184) Because we use SQLite here and I want to avoid it using dates (t: 3190) right now. Also, please use the link component to link from the index page to the board. (t: 3201) See. Okay, let's see if it manages. (t: 3210) Maybe in the meantime, there's some questions. Again, there was another question what I use for text to speech. Right now, this is using Whisperflow. I also use Voice Inc, which is open source. (t: 3220) Both of them work. I just, I'm trialing Whisper this week. Normally, I use Voice Inc. (t: 3230) They give or take equivalent on a Mac. Are there any other questions that I can answer in the meantime? (t: 3240) Because I'm pretty sure this is going to take like four or five minutes for it to produce something reasonable. Okay. So, I'm going to try to get the answer. I'm going to try to get the answer. (t: 3250) I'm going to try to get the answer. Okay. So, earlier there was a question if I'm streaming. This is the first stream I've been doing in three years probably. (t: 3260) So, we'll see if I will do this again. But, yeah, I was kind of lazy. (t: 3270) Yeah. So, how do I write the logs automatically to the devlog file? This is what I'm doing with Shorman. (t: 3280) So, if you... Let's do this. In the meantime, let's put this on GitHub. Then it can sort of steal the Shorman fork that I have. (t: 3290) Then I want to put the whole thing up there. MiniDB. Let's make it a repo MiniDB. Create new repository. (t: 3300) MiniDB. This. Write bullet. A bulletin board for a screencast. (t: 3310) Bum bum. Create repo. And then put this up there. (t: 3320) So, in here in scripts, there's my Shorman fork. I should probably... (t: 3330) Now that this is on GitHub, I should probably make sure that the licenses attribute it correctly. Because I... (t: 3340) I... I... I did not put this in. Let's edit this quickly. (t: 3350) Shorman. (t: 3351) Bop. Bop. Bop. Bop. Bop. Bop. Bop. (t: 3360) Bop. Bop. Bop. Bop. Bop. Bop. Bop. Bop. Bop. Bop. (t: 3370) Bop. Bop. Bop. Bop. Bop. Bop. Bop. Bop. Bop. (t: 3380) Bop. Bop. Bop. Bop. Bop. Bop. Bop. Bop. Bop. Bop. (t: 3390) Bop. Bop. Bop. Bop. Bop. Bop. Bop. Dere wins. Dese fose. Dese doubles. (t: 3400) I navigated to a board and it didn't work. Check the logs. Right, and this is again why the unified logging is so functional. It sees my browser logs, right? So it doesn't just see the server. (t: 3410) So it should hopefully figure out what it did wrong. I don't even know what happened because I'm assuming that (t: 3420) it wasn't too wrong that it did, right? Because there's a bboardtsx. This should work, but maybe it doesn't. (t: 3430) So hold on. Could it be that we don't run the 10-stack plugin for Vite? (t: 3440) That would be a problem. Like this is supposed to be a 10, but (t: 3450) because the plugin for 10-stack should do all of this. (t: 3454) So you probably don't have this plugin in there. (t: 3460) Does it work now? (t: 3464) Not so far. Ah, because I didn't plug it in yet. (t: 3471) I actually think that this is... Ah, look at this! But isn't this nice? (t: 3480) I didn't even have to figure out what's going on. Okay, I did give it a hint that it has to set up the plugin, but I mean this is... I love this. You really do. (t: 3490) You really do. You really do. This is just so nice. (t: 3492) So here you see one of the problems, right? So it was in the wrong folder, so it couldn't figure out how to make the tail. (t: 3500) And then it immediately ran and went for the log itself. And that's one of the big problems right now why I'm so careful about giving it the right context, (t: 3510) because it actually went the wrong way. It still managed to succeed, but it should really have cd'd into the right folder and run make tail log there, and it just didn't do it. (t: 3520) And this is basically contributing to context fraud, right? Now it has remembered that this didn't work, but this worked. And it shouldn't, right? (t: 3530) It should not make these mistakes. And I'm trying to nudge it in the right direction. So one of the things I can try here now is that on the make command, maybe we can... (t: 3542) Honestly, I only have partially good results with this. If you fail to run the make file, (t: 3550) you have to remember that you have to run it from top level. Let's put it here. (t: 3560) Right, so maybe this will nudge it in the right direction, but can't guarantee. (t: 3570) But still, I mean, like, pretty cool. Okay, so we have this now, so the board roughly works. (t: 3580) So let's double check quickly what it did. (t: 3583) So we have... This is auto-generated, we don't care. Now we have here a link, so we can check this again, (t: 3590) but it added the link component as instructed. It goes to the board. And the board itself... (t: 3601) It has a pagination somewhere here, but we don't really see it, because we don't have enough topics. (t: 3610) And now... Now let's do this. Now let's be creative. To test this better, I want you to generate 120 different posts across 10 different topics (t: 3620) and put them into all the different boards that exist already. (t: 3630) To make this easier, please write yourself a little test script. (t: 3633) And just... Actually... Let's see. Let's see. Let's see. (t: 3640) Let's see. Do I want it to write a test script or figure it out itself? (t: 3643) Just use Python for this. Use UV. (t: 3650) And put it into scripts. We might need this later again. Right, so I'm basically... (t: 3660) Actually, hold on. One last thing. But please use inference to generate a bunch of real sounding conversations. (t: 3670) And pipe them into an input file that this script will then use. Okay. So basically, I want to get it into a situation where it now generates me out an entire board, (t: 3680) so I can test this better. (t: 3682) Mario, since you're writing, back to Whisperflow. Um, I'm trialing them both. (t: 3690) But the problem with Voice Ink at the moment is that the AI integration just adds too much latency. And I want a little bit of fixup. Um, so for the screencast, I opted to Whisperflow. (t: 3700) Um, it's all about latency for me. And Whisperflow is just the lowest latency thing I've found. (t: 3710) Um, so this is really why. (t: 3720) For me, one of the really big benefits of agent decoding is actually test data generation. Because I'm actually struggling. I'm struggling a lot with traditional applications that all of my test data just looks not great (t: 3730) enough. Um, and now you can just get an LLM to really create your pretty good looking test data. (t: 3740) And it makes it much easier to see the product, to feel the product, and to experience what it looks like. Um, so that's just such a nice, nice aspect of it. (t: 3750) This is going to take a while, so maybe we go to questions. Um. Try to use Tmux for better locataling and running servers. (t: 3760) I don't know. I like what I have. Works good enough for me. Uh, what else is here? How do you disallow MCPs? (t: 3770) I just don't load MCPs into my context in the first place. Um, one question is, do you have any experience with the amount of (t: 3780) usage you get out of a $20 cloud subscription? (t: 3782) I don't know is the short answer. Um. I think that you don't get that much out of it. (t: 3790) But I'm not sure. You can try it and see. I can tell you that with a $100 cloud subscription and you only use one agent at the time with (t: 3800) Sonnet, you're not going to hit the limits. With two or three simultaneously, you can hit the limits. (t: 3810) Um, with a $200 subscription on Sonnet, I don't think you can run up and run into the limits. I don't think it's possible. Uh, but with the $20 one, I'm pretty sure that you can run out very quickly. (t: 3820) Um, do you use the plan mode? So because I use dangerously bypassing permissions, I don't really use the plan mode explicitly. (t: 3830) And the problem for this is that it actually disables a bunch of things. (t: 3840) So when it plans, it permanently asks for permissions for all the tools. So I basically ask it to plan without plan mode. Because the plan mode, as far as I can tell, at least in parts, auto activates just on (t: 3850) prompting. Um, but that's really why I don't use the plan mode. Um, and, and that's sort of the answer. (t: 3860) So, uh, it's still generating. Um, yeah, they're like, I think they're, they're probably like 10, 15 different pretty decent (t: 3870) voice to text things at the moment, um, for all kinds of different setups. And I think it's a little bit ridiculous. And I think it's a little bit ridiculous to pay for whisper flow. (t: 3880) And I don't really like that because it is the magic is happening on device anyways, uh, on, on the whisper model, which is the open source one. So, um, yeah, I hope we just get to the point where, um, something like whisper flow in (t: 3890) an open source way, um, becomes like a, like a thing that everybody contributes to. (t: 3900) Um, okay. So it's not generating boards. Cool. So look at this. I have, we have stuff to look at. (t: 3910) Is it not nice? It's just auto generates all of it. Nice. Best set up for home office coffee set up for home office. Look at this. Um, cool. (t: 3920) So we have, we have content, which is cool. And it wrote me this little script here, um, to populate the forum, right? (t: 3930) I will not even look at the script. You don't have to. I don't care. It did its job. (t: 3940) Um, so what I will do now is I will commit this. Uh, we'll do first, we do web and we'll format this quickly. (t: 3950) Uh, then we check in, um, edit board listing, no topic listing and boards. (t: 3963) Um, um, um, um, um, um, um, um, um, um, um, um, (t: 3970) um, um., um, um, um, online. Uh, so now that we have this, we can do one last thing where we do, (t: 3980) actually, I should probably have check that we had here. (t: 3985) Um, anyways, it doesn't matter. (t: 3990) Next thing is we're going to handwriting the topics. Now I want you to make, a way to look at all the topics. So basically we are going to add an (t: 4000) endpoint to see all the posts on a topic with pagination same general API flow as we had for the (t: 4010) board index page and we also want to add the front-end component and the front-end page to show that too and again also support pagination. (t: 4020) I don't know if this will work let's see and I'll go to the questions at any time. (t: 4030) Yeah this monthly paying for basically local whisper models is nonsense. (t: 4040) I'm actually quite okay for paying for the API inference but I also (t: 4050) don't know if I can get the API to pay for the API inference. I don't know if this will work let's see and I'll go to the questions anytime. I don't think that I think you could actually fix up a lot of the little issues with voice input on a very well-trained local model too. So yeah. (t: 4060) CI is not an alias for commit that's an alias that I set up. So I have an alias in my git config. (t: 4072) I have a bunch of these ones here. It was an alias. (t: 4080) I have an alias on my curial which I was using before git and I got so used to it that when I moved to git I set up this alias and never went back. (t: 4090) I have no idea what Kimi is. Oh you mean like Kimi v2 the that this new model is that is that is it the new huge model is that Kimi? I haven't tried it. I heard that it's pretty good on open code if you use it through (t: 4100) uh I guess open router. (t: 4110) Or something but I haven't tried it. Um didn't have the time. Um okay. (t: 4120) So very slowly this will start working at one point. (t: 4124) We'll see. We'll see. (t: 4130) I mean this is not a very interesting screencast in many ways because this doesn't really show a Chen decoding all that much because there's really not that much to see. (t: 4140) I'm just adding more of the same now. If I still have some time, I think I have 20 minutes left. If I still have some time, I will try to add some tests, which I think is more interesting. (t: 4150) And there's a question, what do I usually do while waiting for Claude? So this is the moment when I'm going to pitch Vibe Tunnel. This is a thing we built, (t: 4160) or actually I think I was barely involved at this point in this project. This is, I think this is primarily in our Marius and Peter's project, but it's a way to basically run (t: 4170) all of your Claude instances through the browser. So I can go in for a coffee and then see what it's doing. (t: 4180) That's the general idea here. But the answer of like, what do you do while waiting for Claude is you go to Twitter and you write stuff, I guess. (t: 4190) See, the problem with the music is, let's see, let me see. I need to type. Turn on the screen capture sound. So now, hold on. (t: 4200) Can you now hear the terrible music? (t: 4204) No, it doesn't work. Doesn't work. (t: 4210) Can you hear it now? (t: 4220) Anyways, that's the music. (t: 4221) Let's see if we see our topic. No, it doesn't work. It's still been in front then. (t: 4230) Yeah. (t: 4240) So maybe here's an interesting thing. Why am I using Go? Go is a language I don't like as a human writing code. (t: 4250) Maybe now that I'm sort of writing it more indirectly, I don't mind it quite as much, but I kind of want to show why Go, Go, Go, Go, works so well for agentic coding. (t: 4260) Look at this. Okay, maybe this is the bad one. Maybe we are looking at the handlers. I mean, look at this. (t: 4270) This you would not write in any other programming language than Go, right? You wouldn't say, if error not nil, return internal server error. (t: 4280) Like I have one, two, three, three branches just to handle server errors. And I know that there are ways, in which I could do this differently and then return an error (t: 4290) and like handle some of it on a higher level. But my point mostly is in Python, you wouldn't write it because it's ugly code. In Rust, you wouldn't write it because it's ugly code. (t: 4300) In Go, a lot of Go code looks like this and it's perfectly fine. So the bar of error handling in Go is exactly that bar (t: 4310) and an agent are writing exactly that code. So it is not anything, any worse. And one of the consequences of this, (t: 4320) like all the handling is local. So it's very easy for the agent to understand what's going on because it doesn't have to look through so many layers of abstraction. (t: 4330) Right? It sees basically everything that's going on in this function is going on in this function and not anywhere else, right? It doesn't have to understand complicated error handling patterns elsewhere. (t: 4340) It's pretty straightforward. That's why Go is so good from a code writing perspective. The other thing is that, all of the meta shenanigans that this language has is pretty standard too. (t: 4350) Like there's not a lot of complexity you need to understand. Yes, there's some attributes on it, but it's good enough at comprehending this. (t: 4360) And the last part is if you run the Go tests, then it caches them. And so you can basically, and I don't have the test set up yet, but you can with Go, you can basically tell it to run all the tests at all times (t: 4370) without it slowing down the agentic loop. And that is so good because it means it never, never accidentally tests too narrow. (t: 4380) So in Python, I have it that tests one function only because it explicitly only tests that function and it completely forgets that it five minutes ago broke another function (t: 4390) and only at the very end it discovers that it made a huge mess. And with Go, it just doesn't happen. And I will show this in a bit, but (t: 4397) now, now supposedly I can look at the topic, (t: 4400) but I'm actually not sure if that is correct because it doesn't seem to, it doesn't seem to work. (t: 4410) So, well, I can click on something and nothing happens. (t: 4420) What's going on? (t: 4421) So when I click on a topic, nothing happens. I don't actually see the topic. I will stay on the board page. (t: 4430) What's going on? (t: 4431) But we can in the meantime, look at the code that it generated. So. We got more API to return posts. (t: 4440) Then we get, it's here. (t: 4450) What do we have here? Get posts for topic of imagination. I'm assuming this is probably OK. (t: 4456) What did it do here? (t: 4460) What did it do here? parseInt. (t: 4465) That's why. (t: 4470) Why do I have a parseInt, Alisson? That's the kind of slope that should go. This will go into our to-do list. (t: 4480) Get rid of parseInt. (t: 4490) This slope should go away. Um. OK. OK. So, so this. (t: 4500) OK. So what do we have here? We have. What's going on? (t: 4510) VoidT. (t: 4514) I don't actually like the depictT. (t: 4520) Why did it make this folder? I can delete this folder. Um. OK. So. Did it find the problem? Well, I don't think it. I think it's completely wrong. On what it's trying to debug here. (t: 4530) But look, it's checking the route tree if it's regenerated. So that's positive. Wait. I think it's the issue now. OK. So. So. So. So. So. (t: 4540) So. So. So. So. So. So. So. So. So. So. (t: 4550) So. So. So. So. So. So. So. So. So. I excited to look at the (t: 4570) this all wrong. Actually, I think the issue here is like. Let me try. I think the issue here is that you need to create (t: 4580) dollar board. Here. See? then this has to become indexed here's x always (t: 4590) I think it has to go here move and then this has to be (t: 4600) e.topic I think that is how this works is this how it works or did I fuck up everything (t: 4610) what's going on compare (t: 4620) okay what's going on alright (t: 4630) okay I broke everything classic but what did I (t: 4640) break I think I broke something (t: 4650) I'm always confused so not only am I confused by 10-stack router (t: 4660) it's also that the LLM is confused by 10-stack router but I've been in this situation before and I think it's related to that it has to be in this right structure here (t: 4670) yeah look at this now it works so I'm going to go to the next one okay cool so welcome to the new forum (t: 4680) we see stuff here now this works nice still slop though but slightly better slop so let's commit this (t: 4690) and then try to make a test let's finish it off by adding a test make format web edit (t: 4700) topic listing and maybe one last thing we could do was like actually add support for but I want to write a test (t: 4710) I think I want to write a test let's see what else was written there let's see what else was written there how do you feed it (t: 4720) yeah so once more the front-end log to Cloud Code is basically a front-end I probably plug in that forward it's just yeah this is nothing like cursor (t: 4730) like even the cursor agent is nothing like this like this is a complete input function like even the cursor agent is nothing like this like this is a complete input function like this is a complete input function a completely different experience. Okay, so let's write some tests. (t: 4740) This is what we're here for. Let's write some tests. So we want to write some tests, but the problem with tests is that agents are not very good at writing tests. That's really the reality of all of this. (t: 4750) So we're going to write one test. (t: 4754) And I think we're going to, (t: 4760) actually, before we write a test, we will write a way to create posts. I want you to add an internal function to create posts, (t: 4770) which we will then hook up to an API later, but we don't hook it up yet. (t: 4780) And we also want the function to create topics. So that is basically creating a post plus a topic in one go. (t: 4790) And then I want you to write a singular test that creates, (t: 4795) no, no, no, I don't want it yet. Okay. (t: 4800) Let's do this APIs and I will make a test plan here. Test plan. (t: 4806) Because the thing with the test plan is that, (t: 4810) here's how usually one test should work. How tests should work. (t: 4820) All the data by tests should use, you know, test. All the data by tests should use, you know, test. All the data by tests should use, you know, test. And then you have to write the test plan. That's actually the biggest problem. Because the way it wrote the test right now is it wrote it against the underlying SQLite code. (t: 4830) And the problem with this is that this doesn't have enough abstraction to allow you to basically have implicit rollbacks. (t: 4840) The way I really like my code to work is that you can do something like this, that you can do, (t: 4846) this here. The way I like it is that you can write tests that insert, insert, insert, insert, but then (t: 4850) the rollback. (t: 4860) And for this to work, we need to change too much because we need to basically, if you go with post, write this here, for instance, it takes a SQLite DB. (t: 4870) But when you do a transaction, when you basically do, we do txn error equals db.begin, I think, (t: 4880) right? If error not null, return nil error, right? (t: 4890) This here is a different type. I also want this different. (t: 4900) Come on. Yeah, there we go. So this here, this is going to be a problem now. Because my parameter here can be either a database or it can be a transaction, right? (t: 4910) So for my test setup to work, we basically have to refactor the entire code base. (t: 4920) And they're just not amazing ways, I think, to do that. So this might be annoying. (t: 4930) Let's see. So create topic. Right. So here we have this one. Right. See, it creates a transaction. (t: 4940) And now for this to work with my intended rollback strategy, this also has to be save points. So this might be annoying. (t: 4950) All the database code has to be changed so that we can use transactions and db.sql.db. (t: 4960) So this is going to be the point where maybe we're going to ask Gemini. Because I think that Sonnet might not be able to do this in a good way. (t: 4970) So let's see. How do we do this? So let's commit this. Create us. Edit functionality. Let's say we want to do this with the db.sql.db. And we'll do that. So let's commit this. It's going to be the same way. Let's create a new one. (t: 4980) Let's say we want to do this with the db.sql.db. Let's write this. Let's give it a name. Let's call it, let's call it, let's call it. And we'll give it a name. um (t: 4990) how do we do this so let's commit these operators edit functionality (t: 5000) do create posts and topics so now we should we should come up with this test plan um (t: 5010) let's let's see if sonic can do it um i want to write some tests but the way we're doing database transactions (t: 5020) right now doesn't work for how one tests to work please ultra think how to re architect the code to support this better (t: 5033) um let's let it do this thing (t: 5040) i just don't want any more complicated um but there's just one way to test databases and that's rollbacks and (t: 5050) i think i think we might need to do this refactor uh any other question in the meantime (t: 5057) no no other questions so at least i think there are no other (t: 5060) questions um so i think that's it for this one um (t: 5070) um so the git the git repo is on git already on github it's (t: 5080) here mini db why did i call it mini db it should be mini db (t: 5091) it's mini bb there you go hey (t: 5100) It's based on that. There you go. (t: 5110) The other thing is like for this authentic coding with streaming, I don't quite work like I work normally (t: 5120) because first of all, I don't talk all the time, but I also don't stay engaged with the agent as much as I do right now. (t: 5130) There's a lot of waiting involved. So I try to paralyze work. I try to do other things in the meantime. So it's a little bit different. (t: 5140) So let's see what it did. It decided that we are going to use an interface called QueryR. (t: 5150) Huh. (t: 5160) Really? That's what we're going to do? (t: 5170) I don't think it's going to work. I don't think it's going to work, man. (t: 5180) Come on, come on, come on. (t: 5183) Think hard for this problem. Does this actually work with nested transactions (t: 5190) and save points? Correct? Correctly? Question mark. Because I don't think it works because it will have to know how deep it is. (t: 5201) How do we distinguish between Clo and Gemini? (t: 5210) What would we use Gemini for? Gemini, the model, is excellent at programming. It is also excellent. (t: 5220) It is also excellent at thinking, if you can call it this way, and creating architecture and back and forth for this. Gemini, CLI, the command line tool, (t: 5230) it's not amazing. And Gemini, the model, is not very good at tool usage. So for the agentic loop, I still haven't found anything better than Sonnet and Opus. (t: 5240) But this is also why, and I mentioned this earlier, I use O3, and sometimes I use Gemini to plan larger changes. (t: 5250) And then I give the output of that to Sonnet. And I just do the planning of larger changes just in the UI in ChatGPT or in the AI studio, for the most part. (t: 5260) Then there was a question of vector. I tried OpenTelemetry and a bunch of other things. It creates too much nonsense, (t: 5270) too much output with all of the spans that it produces. And it didn't work quite as well (t: 5280) as just the way it works. It's just a simple thing of logging everything into one file. I actually struggle to make this work. And I find it also to be quite involved and also Gemini, sorry, Gemini, (t: 5290) Claude, who just not fully understand how OpenTelemetry works. So right now, at least, (t: 5300) simple log files work incredibly well. Complicated OTEL stuff, it doesn't work good enough for me. Okay. (t: 5310) I would actually love to see someone show how to use OTEL successfully for agentic workflows. Just it didn't work for me is all I can say. (t: 5321) So what did it say? (t: 5327) What did it say about my interjection? (t: 5330) Did it say something? What is it even? I don't know. (t: 5340) I think I actually just lost my count. It did go and configured it and 있으니까 and Why on Earth? Well, if I apply estão até que só no é isso? Porque eu fiz o gráfico também com nodes espanhóis Atmologia foi pela minha segunda vez quando eu tinha a transação do schlepeio. (t: 5350) So I have to ask my hun 제 giant entreverditei an in the time I worked��피 The fixing point for me is on the way that 보이는 In the time, for example, (t: 5360) I can see DecreeCard doing a job for You I'm sure you have obviously all the atakuار have to work. AtOME juks (t: 5370) The problem is I know how I set up this to normally work, and I don't know if the AI can actually one-shot this. (t: 5380) Okay, so it has a nesting level now, it has save points. (t: 5390) Maybe, maybe, maybe. Okay, so we have a board test. So we're gonna set up TestDB, so it creates a SQLite memory database. (t: 5400) So really a net- Muslim- grounded database that lets mm moet 내 handsi (t: 5420) Okay, so I can find the code that's SOS Y2C, and that's going to role call. And I'm gonna run this, and I'm gonna answer these. This is slopp, pure slopp. (t: 5430) Why do we do this? is already making mistakes that it doesn't do on a smaller context. (t: 5433) It went too narrow on one specific problem. (t: 5440) And this is the point where I no longer expect good output from this, actually. (t: 5460) Does it even manage to run migration for testing? Like, why? (t: 5470) Why are we doing this? And what is begin tx here? (t: 5480) Now it's turning into full slop. (t: 5483) And this is all just for the test setup. So what I will do now is... (t: 5490) I will make a branch. (t: 5493) Testing setup. Because I don't like any of this. (t: 5502) Make format. So we're going to... Pretty initial test setup that doesn't quite work. (t: 5512) So we're going to... We're going to go back to the drawing board here. (t: 5520) So... I think this is awful. (t: 5530) This might be really, really awful. So let's do a div to main. So where did the slop start? (t: 5540) This might still be okay. Okay. (t: 5543) So... (t: 5550) The strategy now will be to unslopify this and to get it to do something. And maybe the way of doing this will be to get the test's RNS to run. (t: 5560) So the board test, we're going to make this not terrible. (t: 5570) So run migrations for testing should just be run migrations. Why is run migrations here in lower case? (t: 5580) Because we have init. Okay. So we have init, which runs migrations. And that is what the server is doing. (t: 5590) Right? (t: 5592) So... So run migrations for testing does this instead. (t: 5600) So let's start with this. Take this and make a test. Okay. (t: 5610) So let's start with this. So let's start with this. So let's start with this. So let's start with this. So let's start with this. Okay. So let's start with this. So let's start with this. Which creates the test harness. Run all. Add a test setup function, which takes a callback. (t: 5620) Which handles migrations database in memory. (t: 5630) Then update the tests to use this. I always use this. (t: 5640) I also want you to reuse the test database. (t: 5645) We test runs so we don't waste as much. (t: 5653) Actually, I don't wanna explain it, but I don't wanna migrate all the time, basically. (t: 5661) And then we should test if this actually works. So create, let's review this. (t: 5670) So create post now uses Q exec. (t: 5680) And so let's see what we have begin. Dot begin, let me do dot begin. (t: 5690) Begin transaction go, db go. Migration word test. (t: 5700) penet Mai. (t: 5700) All right, so this is just all nonsense. Nonsense. (t: 5720) Pests are the worst because it doesn't understand how to create a test harness. This entire thing should go, yeah, (t: 5730) TestTransactionManager. So, set up TestDB. (t: 5734) What is it doing? What's it doing? (t: 5740) Alright. I might actually have to defer this to (t: 5750) next time, but I would love it to at least set up the harness correctly. (t: 5760) So, here's my best recommendation here at this moment. (t: 5763) Don't set up tests initially with cloth, because (t: 5770) it just doesn't understand how good tests should look like. And I don't know what it says about us as programmers, but the way it sets up tests is just bad. I can only assume that the bulk of people out there (t: 5780) are writing horrible tests. (t: 5784) All of this is wrong. (t: 5790) What it should actually do is we should set up a really good transaction infrastructure in the beginning. The pattern I like to use here is actually from Django. Django has these atomic blocks. (t: 5800) They work quite well and they hide save points in transactions properly. So, I should actually do that first, get this in a good slot, and only then start writing tests. (t: 5810) Because everything that it has done here so far is really, really bad. (t: 5814) Um... (t: 5820) Really bad. Yeah, so, um... I might do the following. I might let this run, and actually set up the tests correctly (t: 5830) in a way that I like. And then I will show either at a future stream or just in another video or just a follow-up post of how to run the tests, because I don't think we're (t: 5840) going to get to a reasonable point in the next 20 minutes. Okay. (t: 5850) And... I don't have that much time. I gave myself an hour and a half and already over time, so... Maybe I will do two more minutes of last questions. (t: 5860) Um... (t: 5862) Yeah. But I think I will leave it here. And then, uh... Thank you so much for watching. (t: 5870) See you next time.

