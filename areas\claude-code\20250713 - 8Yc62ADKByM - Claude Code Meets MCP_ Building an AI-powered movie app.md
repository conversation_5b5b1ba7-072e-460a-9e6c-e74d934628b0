---
title: "Claude Code Meets MCP: Building an AI-powered movie app"
artist: <PERSON>
date: 2025-07-13
url: https://www.youtube.com/watch?v=8Yc62ADKByM
---

(t: 0) Today we write a tiny movie site and wire it to real AI-powered chat, start to finish. All with Claude code. Nitty gritty in here. Jabber jabber jabber. (t: 10) Oh, <PERSON>. I love you. In this build, we'll build our own MCP to learn exactly what these MCPs are for (t: 20) and exactly why they're so powerful. We're going to build with FastAPI, FastMCP, OpenAI Assistance API, and of course, Claude code. (t: 30) But let's dive right in and have some fun building a brand new system that will show us movie data and allow us to even chat with it in no time. Here we go. (t: 40) Okay, where else do we start? But of course, in Claude code, in Cursor. I have a lot of videos that kind of go over that recently. So if you want to see how I'm working, go through those. (t: 50) What is this MCP stuff all about? How is it useful? So I thought I would build a simple little application that we can kind of search for or look at different. TV shows or movies or something like that. (t: 60) What I'm trying to do is build enough of it that you can kind of get the understanding of the moving parts more from an architectural standpoint. But we definitely will get into some of the nitty gritty in here. (t: 70) It's worth saying I am here actually filling in a document for the TMDB endpoints itself. The API TMDB is kind of a online movie database environment. (t: 80) You might know IMDB that allows you to go search for movies on the Internet, the Internet movie database. Well, TMDB is the public version of that. (t: 90) People supported, you know, community supported information that's public to it. You can see I've given it a starting point in a document here and said, (t: 100) hey, here's some of the pointers of the different areas in this documentation that I want you to go look at. I need you to go and make sure you have a complete document around how to use this API completely. (t: 110) And I need you to fill in this document. Finally, that's actually kind of interesting. That'll. It'll create its own source document for the information it's going to need to move forward (t: 120) and build this stuff. All right. So that's enough jabber, jabber, jabber about all of this. Let's get in and see a little of it being built so that we can understand it. (t: 130) OK, so here we are. It has built this document. I'll show it to you in a second. But first, I've created a PRD, which is, again, just a requirements document that basically (t: 140) describes all of the things that we need for the back end services, how we're going to build them and which ones we want. And so I will paste this in here and you can see that it has a whole bunch of information (t: 150) about what should be built. This is something that I worked with Chachupito 3 to get to. I kind of like its planning mechanism, but you'll see we end up with a movie and TV interface. (t: 160) That's the final MCP. But here we'll have a slash movie slash TV API endpoint. (t: 170) And so I'm going to untie that in just a second. But let's take a look at what it did for looking up this endpoint information. And the document that it built, I thought this was a really elegant way that you can (t: 180) use cloud code to kind of crawl information that's out there. So if you look through this document now, it really has the required parameters for (t: 190) each one of the API endpoints. It has the endpoint itself. Very interesting. Some of the optional information. I just thought this was really cool to say, hey, can you go look at all of these resources, (t: 200) these URLs, the API itself, and build a document that's local to this package that'll just live here for now? Because this is our understanding. (t: 210) So we're there. This is going to go off and cook and do its thing. In the meantime, let me talk to you briefly about what we're building. So you kind of get the concept. (t: 220) I've told you that we're going to build an application that's kind of going to show details on a TV show or a movie. Nice and easy, right? We'll build another version of something like this that will be the UI. (t: 230) So it'll be shows UI. That will just be another server that we bring up, and it will point at these services to get the information. So that we're seeing this two-part kind of deployment environment. (t: 240) It's a good architecture, scalable architecture to say the back end only delivers when it delivers. The front end only delivers when it makes changes. (t: 250) Super simple. But that will help, I think, mentally separate them so they don't feel like, well, there's just a whole bunch of magic in this project that he built, and it just works. But the first thing we're going to do here is we're going to build the API itself. (t: 260) And we'll go in and look at how the API works. Should be really plain and simple. And we should be able to say, when you come in... When you come in with movie or you come in with TV and an ID, what is it doing? (t: 270) And then we'll go run it with curl to kind of take a look at what it's doing. Okay, let's take a quick look. So the services have run. (t: 280) Let me get them up and running. So now we have a full application running, a service running that hosts this API. So if we run that, it runs off, calls our service. (t: 290) You can see it downloads and then gives us the output of that information. Let's build it. Let's very briefly take a look at the API and how that works. (t: 300) So over here in the code itself, if we look in main, the main is just setting up a fast API. You don't need to know any of this, but it's just basically a framework that allows you to create APIs very easily like this. (t: 310) And what we're going to do is set up a few end points that allow somebody to call. (t: 320) One of them that we were, we just called is this slash TV. What it runs is in here and in here, you'll see that it's using our code. Okay. So this is just a call. So we're going to call it get TV series. (t: 330) And what that's really doing is running another function, another class that Claude wrote. Of course, Claude wrote all of this, and it's just going through some stuff to fetch the information. (t: 340) If we get inside here, we finally get to the URL, the base URL of the API. The TMDB.org API is here. Slash TV slash series ID. (t: 350) Then they go and call a get command to get it. Blah, blah, blah. So you can see all of this, right? This is just doing standard API stuff. This is how most of the services on the internet kind of work. (t: 360) Similar to this. But let's do something a little bit more interesting than that. Okay. Here we go. Of course, just another cursor instance or really another Claude code instance. (t: 370) Let's build the UI. (t: 374) Okay. It might be fair to ask what on earth I'm asking for here. (t: 380) This is pretty standard. I'm just creating a next JS system, which kind of is an opinionated way to bring in react. And TypeScript and a couple other, the kind of standard practices that I like to pull in. (t: 390) This is also using shad CN as the design language. No big deal. Largely, this is not that much. (t: 400) I basically said, I just want a base system, but I do want two routes. One is the movie route and the other is the TV route. They will take in these IDs that you're seeing it put in here. (t: 410) ID after movie ID after TV. And I gave it the URL that it will need to call for now for the back end. To get the data to fill this in. (t: 420) And of course I gave it the full URL and said, you should make the call yourself. To see what the response looks like so that you can then properly create the UI. (t: 430) So really, actually, I kind of hope we'll see that when this is up and running, all we have to do is, you know, go to the TV slash ID and we'll see our results. (t: 440) Let's see how that goes. Okay. It says it's done. We've started it. That's great. Let's come back. And if we look at this screen, let's see what it does. (t: 453) Oh, Claude. I love you. (t: 460) Okay. So this was nothing. What you're seeing here is the service call. So you can see that we call the get to the service up here and down here. (t: 470) Is the actual web application that's executing. So it's no big deal. It's just running, but it's calling this. Service to get the data, which of course is calling TMDB to get the actual data. (t: 480) So kind of fantastic. This is full circle. And of course the screens brilliant. I really did not tell it to do any of this. I just said I needed to do kind of a, a view of a video application. (t: 490) Here are the fields. Go take a look at the fields yourself. I pretty much told you what I told it. So you kind of know it's kind of crazy. (t: 500) They can get even this close to something relatively attractive, attractive. Of course, you know, some kind of video UI on the web. It's not exactly unique to the world. (t: 510) It didn't have to be that creative. All right, enough of this. Now the question is how unuseful is this really? So this is great. What we're really saying is this application here knows very specifically how to call the back application. (t: 520) So it has to know there's a slash TV, what we call endpoint, and it has to know that it has to pass in a new ID. (t: 530) Okay. That's fine. If the back application ever. Released again and said, Oh, you know what else we need? (t: 540) We need not only the ID coming in here where we're saying TV series ID, we want to put another one on, which is date or token or something else. (t: 550) We need to put that out there for some reason. That's the new requirement. Well, what happens in that case? And this is not uncommon in the world. Is this other application that somebody else wrote or we wrote and hosted somewhere else or some other team in our company wrote breaks. (t: 560) So these are what we call. Tightly coupled, meaning one moves. The other has to move one deploys. (t: 570) The other has to deploy those kinds of things. They're brittle. The Internet is built on this brittle nature. So when we start talking about intelligence, that's not really going to work. (t: 580) And it's certainly not the most extensible. And this is where the magic of MCPs come in. So I want to talk about that just a little bit. But first, what I want to do is add a little chat box here so that I can have conversations with some chat system about this. (t: 590) So. Ok, real quick, what I've done so far is I've come over to our services and I've created an MCP. (t: 600) So all I've done is added the MCP to this set of services. (t: 610) And if you look at it here, when we call it, if we call for MCP, now we're just going to slash MCP. You'll see what it comes up with is a description. (t: 620) And this is what it's like to call the front door of an MCP. Basically, this is how when you tell Cloud Code Desktop. That you have an MCP server. They actually, when they're instantiating or kind of referencing that MCP are calling for this to say, OK, let me go call this. (t: 630) What's its name? That's its name. What does it basically do? (t: 640) And then let me get each one of its tools. Get TV tool has some information. It says gets TV series details from TMDB. It takes in a property of type ID. (t: 650) So this is just a schema kind of defining what you can send to the thing. And that ID. Is also being described. It's a string and it's also being described. (t: 660) So you can see this structure that's coming out. We don't really have to worry about the absolute details of what's going on here. But when the intelligence parts, you're telling them about these different MCP. (t: 670) Basically, you're saying, go grab this file, this document, and then that will give you all you need to know about what's inside of that MCP. (t: 680) The tools it has, how you call it, what the properties are, and maybe what comes back from it. Those kinds of things. Like it says, this is TV series details, right? (t: 690) So this returns TV series details, including name overview genre seasons and more. So you can see that there's this kind of loose contract being built through natural language. (t: 700) Now, of course, what's going to consume natural language, but intelligence. And that's why MCPs are so powerful from an intelligence standpoint and not quite as powerful for something like an API standpoint. (t: 710) So we have these applications out there that could also use this basic interface. And absolutely can. But at the same time, consuming it and understanding it for free is really where the value comes from. (t: 720) All right. So the next thing I'm going to show you is the UI has been created for a chat system, but it doesn't know anything about this MCP. (t: 730) Okay. Here we are back at iZombie. And okay, we can come down chat to iZombie. (t: 740) What is this about? So this is just using OpenAI, you know, standard interface to talk. About the context that's already been poured into this LLM. (t: 750) So this might be a way or into this chat interface. So this might be a way to just immediately add some kind of intelligence to the system that you have. (t: 760) But what I can't do here is send something like that in. So when I say give me a info on the movie, this number and by the way, I know I'm being really prescriptive here. (t: 770) I apologize. The API that we built is taking in IDs only. So the way to call it is with an ID. And that makes it very. To open AI when we send this in and it actually works well that it'll say, I have no idea what you're talking about. (t: 780) The reason I'm calling it this way is that would be the ID. If intelligence was reading this and knew it had a tool that could take in information about a movie and an ID. (t: 790) So let's add the MCP to this guy, just awareness like we would anywhere else and see if he then can give us this information. (t: 800) Okay. So everything's finished supposedly. Let's take a quick look at what. We have here. What I want to do is I want to get the service running first and you can see it's just startup completed. (t: 810) I'll move this down. Once we start our UI, what it's going to do because it knows about this. (t: 820) MCP is it's going to call for the information that we were looking at, right? We expect that it'll get started. And then once we go to a page and say, I want to chat. (t: 830) All right. Tell me about the movie. With the ID of this. So it calls for MCP gets information about the MCP. (t: 840) And then made the decision to call the movie endpoint. Got that information back and turned it into the results here. (t: 850) So it took those results that it got back from this server, sent all those results back to intelligence. So open AI had a chance to say, oh, I have the record. (t: 860) That's great. Now I see all the moving parts. Let me construct a message and give that message back to you. And say, this is the answer to the request that you had. (t: 870) And that's why it's formatted so well is this is intelligence doing this and understand some of those moving parts. Okay. And I've added one more interface to this to show that you can kind of quickly add a new interface. (t: 880) I have added a search interface on that MCP. And now the MCP message coming back also includes yet another tool on there, essentially, that says you can search and here's what you need to send to us. (t: 890) So let's see if we can say, do you know? If there's other Jurassic movies and you can see it ran search Jurassic, that might be tiny. (t: 900) I apologize, but essentially it figured out in chat that it needed to call this tool. It called this tool. And as you can tell, this is once again, intelligence doing all of this work for us. (t: 910) And so as tools are added, that just enhances the same experience without us having to release this middle tier piece of software. (t: 920) Okay. To me, that's fascinating. Okay. For the eagle eyed viewers out there. I wanted to put in one technical note just so that you understand what we're doing here in this video. (t: 930) Basically, we built an API, then we built an MCP and a UI that could use both the API and the MCP. (t: 940) However, the UI right now is using the MCP to discover all of the tools, but it is also turning around and calling the API. (t: 950) I just wanted to say that if you, if you'd seen that up to this point, you were completely right. And this was just for educational purposes. So that you could understand the difference between them. Sorry. All right, let's finish up. (t: 960) Okay. So this one, I, I know may have been a little, very difficult for me to tell now, if that contextually made any sense to you, I hope it did. (t: 970) So, all right. To me, that's really the magic of MCP is that it's both a way for intelligence to understand how to call services that already exist, as well as automatically being able to enhance. (t: 980) We won't need to release anything in the wild anymore. These things. That go out with intelligence baked in will automatically start getting more and more and more superpowers. (t: 990) I don't know that that's always a good thing, but it is the case. All right. I plan on doing more of these today. We build kind of things. They're a little nerdy, but I really enjoy building. (t: 1000) So it's kind of a fun thing to be able to show off. And I hope I described things well enough that you might be interested in seeing more of these as well. Please subscribe. (t: 1010) It tremendously helps the channel. A lot of you have recently subscribed and I really want to say heartfelt. Thank you. I've enjoyed the comments as well. That's been a lot of fun. A lot of extra time for me to spend in there. (t: 1020) But I really, really would rather hear from you guys and interact in the community than anything else. So thanks again. Thanks for coming along for the ride on this one. (t: 1030) And I'll see you in the next one.

