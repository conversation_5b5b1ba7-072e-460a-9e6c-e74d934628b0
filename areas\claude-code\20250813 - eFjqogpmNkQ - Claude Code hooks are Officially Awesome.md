---
title: <PERSON> hooks are Officially Awesome
artist: Web Dev Cody
date: 2025-08-13
url: https://www.youtube.com/watch?v=eFjqogpmNkQ
---

(t: 0) Before I started playing around with Cloud Code, I used a lot of Cursor. And the great thing about Cursor is when the agent is done running, it'll play like a ding notification so that you can come back and like reprompt it or continue off where you left off. (t: 10) But Cloud doesn't really have that capability, but they do have a way to add in hooks, which I'm going to show you how to play a custom message when the prompting or the agent is done. (t: 20) Let me just dim this out real quick. I'm going to say please change the title in the header to say <PERSON><PERSON><PERSON> in the header. Let's find this header thing. (t: 30) I'm going to kick off this agent. And eventually it'll finish and it's going to play a custom sound that I set up. <PERSON><PERSON>'s all done, <PERSON> vibing in the W's, fam. (t: 40) Time to skibidi bop out of here like a goofy NPC. So as you can tell, we're making some major breakthroughs in software engineering. Instead of just playing ding noises, we can actually generate a message using ChatGPT. (t: 50) We can prompt it to have it be in a particular type of vernacular. For example, I told it to do Gen Z skibidi toilet type of talk. And then I pass that to 11 Labs, which is going to use one of their voice models to generate the voice. (t: 60) It's going to write that voice to disk and then it plays it and then it cleans it up. Pretty cool. I'm not going to walk you through this code because I didn't write it. (t: 70) ChatGPT5 wrote it for me. But I will tell you how to set up the cursor hook. So in the .clod folder, there is a settings.local file. And inside of here, you can start defining custom hooks. (t: 80) So as long as you have an object here and inside of that, you have a property called hooks, you can define what you want to run when certain things finish. So right here, this would be a custom hook. It would be like when an agent stops. (t: 90) You can then match it to have it run a particular hook. And in my place, I'm saying play this command, run an hsg script on play agent done voice. And then that's going to open up this script down here, (t: 100) which loads in a secret environment variable where I have the open AI API key and I have the 11 Labs API key. And then we send a message to open AI saying, (t: 110) Hey, write the most extreme brain rot Gen Z internet slang thing. Skibidi, goofy, ah, sigma, res, NPC, TikTok, meme speak. Make it un- hinged, absurd and full of viral nonsense. (t: 120) Keep it one or two wild sentences. So I want to know when my agent is done. And I figured this is probably the best way to get my attention because sometimes even the ding notification just isn't enough. (t: 130) So we make some API calls. Okay, we get some texts back. And then finally, we make another post request to 11 Labs, which has, you know, a decent amount of free credits every month. (t: 140) Well, we basically pass in that voice ID. We pass in the JSON body of what we want the message to say. And that's going to give us back. So we're going to go back to the (t: 150) And then we're going to go back to the temp file. Now the temp file, we basically just use AF play to run it if we do have AF play, which will basically play the audio and then eventually it'll clean it up. So that's kind of how it works. Now in 11 Labs, you basically just have to go and find a voice that you like. (t: 160) There's a cool German one that I found. I might even try this one. Let's click it. All is something, but none of us. All right, so let's go to my voices and then you can actually click and copy the voice ID, which is over here. (t: 170) We're going to go back and I'm going to update my secret file. I'm going to cut that out of the video so you guys don't see it. And then let's go ahead and print it. And then prompt our agent to change one more thing. (t: 180) I'm going to say, you know what? Revert that change. And while it's thinking, let's go back to the hooks reference on Clogged. Task squad hit the finish line like a skibidi bop. (t: 190) We vibing on that complete drip. Wow, I can really feel the brain rot on that one. Let's just go and look at the hooks reference. All right, so going through here, basically you have different event names you can use. (t: 200) You have the matcher, which I'll explain. And then you have different types of commands you could run when the matcher is done or the. The actual hook is done. (t: 210) And then on the right, they have all the hook events. So you'll see there's pre tool use. So that's like before you kick off a tool post tool use. Obviously it's afterwards. Notification. I'm not really sure what that is. (t: 220) Runs when Clogged code sends a notification. Notifications are sent when Clogged code needs your permission to use a tool. So you could change the sound or the command that runs if you were to get this type of (t: 230) notification. The one I'm using, like I said, is stop. So runs when the main Clogged code agent finishes and has finished responding. Do not run. If the stoppage occurred. During end user interrupt. (t: 240) Which is nice if you just command C, it won't actually play this annoying sound. But as you can tell, there's other hooks that you can kind of listen into. Next up is the matcher. So you can actually put like a regex in here if you want to check for certain tools that (t: 250) are running. Now, I'll be honest, I'm not sure where you can find the list of tools. Here's an example of the pre tool use. (t: 260) So runs after Clogged creates a tool parameter and before processing the tool call. So you have task, bash, glob, grep, read. Okay. So these tools are like when the agent needs to reach out and do a grep on your code base. (t: 270) You can actually in the matcher, you can put a check for grep or put a check for whatever. And I do believe when you're prompting, like if I were to say list all files related to (t: 280) authentication, I think this will actually print out the tools in a different color. I think over here, these green dots, this is a search tool and it looks like it's just (t: 290) running the bunch of search tools over the code base. Ayo, all the vibes are checked off. We just hit the completion flex. And the grind is officially snoozed. (t: 300) We outta here fam. I kind of like this. I don't know. It's probably gonna be super annoying for my viewers, but I kind of like it. I'll be honest. I've only spent like five or 10 minutes playing around with this and I just wanted to record a video about it because I thought it was pretty cool. (t: 310) And I wanted to hook up notifications when my agents were done. So that's how you kind of do it. If you have messed around more with these hooks and the matchers and the different events, let me know if there's something cool or interesting you have set up with Clogged code. (t: 320) Maybe I can add it into my workflow and just make it a little bit more of an enjoyable experience. All right. Let's get to the recording.

