---
title: All the AWESOME NEW Claude Code Features
artist: <PERSON>
date: 2025-08-09
url: https://www.youtube.com/watch?v=o_qZa4ifDdQ
---

(t: 0) Alright, so I'll be going over some of the Cloud Code features that came out in the last two weeks, and I'll be going in reverse order from the latest feature to oldest feature. There will be timestamps and a description down below, so you can skip ahead. Basically, getting started, we have customizable status lines over here. (t: 10) So if you run Cloud Code in whatever folder you're running it in, and you can write slash status line, you can say something like, add the weather in London with emojis, (t: 20) press enter over here, and then it will fire off a built-in sub-agent in Cloud Code to add this to a status line. And that basically means that just below this input box over here, (t: 30) it will show whatever information there is. So you can see, propose this edit over here. I can press yes. And then if I exit Cloud Code, and then run Cloud Code again, (t: 40) and now you can see over here, it says London, partly cloudy, plus 17 degrees. I can add more information to it. So I can say, add how long I've been in the session, the model being used, the git branch, the folder name, alongside the weather, the weather should be last. (t: 50) Press enter, and then it will make those changes as well. And if I press exit over here to close Cloud Code, and then I run it again, then I should be able to see all these new pieces of information. (t: 60) So you can see it says zero minutes, Opus 4.1, main, vibe, and then partly cloudy, like this temperature over here. And you can actually see what's happening behind the scenes (t: 70) by going to your home directory over here, pressing command shift dot on macOS, and you can see the hidden folders, go to dot cloud. And then you can see a dot settings JSON over here, (t: 80) which calls the bash command for this like bash script. And then you can open up the bash script, press open with, and you can see all this information that cloud has over here. (t: 90) And you can see where it's getting the weather from, which is using this API endpoint over here. And basically you can get cloud to add other things for you. So if you have an API endpoint, you can get it to fetch your emails, for example, like how many unread emails you have. (t: 100) You can get it to fetch like how many dollars you've made so far today on Stripe. And you can just like import like other useful things that may be helpful when you're running a coding session, such as how many tokens you've used so far, (t: 110) or like how many files that you've edited so far, or how many lines that you've changed so far. Anyways, the next thing that you can do is you can run background commands. So you can basically have cloud running and monitoring things in the background. (t: 120) So for example, I can say, can you list the docker containers I have running and follow the logs of random container with the dash F. And then it will ask if I want to run this command, (t: 130) I can press enter over here. And now you can see over here, it says one bash running. So this is running in the background and it's monitoring the output of this docker container. And I can also say CD into next JS folder and run NPM run dev over (t: 140) here. And then it will start running this as, like background command too. So press yes over here. (t: 150) And now you can see I have two bashes running. And if I write slash bashes over here, I can see the list of bashes, which has a docker logs over here, the CD over here, I can press enter to view the like what's happening, (t: 160) or I can press K to then kill the like background command. So if I press enter over here, it seems that there's no output available, (t: 170) or I can press K to then kill the like command. And this can be useful because you may have a development server running locally and you want to run it locally. So you can do that. And you want to run it locally. And you want to run it locally. And you want cloud to be able to like see any error logs that end up happening by running its tool bash output. (t: 180) So you don't have to always like copy over there, like error logs every single time. For example, the next thing that you can do is you can use at mentions in slash command arguments over here. (t: 190) So for example, if you make a slash command, which is for example, optimize, I just made this one over here and this is like a project level slash command. I can do at, and then I can like add a specific folder or file, (t: 200) for example. So I can add this like organization manager.tsx over here. Press enter. And then it will only consider this particular file when this slash command (t: 210) is running. They also upgraded the Opus model from four to 4.1. They also made it possible to specify which sub agent is running, which model. And you can watch my video on sub agents that I previously made using link (t: 220) in description down below. So if you were to write slash agents over here, then run a create new agent, for example, and I can describe an agent to be a I18 translator. (t: 230) Keep the I18 files in sync, replace the hardcoded strings with the I18 like values, key pairs, press enter over here. And then when, when you're done, when you're done, when you're done making this agent, I can choose whether it's running a Sonnet, Opus or Haiku. (t: 240) And for this particular agent, Haiku makes more sense. Like Opus is overkill and so is Sonnet. So I can press Haiku over here. And then if I open the agent file, I can see it says model Haiku over here. Something else I also did is adding hidden files and folders to file search (t: 250) when you use an at to mention. So if I write at over here, then I can see .ds store, which is a hidden file on my class, .cloud, .env and so forth. (t: 260) They also made it possible to add specific agents to invoke them. So I can do at and then run I18n. And I'll see the same thing. This I18n agent that I just made. And then I can run that particular agent. (t: 270) They also changed one of the commands for transcript mode. So if you don't know already, if you press control R when a agent is running, then you can see everything that's actually going to cloud. (t: 280) So you can see this bit of code is like going over to cloud and also all these other files as well. And this prompt over here. If you press exit now, rather than interrupting the agent, it just leaves transcript mode. (t: 290) Another small improvement that I found useful is on macOS. You can do control V images. So if you run, command shift four on macOS, you can take a screenshot like this, (t: 300) and then you can press command V into cloud code over here. And then the image easily goes there. So previously on macOS, you would just have to drag and drop images into the terminal when cloud code is running. (t: 310) Now you can just paste them. They also added support for reading PDF files. So you can drag and drop a PDF file into cloud code and then just say, (t: 320) summarize this, for example, and then it will let you know everything that's on the PDF file. And that can be pretty useful for some people, because you may be given a feature spec in like a PDF file or something from your products manager, (t: 330) and you can just put that into cloud code. And one more thing they added is allowing you to specify which model is used with which slash command. So for example, if you go to your commands folder over here, I have an optimized command. (t: 340) I can change this to use Opus, for example. So at the very top, if I write this over here, (t: 346) I can write model, and then I can just change this to Opus, (t: 350) for example. So for some commands, you may want to use more powerful models. Other commands is kind of overkill, and you don't want to eat up all your context or use too many tokens, and you can use simple commands instead. (t: 360) And yeah, that was basically everything that was added to cloud code in the last two to three weeks. If you have found this video useful, then do subscribe because I will be making another one like this two to three weeks from now.

