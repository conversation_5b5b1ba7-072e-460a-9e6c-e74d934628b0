---
title: "3 Tools That Unlock Claude Code's True Potential"
artist: <PERSON>
date: 2025-07-24
url: https://www.youtube.com/watch?v=2bGh_DlkubM
---

(t: 0) If you're using Cloud Code without these three upgrades, you're leaving a lot of its power on the table. I'm about to show you how to transform Cloud Code from a great AI assistant into an unstoppable development machine that can scrape any documentation from any website, visualize (t: 10) user interfaces in real time in your browser, and automate any browser interaction or workflow that (t: 20) you want. By the end of this video, your Cloud Code setup will be so good, you'll wonder how you coded without it. So let's dive in. So what are these upgrades? I'm talking about MCPs, (t: 30) MCP servers to be exact. If you've been following the AI coding space lately, you've probably heard of MCP, which stands for Model Context Protocol. And if you haven't, that's totally fine. I like a recent explanation that <PERSON> gave in one of his videos. Basically, what MCP means doesn't (t: 40) really matter. It's just a tool you give an agent. And that's exactly what we're doing here. We're giving Cloud Code, our agent, access to three incredible tools to make everything you do in (t: 50) Cloud Code faster and easier. And you can do that by using the MCP. So let's dive in. So what are these tools? Context 7, Firecrawl, and Playwright. Now I'll go over what each of (t: 60) these tools does, show you how to set it up in Cloud Code, and give you a real world example of each of them in action. Let's go. Let's start with Context 7. Context 7 is an open source (t: 70) database of LLM friendly documentation libraries for thousands and thousands of popular APIs, third party integrations, etc. LLM friendly just means our agent can read it and use it well. (t: 80) This is way better and way more efficient. than trying to Google documentation for your project and then copying and pasting it into Cloud Code or whatever code editor you're using. So you can go to context7.com and actually go (t: 90) through their documentation this way. And you can look at this. They have 25,700 different libraries (t: 100) in here. They've got Next.js, React, Tailwind, Supabase, MongoDB, FastAPI, and you can search for thousands of other APIs and third party integrations here. But the best part is you don't (t: 110) have to get the documentation this way. They all have the documentation. So you can go to their GitHub repo, which contains directions on how to integrate this into any code editor or code (t: 120) assistant that you're using. We'll be using Cloud Code today and integrating Context 7 into that. And this is why I'm talking about Context 7 first, because this one saves me so much time. (t: 130) So to install Context 7 in Cloud Code, just open Cursor or whatever code editor you're using, start a new terminal, and copy and paste this command. (t: 141) And then you'll see the URL right here. And I'll put this down in the show notes. You don't need to (t: 150) pause the video, but you can, and copy this over. I've already installed this. So once you do that, open another terminal window, launch Cloud, and type slash MCP to see what you have open. You can (t: 160) see I've already got all the three MCP servers that we're talking about in this video installed in Cloud. So what can we use Context 7 for? I have a real code base here that I'm working on. This is (t: 170) an app called Podcast that's in development right now. And I'm going to go ahead and open it. And I'm going to go ahead and open it. And I'm going to go ahead and open it. And I'm going to go ahead and open it. And I'm going to go ahead and open it. And I'm going to go ahead and open it. And I'm working on as a side project. It leverages a bunch of third-party APIs, and it's sometimes hard to (t: 180) keep track of what I'm using. So in order to get all the relevant documentation that I keep having to reuse and have Cloud Code access, I'm going to create a separate folder here called Documentation. (t: 190) You can see I've opened that right here. And then I'm going to copy and paste this prompt that says analyze my code base for any third-party APIs that are required or recommended. And this includes (t: 200) things that are listed in the planning.md and task.css. And I'm going to copy and paste this in the planning.md and task.css. And I'm going to copy and paste this in the planning.md and task.css. If you're not familiar with that framework, check out my other video on this awesome framework to use with planning and tasks in Cloud Code. Now, make a plan to use the context7 MCP to search (t: 210) relevant documentation needed for each of these APIs. Then plan to add relevant documentation (t: 220) to a .md file in the documentation folder, the one we just talked about, of my project directory. And I give an example here of Polar, which is what I'm going to be using in my app for payment (t: 230) processing. Now, for this, it would be helpful to turn planning.md and task.css into a .md file. which you can do by entering shift-tab-tab. So it's scanning my code base right now and (t: 240) analyzing what third-party APIs I'm using. Once it gets that information together, it's going to start coming up with a plan to query context7 with the MCP server that we just connected. (t: 250) And then once it creates this plan, we will execute it and see what the context7 documentation looks like inside our folder. And this will be really helpful so that we don't have to keep (t: 260) pulling them every single time we need Cloud Code to do that. to interact with these code bases. All right, it's starting to pull everything together right now. And you can see there's quite a few queries that it has to do. Now, this is going to take Cloud Code (t: 270) some time, so I might fast forward to show you what the end result is. But the point here is that context7, by leveraging this MCP, we're going to give Cloud Code access to the documentation it (t: 280) needs. You don't have to build the database locally. I'm just showing you the power of what context7 can do for your project. Okay, so after a few minutes, this is finished. And as you can see (t: 290) in the documentation folder, it's going to be able to do a lot of things. So I'm going to go ahead and start doing that. And I'm going to go ahead and start doing that. And I'm going to go ahead and start doing that. And I'm going to go ahead and start doing that. And I'm going to go ahead that we created here. It's created separate MD files for each of the third party APIs that (t: 300) we're using in this code base. And it's got detailed instructions that Cloud Code can then reference to build out these components in your code. Now, as I mentioned, this is sort of an (t: 310) advanced and different use case for context7. The more typical use case that I mentioned you could use is just say, implement clerk authorization on the landing page. (t: 320) use context seven to make sure we are using it correctly. (t: 330) And that's it. But if you find yourself using the third-party documentation a lot and having to use context seven over and over again for the same components, it helps to build these out in a separate documentation file. (t: 340) And again, Cloud Code did that fairly simply. It took about three minutes and it was done. Okay, context seven is really helpful, but while it has many components, APIs, et cetera, (t: 350) in its database, it doesn't have everything. So what happens when you need to use a third-party API or a package that isn't listed in context seven? How do we ensure Cloud Code has the right context information (t: 360) so that it can effectively and accurately work with a new API? That's where Firecrawl comes in. Firecrawl was one of the first dev tools in the AI scraping space, (t: 370) and it has really exploded in popularity over the last year. You may have heard of it, and if you haven't, Firecrawl can basically scrape any website, even if there's capture protection, even if the actual data you're trying to scrape (t: 380) is not actually loaded into HTML. It's super versatile, and it can scrape pretty much anything. So you can find Firecrawl at firecrawl.dev here. (t: 390) You can just sign up for an account. There's a pretty generous free tier. You can see you get 500 credits, which gets you quite a few scrapes for free. And they also have an MCP server, (t: 400) and they have instructions here on how to integrate it. I'm going to show you how to install it in Cloud Code in just a second. Some of you may be watching this and saying, hey, wait a second, I thought Cloud Code has web search built in natively. (t: 410) Can it pull data? And the answer is yes. But Firecrawl is just better, and it actually has the ability to scrape entire website trees. So think parent page plus all the subpages. (t: 420) Cloud Code doesn't do this correctly with web search, and that's why Firecrawl is amazing. We can use it to pull entire documentation sets for the specific things we need Cloud Code to work with. (t: 430) So we don't have to run web search every time, and we can have documentation that we can't get with Context 7. So right after you sign up for an account with Firecrawl, there's a button to generate API keys. (t: 440) You're going to need to generate one of those to add Firecrawl MCP to Cloud Code or whatever code editor you're using. Once you copy that API key, open up a new terminal window in your code editor and paste this command. (t: 450) Now, where I have these brackets, insert key here, copy and paste your API key that you just pulled from Firecrawl, remove those brackets, and press enter. (t: 460) I've already done this. So when you go over into Cloud and type slash MCP, you'll see Firecrawl is running. So real world example, I'm running into a problem building my podcast app (t: 470) because pretty much every single third-party podcast information, database is not in Context 7. You can see from the website here, this is a sample API that I'm considering using for third-party podcast data. (t: 480) It's just a single web page with a bunch of nested pages of documentation. If I ask Cloud Code to go to this page on a web search and pull information, (t: 490) it's not going to do a very good job of doing it. So let's use Firecrawl to pull what we need. So in Cloud Code, I'm going to enter this prompt right here. (t: 500) To use Firecrawl MCP to scrape, so let's go ahead and run this. And it's going to do kind of what we did with Context 7 earlier. It's going to add documentation to our documentation folder (t: 510) that Cloud Code can then interact with without making any more MCP or web search calls. Firecrawl is going to actually go through each and every one of those subdomains and build that context into an MD or text file (t: 520) that we can put in the documentation folder. And perfect. It didn't make an MD file, but it made an LLM.txt file with all the information we need on the Taddy API. (t: 530) And Cloud Code can, and I'll reference this in the future, making it super easy to build in these lesser known APIs and packages into our code. So that brings us to Playwright. (t: 540) Now, what is Playwright? It's a browser automation tool. Basically, anything you can do in a browser in terms of clicking, searching, navigating, it can do automatically. (t: 550) One of the biggest pain points of coding is running your app in development mode, opening it up locally in your browser, and finding that it doesn't work or doesn't match your expectations. Then you either have to copy and paste the error you're getting in the browser, (t: 560) or you have to copy and paste the error you're getting in the browser, or you screenshot what looks wrong and you go back to the code editor. This takes forever, but Playwright automates this entire process. So this is the Playwright homepage right here. (t: 570) But what I want to show you is the Playwright MCP, which is hosted at this GitHub repo. I'm going to show you in just a second how to install this in Cloud Code. (t: 580) Okay. So once we open up Cursor, or your code editor, we'll open up a new terminal and paste this command in. And again, I'll put all this in the show notes and press enter. (t: 590) I've already done this. So if I go and open the Cloud and type in MCP, you'll see Playwright's running. Okay. So in this example, I have a user interface that doesn't quite look right. (t: 600) Now, if you take a look at this, this is supposed to be a task manager or to do list. But the UI just didn't quite look right. This is something I actually coded in a previous tutorial on Grok 4. (t: 610) But I want it to look like this screenshot right here. So the thing with Playwright is instead of me having to manually open the browser, take screenshots (t: 620) of what the output looks like of the app, I can just implement this following prompt. Implement the UI shown in the attached image by using Playwright MCP (t: 630) to screenshot the app until it looks like the image. So I just copy this and paste it by saying control V and now press enter. (t: 640) And you may have to ask it to auto accept activity from Playwright as it will just keep prompting you over and over again. And as you can see, it opened a Playwright browser here (t: 650) to take a screenshot. So now that it has a screenshot from Playwright, it can compare and contrast between the reference image and the actual image until it gets it right. (t: 660) So this could take a few seconds. So I'm going to fast forward a little bit until we get to our result. OK, so after a few iterations, Claude code finally finished, so let's try Playwright one more time (t: 670) and see if we match the UI reference here in the image. All right, and awesome, this matched the image (t: 680) exactly and it took it three or four tries, but it finally did it. This is totally awesome. I didn't have to be involved at all. I just auto accepted every time it was (t: 690) opening a Playwright window and it was done within less than three or four minutes. OK, Playwright can also check the browser logs or console logs in your browser. So you don't have to copy and paste any (t: 700) of that between your browser and your code editor. So all you have to do is say use Playwright MCP to open the app and check (t: 710) browser console logs for errors. And look, it shows the browser console has no errors, so it went and checked that for us so we didn't have to do anything, so this is totally cool. (t: 720) It automates so much of the error and debugging process that you have to go through when building an app. And there you have it. Context seven, Firecrawl and Playwright. (t: 730) These three MCPs completely upgrade how you use Claude code. They've definitely saved me a ton of time in my coding workflows, and I hope they do the same for you. If this helped you level up your Claude (t: 740) code game, let me know in the comments. Thanks for watching and I'll see you next time.

