---
title: <PERSON> is INSANE... Upgrade Your <PERSON> Code Workflow
artist: AI LABS
date: 2025-07-11
url: https://www.youtube.com/watch?v=6Rg5M69bMgQ
---

(t: 0) I really love <PERSON><PERSON><PERSON>. The coding agent that Anthrop<PERSON> has built gives you so much customization that it's crazy, but I keep finding new custom ways to make it even better. Today, I want to show you two free tools that supercharge ClaudeCode and may change how you use it. Both (t: 10) tools are completely free and I think you're going to love what they can do. Let's jump right in. You know that ClaudeCode gives you an extreme level of customization. It has these custom (t: 20) commands and you can even modify the claude.md file to turn it into an agent specialized for your specific task. You can create workflows, but these workflows need to be tested and take a lot (t: 30) of time to create by yourself, especially if you're a vibe coder who doesn't know the full development process. In that case, you won't be able to make them very well. This is where the (t: 40) first tool I want to show you comes in. It's a configuration framework, as the author calls it, and the funny thing is, it's even called super Claude. Basically, it allows Claude<PERSON><PERSON> to gain (t: 50) a new set of abilities and skills that take it to the next level, and it'll be clear what I mean in just a minute. Now, what does it give you? It provides 18 structured commands, and they're all focused (t: 60) on one thing. Then there are flags. When used with the commands, the flags give you additional functionality. For example, it has this persona flag, and you can choose between nine of them. (t: 70) They all represent a specific part of the development lifecycle. That in itself is an amazing feature. Like if you wanted to build you a landing page or a UI component, you can implement (t: 80) the front-end persona for that. And there are personas for literally everything, for architecture for back-end, for security, and for analyzing different problems. It's really crazy. (t: 90) Now, how does this work? I opened it up and took a look. On the back-end, the developer has written configuration files in the form of custom commands and even a set of rules and configured workflows (t: 100) in the form of YAML files. So in a way, it's all just prompting, but that's how you program AI (t: 110) agents and models. For example, in the front-end persona, it gives Claude a really robust workflow that's super beneficial for creating UI. And the workflows that the author has created are very, very, very powerful. So, if you want to use it for your work, you can use it for a lot of things. And if you want to use it for your work, you can use it for a lot of things. And if you want to use it for your work, you can use it for a lot of things. And if you want to use it for your work, you can use it for a lot of things. (t: 120) Give Claude code the context on how real developers go through the development cycle. So it has context for the whole development cycle, and it's all written out. You just need to learn how to use (t: 130) the relevant tags at the relevant time. And not only that, it actually takes advantage of MCPs as well. You can see we have the context seven MCP, which gets external documentation, the sequential (t: 140) MCP, which basically allows multi-step reasoning, and then the magic UI MCP and puppeteer MCP as well, which are really powerful. And then the magic UI MCP and puppeteer MCP as well, which are really great. Also, if I open the Claude.md file, the whole configuration has been set up on how to actually use (t: 150) everything. So this is why it's called Super Claude. To give you an example of how the slash commands (t: 160) and the flags are used, if I scroll down, you can see that we have workflows like the slash build command followed by flags like react, which means to use react, magic, which means to use the magic (t: 170) MCP, and watch, which means it will monitor development continuously. You can also see the persona has been set with the persona.md command, which means to use the persona.md command. And you can see that the flag has been set with the persona.md command. Now, all of this is super (t: 180) powerful, but I found one problem the author really should have taken care of, and that's that there's no proper way to learn to use this. When I installed it and watched other videos from (t: 190) YouTubers, they all just demoed it. I basically found no proper guide on how to actually use it. That creates a bit of a problem because there's a learning curve with this tool, given the amount of flags and tools in this. Even if you could figure it out, (t: 200) you'd still need to learn the commands, the available personas, and where to add the different CPs. But not to worry, I found a workaround for this problem. I opened this folder with the (t: 210) configuration in Cursor. I'll show you how I installed it later in the video, but right now, you just need to know that the configuration was installed in this folder. I used this command, (t: 220) and it opened up in Cursor. Now, going back to Cursor, since all the commands were there, what I did was ask Cursor to go ahead, read all the configuration files, all of them, (t: 230) and make me a comprehensive guide on how to use them with Claude, and when, and where. Those are important parts. It read through all of it, and Cursor's new search tool is really, really amazing. (t: 240) They've posted some results on how much better it is at finding stuff, and it's using that model. It found everything super easily, made the connections between files, and gave me a (t: 250) really cool configuration guide. Now, I didn't want to learn all the commands and flags by myself. Since it had generated the full documentation, I thought, why not create documentation that I can (t: 260) feed to an AI agent in Claude code or Cursor? That way, the agent can tell me the best way to use these commands for my specific use case. So, I told Cursor to put everything into (t: 270) the docs.md file, which you can see right here. It created a comprehensive file of around 450 words. Once I had this documentation ready, I gave it a task. I said I wanted to build a Next.js app (t: 280) with 11 labs API integration and the OpenAI API. The idea was to create a story generator that (t: 290) also has a narration feature. I asked Cursor what workflow I should follow based on the framework. Now, due to some limitations, I couldn't set up the Magic MCP and Puppeteer MCP right away. So, I asked Cursor not to use them (t: 300) in the flags. You still have to configure the MCPs yourself in Claude code. The framework simply tells you where they belong in each workflow. But using the right commands, Claude (t: 310) knows when to apply each tool, which is still incredibly powerful. Here's what happened next. Cursor gave me the commands, but it was missing the prompts. I realized Cursor didn't know it (t: 320) needed to include both the commands and prompts together for Claude code. So, I told it to output the slash commands, flags and prompts in a structured way, and it delivered exactly that phase (t: 330) 1, phase 2, phase 3, all the way to phase 11 for deployment. A complete project outline. Remember, (t: 340) this framework includes commands and workflows for every step of the developer cycle. And now Cursor knows all of it. The beauty is it's not limited to Cursor. You can copy the documentation feed it (t: 350) to Claude code and Claude will generate structured workflows for you. If you've seen my video on context engineering, you will soon see a cortical and a vertical task process. That's that. If you've you know these workflows are evolving quickly. People once thought non-developers couldn't build applications, (t: 360) but development knowledge can be embedded into frameworks like this and shared openly. Now anyone, even vibe coders with little industry experience, (t: 370) can follow a guided workflow written by someone who does know the process. And speaking of sharing, I'll leave this documentation that Cursor created for me (t: 380) in the description below so you can use it for yourself as well. So back to the demonstration. Phase 1 is analysis and research. I'll copy the command with its flags and switch to my terminal. (t: 390) When I open the slash menu in Cloud, you'll see all the extra commands from this framework listed above Cloud's original commands. Those start with add directory. (t: 400) I'll paste the command and its flags here, then copy the accompanying prompt. You can customize that prompt if Cursor added extra wording. It's all just context for the model, and it will output structured commands automatically. (t: 410) No need to memorize everything. Learning it all would take way too much time. When I hit enter, you'll see Cloud start to run. If I scroll up, you can watch the workflow kick in. (t: 420) Cloud recognizes this as a research task, knows which tools to use, like context 7 for external documentation, then creates to-dos. It's using sequential MCP, asks my permission to call the tool, and proceeds. (t: 430) It's all just a workflow, nothing fundamentally new. But these workflows are packaged for you by the framework's author. As I mentioned earlier, you don't need to learn every detail. (t: 440) Just create the documentation, and you're good to go. So let's get started. Let's get started. You can see the entire development cycle spelled out right here, and you can do plenty of creative things with it. Again, I'll include the complete documentation in the description, (t: 450) so you can skip the setup process and start using these workflows immediately. Oh, and if you're enjoying the content we're making, (t: 460) I'd really appreciate it if you hit that subscribe button. We're also testing out channel memberships. Launched the first tier as a test, and 93 people have joined so far. The support's been incredible, (t: 470) so we're thinking about launching additional tiers. Right now, members get priority replies to their comments. Perfect if you need feedback or have questions. Now, what if you could access Cloud Code from anywhere, like any device? (t: 480) That's where the second tool comes in. It's essentially a web-based GUI for Cloud Code. I previously covered another GUI called Claudia in an earlier video. (t: 490) The difference is that Claudia was more complex and feature-rich with a superior interface. This tool offers something entirely different, browser accessibility from any device. (t: 500) So, Cloud Code and my projects could be running on my computer, while I'm actually using Cloud Code through a browser on my mobile. (t: 510) That's incredibly powerful, and the setup is surprisingly simple. You just copy this script for the installation. Also, don't worry about the SuperCloud installation. I'll cover that at the end. (t: 520) Now, this is basically an executable app. Navigate to where you want it installed. For example, I'm in the Cloud Web UI folder, where I've placed the executable. Run the script. (t: 530) It handles everything for you. Paste it in the correct location. And you're ready to launch it. There are different startup methods. The default command launches it on localhost 8080, which only runs locally on your computer. (t: 540) But this lacks the network capabilities I'm describing. To make it accessible from anywhere on your network, use this command instead. Let me copy this, go back to terminal, and paste it. (t: 550) There's a small documentation error. The full executable name isn't shown. So use the ls command like I did to correct it. You'll see the proper executable name, and it'll launch. (t: 560) Now, how do you connect the command to your computer? To connect from other devices. It's running on the network. So to connect from different devices, you need your IP address for the current network. (t: 570) You'll need a separate command for this. You can ask chatgpt or use the one I used here. Warp provided it. And it gave me my IP address. Once you have this IP address, return to where the web UI is running. (t: 580) Copy the port number. Open your browser. This works on any device, mobile, another computer. They just need to be on the same network. I'm demonstrating on my own computer. (t: 590) I'm not using any device connected to the same network via browser. Paste it in, remove the zeros and add your IP address. And there's your web UI. (t: 600) It's quite basic. Nothing fancy. You can create new directories or select existing projects. In existing projects, you can chat with Claude Code and it'll start working. You can see it's initialized here. (t: 610) The amazing part is cross-device accessibility. I use this to run Claude Code from anywhere in the house. I just pull it up on my phone and use Claude Code directly from there. (t: 620) It's genuinely useful and I thought it was worth sharing. Beyond that, it doesn't have special features. I even tried slash commands, but those don't work. So unfortunately, the Super Claude configuration won't function here. (t: 630) Still, it's a pretty cool tool you can utilize. Now, for the installation of Super Claude installed, you need to head over to its GitHub repo where you'll find the installer. (t: 640) Simply copy these lines and paste them wherever you want the framework downloaded. This will clone the repository to your system. Once that's done, navigate into the folder and you'll see the install script. (t: 650) This is what handles everything for us. Now you've got two installation options. The first is global installation. This means every instance of Claude Code will have access to Super Claude. (t: 660) The second option is project-specific installation. You'll have to use this command instead and you'll remove this part here and replace it (t: 670) with your actual folder path, such as you can see that I have entered. Here's the key thing to remember. Whatever folder you're targeting, you need to add .claud to the end of the path, just like I've added it here. (t: 680) The configuration files need to be in the folder. If you're going with the global option, just make sure to clear out your existing .claud folder first, then run the installation script. That brings us to the end of this video. (t: 690) If you'd like to support the channel and help us keep making videos like this, you can do so by using the Super Thanks button below. As always, thank you for watching and I'll see you in the next one.

