---
title: "Claude Code Agents: Build AI Teams That ACTUALLY Work"
artist: <PERSON>
date: 2025-08-05
url: https://www.youtube.com/watch?v=Pif98jOScYc
---

(t: 0) Imagine being able to create a specialized team of AI agents in Cloud Code. Well, now you can. Cloud Code just introduced sub-agents, and these are a game-changer. (t: 10) If you've been using Cloud Code, then you know it's extremely powerful for coding tasks. But here's the thing. As your conversations get longer and more complex, the more the context becomes messy. (t: 20) You might be jumping between front-end changes, back-end changes, database changes, (t: 30) and a whole bunch of other stuff, all within the same conversation. And this is where sub-agents come in. Imagine each sub-agent as being a specialized team member that focuses on one specific task. (t: 40) And each of these agents have a very specific system prompt and their own context window. So this keeps your main conversation clean, and you actually get better results (t: 50) than you would if you were to use Cloud Code. Because each sub-agent is fine-tuned for its specific task. So to try and explain this visually, Cloud Code always gives us access to a general-purpose agent. (t: 60) This is the one you've been communicating with in Cloud Code up to now. So we can actually call this our default agent. (t: 70) And this has a very broad and general context and system prompt. So this basically means that we can ask this agent to do whatever we want, whether it's back-end changes, front-end, it doesn't matter. (t: 80) It will use the LLM to try and explain the system prompt. And this is where we can try and make those changes for you. But what if you have very specific rules with regards to which libraries we're supposed to use (t: 90) on the front-end versus the back-end? Now you have to cram all of the system rules into a single system prompt for the single LLM. Now, of course, the more stuff you provide, the more muddy the context gets, (t: 100) and the worse the results get as well. But with sub-agents, we can have a specialized and narrow context or system prompt. (t: 110) So, for example, we can have a specialized, code reviewer agent with a very specific system prompt. We could have a front-end expert that knows exactly which libraries and tools it's allowed to use. (t: 120) And then we can also have a back-end engineer, a database engineer, a payments expert, a debugging expert, and whatever our project needs. (t: 130) And within Cloud Code, we can use these agents in a couple of ways. First, we can get this general purpose agent to orchestrate the tasks (t: 140) between these different sub-agents automatically. Or we can also reference these agents directly in the conversation by using the at symbol. (t: 150) So let's look at this in action in Cloud Code. To manage your agents, run the command slash agents. Now, by default, we only have access to one agent, and that's this built-in agent called general purpose. (t: 160) So it's this guy over here. Now let's set up our very first agent. (t: 170) So I'll select create new agent and press enter. Now, we can choose to create a new agent. You can choose to create this agent at project level or personal level. A personal agent is effectively assigned to your user and is available to all of your projects. (t: 180) I'll simply create this agent at project level. In order to generate the agent, we can let Cloud do that for us, or we can configure it manually. (t: 190) I'll let Cloud create this agent. All we have to do is describe this agent. So let's say something like an expert code reviewer with extensive experience in full stack development (t: 200) using Cloud Code. We don't have to be expectant to do the entire plan, ace this legislative designs. You might be familiar with void го bättre on Cloud Code. And if we think about how much PowerPointız�� cancer is in one document, (t: 210) if we think about a single document that we could use for this document. Our agent is not an excellent matchup to this project. (t: 220) Now, the separated Entry Distribute is an excellent practice by Cellbox. (t: 230) That's by far why Cloud Code is ended in good shape. important. This is where we can tell this agent which tools it will have access to. By default, (t: 240) everything is enabled. In other words, it can read files, edit files, execute commands, and access MCP tools. We can also go to advanced options to see a more detailed list of all the (t: 250) tools that are available. Now, I don't want this code reviewer agent to make any changes. So, I'm actually going to press enter to deselect all tools and a code reviewer should only be able (t: 260) to view files. So, I'll select read only tools. Then, let's click on continue. Now, we can select (t: 270) the model that's available to this agent. We can inherit the model from the parent agent, which is the general purpose agent. And by default, this will be Sonnet. Or we can use (t: 280) Haiku, which is a super cheap and fast model for easier tasks. I'll simply select Sonnet. Then, we can select the color of this agent. And this is because (t: 290) Cloud Code will show us which agent is currently active in the conversation. So, we can use these (t: 300) colors to easily identify it. Let's select pink. All right, cool. So, we can see Cloud Code is calling this agent code reviewer. And we can see all the tools that it has access to and which (t: 310) model we selected. Then, we get the general description of this agent along with a system prompt. Now, we can press enter to accept this. Or we can press E to open all of this in the code editor. (t: 320) So, I'll simply press enter. And now, we can see our agent in this list. And if we look at the (t: 330) project files, we can see in this Cloud folder, we now have a subfolder called agents. And within here, we can see our code reviewer system prompt. So, at the top, we get the name of the agent, (t: 340) this description. And by the way, the description is really important. This will tell them, if you want to change the name of the agent, you can change the name of the agent. So, if you want (t: 350) the main agent to always use the subagent, what you can do is change this description to say (t: 360) something like always use this agent. So, I'm just going to remove that. And now, if we have a look at the system prompt, we can see exactly what Cloud came up with. So, I'll just assume everything is (t: 370) fine and close this file. Now, let's go back to the main chat by pressing escape. And now, let's give this a spin. And just to give you some context on what this project actually (t: 380) does, in my previous video, we used Cloud Code to build this budget tracking app from scratch. So, if you would like to learn how to build projects from scratch using Cloud Code, (t: 390) then definitely check out that video. So, let's say we wanted our code reviewer to review all the code related to adding transactions. We can access that agent in a couple of ways. (t: 400) First, we can simply tell the main agent to use that subagent to do the review. So, let's say, please use the code reviewer agent to review all the code related (t: 410) to adding transactions. Let's run this. And this is what I was referring to. We can now see in pink (t: 420) that the code reviewer agent was called. So, at this point, our main agent handed this task over (t: 430) to our code review agent. So, it's kind of saying, hey, the user wants you to review the transaction process. So, go and do your thing. And I'll give you a little bit of a hint here. So, if you want to complete, afterwards, our code reviewer will formulate a response and then pass that back to (t: 440) the general purpose agent. And the general purpose agent will then decide what to do next. (t: 450) The code review was completed and the analysis reveals that while your transaction management code has a solid foundation, there are several critical areas that need immediate attention. (t: 460) So, we're missing things like input validation on the API routes. Well, that's massive. We've got a performance and a performance management code. So, we've got a performance and a performance management code. So, we've got a performance and a performance management code. So, we've got anti-pattern. That's interesting. Duplicate component logic and much, much more. And then (t: 470) we also get recommendations that we can action immediately. Now, let's take this a step further (t: 480) by adding two more agents. So, let's go to agents. Then let's go to create new agent. And let's add this one on the project. Generate with Claude. And let's say an expert Next.js (t: 490) engineer with extensive experience in building Next.js applications. using the app router, server actions, and route handlers. (t: 500) So I'm being very specific with my language here. I'm trying to define an agent that's really good at doing the backend development. (t: 510) I also want this agent to have access to an MCP tool called Context7. If you're new to Context7, it's this repository that gives you up-to-date documentation on pretty much any technical framework, (t: 520) like for instance, Next.js, which is effectively our backend. So let's say, always use Context7 to retrieve up-to-date documentation (t: 530) when implementing new features. And I'll also add, focus on the backend logic and libraries after application only. (t: 540) Let's press enter. And I'm adding that last bit because we are going to create a frontend expert in a minute. And then for the tool calls, we actually want this agent to be able to do all of this. (t: 550) So let's continue. We'll use Sonnet. And for the backend, I'll just use Red. And that should be it. Now we have this Next.js backend engineer. (t: 560) And let's create one for the frontend as well. So let's select project, generate with Claude. And then let's say, an expert frontend engineer with extensive experience (t: 570) in React, Tailwind, and ShadCN. And we'll also give this agent access to Context7. (t: 580) So let's say, always use Context7 to retrieve up-to-date information on the libraries and frameworks. Before implementing new features. (t: 590) Then focus on the frontend libraries and logic only. Let's send this. And we also want to give it access to all tools. So we'll press continue. (t: 600) Let's select Sonnet. And for the color, let's go with purple. And let's press enter. And just like that, we have three specialized sub-agents. I'll press escape to go back to the conversation. (t: 610) And now we're back to this conversation with the code reviewers output. So now we can get our backend. So this talk is only yesterday. (t: 620) So all we've learned from this go toies talk talk described in the chat room, we'll be able to see from here, and we can actually use that conversation solvency stop sign sensors. (t: 630) This is the então in real life. So let's talk again at peak Lebenszeit tomorrow. leadership. Let's take the first step and look atanse. Here with the but ambition trigger, (t: 640) you can see that I'm peering through the backup that is gifting us Release sigh. Data factors were mentioned earlier. Let's make a quick stop here. We could enter the at symbol and let's select our backend engineer. (t: 650) And let's say, please address the above concerns. Now in a minute, I will show you how to get that main agent to orchestrate the tasks between (t: 660) different sub agents. But I wanted to show you how you can tag an agent yourself. So of course, we can see that our backend agent was indeed invoked and it wants to use (t: 670) the MCP server. So it is following that instruction. I'll just say yes and yes again. And now our backend engineer is working through its own to-do list and making all of these (t: 680) changes. So while this is running, I'm actually curious. What do you think about cloud code? Do you use cursor or windsurf or cloud code? (t: 690) Or do you maybe use a combination of cursor with cloud code? Let me know. And cool. So our backend engineer apparently fixed all of these issues. (t: 700) And when I switch back to the app, everything still seems to be working. But since it simply refactored the code, I doubt there's anything exciting that we'll really see on the front end. (t: 710) But let's actually improve the front end a bit. I'm going to start a new conversation and let's get the main agent to orchestrate tasks between several agents. So let's say something like, please get the front end engineer to suggest changes for (t: 720) improving the UI of our app. Then get the front end engineer to implement those changes. Then get the code reviewer to review the changes made by the front end. (t: 730) Finally, get the front end engineer to fix up any issues pointed out by the reviewer. (t: 740) Let's see what happens. Let's end this. And now we're really relying on this guy to probably delegate the tasks between our front end expert as well as our code reviewer. (t: 750) So we can see that our main agent created its own to-do list based on the instructions that we gave it. And now it's currently busy with the front end engineer. (t: 760) And of course, we can see the front end engineer is indeed busy. And by the way, if you ever wanted to see what exactly these sub-agents are busy with, you can press control and R to expand this view. (t: 770) Okay, here we go. So we can see the front end specialist now completed its changes. Another code review sub-agent is having a look at those changes. (t: 780) And now that the code review is done, the main agent handed the task over to the front end engineer, which is now busy implementing those fixes. (t: 790) And of course, we can see the front end sub-agent is now busy. And now we can see the front end sub-agent is now busy. And there we go. The front end engineer just completed its work as well. And if we swap back to the app, the changes might seem subtle, but this is actually a (t: 800) very big improvement. When we load the page, we actually have some animations. We have proper icons now. (t: 810) And we have these subtle animations when we hover over these cards. There's also a big improvement to some of these headings on these pages. And generally speaking, I think the code reviewer and our front end engineer did a very good (t: 820) job. So will you be using sub-agents in your workflows going forward? Let me know down in the comments. If you enjoyed this video, please consider hitting the like button and subscribing to (t: 830) my channel for more cloud code content. And to watch more of my content, click on the card on the screen right now. (t: 840) Otherwise, I'll see you in the next one. Bye-bye.

