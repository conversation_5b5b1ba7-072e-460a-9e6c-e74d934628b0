---
title: "<PERSON>'s Context is Now INSANE"
artist: <PERSON>
date: 2025-08-14
url: https://www.youtube.com/watch?v=x3GeLesYlls
---

(t: 0) As of yesterday, Anthropic now supports up to 1 million tokens of context on their Cloud Sonic 4 model, and I'll be talking about what this means for you and how you can make the most of it. Basically, the pricing has also gone up, so if you're using between 200,000 tokens and 1 million (t: 10) tokens, you now expect to pay $6 per million input tokens, which is double than before, and $22.50 per million output tokens, which is 50 times more expensive than before under 200,000 (t: 20) tokens, which was the previous limit. And this puts Cloud Sonic 4 on par with Gemini 2.5 Pro, (t: 30) which also has a 1 million token context window, and is quite a bit cheaper than Cloud Code or Cloud Sonic 4 over here. Basically, despite that, Cloud Sonic 4 does do better on some benchmarks, (t: 40) so there's a benchmark over here called LocoCodeDiff, Natural Long Context Benchmark, and basically the way this benchmark works over here is they get some really long coding files, (t: 50) and then they do the Git log output for the file. So you can see a file over here, Shopping List, it shows the items that were added to the files, the lines, the lines that were removed, the lines that were added, and what the model is meant to (t: 60) do, or what a different LLM is meant to do, is it's meant to take this entire input, and then come up with the final state of the file after all these changes were made. And this happens for (t: 70) about 200 files, and it's like a binary answer, either it got it perfectly right, or it got it wrong. And across 200 files, when you increase the context over here, you can see that Sonic 4 seems (t: 80) to do really well, the accuracy, or success rate is still like pretty constant for long context tasks, whereas Gemini 2.5 Pro, (t: 90) if I remove these other ones over here, whereas Gemini 2.5 Pro does pretty badly, because you can see it drops to about 20% over here, compared to Sonic 4's 66% over here. But they haven't yet done the benchmark up to 1 million tokens of context, so it'll be quite (t: 100) interesting to see how much Sonic 4 holds up until then, and if they do publish that, then I will link it down below in the comments section. But basically you can see that for very (t: 110) long context, Cloud Opus 4.1 does pretty well over here, and Sonic 4 and Sonic 4 Thinking actually do pretty well over here. So I'm going to go ahead and show you how they do, and I'll see you in the next video. Sonic 4 actually does better than Opus 4.1 for longer context tasks, and this is also true when (t: 120) you're using different programming languages as well over here, you can see some of the results, and this is linked down below. And if I compare against 4.1, Opus 4.1 over here, remove these (t: 130) areas, then basically you can see that now, whilst Opus 4.1 does start out pretty strong for very long context tasks, it quickly falls off, whereas Sonic 4 seems to hold up pretty well. And whilst the (t: 140) benchmark has still not yet been updated for the 1 million token context window, it does seem promising and will still do better than Gemini 2.5 Pro for the similar context window size. And there's another benchmark over here called Live MCP Bench, and basically what they did is they took 10 leading (t: 150) models, and they gave each model access to 70 MCP servers with 527 tools overall. And basically they (t: 160) gave it different tasks, and they saw how, like, well it completed each task, and they came up with a success rate. And Cloud Sonic 4 did the best overall, and it did it better than Cloud Opus 4 (t: 170) over here, for a better price as well. So you can see this graph over here, it does better than every other model. I'm hoping they update this as well for Cloud Sonic 4's, like, 1 million token context window, and they throw even more MCP tools at it. But the general idea is that Sonic 4 is (t: 180) really good when it comes to tool calling as well, and knowing which tools to use, and when to use them. And even though the context window is bigger, you still want to be aware of what you're actually (t: 190) putting in the context window. There's a good paper link down below called Context Drop by Chroma over here. And basically what they did is they took the Needle in a Haystack experiment much further. So the Needle in a Haystack is you basically fill an entire context window with some (t: 200) information, like it could be a story or something, and then you include one piece of information that a model is meant to, like, get out of the context window. So for example, I could (t: 210) include someone's name, a place. In this case, it says, the best piece of writing advice I got from my college classmate was to write every week. That's the Needle, and this is a Haystack around (t: 220) it. And then you ask the model a question, such as, what is the best piece of writing advice I got from my college classmate? And if it finds this Needle successfully, and, like, answers the (t: 230) question successfully, then it scores well on the Needle in a Haystack. And most new models, these days, do seem to be getting perfect scores in the Needle in a Haystack experiment. But they actually decided to take this experiment further and include distractors, which basically distract (t: 240) the model and can lead it down the wrong path. So you can see over here, the best piece of writing advice I got from my college classmate was to write every week. And there's a distractor saying, (t: 250) I think the best writing tip I received from my college professor was to write every day. So you can see that's semantically similar to a Needle in a way, but it can actually lead the model down the wrong track. And what they did is they also mixed up the distractors as well, (t: 260) so they used more confusing distractors over here. So they said, I thought the best piece of writing advice I got from my college classmate was to write (t: 270) each essay in four different styles, but not anymore. So that kind of flips the Needle around and is used as a distractor. And then they put it in different areas, and they saw how many distractors, like, impact their performance, and which distractors impact their performance the (t: 280) most. So you can see for all the models over here, as you increase the number of input tokens, if you have no distractors, then the performance is more or less the same. But as you increase the (t: 290) number of distractors, so you have one distractor, the performance actually decreases as you increase the number of tokens. And if you have four distractors, then it, like, massively drops over here. And you can also see that different distractors have different hits on the performance. (t: 300) So the last distractor that I said earlier is the most confusing, and most models do pretty bad on it because it kind of flips the Needle around. And what this means for you is that even though (t: 310) you have a 1 million token context window, you should not be filling up the entire context window just for the hell of it. You should still be careful about what you're actually putting in (t: 320) there because otherwise the model performance can be worse because you've accumulated these distractors in the context window. So that's what they did. And then they put in a new distractor for example, like in this codebase over here, it's a monorepo, and there's an exp application (t: 330) over here, and there's a Next.js application. And the Next.js application has a landing page, which is a homepage over here. And I remember previously, I loaded the entire codebase into a context window, and I said, hey, can you edit the button on the home screen? And what it did (t: 340) is it edited the button on the home screen of the mobile application instead of the, like, landing page of the Next.js application because there are two different home screens going on (t: 350) and two different buttons. And that is the next-gen application. And so, like, the distractor, the needle that I wanted was the home screen of the Next.js application, and the distractor was actually the home screen of the exp application. So, for example, (t: 360) if you have a codebase with multiple payment providers, for example, you're using Paddle, and you're also using Stripe, and you add a refund feature, for example, and you load in one of the (t: 370) docs, then the model can get pretty confused, and its performance can be worse in some ways because you've introduced distractors, things that are semantically related to each other, (t: 380) but different in other ways. And in another project that I was vibe coding recently using Cloud Code, I said to Cloud Code, can you move this model into a separate folder and make a separate file? And it copied the model over, but it didn't actually delete the model from the (t: 390) original file. So, then I had two different models, and when I asked it to edit the model to, like, add new things, and I didn't realize that there were two different models still there, (t: 400) it just randomly edited the old one, and it randomly edited the new one. And I kept wondering, like, what the hell is going on until I looked at the codebase and realized that it didn't actually remove the old model. And to actually use a 1 million token context window, right now, (t: 410) at least, you can't use it via the Cloud Code subscription. You have to use it via the Cloud Code, like, Anthropic API. So, basically, in Cloud Code, you have to first log out by doing (t: 420) slash log out, and then you have to log in again by running Cloud Code, and you have to choose Anthropic Console Account over here, API Usage Billing, and then you have to link it to your (t: 430) account over here, and then you have to write slash model sonnet, and then in brackets, a 1 million over here. And if I say hi, then you can see that responded to me over here, (t: 440) and if I go to my console for Anthropic, then you can see if I group, hi, context window, it used under 200,000 tokens or 200,000 context window right now. And if you want to implement the 1 million token context window in your application, (t: 450) then you just follow these instructions. But it is worth noting you have to use being usage tier 4 to be able to use a 1 million token context window. And usage tier 4 means that (t: 460) you need to have purchased at least $400 in credits from them. But if you still want to try out for 1 million tokens of context without spending $400 on a credit purchase, then you can use Open Router instead, because they are already in usage tier 4 or higher, I think. And basically, (t: 470) they will route your request via their own API to Anthropic's servers or API, and you still pay the same pricing. But you still pay a tiny bit more when you're buying credits on Open Router. (t: 480) Or you can use something like Klein, so you get this on the left-hand side over here, and then link it to your account, and then top up your account with credits. Or you can use (t: 490) something like Cursor instead, because I'm sure it's supporting Cursor now. And Klein did release an article yesterday about how to make the most of it. And firstly, they said stop being context stingy. You don't have to be careful or strategic about what to include in context. Just pull in (t: 500) the MCP server. And with the bigger context, you can use plan mode more effectively now, so you can load in (t: 510) your entire project context, discuss architecture decisions thoroughly, explore edge cases, (t: 520) and refine your approach before switching to act mode. And you can have longer development cycles, so you can go through way more iterations before you have to compact the conversation or summarize it. Someone on Twitter did do some vibe checks on Cloudforce on it with 1 million token context, (t: 530) and compared it to JPEG. And they said, please find interesting and insightful connections in all these papers. (t: 540) And then they go through them over here and compare the responses. And they say that Gemini 2.5 Pro is a beast. It provided a very detailed and comprehensive response. But Sonic 4 prefers (t: 550) to output more concise responses, which is useful in the context of AI agents. And apparently for this guy, Sonic 4 did highlight a lot of gems from the papers provided. And I will link this down below so you can look at the whole like video. And Evry also did a vibe check as well of the 1 (t: 560) million token context. And they said, please find interesting and insightful connections in all these papers. And they basically did three different tests. They say the 1 million token context window is basically the length of all the Harry Potter books combined. And for the first (t: 570) test, what they did is they hid two movie scenes in 1 million tokens of context and asked Claw to find those scenes and do a detailed analysis of them in one shot. And you can see over here, (t: 580) they compared it against Gemini 2.5 Flash, which also has a 1 million token context window and Pro as well. And Sonic 4 was the fastest out of the three. Gemini incorrectly identified the title (t: 590) of the movie as another movie over here. And they said, please find interesting and insightful connections in one shot. And I think this is an interesting thing to do. And I think it's interesting to do. And I think it's a good idea to do that. And I think it's a good idea to do that. Sonic 4 never hallucinated it, and it just declined to give it a certain title. And basically, this is an analysis that I gave. And you can see that Claw gave a much like brief analysis, (t: 600) which it has a habit of doing. It's much more concise. So if you do want high quality detail analyses, then Gemini is a better bet, which we also saw in this previous tweet over here. And then they tested their ability to analyze code. So they put in the entire like content (t: 610) management system that they have for their website, which is 250,000 tokens of Ruby on Rails code. And they also put in 700,000 tokens of padding code, which is just, I guess, like (t: 620) random related code. And it seems that Sonic was faster by about three seconds, but it did score lower on their own vibe check. And then they got Claw to play AI diplomacy, which is their own (t: 630) variation of the strategy game diplomacy. And they say that Claw did surprisingly well at this. With aggressive prompts, Claw and Sonic 4 came in second only behind Overy. And it was also really (t: 640) fast, completing games faster than Gemini 2.5 Flash. And you can see over here, it took two minutes. And on aggressive, it took 1.7 minutes instead. And basically, their own verdict is that (t: 650) Claw and Sonic 4 makes a very good use of its longer context. And they also said that Claw and Sonic 4 has a long context window, if you need a model that's fast and reliably free of hallucinations for long context tasks. And of course, we did mention earlier in the video, that's more expensive than Gemini, so you have to bear that in mind. Now, once it's released as (t: 660) part of the cloud code subscription, which I hope is soon, then I will be trying out with my own application, Tenza AI, which is an AI news application, to stay up to date with the latest (t: 670) AI news. And basically, right now, the code base is 363,000 tokens. So this should fit quite comfortably into a 1 million token context window. And it should mean that I'm able to code for quite (t: 680) a long time. And after doing that, I'm going to be able to code for quite a long time. And I'm going some more testing myself, I will have my own vibe check ready, and I will have some more best practices on how to use it. So if you do want to see that video, then do subscribe for that.

