#!/usr/bin/env python3
"""
YouTube URL Transcript Manager
==============================

This script allows users to browse for a markdown file, select a section containing
YouTube URLs, and automatically creates transcripts for those videos. It checks for
existing transcripts and adds markdown links after each URL.

Features:
- Browse and select markdown files
- Choose sections containing YouTube URLs
- Extract YouTube video IDs from URLs
- Check for existing transcripts in designated folder
- Create new transcripts using yt-dlp and Whisper
- Add markdown links to transcript files after each URL
- Progress tracking and error handling

Requirements:
- openai-whisper: pip install openai-whisper
- yt-dlp: pip install yt-dlp
- tkinter (usually comes with Python)

Author: Generated for YouTube Transcript Manager
"""

import os
import sys
import logging
import json
import subprocess
import re
import signal
import time
from datetime import datetime
from pathlib import Path
from typing import Optional, List, Dict, Tuple
import tkinter as tk
from tkinter import filedialog, messagebox

try:
    import whisper
except ImportError as e:
    print(f"Missing required package: {e}")
    print("Please install required packages:")
    print("pip install openai-whisper yt-dlp")
    sys.exit(1)

try:
    from mutagen.mp4 import MP4
    MUTAGEN_AVAILABLE = True
except ImportError:
    MUTAGEN_AVAILABLE = False

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[logging.StreamHandler()]
)

# Configuration
AUDIO_FOLDER_PATH = r"D:\Scrapbook\202506150844 Audio-Only"
SUPPORTED_AUDIO_EXTENSIONS = {'.m4a', '.mp3', '.wav', '.aac', '.ogg', '.flac', '.wma'}

# Transcription settings (matching youtube_transcript_manager.py)
CHUNK_SECONDS = 30
WHISPER_MODEL = "large"  # Use large model for better accuracy
PADDING_SECONDS = 0.0  # Padding for each chunk (set to 0 for new format)
TIMESTAMP_INTERVAL = 10  # Default interval for timestamp markers in seconds


def sanitize_filename(filename: str) -> str:
    """Sanitize filename for filesystem compatibility."""
    invalid_chars = '<>:"/\\|?*'
    for char in invalid_chars:
        filename = filename.replace(char, '_')
    return filename[:200].strip()


def safe_yaml_value(value: str) -> str:
    """Safely format a value for YAML front matter by quoting if necessary."""
    if not value:
        return '""'
    
    # List of patterns that require quoting
    quote_blacklist = [
        ': ',  # Colon followed by space (YAML key indicator)
        '- ',  # Dash followed by space (YAML list indicator)
        '[',   # Square bracket (YAML list indicator)
        '{',   # Curly brace (YAML dict indicator)
        '#',   # Hash (YAML comment indicator)
        '|',   # Pipe (YAML literal block indicator)
        '>',   # Greater than (YAML folded block indicator)
        '@',   # At symbol (can be problematic)
        '`',   # Backtick (can be problematic)
    ]
    
    # Check if value starts with any problematic pattern
    needs_quoting = False
    for pattern in quote_blacklist:
        if pattern in value:
            needs_quoting = True
            break
    
    # Also quote if it starts or ends with whitespace, or contains quotes
    if value.strip() != value or '"' in value or "'" in value:
        needs_quoting = True
    
    # Quote if it's a number-like string or boolean-like string
    if value.lower() in ['true', 'false', 'yes', 'no', 'null', 'none']:
        needs_quoting = True
    
    # Try to parse as number
    try:
        float(value)
        needs_quoting = True
    except ValueError:
        pass
    
    if needs_quoting:
        # Escape any existing double quotes and wrap in double quotes
        escaped_value = value.replace('"', '\\"')
        return f'"{escaped_value}"'
    else:
        return value


def format_time(seconds: float) -> str:
    """Convert seconds to HH:MM:SS format."""
    try:
        if seconds < 0:
            seconds = 0

        integer_seconds = int(seconds)
        hours, remainder = divmod(integer_seconds, 3600)
        minutes, secs = divmod(remainder, 60)
        return f'{hours:02}:{minutes:02}:{secs:02}'
    except:
        return '00:00:00'


def get_audio_metadata(file_path):
    """Extract metadata from audio file using FFprobe (adapted from 2b_check_audio_metadata.py)."""
    try:
        cmd = [
            'ffprobe',
            '-v', 'quiet',
            '-print_format', 'json',
            '-show_format',
            '-show_streams',
            str(file_path)
        ]

        result = subprocess.run(cmd, capture_output=True, text=True)

        if result.returncode == 0:
            metadata_info = json.loads(result.stdout)
            format_info = metadata_info.get('format', {})

            # Get metadata tags
            tags = format_info.get('tags', {})

            # Normalize tag keys (FFmpeg can return different case variations)
            normalized_tags = {}
            for key, value in tags.items():
                normalized_tags[key.lower()] = value

            metadata = {
                'title': normalized_tags.get('title', 'Unknown Title'),
                'artist': normalized_tags.get('artist', 'Unknown Artist'),
                'date': normalized_tags.get('date', 'Unknown Date'),
                'url': normalized_tags.get('url', ''),
            }

            # Try to get URL from mutagen if available and file is M4A
            custom_url = ''
            if MUTAGEN_AVAILABLE and str(file_path).lower().endswith('.m4a'):
                try:
                    audio_file = MP4(str(file_path))
                    url_field = audio_file.get('----:com.apple.iTunes:URL')
                    if url_field:
                        custom_url = url_field[0].decode('utf-8') if url_field[0] else ''
                except Exception:
                    pass  # Ignore mutagen errors

            # Use custom URL if available, otherwise use standard URL
            if custom_url:
                metadata['url'] = custom_url

            return {
                'success': True,
                'metadata': metadata
            }
        else:
            return {
                'success': False,
                'error': f"FFprobe error: {result.stderr}",
                'metadata': {
                    'title': 'Unknown Title',
                    'artist': 'Unknown Artist',
                    'date': 'Unknown Date',
                    'url': ''
                }
            }

    except json.JSONDecodeError as e:
        return {
            'success': False,
            'error': f"JSON parsing error: {e}",
            'metadata': {
                'title': 'Unknown Title',
                'artist': 'Unknown Artist',
                'date': 'Unknown Date',
                'url': ''
            }
        }
    except Exception as e:
        return {
            'success': False,
            'error': f"Unexpected error: {e}",
            'metadata': {
                'title': 'Unknown Title',
                'artist': 'Unknown Artist',
                'date': 'Unknown Date',
                'url': ''
            }
        }


def create_timestamp_url(base_url: str, seconds: float) -> str:
    """Create a timestamped YouTube URL."""
    try:
        timestamp = int(seconds)
        if base_url and 'youtu' in base_url:
            # Handle YouTube URLs
            if '&t=' in base_url or '?t=' in base_url:
                # URL already has timestamp, replace it
                import re
                base_url = re.sub(r'[&?]t=\d+', '', base_url)

            separator = '&' if '?' in base_url else '?'
            return f"{base_url}{separator}t={timestamp}"
        else:
            # For non-YouTube URLs or empty URLs, return placeholder
            return f"#t={timestamp}"
    except:
        return "#"


def get_markdown_file():
    """Browse for and select a markdown file."""
    root = tk.Tk()
    root.withdraw()  # Hide the main window
    
    file_path = filedialog.askopenfilename(
        title="Select Markdown File Containing YouTube URLs",
        filetypes=[("Markdown files", "*.md"), ("All files", "*.*")],
        initialdir=os.getcwd()
    )
    
    if not file_path:
        messagebox.showwarning("No File Selected", "No markdown file was selected. Exiting.")
        return None
        
    return file_path


def get_transcript_folder():
    """Browse for and select a folder to save transcripts."""
    root = tk.Tk()
    root.withdraw()  # Hide the main window
    
    folder_path = filedialog.askdirectory(
        title="Select Folder to Save Transcripts",
        initialdir=os.getcwd()
    )
    
    if not folder_path:
        messagebox.showwarning("No Folder Selected", "No transcript folder was selected. Exiting.")
        return None
        
    return Path(folder_path)


def detect_markdown_headers(file_path):
    """Detect markdown headers in the file and return a list of sections."""
    headers = []
    if os.path.exists(file_path):
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                lines = f.readlines()
                for i, line in enumerate(lines):
                    line = line.strip()
                    if line.startswith('#'):
                        # Count the number of '#' characters to determine header level
                        level = 0
                        for char in line:
                            if char == '#':
                                level += 1
                            else:
                                break
                        header_text = line[level:].strip()
                        headers.append({
                            'level': level,
                            'text': header_text,
                            'line_number': i + 1
                        })
        except Exception as e:
            print(f"Warning: Could not read file {file_path}: {e}")
    
    return headers


def select_section(headers):
    """Let user select which section to process URLs from."""
    if not headers:
        return None
    
    print("\nFound markdown headers in the file:")
    print("0. Process all URLs from entire file")
    
    for i, header in enumerate(headers):
        indent = "  " * (header['level'] - 1)
        print(f"{i + 1}. {indent}{header['text']}")
    
    while True:
        try:
            choice = input(f"\nSelect section to process URLs from (0-{len(headers)}): ").strip()
            choice_num = int(choice)
            if choice_num == 0:
                return None  # Process entire file
            elif 1 <= choice_num <= len(headers):
                return headers[choice_num - 1]
            else:
                print(f"Please enter a number between 0 and {len(headers)}")
        except ValueError:
            print("Please enter a valid number")


def find_section_boundaries(file_path, selected_header):
    """Find the start and end line numbers for the selected section."""
    if not selected_header:
        return None, None  # Process entire file
    
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            lines = f.readlines()
        
        start_line = selected_header['line_number']
        end_line = len(lines) + 1  # Default to end of file
        
        # Find the next header at the same or higher level
        for i in range(selected_header['line_number'], len(lines)):
            line = lines[i].strip()
            if line.startswith('#'):
                # Count header level
                level = 0
                for char in line:
                    if char == '#':
                        level += 1
                    else:
                        break
                
                # If we find a header at the same or higher level (lower number), this is where the section ends
                # But only if it's not the selected header itself
                if level <= selected_header['level'] and i != (selected_header['line_number'] - 1):
                    end_line = i + 1
                    break
        
        return start_line, end_line
    
    except Exception as e:
        print(f"Error reading file: {e}")
        return None, None


def extract_youtube_urls(file_path, selected_header=None) -> List[str]:
    """Extract YouTube URLs from markdown file, optionally from a specific section."""
    youtube_urls = []
    if not os.path.exists(file_path):
        print(f"Error: Markdown file not found at {file_path}")
        return youtube_urls
    
    # Get section boundaries if a specific section is selected
    start_line, end_line = find_section_boundaries(file_path, selected_header)
    
    if selected_header:
        print(f"Processing URLs from section: {selected_header['text']}")
    
    youtube_url_pattern = re.compile(r'(https?://(?:www\.)?(youtube\.com/watch\?v=|youtu\.be/)[^\s]+)')
    
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            for line_num, line in enumerate(f, 1):
                # Skip lines outside the selected section
                if start_line is not None and end_line is not None:
                    if line_num < start_line or line_num >= end_line:
                        continue
                
                line = line.strip()
                if not line or line.startswith('#'):
                    continue
                
                matches = youtube_url_pattern.findall(line)
                for match in matches:
                    if isinstance(match, tuple):
                        youtube_urls.append(match[0])
                    else:
                        youtube_urls.append(match)
    
    except Exception as e:
        print(f"Error reading {file_path}: {e}")
        return []
    
    return youtube_urls


def extract_youtube_id(url: str) -> Optional[str]:
    """Extract YouTube video ID from URL."""
    patterns = [
        r'(?:youtube\.com/watch\?v=)([^&\s)]+)',
        r'(?:youtu\.be/)([^?\s)]+)',
        r'(?:youtube\.com/embed/)([^?\s)]+)'
    ]
    
    for pattern in patterns:
        match = re.search(pattern, url)
        if match:
            return match.group(1)
    
    return None


def check_existing_transcript(video_id: str, transcript_folder: Path) -> Optional[Path]:
    """Check if transcript already exists for given video ID."""
    if not transcript_folder.exists():
        return None
    
    # Look for files containing the video ID
    for file_path in transcript_folder.iterdir():
        if file_path.is_file() and file_path.suffix.lower() == '.md':
            if video_id in file_path.name:
                return file_path
    
    return None


def extract_video_id_from_filename(filename: str) -> Optional[str]:
    """Extract YouTube video ID from filename using multiple patterns."""
    # Method 1: Handle "YYYYMMDD - VideoID - [Title](URL)" format specifically
    dash_pattern = r'^\d{8}\s*-\s*([a-zA-Z0-9_-]{11})\s*-'
    match = re.search(dash_pattern, filename)
    if match:
        return match.group(1)
    
    # Method 2: Extract from YouTube URL in markdown format [Title](URL)
    url_pattern = r'\[.*?\]\((https?://(?:www\.)?(youtube\.com/watch\?v=|youtu\.be/)([^\s&)]+))'
    match = re.search(url_pattern, filename)
    if match:
        url = match.group(1)
        video_id = extract_youtube_id(url)
        if video_id:
            return video_id
    
    # Method 3: General pattern for 11-character YouTube video IDs
    video_id_pattern = r'[a-zA-Z0-9_-]{11}'
    
    # Look for 11-character patterns that could be video IDs
    matches = re.findall(video_id_pattern, filename)
    
    for match in matches:
        # Basic validation: YouTube IDs typically contain both letters and numbers
        if re.search(r'[a-zA-Z]', match) and re.search(r'[0-9]', match):
            return match
    
    return None


class TimeoutException(Exception):
    pass

def timeout_handler(signum, frame):
    raise TimeoutException("Audio file search timed out")

def find_audio_file(video_id: str, timeout_seconds: int = 60) -> Optional[Path]:
    """Find corresponding audio file for given video ID with timeout."""
    audio_folder = Path(AUDIO_FOLDER_PATH)
    
    if not audio_folder.exists():
        print(f"   ❌ Audio folder not found: {audio_folder}")
        return None
    
    # Get all audio files
    audio_files = [f for f in audio_folder.iterdir() if f.is_file() and f.suffix.lower() in SUPPORTED_AUDIO_EXTENSIONS]
    total_files = len(audio_files)
    
    if total_files == 0:
        print(f"   ❌ No audio files found in: {audio_folder}")
        return None
    
    # Set up timeout (only on Unix-like systems)
    if hasattr(signal, 'SIGALRM'):
        old_handler = signal.signal(signal.SIGALRM, timeout_handler)
        signal.alarm(timeout_seconds)
    
    start_time = time.time()
    current_file = None
    
    try:
        # Look for audio files containing the video ID or with matching metadata
        for i, file_path in enumerate(audio_files, 1):
            current_file = file_path
            current_time = time.time()
            
            # Check for timeout on Windows (no signal support)
            if not hasattr(signal, 'SIGALRM') and (current_time - start_time) > timeout_seconds:
                print(f"\r   ⏱️  Search timed out after {timeout_seconds}s on file: {current_file.name}")
                return None
            
            # Update progress indicator
            elapsed = int(current_time - start_time)
            print(f"\r   🔍 Searching audio files {i}/{total_files} ({elapsed}s)...", end="", flush=True)
            
            # Method 1: Check if video ID is directly in filename
            if video_id in file_path.name:
                print(f"\r   ✅ Found audio file by direct ID match: {file_path.name}")
                return file_path
            
            # Method 2: Extract video ID from filename and compare
            extracted_id = extract_video_id_from_filename(file_path.name)
            if extracted_id and extracted_id == video_id:
                print(f"\r   ✅ Found audio file by extracted ID match: {file_path.name}")
                return file_path
            
            # Method 3: Check metadata for matching URL
            try:
                metadata_result = get_audio_metadata(file_path)
                if metadata_result['success']:
                    url = metadata_result['metadata'].get('url', '')
                    if video_id in url:
                        print(f"\r   ✅ Found audio file by metadata URL match: {file_path.name}")
                        return file_path
                    
                    # Method 4: Extract video ID from metadata URL and compare
                    metadata_video_id = extract_youtube_id(url)
                    if metadata_video_id and metadata_video_id == video_id:
                        print(f"\r   ✅ Found audio file by metadata video ID match: {file_path.name}")
                        return file_path
            except Exception:
                continue  # Skip files with metadata errors
        
        # Clear the progress line and show failure message
        print(f"\r   ❌ No audio file found for video ID: {video_id} (searched {total_files} files)")
        print(f"   💡 Available files with video IDs:")
        for file_path in audio_files[:5]:  # Show first 5 files as examples
            extracted_id = extract_video_id_from_filename(file_path.name)
            print(f"      - {file_path.name} (ID: {extracted_id})")
        if len(audio_files) > 5:
            print(f"      ... and {len(audio_files) - 5} more files")
        
        return None
        
    except TimeoutException:
        print(f"\r   ⏱️  Search timed out after {timeout_seconds}s on file: {current_file.name if current_file else 'unknown'}")
        return None
    finally:
        # Clean up timeout
        if hasattr(signal, 'SIGALRM'):
            signal.alarm(0)
            signal.signal(signal.SIGALRM, old_handler)


def find_audio_file_with_retry(video_id: str, max_attempts: int = 3) -> Optional[Path]:
    """Find audio file with retry logic for timeout situations."""
    for attempt in range(max_attempts):
        if attempt > 0:
            print(f"   🔄 Retry attempt {attempt + 1}/{max_attempts} for video ID: {video_id}")
        
        result = find_audio_file(video_id)
        if result is not None:
            return result
        
        if attempt < max_attempts - 1:
            print(f"   ⏳ Waiting 2 seconds before retry...")
            time.sleep(2)
    
    return None


def detect_speech_pauses(segments: List[dict], min_pause_duration: float = 2.0) -> List[float]:
    """Detect speech pauses and return timestamps where additional markers should be placed."""
    pause_timestamps = []
    
    if not segments or len(segments) < 2:
        return pause_timestamps
    
    for i in range(len(segments) - 1):
        current_seg = segments[i]
        next_seg = segments[i + 1]
        
        current_end = current_seg.get('end')
        next_start = next_seg.get('start')
        
        if current_end is not None and next_start is not None:
            pause_duration = next_start - current_end
            
            # If there's a significant pause, add a timestamp marker
            if pause_duration >= min_pause_duration:
                # Place marker at the end of the current segment
                pause_timestamps.append(current_end)
    
    return pause_timestamps


def insert_timestamp_markers(text: str, segments: List[dict], metadata: dict, interval: int = 15) -> str:
    """Insert (t:) timestamp markers throughout the text based on segments and pauses."""
    if not segments:
        return f"(t: 0) {text}"
    
    # Get speech pause timestamps
    pause_timestamps = detect_speech_pauses(segments)
    
    # Create a list of all timestamp positions (regular intervals + pauses)
    timestamp_positions = set()
    
    # Add regular interval markers
    max_time = max(seg.get('end', 0) for seg in segments if seg.get('end') is not None)
    for t in range(0, int(max_time) + interval, interval):
        timestamp_positions.add(float(t))
    
    # Add pause-based markers
    for pause_time in pause_timestamps:
        timestamp_positions.add(pause_time)
    
    # Sort all timestamp positions
    sorted_timestamps = sorted(timestamp_positions)
    
    # Build the transcript with embedded markers
    result_parts = []
    current_pos = 0
    
    for i, timestamp in enumerate(sorted_timestamps):
        # Determine the next timestamp boundary for this interval
        next_timestamp = sorted_timestamps[i + 1] if i + 1 < len(sorted_timestamps) else max_time + interval
        
        # Find all segments that should appear in this timestamp interval
        text_parts = []
        
        for segment in segments:
            seg_start = segment.get('start', 0)
            seg_end = segment.get('end', 0)
            seg_text = segment.get('text', '').strip()
            
            # Include segments that start within this timestamp interval
            # (start at or after current timestamp and before next timestamp)
            if seg_start >= timestamp and seg_start < next_timestamp and seg_text:
                text_parts.append(seg_text)
        
        if text_parts or timestamp == 0:  # Always include marker at start
            # Add timestamp marker
            marker = f"(t: {int(timestamp)})"
            if result_parts:  # Add space before marker if not first
                result_parts.append(" ")
            result_parts.append(marker)
            
            # Add the text content
            if text_parts:
                combined_text = " ".join(text_parts)
                result_parts.append(" " + combined_text)
        
        current_pos = next_timestamp
    
    return "".join(result_parts).strip()


def transcribe_youtube_audio(audio_path: Path, url: str, video_id: str, transcript_folder: Path, model) -> Optional[Path]:
    """Transcribe a single audio file using Whisper with new timestamp marker format."""

    print(f"🎵 Transcribing: {audio_path.name}")

    try:
        # Check file exists and has content
        if not audio_path.exists():
            print(f"❌ Audio file does not exist: {audio_path}")
            return None

        file_size = audio_path.stat().st_size
        if file_size == 0:
            print(f"❌ Audio file is empty: {audio_path}")
            return None

        print(f"   File size: {file_size:,} bytes")

        # Get metadata from audio file
        metadata_result = get_audio_metadata(audio_path)
        if metadata_result['success']:
            metadata = metadata_result['metadata']
            print(f"   📋 Using metadata from audio file: {metadata['title']}")
        else:
            # Fallback metadata
            metadata = {
                'title': f"YouTube Video {video_id}",
                'artist': 'YouTube',
                'date': 'Unknown Date',
                'url': url
            }
            print(f"   ⚠️  Could not read metadata, using fallback")

        # Transcribe using Whisper
        print("   🔄 Starting Whisper transcription...")
        result = model.transcribe(str(audio_path))

        # Extract results
        segments = result.get('segments', [])
        text = result.get('text', '').strip()

        print(f"   ✅ Transcription completed - {len(segments)} segments, {len(text)} characters")

        # Create transcript filename with YYYYMMDD format
        date_str = metadata.get('date', 'Unknown Date')
        if date_str and len(date_str) >= 8 and date_str.replace('-', '').isdigit():
            # Handle YYYY-MM-DD or YYYYMMDD format
            clean_date = date_str.replace('-', '')
            if len(clean_date) >= 8:
                formatted_date = clean_date[:8]  # Take first 8 digits (YYYYMMDD)
            else:
                formatted_date = "00000000"
        else:
            formatted_date = "00000000"
        
        safe_title = sanitize_filename(metadata['title'])
        filename = f"{formatted_date} - {video_id} - {safe_title}.md"
        transcript_path = transcript_folder / filename

        print(f"   💾 Writing transcript to: {transcript_path.name}")

        with open(transcript_path, 'w', encoding='utf-8') as f:
            # Write YAML front matter using metadata from audio file
            f.write("---\n")
            f.write(f"title: {safe_yaml_value(metadata['title'])}\n")
            f.write(f"artist: {safe_yaml_value(metadata['artist'])}\n")
            f.write(f"date: {safe_yaml_value(metadata['date'])}\n")
            f.write(f"url: {safe_yaml_value(metadata['url'])}\n")
            f.write("---\n\n")

            if not segments:
                # No speech detected - create a placeholder entry
                print("   ⚠️  No speech segments detected, creating placeholder entry")
                f.write(f"(t: 0) No speech detected\n\n")
            else:
                print(f"   📝 Processing {len(segments)} segments with {TIMESTAMP_INTERVAL}-second interval markers and VAD pause detection")
                
                # Create transcript with embedded timestamp markers
                transcript_with_markers = insert_timestamp_markers(text, segments, metadata, TIMESTAMP_INTERVAL)
                
                # Write the transcript
                f.write(transcript_with_markers)
                f.write("\n\n")

        print(f"   ✅ Transcript created successfully: {transcript_path.name}")
        return transcript_path

    except Exception as e:
        print(f"❌ Transcription error for {audio_path.name}: {e}")
        logging.error(f"Transcription error for {audio_path}: {e}")
        return None


def add_transcript_links_to_markdown(file_path: str, url_transcript_mapping: Dict[str, Optional[Path]]) -> bool:
    """Add transcript links after each URL line in the markdown file."""
    try:
        # Read the original file
        with open(file_path, 'r', encoding='utf-8') as f:
            lines = f.readlines()
        
        # Process lines and add transcript links
        new_lines = []
        for line in lines:
            new_lines.append(line)
            
            # Check if line contains a YouTube URL
            for url, transcript_path in url_transcript_mapping.items():
                if url in line and transcript_path:
                    # Extract video ID for the link text
                    video_id = extract_youtube_id(url)
                    if video_id:
                        # Add transcript link after the URL line
                        transcript_link = f"- [{video_id}]({transcript_path})\n"
                        new_lines.append(transcript_link)
                    break
        
        # Write back to file
        with open(file_path, 'w', encoding='utf-8') as f:
            f.writelines(new_lines)
        
        print(f"✅ Added transcript links to {file_path}")
        return True
    
    except Exception as e:
        print(f"❌ Error updating markdown file: {e}")
        return False


def main():
    """Main function to process YouTube URLs from markdown file."""
    print("YouTube URL Transcript Manager")
    print("=" * 50)
    
    # Step 1: Browse for markdown file
    print("📁 Select a markdown file containing YouTube URLs...")
    markdown_file = get_markdown_file()
    if not markdown_file:
        return
    
    print(f"Selected file: {markdown_file}")
    
    # Step 2: Check for markdown headers and let user select section
    headers = detect_markdown_headers(markdown_file)
    selected_header = None
    if headers:
        selected_header = select_section(headers)
    
    # Step 3: Extract YouTube URLs
    youtube_urls = extract_youtube_urls(markdown_file, selected_header)
    if not youtube_urls:
        print("❌ No YouTube URLs found in selected section.")
        return
    
    print(f"\n🔗 Found {len(youtube_urls)} YouTube URL(s):")
    for i, url in enumerate(youtube_urls, 1):
        video_id = extract_youtube_id(url)
        print(f"  {i}. {url} (ID: {video_id})")
    
    # Step 4: Browse for transcript output folder
    print(f"\n📁 Select folder to save transcripts...")
    transcript_folder = get_transcript_folder()
    if not transcript_folder:
        return
    
    print(f"📁 Transcripts will be saved to: {transcript_folder}")
    print(f"🎵 Audio files will be searched in: {AUDIO_FOLDER_PATH}")
    
    # Step 5: Load Whisper model
    print(f"\n🔄 Loading Whisper model: {WHISPER_MODEL}")
    print("   ⏳ This may take some time for larger models...")
    try:
        model = whisper.load_model(WHISPER_MODEL)
        print("✅ Whisper model loaded successfully")
    except Exception as e:
        print(f"❌ Error loading Whisper model: {e}")
        print("   💡 Try using a smaller model like 'base' if you encounter memory issues")
        return
    
    # Step 6: Process each URL
    print(f"\nStarting transcript processing...")
    print("-" * 50)
    
    url_transcript_mapping = {}
    successful_transcriptions = 0
    failed_transcriptions = 0
    skipped_transcriptions = 0
    
    for i, url in enumerate(youtube_urls, 1):
        start_time = datetime.now()
        print(f"\n[{i}/{len(youtube_urls)}] Processing URL:")
        print(f"   🔗 {url}")
        print(f"   🕐 Started at: {start_time.strftime('%Y-%m-%d %H:%M:%S')}")
        
        video_id = extract_youtube_id(url)
        if not video_id:
            end_time = datetime.now()
            duration = end_time - start_time
            print(f"   ❌ Could not extract video ID from URL")
            print(f"   🕐 Completed at: {end_time.strftime('%Y-%m-%d %H:%M:%S')} (Duration: {duration.total_seconds():.1f}s)")
            failed_transcriptions += 1
            url_transcript_mapping[url] = None
            continue
        
        # Check if transcript already exists
        existing_transcript = check_existing_transcript(video_id, transcript_folder)
        if existing_transcript:
            end_time = datetime.now()
            duration = end_time - start_time
            print(f"   ⏭️  Transcript already exists: {existing_transcript.name}")
            print(f"   🕐 Completed at: {end_time.strftime('%Y-%m-%d %H:%M:%S')} (Duration: {duration.total_seconds():.1f}s)")
            skipped_transcriptions += 1
            url_transcript_mapping[url] = existing_transcript
            continue
        
        # Find corresponding audio file
        audio_path = find_audio_file_with_retry(video_id)
        if not audio_path:
            end_time = datetime.now()
            duration = end_time - start_time
            print(f"   ❌ No corresponding audio file found for video ID: {video_id}")
            print(f"   💡 Make sure to run 2_create_audio_files.py first")
            print(f"   🕐 Completed at: {end_time.strftime('%Y-%m-%d %H:%M:%S')} (Duration: {duration.total_seconds():.1f}s)")
            failed_transcriptions += 1
            url_transcript_mapping[url] = None
            continue
        
        # Transcribe audio
        transcript_path = transcribe_youtube_audio(
            audio_path, url, video_id, transcript_folder, model
        )
        
        end_time = datetime.now()
        duration = end_time - start_time
        
        if transcript_path:
            print(f"   ✅ Transcript created: {transcript_path.name}")
            print(f"   🕐 Completed at: {end_time.strftime('%Y-%m-%d %H:%M:%S')} (Duration: {duration.total_seconds():.1f}s)")
            successful_transcriptions += 1
            url_transcript_mapping[url] = transcript_path
        else:
            print(f"   ❌ Transcription failed")
            print(f"   🕐 Completed at: {end_time.strftime('%Y-%m-%d %H:%M:%S')} (Duration: {duration.total_seconds():.1f}s)")
            failed_transcriptions += 1
            url_transcript_mapping[url] = None
    
    
    # Summary
    print("\n" + "=" * 50)
    print("TRANSCRIPTION SUMMARY")
    print("=" * 50)
    print(f"✅ Successful transcriptions: {successful_transcriptions}")
    print(f"⏭️  Skipped (already exist): {skipped_transcriptions}")
    print(f"❌ Failed transcriptions: {failed_transcriptions}")
    print(f"📁 Markdown file: {markdown_file}")
    print(f"📁 Transcript folder: {transcript_folder}")
    
    if failed_transcriptions > 0:
        print(f"\n⚠️  {failed_transcriptions} transcriptions failed. Check the URLs and try again.")
    
    if successful_transcriptions > 0:
        print(f"\n🎉 Successfully transcribed {successful_transcriptions} YouTube videos!")
        print(f"📝 Transcripts use (t:) markers at {TIMESTAMP_INTERVAL}-second intervals with VAD pause detection")
    

    input("\nPress Enter to exit...")


if __name__ == "__main__":
    main()
