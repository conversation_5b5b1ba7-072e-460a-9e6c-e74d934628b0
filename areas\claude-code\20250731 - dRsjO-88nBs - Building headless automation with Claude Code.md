---
title: Building headless automation with <PERSON>
artist: Anthropic
date: 2025-07-31
url: https://www.youtube.com/watch?v=dRsjO-88nBs
---

(t: 0) Good afternoon, everybody. My name is <PERSON>. I am an engineer on the Cloud Code team. (t: 10) And today, we're going to be talking a little bit about the Cloud Code SDK and the Cloud GitHub Action that was just announced today. (t: 21) Cool. So a little bit about the agenda. We do a little quick start for the SDK, just to give some examples of how to get started and how to use the SDK. (t: 30) We will then dive into a live demo of the GitHub Action, which should be fun. The GitHub Action was built on top of the SDK, so it's meant to be a source of inspiration for the kind of things that you can do using the Cloud Code SDK. (t: 40) We'll then dive into some more advanced features of the SDK. (t: 50) And then we'll end with having all of you set up. So if you want to build up the Cloud GitHub Action on your repos, you guys can start using it and build on top of it. (t: 61) Cool. Actually, before we get started, can I get a show of hands here? How many people have used Cloud Code? (t: 70) Okay. That's a lot of people. And of the people who have used Cloud Code, how many have used Cloud-P or know what that is? Okay. Far fewer people. (t: 80) It's good to know. If you guys have... If you guys don't have Cloud Code installed on your laptop, that's how you get it. I'd encourage you to install it on your laptops and follow along. (t: 90) There will be parts of this talk that will be beneficial to follow along. And then if you don't want to, you don't have to. It's all good. (t: 100) Cool. So what is the Cloud Code SDK? It is a way to programmatically access the power of the Cloud Code agent in headless mode. (t: 110) This is powerful because it's a new kind of primitive and a new kind of building block that allows you to build applications that just weren't possible before. (t: 120) Things that you can do with the SDK are super simple things to get started. For example, you can use it like a Unix tool. The Unix-ish tool philosophy is what really makes Cloud Code powerful (t: 130) because you can plug it in anywhere where you can run Bash or a terminal. So you can use it in your Unix pipelines. You can pipe stuff into it, pipe stuff out of it, make complex chains out of it, (t: 140) and stuff like that. You can then build CI automation on it. So you can have Cloud review your code. (t: 150) Some people actually get Cloud to write new linters for them too. So Cloud can lint your code. If there are specific things that you can't define programmatically, you can get Cloud Code to do it. And then we get into fancier applications as well. (t: 160) So if you want to build your own chatbot that's powered by Cloud Code, that's certainly possible. If you want Cloud Code to write you code in a new environment or a separate remote environment, (t: 170) you can build those kinds of applications as well. And finally, these are a few features. We'll talk more about the features in the coming slides. (t: 180) And we have Python and TypeScript SDKs or bindings for the Cloud Code SDK coming up soon. So that should make it much easier for you guys to consume it and build on top of it. (t: 190) So let's jump into some basic examples. Calling the Cloud Code SDK is as simple as doing cloud-p (t: 200) and following it up with the string that you want to ask Cloud. So in this example, I'm telling Cloud to write me a Fibonacci sequence generator. And if you notice, I also give it a dash dash allow tools write, (t: 210) which is a way for me to proactively give it access to the write tool. So it can write files to my file system. (t: 220) And then this is something I like doing too, piping logs to Cloud. So you can do cat app.log and then pipe that into Cloud-P. (t: 230) I don't like looking at logs manually, so this is something I do quite often. And as you can see, it does a pretty decent job of summarizing what the log failures were. (t: 240) Similarly, if you're anything like me, I just can't get myself to understand the output of ifconfig. I still don't know what it means, but Cloud does, and Cloud does it for me over here. (t: 250) And finally, this is kind of what makes the SDK tick. We have an output format. (t: 260) If you do dash dash output format JSON, Cloud Code will actually output things or its response in JSON as opposed to plain text. And you can parse this JSON and build on top of it. (t: 270) So we'll talk more about details for how this is, what else you can do with this JSON, but I wanted to throw that example out there. (t: 280) Let's get into a significantly more complex example now, which is the GitHub action. So the GitHub action was built on top of the SDK. (t: 290) And this is a very simple example. It's a very simple example of how you can use the API to create a new API. And it can be used to review code. It can be used to create new features. It can be used to triage bugs and so on. (t: 300) And this is also open source. So I'll include a link at the very end of the talk so you guys can go have a look at the source for inspiration for how to use it. (t: 310) But for now, let's jump into a live demo on my laptop. So I have cloned a popular small, like, open source quiz app for the purposes of this demo. And we are going to fire it up just to see how that works. (t: 320) And then we will tell Claude to build something on top of it for us. So I just did an NPM start, which opened up my shiny new quiz app. (t: 330) It's actually pretty nifty. It allows you to, like, choose a bunch of categories, how many questions you want, difficulty. (t: 340) Definitely easy for me. I suck at trivia. Type of questions. And then there's, like, a countdown timer. So we're not going to actually answer all of them. Unless someone feels very strongly, please shout out the answer. (t: 350) But I'm just going to fly through these just to show you guys how this quiz app works. (t: 360) There we go. Not surprising. You got a great F. But that's okay. So this was the little, like, demo quiz app that's open sourced. (t: 370) And if you look at the issues for this repo, we see a couple of things. So we see a couple of very interesting ones. (t: 380) There's one issue that says we should add power ups for 50-50 elimination of options and skip questions for free. (t: 390) Because I suck at trivia, I really like that feature. And I want to build it. And before this presentation, I already installed Claude GitHub action on my repo. (t: 400) So it's already available. But we'll go over, like, how to set that up later, too. Okay. So here's the issue. It has pretty sparse details on how to implement this. (t: 410) It's just literally a feature, a wish list, really. Like a wish feature. It's saying add a power up option in the config, 50-50 elimination. (t: 420) For the skip question, it should award user points even though the question was skipped. And users should be able to configure this from the config page. (t: 430) So there's a lot of, like, creative room for Claude to do whatever it wants to do in this case. And I'm excited to see what it actually ends up building. So what I'm going to do is say, at Claude, please implement this feature. (t: 440) And comment on it. So it usually does take four or five seconds for it to respond. (t: 450) And while it's doing that, for good measure, we'll just also take this other GitHub issue. This is talking about a per question timer. (t: 460) So we saw there was a global timer on the quiz app, but there was no per question timer. So that's what this one's talking about. So let's go and say Claude, please build this. (t: 470) And now we have two things building. Cool. So now when I get back to this tab, you see that Claude responded with a comment on this GitHub issue. (t: 480) Saying that it's working. It also has a link to the job run, which is the GitHub action run. (t: 490) If I click into it, and if I actually, like, click on the logs, I'll see that it's doing a bunch of stuff. You can see all this JSON being output. This is from the SDK. (t: 500) So you won't look at the JSON too much because it's not much fun to parse it manually. But over here we can see that it also created a to-do list for us. So Claude's now going to actually go through this to-do list and try to implement the power up feature. (t: 510) And similarly for the question timer, it's going to do something similar. (t: 520) One more thing that we should do here is there are already a couple of things that we need to do. There are already a couple of pull requests that have been opened for this repo. (t: 530) And let's get Claude to review it or change some of these pull requests. Just for fun. There's this one which is change background color to blue. (t: 540) All right. I actually think I like green better. So I'm just going to be like, all right, Claude. Please change this to green. (t: 550) And then I'm going to go ahead and do this. And this one is fairly easy. And I'm pretty sure Claude's going to do this. But I just wanted to show you guys that it can also add commits for a pull request that's already open. (t: 560) Okay. So this is going to take a few minutes to run. And while this runs, let's go back to the presentation. And then we'll check up on how this is doing towards the end. (t: 570) Okay. Cool. So let's do a little bit of a deep dive on the features of the SDK. (t: 580) When you call Claude-P by default, it has a default. It has a default. It has no edit or destructive permission access. Which is great for safety. But it's not great for actually getting things done. (t: 590) Which is why there is a dash dash allow tools option which allows you to preconfigure Claude with any permissions that you think it might need in the future for your given task. (t: 600) So in this case, the first example you see that I've given it permissions, bash permissions to NPM run build, NPM test. (t: 610) And the write tool. Which is a good set of permissions because this allows Claude to self verify what it's writing and build your project and test and then continue writing. (t: 620) Similarly for MCP, if you have MCP servers configured, you can allow list those MCP tools as well. (t: 630) So it's a very similar process. Then structured output. We already saw an example of structured output. (t: 640) Both from the GitHub actions logs. And also the little screen shot I showed you earlier. But there's two modes here. There's stream JSON and JSON. It does exactly what it sounds like. (t: 650) If you select stream JSON, it will actually stream messages to you as and when they're available. Versus JSON will just give you one giant blob of JSON at the end. (t: 660) And parsing this JSON and building on top of it is really how you can make use of the Claude code SDK and increase the performance of the project. So that's the first example. (t: 670) And then the second example is the first one. So this is the first example of the SDK and create features for your users. And then you can also configure the system prompt. So you can do dash dash system prompt talk like a pirate and you can get Claude code to talk like a pirate for the rest of your day. (t: 680) Which is actually quite fun. If you haven't done it, I encourage you to try it out. (t: 690) We also have a few user interaction features built into the SDK. And what that means is that the first one is the user interaction. And then the second one is the user interaction. (t: 700) And the third one is resuming session state. So when you call Claude dash P in structured output or JSON mode, it's going to return a session ID. (t: 710) And this session ID is useful because you can then reference the session ID to go back to the same context state that Claude had when it finished that process. (t: 720) So by preserving these session IDs and keeping track of them, you can enable or, like, build user interactive features where, like, the user says something, you pass that on to Claude. Claude returns a response. (t: 730) And now you want the user to give feedback on that response. And that's how this kind of enables you to build those types of interactions in your apps. And then the last one, and this one's actually pretty interesting, and it's fairly recent, too. It's dash dash permission prompt tool. We talked a little bit about how to give Claude permissions using the allowed tools flag. And that requires you to pre-configure them in advance. But what if you have a server that's already been configured to allow that, and you have to do that now? So you can do that in a way that's more easy to do. So you can do that in a way that's more efficient. So you can do that in a way that's more intuitive. (t: 740) You can do that in a way that's more efficient. And then you can also do it in a way that's more user friendly. So then you have these best practices. And once that's done, it's fine. But you can also get a little bit more custom around that. There's the last one, and this one's actually pretty interesting, and it's fairly recent, too. It's dash dash permission prompt tool. (t: 750) We talked a little bit about how to give Claude permissions using the allowed tools flag. And that requires you to pre-configure them in advance. But what if you didn't want to do that, because you don't know what tools Claude would want (t: 760) to use in the future? In that case, you can use the dash dash permission prompt tool and offload the permission management (t: 770) to an MCP server. server. So you can ask users in real time for whether they want to accept a tool or reject a tool. And you can have an MCP server handle that for you, (t: 780) as opposed to trying to predict which tools are OK and which tools are not. So this is fairly recent. And we'd love to get feedback on this if you guys end up trying it out. (t: 790) OK, let's go back to our demo and see what Claude's done. (t: 800) All right. So this is the power-up issue. We can see that Claude has actually gone through his to-do list. (t: 810) OK, I'm going to open a PS. There's a link over here to create a PR. And I'm going to click that and see what that gives us. (t: 820) And I actually create the pull request, too, so it's easier for us to review. I don't really know how this code base works, but we'll still eyeball it just to see if it's doing the right thing. (t: 830) So we see some set power-up stuff. Seems all right. (t: 840) OK, there's some configuration in the main component. (t: 843) All right, I think what we should do and what will make this fun is that we should just (t: 850) get this branch locally and see what Claude did. Because there's no way that we can actually figure out what it did in the short amount of time that we have. So I'm going to go back to my terminal, do a test. And we're going to see what we've done. (t: 860) So we're going to go back to our terminal. And we're going to see what we've done. So we're going to go back to our terminal. And we're going to see what we've done. And we're going to see what we've done. So we're going to do a good fetch. (t: 862) Check out the branch that Claude just created. And restart our process. (t: 870) OK, awesome. It looks like we have a power-up section now (t: 880) at the bottom of our config page. And it's a little check box. I like that touch. We'll keep both of them on. And let's select general knowledge. (t: 890) And let's start playing this game. Let's see what it did. Oh, sweet. So you see it has this little 50-50 button on the bottom left (t: 900) and a skip questions button on the right. I'm just going to go with 50-50, because I have no idea what the answer to this is. Does anybody know what that is? (t: 910) D? OK, there we go. That makes sense. Cadbury, yeah. (t: 913) All right, I'm going to skip this one. And then let's just breeze through the other ones. (t: 920) I'm going to skip the other ones for the sake of time. (t: 930) All right, still got an F. But we got one correct answer, which is better than zero correct answers. And yeah, I guess. (t: 940) Yeah, it tricked us. That was a good one. But yeah, it seems like it worked. I think there's definitely more we could do. (t: 950) Here we could show which questions we use the power upon over here. And there's definitely more we can do. But at the most basic level, I think (t: 960) Claude was able to do the task that we assigned it to do, which is exciting. This is kind of the power of the GitHub Action, because you didn't really have to run this on your own infra. (t: 970) You can just literally comment on a thread saying, please build this for me. It uses your GitHub Action runners and just does the thing. So it's a really good way to do that. (t: 980) Let's also look at the PR that we told it changed from blue to green. It's all hex code. So let's just see what it did in the commits. (t: 990) So we see there's two commits. And Claude has added this last one to switch it from blue to green. And it did it for all three of the places where the color was defined, which is awesome. (t: 1000) OK, I'm now going to go over the last one, the question timer, because we might run out of time. (t: 1010) But this hopefully gives you insight into what the Claude GitHub Action can do for you. Let's go back to the presentation now. (t: 1020) OK, so just as a recap, the Claude GitHub Action, as it's implemented today, is able to read your code. It's able to create PRs for you from GitHub issues, (t: 1030) like we just saw. It's able to create commits for you. So if you already have a PR and you want to comment on it, it can add a commit to an existing (t: 1040) branch or an existing PR. It can answer questions. It doesn't have to do something. It can just literally answer questions for you. If you don't understand something, you can be like, hey, Claude, how does this work? And you can get it to answer questions. (t: 1050) And it can, of course, review your code. The best part of all of this is that you don't have to take care of the infra. It runs on existing GitHub runners, which almost everyone (t: 1060) has configured if you're using GitHub Actions. So that's kind of the really nice thing about this, is you don't have to worry about any of the info. OK. Thanks. Thanks, Manfred. (t: 1070) OK. So how were the actions built? I think I may have mentioned that these actions were built on top of the SDK. (t: 1080) So the SDK does form the foundation of how these actions were built. And then we have two other actions on top. We have the ClaudeCode base action. This is a thin layer that just implements (t: 1090) the piece which talks to ClaudeCode. And returns the response from ClaudeCode. And then we have another action on top of this, (t: 1100) which is called the PR action. And this action is responsible for all the fancy things that you saw on the PR. So it's responsible for making comments, for the to-do lists, (t: 1110) for rendering it the right way, for adding the PR links, and things like that. So it's kind of three layers in which it's built. (t: 1120) Both the base action and the PR action are open sourced. So I would encourage you guys to go have a look. Take inspiration from how that works, and maybe that inspires more ideas. (t: 1130) And then finally, you guys can install the Cloud GitHub (t: 1140) actions today. The easiest way to do this is to open up ClaudeCode in a terminal in the repo (t: 1150) that you want to install it in. And once you open up ClaudeCode, just do slash install, and then you're good to go. And then you can go ahead and install GitHub action. (t: 1160) And that is going to present you with a nice flow, which guides you through configuring your GitHub action, as well as merging it. So the end result of this would be a PR, which would be a YAML (t: 1170) file for your GitHub action. And once you merge that in, and you configure your API keys and things like that, you're off to the races. (t: 1180) And you can go ahead and start tagging Claude and using Claude, like we just did right now. Small caveat, if you're a Bedrock or Vertex user, (t: 1190) the instructions are a little bit different and a tiny bit more manual. So please have a look at the docs. The docs are pretty comprehensive in helping (t: 1200) you set up the GitHub action for both Bedrock and Vertex. (t: 1204) Cool. Finally, resources. (t: 1210) These are resources for things that we've talked about today. If you want to snap a picture, go ahead. There's a lot of stuff. There's a lot of stuff. But the open source repos for both the base action (t: 1220) and the Claude code action are here. And we absolutely love your feedback as well. So if you guys have any feedback on the SDK, on the GitHub action, or on Claude code, please (t: 1230) go to our public Claude code GitHub repo and file an issue there. And someone will have a look and get back to you. (t: 1240) Cool. That's all I have for today. Thanks for joining me. And I hope you guys have a good rest of the day.

