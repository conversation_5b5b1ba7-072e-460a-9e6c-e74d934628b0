---
title: <PERSON> Claude is HERE… Upgrade your Claude Code Workflow
artist: AI LABS
date: 2025-08-04
url: https://www.youtube.com/watch?v=ytn0aXK2gzE
---

(t: 0) The code that your AI agents are writing is not good, and I'll tell you why. On this channel, we've solved a lot of problems with AI coding, such as the models not having enough context of the project it's building. (t: 10) And then the models even forget the app that they were supposed to make, but many of these were solved by coding using context and task management. But even when humans write out code, they are bound to make mistakes, (t: 20) and they might miss something, causing poor quality code that fails. So, even before AI, there were already ways to check that the written code was up to standards and being written correctly. (t: 30) And today, we're going to combine these existing ways with AI coding. I'm going to show you how to actually get your AI agents to write clean code. Now, as we all know, Cloud Code is an amazing AI coding tool. (t: 40) It has given us so many features that make Cloud Code the best coding agent out there, and one of those features is hooks. Hooks allow you to run specific commands at certain points, (t: 50) while Cloud Code is writing code. So, you don't rely only on giving instructions in a simple .md file, but you actually... hard code the commands to be run at certain points. (t: 60) Therefore, there is no chance of failure. Now, a quick break to tell you about today's sponsor, Scrimba. So, here's the thing. I've been scrolling through Twitter lately, and suddenly, AI is everywhere. (t: 70) Everyone's building, prompting, deploying, and honestly, it started making me feel like I was being left behind. That's when I finally found a place to actually learn AI. (t: 80) But in a structured, hands-on way, it's not just another site with random lessons. The learning experience is fully interactive. You're coding right alongside the instructor, directly inside your browser. (t: 90) And their new course, the AI Engineer Path, is a total game changer. This path is built specifically for developers like me. It focuses on real tools you can use like OpenAI's API, Hugging Face, (t: 100) embeddings, AI agents, and more. Everything you build is practical, visual, and designed to help you understand (t: 110) what's happening behind the scenes. So, if you're serious about learning AI and want to build real projects that actually make sense, this is where you start. Save 20% on Scrimba Pro with the link in the description. (t: 120) So, for example, we can actually get notifications using Claude Code hooks, meaning that whenever Claude Code stops giving out its output, I can program it to play a sound that's on my system. (t: 130) Let me actually show you. I'm going to ask it to create a hello.md file, and then I'll switch tabs. So, you can see the hello.md file was created, and shortly after that, we heard the sound. (t: 140) And that happened because it was programmed to trigger the sound whenever Claude Code stops outputting. Just like this, we can make sure that it runs commands when it stops working on certain files, such as a SwiftUI file. (t: 150) For example, we have Prettier, which is a code formatter that many people use. It works with a lot of languages, and we can now program Claude Code (t: 160) to run these code formatters every time it edits certain files. That way, we get good code quality and good coding practices are enforced. And these types of formatters exist for every language. (t: 170) Now, making these hooks might seem a little complicated with the syntax and everything, but that is what I will show you in this video, a way that you can automatically generate the best Claude Code hooks for any project. (t: 180) Now, to clearly show you what I'm actually talking about, take a look at this SwiftUI project that I made. I've implemented this small recipe app, and I'm going to guide you on how I use the Claude Code hooks (t: 190) to make sure the code being written is clean. Every time Claude Code writes code, it's tested, and so I don't have to come back later to check for errors or fix anything that's broken. Claude Code automatically takes care of that. (t: 200) First of all, I went ahead and created this implement.md file. For the sake of this video, I just made this small task file that Claude Code could read and use to set up its own tasks. (t: 210) It's an iOS app with a local SQLite database, and it's pretty simple. If I were to implement it fully, there would have been better ways like using the bmad method, (t: 220) but for simplicity, I used this approach. I then opened up Claude Code with the dangerously skipped permissions tag so it would automatically write the code without requiring me to accept the commands. (t: 230) I told it the iOS app was already set up and that it needed to start implementing the implement and then I set up the implement.md file as the plan. It then set up its to-dos and began executing them one by one. (t: 240) Along the way, whenever it wrote code, it ran three commands after every write, specifically on SwiftUI files only. You can actually configure this in hooks, (t: 250) so that a specific hook only runs on a specific file. So let me show you what those were. Okay, so the hooks are actually implemented in this settings.json file, and if you look here, you can see the three bash commands (t: 260) that run after Claude Code has written any code. Basically, if I use edit, multi-edit, or the write tool to write to those Swift files, then these three bash commands are the ones that execute. (t: 270) First, we run the Swift format command, which formats our Swift code. It's a separate formatter that automatically formats the Swift code when you run it. We also have its configuration file here, and it uses that configuration. (t: 280) It then formats the code automatically, making sure our code is clean and properly maintained. One of the advantages of having properly clean code (t: 290) is that it's also easier for Claude Code to come back and read it. Clean, maintained code is easier for Claude Code to understand, as opposed to unmaintained code, (t: 300) which can cause some level of hallucination. Next, we have our SwiftLint code quality check. For that, we use our SwiftLint.yml file. These are just some conditions that make sure the proper rules for code are being followed. (t: 310) If they are not, then when the lint check runs, it flags any violations. In most cases, it passed, but in some cases, it did give a lint check error. (t: 320) Claude Code automatically caught those errors and used them to fix the code quality. As I mentioned earlier, every language has a similar tool specific to it. The method I'm showing you can be replicated easily. (t: 330) You can ask any model to generate these code quality checks for you. Then using a slash command that I will give you, everything can be implemented for you just like it was done for me in my settings.json file. (t: 340) The next command that runs after file editing is a build verification. You can actually run a SwiftUI app from the terminal, and if anything is broken, whether something isn't connected (t: 350) or the app isn't receiving data, the terminal outputs errors automatically. What happens then is Claude Code fetches those errors and fixes them immediately. (t: 360) This way, I don't have to come back to a broken app, screenshot the errors from this pane, and send them to Claude Code to fix, because it already performs the verification by building the app itself. (t: 370) This is why it becomes a loop. Every time it edits files, all three of these commands run. My code quality is maintained, and the app is always checked to ensure it's working as expected. (t: 380) At first, the UI it created was pretty basic, so I went ahead and gave it this screenshot right here, and told it to implement the color scheme from it. Again, there are better ways to implement a design from an image, (t: 390) but it's just a demo project, and I am just directly giving a screenshot to Claude Code here. So it went ahead and did that, and there were errors for SwiftUI as well. So I came back and decided I wanted to generate hooks for SwiftUI too. (t: 400) This one in particular, the accessibility audit, was pretty interesting, so I thought I'd show you this as well. I also asked Claude Code to implement the set of rules, and it went ahead and did that too. (t: 410) It ran these tests, and there were accessibility failures in the audit at first. Initially, it wasn't successful, but then Claude Code went ahead and started fixing those issues as well. (t: 420) And in the end, we got our app right here, fully updated. Now, how do you implement the hooks? Let me walk you through this step by step. It all started with this GitHub repository called Claude Code Rule to Hook. (t: 430) What this repository does is pretty amazing. It transforms your natural language rules into Claude Code hooks automatically. So instead of writing complex code, you can just say something like, (t: 440) this should be run after this. And it converts that into proper hooks that can run programmatically. Here's how it works. You write your rules as plain text, either directly or from a file. (t: 450) The magic happens through a slash command. For example, when I type the slash command, the system takes those natural language rules and converts them into proper hooks. (t: 460) The file contains the full implementation details of how hooks work, what tools Claude Code has available, and using all that information, it generates the exact hooks you need. (t: 470) Let me give you a simple example to make this clearer. Say you want to format Python files. You would just write the rule and give that to the slash command. It would then generate the Claude Code hook automatically (t: 480) because it understands the context and knows exactly what needs to be done. The hook would be written specifically for Python files. So whenever Claude Code edits or modifies a Python file, the formatting happens automatically. (t: 490) Now, I ran into a problem when I first tried this. The implementation was a bit outdated. It was using hooks.json, which I believe was the old format. Claude Code is a little bit more complicated than Python, (t: 500) but it's not a problem. It's just a little bit more complicated. Claude Code now looks for hooks in settings.json, so I had to update that. There were also some key tools missing from the original implementation, (t: 510) so I went ahead and added those as well. The updated file will be available in my resources for you to use. But there's another important file you'll need called tools.md. (t: 520) This file explains all the Claude Code tools and their execution points. Why is this important? Well, let's say I want to generate rules for a Swift UI project to ensure the code is clean and properly tested. (t: 530) I need to provide this file to ChatGPT when I'm generating the rules. Not everyone knows the specific rules and best practices for each language and framework, (t: 540) so instead of researching them manually, you can just ask ChatGPT to generate them. But ChatGPT needs to know about the tools and the natural breakpoints where Claude Code can run commands. (t: 550) So here's what I did. I gave ChatGPT the tools.md file and asked it to generate rules for my Swift UI project. It came back with several tools I could use, including Swift Format and Swift Lint. (t: 560) I then went back to Claude Code with the file ChatGPT had generated. I ran the slash command and placed all the rules ChatGPT generated into a rules.md file. (t: 570) Having all the rules in one place made it easy for Claude Code to read them. I also told it to perform a web search to find the accurate commands it needed to run. Next, I instructed it to implement the hooks (t: 580) in the project's settings.json file. Here, you can also specify what not to implement. For instance, ChatGPT also suggested commands for proper Git tracking, automatically committing code, (t: 590) and running CI-CD pipelines. I didn't need those for this project, so I simply excluded them. Claude Code then went ahead and implemented just the hooks I wanted, (t: 600) and that's really all there is to it. It's surprisingly simple once you understand the process. Whatever framework you're using, whether it's Python, JavaScript, or in my case, Swift and Swift UI, (t: 610) you can use these two files that I'll provide in my resources, the tools file and the rule-to-hook file. With these, you can create your own hooks for any framework you're working with. I really encourage you to try this approach. (t: 620) It ensures that the code being written is consistently clean, and that your project is properly tested at all the right points. It's a game changer for maintaining code quality (t: 630) with AI coding tools. That brings us to the end of this video. If you'd like to support the channel and help us keep making videos like this, you can do so by using the Super Thanks button below. (t: 640) And as always, thank you for watching, and I'll see you in the next one.

