---
title: <PERSON> Keeps Going Off the Rails? Here’s How I Fixed It
artist: Solo Swift Crafter
date: 2025-08-23
url: https://www.youtube.com/watch?v=c7I8TKkk544
---

(t: 0) So here's the scene. You're in the zone, right? Building out that Swift UI feature you've been noodling on all week. <PERSON> code is open on one screen, Xcode on the other. You're tossing ideas (t: 10) back and forth, shipping features, squashing bugs, and then out of nowhere, <PERSON> just drifts. The answers get vague. Suddenly it forgets that you already made that change, (t: 20) starts missing context, and honestly, the suggestions get a little random. (t: 30) If you've been there, and I think we all have, you know that feeling. It's like you're losing (t: 40) the thread of your own conversation, except the other person is supposed to be an AI that remembers everything. What actually happened? Did you hit some magic token wall? Did you just (t: 50) talk too much? Or is <PERSON> just having an off day? Well, today, I'm going to be talking about the fact that I'm not going to be talking about the fact that I'm not going to be talking about the fact that I'm not going to be talking about the fact that we're going to break down what's really going on behind the scenes. What is the context window? (t: 60) Why does it fill up so fast? And how you can actually manage it as a solo iOS dev so you stop wasting tokens, stop getting burned out answers, and keep <PERSON> locked in on what (t: 70) matters for our projects. Just real quick, I'm Daniel. I've been in the iOS world for almost (t: 80) eight years now, mostly freelancing and shipping apps for other people. But since WW25, I've gone all in on solo dev life. I've launched over five of my own apps since then, (t: 90) started building everything out in public. And yeah, I'm still figuring out this whole personal (t: 100) brand thing as I go. But hey, more on that for another video. All right, so let's just talk about what's really happening under the hood when you spin up a new Claude Code session. (t: 110) It honestly feels like cracking open a fresh moleskin, no memory, no baggage, just a clean page. You send that very first message, whatever it is, (t: 120) could be as simple as refactor this view, could be as gnarly as why is my app crashing on iOS 18? (t: 130) And boom, that exchange, your prompt plus Claude's reply drops into this thing called the context window. Here's the part nobody really tells you. With every single thing you do, Claude's quietly (t: 140) replaying the same thing over and over again. And you're like, oh, I'm not sure what's going on. And then you're like, oh, I'm not sure what's going on. And then you're like, oh, I'm not sure what's going on. And then you're like, oh, I'm not sure what's going on. And then you're like, oh, I'm not sure what's going on. The entire conversation behind the scenes, every prompt, every answer, every code snippet, (t: 150) every time you drag in a file, paste in a big log, screenshot your latest crash, it all gets layered into the same giant running buffer. You know, it's not just you chatting back (t: 160) and forth. It's every artifact you lob in there. That window fills up way faster than you'd think, (t: 170) especially on those days when you're debugging with law. And yeah, 200,000 tokens sounds massive. Like, who could possibly run out, right? But you'd be (t: 180) surprised. If you're pasting entire files, or you're in the middle of some three-hour rubber (t: 190) duck debugging adventure, you're going to hit that limit. When you do, Claude tries to play it cool. (t: 200) It'll squash the earlier stuff into a summary, do its best, and then you're like, oh, I'm not sure what's going on. It's best to keep the thread going. Sometimes it nails it. Other times, it's like Claude just kind (t: 210) of shrugs and loses the thread. And sure, a million token window is coming, and that sounds wild on paper. But if you fill it with the same random noise, all you get is, well, more room for (t: 220) mess. It's not always better, just bigger. So that's why everybody is suddenly obsessed with (t: 230) context engineering. The trick isn't to cram as much as you can into the chat, but it's to make sure that you're not just going to be in the chat window. It's the opposite, honestly. You have to get a little (t: 240) ruthless, start being picky, really decide what matters and what you're better off leaving out. And yeah, sometimes that means fighting your own urge to just dump everything in and hope for the best. That's kind of the game. (t: 250) All right, so let me pull up a free form I made. And honestly, this is probably the best way to picture what's (t: 260) happening inside Claude code when you're building as a solo dev. Just look at this flow. You start up top with your prompt, super basic, totally empty, zero tokens, no history. (t: 270) The second you hit enter, everything you throw into the session starts flowing in. Your main prompt, bits of research, any docs you fetch, files you read, screenshots you drop in, you want Claude (t: 280) to analyze. It all funnels down through Claude's plan step, and then you accept Claude's plan. It (t: 290) lands in that big orange box. And then you hit enter. And then you hit enter. And then you hit enter. And then you hit enter or stop. And then you just note what was in there right there, are long and short (t: 300) and that sort of kind of injustices, and then ее just Zack Stephens talks about it in detail on the camera. OK, so cat cat, jumping from已經 крут discounts on your own underground (t: 310) virtual level in order to look cutting edge, to even dent into your applications. All of this MV (t: 320) getting clean answers, maybe adding a doc or two, and everything feels tidy. (t: 330) But as soon as you start pulling in more files, like, hey, read this controller too, or can you scan this big log? You're suddenly flooding that window with way more context than you realize. (t: 340) If you're not careful, it's super easy to end up with a session that's packed with old logs, (t: 350) duplicate code, maybe even some screenshots you forgot about. And yeah, Claude has to process all of it every single time you send a new message. (t: 360) It doesn't just glance at the latest thing. It chews through the whole stack, top to bottom, every time. And that's where things get sticky. You start to notice that the answers get a little slower, maybe a little off, (t: 370) or Claude's suddenly looping back to issues you already fixed. (t: 380) That's usually a good thing. It's a good thing that you can assign your context window is packed with noise, old stuff you don't need or details that are just burying the signal you actually care about. (t: 390) I can't tell you how many times I've burned through a session, pasting the same log over and over just to realize Claude's working twice as hard to find the actual bug (t: 400) because I didn't keep things clean. So yeah, when you look at this diagram, it's a real reminder. Every new artifact, (t: 410) every extra file, it's all fighting for space. That's why I try to stay super intentional about what I send. The cleaner you keep this flow, the sharper your answers get. (t: 420) So let's talk about a better approach for solo devs who want to keep their sessions tight. Up at the top, you've got your prompt engineering layer, (t: 430) all your research, your product thinking, docs you've been gathering. This is the part nobody ever wants to skip, but trust me, it actually matters. (t: 440) Before I ever drop into Claude, I'll do my digging, gather links, dig through the docs, jot down what I'm trying to build, what's blocking me, (t: 450) even what I want the session to feel like. So that product management step, that's not just for teams. Even solo, I'm my own product manager. (t: 460) That happens before I start copy pasting stuff into Claude. I'm sketching out the plan, clarifying what I want from the session, (t: 470) making sure I'm not just sending a random half-baked prompt that's gonna confuse the model and waste my tokens. Now when I'm ready, that's when I actually hit enter and start a Claude session. (t: 480) All the good stuff, images, that latest Claude.md with my updated rules, files I actually need read, (t: 490) those flow right into the plan step. But here's the trick, I don't dump everything at once. I'm picking the essentials, the stuff I know will move this coding session forward. (t: 500) If I don't need a log, it stays out. If I only need a snippet from a file, I'll just grab that part. The more specific I am, the more focused and sharp Claude gets. (t: 510) When I used to treat it like a magic box and just pasted everything in, my results were chaos. Now, being a little bit ruthless about what comes in (t: 520) means my context window is basically live work. It's not clutter, it's curated. If I do need to reset or I feel things are drifting, (t: 530) I'll just clear and start again with only what's essential. And if you want Claude to sound like you or remember your quirks, toss it in SjotMD so every session starts on the right foot. (t: 540) So yeah, it's all about that intentional pre-work, get clear, gather your artifacts, and bring in just what matters. (t: 550) Once I started running my workflow this way, everything felt lighter. Claude got way more on target and my sessions just flowed. (t: 560) It's honestly less about prompt hacking and more about prepping like a real indie dev. Okay, so here's where I landed. (t: 570) And I feel like not enough folks talk about this. My workflow for Claude code is actually a little different from what you might expect. (t: 580) See, I don't treat Claude like my product manager or my architect. Or the boss of my roadmap. Not at all. I literally built myself a separate product manager agent (t: 590) outside of Claude code. That's where all my high-level plans, features, bugs, and product thinking actually live. That way when I jump into Claude code, (t: 600) it's purely my coding sidekick. That's it. No mixed signals. No asking Claude to juggle priorities. Or remember my roadmap. Just pure focused code help. (t: 610) And honestly, that's been a total game changer. Every time I start something new, maybe it's a fresh Swift UI feature, maybe I'm finally tackling a gnarly core data bug, (t: 620) I spin up a clean Claude session, I let my Claude end, I do the heavy lifting for long-term memory, (t: 630) and then I just paste in whatever's fresh. A couple of functions, a snippet I'm stuck on, that's it. If I'm deep in a debug spiral, (t: 640) I'll trim my logs, keep things sharp, and only bring in what's absolutely needed. When things start to drift, or Claude's answers start to get muddy, (t: 650) I just reset, slash clear, reload what's relevant, and move on. The only stuff Claude sees is what's needed to code, (t: 660) not to run my whole product life. The wild thing? It actually makes Claude way better at what I want it to do. I'm not asking it to be a mind reader or a strategy leader. (t: 670) Just a super focused pair programmer. All my house rules and project quirks, so I'm not repeating myself a million times. And yeah, that means way less frustration, way fewer, uh, (t: 680) why did Claude just forget everything? moments. And honestly, I just move faster. And I know, the whole product manager system probably deserves its own video. If you want to see how I keep all that straight as a solo dev, (t: 690) let me know, because it's a whole different story. I'm not saying that I'm going to do it all by myself, but I'm just saying that I'm going to do it by myself. I'm going to do it by myself. I'm going to do it by myself. I'm going to do it by myself. Because it's a whole thing in itself. (t: 700) But for now, that's how I keep Claude in the zone. Just focused, just coding, no extra noise. You know? It works. So, (t: 710) yeah. If I had to sum it up, the Claude code context window is this crazy powerful tool, but only if you actually respect the boundaries. (t: 720) The minute you start treating it like a junk drawer, just tossing in every file, log, or half-baked idea, things start falling apart. But when you do that, when you're intentional, (t: 730) when you keep it tidy and focused, suddenly you've got this super capable teammate that actually remembers what matters. And you know, (t: 740) for me, I've learned the hard way. I do all my product planning, my big picture thinking, even my bug triage outside of Claude code. That way, (t: 750) when I drop into Claude, I know exactly what I want. Help me code, help me debug, keep the session sharp. Claude's not my PM, it's my engineer. And honestly, that move alone took so much friction out of my workflow, (t: 760) but I'll save the deep dive on how I set up my product manager for another video, because yeah, it's its own little system and totally worth sharing. (t: 770) So if any of this helped, or if you've got your own weird rituals for keeping Claude in check, let me know. I love hearing what other solo devs are up to. (t: 780) Drop your comments, hacks, the stuff that actually makes a difference for you. And if you have any questions, I'll be happy to answer them. And if you want more of these devlogs, (t: 790) you know the drill, like subscribe, tell a friend, whatever feels good. Alright, that's it for now. Keep crafting, keep tweaking, (t: 800) and remember, context is power, but only if you actually use it on purpose. Catch you next time. Peace.

