---
title: "I Reverse-Engineered Claude Code: Learn These Agent Tricks"
artist: "Yifan - Beyond the Hype"
date: 2025-08-07
url: https://www.youtube.com/watch?v=i0P56Pm1Q3U
---

(t: 0) I've always wondered why does Cloud Code feel so much better than other coding agents despite having the same models under the hood. In this video, I'll show you my journey reverse (t: 10) engineering Cloud Code to find its secret sauce, show you how you can leverage this understanding to extract way more out of it, and take away principles for building your own coding agents (t: 20) in the future. Quick preview, it's a lot of prompt engineering. Quick update before we dive in, (t: 30) I'm super excited to be launching Beyond the Hype newsletter where you can receive all these pro tips and insights directly in your inbox, plus insider-only content and early access to my future (t: 40) projects. Subscribe at beyondthehype.dev. Now back to the video. When Cloud Code was first released, I got quite excited thinking it was going to be an open source project, (t: 50) but it wasn't. All we have is this bundled 9MB CLI.js file for us to analyze, so we'll dive (t: 60) straight into that. I used a tool called Webcrack here to unbundle and deobfuscate the compiled.js file. And what I got at the end was quite shocking. It was a 443,000 line long JS file for us to (t: 80) analyze. And looking into the file, there are a ton of Cloud Code's dependencies, and there's a lot of code that's been used to analyze the code. So I'm going to go ahead and start with the first one, which is the Cloud Code. This is a file that I've been using for a long time. I've been using it for a long time. So it's a very simple, simple, simple format that's been used for a long time. And it's not a lot of Cloud Code dependencies built in. But what I really want to find is the prompt, the LLM interaction Cloud Code actually makes. So I started searching for the text Cloud Code (t: 90) directly in the file. Found a couple of usage and I quickly realized that much of the prompt and LLM (t: 100) APIs are very much dynamically constructed. So there wasn't a long string piece of text that we can easily directly extract out of this. So I had to change approach a bit, but I think that it's (t: 110) going to be a very useful tool that's going to be a pain in the ass to get into the cloud code. So I've got a lot of code here. I've got a lot of code here. I've got a lot of code in here. So I had to change approach very quickly. And then I realized that Cloud Code allows people to manually set the anthropic base URL environment variable, (t: 120) which must mean that Cloud Code is actually making the LLM request directly with the original content. Otherwise, this base URL wouldn't work. (t: 130) So if we throw a proxy in there to intercept the request, we should be able to capture everything. Then moving on to approach two. (t: 140) I used a tool called ProxyMan to intercept all of the requests between Cloud Code and the anthropic APIs. And voila, everything started appearing. (t: 150) You can see for every Cloud Code interaction, there are a ton of messages that get sent to the anthropic API as expected. And just to walk you through the core parts of an anthropic API call, (t: 160) you have the user message, which contains a full message history, the system prompts, and the tool definitions, but also the response, as well as the occasional tool use. (t: 170) Let's actually go into a diagram to illustrate this more cleanly. So the client is our Cloud Code, LLM API, anthropic, and the tools that we've defined. (t: 180) At the top of the message history, you always have the system prompt that defines the agent's role. (t: 190) And then we'll have the tool definitions that's also included just below the system prompt. Then the user message actually comes in with a request. Let's say we're checking out the weather here. (t: 200) We call out to the anthropic API with this request. Then the assistant will respond with a message and likely some further tool calls to execute to get more information. (t: 210) Our client then takes those tool calls based on the definitions, calls out to the relevant APIs and tools, get the results, appends that to the history, makes another request to the LLM API. (t: 220) Then we see it responding with more requests. And then we do a little bit more. (t: 230) We do a few more rounds of this tool calls, getting the results, responding to LLM. And at one point, the LLM will decide that, okay, the work is done and return the final message to the user. (t: 240) And say here, it decided to get the weather condition in one round, get the precipitation, and then finally return the weather summary directly to the client. (t: 250) So that would be the final summary message that you see inside of Cloud Code. That's the core orchestration that Cloud Code does. So we need to expect that we areosi being serviced by this chip Ajax and we're nearly 50% done with the vite timelapse by Allahu, (t: 260) which our implementation is a long, long tally of. (t: 270) Already the trabajo you'll see here. This is maintaining the asta of the Qt version of the project to longer stream. And what it does for us is really easy to show how this works. (t: 280) So the channel names, the date of the LLM services are LOOKIN into that. Otherwise, they never show. ever add any comments. Funny that even with this in the system prompt, we still see a ton of (t: 290) comments that Cloud tend to add. How it should be doing task management, how it should be working (t: 300) on using tools, and finally, how it should be referencing code. Quite a long file. I won't walk through every line, but we'll focus on the key observations. Number one is that the core (t: 310) workflows has to be reiterated quite a few times across different sections inside the same system (t: 320) prompt. Take the to-do tool as example. That's a tool that Cloud Code uses to show you the lovely checklist as it progresses through the task. And we see that mentioned in multiple sections of this (t: 330) document inside the task management and even shown with examples of how it should be managing the (t: 340) different type of task. One, one with breakdown, one without breakdown. And then during the executing tasks, then using the to-do write tools as required. Then finally, in the tool use policy with another reminder that (t: 350) always use the to-do files to plan and track tasks throughout the conversation. And you also notice (t: 360) that these kind of modified use across the document, things like important, very important, never, must. You use this (t: 370) to implement to iterate on things that the LLM should pay special attention to. You'll notice that these descriptions aren't exactly short. So these reiterated long detailed descriptions and clearly (t: 380) stated with a ton of examples spread around this whole system prompt is how Cloud Code is able to (t: 390) get those extremely accurate function calling capabilities out of it. If we compare the mention (t: 400) of to-do written, to-do written, to-do written, to-do written, to-do written, to-do written, to-do written, to-do written, to-do written, to-do written, to-do written, to-do written, to-do written. , to the lint tool here, lint does actually get mentioned. Cloud even specifies that when you have completed tasks, you must run lint and type check commands. In my experience, Cloud Code, (t: 410) maybe only does that five out of 10 times. So not reliable at all. And this is clearly (t: 420) reflected in the fact that lint is only mentioned once compared to the to-do write, which is mentioned in multiple places so that you get different reliability, (t: 430) for these different commands. I always also add the requirements to run lint and type checks directly in the cloud.md file to have a better experience. (t: 440) Cloud Code actually has another way of further reminding the model of key tool call requirements. (t: 450) Inside our message history, it also adds this system reminder block for the model and reminding it that there is a to-do list (t: 460) and the to-do write tool to use it should it benefit. This gets reinserted after every time the task progresses and after every call to the to-do write tool. (t: 470) This is by no coincidence that cloud to-do management and planning work so well. It's precisely because of this constant reminder (t: 480) of these key tools inside the message history. So next time when you have to remind Cloud Code of something, don't feel too bad about it. (t: 490) It's not you. It's just that. Agents forget. The second observation here, all of the workflows inside of Cloud Code is pretty much defined inside the system prompt. (t: 500) Say for task management, you can see that it clearly states the style it should manage its task on and even gives examples of how it should be working (t: 510) on the task breakdowns. A simple task that didn't need it and a more complex ones that did require it. (t: 520) And inside the doing task, it clearly outlines how it should be using the to-do tools to further track and how to use the search tool. And also, well, the link check it was being reminded of (t: 530) and never to commit. So much of this is all defined directly in the system prompt (t: 540) in natural language and not hard coded inside of the CLI. This is actually quite powerful because if you need to change or vary any of the tools, you can do it. So if you need to change or vary any of the tools, you can do it. So if you need to change or vary any of the tools, you can do it. (t: 550) You just need to change the prompt. There's barely any code that you need to change. Maybe there's UX improvement that's required to deal with the user approvals, (t: 560) but to change the core flow is all very much just about the prompt. The third observation here is that formatting is still quite important. (t: 570) This system prompt, one, is very much human readable and two, having the structure actually adds more semantic meaning to the text. For example, inside the coding section, we'll see that everything is all caps. (t: 580) Important, do not add any strong and bold letters, comments, unless asked. (t: 590) So all of this formatting is actually taken in by the model when they try to comprehend this prompt. Additionally, the use of XML tags is also really powerful here. (t: 600) As shown in the previous example sections, you can use these XML tag pairs to clearly outline sections of text that the LLM should respect as a single cohesive piece. (t: 610) You can also nest these XML tags, much like how you would write any XML documents, to add more semantic meaning. (t: 620) So if you have something that's a bit more complicated that spans multiple lines, using XML tags to format will yield some great results. (t: 630) Now that we understand how the main agent works, let's dive into a more interesting feature, subagents. (t: 640) This is a feature that Cloud Code recently released to allow the main agent to trigger subagents to run specific sets of tasks with their own defined system prompts. (t: 650) Here's how it works from a high level. You'll remember that we have the system prompt at top and the tool definitions, the user input. (t: 660) And based on the first LLM call, we get a response. And together with some tool calls. And the agent here is actually just treated as another tool that gets triggered. (t: 670) Let's say that one of the tool call here actually called out to a subject agent to be created. (t: 680) Then Cloud Code will create a new message history for this subagent. The subagent gets its own system prompt. And then the tool call will contain the message it should start with. (t: 690) This user input. Is provided by our main agent. Then this continues to execute much like our previous history, right? (t: 700) You'll have the assistant messages. And then that continue with the tool calls. Then you get some tool call results here. (t: 710) This cycle continues until at the end, the subagent decided, well, from the LLM calls, that the work has been complete. And so it returns a assistant message without any further tool calls. (t: 720) Returns. To the main agent. And this final message. Gets appended back into the main agent, but not as a system message here, but as a tool call result. (t: 730) So this gets appended back. So much like the main agent will call out to say, a read tool to get the content of a file, the edit tool to change the content. (t: 740) Equally, when it calls out to the agent, it delegates a task and then gets just a summary back. (t: 750) Notice that. All of the message history in the middle is actually discarded here. (t: 760) This is actually very, very important because when you delegate tasks to subagents, the memories between the main agent and the subagent is actually not shared. (t: 770) So you want to make sure that whatever message that gets returned to the main agent, that's all the information the main agent is actually going to need to execute on that task to avoid rework. (t: 780) Then the rest of the agent will just continue like normal. Just to give you an idea of what this looks like in the actual request. (t: 790) Here we have a user message that asks the main agent to use a require analyzer agent to plan the work. (t: 800) You can see that in the response here, it creates a tool call and starts up the requirement analyzer agent with a very detailed prompt on the creation. (t: 810) Obviously this input for the subagent. The subagent call is synthesized from the cloud or MD context together with the user input. If we go on to the next message, we'll see that it again gets the same cloud or MD file and only the input. (t: 820) The main agent's memory is also not shared here and our agent will then to continue doing the work. (t: 830) It starts with a whole bunch of file reading before it continues. Our subagent then executed for a few more rounds of the request. (t: 840) In the final one, you can see the message history here on the left with the tool calls and the tool call results. (t: 850) Then it returns with a final message of the summary for the requirements. This summary gets taken back into the main agent's memory message history. (t: 860) Then just gets shoved in as the results for the tool call. So the tool call that we previously saw here and then the results gets inserted directly. (t: 870) Note that the middle. Of the message history for the subagent run is completely discarded. And then the main agent continues running like normal. (t: 880) And because it was reminded of the to do tool here and use the to do tool to create the plan, then continues executing for the whole task. (t: 890) This goes on for another 30, 40 requests before the agent finally returns the user with the final implemented feature. (t: 900) At this point, you're probably thinking at no point. You're probably thinking. At no point. In the system prompt, did it mention the requirements or the need to use subagents to work on tasks? (t: 910) So how did Claude end up knowing to use that thing? The secret is all defined in the tool section. So if we scroll past the system prompts here, we'll see tools actually defined. (t: 920) The task tool is basically the thing that manages the launch for new agents to handle complex and multi-step tasks autonomously. (t: 930) You'll see that this description is again very much detailed compared to the traditional MCP tool one line description that we're used to seeing. (t: 940) Let's actually break this down further. Here's a description nicely formatted. You'll first see that it lists all of the agents that's available to Claude. (t: 950) And again, abundant amount of examples of how it should be triggering those different agents, the tool that's available to each one of the agents. (t: 960) And again, abundant amount of examples of how it should be triggering those different agents, the tool that's available to each one of the agents. As well as the when to use and when not to use the agent with a ton of examples, including notes on how it should be extracting information directly from the agent. (t: 970) Much like the system prompt, there's a lot of detail in here. When we see that Claude Code is able to make such accurate tool calls, (t: 980) these detailed prompts and descriptions are the key to those accuracy. We also observe the similar kind of (t: 990) workflow design that we saw inside of the system prompt. Inside the usage note, it will say how we should launch the agent, how we should handle the agent returns, (t: 1000) and how each invocation is completely stateless so that there's no additional messages that get sent to the agent. (t: 1010) And that's how it should be trusting the agent, how it should also be triggering the agent, should it be defined to be used proactively. So if you've tried to create subagents yourself inside of Claude Code, (t: 1020) you'll notice that during the creation process, it will ask you whether you want manual configuration or generate with Claude. (t: 1030) The reason that the generate with Claude is marked as recommended is because Claude actively adds those detailed descriptions for your agent together with examples (t: 1040) generated based on your past conversation history to give you the best results possible for when executing those agents. (t: 1050) You should additionally note that when triggering subagents, the system prompt for Claude Code actually gets replaced with the agent's description. (t: 1060) So for your subagents to have similar kind of performance as the main agent, it needs to follow the exact same best practices the main agent system prompt follows to get the best results. (t: 1070) And that's exactly why Claude generally prefers you to generate those agent descriptions directly using Claude. (t: 1080) Because of the extra information it adds in and also the best practices it will follow. But obviously once a generates agent with sufficient detail, (t: 1090) you are more than welcome to go and modify it. But it would be great to just make sure you follow the structure there. A few other notable facts from this investigation. (t: 1100) You'll remember this slash init command called defines. That workflow, again, not hard coded, just another prompt template running. (t: 1110) If you've ever wondered why it always knows to look inside the cursor rules file and also the GitHub copilot instructions file, (t: 1120) it's defined very clearly inside this prompt. And then the file structure is also clearly outlined here. And then also you'll remember the compact command, (t: 1130) the thing that it runs when it runs out of context. This clearly outlines the work it needs to do. Again, a very much detailed prompt with (t: 1140) very detailed descriptions on the workflows, the dos and don'ts, examples. Ton of great formatting here. Now you see that there's so much prompting that goes into this thing. (t: 1150) It's also worth noting that prompt tuning is very much model family specific. For those of you who have tried switching out the Sonnet models to use other external provider models, (t: 1160) you'll realize that the tool calling capability and accuracy doesn't seem as (t: 1170) good as the actual model. It's good as Sonnet. And most of the time, this is not due to the fact that those models are less capable, but due to that these system prompts, and I mean, all of the prompt that you see here must be tuned very specifically to the model family that you're using. (t: 1180) The one that works for OpenAI might not work anywhere near as well as it does for Sonnet. (t: 1190) So whenever you are switching out models or even inside your agent build, (t: 1200) you want to make sure that you have evals and prompt tuning techniques in place so that you can iterate on those things really fast. With all that, you can clearly see great agent loops are defined by sophisticated prompts with clear instructions and workflows. (t: 1210) Prompt engineering is still very much a thing in 2025 and it will continue to be for quite some time. (t: 1220) You're now armed with all of the principles needed to start building your own coding agents. (t: 1230) If you want to learn more about the code engine, you can click on the link in the description. If you want to learn more about the code engine, you can click on the link in the description. If you want to learn more about the code engine, you can click on the link in the description. If you want to learn more about how to leverage color code to extract more value out of it, check out this workflow video just up there. Until then, happy shipping, and I'll see you in the next one.

