---
title: My Most Helpful Claude Code Tips
artist: Pocketful of Sunshine
date: 2025-07-30
url: https://www.youtube.com/watch?v=eA1FKPNaDWw
---

(t: 0) So I've been using cloud code for just under a month now while I'm reacquainting myself with the coding world. I'm still bumbling around trying to figure out what I'm doing half the time. (t: 10) So I'm constantly bumping into things that I can't quite remember how to do or are just completely new to me. That's why whenever I run into something that makes coding feel a little bit more comfortable (t: 20) or easier and often less frustrating, I grab onto it. So today I just wanted to share the top tips that have brought me real value in the last month (t: 30) and share them with you and hope that some of them can help you too. All right, let's jump right in. First up, this one isn't in cloud code, but it's perfect if you've been wondering, (t: 40) is my subscription worth it? Am I actually getting my money's worth? It's called CC usage, and it shows you how many cloud tokens you've been using (t: 50) and what the cost would have been had you been paying per token. You can view your usage by month. This definitely answers the question for me. Have I gotten my money's worth? I'm on the $20 a month plan, so yes, definitely. (t: 60) You can view it by session. For me, it breaks down into projects because I tend to work on one project at a time, but this would be useful if you're working on multiple sessions a day (t: 70) and you're trying to figure out where most of your usage might be going to. You can even view it by block. Every five hours, your token count gets reset, (t: 80) and so this is just showing you what that breakdown looks like. You can see my most recent session was active for three hours and 47 minutes, and would have cost $3.65. (t: 90) This can be really useful if you're in the middle of a session and you're wondering how close you are to your usage cap, or if you're wondering where you are in that five-hour session. Along those same lines, you can run this as a live monitor. (t: 100) So this will show you how far along you are in your session, what your usage has been, and what the projected usage is. (t: 110) It might give you a slightly false readout because I just opened a project and I just asked it to make sure everything was checked in together. And it's not going to give you a false readout. It's going to give you a false readout (t: 120) because you're not going to be able to get it. And that apparently is a heavy usage. And so it is saying, even though you've barely done anything, if you continue at this rate, (t: 130) you're projected to run out of tokens during this session. I have seen this from time to time, and I am usually nowhere close to my usage limit at the end. So do take that with a slight grain of salt. Very often, I will just shrink this (t: 140) and put it up in the corner. I can glance at it from time to time. The next tip is to open Claude inside of Cursor. This has been one of the single most useful things I can do. (t: 150) Because I am still struggling sometimes with figuring out where my files are or what kind of directory structure Claude might have made behind the scenes. It's really nice to be inside of an IDE while I'm coding. (t: 160) And the way to do that, while still getting the most real estate, is to open Claude code inside of Cursor. So to do that, (t: 170) once you open Cursor to the project that you're working on, if you hit Command-Escape, it opens Claude code for you right inside of Cursor. You can close this window (t: 180) and then you can close all the side panels and just get a full screen what feels like a regular terminal window with Claude code running in it. But of course the beauty of this is (t: 190) at any time if you want to edit a file, it's right over here in the file directory. You can even open something and be looking at it side by side while running Claude. (t: 200) You might need to update Cursor in order to get this IDE integration. But once you do that, you have access to everything at your fingertips. You can always even open up that other LLM (t: 210) in case you have questions about what exactly it is that Claude is doing, but you don't want to waste your Claude code tokens asking questions about some of the technology it's using. You can just pop over here (t: 220) and ask a simple, quick question or ask it to install a package or something that you're missing in your environment. It's like the best of all worlds (t: 230) rolled together into one. So the question that I get more frequently than anything else across all of my projects and my videos is, what am I using for speech-to-text? (t: 240) And that's my next tip. Use a speech-to-text tool. I use SuperWhisper. It's only available on Mac or iPhone right now. Sorry Windows users, (t: 250) they are working on a version, but nobody knows when that's going to come out. But if you have access to any speech-to-text tool, this will allow you to work both faster but also more organically (t: 260) because you can just worry about what you're thinking about and talk to Claude about your ideas, what you're thinking about, plus how to type and how to get it grammatically correct (t: 270) and why can't I type faster. In my opinion, it just makes it much, much easier to get your ideas directly to Claude. The next thing that I find helpful (t: 280) is to always use Claude in planning mode. If you hit Shift-Tab twice, you will get the message down here that plan mode is on. This just allows Claude to take everything (t: 290) that you've said, make a plan, and present it to you and say, is this what you wanted? Is this complete? Before you continue on with coding. I can't tell you how many times I have said, (t: 300) oh, I need something that does this, this, and this, and I hit Enter, and then I remember, oh, there was a whole second half of that sentence that I forgot. Meanwhile, Claude is off coding, (t: 310) and by the time I come back and say, oh, and can you also do this, there is inevitably some refactoring that needs to take place. If I turn planning mode on, (t: 320) it just saves a ton of back and forth and re-coding, which always has the potential to introduce more errors. So now is a great time to try something that I literally just learned. (t: 330) So this Claude.md file is kind of like the brains behind your entire project. It sets the field and tells Claude how to behave if you have any standards or (t: 340) tools or anything that you want to make sure that the project always follows. And you can always go in and edit this, but if you're in the middle of something and you realize you need to make a project (t: 350) level change, you can always add a memory, which will effectively go into this Claude.md file, by using the hashtag. So if you hit the hashtag, you can add a memory. (t: 360) And so I might want to say something like, always make sure that projects start out with planning mode on. So once you hit enter, it's going to ask where exactly this memory is saved (t: 370) and stored. Project memory checked in, project memory get ignored, or user memory. I think I want to make this at the project level and check it in. Done. (t: 380) Got it. And you can see it down here under the best practices. Always make sure that projects start out with planning mode on. I don't have to remember that now. Alright, so my next tip. (t: 390) As much as I love Super Whisper, sometimes it is just easier to take an image of your project and send that into Claude. And ask for a change from the image. (t: 400) This project runs a simple little dashboard that analyzes my credit card statements and looks for subscriptions. And then displays just a simple little dashboard. So here is my subscription dashboard (t: 410) and you can see that it has pie charts and bar graphs. Now, if you're a Mac user, you're used to hitting Command V to paste. In order to paste an image (t: 420) into Claude, you will need Control V. I'd like all the pie charts to use the same colors and I'd like them to use the colors that the upper left quadrant pie chart has. And now all the pie charts (t: 430) have consistent colors. Much nicer. This leads me to my next tip. This one may sound obvious, but make sure you're committing your changes as you work to some kind of (t: 440) repository. I use Git. Well, I should say I'm using Git now. For quite a while I did not have it hooked up and I have definitely paid the price. So it is worth (t: 450) saying again, as you're working, make sure you're committing, make sure you're checking your changes in. And Claude makes this super easy. I can just ask it, can you make sure I have everything checked in? (t: 460) And I'm pretty sure it's going to tell me this project has never been checked into Git. Oops. Yikes. Can you create a Git repository for me please? This is one of my favorite things about Claude. (t: 470) I used to kind of fight with Git. I could never quite remember the right commands or which buttons to push when. And this walks me through step by step making sure that everything is (t: 480) where it needs to be and in fact the comments are better than anything I'm going to remember to put in. I wouldn't necessarily know what the changes were to do that. Ta-da! (t: 490) We are now successfully backed up. My next tip is of course sub-agents. As of this recording, sub-agents were just released by Claude Code and they really are (t: 500) as fantastic as everybody says. They are worth an entire video unto themselves. I have one of my favorite videos on this channel and a lot of people have really great videos. (t: 510) But real quick, a sub-agent is just something you can create in Claude that has a specific task, a specific job to do. You give it a definition and you can either call it by name (t: 520) or anytime that Claude realizes there is something to be done that falls under the definition of this sub-agent, it will make sure it runs the sub-agent using all of the (t: 530) directives and information that you have given the sub-agent. Let's do a real quick example. So you create an agent by running the slash agents command. We're going to create a new agent. (t: 540) You can create these either at the project or the user level. I want this at the project level. I do want it generated with Claude and then you get to give it a description. And this is where you get to (t: 550) free form just describe what the agent can do. I'd like you to create an analytics inside agent for me. I want this agent to look over the subscription data after it's been identified and give recommendations to the user (t: 560) as to what actionable steps might be taken and maybe what the best steps to take might be. You can give much more detailed information here. You can tell it coding practices or coding standards, but (t: 570) you can also give just a broad overview and Claude will do a fantastic job of taking what you've said and analyzing it and creating something really, really (t: 580) solid out of it. So this step it's asking what tools we want to allow the agents. You can give it all tools. You can restrict it to read only or you can go down (t: 590) into advanced options and select individual tools that you want to allow your agent to use or not use. In my case, I'm going to keep it simple and allow (t: 600) the agent access to all the tools. Just hit continue and then I get to pick a color for the agent. I'm going to give him orange. It's going to give you a final overview of your agent and I'm going to say (t: 610) yes. And then it shows you a list of the available agents. I've already built a couple of other agents in here, but we can see the subscription analytics advisor. Hit escape to (t: 620) exit out of there and then you can ask Claude to run your agent or as you're working with Claude, when it identifies that this agent is needed, it will automatically run it. Can you run the subscription (t: 630) analytics agent? And so in the midst of your coding or Claude's coding, you might see these blocks in different colors and that's where you can see it is running (t: 640) the agent that you've built. So that's the list for today. I'm still figuring out everything as I go, but these tools and tricks have genuinely helped me get more comfortable (t: 650) and productive using Claude. If you've got any tips that this sort of newbie programmer should know about, please drop them in the comments below. I would love to hear about them (t: 660) and I'm sure everybody else would too. Alright, that's it for today. Thanks for watching and I'll see you next time.

