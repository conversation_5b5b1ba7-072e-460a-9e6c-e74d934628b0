---
title: 8 MCP Servers That Make <PERSON> Code 10x Better
artist: <PERSON>
date: 2025-08-21
url: https://www.youtube.com/watch?v=Gqh_KdHP1Xk
---

(t: 0) Over 90% of all MCP servers are complete overhyped garbage. But some of them are not only worth it, they can be complete game changers. And I do not say this lightly. So after testing over 100 of (t: 10) them, here are the only 8 MCP servers that I actually use to build apps even faster. (t: 20) But first let me save you from making the same expensive mistake that I made when I started using MCP servers. Because what no one explains about them is that the more of them you install, (t: 30) the dumber your AI becomes. I know this sounds backwards because everyone thinks more tools equals smarter AI. But here is what's actually happening. Because every MCP server you (t: 40) add is like giving your AI yet another tool to choose from. Imagine asking someone to fix your bike but instead of giving them a toolbox with 8 essential tools, you just dump 100 tools on the (t: 50) floor. You don't even need to fix the bike. You just dump 100 tools on the floor. You can just throw them into your bike and you're done. You just dump 100 tools on the floor. You just dump 100 tools on the floor. They will spend more time figuring out which tool to use than actually fixing your bike. That is exactly what happens with <PERSON> and <PERSON>tGPT too. Because every (t: 60) MCP server needs instructions in the context window or chat when you start. But new research now shows that this is actually really bad for AI. It's like trying to focus on a specific task (t: 70) while browsing social media. You're just pumping useless information into your brain and that is kind of what happens too when you use too many MCP servers in your AI. So if you want your AI to (t: 80) stay focused, remove all distractions. I stripped my list down to these 8 MCP servers and even then (t: 90) I don't use all of them in every project. I turn them on and off because the fewer tools you give your AI, the more focused it stays. So now let's look at my full list. (t: 100) Hey what's up guys, my name is Rob and I've been a coder for over 20 years. But now I teach people how to build their ideas with AI in ways that even non-technical people can understand. I do this (t: 110) here on YouTube and I'm going to show you how to do it. So let's get started. And that is where this MCP server comes in. Because this turns your AI into a data gathering (t: 130) machine. AAPIFI is a marketplace for web scrapers which you can think of as little robots that go (t: 140) all over the place. And they're actually very easy to use. So let's get started. the internet and grab information from any website that you want and here is the really crazy part because this MCP server doesn't just help you pull the data into your app it actually (t: 150) makes accessing the full documentation of every web scraper available on this platform very easy (t: 160) it can also help you debug them when things go wrong or check the status on recent jobs I personally use this to gather data on my own YouTube competitors because I can literally pull (t: 170) in all the data that I need to get the data to the right place. So let's get started. all the data and this MCP server will allow you to make this work AAPIFI is not the cheapest platform in the world but it is very reliable if you choose the right (t: 180) web scrapers the link for this and all the other MCP servers is in the description down below (t: 190) next up number seven and this one is going to save you a bunch of hours of pure frustration because you know the feeling when you ask your AI to do something (t: 200) and it just cannot figure it out sometimes it's as simple as setting up a new project or adding a new service into your application and that is because AIs are trained on old data (t: 210) sometimes this data is just a few months old and sometimes it's literally a few years old which is pretty wild so when you ask to set up a Next.js project with Tailwind it can easily get confused (t: 220) because it doesn't know that Tailwind is now in version 4 and it tries to use outdated documentation it's extremely frustrating and this is where ref comes in you might have heard about context 7 which (t: 230) is a very similar but it's very inefficient for the same reason that I explained earlier it pulls in way too much unnecessary data and ref fixes this by reducing the data that it pulls in by (t: 240) what they claim is up to 80 percent this will keep your chat or token context window clean (t: 250) and your AI focused it also supports nearly every framework every library and every tool that you can think of all in their most current and most accurate versions since I installed it (t: 260) I use it constantly there's just one problem ref isn't free you get 200 free requests per month (t: 270) but even if you go over it's nine dollars a month for every 1000 other requests I know it's another subscription which sucks but it will save you time I honestly don't want to live without it anymore (t: 280) number six blew my mind because did you know that AI can literally control your browser that means (t: 290) clicking buttons filling out forms taking screenshots and testing your app all for you there are other solutions like playwright but they often use their own browser (t: 300) instance which means that it doesn't have the same cookies and logins that are already active in your own browser browser mcp fixes this by using your actual browser with all your information (t: 310) available this might sound scary and it can be so do be careful but here is why it matters I could literally tell it to go to x.com then go to a specific profile using my own browser and then (t: 320) log in that is already saved in the browser and then read their tweets and summarize them for example or I could ask it to go to my local development server and test if the feature that (t: 330) it just implemented number one actually looks the way that it should and number two actually works (t: 340) it can then click through read console errors and actually continue iterating over this new feature or problem until it is fixed it can do all this while you go grab a coffee so it's really kind (t: 350) of like having your own quality insurance tax and then number five I ignored for way too long since it came out which I now agree was a big (t: 360) mistake because again the amount of focus that your ai has depends on how much context you have stuffed into the current chat sequential thinking outsources complex thinking to an external threat (t: 370) think of it like this imagine you want to figure out the best hotel for your upcoming vacation you (t: 380) spend hours going through different options and by the end you have wasted hours your brain is completely fried and you don't even feel like booking something anymore that's what happens to your ai too when you ask it to break down complex things instead imagine you have a (t: 390) personal assistant and you can tell her something like hey think this through and come back with an (t: 400) answer then sequential thinking gives you the outcome of all this sequential thinking without wasting your ai's memory in the process your ai model just sees what it has to do (t: 410) next so in order to be able to figure out the context and what the data is saying it has to figure out which way to go and then you can find out the context without figuring it out and wasting (t: 420) context or chat memory in the process and once again this will lead to a cleaner context a more focused ai and better results and this is completely free by the way one ai coding tool (t: 430) that's really good with mcp servers is augment code which sponsors this part of the video i'm myself a heavy user of augment code and one of my favorite features (t: 440) Notion, Superbase, Stripe, Sentry and many more. But even adding new ones could not be easier because most MCP servers give you (t: 450) a snippet that looks something like this. And from here, all you have to do is copy it, then go into the augment code settings, (t: 460) find the import from JSON button, click it, paste in your snippet and click on import. And just like that, (t: 470) all the tools are ready to use. But beyond that, Augment Code is simply a really good AI coder and they even just added GPT-5 on top of Sonnet 4. (t: 480) Their other big feature is that they have a context engine, which means that Augment Code always knows exactly what is going on in your project, even if you open a brand new chat. In my experience, (t: 490) Augment Code is a really solid AI coder, one of the best out there. So download Augment Code today and test it for yourself because it's completely (t: 500) free to start. And then number four, it is actually a plot twist because GitHub has their own MCP server, which you absolutely should not install. I know, (t: 510) weird thing to include in a list of top MCP servers that I use, but here's the thing. GitHub already made something better. And that's the GitHub CLI app. (t: 520) Every AI knows how to use this perfectly and installing the MCP server is like buying a Spanish English dictionary for someone who (t: 530) already speaks both languages. Your AI can already create repositories, manage issues, commit files and push them all without this MCP server. (t: 540) So instead just save yourself about 30 minutes or so that it would probably take you to set this up as a non-technical person and use the GitHub CLI instead. And then number three is one of my favorites, because listen, (t: 550) if your app doesn't make money, it's not a business, it's an expensive hobby. And if you are anything like me, you do like making money. And that is exactly where the Stripe MCP server comes in. (t: 560) Which is a must if you actually use Stripe to collect payments, because it can create products for you, set up subscriptions, (t: 570) manage customer, handle refunds, pull revenue data, everything that you can do in the Stripe dashboard, this thing can do too. But the killer feature is actually debugging. For example, (t: 580) I actually have a discord bot for my AI coding blueprint students where people can self verify their purchases. They just have to enter the email that they used once, (t: 590) while making the purchase, and then the bot will verify it. But when I first launched this bot, some students couldn't verify despite having paid for it. (t: 600) Pain in the ass to figure this out. But then I used the Stripe MCP server to debug this. Turns out I had archived some old products. (t: 610) So the verification bot could no longer see these purchases, but the MCP server figured out alternative ways to verify them. The entire process took like 10 to 15 minutes and I watched the magic happen. (t: 620) The fact that this MCP server also gives you the most accurate Stripe documentation so that your AI knows exactly what to do was just the (t: 630) cherry on top. And number two is probably my most used MCP server ever. Superbase is your apps brain, all your user accounts, (t: 640) every piece of data, every logging, it all lives there. And you might think, but I don't know anything about databases and authentication. Perfect because you don't need to anymore. With the Superbase MCP server, (t: 650) AI can handle everything. It can create tables, set up authentication, manage users, and even add fake users and fake data so you can test the app. (t: 660) You will never need to look at your Superbase dashboard again, which let's be honest, can get quite overwhelming. (t: 670) But the best part about this MCP server is that when something breaks, this server doesn't just say, oh, yeah, there's a database error. It tells you what broke, why it broke and how to fix it. (t: 680) It can do security checks, security recommendations. It can see database logs and it can find queries that failed. So if your app is trying to fetch something that doesn't exist, (t: 690) this MCP server can find it. But there's one thing that you need to know. Superbase is not the cheapest. It's really good, (t: 700) but their free plan kind of sucks. And their paid plan starts at $25 a month. And then number one is a brand new MCP server that is currently in beta. (t: 710) Install it fast. Especially if you don't know how to publish your products quite yet. I get people asking me about Amazon's AWS or running their own servers. (t: 720) I don't deal with any of that. I use Vercel and I tell my students to use Vercel too. And their MCP server puts your apps on the internet. Not in a week, not tomorrow, but right now. (t: 730) You just build something and then you publish it. It's life and it's completely free because Vercel has a very generous free plan (t: 740) that I only upgrade. I upgraded from because I felt bad not to. I have so many projects there and I still only pay $20 a month. It can get expensive if you have a huge project, (t: 750) but most of my projects are relatively small. And here is why it's number one on this list. Production, debugging and live documentation. (t: 760) When users complain about weird bugs and you're not super technical, you don't always know what happened. But the Vercel MCP server can see why deployments fail (t: 770) for example, when you try to publish a new version and it doesn't work, it can read the latest documentation and it can even help you use AI in your app (t: 780) with Vercel's AI gateway automatically. I highly recommend that you do not only use this MCP server, but also Vercel itself. And if you're still here, there's a final bonus one that might make everything (t: 790) that I just told you irrelevant. Because remember how I said that having too many MCP servers can break your AI? (t: 800) Well, there might actually be a way around this. Because instead of installing a hundred plus MCP servers and confusing your AI, you can just install one. (t: 810) Think of it like this. Imagine instead of carrying 100 tools, you just carry a phone in your pocket that can call a specialist anytime you need it. This way, your AI stays fast because it has only one tool to think about. (t: 820) And when it needs something, it picks up its phone and your AI memory just stays clean. (t: 830) It stays focused. But you still have unlimited power to countless MCP servers. Here is the thing, though. I have just begun testing these tools and they might not end up being that great. (t: 840) But leave a comment down below if you would like me to take a deep dive into this service and maybe a few more to check out my AI coding blueprint where I (t: 850) teach my students in exactly the same style as I do in these YouTube videos. But with direct access to this guy and an exclusive student community. (t: 860) I hope to help you when you get stuck. The link is in the description down below.

