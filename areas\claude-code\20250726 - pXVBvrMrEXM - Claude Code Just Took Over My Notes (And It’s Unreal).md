---
title: <PERSON> Just Took Over My Notes (And It’s Unreal)
artist: Rapid Ruby
date: 2025-07-26
url: https://www.youtube.com/watch?v=pXVBvrMrEXM
---

(t: 0) So I previously had all my notes and projects stored inside of ChatGPT as projects. I'd upload (t: 10) files and set context in the instructions. This approach worked but it had some downsides. The main issue is that all of your projects notes documentation is sitting inside of ChatGPT's (t: 20) platform instead of on your own computer and I also find it challenging to share context across (t: 30) projects. So if I wanted to ask it questions about all of the specific startups I'm working on, it didn't really work well across those projects. So I rebuilt my workflow from scratch using (t: 40) Cloud Code and Obsidian for Mac. I want to show you how I put it together and how you can as well. So it starts with Obsidian and I've just gone ahead and created a fresh (t: 50) fold. A vault is essentially a folder where you can have loads of documents and they're all written in markdown files. So you can create subfolders, add notes and you can do like graph stuff where (t: 60) you link them all together as well. So I'm going to be back in a second when I add a few projects (t: 70) to this vault. So I've added a few projects here. We've added RapidRuby, Happy and a general folder to put general notes. So I've got technical overview and the core (t: 80) technologies that I use for most of my apps. Now we'll take a look at setting up Cloud Code to work with this. So if you open your terminal and go to wherever your vault is and then just (t: 90) type Cloud, you'll first see a screen asking if you trust this folder or not and you can hit yes (t: 100) proceed. And next you'll want to run forward slash init to set up your cloud.markdown file which just instructs Cloud about the project and where it can store project specific information. Now you'll see (t: 110) Cloud comes back and it's going to show you the project that you've created. So I'm going to go back and run the test. So you can see that Cloud comes back asking you to create a cloud.markdown file with a full set of comprehensive instructions on how this project is set up and what it's for. Now that Cloud (t: 120) is set up we can put it to work and we can say Cloud create me a readme file in the root of this (t: 130) project that summarizes all the subfolders which are projects and links to them. (t: 140) And you'll see Cloud's come back. It's got our readme file ready. And we'll just hit yes here and accept that change. And if we come back to Obsidian and click into readme, you'll see that it's got all of our projects listed and introductions for each of those and also (t: 150) just the vault structure getting started and just information that's valuable about this project. (t: 160) What becomes really valuable here though is the fact that we can now use Cloud with all of our context in place. So we can ask Cloud things like, (t: 170) what's the most valuable thing I can work on this week? And as you can see, it's come back and it's told me based on the information it has and the projects that the most valuable thing I can do (t: 180) this week is marketing for happy.team to add a new customer and do some customer acquisition. The next cool thing we can do here is set up custom Cloud commands in this project that work (t: 190) with our notes. Create me a new Cloud command in .cloud forward slash commands forward slash readme.md. (t: 200) This Cloud command is to make a summary in the readme file of all the existing projects. So (t: 210) as we add new folders and run this command, it should update the readme file to reflect the new projects we've added or any changes to existing projects. (t: 220) And as you can see, it's going to write a Cloud command for us and is actually pretty comprehensive. So it's going to scan through, it's going to exclude any hidden folders for each folder. (t: 230) And it's going to create a new document. So we can go ahead and create a new document. And we can also create a new document for an overview document, extract the first paragraph (t: 240) or key description from each update the readme file with a structured list of all projects and their descriptions. And this is cool preserve any custom content in the readme that appears before (t: 250) or after the project section. This is cool. We'll go ahead and accept that. And then we can restart Cloud and we just type forward slash readme. And we can auto complete that to run our readme command. (t: 260) And that's it. So if you want to see more or a part two of this video, let me know in the comments and I'll share that with you. This setup is awesome. And it means I own all my data in my (t: 270) own machine. And I can still use AI tools like Cloud Code to help me work with it all and save (t: 280) me a bunch of time. I'll be sharing a bunch more Cloud Code productivity videos soon. So if you want to see more, please like and subscribe. And check out the next video here.

