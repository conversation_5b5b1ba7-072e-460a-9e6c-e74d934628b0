---
title: Keynote: Rewiring How We Learn: The Power of an Experimental Mindset | SXSW EDU 2025
artist: SXSW EDU
date: 2025-03-04
url: https://www.youtube.com/watch?v=BCIT1L5muoQ
---

- [00:00:00](https://www.youtube.com/watch?v=BCIT1L5muoQ&t=0) - <PERSON> Reviewer Reviewer Hello, everyone. I know this is a very cliché thing to say, and they tell you, never start a keynote by saying this, but I'm actually really, really happy to be here today. And the reason why is because not so long ago, I was actually terrified of public speaking. And by terrified, I mean I would have stomach cramps and nightmares for weeks before any kind of presentation. But through experimentation, iteration,

- [00:00:30](https://www.youtube.com/watch?v=BCIT1L5muoQ&t=30) - I was actually terrified of public speaking. And by terrified, I mean I would have stomach cramps and nightmares for weeks before any kind of presentation. But through experimentation, iteration, making mistakes and learning from them, I got to a point where, honestly, this is still a bit scary, but the good kind of scary. And this is what we're going to talk about today, how being more experimental can completely transform the way we approach challenges and we learn. And we're going to start by talking about children. Children love solving problems. Give them a puzzle or a riddle, and they can't help themselves.

- [00:01:00](https://www.youtube.com/watch?v=BCIT1L5muoQ&t=60) - the way we approach challenges and we learn. And we're going to start by talking about children. Children love solving problems. Give them a puzzle or a riddle, and they can't help themselves. Their brains are instantly going to get to work and trying to find patterns and make connections and really try to crack the code. Their prefrontal cortex, their hippocampus, their dopaminergic system are all going to start working together. It's like they say, to the point of a Tell me, give me more. I'm telling you, but to the point of thousands. Refund yourself after each question. It's not just recalibrating,

- [00:01:30](https://www.youtube.com/watch?v=BCIT1L5muoQ&t=90) - their dopaminergic system are all going to start working together. It's like they say, to the point of a Tell me, give me more. I'm telling you, but to the point of thousands. Refund yourself after each question. It's not just recalibrating, it'sirim затем, rewiring and rewriting time. And then, most importantly, your brain starts using a tool that allows you to recognize what is going on. A fewiments нужive are English, anoling, ascular, aesthetics, both of these things. I think we all have them in mind. And if you want to know what it is that always reminds you of, you should begin by telling yourself that you actually are ask a lot of questions. Studies show that on average, children ask more than 100 questions every day. They will ask you, why is the sky blue? They will ask you things like, what

- [00:02:00](https://www.youtube.com/watch?v=BCIT1L5muoQ&t=120) - you should begin by telling yourself that you actually are ask a lot of questions. Studies show that on average, children ask more than 100 questions every day. They will ask you, why is the sky blue? They will ask you things like, what happens if I drop this? Or things like, why do you have to go to work? And every time you provide an answer, they will come up with even more questions. But then, something starts changing. By age five or six, the questions start to slow down. By middle school, children

- [00:02:30](https://www.youtube.com/watch?v=BCIT1L5muoQ&t=150) - you provide an answer, they will come up with even more questions. But then, something starts changing. By age five or six, the questions start to slow down. By middle school, children start worrying more about getting things right rather than figuring out things on their own. And little by little, progressively, they start replacing their focus on curiosity with a focus on performance. Instead of wondering, what happens if I try this? They start worrying, what if I get it wrong? So, what's going on here?

- [00:03:00](https://www.youtube.com/watch?v=BCIT1L5muoQ&t=180) - their focus on curiosity with a focus on performance. Instead of wondering, what happens if I try this? They start worrying, what if I get it wrong? So, what's going on here? To understand this, we need to go back to the very definition of success. Success. This is something that we all want. We praise it. We admire it. We also spend a lot of time trying to make it work. We want to make it work. We want to make it work. We want to measure it. We have OKRs, KPIs, performance reviews. It's as if we were all looking at a giant leaderboard where we're always asking, who's doing better, faster, bigger work? So,

- [00:03:30](https://www.youtube.com/watch?v=BCIT1L5muoQ&t=210) - measure it. We have OKRs, KPIs, performance reviews. It's as if we were all looking at a giant leaderboard where we're always asking, who's doing better, faster, bigger work? So, what is this success that we're all so ardently chasing? If you open a dictionary and you go to S, success, the definition that you're most likely to find is something like this. Reaching a desired outcome. And the reason why it's important is that this is the definition that we seem to all have agreed on collectively as a society. But I'm a scientist, so I hope you're going to indulge me for a moment as we break this down and try to understand exactly

- [00:04:00](https://www.youtube.com/watch?v=BCIT1L5muoQ&t=240) - Reaching a desired outcome. And the reason why it's important is that this is the definition that we seem to all have agreed on collectively as a society. But I'm a scientist, so I hope you're going to indulge me for a moment as we break this down and try to understand exactly how this definition is working and what it implies. So, first, you have reaching. So, there's the idea of movement, of progress. Towards what? Something we desire. Something that we think is going to be good for us. And that something is an outcome, which means that success implies the idea of reaching a specific destination. This seems like the only obvious definition of success, but it's not. This

- [00:04:30](https://www.youtube.com/watch?v=BCIT1L5muoQ&t=270) - what? Something we desire. Something that we think is going to be good for us. And that something is an outcome, which means that success implies the idea of reaching a specific destination. This seems like the only obvious definition of success, but it's not. This definition of success is based on linear goals. They imply that in order to be successful, you need to know your desired outcome. You need to know your desired outcome. You need to have a clear vision and a clear plan. And so, what do you do in order to be successful in this society? Well, first, you define the desired outcome, and then you work really hard to get there. This sense of certainty is very reassuring. There's only one problem.

- [00:05:00](https://www.youtube.com/watch?v=BCIT1L5muoQ&t=300) - to have a clear vision and a clear plan. And so, what do you do in order to be successful in this society? Well, first, you define the desired outcome, and then you work really hard to get there. This sense of certainty is very reassuring. There's only one problem. We don't live in a linear world. We live in a nonlinear world. There are market trends that keep on shifting, technology that keeps on evolving, and global events that can change everything overnight. And so, we try to go from point A to point B to point C, but instead, we find ourselves lost in this very complex interconnected web

- [00:05:30](https://www.youtube.com/watch?v=BCIT1L5muoQ&t=330) - There are market trends that keep on shifting, technology that keeps on evolving, and global events that can change everything overnight. And so, we try to go from point A to point B to point C, but instead, we find ourselves lost in this very complex interconnected web with unpredictability at every turn. And what happens when we try to reach our desired outcome? But somehow we don't get there. We blame ourselves. And maybe we start comparing ourselves to others who seem to be more successful, who seem to have it all figured out. And so, we feel like, you know what? Those failures, let me hide them. Probably don't need to share

- [00:06:00](https://www.youtube.com/watch?v=BCIT1L5muoQ&t=360) - But somehow we don't get there. We blame ourselves. And maybe we start comparing ourselves to others who seem to be more successful, who seem to have it all figured out. And so, we feel like, you know what? Those failures, let me hide them. Probably don't need to share them with anyone. When you focus on linear goals in a nonlinear world, the only possible result is overwhelm and burnout. You know who has a completely different approach to success? Scientists. For a scientist, success is not reaching a desired outcome. Success is learning something new. Whatever the outcome,

- [00:06:30](https://www.youtube.com/watch?v=BCIT1L5muoQ&t=390) - You know who has a completely different approach to success? Scientists. For a scientist, success is not reaching a desired outcome. Success is learning something new. Whatever the outcome, whatever the result, they're able to look at it without self-blame or self-judgment. And today, I want to convince you to start turning your studies, your teaching, your work, and maybe your entire life into a giant laboratory. I want to convince you that curiosity is more powerful than certainty. And I want you to start imagining how everything could

- [00:07:00](https://www.youtube.com/watch?v=BCIT1L5muoQ&t=420) - work, and maybe your entire life into a giant laboratory. I want to convince you that curiosity is more powerful than certainty. And I want you to start imagining how everything could change if we all decided to approach any challenge as an opportunity for growth and discovery. And I'm going to do that by sharing three very simple changes that you can make. The first one is to develop an experimental mindset. When a scientist gets an unexpected result, they don't go like, shame, shame, shame. I'm such a bad scientist. What did

- [00:07:30](https://www.youtube.com/watch?v=BCIT1L5muoQ&t=450) - The first one is to develop an experimental mindset. When a scientist gets an unexpected result, they don't go like, shame, shame, shame. I'm such a bad scientist. What did I do wrong? No. Instead, they go like, huh. That's interesting. What's going on here? What can we learn from this? They go, no. I did this. They say, eh. And this is because scientists understand that failure is an inherent part of learning. We need failure in order to learn. The interesting thing is that embracing failure as part of learning is actually aligned with how your

- [00:08:00](https://www.youtube.com/watch?v=BCIT1L5muoQ&t=480) - failure is an inherent part of learning. We need failure in order to learn. The interesting thing is that embracing failure as part of learning is actually aligned with how your brain naturally works. Or should I say how your brain would rather work if you let it instead of clinging to linear goals. Your brain works based on something neuroscientists call the perception action cycle. Sounds a bit fancy, but it's very simple. First, your brain is going to observe something in the environment, some data. And based on that, it's going to make a prediction.

- [00:08:30](https://www.youtube.com/watch?v=BCIT1L5muoQ&t=510) - Your brain works based on something neuroscientists call the perception action cycle. Sounds a bit fancy, but it's very simple. First, your brain is going to observe something in the environment, some data. And based on that, it's going to make a prediction. Sometimes the prediction is correct. And this is great. Sometimes the prediction is wrong. And that's okay, too. As long as the prediction was not so wrong that you're dead, your brain is going to take that new data, that new information, and is going to use it to make a new prediction. And this is how your brain learns.

- [00:09:00](https://www.youtube.com/watch?v=BCIT1L5muoQ&t=540) - As long as the prediction was not so wrong that you're dead, your brain is going to take that new data, that new information, and is going to use it to make a new prediction. And this is how your brain learns. The one thing that makes it a little bit more complicated, though, is that your brain is also designed for survival. It's constantly optimizing for your survival. And that means that your brain is always going to try. It's always going to try to reduce uncertainty as much as possible. And that makes sense from an evolutionary perspective. If you think about our ancestors back in the jungle, the more you knew whether that was where are the resources or what is that weird noise in the bushes, the more you knew, the

- [00:09:30](https://www.youtube.com/watch?v=BCIT1L5muoQ&t=570) - And that makes sense from an evolutionary perspective. If you think about our ancestors back in the jungle, the more you knew whether that was where are the resources or what is that weird noise in the bushes, the more you knew, the more likely you were to survive. But I think we all agree here that us, modern people, we're not going to survive. We're not going to survive. We're not going to be human beings. We want more than just survive, right? We want to thrive. And in order to do that, what we need to do is to replace that fear of uncertainty with curiosity instead.

- [00:10:00](https://www.youtube.com/watch?v=BCIT1L5muoQ&t=600) - We want to thrive. And in order to do that, what we need to do is to replace that fear of uncertainty with curiosity instead. You can still use your very powerful perception action cycle, but instead of trying to use it to reduce uncertainty as quickly as possible, you can use it to embrace that uncertainty, explore it, and see it as an opportunity to grow and learn. The reason why scientists are so good at this, being more curious and experimental, is not because they're better or smarter than everybody else.

- [00:10:30](https://www.youtube.com/watch?v=BCIT1L5muoQ&t=630) - The reason why scientists are so good at this, being more curious and experimental, is not because they're better or smarter than everybody else. It's because they've been trained to do so. So anyone who has taken science classes or is teaching science classes will remember the cycle, the experimental cycle. It's very simple. It always starts with observation. It's very simple. It's very simple. So you observe. What is the current situation? Then you formulate a hypothesis. You ask what could be different here? Then you collect that data. And finally, you analyze the results. And you check whether your prediction was correct or wrong.

- [00:11:00](https://www.youtube.com/watch?v=BCIT1L5muoQ&t=660) - Then you formulate a hypothesis. You ask what could be different here? Then you collect that data. And finally, you analyze the results. And you check whether your prediction was correct or wrong. Sounds familiar, right? And you use that prediction in order then you use that information in order to make a new prediction and restart the experimental cycle. The very important aspect of this is that you can't just do it. The very important aspect of this that you can apply in your work and in your life in general is that in order to grow through experimentation, you always need action and reflection. Reflection and action. This is what will make you be able to grow through them, create those growth loops instead

- [00:11:30](https://www.youtube.com/watch?v=BCIT1L5muoQ&t=690) - general is that in order to grow through experimentation, you always need action and reflection. Reflection and action. This is what will make you be able to grow through them, create those growth loops instead of just repeating the same thing over and over again. When you start applying these equations, trusting that failure is always part of learning, you're going to get better at that. You're going to get better at that. You're going to get better at that. You're going to get better at that. You're going to get better at that. You're going to get better at that. And knowing that you always need to pair action with reflection and reflection with action, these can become a sort of almost like an operating system for you to have an experimental mindset.

- [00:12:00](https://www.youtube.com/watch?v=BCIT1L5muoQ&t=720) - And knowing that you always need to pair action with reflection and reflection with action, these can become a sort of almost like an operating system for you to have an experimental mindset. This will allow you to grow and learn even if you don't have a specific destination. The second change you want to make is to embrace systematic curiosity. Yes. By systematic curiosity, I mean being curious about absolutely everything. Even the uncomfortable stuff we would rather not look at too deeply. And for this, I'm going to use an example of something that we have all experienced.

- [00:12:30](https://www.youtube.com/watch?v=BCIT1L5muoQ&t=750) - Even the uncomfortable stuff we would rather not look at too deeply. And for this, I'm going to use an example of something that we have all experienced. And the reason why I picked that example is because this is something that we're all very happy to talk about at a conceptual level, but we rarely feel comfortable admitting to in public. And that thing is procrastination. And that thing is procrastination. Everybody's like, yep, that's me. We've all been there. Right? You're staring at a task, and you feel like, yeah, now is the right moment to reorganize my bookshelf. Or I feel like I'm going to do a little, like, deep clean of the kitchen.

- [00:13:00](https://www.youtube.com/watch?v=BCIT1L5muoQ&t=780) - You're staring at a task, and you feel like, yeah, now is the right moment to reorganize my bookshelf. Or I feel like I'm going to do a little, like, deep clean of the kitchen. This is the right moment. Procrastination. Procrastination is basically delaying doing something that you just couldn't do. Procrastination is like the idea of procrastination. Procrastination is about the idea of procrastination. Procrastination is about the idea of procrastination. decided you should be doing. And again, when we experience procrastination, usually the feeling, the emotion that's associated with it is self-blame and self-judgment. We feel like, why am I not doing this thing I decided I should be doing? So what would happen if instead we replaced that self-blame

- [00:13:30](https://www.youtube.com/watch?v=BCIT1L5muoQ&t=810) - usually the feeling, the emotion that's associated with it is self-blame and self-judgment. We feel like, why am I not doing this thing I decided I should be doing? So what would happen if instead we replaced that self-blame with systematic curiosity, if we decided to look at procrastination with curiosity? To explore something with systematic curiosity is basically just asking without any self-judgment, where is this problem coming from? And so you can ask, is it coming from the head? Is it coming from the heart? Or is it coming from the hand? So in the case of procrastination, for example,

- [00:14:00](https://www.youtube.com/watch?v=BCIT1L5muoQ&t=840) - where is this problem coming from? And so you can ask, is it coming from the head? Is it coming from the heart? Or is it coming from the hand? So in the case of procrastination, for example, you could say, where is the procrastination coming from? Is it coming from the head? In that case, that means that at a rate of rational level, you're not quite convinced you should be working on this, even if it's subconscious. Maybe you feel like the task is badly designed. Maybe you feel like you're not the one who should be doing this. It should be delegated. Or maybe you feel like this is just not aligned with your priorities. At a subconscious level, you're not rationally convinced that you should be working on this, and so you procrastinate. If the problem is coming from the heart,

- [00:14:30](https://www.youtube.com/watch?v=BCIT1L5muoQ&t=870) - It should be delegated. Or maybe you feel like this is just not aligned with your priorities. At a subconscious level, you're not rationally convinced that you should be working on this, and so you procrastinate. If the problem is coming from the heart, it means that although at a rational level, you think, yeah, I should be doing this, at an emotional, it doesn't look like it's going to be very fun. And so you procrastinate. And if the problem is coming from the hand, it means that although at a rational level, the head says, yes, let's do this, the heart says, yeah, it looks like fun, at a practical level, you don't believe you have the right tools, the right resources, the right support in order to complete the task.

- [00:15:00](https://www.youtube.com/watch?v=BCIT1L5muoQ&t=900) - the head says, yes, let's do this, the heart says, yeah, it looks like fun, at a practical level, you don't believe you have the right tools, the right resources, the right support in order to complete the task. What's great is that when you start looking at any challenge, any problem, with curiosity instead of self-blame, then you can also systematically come up with solutions to address that problem. So if the task is not appropriate, the head says no, then you can just redefine the strategy, either on your own or as a group. Talk to other people and say, hey, I've been procrastinating on this, not fully convinced. Can we brainstorm together and figure out if this is the right approach?

- [00:15:30](https://www.youtube.com/watch?v=BCIT1L5muoQ&t=930) - then you can just redefine the strategy, either on your own or as a group. Talk to other people and say, hey, I've been procrastinating on this, not fully convinced. Can we brainstorm together and figure out if this is the right approach? If the problem is coming from the heart, then just redesign the task. Maybe experience around the task. Maybe make it more fun. Take the classroom outside and do it from a park. Go to a coffee shop. Grab a friend who's always fun to work with and say, hey, can we do a little co-working session while I do this thing that's not very fun? And if the problem is coming from the hand, then raise that hand. Ask for support.

- [00:16:00](https://www.youtube.com/watch?v=BCIT1L5muoQ&t=960) - can we do a little co-working session while I do this thing that's not very fun? And if the problem is coming from the hand, then raise that hand. Ask for support. Ask for training instead of feeling paralyzed and procrastinating. So as you can see, when you systemically curious, it really changes your relationship to those signals your brain is sending you. And you can learn from them and use that information to make decisions instead of feeling stuck. Ultimately, this entire change in mindset, the experimental mindset, is about defaulting to curiosity. It's about relearning how to fall in love with problems.

- [00:16:30](https://www.youtube.com/watch?v=BCIT1L5muoQ&t=990) - Ultimately, this entire change in mindset, the experimental mindset, is about defaulting to curiosity. It's about relearning how to fall in love with problems. It's about letting go of the analysis paralysis, the imposter syndrome, the overthinking, and the self-blame. It's really about approaching any challenge as an opportunity for growth and self-discovery. The last change that I want you to start thinking about in order to work a little bit smarter without sacrificing your mental health and learn better in the process is to make space for your students

- [00:17:00](https://www.youtube.com/watch?v=BCIT1L5muoQ&t=1020) - as an opportunity for growth and self-discovery. The last change that I want you to start thinking about in order to work a little bit smarter without sacrificing your mental health and learn better in the process is to make space for your students and for yourself for continuous iteration. So that experimental cycle that we talked about earlier, another amazing thing about it is that you don't need a lab to use it. You can apply it to literally any area in your life, anything you want to learn, anything you want to explore, any challenge that you're facing. And it all starts with observation, as I said. And I know when we're doers, we have high agency,

- [00:17:30](https://www.youtube.com/watch?v=BCIT1L5muoQ&t=1050) - You can apply it to literally any area in your life, anything you want to learn, anything you want to explore, any challenge that you're facing. And it all starts with observation, as I said. And I know when we're doers, we have high agency, we want to try things, we might want to start experimenting straight away. But in order to design, we have to start with observation. And if you want to design a good experiment, you need to start with observation. I call this self-anthropology, because the idea is to pretend for a little while that you're an anthropologist, but with your life and your work as the topic of study. If you think about what an anthropologist does, is that they go to a new culture. They know nothing about this culture.

- [00:18:00](https://www.youtube.com/watch?v=BCIT1L5muoQ&t=1080) - but with your life and your work as the topic of study. If you think about what an anthropologist does, is that they go to a new culture. They know nothing about this culture. And they take their notebook, their field notes, and they start capturing what they observe. And because they know nothing, they don't know anything. They won't have any judgment. They don't have any preconceptions. They just ask questions like, why are they doing things this way? Why do they care about this thing so much? Why is that interesting to them? And you can do the very same thing with your work and with your life. For 24 hours, you can do it on your phone and a notebook, but really observing the way you spend your time, your energy, and your attention, and asking questions.

- [00:18:30](https://www.youtube.com/watch?v=BCIT1L5muoQ&t=1110) - And you can do the very same thing with your work and with your life. For 24 hours, you can do it on your phone and a notebook, but really observing the way you spend your time, your energy, and your attention, and asking questions. The second stage is to try and change something. So if you observe something interesting, an assumption that you made, something that you've been doing in exactly the same way for a very long time, or something you've been avoiding doing for a very long time because you're scared of it, you can design what I call a tiny experiment. And for that, you only need two ingredients. You need to know what is the action you're going to perform,

- [00:19:00](https://www.youtube.com/watch?v=BCIT1L5muoQ&t=1140) - because you're scared of it, you can design what I call a tiny experiment. And for that, you only need two ingredients. You need to know what is the action you're going to perform, just like a scientist that says, this is what we're going to test. And you need to know the duration, which for a scientist in the lab would be the number of trials. I want to show you how flexible this is. And this is the wrong slide. So before I tell you how flexible this is, let me tell you why it's important to have several trials. So you don't want to do the thing just once.

- [00:19:30](https://www.youtube.com/watch?v=BCIT1L5muoQ&t=1170) - So before I tell you how flexible this is, let me tell you why it's important to have several trials. So you don't want to do the thing just once. If you do the thing just once, you can't really draw any conclusions. You need to do it several times. This is why you see in any scientific experiments that scientists will say, we're going to run this trial for three months. We're going to run this trial with 200 people. This is how you know that the patterns that you're seeing are actually patterns and not just a coincidence. And now I want to show you how flexible this is. So you can use this for literally anything,

- [00:20:00](https://www.youtube.com/watch?v=BCIT1L5muoQ&t=1200) - are actually patterns and not just a coincidence. And now I want to show you how flexible this is. So you can use this for literally anything, for learning languages. For coding. For reading more. For photography. It really applies to anything. I've seen people use that to experiment even with their diet, nutrition. Whatever it is you're curious about, you can design a tiny experiment by deciding on an action and a duration. And for the duration, that's why I call them tiny experiments, I recommend going for something small, especially the first time you do something you've never done before. And this is actually how I got a little bit better

- [00:20:30](https://www.youtube.com/watch?v=BCIT1L5muoQ&t=1230) - on an action and a duration. And for the duration, that's why I call them tiny experiments, I recommend going for something small, especially the first time you do something you've never done before. And this is actually how I got a little bit better at public speaking. So I started with practicing public speaking by committing, making this little tiny experiment of saying, I'm going to record myself every day for 10 days. And I'm going to post it online without any editing. These are still online if you want to look them up later. And I cringe every time I see them. But this is what allowed me to get over that initial fear. Once I was done with this first cycle of experimentation,

- [00:21:00](https://www.youtube.com/watch?v=BCIT1L5muoQ&t=1260) - These are still online if you want to look them up later. And I cringe every time I see them. But this is what allowed me to get over that initial fear. Once I was done with this first cycle of experimentation, it felt like I learned enough. I said, OK, what does the next cycle look like? How can I be a little bit more ambitious with this? And now, which is currently running, I have an experiment where I say that every quarter, I have to say yes to at least one thing like this that's much bigger and scarier. And I actually want you to start trying it here at South by edu. You can start today.

- [00:21:30](https://www.youtube.com/watch?v=BCIT1L5muoQ&t=1290) - much bigger and scarier. And I actually want you to start trying it here at South by edu. You can start today. You don't need anything else. The tiny experiment I recommend you try for the next few days is this one. I will connect with three new people over the next three days. The reason why I underlined, oh, there have been no slides behind me the entire time. And I've been pointing at this. Sorry. So the reason why I underlined new people here is because I want you to really connect with three new people, not someone that you connected with on LinkedIn before.

- [00:22:00](https://www.youtube.com/watch?v=BCIT1L5muoQ&t=1320) - So the reason why I underlined new people here is because I want you to really connect with three new people, not someone that you connected with on LinkedIn before. But someone that you really knew. You don't know who they are. You don't know what they're working on. And then that's the last part of the experimental cycle. When you go home, I want you to take a little bit of time. It can be just 15 minutes while having a cup of coffee. But reflect on the experience. How did that feel? Look both at the external and internal signals. External signals are the normal, kind of like traditional metrics of success that we use as a society.

- [00:22:30](https://www.youtube.com/watch?v=BCIT1L5muoQ&t=1350) - How did that feel? Look both at the external and internal signals. External signals are the normal, kind of like traditional metrics of success that we use as a society. So you could say, external signals, this was great. I connected with this person. And we're probably going to work together. And maybe we're going to create a course. And so great. Internal signals, how did that feel? Was it awkward? Was it uncomfortable? Or maybe it was really fun and you want to do it more. And based on that, you can decide how you want to tweak your next cycle of experimentation. You can go smaller or you can go bigger.

- [00:23:00](https://www.youtube.com/watch?v=BCIT1L5muoQ&t=1380) - Or maybe it was really fun and you want to do it more. And based on that, you can decide how you want to tweak your next cycle of experimentation. You can go smaller or you can go bigger. Tiny experiments can lead to big changes. Imagine a society where it would be completely normal to walk around and ask people, what have you been experimenting with? What have you learned recently? What are some of your latest failures? This would be the kind of society where not only we would foster more imagination and innovation, but it would also be the kind of society

- [00:23:30](https://www.youtube.com/watch?v=BCIT1L5muoQ&t=1410) - What are some of your latest failures? This would be the kind of society where not only we would foster more imagination and innovation, but it would also be the kind of society where we value people, not based on the quantity of answers they provide, but based on the quality of the questions they ask. Thank you. That was amazing. Oh my goodness. That was incredible. Hi, everyone. So I have so many questions. That was so inspiring.

- [00:24:00](https://www.youtube.com/watch?v=BCIT1L5muoQ&t=1440) - That was amazing. Oh my goodness. That was incredible. Hi, everyone. So I have so many questions. That was so inspiring. How did you come up with this amazing framework? Was it all based on neuroscience? Was it a dream that you had in the middle of the night? What was it? I think it was the realization that for the first part of my life, I kind of think of my life in terms of having two chapters. And the first chapter was very linear. I tried to do very good in school. I got a job at Google. I was obsessed with getting the next promotion, working on the next big project.

- [00:24:30](https://www.youtube.com/watch?v=BCIT1L5muoQ&t=1470) - And the first chapter was very linear. I tried to do very good in school. I got a job at Google. I was obsessed with getting the next promotion, working on the next big project. And I was like, oh, I'm going to do this. I'm going to do this. I'm going to do this. I'm going to do this. And I had a map in front of me of what success was supposed to look like. It's only when I had a health scare, where I'm going to quickly tell you the story, because I find it absolutely ridiculous that I did that at the time. But I was working at Google. And one day I woke up, and my entire arm was black. And I went to the Google infirmary and showed it to them. They were like, you need to go to the hospital. So I went to the Stanford Hospital.

- [00:25:00](https://www.youtube.com/watch?v=BCIT1L5muoQ&t=1500) - And one day I woke up, and my entire arm was black. And I went to the Google infirmary and showed it to them. They were like, you need to go to the hospital. So I went to the Stanford Hospital. And they looked at my arm, and they said, we need to do surgery as quickly as possible. You have a blood clot that's traveling to your lungs. And what I did was, one second, I need to check my calendar. And I wanted to make sure that we scheduled the surgery at a time that would not disrupt any product launches I was working on. In that moment, I had an almost like an out-of-body experience. You know when you see yourself doing something, and you're like, what?

- [00:25:30](https://www.youtube.com/watch?v=BCIT1L5muoQ&t=1530) - at a time that would not disrupt any product launches I was working on. In that moment, I had an almost like an out-of-body experience. You know when you see yourself doing something, and you're like, what? And that's when I really started questioning the way I was approaching things. And that's when I started exploring what it could look like to live a life where, yes, you're ambitious, but also you're not so focused on those linear goals, that destination that you attach your success and your self-worth to, that you start treating yourself in this way. And when I went back to university a few years ago to study neuroscience, I discovered there, rediscovered that experimental cycle, and something clicked. And I was like, what? What?

- [00:26:00](https://www.youtube.com/watch?v=BCIT1L5muoQ&t=1560) - that you start treating yourself in this way. And when I went back to university a few years ago to study neuroscience, I discovered there, rediscovered that experimental cycle, and something clicked. And I was like, what? What? What? What? What? What? Did you come with the exercise? Yes. I should be bouncing around. So I was like, what is the real program I need to take this now and have a world where I could be coming out on this world with no set . You know what? The answer, that was different. I felt like that was the answer, that was one way to provide some form of structure, the way to explore and feel like I could follow my curiosity without needing to have this illusion of certainty

- [00:26:30](https://www.youtube.com/watch?v=BCIT1L5muoQ&t=1590) - I felt like that was the answer, that was one way to provide some form of structure, the way to explore and feel like I could follow my curiosity without needing to have this illusion of certainty how do we become more comfortable in uncertainty? I think it starts with, first, knowing that it's completely normal that you feel this way. I talked about it a little bit, right? But our brains are designed to reduce uncertainty as much as possible in order for us to have higher chances of survival. Another reason why we tend to avoid uncertainty and to feel like we know what we're doing and look like we know what we're doing is that, again, if you think about our ancestors,

- [00:27:00](https://www.youtube.com/watch?v=BCIT1L5muoQ&t=1620) - Another reason why we tend to avoid uncertainty and to feel like we know what we're doing and look like we know what we're doing is that, again, if you think about our ancestors, being excluded from the tribe also meant certain death. And because of that, we're also optimizing to try and be as useful, be a good contributor, be productive. Because our brains are telling us that if you do all of these things, nobody's going to reject you and you're going to stay alive. So first, I would start with that step of just not feeling bad and then, you know, for having this natural resistance to exploring uncertainty.

- [00:27:30](https://www.youtube.com/watch?v=BCIT1L5muoQ&t=1650) - Because our brains are telling us that if you do all of these things, nobody's going to reject you and you're going to stay alive. So first, I would start with that step of just not feeling bad and then, you know, for having this natural resistance to exploring uncertainty. That's completely normal. And the second one is just to have a little mindset shift here. And whenever you see something that feels a little bit uncomfortable that you don't know, seeing it as an opportunity to try a tiny experiment. And I tell people about having an experimental mindset. Some people think that I'm saying, you need to just experiment with everything in your life. And that's not what I'm saying. I'm just saying, pick, pick a small thing that looks a little bit uncomfortable

- [00:28:00](https://www.youtube.com/watch?v=BCIT1L5muoQ&t=1680) - And I tell people about having an experimental mindset. Some people think that I'm saying, you need to just experiment with everything in your life. And that's not what I'm saying. I'm just saying, pick, pick a small thing that looks a little bit uncomfortable and try experimenting around that. You don't have to start with something big and scary. You don't have to quit your job. You don't have to do any of that. So this is what I would tell people. First, it's completely normal. Second, you can start very small. Okay. So speaking of small, one of my favorite things about your book, and I had like all these little tabs in my book, was as I was reading, I got ideas for little tiny questions that I had. So I was wondering for the audience, how can we come up with more ideas for tiny experiments?

- [00:28:30](https://www.youtube.com/watch?v=BCIT1L5muoQ&t=1710) - and I had like all these little tabs in my book, was as I was reading, I got ideas for little tiny questions that I had. So I was wondering for the audience, how can we come up with more ideas for tiny experiments? Like what are things that we can do like from small to medium? How do we think about impact? Like what are more things that we can do in the room that people can have little challenges for themselves? I love the one of like make three new connections. Is there any other ways that we can think of ideas? Yeah. So I kind of think about the way to find them in two different ways. The is the one that is a little bit more like I'm going to sit down and try to figure out something I want to experiment with. Yeah. Yeah. Yeah. Yeah. Yeah. Yeah. Yeah. Yeah. Yeah. Yeah. Yeah. Yeah. Yeah. Yeah. Yeah. Yeah. Yeah. And that you can ask yourself questions like.

- [00:29:00](https://www.youtube.com/watch?v=BCIT1L5muoQ&t=1740) - So I kind of think about the way to find them in two different ways. The is the one that is a little bit more like I'm going to sit down and try to figure out something I want to experiment with. Yeah. Yeah. Yeah. Yeah. Yeah. Yeah. Yeah. Yeah. Yeah. Yeah. Yeah. Yeah. Yeah. Yeah. Yeah. Yeah. Yeah. And that you can ask yourself questions like. What was curious about as a child and that somehow I've decided is not productive right now, not aligned with what I'm working on. That could be a really good source of inspiration for an experiment. Another one is to look at your calendar industry. Open your calendar and have a notebook. And look at the way you spend your time and your energy and ask yourself, is there anything I could do differently? So, for example, if you've been trying to write a little bit every day

- [00:29:30](https://www.youtube.com/watch?v=BCIT1L5muoQ&t=1770) - And look at the way you spend your time and your energy and ask yourself, is there anything I could do differently? So, for example, if you've been trying to write a little bit every day on something you're working on, right? But you've noticed that every time you do it late in the day, you don't have any energy left, and it's really hard, and you have to force yourself. You could design a tiny experiment saying, for the next 15 days, I'm going to write first thing in the morning. And the great thing about these experiments is that you can withhold judgment while you conduct the experiment. Just like a scientist doesn't go and poke at the data in the middle and feels like, oh, I don't like what I'm seeing. I'm going to stop the experiment. You don't do that. You just complete it.

- [00:30:00](https://www.youtube.com/watch?v=BCIT1L5muoQ&t=1800) - is that you can withhold judgment while you conduct the experiment. Just like a scientist doesn't go and poke at the data in the middle and feels like, oh, I don't like what I'm seeing. I'm going to stop the experiment. You don't do that. You just complete it. And at the end, you can decide whether that worked for you or not. So that's if you want to be intentional and say, I'm going to design an experiment. But there's also another way that I think is really powerful, is just noticing whenever you have a lot of resistance around something, catching yourself, having a fixed mindset. So an example I'm going to give you for me was last year. I was talking with a friend, and she was telling me. That she was coming back from a meditation retreat. And I said, I could never do that.

- [00:30:30](https://www.youtube.com/watch?v=BCIT1L5muoQ&t=1830) - So an example I'm going to give you for me was last year. I was talking with a friend, and she was telling me. That she was coming back from a meditation retreat. And I said, I could never do that. I'm so bad at meditation. This is not for me. And I knew it was good. I know so many people told me it's so good for them. And still, I could not do it. I had never gone past. You know if you use those meditation apps, they have the onboarding, the 10 days. I think I had never managed to get past three days. That's how bad I was. And I caught myself saying this. Oh. This is something I'm 100% sure I'm not capable of. That is actually a really good source of inspiration

- [00:31:00](https://www.youtube.com/watch?v=BCIT1L5muoQ&t=1860) - I think I had never managed to get past three days. That's how bad I was. And I caught myself saying this. Oh. This is something I'm 100% sure I'm not capable of. That is actually a really good source of inspiration for a tiny experiment. And so I designed an experiment where I said for the next 15 days, I'm going to meditate for 15 minutes every day. And because there was so much resistance, I decided to do it in public. Oh. Oh? Yeah. I didn't film myself doing it. That would be very boring. Yeah. Very just like meditating for 15 minutes. And that's it. Yeah. No. I created a Google Doc. And I shared the link online. I posted it on my socials and my newsletter.

- [00:31:30](https://www.youtube.com/watch?v=BCIT1L5muoQ&t=1890) - I didn't film myself doing it. That would be very boring. Yeah. Very just like meditating for 15 minutes. And that's it. Yeah. No. I created a Google Doc. And I shared the link online. I posted it on my socials and my newsletter. And at the end of each meditation, I would just write how it felt, the challenges, the questions I had. And then a bunch of people started commenting on the Google Doc, sharing resources. When I was like, is it normal that I feel itchy everywhere? Yes, that's completely normal. So that's an example of identifying something where you feel like, I can't do it. And using a tiny experiment to prove, to prove, to prove, to yourself that you can do it. And that doesn't mean at the end of the experiment that you need to keep going forever.

- [00:32:00](https://www.youtube.com/watch?v=BCIT1L5muoQ&t=1920) - So that's an example of identifying something where you feel like, I can't do it. And using a tiny experiment to prove, to prove, to prove, to yourself that you can do it. And that doesn't mean at the end of the experiment that you need to keep going forever. If the answer, again, external, internal signals, if the answer is, first, I can do it, but I hated it, that's fine. Now you know. And it's not based on just the assumptions that you have. It's based on having actually collected data. Do I need to do a meditation experiment? No, please. No. No. You're the scientist of your own life. I'm not going to tell you what to do. So ideally, like, everybody should be able to do it. OK. So you should have very different experiments that are really based on their own experience. You know, I like what you said with this hand,

- [00:32:30](https://www.youtube.com/watch?v=BCIT1L5muoQ&t=1950) - You're the scientist of your own life. I'm not going to tell you what to do. So ideally, like, everybody should be able to do it. OK. So you should have very different experiments that are really based on their own experience. You know, I like what you said with this hand, where it was the internal, like, I now know the answer. I think a lot about confidence and how we get more of it. Could tiny experiments be a kind of a side door into confidence? Have you noticed as people are doing these, do they leave more confident? Are they getting more confidence? Like, could we help our students do tiny experiments, even if the answer is no, to get more confidence? How do they tie in together? Yeah. So the reason why, so first, yes, absolutely. The reason why is because when you run an experiment,

- [00:33:00](https://www.youtube.com/watch?v=BCIT1L5muoQ&t=1980) - even if the answer is no, to get more confidence? How do they tie in together? Yeah. So the reason why, so first, yes, absolutely. The reason why is because when you run an experiment, it completely changes your relationship to success and failure. When you have a linear goal with a specific outcome, it's very binary. So either you get to that specific outcome, and that is success, or you don't, and that's failure. And that's normally how we measure success. But again, when you run an experiment, success is just learning something new. So instead of saying, I'm going to try to get this, you start from a hypothesis, from a bit of a research question. You say, I'm curious about this.

- [00:33:30](https://www.youtube.com/watch?v=BCIT1L5muoQ&t=2010) - success is just learning something new. So instead of saying, I'm going to try to get this, you start from a hypothesis, from a bit of a research question. You say, I'm curious about this. What if I tried this? What would happen? And it really changes the starting point. You don't need to have any confidence about the outcome, because you're only trying to figure out whether this is going to work out or not. So it will first build confidence in getting started, because you say, it's just a tiny experiment. That's all I'm doing. But then you build confidence through the repetition, the repeated trials. The more you do it, the more you become familiar with the action, and the more comfortable it becomes.

- [00:34:00](https://www.youtube.com/watch?v=BCIT1L5muoQ&t=2040) - That's all I'm doing. But then you build confidence through the repetition, the repeated trials. The more you do it, the more you become familiar with the action, and the more comfortable it becomes. OK. So if we have the confidence to do it, to see this hypothesis, one aspect of the book you talked about, which I wanted you to touch on, is cognitive scripts. Can we dive a little bit deeper into that? And how do we uncov- what are they? And how do we uncover our own cognitive scripts? So cognitive scripts are patterns of behaviors that we follow in an unconscious way, because we've been kind of taught to follow them. And that's what everybody around us is doing. So that was the- this entire research

- [00:34:30](https://www.youtube.com/watch?v=BCIT1L5muoQ&t=2070) - that we follow in an unconscious way, because we've been kind of taught to follow them. And that's what everybody around us is doing. So that was the- this entire research started with a seminal studies in 1979 that was- had a very simple research design. They asked people, if I put you in this situation, what do you do? And what they found is that most people, given a certain situation, will actually behave in the exact same way, which is fine for a lot of the situations that they shared, like going to the doctor, for example. So you go to the doctor. You wait in the waiting room. That's why it's called a waiting room. Then they call your name. And then you go in the doctor's office.

- [00:35:00](https://www.youtube.com/watch?v=BCIT1L5muoQ&t=2100) - is fine for a lot of the situations that they shared, like going to the doctor, for example. So you go to the doctor. You wait in the waiting room. That's why it's called a waiting room. Then they call your name. And then you go in the doctor's office. And maybe they ask you to get undressed to check what's wrong, right? If the doctor walked out of their office into the waiting room and asked you to get naked in front of everybody, you would feel really weird. And that's because they're going off script. So scripts are OK for a lot of social situations where you don't want to overthink about how you're supposed to act and what you're supposed to do. The problem is that scientists discovered that we follow cognitive scripts in many more areas of our lives, areas of our lives where maybe we don't want

- [00:35:30](https://www.youtube.com/watch?v=BCIT1L5muoQ&t=2130) - where you don't want to overthink about how you're supposed to act and what you're supposed to do. The problem is that scientists discovered that we follow cognitive scripts in many more areas of our lives, areas of our lives where maybe we don't want to follow a cognitive script, including designing our career, deciding what project to work on, or even what to wear. So in the book, I talk about three cognitive scripts that are the most important ones and that I would highly recommend trying to pay attention to in your life and in your work and seeing if maybe you're following some of them. So the very first one is the SQL script. That's the SQL script. It's the belief that whatever you do today, whatever decisions you make today, it needs to make sense based on the decisions you made in the past.

- [00:36:00](https://www.youtube.com/watch?v=BCIT1L5muoQ&t=2160) - So the very first one is the SQL script. That's the SQL script. It's the belief that whatever you do today, whatever decisions you make today, it needs to make sense based on the decisions you made in the past. It needs to follow a nice narrative. And this is why a lot of people start only looking for jobs that are aligned with whatever they studied in school instead of opening up those possibilities. This is also why, by the way, we rewrite our CVs, our resumes, because we want it to make sense, have a nice story to it. So that's the SQL script. The second script is the crowd pleaser script. That's when we make decisions to make people around us happy. And those people around us could be our colleagues, but that could also be our partner. That could be our parents.

- [00:36:30](https://www.youtube.com/watch?v=BCIT1L5muoQ&t=2190) - So that's the SQL script. The second script is the crowd pleaser script. That's when we make decisions to make people around us happy. And those people around us could be our colleagues, but that could also be our partner. That could be our parents. That's very common with people from an immigrant background where their parents worked really hard to help them go through their studies, and then they feel like they have to do the thing that's aligned with whatever those studies were. And the last script, which I think is the most insidious one, because that's the one we celebrate as a society, is the epic script. And that's the last script. It's the idea that whatever you do in life, it needs to be big. It needs to be impressive. It needs to follow your passion.

- [00:37:00](https://www.youtube.com/watch?v=BCIT1L5muoQ&t=2220) - the one we celebrate as a society, is the epic script. And that's the last script. It's the idea that whatever you do in life, it needs to be big. It needs to be impressive. It needs to follow your passion. And because of that, a lot of people first put all of their eggs in the same basket and tie their identity to whatever their work is, feeling like, that's my big thing. That's the thing I'm passionate about. But also, a lot of people are miserable because they feel like they haven't figured out what their passion is yet and something's missing, when, in reality, they're not. And so, when you ask people, hey, you look pretty passionate about what you're working on right now. How did you figure that out? None of them will tell you they went through a five-step process

- [00:37:30](https://www.youtube.com/watch?v=BCIT1L5muoQ&t=2250) - what their passion is yet and something's missing, when, in reality, they're not. And so, when you ask people, hey, you look pretty passionate about what you're working on right now. How did you figure that out? None of them will tell you they went through a five-step process with a worksheet where they found their passion. They will all tell you, I kind of stumbled upon it. I was working on this thing. I met this person. I started working on this project. And one morning, I woke up, and I felt like, whoa. I feel like I could be doing this for the rest of my life. And this is really how you find your passion, experimenting, trying new things, and putting yourself out there, not by obsessing over having this epic kind of work that you should be doing.

- [00:38:00](https://www.youtube.com/watch?v=BCIT1L5muoQ&t=2280) - I feel like I could be doing this for the rest of my life. And this is really how you find your passion, experimenting, trying new things, and putting yourself out there, not by obsessing over having this epic kind of work that you should be doing. The middle script is, it got me thinking about social scripts. And this was actually my favorite part of the book, the very end, the last section, get to it for sure, on social flow and how our tiny experiments can be impacted by people around us. By the way, my tiny experiment is not asking, what do you do? Because I feel like it's a social script that you get into autopilot. And breaking. And breaking that has been hard. So can you talk about how this works with social flow and, yeah, that idea? So social flow is fascinating. So probably in this room of educators,

- [00:38:30](https://www.youtube.com/watch?v=BCIT1L5muoQ&t=2310) - Because I feel like it's a social script that you get into autopilot. And breaking. And breaking that has been hard. So can you talk about how this works with social flow and, yeah, that idea? So social flow is fascinating. So probably in this room of educators, you're all familiar with flow states. Something not as many people know is that flow states, we think about them as this very individual thing. You're in the zone, working on something highly focused, feeling like your creativity is very high. There's also new research showing that we can have better states of flow when we get in the flow as a group. So if you can design tasks or exercises or projects or tiny experiments that people are trying together

- [00:39:00](https://www.youtube.com/watch?v=BCIT1L5muoQ&t=2340) - that we can have better states of flow when we get in the flow as a group. So if you can design tasks or exercises or projects or tiny experiments that people are trying together that are really aligned with people's sense of curiosity, what they're interested in, then you're going to demultiply this flow effect. And people are going to feel more focused and more creative in the process. So this is really fascinating. And this is something that you can use in any kind of group. And even for yourself, like, again, by just grabbing someone, who's working on something similar and saying, actually, I want to do a deep work session on this to maximize how productive, creative, and focused

- [00:39:30](https://www.youtube.com/watch?v=BCIT1L5muoQ&t=2370) - And even for yourself, like, again, by just grabbing someone, who's working on something similar and saying, actually, I want to do a deep work session on this to maximize how productive, creative, and focused I'm going to be. I'm going to grab someone who's working on something similar, and we can do a little co-working session. OK, so I wanted to talk about buy-in, like saying to someone, I want to do this experiment. Will you do it with me? Saying to students, we have an idea. Let's do this experiment together. Even like bosses, colleagues, how do we get buy-in on, I want to have an experimental mindset. I want to do this tiny experiment. Do you have any ideas or even scripts of, like, how do we get buy-in from our bosses, our colleagues, our students on that?

- [00:40:00](https://www.youtube.com/watch?v=BCIT1L5muoQ&t=2400) - Even like bosses, colleagues, how do we get buy-in on, I want to have an experimental mindset. I want to do this tiny experiment. Do you have any ideas or even scripts of, like, how do we get buy-in from our bosses, our colleagues, our students on that? That's the great thing is that you don't need buy-in. So you can just do it. And you should. But I'm waiting for buy-in. Yay. That's what I love about it is that just do it. And then come with the results. Share the results. And I think this is something we don't necessarily feel comfortable doing. But everybody would appreciate it if a colleague comes and says, hey, I tried this thing. It was a complete failure. Don't do it. Let us save a little bit of time as a team.

- [00:40:30](https://www.youtube.com/watch?v=BCIT1L5muoQ&t=2430) - But everybody would appreciate it if a colleague comes and says, hey, I tried this thing. It was a complete failure. Don't do it. Let us save a little bit of time as a team. And so that's the great thing. Just do it. Whether it succeeds or it fails, you are going to have new data, something interesting to share back with the team. No buy-in. Just do it. Courage. Courage. Courage. OK. I have one more question before we take questions from our lovely audience, which is, highly ambitious people are very linear. If they have a structure, they have goals. Can you be both highly ambitious and highly curious? Are they opposing forces, or do they go together and speed each other up?

- [00:41:00](https://www.youtube.com/watch?v=BCIT1L5muoQ&t=2460) - are very linear. If they have a structure, they have goals. Can you be both highly ambitious and highly curious? Are they opposing forces, or do they go together and speed each other up? I hope the answer is second, but. Yes, you can be highly curious and highly ambitious, although it is kind of discouraged in today's society. So that's something I'm actually very sad about and I hope to change is that for a lot of people who are highly ambitious, curiosity can almost be perceived as a distraction. Mm-hmm. Mm-hmm. So you're supposed to be focused on the goal. You're supposed to do the thing. You're supposed to apply the playbooks and just do the things in order. And there's really this mental model of a ladder

- [00:41:30](https://www.youtube.com/watch?v=BCIT1L5muoQ&t=2490) - Mm-hmm. Mm-hmm. So you're supposed to be focused on the goal. You're supposed to do the thing. You're supposed to apply the playbooks and just do the things in order. And there's really this mental model of a ladder that you're supposed to climb, right? So anytime you look right or left and you stop, you're wasting time. But if you're able to be both ambitious and curious, if you're able to embed it, to have those experiments, to make some space for, yes, you can still climb if that's what you want to do, but have that space. Mm-hmm. Or having those tiny experiments on the side, you will actually find that you can also climb that ladder faster, but without being obsessed with it. Because again, you're going to be able to be the kind of person

- [00:42:00](https://www.youtube.com/watch?v=BCIT1L5muoQ&t=2520) - Mm-hmm. Or having those tiny experiments on the side, you will actually find that you can also climb that ladder faster, but without being obsessed with it. Because again, you're going to be able to be the kind of person who comes with new ways of climbing that ladder. Or maybe letting go of that ladder and going and climb a different one. Or just laying on the ground and saying, I don't want to climb anymore. You can decide whatever it is that you want to do. But for this, you do need the curiosity aspect. You can't have just ambition. OK. One more, which is, do you have a favorite tiny experiment that someone has sent you or told you about? Because I know that you're hearing all these great stories. And hopefully everyone can send their stories. Do you have one that you're just like, oh, it's so good. I didn't even think about that.

- [00:42:30](https://www.youtube.com/watch?v=BCIT1L5muoQ&t=2550) - One more, which is, do you have a favorite tiny experiment that someone has sent you or told you about? Because I know that you're hearing all these great stories. And hopefully everyone can send their stories. Do you have one that you're just like, oh, it's so good. I didn't even think about that. Or I'm so inspired by it. You can be two. Can I share two? Oh, thank you. Of course. I had to be, of course. There are no rules. Yeah. So two of them. So they have nothing to do with education, I'm sorry. But they're really my favorite. So the first one was someone in the Nest Labs community had noticed that they had lost touch with a lot of friends up there. They just got busy, work, family, and all of that. So they designed a tiny experiment where they say that every week they will reach out to a friend they haven't talked to in a while.

- [00:43:00](https://www.youtube.com/watch?v=BCIT1L5muoQ&t=2580) - up there. They just got busy, work, family, and all of that. So they designed a tiny experiment where they say that every week they will reach out to a friend they haven't talked to in a while. For, I think, a couple of months, they do this. And it was just a quick voice note and just saying, hey, how are you doing? That's it. And I talked to them. And they told me that was life changing, because they reconnected with people. And because it was part of this experiment, sometimes they would sit and feel a little bit uncomfortable, feeling like, I haven't talked to this person in a year. But again, it was the experience. And they did the experiment. So they did it. And it was amazing for them. So that's one of my favorite ones. Another one, again, from the Nest Labs community, was this woman, this artist. So she is still a painter.

- [00:43:30](https://www.youtube.com/watch?v=BCIT1L5muoQ&t=2610) - But again, it was the experience. And they did the experiment. So they did it. And it was amazing for them. So that's one of my favorite ones. Another one, again, from the Nest Labs community, was this woman, this artist. So she is still a painter. And she had surgery where, and that's where we bonded over, actually, same arm as I had my problem. So she had surgery, and she couldn't paint for a very long time. And when it was time to go back to her art studio and paint, she noticed that she was so anxious that she couldn't do it. So she designed a tiny experiment where she said, for 10 days, I'm going to go in my art studio, and I'm just going to sit there. I'm not supposed to paint. I'm not supposed to do anything. I'm just going to sit there. And she completed the experiment.

- [00:44:00](https://www.youtube.com/watch?v=BCIT1L5muoQ&t=2640) - So she designed a tiny experiment where she said, for 10 days, I'm going to go in my art studio, and I'm just going to sit there. I'm not supposed to paint. I'm not supposed to do anything. I'm just going to sit there. And she completed the experiment. And even before she finished it, she actually started painting again, because there was no pressure at all. It was really just about sitting in the studio and seeing what happened. So those are two of my favorite ones. EMILY FORTUNA- Oh, I love it. I hope those are inspiring. All right, I would love to take questions from the audience. So we have a lot here. Let's see. Failure, this is from Pedro. Failure is often framed negatively. How can educators and learners reframe failure to make it a productive part of the learning process? The hardest one, yeah. Yeah. I think it's a little bit like in science, where whatever the result, if you publish it,

- [00:44:30](https://www.youtube.com/watch?v=BCIT1L5muoQ&t=2670) - Failure is often framed negatively. How can educators and learners reframe failure to make it a productive part of the learning process? The hardest one, yeah. Yeah. I think it's a little bit like in science, where whatever the result, if you publish it, then it was worth your time. And so something I'm a bit advocate for is learning in public and saying, try this. And if it fails, all I ask you is to share what you learned. Share what you learned with your classmates. Share what you learned with your friends. Share what you learned with your classmates or just with me if you want to have a setting where it feels even more comfortable at first. Again, starting small. But saying, it's not failure if you learn something new

- [00:45:00](https://www.youtube.com/watch?v=BCIT1L5muoQ&t=2700) - Share what you learned with your friends. Share what you learned with your classmates or just with me if you want to have a setting where it feels even more comfortable at first. Again, starting small. But saying, it's not failure if you learn something new in the process. And by learning something new, it means, again, sharing those lessons, but also think about the cycle. What am I going to do differently next time? What is the new prediction? And so this is really how you can reframe your relationship to failure. EMILY FORTUNA- OK, so we have a couple of questions. Kelly, Zach, and John, these are sort of similar. So one is, how do we make sure that we're able to create a system where school systems are based on the traditional definition of success? Grades, scores, how do we juxtapose systems that cultivate curiosity? KELLEY GARCIA- Yeah, that's why earlier I

- [00:45:30](https://www.youtube.com/watch?v=BCIT1L5muoQ&t=2730) - So one is, how do we make sure that we're able to create a system where school systems are based on the traditional definition of success? Grades, scores, how do we juxtapose systems that cultivate curiosity? KELLEY GARCIA- Yeah, that's why earlier I was talking about the fact that there are still ladders that we need to climb in our society. And they're there, right? So I'm not here to say, let's tear all of these down. It's really about making space for that experimentation and that curiosity. And that could be as simple as having one hour every month, and then having the kids saying, that's my experiment for the month. That's the thing I'm going to try. I'm going to do this thing differently. And next month, I'm going to share what I learned. So just making that space and saying that it's OK. And the second thing you can do is whenever you have a student

- [00:46:00](https://www.youtube.com/watch?v=BCIT1L5muoQ&t=2760) - and then having the kids saying, that's my experiment for the month. That's the thing I'm going to try. I'm going to do this thing differently. And next month, I'm going to share what I learned. So just making that space and saying that it's OK. And the second thing you can do is whenever you have a student who looks like they're a bit stuck, they feel like they can't do it, and yeah, they have whatever, you know, negative emotion and resistance around it, you can ask, what would it look like if you turned this into an experiment? Can we turn this into an experiment together? So this should help. What would it look like? I love that. So this one is about middle school students. How can we get middle school students to embrace this mindset while already dealing with so many developmental changes and societal cultural expectations?

- [00:46:30](https://www.youtube.com/watch?v=BCIT1L5muoQ&t=2790) - I love that. So this one is about middle school students. How can we get middle school students to embrace this mindset while already dealing with so many developmental changes and societal cultural expectations? I think actually this is the perfect age to start experimenting because in any case, so much is changing at this age. There's so much uncertainty. So there should be so many ideas for experiments there. And so I think the only thing that, you know, you can do as an educator is actually helping them see that all of this uncertainty can be a source of growth and self-discovery and giving them the sense of agency by saying, like, I know everything is changing.

- [00:47:00](https://www.youtube.com/watch?v=BCIT1L5muoQ&t=2820) - you can do as an educator is actually helping them see that all of this uncertainty can be a source of growth and self-discovery and giving them the sense of agency by saying, like, I know everything is changing. I know there's a lot of uncertainty. I know there's a lot of pressure for you to succeed. Is there one area of your life where you feel kind of curious? And if we remove this traditional success from the equation, what is something you would like to experiment with? Okay, I love this idea because... Sorry, they're moving around. Okay, yes. So this is similar to, like, how we reshift, right? So this is from Lace, I think. How to reshift students' thinking to questioning and curiosity rather than only aiming at success.

- [00:47:30](https://www.youtube.com/watch?v=BCIT1L5muoQ&t=2850) - Okay, I love this idea because... Sorry, they're moving around. Okay, yes. So this is similar to, like, how we reshift, right? So this is from Lace, I think. How to reshift students' thinking to questioning and curiosity rather than only aiming at success. Specifically, I love this, what are the soft skills we should emphasize more? You know I love soft skills. Yes. The main soft skill that I would be trying to develop and which you're all going to be familiar with as educators is metacognition. And that's a really important skill. Okay. So, the main thing we're trying to work on is maybe, you know, what are the things we should focus on or why we should focus on to help our students in the future. And then finally, maybe maybe we should focus on the impact of the success

- [00:48:00](https://www.youtube.com/watch?v=BCIT1L5muoQ&t=2880) - is maybe, you know, what are the things we should focus on or why we should focus on to help our students in the future. And then finally, maybe maybe we should focus on the impact of the success on students' thinking, and really this ability to observe your own thoughts, your own behaviors, your own emotions. The problem when we're so focused on aiming at success is that we're only focused on those external signals. What are my grades? what was happening around that? What was the internal landscape? What did they do differently this time that made them maybe a little bit more successful here? And so really having this curiosity, not only for external topics, like we talk a lot about curiosity,

- [00:48:30](https://www.youtube.com/watch?v=BCIT1L5muoQ&t=2910) - What did they do differently this time that made them maybe a little bit more successful here? And so really having this curiosity, not only for external topics, like we talk a lot about curiosity, about learning about history or math and all of that, but having this inner sense of curiosity, helping them cultivate this inner curiosity as to how they think and how they learn best. Hmm. So this is from Anonymous. Anonymous, I've recently been laid off from a position I love. I feel like a failure. What do you recommend I can do to frame my mindset through this change? Thank you for that question. So first, I'm sorry. And also, I'm excited for you.

- [00:49:00](https://www.youtube.com/watch?v=BCIT1L5muoQ&t=2940) - What do you recommend I can do to frame my mindset through this change? Thank you for that question. So first, I'm sorry. And also, I'm excited for you. This is, I had some of my best experiments when I was in between jobs because you don't have the limitations that you have when you have a nine to five. And so you can actually go a little bit crazy during this. During this period while you're looking for your next job. And maybe experiment with something which normally would not have been possible if you only had like a couple of hours on the weekend or just really try to think about something you could do that you can only do right now while you're in between jobs.

- [00:49:30](https://www.youtube.com/watch?v=BCIT1L5muoQ&t=2970) - And maybe experiment with something which normally would not have been possible if you only had like a couple of hours on the weekend or just really try to think about something you could do that you can only do right now while you're in between jobs. And I think this can be a really good way to reframe it. And this can also be part of figuring out what you want to do next. Because as you experiment, you might discover things that you really enjoy and you're really curious about that you would have not realized if you just jumped from your previous job to just finding the next one and following that SQL script that we talked about. I agree. If it's helpful, my biggest failures have led to my greatest sparks.

- [00:50:00](https://www.youtube.com/watch?v=BCIT1L5muoQ&t=3000) - if you just jumped from your previous job to just finding the next one and following that SQL script that we talked about. I agree. If it's helpful, my biggest failures have led to my greatest sparks. Okay, from Josh. Can you provide an experiential example of how this framework has worked and the effects it produced in a learning situation like a classroom, a lab, or a workshop? And that's great. Like what are some specific examples that maybe people can use in their schools? Yeah, absolutely. So I do teach classes both at King's College London and at Nest Labs. And to me, really, this is about always trying to be framing everything as an experiment. And so if you're going to study a topic,

- [00:50:30](https://www.youtube.com/watch?v=BCIT1L5muoQ&t=3030) - So I do teach classes both at King's College London and at Nest Labs. And to me, really, this is about always trying to be framing everything as an experiment. And so if you're going to study a topic, you're probably going to have to go through a curriculum. And that's something I'm frustrated about at King's College London too. I have to follow the curriculum. But I always try to figure out how can I inject a little bit more experimentation in there. And so something you can do is just to literally ask students as part of their homework or whatever they're supposed to do to come up with an experiment. And that's not going to be something you want to grade necessarily because that wouldn't make sense with what an experiment is, right? Just design an experiment around this topic.

- [00:51:00](https://www.youtube.com/watch?v=BCIT1L5muoQ&t=3060) - as part of their homework or whatever they're supposed to do to come up with an experiment. And that's not going to be something you want to grade necessarily because that wouldn't make sense with what an experiment is, right? Just design an experiment around this topic. Do it at the beginning when you know nothing about the topic. Is something you're curious about, like, you know, you vaguely know about what we're going to study together. Is there something that you want to experiment with? And at the end of the semester or however long, the class is again, have everybody come and present what they learned with no success or failure in the traditional binary sense of the way, but in the sense that I went there, I tried that. I learned something new and I'm coming to share it with you now. Okay. This is a hard one from Jane.

- [00:51:30](https://www.youtube.com/watch?v=BCIT1L5muoQ&t=3090) - in the traditional binary sense of the way, but in the sense that I went there, I tried that. I learned something new and I'm coming to share it with you now. Okay. This is a hard one from Jane. Jane asked, working with children, oftentimes you have to also convince parents about what we're doing. What suggestions do you have for parent education? Yes. Yeah. Thank you. I would actually present that as an amazing bonding opportunity with children. Like, they can run their experiments on their own. That's the nice thing about keeping them tiny is that they can run something from beginning to end on their own. But it can also be really fun, actually.

- [00:52:00](https://www.youtube.com/watch?v=BCIT1L5muoQ&t=3120) - Like, they can run their experiments on their own. That's the nice thing about keeping them tiny is that they can run something from beginning to end on their own. But it can also be really fun, actually. Anyone who has children and who has tried something a little bit messy with them where you don't really know what you're doing, will know that it can actually be an amazing way to connect at a deeper level with your child. Letting them know that this is not about being graded and this is really about equipping them to be successful in our nonlinear world. I also think that deep down, most parents know that what is going to make their children successful is not how well they remember those historical facts that they're supposed to learn by heart for an exam.

- [00:52:30](https://www.youtube.com/watch?v=BCIT1L5muoQ&t=3150) - I also think that deep down, most parents know that what is going to make their children successful is not how well they remember those historical facts that they're supposed to learn by heart for an exam. It's the learning how to learn. It's this ability to adapt, to navigate uncertainty, to extract information from their environment and to iterate. This is what is going to make them successful. So if you're the kind of teacher who says, hey, actually, I know and I agree with you, and we're actually going to make space for that, I think most parents are actually going to be quite happy about it. Mm-hmm. Okay, to a fun one. From Andy, how might playfulness and humor and humility fit into this idea of an experimental mindset? That's a good one.

- [00:53:00](https://www.youtube.com/watch?v=BCIT1L5muoQ&t=3180) - Mm-hmm. Okay, to a fun one. From Andy, how might playfulness and humor and humility fit into this idea of an experimental mindset? That's a good one. They're absolutely core to it. You do need to be playful. You need to not take yourself too seriously when you experiment. You know, you're very often starting an experiment with the hypothesis that this might go horribly wrong. Mm-hmm. You can only do that if you're not taking yourself too seriously. So they're core, and actually they can even be a compass for choosing your next experiment. If there's something that feels like it could be a lot of fun, again, you're not quite sure how that would align with your current work or with the curriculum you're supposed to

- [00:53:30](https://www.youtube.com/watch?v=BCIT1L5muoQ&t=3210) - So they're core, and actually they can even be a compass for choosing your next experiment. If there's something that feels like it could be a lot of fun, again, you're not quite sure how that would align with your current work or with the curriculum you're supposed to build with your classroom. It just feels like it could be really fun, and maybe there's a possibility that you would look ridiculous. Yeah. Like, that actually can be a really good compass for choosing. Have you had any silly ones? Have you had any silly ones that you've done or seen people do where you felt silly? I mean, honestly, just recording myself talking about, like, literally the most boring stuff on camera and posting it on Instagram straight away, unedited. That was, I had my cringe moments, for sure.

- [00:54:00](https://www.youtube.com/watch?v=BCIT1L5muoQ&t=3240) - I mean, honestly, just recording myself talking about, like, literally the most boring stuff on camera and posting it on Instagram straight away, unedited. That was, I had my cringe moments, for sure. Yeah. So that was one. Oh, I had another one also where I decided to make cheese live on video from my home. Cheese is hard to make. Oh, yeah. A lot of strain. Yeah, that didn't go very well. Okay. So, yeah, that's what I love about it is that I mentioned earlier that you can use curiosity as a way to decide what you want to experiment with, but sometimes there's a little discomfort of I might look a little bit ridiculous if I try this, which, if you go one level deeper, is really just fear of being judged.

- [00:54:30](https://www.youtube.com/watch?v=BCIT1L5muoQ&t=3270) - curiosity as a way to decide what you want to experiment with, but sometimes there's a little discomfort of I might look a little bit ridiculous if I try this, which, if you go one level deeper, is really just fear of being judged. That can actually be a really good source of finding your next experiment. By the way, please come find me afterwards if you have a fun or silly one, because I want to hear it. Like, that's what I want to hear. I want to know what you're going to start the next few days. Okay. What's an easy way to start your day? So this is from Francesca. What's an easy way to start your day engaging in a productive and positive mindset? Because to be able to do these tiny experiments, I think we have to bring that kind of growth positivity. So how do we start the day with that? I actually don't like being prescriptive with these things, because I think it looks

- [00:55:00](https://www.youtube.com/watch?v=BCIT1L5muoQ&t=3300) - What's an easy way to start your day engaging in a productive and positive mindset? Because to be able to do these tiny experiments, I think we have to bring that kind of growth positivity. So how do we start the day with that? I actually don't like being prescriptive with these things, because I think it looks different for everyone. And I actually do think this is something you can experiment with if you pay attention to it. So you can actually say that for the next week you're going to try a specific morning routine. So this could be an experiment. This could be an experiment. This could absolutely be an experiment. And this is why I'm not a big fan of when people tell you this is what your morning routine should look like. Wake up at 5 a.m. and meditate and journal and stretch. We're A-type people. We love the prescriptions. That's why. Tell us what to do. I know. And so I'm sorry. I'm not going to tell you what to do.

- [00:55:30](https://www.youtube.com/watch?v=BCIT1L5muoQ&t=3330) - routine should look like. Wake up at 5 a.m. and meditate and journal and stretch. We're A-type people. We love the prescriptions. That's why. Tell us what to do. I know. And so I'm sorry. I'm not going to tell you what to do. No. Francesca is me. Sorry, Francesca. But, yeah, that's what I'm hoping to provide here is more of a toolkit that you can use to figure out for yourself. What that might look like for you. Okay. From Devin. The current education environment is very unstable. How do we talk to funders and stakeholders about embracing uncertainty and experimentation? I think you need to talk about it and just it's necessary. To me it should not be even a discussion.

- [00:56:00](https://www.youtube.com/watch?v=BCIT1L5muoQ&t=3360) - How do we talk to funders and stakeholders about embracing uncertainty and experimentation? I think you need to talk about it and just it's necessary. To me it should not be even a discussion. There is no way for any kind of company in education or otherwise to be successful long-term if you don't understand the concept. So I think it's important to embrace uncertainty. I generally think this is going to be the defining factor in between educational companies that are going to be successful and the ones that are not going to be successful is really knowing that, yes, things are changing, things are very uncertain, but it's actually a good thing. That means that anything is possible if you choose to experiment.

- [00:56:30](https://www.youtube.com/watch?v=BCIT1L5muoQ&t=3390) - that are going to be successful and the ones that are not going to be successful is really knowing that, yes, things are changing, things are very uncertain, but it's actually a good thing. That means that anything is possible if you choose to experiment. So I have one final question before we wrap, which is what's one question you would leave the audience with? Hmm. What will be your first tiny experiment? Thank you so much. You can find us after and tell us. Thank you. Thank you. Thank you. Thank you. Thank you.

