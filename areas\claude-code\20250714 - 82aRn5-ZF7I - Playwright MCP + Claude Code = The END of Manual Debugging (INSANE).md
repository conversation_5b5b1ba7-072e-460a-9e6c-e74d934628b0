---
title: Playwright MCP + Claude Code = The END of Manual Debugging (INSANE)
artist: Income stream surfers
date: 2025-07-14
url: https://www.youtube.com/watch?v=82aRn5-ZF7I
---

(t: 0) Okay, so as you can see, guys, this is actually using <PERSON><PERSON> to test the code. Not only that, not only can it take screenshots, it can also read the browser logs. (t: 10) So you can see here, it's looking at the console logs of the browser, right? As you can see, it finally managed to actually click these buttons on the side. (t: 20) It fixed everything. Yeah, let's talk about this. Let's talk about this entire system. Okay, so we're going to be talking about the Playwright MCP inside WSL using context engineering with the Playwright MCP to basically do bug-free coding. (t: 30) It can literally once shot a SAS MVP. (t: 40) Let's talk about it. So first things first, obviously, context engineering intro. This is my repo that was created from <PERSON>'s repo, which I believe was originally created from <PERSON><PERSON>'s repo. (t: 50) Now, mine is slightly different. (t: 60) Mine works in a slightly different way. You can get everything in the description of this video. If you want direct access to me where I will literally just respond to whatever you write to me or if you write a comment here as well, I'll respond to it. (t: 70) And also we have weekly meetings every Thursday and feel free to check out the school. There's a lot of content on there as well. (t: 80) And there's also this prompt, which I need for this video and also the context engineering template. And also the context engineering template. And how to set it up right here. Now, I do need to quickly mention something, which is Playwright. (t: 93) Obviously, I'm using the official Playwright MCP server from Microsoft. (t: 100) However, one thing that you need to know that it's for some reason isn't written here is you also need Chromium to be activated on WSL. (t: 110) Right. So you just go to chat GPT and you say something like. Help me install Chromium tools or Chrome Dev tools for play right on my WSL system. (t: 120) Say something like this. Right. (t: 130) It should give you some pseudo commands. Yeah, it's this here. You basically need to run these commands here. You have to try and get Chromium browser. (t: 140) Right. Playwright is not going to work out of the box for you. It just doesn't work out of the box. Unfortunately, you have to play around. With it. You have to feed Claude code the errors and you have to actually fix it yourselves. (t: 150) Now, while I'm sitting here explaining this to you, this is continuing to test my application for me to ensure that everything is working. (t: 160) This is basically just creating a SAS MVP in the background. So this goes beyond what we've been talking about recently, where you kind of you can fix most things, but now you can literally fix everything. (t: 170) So let's just get into this process. So open up a new terminal here so I can show you guys exactly how this is done. (t: 180) MKD video example. Um, what is this context? Engineering. Then we'll see the video example context engineering. (t: 190) Then we'll do WSL dash D Ubuntu. Now, one thing you want to make sure that you're doing here is that you're using, um, dangerously skip permissions because otherwise it's just going to be. (t: 200) Way too. Annoying to use. Right? So before we do that, actually, let's just do Claude. (t: 210) Actually, we can just manually clone it. I know how to clone something. Get clone. (t: 220) And then a full stop. Actually, we'll yeah, we'll do it. We'll stop. Yeah. Now, if we do LS here, you'll see this stuff is here. So now if we do Claude dangerously skip permissions. (t: 230) Hang on. There it is. Okay, so let's grab the prompt from my school community. (t: 240) First thing in the description, by the way. Okay, so let's just exit out of there and let's actually just do code and then a full stop. What this does is it opens up in Visual Studio code. (t: 250) Um, we need to actually create an initial dot MD. So we'll just do that now. Initial dot MD. And then we'll copy and paste this onto here. (t: 260) Right? I'm just gonna grab one from before. It's a very simple one. Okay, so the first problem we run is this. Uh, first prompt here. I'll put it in the description of the video. (t: 270) All this does is it reads the, oh, wait, oops. I forgot to put this. (t: 280) Let's put this inside. Initial. MD. Make sure that you put something inside that initial MD. Obviously, let's say continue here. (t: 290) Okay, so that reads the instructions and then what you can do is you can do slash (t: 293) slash. Generate. Dot. B. Uh, dash. P. R. P. (t: 300) Initial. Dot. MD. Press enter. Okay, so once it gets to this stage, you can say spin multiple serve agents to do this more quickly. (t: 310) Okay, guys, so I've recently done like 12 of these builds. I'm not gonna go through this entire thing, but once you've got the playwright MCP, right? (t: 320) So actually just thinking about this because this is a new instance, I don't actually have the playwright MCP installed. Right? Oh, I do. Okay. Okay. I actually have no idea how the playwright MCP is actually installed here. (t: 330) But you do need to make sure it's installed. If you don't know how to install an MCP, it's extremely simple and extremely easy. (t: 340) People overcomplicate this massively. All you need to do is grab playwright is slightly different because you need chromium on WSL, which makes it slightly more annoying. (t: 350) But just grab this information here and copy it. And then you can just go back to the game. Okay. And then say to chat, GBT, create me a command to add this MCP to world code on WSL. (t: 360) Right. Give it that. And then go to here and do cloud code MCP install on Google. (t: 370) And let's go here. Right. And then we can just grab this page by pressing copy page. (t: 380) And then put this underneath this like that. And it should just give us a command here. (t: 390) There it is. So you can choose which one you want. You can tell it which one you want, et cetera, et cetera. Right. But yeah, that's how you set it up. So you then would run this command like that. (t: 400) But for some reason, I'm not sure why this was already included in the GitHub repo, I believe. So probably when you clone this, it will already have MCP, the playwright MCP installed. (t: 410) If not, I'll leave this command in the description of this video. Okay. So I'll just stop the video. If you want to see a full build, guys, feel free to watch the video yesterday. (t: 420) This goes through the entire building process just without playwright. All I've done is I've added the playwright MCP stuff to the prompt so that it can actually fix these things itself instead of me spending hours and hours and hours copying and pasting browser logs from here into here. (t: 440) So, yeah. So you can see when you activate dangerously skip permissions, you open up browser console like this. (t: 450) You right click inspect, open up. And then you just let it fix itself. You can come back after two to three hours. (t: 460) And again, you're not paying per usage with clawed code. I can come back in a few hours and literally everything will just be ready to go. (t: 470) Right. So it's like having a replit on your computer with the ability for it to code. Right. So it's a code whatever you want. I would honestly say rip replit and things like that with this because this is like next level shit. (t: 480) I think I'll leave the video there, guys. I'm not going to go and do an entire build. (t: 490) As you can see, this is a build that I started last night using this exact methodology. And you can see that it's doing exactly that. (t: 500) So I let it run. Right. And as you can see, it said error loading customers. Now it can add customers. I didn't feed it a single thing. (t: 510) Right. This all came out. Wait, it's trying to do this. So it's now compacting. So I'm definitely going to leave the video there. (t: 520) But you can just see how it just fixed itself. Right. Obviously, it hasn't got through to the pipelines or the AI system or whatever. But this is the stuff that if you just leave it overnight on dangerously skip permissions, (t: 530) it's not connected to GitHub or anything like that. So you don't have to worry. And it will literally just use Docker to run the project. (t: 540) Right. So this is Swaggins Yolo. This is the project that is running, I believe. Yeah, there it is. And then it will use Docker alongside Playwright. (t: 550) And this is all in the prompt. Right. All you have to do is have Docker's CLI set up, Playwright set up with the MCP. (t: 560) And it will literally just error fix for you all day. Now, if people want to see a full build of this, I'll probably do that tomorrow anyway. But let me know in the comments if that's what you want to see. (t: 570) But effectively, it's just yesterday's build with Playwright installed. It took me about 20 minutes to work out how to install Playwright on WSL. I'll leave the video there, guys. (t: 580) Thank you so much for watching. Thank you for watching all the way to the end of the video. As usual, you're an absolute fucking legend. And I will see you very, very soon with some more content. Peace out.

