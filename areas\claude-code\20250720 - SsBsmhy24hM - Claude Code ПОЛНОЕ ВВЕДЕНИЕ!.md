---
title: <PERSON> Code ПОЛНОЕ ВВЕДЕНИЕ!
artist: AI RANEZ
date: 2025-07-20
url: https://www.youtube.com/watch?v=SsBsmhy24hM
---

(t: 0) Я занимаюсь AI и делюсь этим на канале AI Runnits. Меня зовут Александр, желаю вам приятного просмотра. Как вы могли заметить, сейчас много шума поднимается вокруг Cloud Code. (t: 10) Многие говорят, что этот инструмент круче, чем Cursor, Visual Studio Code, Scopilot, Windsurf, Trey и тому подобное. Я скажу вам сразу, что да, это так и есть. (t: 20) Cloud Code реально круче. Именно всеобъемлющий тул, в него можно копать очень-очень долго. Это очень мощный инструмент, с которым нужно разобраться, как он работает и уметь им пользоваться. (t: 30) В этом видео я расскажу вам, что такое Cloud Code, что значит CLI, как его настроить, как им пользоваться, какие базовые фишки есть, как подключить MCP. (t: 40) В общем, я вам дам полную introduction в Cloud Code. Лучше разобраться сейчас, чем потом, когда это будет уже поздновато. (t: 50) Хочу сначала начать с некоторых опасений, про которые многие думают, я и сам про них думал. В общем, первое, это, наверное, прайсинг. Многие думают, что Cloud Code это очень дорого, что он работает только по API, нужны сотни-сотни долларов. (t: 60) Ну, во-первых, здесь есть подписки, то есть это не так. Подписки есть 20 баксов минимальная Pro версия и Max, которая стоит 100 баксов. (t: 70) В подписку за 20 баксов выходит Cloud Code. Это как раз-таки инструмент, про который я сегодня снимаю introduction. (t: 80) Да, с определенными лимитами. Достаточно адекватный. Плюс сюда же, в эту же подписку у вас входит Cloud Desktop. (t: 90) Это выглядит вот так. Вот он, Cloud Desktop. Здесь можно выбрать модели разные. Cloud Zanet 4 и Cloud Opus 4. Ресерч. (t: 100) У вас подписка Pro в самом Cloud Desktop. Вы можете им пользоваться в браузере, либо вот удобно иметь приложение. Только вдумайтесь, за 20 баксов вы получаете Cloud Code для кодинга. (t: 110) Вы получаете Cloud Zanet 4. И получаете Cloud Opus 4 в Cloud Desktop. В топе. Ресерчи и всякое другое. Да, с определенными лимитами. (t: 120) Для работы с Cloud Code, активной работы, лимита хватает примерно на час-два. В зависимости от того, кто как работает. Я говорю среднее значение. (t: 130) Но если вы так много работаете и вам нужны очень большие лимиты, просто берете Max за 100 баксов в месяц, и вы в эти лимиты упираться вообще не будете, (t: 140) потому что она дает 20x на то, что было в подписке Pro. В общем, подытожить, ребята. Cloud Code это не дорогой инструмент. (t: 150) Он стоит точно так же, как курсор. Но, действительность в том, что за 20 баксов вы еще получаете Opus Zanet в Cloud Desktop. Второе. Есть еще такое опасение. (t: 160) То, что этот AI-агент не в чате справа, как в курсоре. Или в Капайлоте, как все привыкли, что в чате у вас справа такой friendly user interface. (t: 170) А этот инструмент находится в терминале. И многих вайп-кодеров это напрягает. Они это боятся. И это очень важно. Они боятся терминала. В принципе, не только вайп-кодеры. Многие люди почему-то боятся терминала. (t: 180) Но в этом реально нет ничего страшного. Во-первых, работать в терминале с этим инструментом несложно. Вы быстро привыкаете. Плюс сейчас появляется множество расширений для работы с Cloud Code. (t: 190) Вне терминала. Всякие user interface. И я думаю, что это просто вопрос времени, когда кто-то сделает поистине крутой user interface для работы с Cloud Code. (t: 200) Но даже на данный момент работать в терминале достаточно удобно. Реально. Единственная проблема это редактировать текст. Не бойтесь работать в терминале с этим инструментом. (t: 210) Это реально достаточно удобно. Плюс есть официальное расширение от Visual Studio Code для работы с Cloud Code, о котором я сегодня тоже расскажу. (t: 220) Cloud Code, CLI, это Command Line Interface. То есть CLI это Command Line Interface. Cloud Code это просто AI-агент в терминале, (t: 230) который может писать код. То есть это Agentsy Command Line Tool от компании Antron. Код, который работает прямо в терминале. И может писать код. Я хочу сразу поговорить про ключевые отличия от конкурентов, (t: 240) таких как Cursor, Visual Studio Code, Copilot, WinSurf и тому подобное. Самый главный плюс, то что вы работаете без посредников. (t: 250) Cloud Code это инструмент от разработчиков Antropico. То есть вы напрямую работаете с самим Cloud, с самим Antropico без каких-либо посредников в виде Cursor. (t: 260) Cloud Code глубокий, и вы можете работать с кодом, с кодом, с кодом. Более глубокое понимание контекста. Реально удивитесь насколько лучше Cloud Code действует вашим командам, (t: 270) пишет код и вообще лучше работает. Потому что опять же все происходит напрямую. Поэтому я думаю со вступлением мы закончили. Давайте перейдем уже к установке. (t: 280) Посмотрим как это все выглядит на практике. Для начала вам нужен Node.js хотя бы 18 версия. Ссылку я оставлю в своем первом канале. (t: 290) Переходите, ссылка тоже в описании под этим видео. В общем устанавливайте Node.js. Дальше переходите Visual Studio Code либо Cursor. Совершенно не важно. Просто вы любое по факту IDE. (t: 300) Я сегодня буду это ранить Visual Studio Code. Но опять же вы можете использовать это где вам угодно. В Cursor совершенно не важно. Открываете терминал. (t: 310) Открываете терминал. Для того чтобы установить Cloud Code вам нужно просто открыть терминал и приписать вот эту команду. Не переживайте, команда будет либо в описании, (t: 320) либо в закрепленном комментарии. В общем найдете пониже. И у вас происходит установка кода. Дальше вы просто нажимаете Yes. Авторизируйте с помощью вашего аккаунта на котором есть подписка на Cloud. (t: 330) То есть вы купили подписку за 20 баксов либо за 100. И авторизируйтесь именно с этим аккаунтом. Там ничего сложного. Правда вы разберетесь. (t: 340) После того как вы зашли, везде авторизируетесь. Вы хотите запустить Cloud. Для этого вы просто прописываете в терминале Cloud. Это прописывается в абсолютно любом терминале. (t: 350) Вы можете открыть, к примеру, просто обычный терминал. NIV IDE. Навестись на директорию с вашим файлом, вашей папкой. В моем случае это Content.TG. (t: 360) И прописать Cloud. Нажимаете Enter. Активируется Cloud код. Он вас встречает. И вот так это все выглядит в терминале. (t: 370) Не пугайтесь. Это реально не так страшно. И здесь в принципе вы пишете что вам нужно. Например, давайте вот уже у меня готовый проект. Я его делал. Я его не доделал. (t: 380) Допустим пишем Explain me this project. Прописываете команду. Вы общаетесь точно так же как вы делали это в курсоре в чате. Точно так же как вот здесь. (t: 390) И вот есть количество токенов, которые расходуются. Все мы получили ответ, что это Telegram Channel Parser. И здесь описание Purpose, Key Components. В общем он сделал отличное описание проекта. (t: 400) Объяснил про что он. Также еще важно уточнение, что на подписке PRO у вас доступна только Cloud Zone. На подписке MAX у вас доступна модель Opus. (t: 410) Давайте честно, ребята. Разницы, огромной разницы нет между Cloud Zone и Cloud Opus. Дальше. Давайте пропишем слэш. И здесь будут разные команды. (t: 420) Переключаться здесь не с помощью мышки, а с помощью вверх-вниз клавиш. Просто как в терминале. Здесь вот лист команд всех, которые есть. (t: 430) Add Directory, Init. И описание, что эта команда значит. То есть вы просто прописываете слэш и можете выбрать команду, которая вам нужна. Давайте пропишем слэш Init. (t: 440) Это нужно для инициализации проекта. Для инициализации нового Cloud.md. Markdown файла с Cloud. Сейчас я вам объясню, что в нем находится. Прописываете Init. (t: 450) Просто у вас, допустим, новый проект или уже в готовом проекте вы хотите работать с Cloud кодом. Вы просто прописываете Init, как такой первый шаг. У вас создается файл Cloud.md. (t: 460) Это Markdown файл с инструкциями и настройками проекта для Cloud код. В общем, это специальный конфигурационный файл, который помогает Cloud лучше понимать контекст вашего проекта. (t: 470) В нем обычно есть описание проекта, структура проекта, технологический стэк, правила и специфические инструкции, которые вы можете дать Cloud. (t: 480) Этот файл настраивается именно вами. Вы можете настроить, как хотите. Самое главное, что Cloud код читает этот файл при каждом обращении к проекту. (t: 490) Грубо говоря, у него всегда в контексте, о чем ваш проект. Ваш технологический стэк, правила и так далее. На самом деле, похоже на... Memory Bank курсор. (t: 500) Если вы смотрели... Так, давайте нажмем Yes. Если... Чтобы нажать Yes, нужно нажимать Enter. Если вы смотрели видео про курсор Memory Bank, то знаете, что такое Memory Bank. (t: 510) И, в общем, Cloud.md это как такой некий Memory Bank для Cloud кода. Также, как и с обычным чатом, который здесь справа, вы всегда видите изменения, которые Cloud хочет сделать. (t: 520) Вы можете с ними соглашаться, можете не соглашаться. То есть, все, в принципе, тоже самое. Я, конечно, соглашаюсь, нажимая Enter. (t: 530) Это означает Yes. У нас вот здесь появился файл Cloud.md. Здесь описание, опять же, Project Overview. Development Setup, о чем я говорил. Installation. (t: 540) Environment Configuration. Architecture. Core Components. Data Flow. В общем, вся суть, вся информация о проекте, опять же, похожа на Memory Bank. Дальше, давайте поговорим о том, как сюда закидывать картинки. (t: 550) Как в чат, вот сюда. Для этого просто делаем скрин. Например, вот этой части. И просто этот скриншот перетаскиваем сюда, в сам терминал. (t: 560) Есть путь до вашего скриншота, и он понимает то за картинка. Дальше, давайте напишем, например, таким вот простым способом, просто скидываете в терминал скриншот, вы можете давать, соответственно, фотографии в Cloud Code. (t: 570) Ничего сложного, просто перетаскиваете в терминал. (t: 580) Так, To Identical Projects. Cloud MC Highlighted. Python Project Structure. Это, кстати, важно. Потому что у многих вопросы, как сюда закинуть картинку. (t: 590) Дальше, давайте поговорим про MCP. Как интегрировать MCP в Cloud Code. Достаточно несложно, но для начала нам нужно выйти из Cloud Code. (t: 600) Как это сделать? Мы нажимаем Ctrl C и жмем еще раз Ctrl C. И тогда мы как бы выходим с Cloud. А дальше, для того, чтобы добавить какие-то MCP, посмотрите, какие MCP у вас есть. (t: 610) Вы прописываете Cloud MCP List. И так далее. Вот у меня, допустим, сейчас установлена MCP-шка Play Right. (t: 620) Давайте ее удалим. Для того, чтобы удалить эту MCP, мы прописываем Cloud MCP Remove. (t: 630) И название нашего MCP. В моем случае это Play Right. Все, я удалил. Для того, чтобы установить MCP, вам нужна просто команда. (t: 640) Давайте на примере, опять же, Play Right. И установим обратно этот MCP-сет. Для этого можно перейти на GitHub-репозиторий с Play Right. (t: 650) И вот здесь будет просто команда для добавления MCP в Cloud Code. В принципе, для многих MCP уже есть такие команды. И в этом ничего сложного нет. (t: 660) Переходим обратно в Visual Studio Code. (t: 663) Вставляем просто эту команду. Нажимаем Enter. Все. MCP Edit. (t: 670) Для того, чтобы посмотреть, добавился ли он, мы опять пишем Cloud MCP List. И смотрим все MCP, которые у нас есть. Мы установили. Потом мы переходим обратно в Cloud. (t: 680) Пишем Cloud. У нас запускается Cloud. И можем здесь прописать команду MCP. И и правда у нас здесь MCP. Можете им пользоваться так же, как вы пользовались, допустим, в этом чате. (t: 690) Все точно так же. Он будет точно так же работать. Также есть Plan Mode. Для того, чтобы его активировать, вы нажимаете Shift. (t: 700) И первый этап Auto Accept. Все, нам не нужно Enter нажимать и так далее. И второй этап это уже Plan Mode. Очень крутой мод. На самом деле он экономит много токенов. (t: 710) То есть, когда вы что-то хотите, например, закодить, что-то забилдить, вы можете в этом Plan Mode порассуждать, какие вам технологии использовать, какой стэк, как правильно это реализовать. (t: 720) То есть, Plan Mode на самом деле интересная вещь. Например, давайте вот Plan Mode мы включили. Напишем Do we need. И все. Plan Mode активирован. Он думает. (t: 730) I'll have to understand what needs to be done in this project else. Let me examine the key files. Он читает. Read me Cloud MD. Он дает как бы план, что можно, допустим, сделать еще. (t: 740) Critical Bug Fixes, Missing Development Infrastructure, Improvements, Code Quality. Крутой мод. Вы его можете использовать как бы для планинга, для Development Plans и тому подобных вещей. (t: 750) Включается опять же Shift и два таба. В общем, это такие основные вещи, которые вам нужно про него знать. (t: 760) После того, как вы инициализировали проект, у вас есть Cloud MD. Вы уже можете, в принципе, сюда писать промты и уже билдить то, что вы хотите. Вы точно также работаете, как и в этом чате справа, в курсоре Visual Studio Code. (t: 770) Точно также пишете промт. Еще, кстати, крутая фишка. Допустим, вам нужно сослаться на какой-то файл. (t: 780) Вы просто его перетаскиваете в терминал и будет путь до этого файла. Все. Нажимаем Enter. Здесь, опять же, можете наблюдать токены. Все. Вот у вас изменения. Слева, справа. (t: 790) Слева то, что было старое. И справа то, что, соответственно, не было. И справа то, что, соответственно, новое. И вы можете также изучать эти изменения. Соглашаться с ними или не соглашаться. Допустим, мне они понравились. (t: 800) Я нажимаю Yes. И как мы видим, что да, он изменился. Как бы вот только что Cloud Code мне дописал код. Не пугайтесь. Этот инструмент не сложен. (t: 810) К нему надо уметь работать. И так вы дальше продолжаете работать просто в командной строке. Допустим, вы не хотите делать это в терминале. Для этого есть официальное расширение от Anthropica. (t: 820) Вы просто пишете в Extensions Cloud Code. Сюда заходите и устанавливаете это расширение. Чтобы это расширение вызвать, вы нажимаете вот сюда. (t: 830) И у вас справа есть что-то наподобие чата. Словно говоря, вот чат. И точно так же у вас есть расширение для Cloud Code. И здесь, опять же, вы пишете, промотаете, вайб-кодите, работаете со своим проектом. (t: 840) Вот такое introduction в Cloud Code. Мы поговорили о пассениях, о том, что это такое, в чем плюсы состоят. (t: 850) Опять же, в том, что вы работаете напрямую. Как это все дело установить, как с этим работать, что такое init, команды, как подключить MCP. Что значит этот файл Cloud MD. (t: 860) Зачем это вообще нужно. Как сюда скидывать фотографии, как с ними работать. Поговорили про расширение официальное от Anthropica в Visual Studio Code. (t: 870) Такая основа, это introduction в Cloud Code. От себя добавлю, что я отказался от курсора. Я теперь там не работаю. Мой основной инструмент это... (t: 880) Cloud Code. Очень мощный инструмент. В него можно еще очень глубоко копать. Если вам интересно дальше продолжить со мной копать этот инструмент. Обязательно поставьте лайк. (t: 890) Напишите комментарий под этим видео, что вам это реально интересно. И я буду дальше выкидывать какие-то фишки, наблюдения и тому подобное. Для более глубокой работы с Cloud Code. (t: 900) Потому что, честно, инструмент всеобъемлющий. Апдейты выходят чуть ли не через день. То есть копаться здесь можно очень долго. Если правильно работать с этим инструментом. (t: 910) То можно делать поистине грандиозные вещи. Я всех благодарю за просмотр. Скоро увидимся.

