#!/usr/bin/env python3
"""
Semantic Marker Insertion Tool
=============================

This script reads text from a text file, finds timestamp markers with titles
in the format "(t: X) Title", and inserts corresponding semantic topic markers
into a user-selected markdown file.

Features:
- Reads text from a .txt file (browsed or specified via command line)
- Detects "(t: X) Title" patterns
- Prompts user to select markdown file using tkinter (or specify via command line)
- Inserts "(topic: title)" markers after timestamp markers
- Preserves original file formatting
- Can be used by agents with command-line arguments

Example transformation:
Input: "(t: 15) This is a cool title"
Output: "(t: 15) (topic: this is a cool title) This is a cool title"

Usage:
- Interactive mode: python 4_insert_semantic_markers.py
- Agent mode: python 4_insert_semantic_markers.py --txt-file "input.txt" --markdown-file "output.md"

Requirements:
- tkinter (usually comes with Python)

Author: Generated for Transcript Management System
"""

import os
import sys
import re
import argparse
import tkinter as tk
from tkinter import filedialog, messagebox
from typing import List, Tuple, Optional


def get_text_file() -> Optional[str]:
    """Browse for and select a text file."""
    root = tk.Tk()
    root.withdraw()  # Hide the main window
    
    file_path = filedialog.askopenfilename(
        title="Select Text File with Timestamp Markers",
        filetypes=[("Text files", "*.txt"), ("All files", "*.*")],
        initialdir=os.getcwd()
    )
    
    if not file_path:
        messagebox.showwarning("No File Selected", "No text file was selected. Exiting.")
        return None
        
    return file_path


def read_text_file(file_path: str) -> Optional[str]:
    """Read content from a text file."""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read().strip()
        
        if not content:
            print(f"❌ File is empty: {file_path}")
            return None
            
        return content
    except Exception as e:
        print(f"❌ Error reading file {file_path}: {e}")
        return None


def get_markdown_file() -> Optional[str]:
    """Browse for and select a markdown file."""
    root = tk.Tk()
    root.withdraw()  # Hide the main window
    
    file_path = filedialog.askopenfilename(
        title="Select Markdown File to Insert Semantic Markers",
        filetypes=[("Markdown files", "*.md"), ("All files", "*.*")],
        initialdir=os.getcwd()
    )
    
    if not file_path:
        messagebox.showwarning("No File Selected", "No markdown file was selected. Exiting.")
        return None
        
    return file_path


def extract_timestamp_titles(text: str) -> List[Tuple[str, str, str]]:
    """
    Extract timestamp markers with titles from text.
    
    Returns:
        List of tuples: (full_match, timestamp, title)
    """
    # Pattern to match "(t: X) Title" where X is a number and Title is the rest of the line
    pattern = r'\(t:\s*(\d+)\)\s+(.+?)(?=\s*\(t:\s*\d+\)|$)'
    
    matches = []
    for match in re.finditer(pattern, text, re.MULTILINE | re.DOTALL):
        full_match = match.group(0)
        timestamp = match.group(1)
        title = match.group(2).strip()
        
        # Clean up the title (remove extra whitespace and newlines)
        title = re.sub(r'\s+', ' ', title)
        
        matches.append((full_match, timestamp, title))
    
    return matches


def create_semantic_marker(title: str) -> str:
    """Create a semantic topic marker from a title."""
    # Convert title to lowercase and create topic marker
    topic_text = title.lower()
    return f"(topic: {topic_text})"


def insert_semantic_markers(markdown_file_path: str, timestamp_titles: List[Tuple[str, str, str]]) -> bool:
    """Insert semantic markers into the markdown file based on patterns found in text file."""
    try:
        # Read the markdown file
        with open(markdown_file_path, 'r', encoding='utf-8') as f:
            markdown_content = f.read()
        
        # Create a copy to modify
        modified_content = markdown_content
        
        # For each timestamp title from the text file, find matching patterns in the markdown
        successful_replacements = 0
        
        for full_match, timestamp, title in timestamp_titles:
            # Look for existing timestamp markers in the markdown that match this timestamp
            timestamp_pattern = rf'\(t:\s*{re.escape(timestamp)}\)'
            
            # Find all occurrences of this timestamp in the markdown
            matches = list(re.finditer(timestamp_pattern, modified_content))
            
            if matches:
                # For each match, insert the semantic marker right after the timestamp
                for match in reversed(matches):  # Process in reverse to avoid position shifts
                    start_pos = match.end()
                    
                    # Create the semantic marker
                    semantic_marker = f" {create_semantic_marker(title)}"
                    
                    # Insert the semantic marker after the timestamp
                    modified_content = (
                        modified_content[:start_pos] + 
                        semantic_marker + 
                        modified_content[start_pos:]
                    )
                    successful_replacements += 1
            else:
                print(f"   ⚠️  No matching timestamp (t: {timestamp}) found in markdown file")
        
        if successful_replacements > 0:
            # Write back to file
            with open(markdown_file_path, 'w', encoding='utf-8') as f:
                f.write(modified_content)
            print(f"   ✅ Applied {successful_replacements} semantic marker insertions")
        
        return successful_replacements > 0
    
    except Exception as e:
        print(f"❌ Error modifying file: {e}")
        return False


def parse_arguments():
    """Parse command line arguments."""
    parser = argparse.ArgumentParser(
        description="Insert semantic markers into markdown files based on timestamp markers.",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  Interactive mode:
    python 4_insert_semantic_markers.py
    
  Agent mode:
    python 4_insert_semantic_markers.py --txt-file input.txt --markdown-file output.md
        """
    )
    
    parser.add_argument(
        "--txt-file", 
        help="Path to input text file containing timestamp markers"
    )
    
    parser.add_argument(
        "--markdown-file", 
        help="Path to markdown file where semantic markers will be inserted"
    )
    
    return parser.parse_args()


def main():
    """Main function to process text content and insert semantic markers."""
    args = parse_arguments()
    
    print("Semantic Marker Insertion Tool")
    print("=" * 50)
    
    # Determine if running in interactive or agent mode
    interactive_mode = not (args.txt_file and args.markdown_file)
    
    if interactive_mode:
        print("Running in interactive mode...")
    else:
        print("Running in agent mode...")
    
    # Step 1: Get input text file
    if args.txt_file:
        txt_file = args.txt_file
        if not os.path.exists(txt_file):
            print(f"❌ Text file not found: {txt_file}")
            return
        print(f"Using specified text file: {txt_file}")
    else:
        print("📁 Select text file with timestamp markers...")
        txt_file = get_text_file()
        if not txt_file:
            return
        print(f"Selected file: {txt_file}")
    
    # Read text content
    text_content = read_text_file(txt_file)
    if not text_content:
        return
    
    print(f"✅ Found {len(text_content)} characters in text file")
    
    # Step 2: Extract timestamp titles
    print("🔍 Searching for timestamp markers with titles...")
    timestamp_titles = extract_timestamp_titles(text_content)
    
    if not timestamp_titles:
        print("❌ No timestamp markers with titles found in text file")
        print("   Expected format: (t: X) Title")
        return
    
    print(f"✅ Found {len(timestamp_titles)} timestamp markers with titles:")
    for i, (full_match, timestamp, title) in enumerate(timestamp_titles, 1):
        preview = title[:50] + "..." if len(title) > 50 else title
        print(f"  {i}. (t: {timestamp}) {preview}")
    
    # Step 3: Get markdown file
    if args.markdown_file:
        markdown_file = args.markdown_file
        if not os.path.exists(markdown_file):
            print(f"❌ Markdown file not found: {markdown_file}")
            return
        print(f"Using specified markdown file: {markdown_file}")
    else:
        print(f"\n📁 Select markdown file to insert semantic markers...")
        markdown_file = get_markdown_file()
        if not markdown_file:
            return
        print(f"Selected file: {markdown_file}")
    
    # Step 4: Insert semantic markers
    print(f"\n🔄 Inserting semantic markers...")
    success = insert_semantic_markers(markdown_file, timestamp_titles)
    
    if success:
        print(f"✅ Successfully inserted {len(timestamp_titles)} semantic markers into {os.path.basename(markdown_file)}")
        print("\nExample transformation:")
        if timestamp_titles:
            _, timestamp, title = timestamp_titles[0]
            semantic_marker = create_semantic_marker(title)
            print(f"  Before: (t: {timestamp}) {title}")
            print(f"  After:  (t: {timestamp}) {semantic_marker} {title}")
    else:
        print("❌ Failed to insert semantic markers")
    
    if interactive_mode:
        input("\nPress Enter to exit...")


if __name__ == "__main__":
    main()