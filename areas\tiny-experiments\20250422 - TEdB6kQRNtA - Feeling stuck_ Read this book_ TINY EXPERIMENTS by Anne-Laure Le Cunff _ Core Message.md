---
title: Feeling stuck? Read this book: TINY EXPERIMENTS by <PERSON><PERSON><PERSON><PERSON> | Core Message
artist: Productivity Game
date: 2025-04-22
url: https://www.youtube.com/watch?v=TEdB6kQRNtA
---

- [00:00:00](https://www.youtube.com/watch?v=TEdB6kQRNtA&t=0) - I recently read Tiny Experiments by <PERSON><PERSON><PERSON><PERSON>. Several years ago, <PERSON><PERSON><PERSON><PERSON> was successful by society's standards, but deeply unhappy. She had landed her dream job at Google, worked with a brilliant team, and was ascending the corporate ladder. But inside, something was off. Her unhappiness came from years of quietly suffocating her professional curiosity. Every time she contemplated a new path, like taking a university course unrelated to her

- [00:00:30](https://www.youtube.com/watch?v=TEdB6kQRNtA&t=30) - But inside, something was off. Her unhappiness came from years of quietly suffocating her professional curiosity. Every time she contemplated a new path, like taking a university course unrelated to her job, starting a newsletter, or testing out a startup idea, she shut it down. She stuck to the linear path from school, to steady promotions, to a senior position. But the predictable path started to feel like a prison. Like <PERSON><PERSON><PERSON><PERSON>, ignoring your career curiosity is like disconnecting the smoke alarm in your house. It might seem harmless at first, but eventually, it becomes catastrophic. Sadly, many of us can't hear our inner curiosity alarm at all, because it's drowned out by

- [00:01:00](https://www.youtube.com/watch?v=TEdB6kQRNtA&t=60) - It might seem harmless at first, but eventually, it becomes catastrophic. Sadly, many of us can't hear our inner curiosity alarm at all, because it's drowned out by two loud cognitive scripts that pull us toward professional misery. The sequel script, and the crowd-pleaser script. The sequel script is the story we tell ourselves that we must keep building on what we've already done. Like a movie studio that greenlights yet another sequel because the last one made money, we follow the same formula again and again. Listening to the sequel script might lead to financial stability, but it rarely leads to mornings where you wake up energized by your work.

- [00:01:30](https://www.youtube.com/watch?v=TEdB6kQRNtA&t=90) - we follow the same formula again and again. Listening to the sequel script might lead to financial stability, but it rarely leads to mornings where you wake up energized by your work. The crowd-pleaser script stems from a belief that we shouldn't disappoint people or risk looking foolish. So if we can't easily justify a change to others, we maintain the status quo. In other words, we say no to unconventional paths because they might raise some eyebrows. Together, these scripts lock us into a linear mindset that we can't just ignore. We can't just ignore the fact that we're not the only ones who can do it. We can't just ignore the fact that we're not the only ones who can do it. And prevent us from exploring the unconventional and uncertain detours that may lead to a fulfilling career. It's like planning a cross-country trip to visit a legendary theme park, only to arrive

- [00:02:00](https://www.youtube.com/watch?v=TEdB6kQRNtA&t=120) - And prevent us from exploring the unconventional and uncertain detours that may lead to a fulfilling career. It's like planning a cross-country trip to visit a legendary theme park, only to arrive and find it's nothing like the brochure. The rides are closed, the paint is peeling, and the staff look like they gave up years ago. And all the while, you passed up better opportunities along the way. Like stopping in a small town to enjoy a music festival where you meet a new friend who randomly invites you to go rock climbing nearby. And then you're like, I'm not sure I'm going to be able to do this. I'm going to have to do this. There, you learn that you enjoy rock climbing more than you thought, and want to spend the next few weeks learning to climb. In a professional context, those side roads are what Ann Lohr calls

- [00:02:30](https://www.youtube.com/watch?v=TEdB6kQRNtA&t=150) - There, you learn that you enjoy rock climbing more than you thought, and want to spend the next few weeks learning to climb. In a professional context, those side roads are what Ann Lohr calls tiny experiments. Tiny experiments are how you explore curiosity without quitting your job or blowing up your life. They're short, focused, and designed to create momentum. Think of them as low-stakes tests with high upside potential. Here are a few examples. 30 days of newsletter writing. Each morning, write a short piece on a topic you're eager to master, like investing or AI. Curate the latest news in your unique style, or bring one key concept to

- [00:03:00](https://www.youtube.com/watch?v=TEdB6kQRNtA&t=180) - 30 days of newsletter writing. Each morning, write a short piece on a topic you're eager to master, like investing or AI. Curate the latest news in your unique style, or bring one key concept to people's attention. This tiny experiment will work your creative muscles, accelerate learning, and build a body of work. Or maybe commit to a 50 days of contribution tiny experiment where you offer daily help on Reddit or Discord. If you're interested in learning more about the world, you can subscribe to our channel and hit the bell icon to get notified every time we post a new video. And if you're interested in learning more about the world, you can subscribe to our channel and hit the bell icon to get notified every time we post a new video. If you're interested in learning more about the world, you can subscribe to our channel and hit the bell icon to get notified every time we post a new video. Choose something you know well, whether it's a specific software program, a certain lifestyle, or a hobby you excel at. Your posts and replies could lay the groundwork for a future coaching career or an online course focused on your niche subject. Or how about this tiny

- [00:03:30](https://www.youtube.com/watch?v=TEdB6kQRNtA&t=210) - Choose something you know well, whether it's a specific software program, a certain lifestyle, or a hobby you excel at. Your posts and replies could lay the groundwork for a future coaching career or an online course focused on your niche subject. Or how about this tiny experiment? 100 days of video shorts. Share one quick tip a day on productivity, fitness, or mindset. Make each one slightly different to sharpen your video production skills and see if you enjoy the creative process. Make each one slightly different to sharpen your video production skills and see if you enjoy the creative process. Make each one slightly different to sharpen your video production skills and see if you enjoy the creative process. Or 12 weeks of full-course meals for friends. Test out recipes that could one day fuel a food truck or restaurant. And here's an odd but intriguing one. 12 weeks of 3D model designs.

- [00:04:00](https://www.youtube.com/watch?v=TEdB6kQRNtA&t=240) - Or 12 weeks of full-course meals for friends. Test out recipes that could one day fuel a food truck or restaurant. And here's an odd but intriguing one. 12 weeks of 3D model designs. Take a cue from a man in the book who used his 3D printer to sculpt busts of historical figures. Over time, his models became so lifelike he started selling them on Etsy, and now he makes thousands per month. Each tiny experiment is a new experience for him. Each tiny experiment is a new experience for him. Each tiny experiment is a new experience for him. Each tiny experiment is time-bound, output-focused, and iterative. They only last between 15 to 100 days, are focused on daily or weekly output with zero result expectations, such as views or likes, and done in a serial fashion in which you start simple and aim for slight improvements each time.

- [00:04:30](https://www.youtube.com/watch?v=TEdB6kQRNtA&t=270) - Each tiny experiment is a new experience for him. Each tiny experiment is time-bound, output-focused, and iterative. They only last between 15 to 100 days, are focused on daily or weekly output with zero result expectations, such as views or likes, and done in a serial fashion in which you start simple and aim for slight improvements each time. The key is to select a tiny experiment based on something you want to learn and explore, and then commit to a short time commitment where you're bound to learn something new about yourself. Even 100 days is a blip in the span of your working life. The downside is minimal, but the upside could change your life. You might discover something that looks like work to others, but feels like play to you.

- [00:05:00](https://www.youtube.com/watch?v=TEdB6kQRNtA&t=300) - Even 100 days is a blip in the span of your working life. The downside is minimal, but the upside could change your life. You might discover something that looks like work to others, but feels like play to you. You may also find your tribe, a group of people who want to hear what you have to say. A decade ago, committing to writing one book summary a week for 12 weeks connected me with a community of book lovers, lifelong learners, and personal optimizers. My tribe has taught me many valuable lessons. Their support allows me to do what I love for a living. There is one thing you can add to your tiny experiments to supercharge your learning. It's a tool Ann Lohr calls Plus-Minus Next.

- [00:05:30](https://www.youtube.com/watch?v=TEdB6kQRNtA&t=330) - Their support allows me to do what I love for a living. There is one thing you can add to your tiny experiments to supercharge your learning. It's a tool Ann Lohr calls Plus-Minus Next. Once a week, set aside 10 to 15 minutes to create a document with three columns. Put a plus sign in the first column, a minus sign in the second, and an arrow in the third. In the plus column, capture what's working well. These are positive observations from your week of tiny experimentings. For example, I really enjoy learning new video editing techniques. In the minus column, note what's not working well. These are patterns or practices that feel off, clunky, or unproductive.

- [00:06:00](https://www.youtube.com/watch?v=TEdB6kQRNtA&t=360) - These are positive observations from your week of tiny experimentings. For example, I really enjoy learning new video editing techniques. In the minus column, note what's not working well. These are patterns or practices that feel off, clunky, or unproductive. In this column, you might write costs. In this column, write if you're holding time 240 days, or shipping Herman today... In this column, write what adjustments you'll make based on those insights. These are small practical shaves. shifts to double down on what's working and reduce friction. For example, generate a list of 10 video editing techniques to try over the next 10 days, and wake up 45 minutes earlier to do my tiny experiment work.

- [00:06:30](https://www.youtube.com/watch?v=TEdB6kQRNtA&t=390) - shifts to double down on what's working and reduce friction. For example, generate a list of 10 video editing techniques to try over the next 10 days, and wake up 45 minutes earlier to do my tiny experiment work. This simple weekly rhythm turns your tiny experiment into a feedback loop. That's how you grow fast, stay curious, and keep momentum going. After each tiny experiment ends, you get to use your discoveries to chart your next tiny experiment. Maybe it's more of the same, or maybe it's a completely different venture. The key is to never stop experimenting. That was the core message that I gathered from Tiny Experiments by Anne-Laure Lecoq.

- [00:07:00](https://www.youtube.com/watch?v=TEdB6kQRNtA&t=420) - Maybe it's more of the same, or maybe it's a completely different venture. The key is to never stop experimenting. That was the core message that I gathered from Tiny Experiments by Anne-Laure Lecoq. This book was a great wake-up call to start trying new things again and discover new opportunities. I highly recommend this book. If you would like a one-page PDF summary of insights that I gathered from this book, just click the link below and I'd be happy to email it to you. If you already subscribed, please do so. If you haven't subscribed to the free Productivity Game email newsletter, this PDF is sitting in your inbox. If you liked this video, please share it. As always, thanks for watching, and have yourself a productive week.

- [00:07:30](https://www.youtube.com/watch?v=TEdB6kQRNtA&t=450) - If you haven't subscribed to the free Productivity Game email newsletter, this PDF is sitting in your inbox. If you liked this video, please share it. As always, thanks for watching, and have yourself a productive week.

