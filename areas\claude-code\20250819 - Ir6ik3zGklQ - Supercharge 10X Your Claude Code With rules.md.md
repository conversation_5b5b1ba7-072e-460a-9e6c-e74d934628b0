---
title: Supercharge 10X Your Claude Code With rules.md
artist: <PERSON> Swift Crafter
date: 2025-08-19
url: https://www.youtube.com/watch?v=Ir6ik3zGklQ
---

(t: 0) So here's where I'm at lately. I feel like right now, if you're a solo dev, especially if you're living in SwiftUI, Xcode 26, <PERSON>, <PERSON><PERSON><PERSON>, all that, it's kind of wild out here, right? (t: 10) Every week there's some new agent, some AI workflow, or some tool promising to change (t: 20) the way we build apps. It's exciting, but honestly, it can get messy really fast. You find yourself bouncing between so many different ways of working. And if you're not (t: 30) careful, your project turns into this giant pile of half-baked experiments. And I've definitely (t: 40) been there. For a while, I just treated AI tools like, well, a fancy vending machine. I'd drop in (t: 50) a prompt, cross my fingers, and it would be done. And I'd be like, oh, my God, this is and hope whatever came out wouldn't wreck my code base. But yeah, you do that long enough, (t: 60) and eventually something's got to give. You end up cleaning up after the agent as much as you do shipping actual features. Let me just fire up Claude code right now and do a slash in it (t: 70) on this project so you can see what happens when it scans my actual docs and rules.md file. (t: 80) We'll come back to what <PERSON> gets. The thing that really changed it for me, honestly, it wasn't a fancy plugin. It wasn't some new <PERSON> (t: 90) mode. It was just starting to document my workflow, like really document it right alongside my code. I started making this little rules.md file for every project, not because I wanted more homework, (t: 100) not because I needed another checklist, but because I was sick of re-explaining my own thinking (t: 110) to the agent, to myself. And I was like, oh, my God, this is going to be so hard. I'm going to have to do this myself to future me, who's probably just as forgetful as I am now. And when you do that, when you actually treat your rules.md like you're onboarding a teammate, (t: 120) yeah, even if your only teammate is an AI, everything gets easier. Suddenly it feels like (t: 130) you've got someone in your corner. You're not just building alone. So that's what I want to get into today. I'll show you how I set up my rules.md, how it actually fits into my Xcode (t: 140) and Claude.md. Uniqlo dalej bote sem altar.ij' done про셨어요 a de PT 방법 میں (t: 150) rchao proudi o mua if for anyone satisfied safety and election ecosic deus (t: 160) It's too bad people don't mean replacing him is te (t: 170) deal use And yeah, kind of figuring out this personal brand thing as I go. More on that for another time. All right, let's just be real for a second. I did not start out with this super organized rules.md habit. I mean, like every solo dev I know, I was just building an Xcode, scribbling little toedos in the code, maybe dropping a reminder for future me in some Notion doc or worse, my phone. (t: 200) And then like clockwork, I'd fire up Claude or Cursor. And just sort of hope it would, I don't know, pick up on my vibe and not completely miss the point. And yeah, sometimes if you're just cranking out a tiny feature or need a quick bit of code gen, that's actually fine. (t: 220) But the minute your app gets bigger, like you've got logic shared between the app and a widget, or you want the agent to follow your exact architecture instead of riffing off into some wild MVVM experiment. (t: 230) You hit this. Well, the AI has no clue what you actually want unless you really spell it out. (t: 240) That's why rules.emd became a thing for me. It's not just for the agent, honestly. (t: 250) It's the doc I wish I could shove at my past self or, you know, any random teammate, whether they're real or just made of code. Every weird constraint, every little no, don't do it that way. (t: 260) Every time Apple's Hi-Z gives me a headache. Every time I break up a big view. It all lives here. (t: 270) So now whether I'm doing slash init with Claude or booting up cursor or just jumping back into my own code after a break, all my context is waiting for me. (t: 280) It's like this little contract between me, my future self and whatever AI I'm working with. And yeah, it makes everything just a bit less chaotic. (t: 290) And honestly, here's where things got kind of cozy for me. And honestly, this is the part that makes me weird. Yeah. I'm really happy every time I open Xcode. (t: 300) I just stopped tossing these files all over the place. No more buried markdown docs in Dropbox. No half-finished lists floating in Notion. (t: 310) Now, whenever I kick off a new app, I just set up a proper documentation folder right inside Xcode using .docc. Everything, my article, my PRD, my rules.emd lives right there with my code. (t: 320) You know, it sounds simple. But having all my docs in Xcode just feels right. (t: 330) Like I'm already spending most of my day in that window anyway, tweaking Swift UI layouts, refactoring a stubborn view, poking at core data. (t: 340) Why bounce between five tools just to remember what I was thinking? And since I still like to hand build the first pass at any new feature, keeping my rules front and center just fits. (t: 350) It feels native like Xcode is actually home base. Not just where I go to type code. And what's even better, now when I bring Claude or Cursor into the mix, there's zero confusion. (t: 360) The agent's not off hallucinating from some out-of-date doc. (t: 370) It's reading the same stuff I am right from the source. No old copies lurking in a cloud folder. No weird merge conflicts with myself. (t: 380) If you're the kind of person who gets a little burst of joy from a clean project tree, trust me. This just hits you. It's different. So yeah, this is where it actually gets interesting. (t: 390) What goes into my rules.md? It's not just a checklist of do this, don't do that. Honestly, for me, it's like writing a little manifesto for every project, especially with something like this custom clock widget app. (t: 400) I want the agent to feel like it's joining the team and not just spitballing code in the dark. (t: 410) You know, I lay out the whole. Engineering ethos right at the top. (t: 420) I'll literally say, look, you're a senior iOS engineer. We're building an SLC app here. Keep it simple, lovable, complete. Don't get clever with architecture. (t: 430) Stick to MVC. Use core data and delight users, but always stay minimal. Follow Apple's HIG like it's gospel. (t: 440) If you're going to riff within those lines. And then I get super explicit about this. I'm not going to be a genius. I'm going to be a genius. I'm just gonna keep working. I'm going to keep working. It's a way of saying, you know, it's just a mix of the world. (t: 450) It's a way of saying, you know, it's not just about patterns. Shared code between app and widget. It's not optional. It's a rule documented. Every swift UI view, static previews. (t: 460) Always views get split if they get chunky, like a hundred lines is the soft ceiling files. Shouldn't balloon past 200. No third party packages. I don't want the AI going wild and sneaking in random dependencies. (t: 470) Just stick to what ships with swift UI widget. Don'ts are just as important. Don't add random customization. (t: 480) If it's not time zone, hand style, or a label, forget it. No iCloud, no advanced settings, no weird hidden gestures. (t: 490) Widget code? Gotta be shared with the app or it doesn't ship. Even how I handle money. One-time unlock. No subscriptions. No trials. (t: 500) No surprises. But the big thing, and this is what I always hope future me remembers, is the why. The app is SLC, so if you ever get stuck, always lean towards what's more delightful and simple. (t: 510) And if in doubt, just go check article.md. It's all in there. (t: 520) So yeah, it's not just for the AI. It's like a living project brain dump, but structured so any agent or even future Daniel can immediately get the full vibe. (t: 530) And honestly, if you ever find yourself forgetting why you made some weird architecture choice, this is the safety net you didn't know you needed. (t: 540) So, okay, here's where things actually start to feel like magic. Or, you know, at least less like wrangling a stubborn robot. (t: 550) Whenever I'm ready to hand off a new feature or even just a gnarly refactor to clot or cursor, I don't just toss out a random prompt and cross. (t: 560) I point the agent straight at my rules.md and all the other docs in my documentation.c folder. It's like giving the AI a real onboarding packet, not just asking it to guess what matters. (t: 570) With clod code, it's literally slash init, let it scan my files, and suddenly the model (t: 580) actually gets the big picture. It's not just winging it, it knows the project structure. It gets the don'ts. It gets why the previews matter. It understands my weird. (t: 590) No third party rule. Cursor? Same deal. I'm not just hoping it follows my style. I see it actually pick up my preferences and run with them. (t: 600) For once, it feels like it's working with me, not just generating code in a vacuum. And the thing I love when the agent drifts, like maybe it tries to sneak in some wild (t: 610) new architecture or skips out on previews, I just update the rules.md or even ask clod (t: 620) to update. The feedback loop gets tighter every time. It's wild. The AI actually learns. (t: 630) So if you're still treating these tools like slot machines, just hoping for the best, seriously, start briefing them like a real teammate. Reference your rules.md every time. (t: 640) You'll spend way less energy arguing with the bot and honestly, way more time actually shipping features. And yeah. (t: 650) It's kind of the dream, right? And yeah. Let's not pretend this is glamorous. Most days, updating rules.md is just part of the grind. (t: 660) Usually it happens after something breaks. Like I'll realize I forgot to make WidgetKit use the same clock rendering as the app, or maybe I skip previews because I'm in a rush and then pay for it later. (t: 670) But honestly? Every time I add another little reminder or fix to rules.md. (t: 680) My next session with clod or cursor just goes smoother. It's almost like, okay, future me, you won't have to clean up this mess again. (t: 690) And you know, it's kind of become a ritual. I ship a feature. I just sit for a minute, reflect. What was weird? What would have saved me a headache? (t: 700) What do I want the agent or future me to remember next time? Straight into rules.md it goes. If something big changes. Like. I add a new pro unlock or do a big core data refactor. (t: 710) I give myself a little mini retro and update the docs and low key. It's making me a better dev. (t: 720) I spend so much less time re explaining my own weird patterns or backtracking on accidental changes and way more time actually building. (t: 730) Even if I step away for a couple weeks or switch between projects, all my stuff that matters is right there waiting. So. Yeah, it's not always glamorous, but it's real and it actually works. (t: 740) So here's the real bottom line for me. If you're a solo indie dev, you're not just writing code. (t: 750) You're actually managing yourself every single day. There's nobody else to keep you in check or remind you what weird decision you made last (t: 760) Tuesday at midnight. It's way too easy to drown in context switching or just forget some tiny. But critical constraint you swore you'd remember. (t: 770) Honestly, having a rules. MD is like throwing your future self a lifeline. It's the doc you'll actually read later, and it's the one your AI agent will actually follow (t: 780) if you point it in the right direction. And man, it just makes switching between tools or even between whole projects so much less (t: 790) painful. You're not stuck relearning your own quirks every time you come back to a repo. You don't have to make it perfect either. (t: 800) Mine's always a mess full of little. Oh, by the way, notes, random fixes and stuff I thought was obvious, but yeah, it wasn't. (t: 810) But honestly, it saved me so many. Why did I do this? Especially when I'm moving fast or trying to debug something weird months later. (t: 820) And as these AI tools get better, more agentic. More tightly woven into Xcode. I think the indie devs who get used to writing down their intent constraints and habits up (t: 830) front are just going to get way more out of this whole agent work. So yeah, it's not just about the bots. (t: 840) It's about making solo dev a little less lonely and a lot more manageable. So yeah, that's my real take on why I bother with the rules. (t: 850) MD. And honestly, it's not just about making Claude or. I'm not sure. I'm not sure. I don't think it's about making my cursor happy. It's about making life way easier for myself to, you know, it's become this little ritual (t: 860) that keeps my workflow actually human, less lonely, more I don't know, playful. (t: 870) There's something kind of fun about coming back to a project after a month and seeing all those. Hey, don't forget this lines from past me or realizing that. (t: 880) Yeah, I saved myself from tripping over the same bug twice. And honestly, if you've got. Yeah. your own little hacks, weird rituals, or even just a story about how a single line in your (t: 890) rules.md saved your butt, let's hear it. Throw it in the comments, DM me, whatever. I love swapping notes with other solo devs who are figuring this all out. Or if you're just now (t: 900) starting your first rules.md, give it a shot on your next project. Doesn't have to be perfect. (t: 910) Um, even one don't do this line will pay off, I promise. Future you and your AI agent will absolutely thank you. If you found any of this helpful, or you're trying to carve out your own (t: 920) indie dev workflow with SwiftUI, Claude, Cursor, all the usual suspects, feel free to like, (t: 930) subscribe, or send this to a friend who's in the trenches too. I read every comment, really. And honestly, swapping workflow tips (t: 940) is the best part of this whole thing. So yeah. Until next time, keep crafting, keep refining, and just remember, you're not building alone. Even on those days it totally feels like you are. (t: 950) Peace.

