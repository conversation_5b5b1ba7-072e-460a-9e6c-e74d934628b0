---
title: "Claude Code + Claude Sonnet 4: Budget Beast Mode (INSANE)"
artist: Income stream surfers
date: 2025-07-22
url: https://www.youtube.com/watch?v=4zm3B862-Gs
---

(t: 0) Okay guys, so without much introduction, I'm going to try and build a website using just Claude Sonnet. Now, a lot of people say that Opus is the only one that's worth using. I actually disagree. (t: 10) I think Sonnet is also very good. This is Claude code. Let's just jump straight into it. So the first thing I always do is make a new folder. So we'll call this Sonnet4Test, and then we'll cd inside Sonnet4Test, right? (t: 20) And then we write <PERSON> to start the conversation with <PERSON>. Now there are a couple of things missing here. We need to add our MCPs, etc. But before we do that, let's just add the memory. (t: 30) So what I've been doing recently is I've been exploring using a certain prompt to generate (t: 40) websites basically. So this prompt is freely available right here in a document, which will be available in the description, right? It's a two-part prompt. (t: 50) So we'll just grab this prompt here. This is the memory prompt, right? So we'll put this inside this Claude.md file. And then save it and then close it. (t: 60) And then you'll see that that closed the memory thing here. So now if we do memory, we can see that inside this project memory, we have this right here. (t: 70) That's perfect. That's what we needed. Okay. So we're going to close that to close that. There we go. Now let's do the MCPs. (t: 80) So MCPs in Claude are extremely easy. You literally just have to run one piece of code. Okay. So we're going to run it inside the Windows terminal, not inside Claude code itself. (t: 90) So I'll just run this because it has a, what's it called? API key. So I can't show it. (t: 100) Okay. So that's been added. That's the DigitalOcean MCP. And then the only other one that we need to add is Playwright. So I'll just show you guys my process of adding an MCP so that you can actually see it because (t: 110) there's no API key here. Sorry, I've got a lot of things open from last night. Okay. So chatgbt.com, right? So basically what I have is I have a list of MCP commands that have been created specifically for Windows. (t: 120) Right? So all I do is I just grab those commands every time I need them and then I'm ready to run Claude. (t: 130) Right? So I always run when I'm doing this project specifically, I always run it with dangerously skip permissions. (t: 140) Right? So we'll do that now. We'll see if the MCP servers are working. We'll be able to tell instantly. Okay. So one of them is not. DigitalOcean. Failed. Um, okay. (t: 150) So today's video, we will just be using Playwright. There's something wrong with the DigitalOcean MCP right now. I'm not sure. I won't be launching the website today. If you're interested in launching websites, watch the video from yesterday instead. (t: 160) So the first thing we have to do right now is we have to change to Sonnet. We're already on Sonnet. I didn't even realize that. And the memory is already set. (t: 170) If we go back to the prompt here, we're going to grab prompt number one. We'll probably just use this example. Again, just because it's nice and easy, but I'll just show you how to set this up. (t: 180) So I've done quite a clever thing here where all you have to do is fill in these XML tags, right? And it will generate you whatever the hell you want in terms of a service website. (t: 190) It's focused on service websites. You could easily change the memory prompt to not be about services, but whatever. So let's say you're making an HVAC website for someone in Austin, Texas. (t: 200) This is a pretty standard job. You can normally charge about two grand for a web design job. So you can charge about two grand for a web design job. So you can charge about two grand for a web design job. So you can charge about two grand for a web design job. Like this. (t: 210) And I'm going to show you how ridiculous it is that you can charge two grand for something that will take you basically no time at all. So we just need to activate dangerously allow browsing so that we don't have to sorry, dangerously allow dangerously allow dangerously skip permissions. (t: 220) This basically means that we can run it without without babysitting it. (t: 230) Right. So there we go. And then all we need to do is change these XML tags. And then all we need to do is change these XML tags. We're going to keep them the same just because it's simple. (t: 240) So HVAC in Austin, Texas, all HVAC services you can think of, plus emergency ones, English and Spanish, obviously the two prevalent languages in the United States and especially in somewhere like Texas because it's so close to the border. (t: 250) I think anyway, I don't know if that's true, but all large towns and cities with a hundred mile radius of Austin, Texas are the locations. (t: 260) So instead of like individually listing locations, which takes time and basically giving the AI the freedom to just make something for themselves. So instead of like individually listing locations, which takes time and basically giving the AI the freedom to just make something for themselves. Right. One quick word on this guy's after I. (t: 270) So just use placeholders for contact details only for now. Or if you already have the client's contact details, obviously put them here. Now, one quick thing to mention is we're going to be using Docker, right? (t: 280) That's in the prompt. So you do need Docker desktop installed and you need it set up properly. I advise you to just say to code, install Docker and make it so you can use Docker commands yourself. (t: 290) Right. One quick thing to mention is that you can use Docker commands. So when you do that, you can use Docker commands. inside docker on settings on docker engine sorry on resources WSL integration (t: 300) this has to be turned on and this has to be ticked if you're using WSL right you can use Windows now but I still use WSL sometimes just because it it depends (t: 310) what I'm doing right if I'm using docker I used to be yourself normally you also need github CLI right this should be set up anyway again you can just say set up (t: 320) github so you can push and pull from my repos make it so there's an OAuth pop-up (t: 330) where I can add my various accounts right and then just let it run let it set itself up so I'm on WSL right eventually it will be able to set it up (t: 340) by the way you can double tap the space you can double tap space escape to remove the text I just learned that and then the (t: 350) final thing I want to do is I want to do a little bit of a test here so I'm going to hit on window update and that'll give me a little bit of a chance to solve associated side issues today I still need some Bluetooth so I'm going to double tap the space I can't see ma face in here no problem yeah okay so you (t: 360) might be interested to see what happens once we skip hang out each of the features that's on the screen right now I shocked myself a little bit you know there are drops and there are effects per answer and when (t: 370) I'm writing I'm in drag mode there's these commands it's trying their best to block click within words totally fine now I'm! (t: 380) work was actually do so as you can see here it says use this command to make the project this is for Windows so hopefully it will it will kind of (t: 390) discover that I'm on WSL fairly quickly okay there we go perfect so it managed to create the project without me saying yes or no okay so there is a way to one (t: 400) shot one what's it called execution you can one command an XJS website this is what I was working on yesterday so I discovered that right so that means you (t: 410) can create an XJS website without having to create it yourself right it's not a huge thing but so this is the prompt this is in its memory right so what I do (t: 420) is I put memory stuff is the behavior and the workflow and then the prompt is just like the the info right I'm gonna let this run in the background just so (t: 430) you know just to prove I'm not doing anything it's running itself blah blah blah okay so you are a service website generator the time now is 1342 just remember that you are a service website (t: 440) generator that uses docker to fix problems until the website runs fully without any issues you can also use the playwright MCP to read browser logs if an error isn't occurring client-side but browser side you make an individual new (t: 450) folder per project I will give you the services languages etc you just need to create this site with a docker instance to test and then when I tell you to send (t: 460) the code to github blah blah the project must build locally bonus points if you can tell me why that is you must successfully be able to run npm run (t: 470) build and it builds creates all the pages etc this is essential also add a sitemap for the pages to get images follow the following workflow so we're (t: 480) actually using Gina on unsplash for it to automate the process of finding images right so it's doing all of the research for you look comprehensive list (t: 490) of HVAC services cities and towns within a hundred mile radius of Austin's HVAC services cities and towns within a hundred mile radius of Austin's HVAC services cities and towns within a hundred mile radius of Austin's Texas right how cool is that I really really like that it will find images for (t: 500) you using Gina it uses a series of things I called Gina searches and Gina (t: 510) scrapes this thing is unstoppable these eight lines of code allow you to scrape the entire internet for free right it's pretty mad to be honest with you (t: 520) however I do want to mention quickly that if you're doing a lot of scraping and you're worried about getting banned from the internet like I'm sure you're doing I am very very worried of getting banned from the internet my IP or (t: 530) whatever I've probably done serious damage to my IP just because of the amount of scraping I've done definitely check out bright data it's a little bit more complicated to use but it's definitely worth it because it protects (t: 540) your IP its proxy and it does the same thing as Gina now the bright data MCP is amazing and it would be very very relevant to this project specifically so (t: 550) you would just add the bright data MCP instead of using the Gina search engine or the G-N-A stuff that I use I use Gina because like I've already fucked my IP (t: 560) I'm already in trouble with whoever you get in trouble with for doing loads of scraping so there's nothing I can do at this point I probably scraped over a (t: 570) million pages just myself just from making videos and Harbor and blah blah blah so I tend to use Gina just because it's a lot easier to use but if you are (t: 580) worried about security definitely check out bright data you can just add it at the MCP at the start of the project and then you can just go and do your own project and it replaces Gina fairly easily do an insight search on unsplash (t: 590) replace the queue with a keyword that makes sense what you're looking for if you don't know what that means basically an s.gina search is a search of Google (t: 600) and you can replace Gina AI with whatever the fuck you want like for example if you're making an HVAC website you can search for air conditioning (t: 610) units or whatever right create a new next-gen project call or something that makes sense with website niche add some random numbers at the end and you can do that in a different way so you can create a new website and then (t: 620) you can do that in a different way so that's the reason why I'm doing this right by the way the reason it's kind of that this kind of weird prompting of make a new folder every time it's so that you can come here right you can do (t: 630) WSL that's the Ubuntu again I can't remember what this is called this folder sonnet for test right CD sonnet for test and then you can write Claude and you (t: 640) can do this again you can make another website right so all you do is and like you can make 10 of these websites an hour with this method right so what you (t: 650) do is you change HVAC right let's say you this time you're doing I don't know plumber in New York right and then just change literally just New York New York (t: 660) City let's do all the all regions or what they call the the name of the hoods I don't think that's right but and (t: 670) cities within a hundred mile radius in New York City just use placeholders right (t: 680) and then you just run this again and now we're doing two websites at the same time this is the really fucking crazy thing about this right so I'm gonna run (t: 690) this at the same time I'm probably not gonna show you guys the end of this one but I just wanted to show you that the reason the prompting is that way is so that it can do more than one at the same time right so let's open a Visual Studio (t: 700) code look at the time here five minutes has passed let's open up sonnet test and where is it sonnet for test remember I'm using sonnet for this as well which is (t: 710) the really crazy thing where the hell is sonnet for so H and so for testing so we (t: 720) should be able to see inside this folder here there should be a new folder coming very very soon right I forgot to put dangerously allow dangerously skip permissions on I completely forgot but that's fine so look now I'll make a new (t: 730) next.js app there we go HVAC services in New York City we are now making two websites at the same time so let's do that and then we're going to do this and I'm going to show you guys how to do this and I'm going to show you guys this website at the same time and you can do this like an unlimited amount of times I (t: 740) don't think you would get limited by anthropic if you're using sonnet right obviously if you're using Opus it's a different matter but yeah with sonnet I'm (t: 750) pretty sure with the $200 plan you could just do this all day so let's know we don't a CD I want to show you guys what this looks like so I get to public here (t: 760) you'll see these are images from unsplash they're extremely specific to HVAC right there we go bang bang bang I mean that's just beautiful it's a thing of beauty (t: 770) and now has everything it needs logically speaking to create an entire website right and the same will very soon be true for this other website here (t: 780) the this is also HVAC did I do HVAC twice at a plumbing and HVAC (t: 790) oh I said all HVAC services there okay that's my bad that's that's my mistake but that's okay you guys get the point so now the last question becomes can sclodsonnet4 handle this as well as (t: 800) opus because if it does then I honestly you could probably use the $20 plan for this let's find out let's let this run and we'll be back when it's finished (t: 810) just so you know guys this is why I dangerously skip permissions because otherwise you have to literally sit here and press enter repeatedly like this (t: 820) right it's so annoying so much you're gonna quit out and I'm gonna run gonna run dangerously skip permissions then dash see at the end so if you don't know what to do then just go to their website and then just hit enter and it will say no. end and what this does is it uh yeah wait i need to i need to do dash r and i need to select the (t: 830) correct conversation because otherwise it jumps into the other conversation there we go so now we're bypassing permissions i'd have to press enter every five seconds okay so it can't run (t: 840) playwright right now because i'm wsl and wsl sucks for playwright i totally forgot about that (t: 850) next time i do this i'll do it purely with windows just because it works better let's have a look at what it generated in uh i can't remember what time it was when we started it was about 15 minutes i (t: 860) believe right let's have a look so we have this entire website that's been created fully seo'd if you look at the top austin hvac services professional heating and cooling we have all (t: 870) of these different okay this one 404 that's fine it happens right it looks like yeah i'm not sure (t: 880) why but these guys 404'd this would normally have been fixed by playwright but i just stopped it because i haven't got a lot of time right now um i'm getting called downstairs to (t: 890) eat some beautiful food and i can't really resist it because it's pasta and lentils if you don't know about pasta and lentils guys you need to get yourselves in uh in italy as soon as possible (t: 900) okay so let's see the spanish works as well looks like it does slash es and then also i believe this should be fixed by now no it's still not okay so we have an example of a service page right here (t: 910) again it looks great again it's full of content so let's see what we can do here let's go to the website and we can check it out here and dream as just to add a little bit of a (t: 930) playwright we did there and i would say that this is alang ole in am hunting for work and then there's the website section here즉 the main website that supports playingwright all right guys (t: 940) i think you know by this website if you have 각нож out good idea because it's released so coming down the page we can click add service share and then again look at the third on the glolad leave it right and just follow the exact prompting structure here this will work i promise you um (t: 950) it's just yeah i had to i had to dip out early i made several videos on this yesterday i did several streams yesterday oh actually i can just show this one maybe that would work better true (t: 960) i have two so let's see i completely forgot that i have another one okay so this is why you do two (t: 970) when you're making a video just in case one gets messed up because you're impatient so let's open this up let's see if i'm right so this should just work there should be absolutely no issue (t: 980) with running npm run dev i'm pretty confident in this but okay wait uh i need to run npm install first obviously so npm install should be fairly quick okay so i ran npn run dev again i didn't do (t: 990) anything you guys saw this entire video i didn't even touch this website i forgot it existed (t: 1000) right It does seem like it hasn't actually finished everything here. Oh, no. Maybe it has. Okay, let's find out. (t: 1010) Oh, we couldn't use Playwright, obviously. So there might be some things fucked up here. I forgot that Playwright was messed up. But there we go. We have this website that was completely built without me even looking at it. Let's see if I click here. (t: 1020) Okay, this is also 404ing. So this is most likely because Playwright wasn't working. I completely forgot that Playwright is a bit shit on... (t: 1030) What's it called? On WSL. But it's made most of the website. There's just a few things missing. The Spanish is working. You can see here this is... (t: 1040) Wait, let's go... So it's only... Oh, no, the services do work. Services work. It's just the locations don't work, which is fine. That'd be quite an easy fix, I would guess. (t: 1050) But the service pages were all made. You can see, there we go. Bang, bang. Really nice. So this is Heat Pump Services, New York HVAC Services. (t: 1060) And then if you click this one here, this would be a... Manhattan-specific one. However, for some reason, the locations aren't working right now. I will say one thing. Sonnet is not as complete as Opus. (t: 1070) If you do this with Opus, it will do absolutely everything. Sonnet does a good job. It does a really, really good job. It does a much better job than Klein with any other model, etc., etc. (t: 1080) But it does not beat Opus. I'll leave the video there, guys. I think this is a pretty good experiment. You can definitely get away with using Sonnet for this kind of website. (t: 1090) I'll look more into... What's it called? WordPress again tomorrow. But just so you know, you can literally say to Claude Coates, (t: 1100) set me up a WordPress instance to develop inside, and it will create this for you. This is running on Docker, and I can literally, you know, (t: 1110) I can go to slash admin or not. Obviously, it's slash WordPress admin. How did I forget that? And then we log in here, and we effectively have a local WordPress development server (t: 1120) on Docker, right? This is running on Docker. It's not running on local WordPress. So if you want videos on that, let me know. Thank you so much for watching. If you're watching all the way to the end of the video, (t: 1130) you're an absolute legend, and I'll see you very, very soon with some more content. Peace out.

