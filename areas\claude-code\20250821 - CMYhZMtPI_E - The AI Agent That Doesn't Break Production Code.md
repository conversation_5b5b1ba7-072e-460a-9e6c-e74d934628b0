---
title: "The AI Agent That Doesn't Break Production Code"
artist: <PERSON>
date: 2025-08-21
url: https://www.youtube.com/watch?v=CMYhZMtPI_E
---

(t: 0) Everybody wants their AI to generate faultless production-ready code, but the reality is that code breaks weeks later and nobody knows anymore why the AI agent made certain decisions to create features in a specific way. (t: 10) In this video, I'm going to solve that problem for you by showing you how you can create a self-documenting AI agent that won't just create features, (t: 20) but also explains the decisions it made around how to actually develop the features themselves so that later on you can fix any issues that will arise in production. To prove that this works, (t: 30) I'm going to be adding a new feature to my production-grade application. In this video, we're not going to be working on some dummy to-do list project. No, we're going to be working on a real code repository. So let's get into it. (t: 40) We're going to be adding a caching feature to this AI Engineering Tutor app, which is an application that you can use to learn AI Engineering faster. You might have a question like the following. (t: 50) What is the best first step to get started when I want to learn AI Engineering? Now, it takes some time to generate the answer because first, the system has to find relevant YouTube videos to use to generate the answer. So let's get started. And then, of course, it also has to invoke an AI model (t: 60) to generate the answer itself. Sometimes it's fine that this answer takes a long time to generate, but when users have very similar questions like how to get started, (t: 70) what the best programming languages are for AI Engineering, questions like that, you really want to just cache those answers, right? Because the questions are all pretty similar to each other, and that way the users do not have to wait for the AI model to respond. (t: 80) Because I've been talking for 20 seconds now, and only now the answer is fully done and generated. And you can see that there are some examples for technologies that you can use, (t: 90) like FastAPI and a couple of YouTube video sources. Where do we actually get started with this caching feature? Well, I've actually already prepared a PRD, a product requirement document for this video. (t: 100) If we check out my GitHub, you can actually see that I've got this issue here, semantic FAQ caching. And I actually have described how I roughly want this to work. (t: 110) Basically, what I want to do is I want to match similar questions. And if a question is a match with an answer that has been cached, then I want to actually return that in, you know, (t: 120) 100 milliseconds instead of two to three seconds, or even longer if you count all the processing time from the language model. So how am I going to achieve that? Well, there is actually a little bit of a technical implementation in this PRD as well. (t: 130) If we scroll down here, you can see that eventually, it's actually going to note that we want to do the basic semantic caching with Redis. (t: 140) And Redis actually allows for vector search as well nowadays, which is how I can actually make sure that the questions are relevant to me. And to achieve that, what I'm actually going to be doing (t: 150) is also letting the AI model know that it can actually check out this webpage for more information about how vector search is actually implemented in Redis. And this is a little bit of context engineering, right? (t: 160) You want to make sure that you don't just instruct the AI model on what to do, but that it has the right documentation to also read and understand how to do the implementation at a technical level. (t: 170) So with that being said, I'm going to open Cloud Code and get it to implement this feature. While it does that, I'm going to show you how I'm going to make it to make the agent self-document its progress. (t: 180) So we're going to go ahead and check out Visual Studio Code. And now what I'm going to do is I'm simply going to type fetch issue one and implement it carefully. (t: 190) The reason I can just refer to issue one is because I've connected Claw to my GitHub MCP server. So it's actually able to read information from a repository, including all of the issues. (t: 200) So you can indeed see that it's now trying to get the issue details. And I'm just going to go ahead and proceed with that. And now you will actually see that it retrieves all (t: 210) of the issue details. And you will see that it now starts to create a to-do list already. And all of that is going to be based on the issue description, which is basically our product (t: 220) requirement document. So indeed, you now see that it first starts to analyze the system code base, set up Redis, et cetera. But the actual implementation of this feature is not really what you came here for, right? (t: 230) It's just a means to show that this agent is going to self-document how it approached the implementation. So how do I actually make this agent self-document? Well, for that, I actually set up a Clawed command. (t: 240) Now, if you don't use Clawed code, there's no need to worry. In the end, this is just a collection of prompts that work together. So you can even use this in other AI agents, (t: 250) like GitHub Copilot, for example. What we are going to be doing in this auto-document agent command is that it's going to run git diff main to understand what the agent has actually implemented compared (t: 260) to the current production branch. This is a great way. It's a great way to actually make sure that all of the context is provided to the agent (t: 270) instead of manually asking it to just review the changes. By running a terminal command like this, you will ensure that the full context is pushed into the AI model. From there, what it needs to do is read the complete diff output (t: 280) and then create a to-do list of functions that it might want to investigate further. And next to that, it needs to actually decide (t: 290) whether it should create an architecture decision record. And this is where the auto-documentation piece comes in. So the clicker, the magnifier, and the (t: 310) handout were born. And while you are at it, why don't you use it (t: 320) to Arbeit and all the other stuff that works for you normally. Let's go back here. Here comes your application. description below and you can customize it to your needs, right? Maybe your team already has an ADR template that suits your needs more. In my case, you know, there's just a decision that's made, (t: 330) technical approach, key components, etc. Now, in my case, I actually really like creating this ADR in the same pull request in which I implement the feature itself. Some people might want to write (t: 340) the architectural decision record before they even get started on the code. But generally, I find that decisions made in an ADR do slightly change once you actually get to implementing the code itself. (t: 350) So I prefer that the AI code agent will write the ADR after the code has been implemented, but before (t: 360) it's actually been approved and pushed into production. And that is the key, right? If you have the ADR together with the code, then three weeks later, if something might have broken due to the AI code, someone can actually easily understand why decisions were made and fix up (t: 370) any problems. And while it's working on this feature, I just wanted to let you know that these kinds of real AI coding practices that generally work in the real world and aren't just hype are (t: 380) things that I teach in my course. So if you're interested in learning more about AI coding, you can check out my AI engineering community, together with all of the access to applications like the AI Tutor that allow you to learn AI engineering concepts much faster. So if you want (t: 390) to check that out, you should definitely go to the description after this video. But for now, let's wait until Claude is done and then see how far along we are with the Redis implementation. (t: 400) So we can actually see that Claude is starting to wrap up now. And if we check out all of the changed files, you can see that it's added quite a lot. So it's actually updated some (t: 410) readme documentation already. Now that's not the same as writing an ADR, but it's actually updated some ADR, but you do see that Claude is documenting some basics here already, like adding Redis as a potential storage mechanism. Then of course, we also have a couple of changes to our environment (t: 420) variables where now the Redis configuration is actually added in our example environments file. So I'm going to go ahead and add that to my real environments file in just a bit. Next to that, (t: 430) we also have changes in different Python files. And I think a very important part to check out is going to be the chat service here, because that's actually where we're going to call (t: 440) the Redis cache. So for example, it's going to be the chat service here, and I'm going to call it the Redis cache. And then if we keep scrolling down, we can actually see that a new step has been (t: 450) added. So we have got a couple of steps to handle a user request. We enhanced the query, we generated an embedding for the query, but then this new step three is actually that we first check the semantic cache for similar questions. This step was different before because before we just immediately (t: 460) retrieved the relevant YouTube videos using vector search. So now we first actually check the semantic cache to see if a similar question has been answered. So you can see in here, (t: 470) that it will actually yield a couple of things from the cached answer. It will yield the answer itself. And then of course, the metadata as well, because we do actually ideally want to display the (t: 480) sources in the front end UI. Now, one particular thing here that I'm seeing is that the sources themselves are actually not being returned at the moment. So that is something I would definitely (t: 490) want to implement in a second iteration. But for now, let's focus on making sure that the answer itself is returned very quickly. And then you can actually see that it will return and exit early (t: 500) if there is a cache hit. Now, of course, there is more code to explore, but the point of this video is more to show the auto documentation process. So what I'm going to do is I'm going to start up Redis locally and then (t: 510) check out whether or not this new feature even works. And afterwards, we're going to automatically generate all of the ADR documentation that we need to make sure that this code will actually work and (t: 520) stay functional in production. So in the readme, I can see that if I scroll down, I actually have to start up Redis locally with Docker Compose. Probably want to do that automatically later on, (t: 530) but for now I'm okay with doing that manually. Then you will actually see that it will need to download and pull the Redis container. And while that's being done, I'm going to be editing my environment variables to add these values. (t: 540) I'll be right back because I don't want you to see my secrets. So I've started Redis and I've edited my environment variables file. Now what I'm going to (t: 550) do is I'm going to make sure that the backend is actually fully restarted just to make sure that everything will work as intended. Oh, and of course, I actually have not installed a new (t: 560) Redis dependency. So I'm going to go ahead and do that as well. If I check out LS, you can see that in this file, I have a requirements file. So I can simply do pip install-rrequirements.txt. (t: 570) And this way the new Redis package will be installed because we of course need that to communicate with a Redis instance, right? So now I'm going to go ahead and run the server file again. Now, initially, actually Redis didn't want to start. You can actually see that it failed to (t: 580) connect here locally. And that's because this concurrent write mode parameter doesn't even really seem to be a valid parameter. And I noticed because if I check Docker desktop, (t: 590) you can see that this is not a valid option. So it seems like the AI model has just hallucinated that. And this is proof that everyone showing you that these AI agents can code up a real application in five seconds. They're probably lying to you. It's much (t: 600) more complex than that. And you do need to actually check what the AI model is doing. So because I know what I'm doing, I can actually fix this issue. I can go ahead and remove this. (t: 610) And then what I'm going to do now is I'm actually just going to restart the backend, but only after I restart the container. And now what I can do is I can go back and just restart the Redis container. (t: 620) And then let's just go ahead and restart the Redis container. And then let's just go ahead and check if it's probably running. And now you can actually see that it's properly running. So (t: 630) that's fine. And now you can see that the cache has been initialized, but we still have to see whether this actually works. It seems to have created the vector search index. So let's go ahead and go back to our application and ask a question. And then I'm going to go ahead and ask (t: 640) the same question again. Now I'm going to open up my network tab here on the right, just so you can see if the network request actually becomes faster because of the caching. So I'm going to go ahead (t: 650) and first submit this regular request. And then I'm going to go ahead and go ahead and go ahead and request. Of course, we don't have anything in the cache yet. So this is definitely going to take a while to complete. And you can indeed see that this took over 17 seconds to fully resolve. (t: 660) Now let's go ahead and actually refresh this page and ask the exact same question again. Bang. Whoa, it's actually already done. I don't have to show you the network request for you (t: 670) to understand that that was much faster, right? Now, again, if we scroll down, the sources are actually missing here. That's something I want to implement for a second version, but the actual caching mechanism seems to work pretty well. I do want to test one more (t: 680) case though, because I literally asked the exact same question. I do want it to actually still trigger the cache mechanism when the question is very similar, but maybe worded slightly differently. (t: 690) For example, if I fully refresh the page now, I can slightly change the language here and say something like, what is the most important first step to get started when I want to learn AI (t: 700) engineering? And now you see that it still matches. Now just as one more case, let's ask a completely different question and ensure that it's not going to return the cached answer, but actually start to (t: 710) thinking again. So for example, I can ask what is the most important principle when AI coding? (t: 720) And now you will actually see that it's starting to think about this answer for a longer time. And it actually starts to stream in the answer because it's not been cached yet. So now we've kind of tested the caching mechanism end to end, but of course you're here for the (t: 730) auto-documenting agent. So now that we've properly implemented an initial version of a feature, let's see how ClockCode will create an ADR based on the command that I shared earlier in the video. (t: 740) All right, we're back in Visual Studio Code. And what I'm going to do now is simply press slash and then actually use this auto-document agent command, which I have to find once again (t: 750) in this Markdown file. So all I have to do is press enter. And now it understands that it first needs to run a git command to understand all the different changes that it's made. (t: 760) And you can indeed see that there are over 400 lines of git differences between this branch and the latest code that's actually running in production. And now it has a good idea, (t: 770) of the whole Redis implementation. You can indeed see here that it is actually going to read some extra files to get an even better idea of what's going on. It's even going to read one of (t: 780) the test scripts to understand what some common use cases for this caching mechanism might be. And then at the very end, it's going to analyze if it's a significant architectural change (t: 790) that even requires an ADR because small changes like some front-end changes don't really need an ADR, right? They can introduce bugs, but it's not going to completely break your system most of the (t: 800) time when you have small little front-end bugs. But in this case, we're adding a caching layer, a really intrusive backend operation that could completely break my application. So in this case, (t: 810) the AI model should indicate that an ADR is necessary and then continue from there. I'll sit back and relax for a second and I'll wait that the cloud has made a decision on whether it (t: 820) should write an ADR. So now you can see that indeed, this is a significant architectural change. And now it's actually added one extra to do. It's going to create the (t: 830) architecture decision record for semantic caching, and it's going to create a new ADR docs folder because I haven't created that in this repo yet. Totally fine with that. Let's go ahead (t: 840) and proceed. And it will now actually go ahead and read the architecture decision record templates and start creating the record. And you can now see that it's created an ADR. I'm going to allow it (t: 850) to create the decision record. And now you will be able to see in the docs folder that we have the semantic caching markdown file. Now let's see if it actually matches the ADR template in terms (t: 860) of structure. We indeed start with the status. We have context, decision, and we've got the technical approach, key components, consequences, positive, negative. We also have a couple of (t: 870) alternatives. So in this case, it actually comes up with a couple of alternatives that makes sense in the context of the repo. You could do simple string-based caching. The difficulty is, (t: 880) as I said before, you're not going to match questions that are asked in a slightly different way. You could also actually do some caching in Cosmos DB, which is the database that I use for (t: 890) other operations in the application. It's got a higher latency than Redis. It's more expensive for frequent caching reads. And you know, Cosmos DB is not really meant to be a cache. That is really the job of (t: 900) a system like Redis. So here too, you understand better what the alternatives could have been for the implementation and why Redis was still chosen by the AI model in the end. (t: 910) Now, of course, in my case, the PRD already pushed the AI model a little bit into the Redis side of things. But in case your PRD doesn't contain any technical details, this is especially (t: 920) handy because you get a bit of a better idea of the alternatives and why the AI model might have made a specific decision on actually using something like Redis in the end. Now, if you (t: 930) scroll all the way to the top, you will see that the status is just accepted right now. And that is because as an engineer, I'm going to be pushing this ADR and creating a pull request out of all (t: 940) of the code here. Now, of course, based on your repository and your workflow, you can actually set this status super post or anything else that works for you. It's all customizable because it's just (t: 950) a simple prompt that you can change. And of course, if you're using a repository, you can also set this status super post or anything else that works for you. It's all customizable because it's just a simple prompt that you can change. In my case, I'm actually quite happy with this. So I'm going to go ahead and ask Claude to commit all of my files. The great thing is, again, because I've got that GitHub MCP integration, I can ask (t: 960) it to create commits and then even create a pull request out of everything. So that's what I'm going to do is just wrap up this whole concept and system nicely. So what I'm going to do is I'm (t: 970) going to say, please commit all changes and create a PR. You can now see that it's starting to use Git commands to add all of the files that have been changed for this Redis implementation. (t: 980) And then it's going to go ahead and push that in just a little while. Now we're actually going to push those changes to my branch. And now we're even going to be creating (t: 990) a pull request, which is very cool that I can do this automatically with my AI agent because of that GitHub MCP server, right? Interestingly enough, I actually decided to use the GitHub (t: 1000) CLI because I've also got that installed. But just to make sure it actually pushes to the right repo, I am going to ask it to use the MCP server instead. Please use the MCP server instead. (t: 1010) It's interesting. You can see here that these AI agents have different ways to reach the same destination, right? So in my case, I just want to use that MCP server. And now you can indeed see (t: 1020) that it's going to use that GitHub open new pull request tool call. So let's go ahead and actually just create the pull request. And then I'm going to go ahead to my browser and show you that the (t: 1030) pull request was created pretty much right now. I'm going to go and scroll up, go to pull requests, and you will see that we actually have this new feature, add semantic FAQ caching with Redis. (t: 1040) And we've got those three different commits with all the changed files. And because we have this very nice architecture decision record, if an engineer reads this code four weeks in because (t: 1050) there's a small bug with this AI code, then that's not a problem at all. It can easily pick up and fix whatever is needed. I'm confident that you now see that you need to do way more than just (t: 1060) vibe code your way to production when you're using AI to generate code. If you want to become a real AI native engineer, you should subscribe to the channel and check out my AI native engineering (t: 1070) community. And I'll see you in the next video. Bye. In the link in the description below. I hope to see you there.

