---
title: New DeepSeek Model INSIDE Claude Code is INSANE
artist: Income stream surfers
date: 2025-08-26
url: https://www.youtube.com/watch?v=2S8ILMsMKns
---

(t: 0) Did you know that you can use DeepSeq inside Claude Code and it takes less than 30 seconds to set up? In this video I'm going to show you exactly how to do it and I'm going to (t: 10) show you exactly what the results are as well. So the first thing you want to do is you want to come to this page here. You can also just search online DeepSeq Claude Code and then (t: 20) look for the official Anthropic API here. This is on DeepSeq's docs and you just want to Ctrl A or if you want to copy all the sidebar stuff just Shift click at the very bottom here. (t: 30) And you want to put this into a new Claude Code instance. So mkdir DeepSeq Claude, cd DeepSeq (t: 40) Claude. Then I'm going to write <PERSON> and then I'm going to say help me set up DeepSeq. (t: 50) Okay so you can see the model is at the moment it says model opus. There's no nothing. Here either so I'm going to say please help me set up DeepSeq for Claude Code and then I'm going (t: 60) to paste this here and I'm just going to grab my DeepSeq API key as well and I'll just say this (t: 70) is my API key and paste it. So what this will do is it will give you all of the commands that you need in order to set the server for your particular system. Now obviously this knows that I'm on Mac (t: 80) so it's going to run those commands whether it works or not or whether I have to restart my server or whatever. So we'll add a command to this. We'll say mkdir DeepSeq, etc, etc, etc. (t: 90) We'll see in just a second. So let's just actually do mkdir. We'll call this nextjs DeepSeq, cd nextjs DeepSeq. (t: 100) Now it should give me a command to run. Okay so if you look here this is on the other conversation, (t: 110) right? If I do Claude here, you'll see whether you slash model DeepSeq chat has been selected. up people think it's more difficult people didn't even know that you can use other models inside (t: 120) um cloud code and then yeah this is basically the update now apparently um the real mode wasn't (t: 130) available until either yesterday or something now i'm not exactly sure about about that but i was watching a video from from someone before basically they were saying that yeah basically this is the (t: 140) best positions ever been in now what i'm curious to see is whether or not this can actually do um you know something useful basically so i'll just hop on over to my school community quickly (t: 150) and we'll grab my prompts for um for to to test this basically um (t: 160) so inside all my prompts and workflows prompt to create a service-based app there we go first things first we need to create the next js project (t: 170) so i'll just say yes to all of that and then next js we'll just open this up and then here we'll open up rolls royce (t: 180) uh sure this one we just need the images from this folder to be put into that folder so we'll (t: 190) just grab this public and put it here there we go okay so we should be good to go here let's just grab the prompt now this is the first time i'm using a different (t: 200) uh yeah different model basically let's just double check the model here (t: 210) deep seek there we go there's the prompt we smash enter and then we just let it go and we see what happens let's see if it can actually build this properly let's see if deepseek's actually a good (t: 220) model it's just my previous test was with for example i think it was klein and it didn't go that well so let's see how it does with claude code now actually one more thing i'm just going (t: 230) to quickly do is i'm going to do claude dash dash dangerously skip permissions and i'm going to say um let's do the c so we'll do dash r uh i think this one yeah continue (t: 240) okay so now it shouldn't add or permissions do anything it should just build okay so there's (t: 250) kind of two things that we're testing here number one is the deep seek api versus open router a lot of people told me after my video that um actually the deep seek api is much better than whatever is (t: 260) on open router so i'm going to do that and then i'm going to do the c so we'll do dash r and then open router so i thought i'd test that out and the other thing is obviously klein (t: 270) versus claude code because if you can use any model inside claude code and it does a better job with those models than klein then yeah i'm not gonna lie rip klein (t: 280) basically so we'll see how we'll see how this video goes okay so let's have a little little (t: 290) test that'll look at everything here seems to have done a fairly good job okay moment of truth running on localhost 3009 let's see okay so it looks like it did actually work uh yeah okay for (t: 300) a free llm the fact that it even works is a huge improvement every single page works as well i mean (t: 310) this is pretty good when i say free i know it's not free sorry open source so it's a very very cheap llm so if you're curious for context we spend on this website we spend 13 cents which is (t: 320) pretty damn good um it's managed to build everything let's see if the italian works that is an important thing (t: 330) that is quite difficult right it does work does it keep it does clicking through everything is in italian everything has good seo it seems let's see okay the seo hasn't been done which is part of (t: 340) the prompt so i have to give it minus marks for that but this is by far the best result i've ever (t: 350) had from anything that's not like a sota paid model so open source is getting there guys 100 open source is getting there it's not quite there yet but damn this is definitely worth (t: 360) your time i would say this one probably didn't work as a an apostrophe in it i would guess oh that one also didn't work okay so the location pages are all 404. okay so yeah i mean overall (t: 370) it's done probably as well as something like chat gbt 4o or anthropic 3.5 maybe i would say (t: 380) inside klein huge improvement cloud code with deep seek is actually pretty insane now in terms of pricing for claw code i'm not sure how it works you guys (t: 390) might have to let me know but you can probably use it just paying 18 dollars a month for clawed you could use opus for planning or if you could just use deep seekers like a backup in case you (t: 400) run out of usage or whatever it might be i'll leave the video there guys i'm not going to go on when there's no need to i think this is a pretty damn good result i'm actually pretty impressed by (t: 410) this and yeah let me know know what you guys think if you're watching all the way to the end of the video i'll see you in the next one bye bye the video you're an absolute legend as usual i hope you enjoyed and i'll see you very very soon (t: 420) peace out

