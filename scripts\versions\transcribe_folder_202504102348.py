import os
import subprocess
import whisper

VIDEO_EXTENSIONS = ['.mp4', '.mov', '.mkv', '.avi']
TRANSCRIPT_FORMAT = 'txt'  # Can be 'txt', 'srt', or 'vtt'
TARGET_FOLDER = r'D:\Scrapbook\202504070740 DSP - Players\202504070744 CogNaN_'

model = whisper.load_model("medium")  # or "base", "small", "large"

def has_transcript(video_path):
    base = os.path.splitext(video_path)[0]
    transcript_path = base + '.' + TRANSCRIPT_FORMAT
    return os.path.exists(transcript_path)

def transcribe_video(video_path):
    print(f"Transcribing: {video_path}")
    result = model.transcribe(video_path, fp16=False)
    
    output_path = os.path.splitext(video_path)[0] + f'.{TRANSCRIPT_FORMAT}'
    with open(output_path, 'w', encoding='utf-8') as f:
        if TRANSCRIPT_FORMAT == 'txt':
            f.write(result['text'])
        elif TRANSCRIPT_FORMAT == 'srt':
            segments = result['segments']
            for i, seg in enumerate(segments, 1):
                f.write(f"{i}\n")
                start = format_timestamp(seg['start'])
                end = format_timestamp(seg['end'])
                f.write(f"{start} --> {end}\n{seg['text']}\n\n")

def format_timestamp(seconds):
    hrs, rem = divmod(seconds, 3600)
    mins, secs = divmod(rem, 60)
    millis = (secs - int(secs)) * 1000
    return f"{int(hrs):02}:{int(mins):02}:{int(secs):02},{int(millis):03}"

def main():
    for file in os.listdir(TARGET_FOLDER):
        path = os.path.join(TARGET_FOLDER, file)
        if os.path.splitext(file)[1].lower() in VIDEO_EXTENSIONS:
            if not has_transcript(path):
                transcribe_video(path)

if __name__ == '__main__':
    main()
