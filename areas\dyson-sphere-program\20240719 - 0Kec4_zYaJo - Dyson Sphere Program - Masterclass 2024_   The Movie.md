---
title: "<PERSON><PERSON><PERSON>phere Program - Masterclass 2024:   The Movie"
artist: The Dutch Actuary
date: 2024-07-19
url: https://www.youtube.com/watch?v=0Kec4_zYaJo
---

(t: 0) Hey hey it's CDA and welcome to the start of my updated Masterclass series. This is meant to be a step-by-step, clean and super organized playthrough. If you are an experienced player this will likely (t: 10) give you some ideas for updating your blueprints and if you are a new player this will help you get a grasp on not just the basics of the game but on keeping your factory safe from the enemy. (t: 20) Now this is intended to be a play-along series and if you do want to play along all you need to do is start a new game and put in this cluster seed when you start up the game and you will find yourself on exactly the same planet as I am on. Now if you are a very new (t: 30) player you might be inclined to completely turn off the enemy forces at the start of your game and there's nothing wrong with that. I enjoyed the game for almost three years without combat (t: 40) so I'm sure you will too. However combat can actually work in your favor and it does add a lot to the game and to demonstrate I will be playing on a fairly high difficulty setting. (t: 50) You definitely do not need to do this to play along but by following what I do this will help you to get a sense of the game and the game itself. It will help you get ready for your own higher difficulty game whenever you feel like it. (t: 60) Okay here we are on the starting planet getting bombarded with tutorials but first things first I want to look like myself so let's look up my default coloring scheme that looks a lot better (t: 70) let's apply that and remember that you can recolor your mech in whatever way you want. Now something else that we need to do is rename the planet to Bidden There Done That who is the (t: 80) newest member at the highest tier of the channel. Thank you for supporting the channel if you guys want to you can consider becoming a member. (t: 90) Now you might be inclined to start building your first facility near the starting veins that have conveniently been placed close to you on whatever planet you start out on but that's not (t: 100) necessarily the base best place to start in the new combat update so we're going to zoom out and (t: 110) press the letter N so we have the north of the planet facing north which makes it easier to orientate yourself. Now you will find the enemy on the opposite side of the planet. (t: 120) As you can see I have three bases. But you will likely only have one if you're playing on the default settings. But what you basically want to do is move closer to the enemy. Yes I know that sounds really weird. (t: 130) But that will actually help you make sure that you know where they're coming from. Because they will always attack you in the shortest distance. But if you start on the opposite side of the planet. And you start expanding. (t: 140) That might mean that at one point they come from the north. Then if you expand a little bit south they might be coming from the south. And so on. So it makes it a little less predictable. You do still want to have an ample supply of iron, copper, stone. (t: 150) And pretty much any other materials that you can find on your planet. Like oil and coal. So actually this is a pretty good starting position if we wouldn't have combat. (t: 160) But let's see. I already scouted ahead a little bit. And over here on this side. We are a little bit closer to the enemy. Not too close. (t: 170) But we have some iron. We have some copper. We have some more copper over here. We have stone. We have ore. We will need to take the coal from a little further away. (t: 180) I think there's some coal over here as well. That's going to be a little annoying. But we have everything we need right here. So I think this is going to be a perfect place to settle. It'll just be a little walk. Before you leave. (t: 190) Don't forget to actually mine your little starting facility. You might want to mine a couple of these trees and stones as well. Just to get yourself going before you start moving. And you want to queue up the first few attacks. (t: 200) Now you need to start with electromagnetism to unlock the miners. After that. Basically. Logistics. The automatic methodology. And the basic assembling to start. (t: 210) And then after that. Go for the electromagnetic matrix. This is the default start that you always want to unlock first. Whatever you do. Now in order to make this. You're going to need some magnetic coils. (t: 220) And after that you need circuit boards. As well as gears. You can actually already make the coils from the starting materials. You get from your base. And you can also start working on some of the gears for example. (t: 230) So you might as well do that while you're moving to the starting location. Okay. So if you're playing along. Just to make 100% sure you know where we are. Press N so you're facing north. (t: 240) We are on the right side of the enemy. Just in between these two copper veins and this iron vein. And you should recognize this location if you're on the same seat as I am. (t: 250) Now what we want to do is place our first miner. That we got from researching the first technology. And you want to make sure that every miner you place. (t: 260) Ideally has at least six veins covering it. So you see the six X. Because that means that with two miners you can fill. Exactly fill one mark one belt. (t: 270) So six times. That's what you want to be aiming for with every miner that you place. Get the wind turbine up and running close to your mining machine. So it's actually powered. (t: 280) And then by pressing control and clicking on the miner. You can just empty whatever is in there. So you don't have to manually get your stuff from there. Now in the meanwhile. Let's move over to the copper vein to get some copper as well. (t: 290) And start crafting the materials. That you. Are lacking for your next few research items. So in my case that is the gears. (t: 300) I actually made the circuit boards instead. And after that you're going to need some more gears coils. And so on to just unlock whatever we have left here. Now while you're doing the research and you're. (t: 310) I'm manually mining. You might as well just make a couple of buildings. In the meanwhile as well to speed up the process a little bit. So make another minor. Make some more power units in terms of the turbines. (t: 320) As well as the power towers. And then place your second miner. Somewhere around your copper production. And again at least six veins will do. If you have more than six that's fine as well. (t: 330) Make sure that miners of course also power thing. You might as well want to connect these two up. So you can kind of stand in between. And just collect the raw resources. (t: 340) And then start queuing up whatever you need. In order to finish the last few research items. That you are lacking in order to get blue science. In the meanwhile you might as well just start mining some trees for fuel. (t: 350) Because you're going to need those logs and plant fuel. And plant fuel. And plant fuel. In order to make sure you don't run out of power yourself. There's no coal nearby. So that's a little inconvenient. (t: 360) But if you do have coal nearby. And whatever seed you are playing on. Then that's probably the better alternative. You're also going to need a couple of these logs. And plants and stuff like that. For some of the technologies at some point. (t: 370) So there's no waste in doing that. And especially picking up some of this stone early on. Is very really convenient. Because you do need some stone for some of the earlier buildings. (t: 380) Okay with these very bare necessities out of the way. It's time to start building. Our super organized starting base. And in order to do that. I'm going to place a first smelter. (t: 390) Somewhere over here I think. Now we're going to build a few more smelters. And every time we're going to take two spaces in between. And place a new smelter. (t: 400) Because there's a couple of different materials that we will want to build. Now before you start placing this on your own seed. If you're not on the exact same seed as I am. Take a note of the building space you have. (t: 410) Because you're going to need. Quite a bit of building space to the east or west direction. You always want to be building from east to west or west to east. (t: 420) Because the closer you get to the poles. The more shifts there are in the grid. And it can really mess up how your factory looks. It's not the end of the world. But generally speaking. It's a good habit to always build from east to west. (t: 430) To avoid those problems. Now in this case we are actually very close. I think to the equator. We're actually standing almost on top of it. So there's no shifts in the grid anywhere close to the equator. (t: 440) So this is a very. Very convenient place to be building for several reasons. Now the first five smelters that we're going to be building. (t: 450) Are one for copper on the top. The magnets in the middle. Another one for copper. Then we have one for iron. And then we have another one from iron at the bottom here. Now it doesn't really matter which ones you connect first. (t: 460) But I do recommend going for the iron initially. And then hook the other one up with copper. You're going to need to make some belts for that. So make sure you queue up. (t: 470) Whatever you can build. In general in the early phases of the game. You always want to be hand crafting some buildings that you can. Because otherwise your Icarus is not really doing anything anyway. So you might as well make advantage of the fact that you have. (t: 480) This whole walking machine that can produce whatever you need. Once you have the first few connections up. You might want to put in another smelter for the copper. (t: 490) As well as the iron over here. And then I recommend that you place down an assembler over here. And connect it with another one of these sorters. Directly to one of these smelters. (t: 500) And then set this. And this is what we should be able to do. That is to produce gears. That means that we now have an easy way to get some iron. We have an easy way to get some gears. (t: 510) And we have an easy way to get some copper. Which is going to cover all of the basics that we. Are normally producing by hand as you can see. So you might want to cancel these constructions. (t: 520) Get some more of these materials. And then the re-queue them. And that will make things a lot quicker and easier to get going. Also hook up the magnets. (t: 530) As well as the secondary iron. And ignore the flashing power lights initially, because the more power you are consuming, the faster the threat will build and you will get attacked. (t: 540) So it's actually not a bad thing to have low power initially, just to give yourself some time to set your base up and get ready to go. Now, once you do, and this is pretty much as much as you need early on, at least for the moment, you're going to want to research weapon systems. (t: 550) They're in the technology tree, a little bit further to the bottom, and this will allow you to build turrets as well as ammunition for those turrets. (t: 560) So make sure you research that, and then we will be getting ready to defend ourselves. With that research out of the way, again, just cancel whatever you were making and start building some turrets, just to make sure we have a way to defend ourselves. (t: 570) Now, in order to actually make sure we have ammunition for those turrets, we're going to place down once again an assembler like this. (t: 580) Just connect it up with a sorter, place a box. You should be able to create these depots now, and then hook that up as well. (t: 590) In order to make these depots, you are going to need some of that stone I was talking about earlier on. Now, this assembler is actually going to automate making the ammo. You are not going to need a huge amount of that. (t: 600) You're not going to get a huge amount, thanks to the low power. So the next thing we want to do, and hopefully you made some of those wind turbines in the meanwhile, like I showed you, we can use those to kind of power things up. (t: 610) Now, what we can actually do is, because we know the enemy will be coming over here. From the left, we can set up some wind turbines like this. Now, once you do this, this will make the power consumption shoot up, and you will get attacked pretty quickly. (t: 620) So let's make sure you have some turrets before you do that. Okay, once the first wave is incoming, make sure you grab some ammunition from the box. (t: 630) Make sure you split that up in a couple of different stacks. You might want to give yourself some ammo as well, so you can defend yourself. And let's place down these turrets, and just make sure they're in the power zone of your wind turbines. (t: 640) And use those split stacks. Make sure you put some ammo in all of your turrets. (t: 650) And make sure you stick close to your turrets when the first few waves are incoming. We should see the first units coming in very soon. We might be able to grab some more ammunition from over here. (t: 660) Hello? Yep. Good, so we can add in one more turret like that. There we go. (t: 670) As you can see, the first half of that wave was not a problem. There are some slower units coming after. So you can do that. Now this wave will be a lot smaller if you're not playing on the high difficulty that I am. (t: 680) Just stick close to your turret so you can repair them. And as you can see, even at this difficulty level, I have no problem defending myself against this first wave with four of these turrets. (t: 690) We're going to take two more waves. I'm going to skip that, because they're going to be exactly the same as you get the idea now. Once you've cleared out the waves, you should see the threat dropping down a lot lower. And it should be quite a while before you get the next one. (t: 700) Make sure you press Z and hopper up whatever drop material you have. And you can see that the turrets are coming down. You can see that there are a lot of materials that are out here that the dark fog have dropped that you killed. It's a pretty easy way to get some more advanced materials. (t: 710) For example, you might get some fuel, some energy shards from them. It's a pretty efficient fuel, so you might as well use it. As you can see, I actually got some oil apparently and some other random resources. (t: 720) So there's free stuff to get from killing the dark fog. Okay, time to skill up and make the nicest looking starting facility ever. (t: 730) That includes everything you're going to need. So I put down a box to make sure we have an easy way to kill the dark fog. And I have an easy way to collect gears in general. It's very nice to have the limit for the box set to something less than maximum. (t: 740) Maybe just the first row will do. Just to make sure that you're not gathering up thousands of units if you don't really need them to. Now in order to organize our base, we're going to need place nine of these smelters in a row. (t: 750) But we're going to press tab once. So they're spaced out a little bit with sets of three as you can see. (t: 760) Now if you press tab more often, you can have different types of layouts. As you can see, you can space them out in whatever way you want. And if you keep pressing tab, it will scale back. (t: 770) But for now, we only need nine. Now, we don't actually need nine of these to be built, but we want to have some space in between the next assembler that we're going to build after that. (t: 780) We're actually going to take out these middle three. This is just to make sure we have some building room in between. Now in order to scale things up, there's a couple of things to note. (t: 790) First of all, we actually do not want the assembler on this side of the thing. We want it over here and you can actually build the buildings yourself. If you place them down by holding the left mouse button, (t: 800) you don't need your drone to do everything. You can help them out and that's something new in the combat update. It's super super useful. Now we actually want this to produce the circuit boards (t: 810) because we have copper and the iron right over here. We need to hook this up to a belt like so. And in order to make sure we actually have some copper incoming, (t: 820) we're going to add in another mining machine, at least start with one. You're going to want to add a second one to this because these six smelters as well as these six smelters can actually handle the total output of two miners. (t: 830) So you want to make sure you have two iron miners connected to this belt over here and then two copper miners to the incoming belt over here. (t: 840) And just make sure you leave yourself some room when you're making these belts. Add in a little box to make sure you have somewhere to collect your circuit boards and make sure that your sorters are actually facing the correct way (t: 850) because that is not the way to do it. That's order supposed to be moving. Yep. Now it actually works. And then the next thing we want to do is set up a very similar setup (t: 860) for the magnetic coils that we need the magnets as well as copper for. And look, look what we have here. (t: 870) We have the actual magnets. Now you might want to first build the belt before you start dragging these out because that way you can already make the sorters included once you start dragging them out, (t: 880) but they won't actually connect until you have a belt. In place. So if you do it like this, let me show you. We also need a sorter. Of course, actually need sorters in general because I'm out of sorters. (t: 890) So let's queue this up again. Just make sure that you're always producing something and then we are going to do that like this (t: 900) and then we can just drag them out press space. Sorry tap and then like this we can have this all up and running really easily. So this is starting to look like something and make sure that at this point you start. (t: 910) Filling in some of the power as well. Keep it to the south of your base because right now the enemy is likely to the north. (t: 920) So placing these down to the south will make sure that they are still attacking us kind of in this diagonal. Hopefully you might want to add some additional turrets at some point. But for now, as you can see, we still have a lot of threat to go. (t: 930) So no reason to stress about that too much. What we're actually going to do is expand some of these assembly machines because as you can see, we have full belts. (t: 940) So we're not actually making use of all the materials that are producing. So what I want to do. Is actually built. Let's see two more of these like this and two more of these like that. (t: 950) Now this is actually going to be circuit boards and that means we will also need to expand these belts. Now remember that both of these are actually using the copper. So that's super efficient. (t: 960) We can expand the belt like this and we can expand the belt like this. Now unlike what I just showed you you can actually just opt to make the belts like this and then just add them in. (t: 970) It's all super fast. We're not making. Things at the huge scale so far. So you don't have to worry too much about being extremely efficient in the way you build everything. (t: 980) You can always just copy paste it like I'm doing in the right now. Now, this will actually be the main ingredients that we need to automate our blue science because that's going to use both the magnetic rings (t: 990) and the circuit boards. So we're going to set up a little science facility over here. Something like this. You're going to need to build some matrix labs for that. (t: 1000) And in order to do that, you're going to need some glass. So mine some additional stone. If you ran out by now, there should be plenty of that around. So that shouldn't be a problem. We're going to automate that soon. (t: 1010) Don't worry. But for now, we're going to focus on getting our science going now in order to do that. We are going to set these up. We're going to connect these to the belts over here that will have the outgoing materials because right now we're putting some stuff in the boxes over here. (t: 1020) But what we're actually going to do is make sure we keep those up. (t: 1030) I actually reloaded. So apparently I didn't have my sword. There's in here anymore. But by doing it something like this, actually, let's make this a little bit more neat and put it on the belt like this. (t: 1040) We also need the force to connect the iron like that. Copy copy copy copy. And as you can see, we have a lot of resources already coming into the blue science facilities over here. (t: 1050) And this will start producing all that for us. However, we also need some matrix labs in order to process this. (t: 1060) Ta-da! And we have automated. Research over here. Now, of course, we're going to improve on this. But once you have the initial setup, make sure you start researching right away because you don't want to waste any time and you might as well get your research going. (t: 1070) You have a lot of that to do. First of all, make sure you get the upgraded logistics system all the way in the top. We really need these splitters as you will see in a moment. (t: 1080) Then after that, I recommend getting some of the cheaper stuff like the engines. It's only 20 cubes as well as the electric magnetic drive. It's all things we're going to include in our little basic setup over here. (t: 1090) Then after that, I recommend going to the lab. Going to the top of getting steel and then after that getting the smelting purification. So we get access to silicon after that. (t: 1100) I recommend going for reclamation. So we can access to foundations. I promise if you're new player. This will make sense, but this is all going to tie into our starting facility in just a moment. (t: 1110) After that, you might want to get the combustible units over here. And once you have those, you can pretty much just clean up whatever research we have left in terms of only blue cubes. (t: 1120) You definitely want the battlefield analysis base as well, for example. And after that, you just queue up anything that's related to fluids, plasma extracting, chemical engineering. (t: 1130) We're going to need all of that, but there's no rush in getting it. Just make sure you are not letting your research sit still at any time. (t: 1140) As soon as you research something, just queue up something new. Remember to stack up your matrix labs like this. You can stack up to three in the beginning of the game. (t: 1150) So you might as well do that to save some space. And after that, you're probably going to be attacked pretty soon anyway. So let's refocus a little bit on our defense now in terms of defense. (t: 1160) The first few turrets are no longer going to cut it, especially not if you're playing on a higher difficulty level. And what you can actually do is put a belt or right through them like I'm showing over here. (t: 1170) So this has a advantage of letting you just click on a belt selecting your inventory and then just put it in like this. And this allows you to stack up your ammunition in all of your towers. (t: 1180) without having to manually click them one by one. Of course, we don't want to do this by hand for very long, but in case you need to, (t: 1190) this allows you to do exactly what an automated system would do. Now, instead of this, we're going to connect this build up to an ammunition production facility over here, and that will look something like this. (t: 1200) Now, in my case, I'm getting some bigger waves now, so I'm actually going to need to stick to my defense a little bit. If you lose some buildings, don't panic. It's not the end of the world. (t: 1210) Just make sure you don't get yourself killed. I actually lost a building over here, a wind turbine. Just left-click on it, and it will get rebuilt. Your drones will focus on repairing as well, (t: 1220) and as you can see, I built an entire belt over here. I expanded the copper production a little bit over here as well. Again, six of these smelters to make sure we have all the production capabilities to satisfy an entire belt, (t: 1230) and then we are going to be building, again, three assemblers making the ammunition. Now, three assemblers is actually a little bit (t: 1240) more than this setup of these six smelters can handle, but there's no such thing as having too much ammunition, so we might as well. (t: 1250) Another trick to do with your turrets is to actually build them in a square as much as possible. This is much more sturdy in terms of defense than putting them in one long line, (t: 1260) although having a long line makes it less likely for the enemy to creep around it, so it has its pros and cons, but as long as it's in the right location, (t: 1270) squaring them up is actually the optimal defense, and as you can see, I have this belt going through like this, and it's going to go through like this, and then it's kind of looping back on itself. This makes sure there's always a lot of ammunition (t: 1280) moving through the system, and the benefit of that is that if it's using the ammunition while it's shooting at the enemy, it will get replenished as soon as possible. Now, if you're playing on a lower difficulty level, (t: 1290) that's not going to be an issue, but it's a good habit to get into once you move on to a higher difficulty level, if that's something you ever want to do. (t: 1300) Yeah, other than that, let's focus on the turrets. Let's focus on the turrets. Let's focus on scaling up the rest of our factory so we have this little initial starting facility up and running as well as we can. So I'm guessing you see the pattern now, (t: 1310) but we're going to expand the gear production now, and we're going to do that in exactly the same way as we've been doing so far. Now, the gears are actually going to be on the bottom of this belt. (t: 1320) Make sure they're nicely aligned with the assemblers that you already have, and again, remember that you can help your bots build some stuff if that's what you need to do in order to speed things up. (t: 1330) Let's copy in the recipe over here, and then what we're actually going to do is we're going to use some of these new splitters that I produced. Now, the splitter by default will look like this. (t: 1340) This is actually not the splitter that we want to use. If you press the R button, you can shift the direction of the splitter, (t: 1350) but if you press the tab button, you can use different layouts. So there's actually three of them. Let me show you. You have this is the cross splitter, then we have this one that has two inputs on the side, (t: 1360) the same side on a horizontal level, and then we have another one on a different level, basically, but one is actually going straight through, (t: 1370) and the other one is on a higher level, but crossed instead of being the cross on the bottom floor. Now, the version that we need is this one, (t: 1380) and we're going to be placing those something like that, just outside of the range in line with the storage units that we already have. (t: 1390) We have the outgoing belt. We're going into the splitter like this, and then we're going to be placing a box on top of that. Now, you can place a box on whichever layout of splitter that you want. (t: 1400) So it also works with the cross, for example. Well, honestly, the reason I'm using this is twofold. I like this layout a lot better. It just looks less stupid, honestly. (t: 1410) But also, this one is a little bit more convenient, I think, in terms of actually looping things back on itself, as you will see in a moment. And this is what that looks like when you do that. (t: 1420) Okay. So, this is the back side of the box. It's actually a little bit more convenient. I'm going to put these back on since I'm actually using them for all our supply lines. Now, as you can see, I have some of these on a direct line (t: 1430) when we have a straight throughput and we are looping back. So what, for example, we're doing here with the coils and the circuit boards, as well as the gears. (t: 1440) I actually moved this box a little bit. I actually put these in line with the production facilities because I think it looks a little bit more organized. And there's a second reason for that as well, (t: 1450) because we actually need to loop and remember you can actually flip around entire belts if you want with the press of a button now. So, yeah, as you can see, you don't need to replace your belts. (t: 1460) You can just press the reverse path. Very convenient. And basically what I did is I reversed this belt as well, put them into the box and then had an outgoing belt (t: 1470) turning back on itself back to the production facilities over here for the blue science. Now, the reason I did that is because we want to use some of these things in different places as well. (t: 1480) And just having all the building material, in close proximity helps. If for whatever reason you want to start building something that you don't have materials for, let's say, for example, (t: 1490) the iron ingots, I can just say, OK, let's give me a couple of stacks of this, maybe a couple of stacks of all of this other stuff, and then just start building whatever you want. It's just very convenient to have everything close together. (t: 1500) At this point, you probably notice that you are going to need more and more stone, so making sure we have that automated as well is a simple matter of mining the stone facilities that we have. (t: 1510) So I'm going to start with the stone facilities that I have over here, the stone resources, I should say, and bringing those into a couple of smelters on the bottom here. So I added a few more rows of smelters. (t: 1520) I actually didn't build this final line yet because I ran out of stone. And one of these lines is going to produce glass (t: 1530) and then the other one is going to produce stone, or I think it's actually called concrete. Is it? Yeah. Well, no, it's actually stone bricks. I was right the first time around. Anyway, that's what it's going to be producing. (t: 1540) So I'm going to make sure you fill up the other belts in case you haven't done so yet. You should have enough resources here to fill out all of these three iron related belts, (t: 1550) as well as the two copper related belts with just the two nodes that we have up here. Okay, there goes that. So that means we have all the stone and glass in production as well. (t: 1560) Now, honestly, you don't need that much glass, but we might as well just get it up and running because we can. Make sure you keep researching and also make sure that you start working on some of your personal (t: 1570) upgrades, for example, upgrading the drone engines, makes your production or your construction so much faster. It's really noticeable in the beginning and getting yourself an energy shield is going to help you stay alive (t: 1580) in case you're getting larger and larger attacks. Now, as you can see, I scaled up my defensive facilities in the meanwhile as well, just to make sure I don't die from stupid drones getting in my face. (t: 1590) Again, if you're playing on a lower difficulty, you won't need anywhere near as many turrets, but you might as well in case you can. But you might as well in case you can. (t: 1600) And it looks really nice. So it has that going for it as well. Okay, so at this point, you probably produced a couple of hundred of belts already. Now, this is actually a pretty easy thing to produce. (t: 1610) So that's not the biggest thing in the world. But let's make sure we have that automated. It's by far the thing that you need the most. And the same thing holds for sworders. You're actually burning through those at a pretty high rate as well. (t: 1620) Now, in order to make belts, we are going to need the gears as well as iron. And we have gears right here. So what we can actually do, (t: 1630) is bring a second line of iron like this. Now, you have to press the up button to switch to the upper outgoing port over here. (t: 1640) Then we will get a raised belt like this. And then we can bring that belt down again by pressing the down key and bring that over here. Now, let's place another one of these assemblers like so. (t: 1650) Actually, we need one more space in between. (t: 1656) Actually, let's bring this one. (t: 1660) Down a little further. So we can actually have the outgoing belts go like this. Let's copy one of those. (t: 1670) Let's copy a storage unit as well. Now, the nice thing about copying storage units is it will actually copy the settings in terms of the limitations. If you copy it from an existing one, you don't have to reset it every time. (t: 1680) And by bringing in the iron like this and, of course, connecting a sorter to that and an outgoing sorter like this. (t: 1690) And then an incoming sorter over here. If I can actually reach that like so. That means we now have automated the belt production and we will get an infinite amount of belts over here. (t: 1700) As long as we don't run out of resources. But that's not going to happen anytime soon because we didn't use the starting patches, but we used the larger ones further away. (t: 1710) Now, this one is actually not going to make the belts. This one is going to make the initial Mark 1 sorters. We're also going to need some iron for that. (t: 1720) So we can just connect those up. We're also going to need circuit boards. So let me bring in another belt from over here. Something like this. Now, you will have some crossing belts. (t: 1730) So make sure that they're on different heights when you do that. But other than that, it's pretty nice and neat looking facility, I think. And we now have the main things that we need in order to scale up our production in a complete automated production. (t: 1740) Production, production, production. That's a lot of productions there. Now, I'm going to be under attack. But I'm actually going to build some. (t: 1750) Smelters over here in between the assemblers. Now, space them out a little bit so they're nicely aligned with the existing gears over here. And these are going to be producing steel because that's something we don't have automated just yet. (t: 1760) Don't you just love it when a plan comes together and it nicely fits together? I don't know about you, but I think this looks extremely organized, especially considering the amount of different resources we have in here. (t: 1770) We even have a little bit of mole production in here, as we call it. Actually, this entire thing is kind of a mole. And... (t: 1780) If you're not entirely sure where that phrase comes from, the idea is that you can just go shop around for whatever you need at a glance without having to produce anything yourself. That's why it's called like a mole when you go to a mole and shopping. (t: 1790) Anyway, in order to make our mole even better, what we need is some other production facilities because we just unlocked electric motors. (t: 1800) And we also have combustible units and engines, which are all used for a ton of things. So let's get those up and running as well. In order to space it. In order to space things out, I would recommend placing a single assembler in between some of these power facilities, then deleting that. (t: 1810) And then place two assemblers making engines, or I should say electric motors, because this... (t: 1820) These are the engines, but I used to call these other things engines, so this is just confusing to me. Sorry, electric motors is what you want to be producing here. (t: 1830) Why? Well, they need a couple of things. They actually need three things. You need the gears, you need the iron ingots, and you need the magnetic coils. Now, we happen to have both the gears as well as the iron right over here. (t: 1840) So that's very convenient and not completely coincidental because, of course, I planned this out. And we actually have the belt with gears over here. (t: 1850) And you can see we have plenty of those. So all we need to do is connect those up. Now, similarly, in terms of spacing, we can also set up some production for the foundations. (t: 1860) You can use these to alternate the terrain and make the terrain look a lot better. I ideally use the without. I usually use the without decoration portion, but you can use that to kind of fill in these gaps. (t: 1870) Now, you're going to need soil pile for that. And this is where the dark paw comes in because of all the units I have been destroying over here. I actually get a ton of soil pile just from destroying those units. (t: 1880) Now, if you don't have that and you're playing without combat or whatever, what you will actually need to do is go around. Every time you place a building on something that's vaguely a hill, you get a little bit of soil pile from that. (t: 1890) It's quite annoying to gather it yourself. Destroying robots makes it really, really easy because I actually fly the soil pile straight to you. (t: 1900) Anyway, let's set up some belts. Now, we're almost done setting up our mall, but the next thing we need is coal. And considering that's going to come all the way from over here, we only need two miners to get all the coal we need for the moment. (t: 1910) But it's still a long distance, so it's a good thing we have automated that belt production. Now, we actually want to start placing the combustible units. (t: 1920) So that's... where is it? This one. This is actually a very efficient fuel source for your Icarus as your mech itself. (t: 1930) So you might as well use it for that. But it's also used in a couple of other things like missiles, for example. So you're going to need quite a bit of that. (t: 1940) So you might as well start producing a whole stockpile of it. So you want to start producing those combustible units all the way up here. Just make sure you align it with your existing assemblers and make sure you kind of align it with the existing Matrix Labs. (t: 1950) So it looks neat and organized. You're going to feed in the coal from the top and then have the combustible units exported on the top as well. (t: 1960) Because we need this space to build our engine production. At this point, setting up the engine production is really simple as well. Because all we need to do is set down these three assemblers like this. (t: 1970) We're going to set this to produce engines. We need coils as well as copper for that. Like this. We also already have the actual coils over here. (t: 1980) Because we're also using these for the Blue Science. So that means we only need to bring in the copper, which we can do either from down here like so. (t: 1990) Or we can bring that in from up here because we also have more copper than we need for our ammo production. As you can see, this box is entirely full already. (t: 2000) Just make sure that you combine these belts in a nice looking way. If you want to do it like this, you can. I just don't like how this T-intersection looks. (t: 2010) So this is why I every now and then opt for this one. It's completely up to you whether or not you prefer one or the other. Of course, we are also going to need to have somewhere to stockpile all of that. (t: 2020) So let's have an outgoing belt like so. And connect everything up with our Mark II sorters now. So we can do this like this. (t: 2030) And you can see how easily you can set things up now. Okay, so now we have engines in our production line as well. And that brings us to the end of this first episode of this Masterclass. (t: 2040) Now, I actually think this is the most important part of this video. I think this is the most organized, most efficient way to set up a starting base that I have ever designed. Now, if you've been on this channel for a longer time, you know I have made quite a few versions of a starting base by now. (t: 2050) But I do think this is the most efficient way to go. I should probably address the important topic here that some of you might have been typing in the comments already. (t: 2060) TDA, where is the mall? Now, I would say this is the mall. (t: 2070) But some of you might be thinking more of a mall. And I think it's a bit more of a mall than I thought. But it is a mall. It's a mall of something like this. A facility where you bring in kind of a bus-like type of set of materials that you then use to craft all the basic items that you need. (t: 2080) So you can see, for example, again over here, things like the Mark I belts, Mark I sorters, but also things like smelters, assemblers. (t: 2090) Pretty much all the basic materials. Now, the reason I actually don't think you need to build a mall like this very early on, (t: 2100) is because you can buy a bit of time to build this. It's a huge amount of belts, huge amount of sorters and assemblers and things like that. And honestly, you don't need most of these buildings in large quantities. (t: 2110) The only thing that you really need in large quantities are the belts and sorters, which we did automate in the base that I just showed you. But everything else, you might need a few dozen of these assemblers and smelters. (t: 2120) But other than that, all the other buildings, you maybe need like a handful. And of course, you're going to need more and more of them as you go. But at some point, we will build a mall. I just don't think it's efficient to do that early game. (t: 2130) So don't stress about it. It's a nice project and it's definitely something you might want to consider doing if it's like your first time playing, because it just feels really nice to have everything automated. (t: 2140) But don't underestimate how many resources it takes to set this up until, of course, all the boxes are full. You definitely don't want to set the boxes to full, by the way. (t: 2150) Just set them to a few places. So you're never going to be producing more than a few stacks of this anyway. But yeah, keep that in mind. That this is something that you will see a lot of other content creators, including myself, build in their Let's Plays. (t: 2160) I actually don't think you should be doing this early on. Remember, you have all the building materials that you're ever going to need for all those basic buildings right here close at hand. (t: 2170) You have all the fuel you're ever going to need in terms of the combustible units over here as well. (t: 2180) So in other words, this is the entire facility that we have on the planet. So it's not like we're going to cover large distances so far. In the next episode, we are going to focus on getting probably both the red signs as well as the yellow signs going on. (t: 2190) I spent a little bit more time explaining the setup of this basic blue science base. So that will give us a bit more time to focus on the more advanced stuff in the next episode. (t: 2200) If you like this episode, make sure you like it. It really, really, really helps the algorithm find this content. So make sure that you do if you appreciated it. (t: 2210) And if you haven't subscribed yet, make sure that you do so you don't miss anything new coming on this channel. If you're still here, you're awesome. And I do hope to catch you in the next one. (t: 2220) Hey, hey, CBA and welcome back to this Dyson Sphere Masterclass. (t: 2230) Today, we're going to shore up our defenses because this is not going to cut it. (t: 2240) And we're going to be making sure we get access to that juicy red science. On top of that, we're going to do a lot of prep work in order to get ready for the mid game. (t: 2250) So all in all, there's a plenty of stuff to do and to explore. And we're going to do all of that by staying nice and tidy, getting everything collected and not making a spaghetti base. (t: 2260) Before we get started, just a shout out to Rob Miller and Glen Age, who recently became members of the channel. So thank you guys for supporting the channel. It's much appreciated. (t: 2270) Just as an aside, remember that you have this mall of different resources. Over here, we're not actually all the producing most of the building. So whenever you do start to run out of something, let's say it's smelters. (t: 2280) Don't just queue up one or two of them. You have a whole bunch of them, like 50 or 60. That way, the next time you start building something, you won't run out again. (t: 2290) And that just saves yourself a lot of time and frustration. Now, also remember that we have the combustible units over here and you want to be using those as your fuel. They're pretty efficient at doing so. (t: 2300) Something else I want to note is that it's very useful to start. Rejoice. I'm going to start researching drive engine if you haven't done so, because it makes getting around your buildings a lot easier because you can actually fly over them. (t: 2310) Now, something else you might have noticed is that I switched these arc smelters from producing glass to producing the silicon ore. Now, you will have to research the technology in order to do that, which is over here somewhere. (t: 2320) I always forget where it is. Here. Basically, whatever you need to unlock silicon will also unlock the silicon ore production. (t: 2330) Now, this is really, really, really slow. But it's the only way to get silicon on your starting planet. We don't need that much glass. So you might want to make sure you limit this a little bit, even toss out a little bit of glass if that's needed and start producing some silicon ore. (t: 2340) In case you're wondering why we need that silicon ore. (t: 2350) Well, as you can see, my base actually got damaged quite a bit in the last few attacks. If you're playing at a lower difficulty, this probably won't happen to you. But basically, what I want to do is try to automate my defenses a little bit more. (t: 2360) As you can see, everything is now damaged and it won't repair itself unless I go near it. And then my drones will take care of that. (t: 2370) Now, this is where the battlefield analysis base comes in, because this auto repairs your buildings. In order to build this, you'll need some silicon. So this is why we started to produce that. And specifically, you will need some components, as you can see, microcrystalline components. (t: 2380) Now, build a couple of those and place them in front of your defensive line. (t: 2390) Now, the reason you place these in front is because these things can actually take a while. They can take a lot more damage than the turrets themselves. And that means they will kind of tank your damage and form a protective barrier before your turrets get destroyed. (t: 2400) On top of that, by placing them next to each other, they will be able to repair each other as well. So it will take quite a bit of attacks in order for these things to actually be destroyed. (t: 2410) And hopefully that actually never happens. To give you an idea of how effective this is, as you can see, we have a wave of about 70 units coming in. They start damaging my front line a little bit over here, but they don't even get close. (t: 2420) This is my first time doing this. Because these waves are not that big yet. And as you can see, all my turrets get automatically repaired by the base. And I don't have to worry about this. (t: 2430) I can just start building my factory. Also, very handy, these battlefield analysis bases will automatically capture whatever items the Dark Fog drops. So you will have things like microcrystalline components just available to you in these things. (t: 2440) And you just can fly back here and pick up some of the more high-tech materials if you need them. Now, you can actually switch the drop filter as well. (t: 2450) So you might want to, for example, turn off the logs and the plant field because we're not really using these anymore. Same goes for magnets. You can probably produce those yourself. (t: 2460) And by disabling this, this will just prevent the base from clogging up with things you don't really need. You do not actually get additional resources from turning these off. (t: 2470) So it's not like you're replacing the useless drops with something that's more useful. You're just auto-deleting basically these drops. (t: 2480) So don't think you can just turn everything off and select one thing and then you will get huge amounts of those. That is not how this works. Okay, we're going to be expanding a lot during this episode. (t: 2490) And I'm not going to repeat this every single time. But make sure you keep an eye on your power production. We're probably going to have to almost triple our power consumption. (t: 2500) Well, consumption as well as capacity. And if you don't keep an eye on this, your defenses will suffer from it. Because of course, if you drop down below 100% efficiency, that also means you're not going to be able to do anything. (t: 2510) And that means your defense is not working at 100% efficiency either. So yeah, long story short, make sure you keep an eye on that. What we're going to do is we're going to produce some oil stuff. (t: 2520) Because we want, of course, the red science production up and running. And in order to do that, we're going to have to clear an area over here below this. Well, you can build it wherever you want, but I'm going to build it over here. (t: 2530) Simple reason is that we actually have some oil over here. So that makes it really convenient. Now, if you're going to fill in an area that has water in it. (t: 2540) Then you're going to need some foundations. Now we have some foundations over here in production for exactly that reason. You're not producing a huge amount of these. (t: 2550) And if you start just filling in all the things like this, then you're going to be consuming a lot of those foundations. So rather than doing that, just set it to the smallest amount. (t: 2560) And then you can just click and skip a few spaces every time. And you can very easily fill this in. Now you will see this soil pile being consumed. (t: 2570) And this is the really nice thing about destroying all those dark fog units, because they will actually give you soil pile in return. If you're playing on a lower difficulty level, you probably will have a smaller amount of that because you haven't killed as many units. (t: 2580) So again, this is one of the benefits of playing on a higher difficulty. If you don't have enough of this, all you need to do is find these little hills in the landscape and just place down some something. (t: 2590) It can be either foundations, but you can actually just place down buildings as well. (t: 2600) And you can see you get some soil pile from doing that. So anyway, just clear out a nice little square over here. Okay, so as I said, we're going to be building some oil production. (t: 2610) And if you're building anything from this point onwards, make sure you have a battlefield analysis base or maybe even two close by. Because that will make it a lot faster to build whatever you need. (t: 2620) Now we're going to make a row of six oil refineries like that. And then we're going to flip this one around. Press tab a few times and make another line of six below. (t: 2630) We're going to have an incoming belt with oil over here. This is going to be a hydrogen belt. And then on the outside like this, we are going to have an outgoing belt with the actual refined oil. (t: 2640) Now, as you can see, this goes a lot faster here building with a battlefield analysis base. Now setting up the sorters like this is going to be a little bit tricky. (t: 2650) So remember that we want the oil, the refined oil over here. So I'm going to make an sorter going from here to here. Then I'm not going to place it. But I'm actually going to press F. (t: 2660) And then I'm going to press tab in order to press a filter on this. And I'm going to set this filter to refined oil. And we're going to do the same thing in reverse from the other one over here. (t: 2670) Now, this means that the refined oil is only going to be able to come out through here. You can actually see the filter being set on the buildings themselves. And the hydrogen needs to come out in the back over here. (t: 2680) So again, pressing a filter and doing it like that and doing the same thing over here for this one. Now I'm going to do that for all of these buildings. But you only need to do it for these four because after that you can just copy them. (t: 2690) And for every building that's in the same location relative in the DUOs that we have over here, you can just copy and paste them. (t: 2700) I should probably have set the recipe before I did that. But remember, you can also copy paste the recipes by using the less than and greater than signs. (t: 2710) And that makes it really easy to set a recipe throughout your buildings. We're going to need to bring in some oil and we have that down here. And again, make sure that you use a filter. And again, make sure that you use a bellow foot analysis base whenever you're building a long stretches of belts, (t: 2720) because that will save you so much time. You can just remove them whenever you're done with them. Let's see. (t: 2730) We are also going to need some storage tanks in order to make sure we have a place for all of that oil and hydrogen to go. This can start producing. And then the next thing we want to do is set up a nice line of graphite production. (t: 2740) Now we're going to make sure this aligns with the smelters we have over here. So I believe. Then we need to place them like this. (t: 2750) And we're going to have six of those right here. I've connected some coal up to that as well. And I'm not going to show you where I'm getting every single one of my resource throughout the entire episode. (t: 2760) But mainly because that doesn't really matter. You can just get it from wherever you want. But of course, I'm prioritizing whatever is close or nearby. I'm trying to stay away from anything that is between me and the dark fog. (t: 2770) So this oil is kind of unfortunately in between that. There's actually a lot of oil in between. The dark fog and myself. (t: 2780) So that is going to be a long term problem. But for now, I don't have to worry about that. So just building along like I am. Something else I'm going to be doing. Is I'm placing down a lot of these splitters and boxes in between parts of my facilities. (t: 2790) For the simple reason that I like having multiple places where I can draw these resources from in case I need them. You don't typically need that a lot. (t: 2800) So this is a little bit of overkill on my side. I just got into the habit of doing that. So you can leave these out. If you want. Now with this very simple build over here. (t: 2810) We now have access to both hydrogen and graphite. And that's all we need to start making some red science. So that is exactly what I've done over here. Now, of course, I aligned this production with the blue science production that I already had. (t: 2820) So it looks nice and tidy. And I actually removed the matrix labs that we had over here to do the actual research. (t: 2830) And I placed that down over here again. Nicely aligned with the ones we already had. So bringing down a belt like this. The reason I put the 1.5 here on the belt. At least that's what it's supposed to represent. (t: 2840) Is because we're producing 1.5 red science per second. That's not a whole lot. But it's going to be quite sufficient. And we have a lot of research to do. (t: 2850) So actually prioritizing where you go next in the tree is pretty important if you don't want to waste time. I recommend picking up the signal tower first. And then right after that we're going to make some turbines. (t: 2860) So make sure you queue that up as well. Now other than that. Just to give you an idea where we're going to go next. (t: 2870) You want a couple of things. So you want the crystal smelting. The one over here. Because you actually need these crystals to make the signal. What are these called? Signal towers. (t: 2880) And what else do we need? We are going to. Well you basically need everything at some point. But you might want to prioritize the titanium smelting. (t: 2890) As well as the distribution logistics system. Because we're going to be. Making use of that in the short term as well. After that. Just focus on getting everything you need up to the logistics system. (t: 2900) You're going to need to pick up a couple of other sciences technologies to do that. We're also going to need. (t: 2910) Let's see. The second priority that you should focus on is what? The particle container. Because you're actually going to need these in order to make the logistics systems. (t: 2920) Like you can see over here. While you're waiting for the signal tower to be researched. You might as well make sure we get some missile production up and running. (t: 2930) So I added in a assembly machine over here. We already had all the required items in order to do so. So we have the combustible units on the side here. (t: 2940) We're bringing in the engines from below here. We were bringing in the circuit boards from over here. Which is going to go on top of this belt over here. And then make a little quarter. (t: 2950) And I added in a splitter over here. To make sure that there is some copper going in. As well. Of course we're going to add in a box. This is one of the few boxes that you don't really need a limit on. So you might as well just have that. (t: 2960) There's no such thing as having too many missiles. And we're not going to be using the missiles just yet. We'll do that later on. But of course if you want. (t: 2970) You can make a nice little turret array over here. It's all ready. But the missiles actually need the signal tower to be useful. At least that makes them a lot more easy to use. So let's focus on that first. (t: 2980) In order to collect the missiles. You're actually going to need some of this crystal silicon. And in order to make that. You can't actually do that by yourself. (t: 2990) So you're going to need a smelter to do that. So just place the one down. Put a box in front of it. To put in the silicon. And then just have an outgoing box to make this. (t: 3000) This is a temporary facility. You only need maybe like a handful of signal towers. Actually you technically only need one. But I recommend just making a few. And then deleting this little setup. (t: 3010) Then place these signal towers in front of your. Battlefield analysis bases. And the reason for that is that these things are actually. Even more sturdy than the battlefield analysis bases themselves. And what this will actually do. (t: 3020) Is this will draw the enemy to these towers. So you are no longer in danger of expanding your facility. And suddenly having your enemies go around your defenses. (t: 3030) No they will always home in now on these towers. So you can build wherever you want. As long as you are not putting any buildings in the direct line. (t: 3040) Between the enemy. And these towers. You should have no problem anymore. Defending yourself. And making sure that these enemies are going to go. Exactly in the middle of your defense. (t: 3050) Which is also why I put these towers not on these sides. But in the middle. Because that will draw the enemy into the middle of my defense. And into the maximum range of all these towers. Okay enough about defense for a little while. (t: 3060) This should be able to handle itself now. And we are going to be building on the right side of our base. Because we have a nice open area over here. And of course I paved in a little bit of a wall. And I will be able to. (t: 3070) I paved in a little bit of territory. With the foundation method that I just showed you. What we want to start building now. Is some turbines. I have some benefit analysis bases here. To help me out with the building. (t: 3080) And I want to be producing about two per second. Now in order to calculate how many assemblers you need for that. Just look at the recipe normally. It produces one turbine every two seconds. (t: 3090) So that is 0.5 per second. If you divide those two with each other. Now keep in mind again. I mentioned this in the previous episode as well. That the assembly machine. (t: 3100) Actually produces at 75% speed. So you are not producing 0.5 turbines per second. You are actually producing 75% of that. (t: 3110) Now that means that if I have six of these assembling machines. Normally that would produce three. So 0.5 times six is three turbines per second. (t: 3120) But multiply that with 0.75. And you will get a little bit more than two turbines per second. So I am just going to round it to two. (t: 3130) But I am going to make sure that I keep that fraction in mind. For the rest of this build. Now if you want to make some nice looking layouts. All you need to do is work your way backwards from your original goal. (t: 3140) So right now for example. I know that I need two turbines per second. That is my goal. So I need twice as many motors per second. For example. (t: 3150) Because as you can see. There is a two to one ratio in the recipe. So that means I need to produce about four. Or 4.5 to be exact. Motors per second. And in order to do that. (t: 3160) Again you just need to check the recipe. And it is actually producing at the same speed. So you need exactly twice as many motor assemblers. (t: 3170) As you need turbines. So that means that I have a total of. One, two, three, four, five, six, seven, eight, nine, ten, eleven over here. And it is eleven. (t: 3180) Because I am actually aiming at exactly two turbines per second. So actually you only need five point something of these. And do that time-wise. And do that times two means I only need eleven of these. (t: 3190) Rather than six. So again. It is exactly going to be two turbines per second. So this is why the math doesn't exactly turn out. (t: 3200) But you might be using a calculator to do this. Or maybe a spreadsheet like I am. It doesn't really matter how you do it. Just make sure you keep your ratios in mind. (t: 3210) So you don't over build or under build certain parts of your build. Let's see. What else do we need? We need some magnetic rings. We need some iron over here. And we need some gears in order to make all this. (t: 3220) And the nice thing about the coils is that they are actually used for both the motors as well as the turbines. So this is why these are on a belt in between. (t: 3230) So I can make sure I can use these for both. Now it requires a little bit of planning to make your builds look nice. But for example over here. Because I know I did the math on this. (t: 3240) I need four assemblers making the gears. And the gears are actually also made from iron ingots. And we have the belt of iron ingots over here. I decided well why not place them down in this little corner over here. (t: 3250) So we can keep everything nice and square. If you put them over here. There is nothing wrong with that. But you get a little bit less organized looking facility. (t: 3260) And again I am just putting in some of these splitters and boxes. Just because I can. Editing TDA from the future here. There is a small mistake in this build that I didn't catch until later on. (t: 3270) So what you should be doing is remove these boxes over here. And move one of these turbine assemblers to the right. I am going to remove the one on the left. (t: 3280) The number was okay. But the problem is that we are outputting the motors on this belt. And because this one is actually behind the last turbine assembler. This one will just be producing motors for the belt. (t: 3290) And not for the actual turbine. So that means we are losing some efficiency by having one of our assemblers doing nothing basically. (t: 3300) So as long as you do that. And then hook up everything to the belts. Make sure you are keeping everything in place. Make sure you are actually connecting the outputting sorters on the belt over here. And the inputting sorters somewhere. (t: 3310) For example like over here. That way you can actually output it on the belt. And still catch the production of the last assembler with the last turbine assembler. And you should be just fine. (t: 3320) Okay next up we need 12 of these smelters making the iron ingots. And this belt is going to go all the way around. It is going to fuel the assemblers making the motors. (t: 3330) But it is also going to fuel the assemblers here making the gears. Similarly we are adding in 12 smelters with magnets. 6 assemblers making magnetic coils. (t: 3340) And then we need 4 of these smelters making the copper ingots. And then we have everything ready to go. So the magnets are going to go through the middle. (t: 3350) And then the copper is going to go on top with the outputting belt for the coils over here. It is going to go all the way down here. And then right through the middle over here. So both the turbines as well as the motors can draw from them. (t: 3360) Then I have the motor. And then I have added all the solders and the power poles to everything now. And as you can see it is all humming nicely along. (t: 3370) And we are producing 2 turbines per second. It is exactly 2 turbines per second by the way. I made sure that all the incoming materials are tuned to that. So we should not be producing a 2.25 or whatever I said earlier on. (t: 3380) Just 2.0 flat. Now I actually do recommend that you don't put in the power and the solders. Until you actually have all the buildings where they need to go. (t: 3390) For the simple reason that if you move anything. You will be fiddling around with the solders and the power way too much. So it is best to leave those to the end. Now again I have some resources coming up from the north over here. (t: 3400) That is not a problem anymore. Because remember the signal towers are drawing in the enemy right through the middle. So I can pretty much build wherever I want. (t: 3410) Now there is a problem with this build. And I am going to let you type in the comments for a moment. Pause if you need to. To spot what the problem is. Do you have it? Yes? (t: 3420) If your answer was the belt speed is too low. The answer was correct. As you can see we don't actually have the iron ore reaching the end of this belt over here. (t: 3430) So these smelters are doing nothing. And that is because we need Mark 2 solders in order for this build to actually function. So let's make sure we have some of those. In order to make Mark 2 belts we need you guessed it. (t: 3440) Turbines. And look what we are now making. Turbines. On top of that we actually need the same turbines for the Mark 3 solders as well. (t: 3450) So we might as well make those. And that is where this little gear is. This little setup is coming in. We are going to draw in the Mark 2 solders and the Mark 1 belts over here. (t: 3460) And we are going to add some of these turbines to that. And we are outputting all of that into these two boxes over here. Now in order to make sure the materials are actually reaching these various storage units. (t: 3470) We are going to be making a couple of logic distributors. Along with some of these little drones to ferry stuff back and forth. (t: 3480) Now in order to make those we are going to need some processors. Some prisms and things like that. But all of that should be pretty easy to make from either what you have in your inventory. (t: 3490) Or you should probably have enough of those items in your battlefield analysis bases already. Now just in case you don't know. (t: 3500) You place these logic distributors like so. You place them on top of the box like this. Then you set the recipe to whatever you need to collect over here. So in this case this is supposed to draw in the Mark 1 belts. (t: 3510) You put it to request from other distributors. If that's actually what you are doing. And then you can put in a couple of these drones like so. (t: 3520) And that should automate that. Assuming you actually have somewhere supplying these things. So for example over here I have another one of these distributors on top of a box. I set this to distribute now. (t: 3530) And as you can see minus 48 there is a couple of drones flying in to collect those. Similarly we do the same here for the sorters. And I think I already did that for the... (t: 3540) Nope I didn't actually do that yet. So let's make sure we also do this for the turbines over here. Like so. And you don't actually need to power the one that is being collected from. (t: 3550) Only the one that is sending the drones. So even though that one is on power that doesn't actually matter. Now we are getting in all those resources. (t: 3560) And as soon as the turbines arrive we should start producing both the Mark 3 sorters. As well as the Mark 2 belts. Another item we are going to need a lot of are processors. (t: 3570) And we might as well start processing. Because we can actually already produce them. Even though we need silicon for that. Because remember we can make our own silicon. Or is it going to be fast? Not at all. (t: 3580) But if we have a build plugging along for a couple of hours anyway. We might as well have it do so. Even though it is slow it is still free resources. Well not free resources but it is resources we are going to use a lot of. (t: 3590) So anyway the point is we are going to make some processors. And we are starting out with a build of 12 smelters making silicon. So we are going to make a build of 12 smelters making silicon. Now I am aiming for 1.5 processors per second. (t: 3600) Just because that turns out to be a nice number. Because you need those 12 smelters for that. (t: 3610) Which is actually one full belt of input. So that is why it is tuned to 1.5. Even though that seems like a random number. In order to make 1.5 processors per second. (t: 3620) We are going to need 8 assemblers making micro crystalline components. So you can see this is a pretty expensive. Pretty large recipe in terms of how big the factory is. But it is a pretty big recipe. But it is a pretty big recipe in terms of how big the factory is. (t: 3630) To make all of that. Now in order to make processors we also need the circuit boards. So we only need actually 2 assemblers making that. So that means we are going to bring in not just copper. (t: 3640) But also some iron ingots from somewhere. Which is probably going to be down here. We have an exporting belt over here. And once again I am using some of these boxes to collect the overflow. (t: 3650) In case there is any. And actually that ingot production is all we need to add. And actually that ingot production is all we need to add. So I have 3 of these smelters making iron ingots. (t: 3660) And 5 smelters making copper ingots. And then the copper ingots are going to go all the way across here. And then onto this belt in the top. Again there is just a box to collect the overflow. And similarly I have the iron ingots over here. (t: 3670) Now you could argue to place this assembler over here. So it is more nicely aligned with the ones on the top. But let me demonstrate to you why that is a problem. (t: 3680) I actually need to collect more of these iron ingots. That I can actually reach. Because this will only be able to reach the iron ingots from the belt over here. Which is not enough to keep it fully functional. (t: 3690) So that is why this second assembler. Or the first assembler actually needs to be over here. So the second one can collect the full output of these iron ingot belts. (t: 3700) As well as this one. It is just a tiny little thing. And it is kind of annoying me honestly. That we could not just scooch everything up one space to the left. (t: 3710) You could solve that actually by just placing the smelters a little bit more to the left. But that would not align anymore with this. So whatever your OCD fancies. (t: 3720) Just go with that. But this is what I decided upon doing. And there you go. You have it all powered up and sorted up. Is that a word? I don't know. (t: 3730) Obviously this is not yet going to be producing at 1.5 processors per second. But it is completely up and running. And ready to do so once we get some access to more larger amounts of silicon. (t: 3740) In order to make sure you have any silicon coming in. What you can do is of course use the logistics distributor once again. To collect that from the little. Alternated facility we have over here. (t: 3750) Now if you ever run out of glass. Just turn it back. And have it produce glass for a little while. The glass is really easy and fast to produce. This is really really slow. (t: 3760) So you might as well prioritize this. And only produce glass when you actually need it. Now if you want. You can actually start building a very easy to use mall. By using a similar setup as you can see over here. (t: 3770) Just with boxes pulling in different types of resources. You can actually start building a very easy to use mall. You can put an assembler to make whatever building you want. And then output it into another box. You can even put a logistics distributor on top of that. (t: 3780) And if you do that. And you set the recipe to. Guess filter. Which is going to be the one that is in it. What you can actually do is set your Icarus. (t: 3790) To request those items. For example belts like this. You can say I always want to have at least 3000 of these belts in my inventory. If you do that. Assuming you actually have any bots in there. (t: 3800) So let me get some bots from one of the others. And then you can start building. I want these bots. Give me your bots. I'm going to put the bots in here. (t: 3810) And then as you can see. I will get the items delivered to my inventory. Completely automated. It's not going to go fast with just these two bots. But they will be flying back and forth as long as I am in the range. (t: 3820) To give me more and more belts. You can do that with every single building in the game. So if you want. You can have a fully automated mall up and running now. Now I'm actually not going to do that yet. (t: 3830) Mostly because I don't think it's really necessary at this point. Still. Just not now. But again. If you do want your mall. (t: 3840) Then this is a perfect moment to build it. We are actually producing almost everything you need. In order to make the distributors as well. The only firmware lacking is the plasma exciter. So you could make a tiny little build for that as well. (t: 3850) Based on the examples that I just showed. You shouldn't be too difficult to make a build to do so. I just don't think. Again it's really needed at this point. Because you are not going to be using that many logistics distributors just yet. (t: 3860) And it's pretty much the only thing where you need to build. The plasma exciter for. So it's completely up to you. Whether or not you want to make that build. We actually do have all the items you need for the bots themselves. (t: 3870) And you are going to use those in higher quantities. If you are actually going to go and make that mall. So at least make sure that you start automating that one first. And that concludes this episode. We have the blue and red signs up and running now. (t: 3880) We have some of the more advanced materials completely automated as well. We have a pretty nice looking defense. That is actually the blue and red signs. That is actually the blue and red signs. That is actually the blue and red signs. (t: 3890) And that is actually the blue and red signs. And that is actually the blue and red signs. It is actually self-repairing and self-sufficient. It is actually self-repairing and self-sufficient. And the only thing is you need to watch out. (t: 3900) Especially if you are playing at a higher difficulty level. If you don't get overwhelmed. We are going to make sure we upgrade that a little bit. In the next episode as well. We are actually going to be on this planet. (t: 3910) If you watched my previous playthrough. You actually saw me rushing to the second planet. And starting to build a base there. Having tried that out for a couple of times. I am not entirely sure that is the most optimal way. (t: 3920) I am not entirely sure that is the most optimal way. play things it's definitely a way you could go so if you think that's interesting make sure you watch my other playthrough but the upside of staying on this planet for as long as we can (t: 3930) we actually take advantage of the fact that we have to research all that the red and blue science stuff and we actually are making a couple of builds just like the ones over here we're actually (t: 3940) preparing for the mid game already and that makes it super easy to scale up once we do actually get access to titanium and silicon and there's a lot of prep work that we need to do in order to get (t: 3950) all those ils and vessel productions up and running that we will be focusing on of course pretty soon as well anyway if you're still here you're awesome and i do hope to catch you in the (t: 3960) next one hey hey studio and welcome back to this updated masterclass series today we are going to (t: 3980) visit other planets produce yellow science and transform this nice little starting base to something that will last us all the way to the end game this episode is a little longer than usual but here is a sneak preview of (t: 3990) what you will end up with after these steps i considered splitting this episode in two parts but everything we will be doing today just fits together really well the mid game transition is (t: 4000) also where i think most people make the most mistakes or at least make very inefficient decisions so getting this right will be worth your time (t: 4010) before we get into it i want to thank everyone for leaving comments and liking the episodes of this series it's been a major help in getting the algorithm to show to new people as well a special (t: 4020) shout out is in order for paulo andrade glenn h where's your planet over here and rob miller whose planet is hiding here in the back for becoming members of the channel your support is very much (t: 4030) appreciated for the price of a small coffee you now get early access to all my content okay let's (t: 4040) start with producing something out of the box i'm going to show you what it is in this first chapter of this series so let's get into it some setup for the yellow signs and we're going to make that nice and tidy just below the actual red signs production you'll see why in a moment and i'm going to aim for an exact amount of two (t: 4050) yellow signs per second now why two per second mostly because that turns out to be very nice (t: 4060) in terms of rounded numbers of buildings that you need for that and it's also a very nice amount to aim for again i i noted this in a couple of the comments in the previous few episodes but you (t: 4070) never really want to start making a new type of product with more than maybe two or three per second sometimes even less than that mostly because you just just want to get it going and (t: 4080) most of the time you don't actually have the raw resources to produce much more than that or it will completely drain everything else that you're doing at that time so just aiming at two per (t: 4090) second now in order to make yellow signs of course make sure you start resourcing in that direction as well also try to pick up anything that's related to the interstellar (t: 4100) law of attraction and i'll see you in the next video so until then take care and have a great day and i'll see you in the next video. logistic system so you want to pick that up after that but focus on getting the actual yellow signs up and running first uh what else do you want to focus on the reinforced thrusters you might want (t: 4110) to leave out until until a little bit later but don't wait too long because this will take quite a while to complete this is one of the main bottlenecks in terms of the actual research so (t: 4120) make sure you pick up the more cheap things early that you're actually going to use then make sure you start working on this because this will take a long time but make sure you get the (t: 4130) actual yellow signs first something else that you might want to focus on is getting things like the communication control it's really cheap and gives you some extra drones of course the mass (t: 4140) construction in order to make use of blueprints and the drone engine to make all your drones a lot faster at some point before you leave the planet you're also going to need a drive engine (t: 4150) but this is really cheap to pick up so you can pretty much do this at the last moment okay step one you're going to need six smelters making graphite and then two smelters and making (t: 4160) diamonds and six is going to be the magic number for this entire build and for those of you that did the math you will notice we're actually producing more graphite than we just need for these two (t: 4170) assemblers making diamonds and that is because we want to use the graphite for something else as well and as you can see we also need some plastic and in order to make plastic we need graphite as well (t: 4180) as oil and that's where this belt comes in because any of the graphite that's not being used by the diamonds is going to be used in the production of plastic over here now on top of that we're (t: 4190) actually going to bring the oil from up here down here and we're also going to connect this outgoing oil facility this and the reason that we're doing this is if these storage units will ever get full (t: 4200) this whole facility backs up so we will no longer be producing the hydrogen and that's a problem because we need the hydrogen in order to make the red science now we always want this to be producing (t: 4210) there's no such thing as having too much science so by tying it to another science facility which is also aiming to be always producing the hydrogen and that's why we're going to use the graphite as well (t: 4220) When we're ready to start our production process we're going to make an organic oil so that Brian is going to start with and it will show us the exact number of times we're going (t: 4230) to add oxygen to the oil and if it's three beats is probably more than that you can actually show us that when it's fused together we can visualize a certain number of (t: 4240) times we should be able to put more hydrogen here so this time we're going without the oil so the drop in practically only one incident is available after the production preparation period (t: 4250) to need some water and we're bringing in the water from over here as you can see all we need to do is just put down some foundation something like this if you build the foundations in a nice little (t: 4260) tidy row then you should get a nice left shoreline which will allow us to build some water pumps over here that i still have to handcraft but that will only take a minute next on the list are titanium (t: 4270) smelters because of course we're not going to be able to make titanium crystals without well titanium so um we're currently not producing titanium on this planet so if you want you can (t: 4280) just put in a box and dump any spare titanium that you might have in your inventory on in this box and then onto this floor if you want to grab yourself some titanium and you're in a desperate (t: 4290) need for some you don't have any on your battlefield analysis basis what you can do is find one of these larger stones and typically these will also give you a small amount of titanium so (t: 4300) as you can see i got 18 titanium and i'm going to put this one in here and then i'm going to put this right away um because i was thinking about getting a little more titanium just from that (t: 4310) um you're probably going to have some of these crystals in your battlefield analysis basis as well i'm still researching some upgrades in the meanwhile so make sure you always keep your (t: 4320) science going um and yeah that's one way of getting some crystal now if you're like me and you have a dump box somewhere in your base like for example over here where i dumped all the (t: 4330) resources i'm not really using at the moment you can pick up your titanium at this point and put put it to use now we're almost done just add six more assemblers making titanium crystals so six (t: 4340) six six six six the only thing that we haven't built six off are the smelters over here the diamond smelters and now we have everything going so we have the organic crystals being (t: 4350) outputted on this belt going in between here and then we have the output the titanium crystals which need both the titanium as well as the organic crystals and we have that on that belt (t: 4360) here now make sure that you're always grabbing the titanium as far on the belt as possible (t: 4370) because once again we need to make sure that each of these assemblers can reach at least one of these smelters so you need to make sure for example this first one (t: 4380) gets it from the belt over here because otherwise there's nothing to get and then last but not least we have a total of eight matrix lamps making the yellow science now (t: 4390) as you can see i'm still researching the yellow science because i actually kind of forgot you know what i'm doing here so i'm just going to make sure that i'm making sure that i'm making the yellow science because i actually kind of forgot to make sure that i'm making the yellow science to prioritize this but that won't matter because for now the facility is not working anyway but it is good to go for as soon as we get the automated titanium going now of course this (t: 4400) outgoing belt is going to go all the way down here there's a collecting box just in case we (t: 4410) have to produce more yellow signs that we can use up and then it's going to go in between here into the actual processing facilities over here that we're doing using to make our actual research (t: 4420) and we have all three science types combined in that single facility in the middle over there as you can see i hooked everything up you only need a couple of these water pumps i always built way too many but that's just me um and yeah that's that's all there is to it i really (t: 4430) like this layout by the way where we have the coal coming in at the same place for the coal for the (t: 4440) red science and the reason for that is this will make it a lot easier to automate this later on now remember this little assembler that we made over here making some missiles as you can see it (t: 4450) made a lot of missiles but it's not a big deal because it's a little bit more complicated than what we're used to so we're just going to keep it simple and we're going to keep it simple and now it just has just been plugging away for a very long time that's a good thing as well because as you can see the waves are getting bigger and bigger we're almost up to the limit of 180 units (t: 4460) and these larger waves might actually be able to take out the building or two every time they come in and that's of course not something we necessarily want to happen so let's put these (t: 4470) missiles to use now all you need to do in order to put these missiles to use is build look at a dozen or so maybe even less i have 10 over here of these missile launchers and make sure the power (t: 4480) is up and connected to the belt now you want to have the outgoing build on this side so that any (t: 4490) overflow of missile production is still going to be stocked up because we are going to use those missiles in a couple of other places as well pretty soon but for now just keep stockpiling them (t: 4500) you might even want to add in a second box just to make sure that if the first box gets full you will start adding something into the second box because there is no such thing as too many missiles now (t: 4510) the nice thing about these missiles is that they will actually even though they're in the back you can still get a little bit of a boost from the front so you can see that the missile launchers are actually pretty good at that so if you want to get a little bit more of a boost from the front (t: 4520) you can just go to the left of your base over here they will communicate with these signal towers and this is also one of the reasons you want the signal towers in front because anything that (t: 4530) gets into the range of the signal towers will get obliterated by the missiles from over here not only do these missiles will fly all the way across the planet if they need to in order to do that (t: 4540) they also do explosive damage so they're really good at taking out larger group of enemies instead of getting at them so it's not exactly that hard just by laying them out and then getting them into the ground it should be pretty easy to get them out and then just doing some (t: 4550) more prep work because i can't actually make the ils's that i want to make now for those of you that aren't aware ils stands for interplanetary logistics center what's going on with my mouse (t: 4560) um over here as you can see interstellar logistics station short ils and in order to make that we're (t: 4570) actually going to need a fair amount of particle containers and we're actually also going to need some titanium alloy now in order to make titanium alloy we first need to research this this needs 80 yellow signs so not all that much but in order to make this we're also going to need sulfuric acid which you haven't needed for anything up to this point so usually what it ends up to is me getting a lot of the same but not (t: 4580) being able to do a lot with them so in order to avoid that mistake we're going to do some prep work okay so step one you're going to need to replicate the exact same facility that we just (t: 4590) built over here for the red science and now we're going to have to feed in another belt of oil over (t: 4600) here this time around we're actually not going to be using the hydrogen but we're going to use the oil and specifically we're going to use the oil in these six chemical plants making sulfuric acid (t: 4610) now as you can see this not only requires oil it actually also needs raw stone and water i'm actually thinking we bring that in from the back over here because honestly we have some stone (t: 4620) down here which is already used up mostly but we do have more stone over here in the back we have (t: 4630) plenty of water around you so this should not be an issue we have an outgoing belt over here might as well store up any sulfuric acid that we produce while we are waiting for the actual titanium alloy to unlock but other than (t: 4640) that make sure that you may never have the storage tank full of hydrogen because otherwise you have the reverse problem that we had in the red science facility because if this ever gets full (t: 4650) the rest of the production facility will stop so you will need to drain this off at some point luckily the production of hydrogen is not that fast (t: 4660) and we're going to have to do a lot of work on this one so we're going to have to do a lot of work on this one and there's plenty of places to use it so it's not going to be too much of a problem keeping these empty next on the list is another number magic number six you can see a pattern going in six six (t: 4670) six six these are the six smelters that are actually going to produce the titanium alloy so we need to sulfuric acid for that but on top of that we're actually going to need titanium (t: 4680) which brings us to another six smelters making simple iron another six smelters making steel (t: 4690) and then we have this little bit of an arches unterstütalia which is actually going to push off the titanium alloy And a total of four smelters making titanium looping in over here down to the other build where we're going to be drawing in titanium, the steel and the acid in order to make the titanium alloy. (t: 4700) That is once we research it. Now, although this facility is going to be producing two titanium alloy per second once it's up and running, I would actually recommend that you don't put any titanium in here just yet. (t: 4710) But focus that on the yellow science production down here that I look, I activated it. (t: 4720) So we can actually do something with that. Now, if you get any titanium early on from your benefit analysis basis or from your random mining of stones, just put them into the science production. (t: 4730) And now remember, we have one last ingredient missing, and that is the particle container production. And I cleared out a nice little space up here. (t: 4740) That's right above where we're producing the turbines and the processes over here. And this should be a perfect location to start focusing on building. (t: 4750) Some particle containers, particle containers are actually pretty straightforward to build because if you check out the recipe, let me show it to you over here. Where is it? (t: 4760) We're going to need turbines, which you already have in production. We need some copper and we need some graphene. Now, the graphene itself is always a little bit annoying because you need graphite as well as sulfuric acid. (t: 4770) So it's a pain in the butt in order to produce that early on. Later on, as you can see, there is a secondary recipe using fire eyes, which is going to save us massive amounts of time. (t: 4780) But for now, we're going to have to do it the hard way. So let's start with making some graphite. There's actually a Mark two belt coming in here, as you can see, and we should not connect this up to the splitter. (t: 4790) This is going to supply these twelve two times six graphite production facilities over here and combining those back into a single belt. (t: 4800) I really like this type of layout because it's a lot cleaner than having a very long belt of smelt so that you don't have to flip it back on itself. So. (t: 4810) Just an alternative way of making a nice, compact production facility. Next up on the list are another six. Yes, the magic number six. Once again, six of these oil refineries and making oil. (t: 4820) And again, we're going to need a belt for the actual oil. And then we have a secondary belt siphoning off the hydrogen on the bottom over here. (t: 4830) Next to that, we're going to make a tiny little facility making the actual sulfuric acid this time around. We only need three of those because you actually don't need that. (t: 4840) Much sulfuric acid in order to make this stuff. And of course, remember, you're also going to have to bring in the stone in the water from somewhere. Shouldn't be too much a problem. We have a lot of that stuff on this planet, but still, it is something to keep in mind. (t: 4850) Now we are bringing in the sulfuric acid down here into this storage tank over here so that we can use it in the next step. (t: 4860) The next step being, you guessed it, a total number of six chemical facilities making the graphene. And I don't know if this is intentional. (t: 4870) By the way. I don't know if this is intentional. I don't know if this is intentional. But the magic number six is used in so many places. It's in the Mark I belt. The Mark II belt is two times six. If you're not doing any of the math and you just keep the number six in mind, that actually will carry you pretty far into the game without having your ratios off too much. (t: 4880) But anyway, as you can see, I have two separate facilities making the graphene because one of them is going to go in this belt over here. (t: 4890) We're actually only going to utilize this bottom part for the moment. But it's always... (t: 4900) It's always useful to have the secondary up here and having all of this built is something you could do with. We actually need quite a bit of graphene later on. So you might as well put that to use for the moment. (t: 4910) Okay. So now you might be wondering, that's great. Now we have the graphene, but didn't we need copper and turbines as well? Yes, we do. And that's actually something we're going to be producing down here. (t: 4920) I cleared out this nice little space over here. And what we can do now is actually copy the bill that we have going over here for the turbine production. (t: 4930) Make sure you're not copying anything you don't want to, but make sure you capture all the important belts. Copy paste. And now we can set that down nicely aligned with everything we already have over here. (t: 4940) And ta-da! Make sure you have some battlefield analysis bases helping you out with building this. But this is the exact number of turbines that you need to start producing in order to make the actual particle containers. (t: 4950) However, remember, we also need the actual copper for the particle containers. So that's something we're going to do. (t: 4960) And that is why you need to replace the four smelters that we had up here for the turbine production and replace them with six smelters making copper. (t: 4970) So you're producing just a tiny little extra of copper and that will go on this belt over here. So something I rarely do, but I'm going to make an exception to the rule now is build something from north to south. (t: 4980) And that is going to be the particle container assembling machines over here. You need a total of five of those, not six for a change. (t: 4990) And those fit nicely on the right hand side of the turbine build that we just placed down. Now all we can do is bring in the graphene from the build up top all the way down, nicely aligned with the assembly machines over here. (t: 5000) And then we can bring in the copper from over here. Did I connect the wrong belt? (t: 5010) Yes, I did. Hold on. So like I was saying, bring in the copper from over here. And as you can see, we have a nice little bit of overflow of copper going on over here. And bring that down. And bring that down to the particle containers as well. (t: 5020) And look, we are now producing particle containers nice and tidy with a nice little inflow. Now all of this work is just to get a production of one particle container per second going. (t: 5030) And that might not seem like much. It isn't much. But if you leave this working for maybe half an hour, you will have plenty of particle containers to make all the ILSs that you want. (t: 5040) And you definitely do not want to scale this up any further. At the same time, you also do not want to start the process. You want to start building this as soon as you want to start producing ILSs. (t: 5050) Because you are going to need like 60 per ILS in order to make that. So if you are then producing it at a very slow rate, that's far from ideal. (t: 5060) At this point, I can almost hear you screaming at me in the comments. TDA, enough with the prep work already. Let's get going to some other planet. And that's exactly what we are going to do now. (t: 5070) However, picking your next planet is not entirely trivial. It depends a little bit on the system that you are in. Right now, it seems that most of the Dark Hive is actually focused on this planet over here. (t: 5080) So the Lava planet. As you can see, it's busy here. They are having a party over here. So there is pretty much only one safe place to land on this planet. (t: 5090) Making this not the ideal candidate to start out your first expansion into the system realm. Now we do have actually all the resources that we need on this planet. (t: 5100) So I can see why the Dark Forb is leaning towards this planet. Because it's a pretty nice planet to build on. But let's check out the alternative that we have over here. (t: 5110) So, as you can see, we also have all the resources over here. We don't have that much Silicon. We don't have that much Copper. But for now, this will do. (t: 5120) We have plenty of those resources on our starting planet. What is fairly typical is that you will not have any oceans on the other planets. So you will need the water and the oil from the planet that you start out on. (t: 5130) And you probably also won't find a lot of things like coal on those planets. And you probably also won't find a lot of things like coal on those planets. And you probably also won't find a lot of things like coal on those planets. So, with that said, we're done. Now, we have a little bit of a course. That differs a little bit between the different seats of course. (t: 5140) But in general, you might be having to work around a little bit of that. Now, the most important thing that you need on your second planet is titanium. Silicon you can make from stone if you really need to. So if there's no Silicon over there. And no Silicon in easy reach. That's very inconvenient. But it's not something that's completely impossible to deal with. (t: 5150) But you do need a steady inflow of titanium for a lot of reasons. So, make sure you have titanium in your plan. And you will need it to get all the resources we have. So, this will do. And then we also need the water and the oil from the planet. reach that's very inconvenient but it's not something that's completely impossible to deal with but you do need a steady inflow of titanium for a lot of (t: 5160) reasons so make sure you have titanium on the second planet that you visit apparently we also have fire eyes on this planet which is something that's (t: 5170) not very typical and something that's also not very typical is that there doesn't seem to be any dark fog on this planet I do want to show you how to (t: 5180) actually battle the dark fog and there's a good chance that maybe some dark fog will arrive in this planet while I'm preparing to set up because (t: 5190) what we're going to do now is make ourselves a little battle station in order to conquer planets I like doing my design work in the sandbox mode just (t: 5200) because it's a lot easier and quicker to do but of course you can do it in your own game just in the live game as well now for the setup what we're looking for (t: 5210) is a small or at least not small but a small base that we can build on the ground and then we can build a not too large of a facility that we can initially set down on a planet that will be able to defend itself generate some power and basically fend off the (t: 5220) waves of dark fog that will be coming for us as soon as we start producing any sort of production or at least consuming any sort of power on that planet now in (t: 5230) order to do so you will see a very familiar setup over here with the battlefield analysis basis on the outside a line of turrets on the inner side and then the rocket launcher on the (t: 5240) most inner side now you might be wondering why are you using these basic turrets and not just things like implosion cannons and stuff like that (t: 5250) and honestly you could use those as well the problem is that you don't really need those you do need some extra defense in order to make sure that your missile turrets don't get overwhelmed but honestly the missile turrets will be (t: 5260) doing most of the heavy lifting so these are just there to pick off some of the stragglers now all you need to do is just copy the little starting facility that you had (t: 5270) make sure you get all the turrets in there and then paste it and then you can just copy and paste it around and make sure that you get a nice little circle like this with an open area in the (t: 5280) middle now the inner part of this facility is going to be used for power production you can do that in different ways in whatever way you want I like mixing up some solar energy along (t: 5290) with some wind turbines but you could also use a thermal power plants for example just make sure that you supply more than enough graphite or combustible units or whatever you use to actually (t: 5300) power and make sure you don't run out the reason i like using some solar power and wind turbines is that this is infinite energy and you will be able to power these indefinitely now the problem (t: 5310) with the solar and wind energy is that this is actually not a lot of energy to use in terms of the amount that you're going to need for your defense so there is a solution to that remember (t: 5320) these buildings the accumulators that you might actually have never built as a building because (t: 5330) you're mostly using these to transport power from one planet to the other so you're using them as things that you put on belt charge them up and then transport them and discharge them again (t: 5340) but you can actually build them as buildings as well now i have to admit i pretty much never did so before the last few updates but they have been buffed now and they actually allow you to store a (t: 5350) massive amount of energy and then discharge that and at a pretty high rate as well so what this allows you to do is kind of cheat on power you have the energy that you need to use the power (t: 5360) of these solar energies that will only produce solar panels sorry that only produce power during the day and then you also have some wind powers that will also be producing the entire day of (t: 5370) course and these turrets only need power when they're actually attacking something so as long as you're not under constant attack then these accumulators will actually be able to charge up (t: 5380) whenever you're not under attack and then use all of that power to supply the power to these these defenses when you need it (t: 5390) basically allowing you to work with a power supply that's a lot smaller than what you would typically need in order to power this full time. Now, all I need to do is add in some belts and make sure the belts go all the way around (t: 5400) so you have a nice little flowing stack of ammunition over here. I'm going to put one box over here for the missiles and then one box over here for the other ammunition, (t: 5410) and we're pretty much good to go. Now, in order to make this blueprint, what I actually recommend that you do is make the blueprint first as the total factory, or the total platform that you have over here, (t: 5420) then remove all the battlefield analysis bases and all the turrets, as well as the signal tower, and then make another blueprint of just the belts and the power supply, and you will see why in a moment. (t: 5430) Now, you might be wondering where am I going to get the silicon for all of those solar panels, and you actually don't need that much, but you should have been producing the silicon over here for a pretty long time, (t: 5440) because, again, you don't need that much glass, and you're also producing the silicon. So, what you can do now is actually remove these microcrystalline components for a moment. (t: 5450) That means that this silicon that's being produced over here has nowhere to go other than this box, (t: 5460) and then you can just pick up the silicon from this box and build yourself some of those solar panels. Of course, you can handcraft some of that as well. (t: 5470) You will also have some silicon sitting there in your battlefield analysis bases from the Dark Park, but all in all, that should give you plenty of silicon to work with. So, that means we're about ready to leave the planet. (t: 5480) Make sure that you grab plenty of turrets, plenty of belts, and plenty of ammunition. Other than that, you don't really need that much. Of course, you do need to bring some belts and analysis bases, (t: 5490) and at least one signal tower. You could probably build a couple of them on the way as well. As you can see, I'm having a couple in production still. What you also want to make sure that you bring is some more power production, just in case, (t: 5500) and I also recommend bringing some geothermal, just in case you're going to destroy some Dark Park, and, of course, whenever you... destroy one of their bases, you can place a geothermal power station on top of that to get a lot of free energy. (t: 5510) When you arrive on the other planet, make sure that you start building the initial, just the belts and the power production first, (t: 5520) and don't build the full blueprint just yet, because this will allow you to have everything in place before you actually start consuming any power. (t: 5530) These accumulators will store up the power, but it doesn't actually count as consumption, so you should not be under attack from any waves on this planet. Now, checking this planet, (t: 5540) as you can see, we do actually have a base here now, that popped up when I was preparing to leave to the other planet. I'm really happy that this popped up, because I really did not want to go to the planet with 16 Dark Fog bases on top of it, (t: 5550) in order to show you how to do combat, because again, combat itself is not really a problem in this game, but initially taking on 16 bases at the same time is a little tedious (t: 5560) in this stage of the game. It's actually not that hard later on, as I will show you in one of the next episodes, but for now, let's keep it simple. (t: 5570) Once you have the entire thing built, make sure you put the missiles as well as the ammunition on the belt as well, and this is where the extra power comes in, just because I know the enemy is going to come up from over here, (t: 5580) I might as well put some of these wind turbines in the back to supply myself with some extra power. Now, get yourself the second half of the blueprint, (t: 5590) make sure you flip it out the correct way around, like this, and it will not actually let you place it over it, it collides with other objects, duh, there's a lot of belts in the way. (t: 5600) However, if you align it perfectly with the already existing blueprint, what you can do is press Shift-Enter, and force place it on top of the one you already have. (t: 5610) Now, because there's all kinds of power over here, if you manually build some of the belt of the analysis bases, this will super quickly build the entire thing, and it will initially start consuming a lot of power as well, so you can see the threat ramping up here already, (t: 5620) especially if I add in one of the signal towers, which actually consumes quite a bit of power by itself, it will really jump up, (t: 5630) but that's not a problem because we are ready to defend ourselves, we have all the ammunition in place, we have the missile towers, and we have the signal tower to make sure we can take the fight to the enemy as well. (t: 5640) In order to take the fight to the enemy, what you need to do is basically just process outwards with the signal towers, (t: 5650) and I would recommend placing a belt of the analysis base right next to it, and as soon as you bring this into range of the enemy base, the missiles will start firing at it. (t: 5660) This is a fairly low level base because it was just created, but the same principle applies no matter the level of the base. Just build your way slightly forward, and as soon as you take out all the enemy units, you can place a geothermal on top of it, (t: 5670) the relay station will leave and fly back to the hive. You won't actually build a lot of threat with the hive, you could also destroy the relay, (t: 5680) but that really pisses the dark fog off, so I would not recommend that early on. Just claim it for yourself, and there we go. We have this... ... ...accumulating a huge amount of energy over here. (t: 5690) And that will help us power whatever we want to do on this planet. Which for the moment is not much, just put in some basic mining on one of the titanium veins. (t: 5700) I happen to have access to the silicon vein over here as well, it's the only one on the planet, so need to be a little bit careful about that, but 1.4 million is quite a bit of silicon to work with, (t: 5710) and just put them into a box over here so you can easily collect this. Make sure you... you keep the boxes somewhere close to your defense facility because you might have the relay station (t: 5720) returning at some point setting up another base so you want to make sure that you don't leave your planet defenseless you want to head back to your starting planet again with as much titanium as you (t: 5730) can there's a little trick because if your inventory is full you want to bring even more titanium than you can fit in your inventory you can control click the items in your depot and that (t: 5740) will put them under your cursor and basically just fly off using the thing that you have under your cursor fly all the way back to your starting planet and dump it in a box there it's kind of (t: 5750) cheating because this will allow you to pretty much carry about twice the normal inventory size (t: 5760) with you and you really need to be careful that you don't press the wrong button while you're doing this because otherwise you will be dumping all of this in space but it's a nice little um cheating trick you should you could call it in order to transfer (t: 5770) it more titanium from one planet to the other now what you want to do is put about 1500 of the titanium into a box that is connected to your yellow signs and then put the remainder in the (t: 5780) titanium alloy production up top because we're going to need a lot of that titanium alloy to get started that 1500 titanium should be enough to research both the titanium alloy as well as the (t: 5790) ils and after you completed these two yellow science technologies if you have any of the (t: 5800) red and blue science technologies left you can use the titanium alloy to create the titanium alloy and then you're ready to go and you're going to have a lot of titanium left in your inventory so you can get a lot of titanium just continue resourcing that we don't worry too much about the actual yellow science just yet if you haven't done so yet make sure you repair your processor facility and put in those assemblers (t: 5810) making the components again because we are going to need all the processes that we can get at this point at this point we're finally going to start working on an actual mall and as some of you (t: 5820) that are familiar with my previous work might already know i really like using this type of mall (t: 5830) where we're drawing in all the required resources with the logistic bots and then putting them into an assembler and then simply outputting them into another box with and again a logistic distributor on top of it so i can draw from that (t: 5840) wherever i am on the planet now in order to make the pls or the ils we are going to need to make a pls which requires titanium processors and steel as you can see that we have all those things now on (t: 5850) these belts and we're also going to need the particle containers in order to make the ils and (t: 5860) the pls is going to be the same as the titanium and the steel and then we're going to need to make the pls that are going to be split here there's a couple of them going to go into this box and the other half is going to go onwards into this assembler which will make the actual ils as (t: 5870) soon as we have access to that now just let this work for a little while because we're going to hop back to the other planet and bring some more titanium to this one in order to get the (t: 5880) additional titanium that you need for the pls i actually decided to expand production in the alloy facility a little bit so we actually added in the remaining smelters (t: 5890) That brings us up to a total of 12 smelters, which is really convenient because that's exactly what we can use in a single Mark II belt. (t: 5900) And that means we're producing way too much titanium over here. So this box will stack up pretty fast. So make sure you limit that. And then the remainder will, of course, be used for the actual alloy production. (t: 5910) And you will need to transport the alloy by hand to the actual PLS and ILS facilities. Obviously, we don't want to be doing that by hand for too long. But the reason I'm doing it by hand initially is to make sure I can spread them between the actual production of vessels, which I have a nice little facility over here. (t: 5920) We're producing the thrusters as well as the vessels. (t: 5930) This is not a particularly fast production, but this will do the trick. And we have the logistic distributors on top of here as well. (t: 5940) But initially, these are not actually doing anything because I'm still manually transporting everything from A to B. That is to make sure we also have some of the alloy going in here. Because now... We can actually start making the ILS. (t: 5950) So let me queue that up. And if you let the logistic distributors do their thing, you always get into a situation where they all fly off and supply to, for example, the vessels. (t: 5960) But you're not getting any ILSs or vice versa. And that's, of course, not ideal. Now, once you have your first ILS, make sure that you fly back to the second planet and grab yourself some vessels as well. (t: 5970) And just replace that little mining facility over here with the PLS version. And of course, you can draw in other resources as well. (t: 5980) But initially, all you need is the titanium and the silicon. You also need to keep an eye out on both planets in terms of new relay stations landing there. (t: 5990) Because they will make your life miserable if you let them develop too far. However, if you catch them early on, they will actually be beneficial. Because, again, you can place some more geothermal power stations on top of there. (t: 6000) And that will just make sure you have all the power you're ever going to need on your planets. Then, back on your starting point. So, let's go back to the beginning. Let's go back to the beginning. So, let's go back to the beginning. So, let's go back to the beginning. So, let's go back to the beginning. So, let's go back to the beginning. (t: 6010) Set down your second ILS and set it to request some titanium. And as you can see, we have it flying in now from the other planet. Initially, you might want to set the vessels on the supplying ILS only. (t: 6020) Because, as you can see, I have no vessels in here. But I'm still getting it in here. And that is because on the supplying planet, I actually put that in some vessels. (t: 6030) There's plenty of power there. So, there's no reason to draw power from this planet in order to have my vessels fly back and forth. And as you can see, look. Look, they're arriving. And making sure I now have fully automated my alloy production. (t: 6040) Which I can then pick up and put to use to make some more vessels. So, I can actually start transporting some more stuff in between planets. (t: 6050) As well as make some more ILSs. Now, hopefully this is obvious. But the third ILS that you place down should be, again, requesting some titanium. (t: 6060) And be connected to your titanium crystal production. Or I should be more specific to your yellow science production. Because that means that we now... Have completely automated blue, red and yellow science. (t: 6070) And we can pretty much complete about... Half or even two-thirds of the entire technology tree. Now, I just want to point out that in the meanwhile... (t: 6080) I have been building quite a few wind turbines. In order to make sure I have plenty of power. Because these ILSs will draw in quite a bit of juice. (t: 6090) If you're not careful. And if you draw too much, then you won't be able to defend yourself against the dark fog anymore. So, be careful about that. And also... Make sure of course that you start utilizing the ILSs wherever you get a new one. (t: 6100) In order to start automating everything else in your base. Specifically start out with requesting some of that silicon from the other planet as well. (t: 6110) So you can fully automate your processor production. Again, because we're going to need a lot of those. Specifically, we're going to use a lot of those into the production of both the distributors. (t: 6120) As well as the bots to actually do the transporting between our facilities. Now, this is the pivotal. The pivotal moment of your playthrough. (t: 6130) Because we're going to go from all of our hand crafting stuff. And manually dragging stuff between facilities. To completely automating everything on our starting planet. (t: 6140) And in fact everything in our entire system. However, while you're doing this. Start researching towards the purple science already. (t: 6150) And everything you need leading up to that. Also, make sure you unlock the high strength glass. The Casimir crystal. The particle collider. And the strange matter. Because all of those things are going to be needed. (t: 6160) In order to make the stuff that you're going to need for the purple science later on. Those are all things that you should be grabbing. You can pretty much ignore the bottom half of the combat tree. (t: 6170) Most of this stuff isn't really necessary to defend yourself. So just pick up anything that seems useful over here. And of course feel free to pick up some more upgrades in between as well. Inventory capacity is always welcome. (t: 6180) Mechanical frame in order to move around faster. There's a lot of useful upgrades that you can pick up from this point. Where you're going to need some yellow science in order to do so. (t: 6190) Not in the least the mass construction level 4. In order to unlock even larger blueprints. The first step towards that automation. Is putting a logistics distributor on every single one of these boxes. (t: 6200) That you have been putting up. You're probably not going to have bots enough to supply all of these things just yet. But of course you will be mass producing these bots from this point on. (t: 6210) And as you can see I have the first ones flying around already. Just trying to save myself. As much manual labor as I can. Now initially the range of these bots is pretty small. (t: 6220) So you might not actually be able to reach all of your production facilities. From where you're building your mall. However just started working on some of the upgrades. You should have I think up to level 3 now. (t: 6230) And that should be enough to pretty much cover about half of the planet. Which is about the size of our factory in total. Now if you want to make your life a little easier. (t: 6240) When it comes to developing an analysis base. What you can actually do is put a large depot on top. And then put a logistics distributor on top of that. As you can see it becomes quite a bit of a building. (t: 6250) But what this allows you to do is set a recipe in your logistics distributor. For example for the plasma exciter that we're not actually making anywhere just yet. (t: 6260) So you do need them to actually make the distributors themselves. So I am just hand crafting a couple of them. They're pretty cheap to make. So it doesn't take too long. Assuming you don't make everything from scratch. (t: 6270) But you actually pick up the base requirements for that. But this will allow you to. Take all the useful stuff out. You can always just switch it to something else. (t: 6280) Say I don't know processors or something like that. And then just copy paste that to all the other bases that we have over here. And then hopefully as you can see we have a couple of drones flying off. (t: 6290) Because apparently there were some processors in there. And that allows us to actually make use of that without having to sift through the entire inventory. (t: 6300) However now we have the ILS in main production. Now the real fun starts. Because we're going to replace every single one of the incoming belts to the buildings that we've already built. (t: 6310) And put an ILS in front of that. And then have those ILS request those resources. And then output them on a belt like so. And you can see this looks a lot more tidy and organized. (t: 6320) And if you have some sort of mining operation very close by. You can just put them in here. And then just have it export to wherever it needs to go. (t: 6330) And of course as long as you put some drones in here. The drones will carry it from one ILS to the other. As long as you set the local demands and supply correctly. (t: 6340) And this will make your entire base look a lot more tidy. But on top of that it will actually allow you to replace some of those failing mining operations. (t: 6350) That you might have going on from the start. For example all the way here in our starting base as you can see. The initial iron vein that we started to use is almost completely dry. The same holds for the copper vein that we initially started working out. (t: 6360) You can see the belts are not entirely filled. So by just removing this and replacing this with an ILS. Not only do we clean things up and we remove a lot of these belts going all over the place. (t: 6370) But we actually make sure that we have a setup that we don't need to replicate anywhere else. That is still working at full optimal efficiency. (t: 6380) And that can draw resources from anywhere in the galaxy. Now when I say galaxy. I currently only mean our starting planet and the second planet. (t: 6390) That we colonized. So you might as well make sure you make full use of all the resources we have on this planet. And this is where the PLS that I siphoned off comes in. (t: 6400) Because you can actually set up your mining operations now to feed into one of the PLS's. And if you have any unused nodes nearby. For example like the one I have over here. (t: 6410) What you can just do is place some miners. Miners. There we go. You can place some miners over here as well. Just draw those in and then connect a belt to that. (t: 6420) And put that into that ILS. And then we have yet another resource available to us. Of course you do need to make sure you set this to actually supply. (t: 6430) You need to put in some drones. So make sure you are producing some of those. It takes a little while for that to get going. But other than that this is a very fast and efficient way to make sure you are able to make use of all the resources you have on your entire planet. (t: 6440) Now I do have to warn you that this bot system is not completely ideal in the end. Because sometimes it will draw too much on a particular resource. (t: 6450) So for example over here with the processors. We are producing 1.5 per second. But I actually took off the logistics distributor. Because we have so many places where we are actually using these processors. Because we need them for drones. (t: 6460) We need them for the PLS. We need them for the actual vessels. And we need them for the shuttles as well. So that means that if I let the drones do their own thing. They are going to be able to do their own thing. (t: 6470) And I am going to have to make sure that I have the right amount of resources. So that is the first step. And then the second step is to make sure that I have the right amount of resources. So that is the third step. So that is the fourth step. (t: 6480) And then the last step is to make sure that I have the right amount of resources. So that is the fifth step. And then the last step is to make sure that the drones do their own thing. They are probably only going to supply them to one of those sources. And by making sure I take this out. (t: 6490) I do have to do it manually. But I can make sure that I spread them out exactly in the way that I need to prioritize. At that specific point in time. Now you can put down the head on top of here. And again once you have everything sorted out. (t: 6500) And then you can just let it do its thing. Just make sure that if you do that in your mall. You set some limits. You can set some ILSs to be made. Because you are never going to need so many at the same time. (t: 6510) At least not in this stage of the game. So when you check these boxes. You set the limits to something that is a little bit more reasonable. So for example 150 vessels. Is probably going to be plenty at this point in time. (t: 6520) Similarly one or two stacks of ILSs as well. Similarly one or two stacks of ILSs as well. And as soon as you have the basics that you need. Just have it auto produce. And then once you come back. (t: 6530) You should find that a couple of these have completely filled up. There is of course also going to be a limit set. There is of course also going to be a limit set. On the actual processors that it is going to request. And then everything else will kind of balance itself out in the meanwhile. (t: 6540) And then everything else will kind of balance itself out in the meanwhile. Also remember that we have a couple of these storage tanks. Also remember that we have a couple of these storage tanks. That we were not using for anything. That we were kind of using to siphon off. (t: 6550) For example the hydrogen in our alloy build. As you can see this is starting to fill up quite nicely. As you can see this is starting to fill up quite nicely. But it is very easy now to siphon this off. Just by picking this belt. (t: 6560) Just by picking this belt. Reversing the path. So this will actually drain these storage units. So this will actually drain these storage units. I am going to connect some more storage units on the other side. (t: 6570) So I can just put these in and connect these to the ILS. Which is also set to supply the hydrogen. To where ever it needs to go. And that will help us making sure we have the hydrogen flowing. And that will help us making sure we have the hydrogen flowing. (t: 6580) And we can now draw on this where ever we need. And we can now draw on this where ever we need. And this should never stall again. And there you have it. A very smooth transition to the mid game. (t: 6590) We have ILS's in place. In every single one of the builds that we already built. So we don't have to plan anything else. We actually have to remove anything. (t: 6600) Our initial kind of spaghetti-ish looks of all the mining belts going everywhere have been completely removed. We're utilizing all the resources on our starting planet at least. (t: 6610) We can now easily do so. I'm still in the process of making a couple of more of these PLS up and running. But all in all, it's looking very organized, very tidy. And more importantly, it's also very efficient because we didn't have to replicate anything. (t: 6620) We didn't have to redo anything. We're still on our starting planet. We basically only built a mining station on the second planet. (t: 6630) And yet we are already well into the production of yellow signs, as you can see. So all in all, I would say this is a pretty efficient way to approach the game. (t: 6640) And a very compact way of building your factory as well. We have the first starting of an actual mall over here, which we are going to expand on in the next episode. (t: 6650) Because of course, at this point in the game, we're going to drastically scale things up. And we do need that mall in place. So don't worry, it's coming. For now, I hope you enjoyed this pretty long episode. (t: 6660) But I did feel, like I said at the beginning, that everything kind of went together. And I didn't want to split it up because it would make a very unnatural kind of arc in this particular episode. (t: 6670) And all in all, this should take you at least maybe two evenings, maybe even three evenings to do all of this. (t: 6680) Depending on your play speed and whether or not you're using my blueprints. Which, of course, are going to be upended. Loaded to DysonSphereBlueprints.com as well. I hope you enjoyed this one. If you're still here, you are awesome. (t: 6690) And I do hope to catch you in the next one. (t: 6700) Hey, hey, it's DDA. And welcome back to this Dyson Sphere Masterclass. (t: 6710) Last time around, we built some yellow signs. And now it's really tempting. To use these ILSs and start scaling up to huge city blocks of production. (t: 6720) But if you do that, I can promise you, you're going to run into a lot of bottlenecks. So what we're going to do today is actually kind of consolidate what we already have. (t: 6730) And most importantly, we're going to set up an actual mall. Which, this is not really much of a mall. We're going to expand on this a lot. (t: 6740) And in order to do that, we actually also need to add in quite a few new resources. That we're not going to use. Because we're not automated in terms of production just yet. And if we've done all of that, then we're ready to scale up to the end game. (t: 6750) We're also going to deal with our defenses a little bit. But just settle in and enjoy the ride, I would say. So we've reached the point where we definitely want to start scaling things up at least a little bit. (t: 6760) And that means it's now time to really start expanding our mall. I've been calling this a mall. But of course, this isn't a mall of any decent size. (t: 6770) Because we're only producing a couple of things. So let's go. And fix that. And that is where this blueprint comes in. This is something that a lot of you will be familiar with if you watched any of my previous playthroughs. (t: 6780) But it's actually a streamlined version of what I've been showing you before. So what are we doing over here? Let's first check. Is this thing actually working? (t: 6790) Yes, it is. Okay. What are we doing over here? We're producing all the early game buildings in a single build. That's a lot more efficient than making a build like this for every single building. (t: 6800) Because most of these buildings are actually all using the same. Type of materials. So over here in the start, what I'm doing, I'm actually bringing in the basic resources like iron, concrete, coils, circuit boards and gears. (t: 6810) And in the bottom, I'm doing the same thing. But then for the steel as well as the glass. And with those five, six materials, you can actually make pretty much everything from the early game. (t: 6820) So we're making miners. We're making smelters, assemblers, power towers over here. We're going to be making once the assembling machines show up. (t: 6830) There we go. We're making some of the logistics. Related things like the splitters and the boxes. We're making pumps. Now, in order to make pumps, we actually need to make the gears or the engines as well. (t: 6840) These are not engines. These are motors. Sorry, motors. We're making the motors as well. But you can make those from the same materials that we have in the belt over here. (t: 6850) We're also actually making turrets. Not entirely sure if you still need them at this point. I've included the traffic monitor. I rarely ever use them, but a lot of you do use it. So it's in the blueprint. (t: 6860) And then on the bottom row here, we have everything related to oil. Mostly and the science. We're going to need some of these plasma exciters for that. But we can actually make them from glass and the magnetic coils. (t: 6870) So we're doing that in the build itself as well over here. So we can make sure we always have some refineries and extractors coming in. (t: 6880) New in the build are actually the large storage units because you can use them for putting them on top of the battlefield analysis base. (t: 6890) And then what I actually took out is the sorters, the belts and the foundations because we're making all of that in the starting build that we made a couple of episodes ago over here. (t: 6900) So we no longer need to make them in this basic build. This will take a little moment to get started. Make sure you grab some of these bots that we've been producing. (t: 6910) Make sure that you keep spreading your processes between the different builds so you get a little bit of everything rather than a lot of one thing and then all you need to do is make sure that there's (t: 6920) actually logistics bots in here it seems most of them. Have been filled. Not all of them. Make sure you press this arrow when you make a blueprint for yourself so it will automatically populate the buildings with the bots if you have them in your inventory. (t: 6930) As you can see, most of them already had it. So there's a lot of bots flying in now putting the stuff on the belt and then (t: 6940) slowly we will start producing everything now again. Make sure you set some limits on your depots because you don't need a thousand of these smelters just now. Just a couple of stacks might even be enough so you can even limit this even further depending on what you want. (t: 6950) The problem with a build like this is always that the first few assemblers that require, let's say, circuit boards will draw (t: 6960) them off the belt so anything that needs circuit boards later on won't actually get it until you have enough supply or these earlier assemblers are completely done. Just leave it do its thing. (t: 6970) You should have enough resources to find a little bit of everything in your boxes pretty soon for now. As you can see, we already have a couple of things showing up like Power Poles and things like that. (t: 6980) You can just pick them up or request them from your inventory now. So if I say, okay, I want at least 200 Power Poles now, the boss will start flying in and delivering them to my inventory, so super handy. (t: 6990) So as our science is slowly ticking away and we are progressing through the research tree and we have the mall over there doing (t: 7000) its thing, I actually I'm trying to figure out where I'm going to be building next. There is a lot of resources on the right hand side of my base and I want to keep things as neat as possible just because I (t: 7010) like the looks of a neat base. But this is really inconvenient because we actually do want to use most of these resources. (t: 7020) We have a lot of open space over here that is actually other than the fact that we need to clear out some of that water is very convenient to build in if it wasn't for the fact that that's exactly on the round for the dark box. (t: 7030) So what I actually want to do is move my defensive line a little closer to these bases so we can actually utilize this space and we might actually be able (t: 7040) to grab the oil as well. In that case, we might actually be able to grab the oil as well. In that case, we might actually be able to grab the oil as well. In that case, we might actually be able to grab the oil as well. In that case, we might actually be able to grab the oil as well. In that case, we might actually be able to grab the oil as well. In that case, we might actually be able to grab the oil as well. Okay, so step one clear out the water. This is a nice little desert now and then just relocate your defensive line (t: 7050) to a different position by simply going to the control C, selecting the defensive line that you already have. (t: 7060) Don't fix what isn't broken and control V and just place it wherever you want to go. It's a very easy way to just relocate an entire setup. Now, of course, we are going to need some power. (t: 7070) So what I'm thinking is maybe just use these turbines as a power line might as well get some extra power out of that. And as soon as this power charges up and is connected to the rest of my builds, (t: 7080) then this will actually allow the Battlefield Analysis Base to speed up the actual production line. Now, as you can see, I didn't actually have enough turrets, (t: 7090) so I can either grab them from my mall now or I can just delete the defensive line already have. I actually like to leave these things intact for a little while because there's (t: 7100) a lot of resources in there and then just build them. Now, you do need to make sure you're done building this before you get the next wave. So let me focus on that for a moment. Now, I've actually gotten a couple of questions of people saying, (t: 7110) how are you still alive with this defensive line? It's not enough. Well, let me show you. This is a 180 unit wave. So the maximum wave you can get. As you can see, some of the signal towers are under heavy attack, but it's no problem. (t: 7120) And then actually, it's a lot harder to defend yourself as you get closer to the enemy base because the enemies are less spread out. (t: 7130) If you're on the other side of the planet, they will kind of come in a long line usually, so it's easier to defend. I did do one thing, though. I actually increased the number of rocket launchers a little bit. (t: 7140) Missile turrets, I should say. Not by that much. I think I increased the number by like six or seven, eight, something like that. (t: 7150) But it's a slightly larger number. But that's the only thing that I did in terms of improving my defense. The most important thing with your defense is making sure you have enough power. If you don't have power working at 100% efficiency, you can add as many turrets. (t: 7160) as you want, but they're not actually going to be adding any efficiency because, well, a turret firing at 80% only does 80% of the damage that a turret (t: 7170) functioning at 100% would do. Same line of reasoning applies to any of your factories. And there's no point in expanding your factory if you don't have the power to actually make sure it's functioning. (t: 7180) Now, look at all that nice building space we have cleared out for ourselves over here, and I'm going to put a lot of that to use in this episode, specifically (t: 7190) something that I've been mentioning in the last few episodes is that we're not actually making plasma exciters, and we're actually also not making a couple of other things that we need (t: 7200) for the building. So if you check out what we can produce at the moment, we've been using geothermal, those actually need photo combiners. We're not making those. We also need the super magnetic rings, which we're not producing. (t: 7210) We are also lacking some silicon based things like the crystal silicon that we need for the accumulators. (t: 7220) What else are we missing? We are not. We're not really producing too much silicon in general, so that's probably a point of attention, and we need the plasma exciter for a lot of things like, for example, (t: 7230) also the laser turrets, so photon combiners, silicon based stuff, plasma exciters, and then last but not least, but that's probably a separate (t: 7240) build, we also need the super magnetic rings. So it's building time, and I'm going to be aiming at producing two photon combiners and two plasma exciters per second. (t: 7250) That's probably. More than we need, but it turns out that that aligns really nicely with the rest of the math. So might as well overbuild slightly and put that to use later as well. (t: 7260) As you can see, we need six of the Mark two assemblers in order to make that. I will be switching to Mark two assemblers from now on exclusively because we don't want (t: 7270) to be using the old stuff anymore, and of course, we're still working with Mark two builds. Now, in order to make photon combiners, we're going to need prisms and circuit boards. (t: 7280) And in order to make plasma exciters, we actually also need the prisms, but we also need the magnetic coils. So there you see all the stuff we need on our belts elsewhere. (t: 7290) So let's add in the actual prisms. Now, we actually need eight of these assemblers making prisms. So what I like doing is bringing in the glass from somewhere and then making two (t: 7300) lines of assemblers, making that, and then we have a single outgoing belt that will be drawn from both sides and that goes down here. Now, remember, the plasma exciters also need the prisms. (t: 7310) And this is an exact distance of three away. So I should be able to connect a sorter like this. You do need to do this with Mark three (t: 7320) sorters, otherwise the speed is going to be too low because of the large distance that we're transporting stuff, but it will work just fine like it is. Okay, so that means we have glass, circuit boards and coils to worry about. (t: 7330) And we need a lot of glass. Actually, we need a total of 24 smelters making glass because of the prisms. They require a whole lot of glass, as you can see. (t: 7340) We actually need 1.5 prisms, sorry, glass per second per assembler. So that's a whole lot of glass that we need to make. (t: 7350) So there's two incoming belts with stone over here. And then we have two outgoing belts like this and 24 smelters making the glass. (t: 7360) Now, the nice thing about having 24 smelters is because each of these smelters takes one stone per second, it's actually a total of two full belts of incoming stone. (t: 7370) So that's a really nice thing. We're fully utilizing the belts and we're just fully utilizing all of these smelters over here as well. Then next on the list are the coils and the circuit boards. (t: 7380) We actually don't need that many circuit boards. Only a single assembler will do, but we do need four of them making coils. That means we're going to need to bring in magnets from somewhere. (t: 7390) We're also going to need to bring in some copper that is going to be used by both the circuit boards as well as the coils. And of course, we have the outgoing belt for the coils over here. So nothing too much new here. (t: 7400) Okay. What I also like about all of these builds that we've been making so far is that this clearly shows you that you don't need to rush to Mark III belts. There is not really any point to Mark III belts until you actually exceed (t: 7410) the input capacity of a Mark II belt, and that only happens when you really start scaling things up. So feel free to keep using the Mark II builds for a very long time. (t: 7420) And as number six was the magic number in one of the previous episodes, the number 12 is going to be the magic number this time around, because as you can see, (t: 7430) we need exactly 12. 12 smelters making magnets, and they fit in very nicely with the ones we already have. So there it is. Now we still need to bring in the iron and the copper. (t: 7440) And I love it when a plan comes together, because as you can see, this very nicely fits into the rest of the setup, almost making it entirely square. (t: 7450) We do have a little bit of open space here that you could use to put in an ILS, for example, and just export this if you're building this on a different planet for some reason. But as you can see, we need a total of (t: 7460) five copper smelters and two iron smelters, and they fit nicely into the open space we have left on the left hand side of our assembler rows. We have an input belt or two coming in, and we have an output belt as well. (t: 7470) We have the iron on the top here, so the one with the copper can go extended like this, and it's all nicely flowing through our entire build. (t: 7480) Now, I'm actually moving away from using the intermediary boxes everywhere to catch up in case this ever backs up because, well, otherwise, (t: 7490) this build just doesn't look as good. That's the main reason, honestly. If you want, you can just move everything a little bit to the side and add a couple of boxes in here to catch any overflow materials that you might want to be using elsewhere in your builds. (t: 7500) Remember, the point of these boxes is mostly to be used by your mall, (t: 7510) and I don't think that anything that we're producing in here is really in demand so much that we need to focus on that. (t: 7520) Additionally, we're going to be adding a lot of silicon to our build, so that's the reason I'm not putting in the boxes as well. There's no point in adding boxes for stuff that you don't need, but there's definitely a reason to put in boxes for stuff that you do need, (t: 7530) and we do need a lot of silicon. Now, technically, you could make a separate build for silicon because, well, it's not related to anything else we're doing in here for the Plasma Exciters and the photo combiners, (t: 7540) but, well, it makes sense to kind of all build this together because we're focusing on upgrading our mall. So that means we're going to add in, yes, you guessed it, (t: 7550) 12 smelters making silicon, and then we're going to add a couple of them making crystal silicon. Skipping ahead a little bit to the completed build where I added in all the power towers, (t: 7560) all the swords and things like that, as well as an ILS to request all the materials that we need, which is the four of these. As you can see, I added in four smelters making crystal silicon. (t: 7570) That's plenty. That's actually, again, going to be producing exactly two per second. So that means we have two photo combiners per second, two Plasma Exciters per second, (t: 7580) two Crystal Silicon per second, and I think we have a leftover of four silicon per second. That is actually going to go in this box because we do need that in our mall. (t: 7590) And that's all there is to it. Now, the reason I'm requesting four resources and I'm leaving open this extra space is actually really convenient as well, because this will allow us to put in the War Purse later on so we can start requesting these items from anywhere in the entire galaxy. (t: 7600) So that's just very convenient. For now, we're doing most of the work through the logistics drones that I've been slowly spreading across my existing ILSs as well as this one. (t: 7610) As you can see, I probably want to add in the rest of the 100 we can fit in here because right now we're not adding in enough resources. (t: 7620) And I also need to add in a couple of vessels to make sure we're actually getting the silicon from the other planet. (t: 7630) Now, assuming that you have your drones and your vessels in place and you've let this running for a while, you should have plenty of processes at this point. And at that point, you might as well just start automating the distribution of the processors. (t: 7640) Just grab a couple of thousands of processes that you've produced in the meanwhile, put them in the things where you really need them to go, (t: 7650) and then let things take care of themselves for a long while. This will just make sure you have a bit of a stockpile of everything. And at that point, you can just let the AI figure out what is in demand the most and then fill up the stockpiles of that by itself. (t: 7660) And honestly, if you don't do that, don't use blueprints nonstop, (t: 7670) because if you just plop down all of these blueprints and you're done building everything in a few seconds, your production will need a little while to catch up. So that's the downside of using blueprints. (t: 7680) Sometimes you can kind of go too fast, but that's where manual reallocation comes in if you need it still. But if you are playing and building things by yourself, (t: 7690) you should have plenty of time to let things take care of themselves now. Now we have a lot of new items in production. It's time to start automating our mall a little further. So I want to start making the wireless power towers, (t: 7700) which aren't useful by themselves. I was splitting them off similar to what we've done with the ILS previously. But we also need those in order to make signal towers, (t: 7710) which we can now because we have Crystal Silicon in production. It will take a moment for the drones to collect everything, but that should take care of itself now. On top of that, we also added in a couple of other defensive things like missile turrets and (t: 7720) BFAL bases. So whatever we need in order to resupply our defensive line over here, as you can see, my signal towers are starting a little bit. (t: 7730) We can now just completely automate that and have those replaced. So something I've noticed now is that we're actually not bringing in enough steel. We don't need that much steel, (t: 7740) but we do need more steel than we're currently producing because we are using that for the drone production over there. We're using it for the ILS production. We're using it in the base production. (t: 7750) And the only place where we're actually producing the steel now is in these three smelters in our starting build, which is not really going to cut it. Since we're making the mole expansion miscellaneous type of build here anyway, (t: 7760) we might as well add in another line of smelters to make some more iron and steel. Tada! It looks super nice and tidy over here. (t: 7770) We're producing about one steel per second now, which is not super much, but it's going to be. It doesn't sound like much, but it is actually more than sufficient to supply our need. (t: 7780) And we're actually also producing a little bit more iron than we need, so we have a little bit of strength. We have a lot of surplus going into the box over there as well. (t: 7790) Now, don't worry, I've added this into the blueprint, so if you're downloading the blueprint and you want to use it by yourself, then it will be the complete build that you see over here. (t: 7800) So far, we've been kind of just automating stuff that we had already been doing manually, but now we're trying to really start pushing the progress again. So I have this rather large build to make solar panels. (t: 7810) Now, you might be wondering why so many? Well, mostly because at some point, you might want to utilize the solar panels, but now we're going to start using them. So, in fact, this is a very efficient way of making power, (t: 7820) infinite power on your planets. In order to do that, you're going to need a lot of solar panels. So this is why I have six of them making solar panels over here. (t: 7830) There's actually some room left over here in case you want to proliferate these as well. That won't actually increase the amount that you produce, but it will increase the speed at which you produce them. So you can really churn out a lot of solar panels in a very short time, (t: 7840) assuming you have enough resources, but that's fine. Now, this is very, very easy. It's very, very easy. enough resources however probably more interesting is the addition of the laser turrets over here (t: 7850) with a small build that again includes the plasma exciters and the photon combiners so that's why we wanted to use them as well as the steel that we just added so more steel yes um because we want (t: 7860) to start switching away from using ammo based defenses to more infinite defenses like lasers (t: 7870) because they only require energy so the basic idea is very simple i'm going to take out these turrets replace them with laser turrets however if you do that make sure you don't do it just before a wave and also make sure that you have plenty of excess power to support those laser (t: 7880) turrets because again otherwise they're not going to work because if i would replace them right now well things are probably not going to work out like i want them to okay power and laser turrets (t: 7890) are in place let's find out if this works i think it should uh this extractor by the way is taken (t: 7900) out but this should be fine as long as i'm taking out the other units quickly and ultimately i'm going to be able to use them as well so let's see what happens next so let's see what happens next so let's see what happens next so let's see what happens next they tend to leave alone your production facilities as long as you have turrets and signal towers in the way well that wave went out really really fast it didn't it barely damaged (t: 7910) the signal tower so this is where you can really see the power of laser turrets over the the earlier version of the turrets because i know i'm going to get questions if i don't (t: 7920) answer this right away i actually added in a line of normal turrets in the back mostly because i had them uh we're producing the ammo anyway and it looks kind of cool (t: 7930) but this you really don't have to do this this does serve some function though because sometimes (t: 7940) for some reason there's a couple of those flying units that will just kind of fly over and aim for something in the back there's no turrets here whatsoever so this is just to kind of catch those (t: 7950) stray units in case they try to fly over it doesn't happen that often i'm not entirely sure what triggers it either i think it's actually the the fact that the turrets are all the way in the (t: 7960) back they sometimes tend to aggro some of the units that are still at the base and they will just sort of fly over and aim for something in the back so this is just to kind of catch those zoom over and if enough of them get through uh they can cause a bit of damage in your base so (t: 7970) this is just to have a bit of an additional defensive line and because it looks fun but you don't really need to do this the turrets by themselves the laser turrets i should say along with the missile turrets should be able to take out pretty much anything the enemy throws at (t: 7980) you now in this stage of the game and really at any stage of the game but this stage in particular you do need to really watch your imminent outputs because there's no reason no point in expanding if (t: 7990) you're not bringing the turrets in the back you can't really see them in the back so you can't really see them in the back so you can't really see them in the back so you can't really see them bringing in enough raw resources for example over here you can see i'm really struggling in bringing enough stone here so that means that i'm just going to add in a couple of plss on this (t: 8000) planet or maybe an ils on the other planet that actually brings in stone and has some more vessels and drones you can see i completely filled them up now but still my stone is struggling (t: 8010) there's no point in expanding until i have the production facilities that i already have in place completely optimal in terms of their functioning you also find that you're probably going to be (t: 8020) spending some time in flying to the other planet again to crush any popping up bases that i just (t: 8030) did for example over here there was just another relay station forming a new base you get notified of impending attacks on different planets while you're not on them so that's really convenient however you will only notice this when the attack is already nearly coming in typically that's not a (t: 8040) problem the first few waves are very very small um so so assuming you have a couple of these signal (t: 8050) towers anywhere near your uh your actual facilities that you're going to be able to use and you're going to be able to use a few of them if that's the case they will be able to take care of itself if they land anywhere close to an existing signal tower they will automatically start attacking it (t: 8060) anyway but that might not always be the case so just keep an eye on that the only issue with this that i have is that the two planets that i have tend to be on opposite sides of our star system (t: 8070) which is really inconvenient but yeah that's just the way it is there's no way around that one thing that you might want to do when you visit another planet and take out a couple of bases there (t: 8080) set up in a few mining stations i like just going there and every time i go back to a different planet set up at least one additional mining stations it feels a little bit less like a chore (t: 8090) you don't have to put down five or ten or that of them all at the same time and you're still expanding your resource inputs a little bit further in this case um just bring in a lot of (t: 8100) extra iron and titanium those are the things that you need the most along with silicon but honestly just just pick up whatever you find close and just bring it in because there's no such thing (t: 8110) as having too much resources something to watch out for when you're placing your defenses closer to the enemy base is that sometimes you might get these non-stop attacks that i have right now so (t: 8120) you can see there's these units coming in and then there's new units coming in straight away these are not actually part of the waves that you can see showing up right now (t: 8130) as a show of a proof that these defense are actually capable of handling just anything those were 360 units flying at my base at the same time no problem um (t: 8140) um but what i was trying to say is if you get these non-stop attacks that's actually a problem because this will actually drive up the threat of the dark fog hive a lot which i don't want it's an easy way (t: 8150) to farm a lot of materials from the dark fog but that's not really what we're trying to do here so what i'm actually going to do um that was a signal tower taking out really quickly there uh what i'm (t: 8160) actually going to do is i'm going to do some housekeeping and what i mean by housekeeping is basically blasting the dark fog of the planet where i don't want them to be and that's where (t: 8170) blueprint comes in it's really simple it's just an ios requesting not those we actually want to request the missiles um and then putting those missiles in a lot of rocket launchers this is a (t: 8180) temporary build this is not going to be here forever but basically what i want to do is take out this base over here and with those extra missile turrets it becomes really easy to clear (t: 8190) out any base that you want now i'm actually going to leave in the other two bases because i don't mind getting attacked by the dark park i just don't want interplanetary attacks until i'm (t: 8200) willing to deal with them have been ready to deal with them for a very long time already it's just not what i was aiming to do at the point at that time but you could also just decide to clear out the entire basis of the dark (t: 8210) fog altogether and not have to deal with them anymore other than the fact that you will get some new attacks coming in probably it's completely up to you make sure that you only leave up bases (t: 8220) that you actually are willing and prepared to deal with there's nothing wrong with just taking out the dark fog altogether the main reason i leave them intact is because it's a really easy way to farm (t: 8230) some sort of attack on the dark fog and i'm not going to do that because i don't want to do that because i don't want to do that because i don't want to do that because i don't want to do that because do something you want to do but if you don't let dark fog come down from the ground in a way you're gonna hear it I promise you that wall would be keep down but only if it wouldn't get as much Style attack on then the dark fog would go away we didn't finish the stage but i think (t: 8240) that's not a bad turning point do have an item find to get to and next phase i might turn the dark fog on and look at some chard drops and not just use it as 800 buy some super mana rungs and get that 1 item that's useful at 3ak potion and just, and beat that thus far before we start hunting hidden listing now if you see any (t: 8250) kind of monsters on the main map just change it so that those in the channels inside the chard drops don't so just replace a god type item for the chard that isoon Zoe and then (t: 8260) awesome by the way again we need the super magnetic rings we also need those for a couple of other things for example implosion cannons if you want to use those and let's see we also need (t: 8270) them for the planetary shield generator which is quite important once we actually start to expand into space the enemy will not like that and will get attacked from outer space so making sure we (t: 8280) have enough of these generators going is probably really important there's a couple of ways you could actually go about this the the quick and dirty way would be to just copy the turbine build (t: 8290) that we already have and then just add the missing components to that because if you look at the recipe you need turbines you need magnets and you need graphite and magnets and graphite of course (t: 8300) are extremely simple to make so that's a very easy and quick way to make the turbines and well there's nothing wrong with that two turbines per second is what that build produces and that means (t: 8310) you're going to get one magnetic ring per second out of that quick and easy however what i want to do is make a nice clean build that takes advantage of the new mark ii assemblers that we have (t: 8320) and that's what we're going to do so we're going to make a nice clean build that takes advantage of the new mark ii assemblers that we have and as it turns out that's a really nice clean build exactly like i wanted to so that's what i'm going to do i'm going to go through this build a little quicker than in the previous one because a lot of this we already did but we're starting out again (t: 8330) with the 12 smelters making um iron and then we need four assemblers making the gears now just to remember we're switching from the mark one to the mark two which means that for every four assemblers (t: 8340) that we had before you could pretty much take one out and that's the only math you need to do when (t: 8350) you start he puts three on the grid and then you take a fossee mine get a couple of three make a foundation for your others ooh that's gonna be really nice for the final turns but you're (t: 8360) going to get three relativ discrimination s Deus Lord thanks for that that's going to use a little bit harder now but but it looks pretty straightforward essentially change that (t: 8370) but how to make the best hai as soon as you do some basic steps start with a respecto in the circuit we start with the structure you can change everything all right you're just going to start with the treaty but then we are going to skip out the vegetation and just search exactamente (t: 8380) for whatever we want the ground easier and if there isn't an edge or an oxide or what we do doesn't matter now we start with the final jar box we're working with we really мира we need two months each time so we need four machines and energy we need four meshes and then we need nature motor production assemblers. You can see we only need eight of those which is nicely in terms of symmetry with the eight that we have on top here. So we have a total of 16 assemblers now (t: 8390) making stuff. The belt for the coils is going around here all the way around so we can draw in from both sides and then the other two inputs are going to go in the middle. Remember you never (t: 8400) want to have three inputs in between buildings where you need all three of them because you won't be able to connect your sorters to all of them so that's a problem. And then we have the (t: 8410) outgoing belt. We have an outgoing belt on top here that's going to tie in with the outgoing belt on the bottom and then we have everything going into the turbines over here and then the (t: 8420) turbines go out here. Now I just noticed that this belt is the wrong way around. If you hadn't noticed this because this is a not super new addition but it's relatively new if you haven't (t: 8430) played for a while, you can now just flip your belts with one push of a button. So that's really convenient if you build it the wrong way around like I just did. Then I only need to add (t: 8440) in the two inputs. So I'm going to add in the two inputs and then I'm going to add in the two inputs. Three assemblers for the actual magnetic rings and remember you do need to do the math. And are you guys ready for the cleanest build ever? Well I am. Let's do it. Ta-da! We added in (t: 8450) exactly the eight smelters for the magnets that we need for the rings and then we have two more (t: 8460) that we need for the graphite. There's only two and these are perfectly aligned with the remaining room that we have for the smelters in between here. And look at how nice and square this build is. (t: 8470) I don't know about you, I just love it when a build is exactly square or rectangular, whatever you want to call it. That just makes it so easy to fit it in with anything else and (t: 8480) there's no wasted space or anything like that. At least no significant wasted space in any way. (t: 8490) The belts are all nice and clean. Yeah, as you can notice I'm really liking this build. And this is what it looks like when it's humming along. I don't know, (t: 8500) I don't know about you guys, it's just awesome. Now don't forget to remove the battle stations because well that's not really needed at this point anymore. And if you want to make things (t: 8510) look clean I would recommend that you align your ILSs exactly with each other so they're always exactly at the same distance from each other in terms of the actual build. So as you can see I'm (t: 8520) aligning all these production facilities with each other and then these ILSs are in the exact same distance as well. That's also the reason why I not always include the ILSs in the build. (t: 8530) Especially if it's builds that you might want to include somewhere else. You can always just take them out of course when you're building them if you need to kind of make these belts smaller or (t: 8540) longer. But it's something to consider when you're making your own builds. Anyway that means we now have magnetic rings and that means we can add a ton of stuff to our mall. First and foremost we (t: 8550) now have the pile sorters as well as the mark 3 belts in production and these are things are all awesome for our late game builds. Another thing that we can start producing now is the build (t: 8560) accumulators which are super useful not just for the actual powering between planets but also on the planet itself in terms of defense and having those spikes every now and then. We're also going (t: 8570) to need these chargers in order to charge a couple of them up because we're going to need those in a second. And in order to charge them up of course we need to build a couple of these charges, place (t: 8580) them down, set them to charge, make sure there's an input belt where we have those things going in. I'm not entirely sure why I'm doing this the old-fashioned way and not just putting a splitter (t: 8590) under here and and just putting it in the middle of the build. But I think it's a good idea to do that. Sometimes old habits stick. You're not going to need a huge amount of these. This is not the point on transferring the power on this planet to anywhere else. If anything we're going to be (t: 8600) low on power on this planet the entire time. We just need to get a few of them to charge. As you can see we're not actually charging any at the moment because we're hovering just below 100% (t: 8610) power. So we're going to need to fix that as well. And for those of you that were paying attention you might have noticed that I said we are going to need those charged accumulators for something. (t: 8620) And what we're going to need them for is the orbital stations because these actually need, well, the rings. They need some rockets so we can make those now. We could already make them for a little (t: 8630) while. And we are going to need some charged accumulators in order to make those. So there is a thing that you need to watch out for though when you start making this build. Because this build (t: 8640) will require ILS inputs. And as you can see, because this is completely automated, what the game did is it dumped all the ILSs on this belt. (t: 8650) And it's trying to transform all of those into orbital stations. So first of all in order to populate one gas giant you need a total of 40 orbital collectors. (t: 8660) Now in order to build those you are going to need four stacks of 10. So this is why I limit this to 10. This basically means that I'm never going to produce more orbital collectors than I need for one single gas giant. (t: 8670) At some point in the game maybe you will need more of that. But for now one is going to be more than enough. (t: 8680) You might want to check how many ILSs you actually have in your inventory. As you can see I currently don't have that many. So I might want to grab a couple of them off this belt just to make sure that I'm not running into issues with all my ILSs being transformed into orbital collectors. (t: 8690) That is actually going to take quite a while. So this is also why you want to have this build up and running pretty early on. Because even at a normal production speed these things take forever. 30 seconds just to build a single one. So do that time and time again. (t: 8710) And that means that you're going to need about 20 minutes even at full production to make the orbital collectors. Which is also why I have two assemblers. Just to speed things up in case we actually have the required materials. (t: 8720) Other than that don't forget that we actually built most of our mall at mark 1 assemblers. So what you could do is upgrade them to mark 2 of course. (t: 8730) Now this will make sure that whenever you're using some of your buildings they get replaced as soon as possible. In order to prepare for the next episode make sure you subscribe to my channel. And I will see you in the next episode. Bye! (t: 8740) Make sure that you scale up your raw resource production on the two planets that we have as much as possible. And also make sure you have plenty of power to work with. As you can see I am now making use of my solar panels. (t: 8750) I build a couple of these rings around the planet. There's a gap in my ring because well the enemy is in there. But other than that just a couple of rings around the planet like this is more than sufficient. It doesn't actually take that much time to build them. (t: 8760) And each ring will give you about 100 megawatts of power. So that's actually quite plenty. (t: 8770) As you can see I have about 100 megawatts of power. So that's actually quite plenty. As you can see I have about 100 megawatts of power. So that's actually quite plenty. I am now way above my 100% threshold and I'm actually charging quite a few things. Specifically the accumulators so that makes sure that the orbital collectors are now also in production. (t: 8780) Also now is a perfect moment to make sure we finish up on any upgrades that we haven't done so yet. Specifically all the offensive and defensive things are quite useful as well. (t: 8790) If you haven't done so yet you also want to have the purple signs completed for the next episode as well. You probably have that since I mentioned it last time around as well. (t: 8800) And just keep clearing out anything that's on the yellow side of the tree. That's actually going to be most of it so we're approaching endgame pretty rapidly now even though it might not feel like it. (t: 8810) We're still on the first planet building mostly. But you don't actually need to go through your entire universe in order to complete the game pretty efficiently. So make sure you also grab a couple of things like the upgrades for the logistics carriers and the drones. (t: 8820) Because that will make your life a lot easier. And oh my god I haven't actually researched Vanguards. (t: 8830) Or vein utilization yet which is like the best thing in the game. It's also quite expensive so that's the reason that I skipped it. But if you have some time make sure you grab a couple of levels of that as well. (t: 8840) And there you have it guys. It might not feel like we've done a lot but that's actually not completely true. Remember we expanded our facility with a lot of different materials that we needed for our mall. (t: 8850) We actually have a mall now which is pretty useful as well. And this will make scaling up for the late game super easy and super efficient. (t: 8860) While if you do this too if you start scaling up too early you always run into bottlenecks. And you get into an issue where you're producing a couple of things in huge amounts and other things not at all. (t: 8870) And you're basically just spending a lot of time debugging your system. Setting it up like this means that all the resources that we can draw from anywhere in the universe. (t: 8880) That we produce on this planet are completely dedicated to the little science production we have at the moment. As well as our mall. Which means our mall will never suffer. (t: 8890) And we will never suffer from input issues. We also moved of course the defense lines. We did some housekeeping and set up some more defense. And in the next episode we will start focusing on transitioning to the end game. (t: 8900) I hope you enjoyed this one. If you're still here you're awesome and I do hope to catch you in the next one. (t: 8920) Hey hey cda and today we're going to get some purple science going. As well as do some other really fun stuff. First things first I'd like to thank Austere, Panic, Skyshredder, (t: 8930) Chepster, WillLoker and 23kirber for becoming members of this channel. Your support is very much appreciated and you will now get early access to everything I do on this channel. (t: 8940) And your names will start showing up on the actual map as soon as I actually visit this planet. So in the next two episodes we're going to work towards that. (t: 8950) So there's a couple of small things that I added to my build over here. As you can see I now have a giant power bank in the back of my defense line over here. (t: 8960) Why? Well mainly to make sure that I'm storing up some power in case I have a power spike here. The power spike specifically happens when you start placing planetary defenses. (t: 8970) Which is the next thing I started to do. So these planetary shields are actually really power hungry. So don't place too many of these. (t: 8980) So don't place too many of these. At the same time just just place one or two at the same time and then let them charge up. And once they're kind of done place a few others. Unless of course you're expecting a very early attack. (t: 8990) So do start placing them around your most vital facilities. If you have a few miners taken out or a couple of turbines or whatever when you get attacked. (t: 9000) That's not the biggest problem. You ideally also want to cover these. But start with things like your production facilities and your malls. Speaking of the mall I added in. (t: 9010) I added in pretty much every building that I hadn't really added in that we can construct at the moment. Including things like implosion cannons that I don't really use. But we also for example have now the particle colliders. (t: 9020) Which you can't actually build just yet because we don't have the free materials in production. But basically I'm just trying to kind of finalize my mall over here. (t: 9030) So I don't have to worry about it in the long run. Something else that I've managed before. But you probably start noticing that as you're playing as well. Is that you have processors. (t: 9040) That are being in a really high demand. Now to be honest at this point it's probably not going to be a problem. You can always just grab whatever you have stocked up in terms of the processors. And dump them in whichever logistic distributor seems to be ignored. (t: 9050) It's not really a problem. But honestly it's more ideal to just start producing a little bit more processors. Now we also happen to need processors for the information matrix. (t: 9060) Or purple science if you want to call it that. So might as well hit two birds with one stone. Does it actually work? I'm not entirely sure. How that works. (t: 9070) But let's do it anyway. Before we do that I actually have to make a little rectification. Because as someone pointed out in the comments. I actually made a small mistake in my turbine build. (t: 9080) So there's four of these assemblers making gears. But you actually need six. Well actually you need five point something. Just a little bit over five of these assemblers. If you want to completely optimize the build. (t: 9090) But in theory you would need six to have it completely functioning at 100%. The reason I made that mistake is well actually. If you just upgrade these assemblers to mark two. (t: 9100) It's actually the exact amount that you need. And I used that math rather than the mark one math. However there's an other easy way to solve this. (t: 9110) Just add in a fifth assembler. Mark one assembler that is in the open space that we have left in this build. And that should have the build functioning at almost full capacity. (t: 9120) I think it's like 96%. But that's close enough and you won't notice the difference. And I didn't really want to make that. I just wanted to make sure that I was making the right decision. (t: 9130) So I just added that additional assembler into the build that I have uploaded to DysonSphereBlueprints.com. So if you download my builds. You should see the correct one or at least the optimized one right there. (t: 9140) So you might be wondering. Didn't we already have a processor build? And the answer is yes we do. We have one right over here. But again I want to re-optimize this based on mark three bells. (t: 9150) And everything else that goes along with that. I don't want to reuse an old blueprint. I'm no longer actively making those materials. And remember. Remember. If you want to optimize it in such a way that we can also use it for the purple signs. (t: 9160) Because that's just again hitting two birds with one stone. So let's get to it. Now I'm going to switch up the build a little bit to keep it a bit more compact than it would otherwise be. (t: 9170) Although, depending on how you space this out you could actually make it more compact. But I also want to have it look neat and organized. (t: 9180) So we have these 16 silicon smelters over here. We have a total of five of the smelters for copper on the top row and then one more in the bottom. reason that I put this one on the bottom is we need four of these iron ones and this makes a (t: 9190) nice little square. Then next up I'm going to break one of my rules and actually build something top bottom rather than east west or north south I should say and that's the circuit boards over (t: 9200) here. You can also see that I actually directly connected the smelters over here to the assembler (t: 9210) because that means it's just a little bit more organized because we have exactly one smelter going to one assembler and then the other four are going to be used elsewhere so this is actually (t: 9220) going to go on a belt. The iron is also exclusively used for the circuit board so we have this belt going up here and then we have the outgoing belt down here. Then next on the list are the (t: 9230) microcrystalline components that's a mouthful but yeah it's a very long component and as you can see we have this belt going around here and collecting the silicon for that and then we have (t: 9240) the other belt with the copper is going in between and then we can have some more components couple of outgoing belts something like this they are going to collect a couple of things over here (t: 9250) and then that this is going to go on a belt up here so we have a nice and organized little facility so we have eight assemblers making the components and then we need six assemblers making (t: 9260) the actual processor so this is a very small build actually once you upgrade to the mark 2 assemblers and once again i'm putting these in a box over here so they can be exported to wherever they (t: 9270) need are needed in my mall and of course we're going to add an ils to that make sure you align everything nice and neatly with the builds you already have adding in some sorters and stuff (t: 9280) like that to that you will quickly notice that your main bottom like as long as you're on my playthrough at least is going to be silicon because we have only a single node of silicon (t: 9290) on the actual uh planet that we are on and well that's not on this planet it's on the other one as you can see we have still a million silicon so we're in no way in any (t: 9300) danger of running out or anything like that but because it's all in one single node it's actually pretty hard to extract a decent rate out of that now that's something we'll be (t: 9310) able to fix soon enough but before we do that make sure you set one of your ils to actually supply water to your system as well as oil doesn't need to be huge amounts you definitely don't want to (t: 9320) set all of your ils to supply it to remote supply i should say specifically so you can get access to that on other planets you just have one of them specifically one that is (t: 9330) is getting more than it needs so you can actually access this from the other planet the reason for that is that i actually want to turn this second planet into a science production (t: 9340) hub and when i mean science production hub i actually meant mean something that we can scale to the end game so that means we're going to build our broadband facility which is the second thing (t: 9350) we need in order to get purple science right here on this planet now remember other than the processors we need the broadband i'm just going to aim for one per second that seems like a little (t: 9360) a tiny little amount but again don't worry about it it's going to be more than enough and you don't want to scale up too fast because otherwise you simply won't have the resources to do so (t: 9370) now we're going to need a ton of things that we aren't actually mass producing yet so we need the carbon nanotubes we need the crystal silicon and we need plastic honestly this is a really really annoying recipe but let's get to it i like starting with something easy which the crystal silicon (t: 9380) definitely is so just four smelters making silicon and four smelters making crystal silicon will fix (t: 9390) the graphite which we're going to have on this belt coming in at least the coal which is where this is coming in and then we have all the graphite unloading on this belt that we're actually turning (t: 9400) around here because we need the graphite to go into several other builds as well however we also need a tiny little bit of titanium so i'm actually bringing in the titanium in between these other (t: 9410) things going down and then again breaking my own rule and building from north to south a little bit just to align these two smelters with the smelters that we already have to kind of keep it all square (t: 9420) nice when designing a build that is so complicated like the one we're getting now at least has a lot of different components in it i kind of like working my way backwards sometimes so (t: 9430) because we already have an end product over here so this is the crystal silicon that goes straight into the broadband i figured well why not just make the broadband over here we're going to need (t: 9440) some room in between these buildings because we need two more recipes and then we need to be able to export it as well so we're probably going to export it from the outside and bring it in from (t: 9450) the inside and then hopefully make everything else that we need in order to make it so that's the the nano tubes as well as the plastic down here somewhere we actually don't need huge amounts of (t: 9460) this is just a very slow recipe but we need quite a few different things and most of those things are actually related to oil so i actually decided in this run to build everything from base materials (t: 9470) including anything related to oil usually i just make a giant oil refinery somewhere and i used to refine oil in my builds as a raw material and i figured why not just include it in the build if (t: 9480) you have a lot of enough parts up to the Black Syndicates or Crevice ASAP why don't you make it to put the homene bromide in it that's the next step so now that we have our highlights you can see that we will be giving away some of thatquelear oil and of course i will also be (t: 9490) having little hard watt and the Scor где we're also going to be giving away three of the types of pounds that you can stick around your scientifically available oil and you have a big panel jutting améric ϯ dos because we want to not using wouldby產ner to go for any materials as well as many that we can take and we need to make for that opening this is a little bit more (t: 9500) muchiting part where we also need need to be on here at the endinflammatory as it is Tan하 hensens and curries is all you need we also might as well will get some Surf frozen polygons at the need stone and water as well so we have that coming in as well so from the belt over here (t: 9510) and then the acid goes out on another belt because we're going to use that in the next three chemical plants that are making plastic and this is also where we're bringing the graphite all the way from (t: 9520) down here because the graphite also needs to go into the plastic and then we have the exporting belt for the plastic down here so you can see that's a lot of belts to keep track of so i really (t: 9530) recommend marking up so you can easily see what goes where we're not quite done yet with the (t: 9540) chemical plants though because we still need some graphene we need five of these chemical plants making graphene luckily these need the acid as well as the graphite so by making sure you have your belts in the correct order you can actually draw the graphite from over here the acid from (t: 9550) over here as well and then just make sure you're combining everything so this actually needs the oil the oil is coming from this belt it's still in reach it's three spaces away so you can draw (t: 9560) it in from over here the graphite is coming from this belt so you can see that the graphite is from down here and then as you can see this is the most efficient way to place these belts so that (t: 9570) you can basically combine the use of belts between different types of facilities now of course this belt needs to be a little bit longer because otherwise it won't actually work and then we're (t: 9580) still not done with the chemical plants because we need a total of four chemical plants making nanotubes and look look look how nice and tidy this looks the belts maybe not so much so there's (t: 9590) different ways you can make this look tidy and then we're gonna go ahead and make this a little bit more (t: 9600) more but honestly it's not that bad also we're not quite done yet because now we need to bring in (t: 9610) the plastic as well as the nanotube all the way up here in between here and then we can have these (t: 9620) exporting belts on the outside like I mentioned before and then we are producing exactly what we actually need to do one emplate and if you report immediately the (t: 9640) mejorar with the watts like the our airoid I Ladies and gentlemen so all these because we Slice and it's a really good finished look because it's not that much harder It's actually a pretty strategy kind of look I'm going to put in a (t: 9650) or you can as well but if you look at them it's not the manufacturing places to nowhere like this the Ludicrous it's pretty much a solid veya finish I'm going to put it in there and we'll see about the length of time that you can hope that the Kennedy you could match but the form size should feel great compared to drive the space it still makes no difference from this yo and it look really simple you can see that the leg of ades starting to work the grabs a you can see that there's some different components and There's so much complexity going on here, and I think the end result looks really, really nice. (t: 9660) I'm really proud of this one, honestly. So, one broadband per second. Now, all we need to do is add in the build that we just made for the processors. This is actually producing two processors per second, which is in the exact amount that we need in order to combine with the one per second broadband. (t: 9670) And this is all the materials you need to make one Purple Science per second. (t: 9680) Now, I'm going to need to figure out what is destroying my solar panels, and I'll be right back. Okay, I don't know about you, but I'm getting bored chasing the dark part of my planet. (t: 9690) So, let's go and fix that. I'm going to switch my missile turrets to also fire at the upper air. That means that any relay station that tries to land on your planet that comes even close to a missile launcher will get shot at, which includes, of course, the signal towers. (t: 9700) So, by placing a couple of these signal towers... (t: 9710) ...all across your planet, it's pretty easy to fend them off. Honestly, I wouldn't go too much in detail about that. Don't try to cover your entire planet with these signal towers. (t: 9720) Just keep adding them as you expand and as you keep fighting off the Dark Fog. And basically, every time you plant one of them, that's another area of the planet that's never going to see another relay station land there ever again. (t: 9730) However, shooting down relay stations is a very good way to get the Hives pissed at you. (t: 9740) And they will send your fleet... ...their fleets at you. At that point, you really need the planetary shield generators on every planet that you have a sizeable presence at. Typically, they will attack the planet that you have the most production on. (t: 9750) But I've seen every now and then the fleets going to other planets as well. So, I figured, why not just make sure the planet is actually safe and set some defenses up on both our planets? (t: 9760) And let's do so in a very cool way. Now, what I like to do on my planets is actually set up a... (t: 9770) ...polar defense line. The reason I like to use the poles is because you're not really using the poles for anything else usually other than... ...well, a couple of other things maybe that I will show you later on. (t: 9780) But on the production and mining planets, you're not going to use your poles that much. So, might as well put some nice defense lines on there. (t: 9790) And as you can see, I made a nice little ring around one of the... ...the vault lines. And I put a signal tower in every corner. Now, the signal tower is actually placed in such a way... (t: 9800) ...that it's almost hitting the middle. There's a little open area technically that's not covered by the signal tower in the middle. But because there's actually a building there, the dark fog will never try to land there. (t: 9810) And there's one on every corner. And a little ring of solar panels mostly to help me see where the ring is. But it actually kind of looks really nice as well. (t: 9820) Now, what we want to do is add some turrets to this. And when I say some turrets, I mean a lot of turrets. Because why not just add in as many turrets as we can fit? (t: 9830) Well, technically, we can. But, I don't know. I don't know. Technically, I think we could fit even more turrets on this pole. But, well, I think this is more than enough. The battlefield analysis bases are mostly there just to help you actually build this thing on other planets as well... (t: 9840) ...when you're placing down the blueprint. Because this is actually quite a bit of stuff to build. There's a lot of belts in here. There's a lot of solar panels in here. (t: 9850) And a couple more lines. The idea is basically that this thing is going to power itself. Also, if you place this on a new mining planet, for example... ...not only is it going to help you defend that thing... (t: 9860) ...but it's actually also going to power everything you're doing on that planet initially. Because, of course, the missile turrets are not going to consume that much power if you're not actually shooting at anything. (t: 9870) Now, speaking of power... This is complete overkill. But I figured, why not just add in some accumulators as well? This way, you can actually store up all of that power that you're gathering up through your solar panels... (t: 9880) ...and use it in case you're getting attacked to power your shield, for example. (t: 9890) You can use it to power your missiles. But you can also power it while you're expanding. In general, I'm really, really loving these accumulators now, as you can maybe tell... ...in order to make sure you cover some of those spikes in your power production. (t: 9900) All in all, this looks really cool. And I wish I could show it to you when the fleet is actually attacking. But once the fleet starts attacking, the pole defense coming in action is really cool to look at as well. (t: 9910) Of course, make sure that you have an ILS in there as well. That's actually requesting the missiles. (t: 9920) We're going to focus on that at some point. But for now, you can just put in the missiles that you have in your other defensive station... ...that you might have on this planet, or whatever you were using before. (t: 9930) Where is it? Over here. Alternatively, you can just remove this again and just move the missiles elsewhere. You can also just have this ILS itself actually request missiles as well... (t: 9940) ...and just use this as a kind of a backup station as well. There's no Dark Fog on this planet. So, honestly, this is not very useful. But it's completely up to you whether or not you want to remove it. (t: 9950) Honestly, this looks really, really cool. Now, of course, we're going to get most of the fleets attacking our main planet over here... ...that we are building, well, everything at right now. (t: 9960) So, we're also going to need some more defense on our home planet. Because, honestly, the few missile turrets that we have over here... (t: 9970) ...I think it's like, what was it, 12 or something like that? That's not going to cut it for very long. We're getting heavy attacks. And I'm also not entirely sure why I have these... ...stray units going into my base. (t: 9980) It's because the missile turrets are not actually part of my defense line. Every now and then, some stray units get attracted by that... ...and just fly straight through the base. (t: 9990) Anyway, they're being picked off, so that's not a problem. Let's add some more firepower to the defense. But we're actually also going to need a place to store our science. Because, well, we're for the moment still producing all our science over here... (t: 10000) ...in this little starting facility with the three first types of science. But once you hit purple science... ...I really think you need to start scaling this up. (t: 10010) Which is where this build comes in. And it looks way more complicated than it is. But for most of you, I think, if you've seen any of my previous playthroughs... (t: 10020) ...I've been using this blueprint for quite a while now. So let me just walk you through it. There is an ILS over here that's requesting all the different colors of science. And then there's an ILS over here that is actually also requesting two colors of science. (t: 10030) But that will later on also start requesting the antimatter that you need to make white science. Now, the only reason that it's requesting... ...these two, or it's actually supplying them... (t: 10040) ...but it's not actively supplying anything... ...is because I have these six belts running on the outside. Two of them are running on the inside over here where this ILS comes in. (t: 10050) And that's why this is actually storing up these two colors... ...because this is where the two colors of science go on. So we have three colors of science on the outside. (t: 10060) We have two colors of science on the inside. And then the last belt is going to be for the antimatter. So for now, this is actually going to be able to process science. But once we have all five colors and we're working on white science... (t: 10070) ...we actually need to transform those five colors into white science. So then we will completely switch around what this facility does... (t: 10080) ...without having to build a completely new facility for that. In the middle, a planetary shield generator that was not in my previous blueprint. But of course, we need to make sure this thing is protected. (t: 10090) And again, the battlefield analysis base is mostly just here to help you build it once you place it. However, I want to take this build a little bit further. (t: 10100) Or actually, a lot further. So I cleared out the whole area around the pole where I was initially building... ...to make space for a little bit more. Now, what I want to do is actually want to produce the science on site as well. (t: 10110) Not necessarily the raw materials like the broadband and processor that we just made... ...but the actual science itself, all processed here locally. (t: 10120) So what I've done here to facilitate that... ...is because we have the actual science here... ...we have five different colors of science... ...and then later on we have the white science as well. (t: 10130) We actually need six portions of the pole, I would say, in order to produce that science. Now, I actually like making a couple of these slightly smaller than the others... (t: 10140) ...because right now we only need just a few matrix labs, honestly... ...in order to make blue and red science because that's super efficient. (t: 10150) And then the other types of science we actually need more space for. Specifically, the purple and green science. Actually, you need quite a lot of matrix labs in order to make that in large quantities. (t: 10160) So we need two of these spaces to be slightly bigger than the rest. So these four on the side, or the two on the side over here and the two on the side over here... (t: 10170) ...are going to contain about six matrix labs next to each other. And then we can, of course, stack those up. And then in the middle over here, there's going to be about nine, I think... (t: 10180) ...depending a little bit on how it fits in... ...because I do want it to look like that. So that's actually 50% more. (t: 10190) And those are going to be dedicated towards the purple and green science. The layout itself is going to be very straightforward. So we're just going to request the materials that we need in order to make a specific color of science. (t: 10200) Then we have these belts on the outside going into the matrix labs over here... ...making a specific color of science and then just transporting it out on the side over here. (t: 10210) Now, again, like I said before, we have six of them making blue science over here. We're going to have six of them making red science. (t: 10220) And we're basically going to repeat that across the entire section of this pole. At this point, you probably unlocked quite a bit of stacking for the matrix labs... ...but you don't necessarily need to stack them all the way. (t: 10230) I definitely did not do that in the blueprint just to make sure that you can actually build it with some minimal upgrades. But as you can see, I'm still progressing through a lot of those that are related to yellow science... (t: 10240) ...and you can already stack them up this high. You're not going to need that for a very long time. But still, it's nice to have it ready. Most of this build is actually not going to be doing much until the next few episodes... (t: 10250) ...when we're going to really scale this up. But we're going to get it ready so this will kick into gear as soon as we do. Now, the one part that is actually going to kick into gear, or at least it should... (t: 10260) ...is the one for the purple science because remember, we are making all that stuff on the other planet... ...and this has been working for a little while already while I was designing all of this. (t: 10270) So you can see we are outputting quite a nice amount of purple science now. That also means that we're actually going to replace the processing facility that we had in the starter build over here... (t: 10280) ...collect the three colors of science that we're making here. Because remember, we're making science and we can use it. We don't need to wipe out this facility or anything like that because we automated all the inputs. (t: 10290) So this can now be exported to our PolarHub. And here you can see all the different colors of science moving in different directions. (t: 10300) Now, we're not producing a massive amount of anything just yet... ...so it's not going to look as cool as it will later on. But you kind of see the idea coming to fruition here. (t: 10310) Now, of course, I mentioned something about defense. So let's add some missile turrets to this as well. As you can see, it's not quite as much as we had on the other planet. (t: 10320) Honestly, you don't need that much turrets anyway. It fits nicely in here and well read it. And there you have it, a very cool build that is actually producing all the different colors of science... (t: 10330) ...as well as consuming those different colors of science in a single build. It's also going to be able to switch to making white science later on and then consuming that. (t: 10340) Because remember, we have six colors of science over here. So we can use one of these facilities later on to actually... We will produce the white science in the middle and then consume the white science on the outside. (t: 10350) So we'll have five different colors in production here and the white in production here. And then we're going to consume it on the side, again, keeping all the science in one place. (t: 10360) Well defended, with a power bank on top of that and some power production as well. I'm really, really loving this build. (t: 10370) Now, honestly, this build is kind of oversized and really big. So you might want to scale it down a little bit if you're a little bit more frugal with your resources. (t: 10380) But honestly, we're getting close to the late game. So this is where we want to start scaling things up, right? With purple science now in production, it's actually only a few steps to green science... (t: 10390) ...which will open up the rest of the game. So we're going to have to build a lot of the universe to us, so that's going to be really exciting. But don't underestimate how much resources you actually need to produce this at a decent speed. (t: 10400) For that reason, we're going to need that third planet back now. Capturing that third planet is not going to be trivial because, as you can see, it's completely covered in enemy bases. (t: 10410) There's only a little bit of open space here, honestly, to land. And everything else is completely taken over by the dark fog. Actually, I'm going to show you a fairly easy way in the next episode to take over a planet like this. (t: 10420) But yeah, that will be the next episode. For now, I just want to recap kind of where we are now in terms of our process. So we have a mall set up over here with all the buildings that we need for the end game. (t: 10430) We have a small amount of production for every resource that we need in order to make all of that stuff. So just one or two per second, which is more than enough. (t: 10440) So you can see it's not a huge factory like I've mentioned before, but it is actually making a lot of stuff. We are making four different types of colors of science, which we are all consuming over here. (t: 10450) As well as you can see, pretty efficient. Actually, the bottleneck is a red science of all things. So it's really easy to scale this up later on. (t: 10460) And this is a facility that will carry us really far away into the end game as well. So if you are thinking that this looks like complete overkill, we're actually going to be able to utilize pretty much all of it. (t: 10470) We might actually want to expand the stacking of the Matrix Labs later on. This is really going to last you until the end of the game. (t: 10480) It is really important though that before you take on a planet like that, you make sure you turn your existing planets into a fortress. Which is exactly what we've done now with our polar hubs with all the missile launchers. (t: 10490) Along with the planetary shield generators that I've been placing around the planet as I was going. The only part of the planet that's not actually covered by it is where the Dark Fog is already at. (t: 10500) So there's not going to be a problem with more Dark Fog landing here or anything like that. That's not really something I'm worried about. You could also use the Dark Fog to build a base. You could also use the Dark Fog to build a base. (t: 10510) You could also use the Dark Fog to build a base. You could also use the Dark Fog to build a base. That's not really something I'm worried about. However, if you decide to completely wipe the Dark Fog off your planet, there's no real argument against that. Other than the fact that these things do actually drop some items that you might want to use later on. (t: 10520) For example, they drop this Dark Fog Matrix which you can actually use to unlock some special buildings. So don't do away with them too easily, but if they really get out of control you can always set up a farm of this later on somewhere else. (t: 10530) For now, I hope you enjoyed these builds, and I do hope to catch you. hope to catch you in the next one. (t: 10550) Hey, hey, CDA, and welcome back to this Dyson Sphere Master Class. Now, during the last couple of episodes, I actually got a lot of comments of people saying that they were struggling to deal with the Dark Fog. (t: 10560) So I hope I demonstrated well enough how to set up a defensive line, but there's a little bit more to it than just, well, farming them like I'm doing over here. And I'm actually going to combine the All About Combat episode that this is going to (t: 10570) be with something useful while we're doing it, because, well, we have a third planet in our system that we definitely want to take care of as well, and there's a lot of Dark (t: 10580) Fog on it, as you can see. If I count it correctly, there's going to be 18 bases on here, so what better way to demonstrate how you deal with the Dark Fog by taking out this planet. (t: 10590) Okay, so let's talk about how you can actually take advantage of all those resources you're farming. So let's start by taking out the Dark Fog, and all you need is a defensive line, like (t: 10600) I've said over here, and a circular belt that runs across your battlefield analysis bases. Then every battlefield analysis base has a big box on top of that, and a distributor (t: 10610) on top of that, and that's set to export a particular type of resource, for example, in this case, the processor. Then I have a sorter on the belt over here that's importing all the processors, so it (t: 10620) has a filter on top of it, so all the processors will be going in here, and then everything else is going out here. Okay. It includes the processors, but those processors will be picked up again and put back in the (t: 10630) box, so that's not really a problem. Then the next battlefield analysis base will export something different, have a different filter set on the belt, and so on. (t: 10640) So basically, you can export as many items as you want based on the number of battlefield analysis bases that you have. It's a very simple setup, and it's a nice way to supplement your resource income. (t: 10650) However, it's also really important that you realize you don't actually need to farm the Dark Fox. You can just clear them out of your planet, just place your signal towers a little bit (t: 10660) closer to your bases, and as you can see, your missile launchers with their rockets will take them out quite quickly, as long as you have built enough of them. (t: 10670) Remember that once you do attack the base, it will attack you back, so all the enemies that are waiting for you there will come and try to clear you out, but honestly, if you (t: 10680) have enough missile launchers, this should not be a problem. So once you've taken out all the buildings and all the units, you should be able to get your missile launchers back. You can also place a geothermal power station on top of that, and the relay station will (t: 10690) then take off and fly back to the hive it came from. If you do clear out the enemy from your planet, and you build shield generators around your (t: 10700) entire planet to make sure that the entire planet is covered in shield, like I have over here, except for this small part that I just cleared out, this will actually prevent the (t: 10710) Dark Fox from landing on your planet. So it's a very easy way to make sure there's no new bases popping up. If you don't have shields around your entire planet, which you probably won't, you can place a relay station close to the base, and take it out, and it should only take a few (t: 10730) seconds to do that. (t: 10740) As a bonus, you get a new geothermal power station on top of that. It's also worth noting that aside from turrets, you also get access to the base from the base. You can also get access to the base from the base, but you can also get access to the base from the base. You can also get access to these drones that you can use to defend yourself against the (t: 10750) Dark Fox. Now, on higher difficulties, these are not super effective because they are taking out quite fast, and it takes quite a while before you get large numbers of them, so you can (t: 10760) actually take on larger groups. And by that time, the Dark Fox is again a lot stronger as well. But on lower difficulties specifically, or with bases that have just spawned, a couple (t: 10770) of these drones can actually do a lot of damage on top of the damage that you do yourself. So you could actually take out bases without any sort of trouble. So, I've actually included a couple of blueprints for the mall that builds at least the first (t: 10780) few levels of drones. So this is the technical level that you should be at this time of the playthrough. (t: 10790) And well, especially these precision drones are really awesome. You can carry around about 500 of them in your hangar, so this is a lot of units you (t: 10800) can carry around. Again, they take get taken out pretty easily, so this isn't as much as it looks. And once you have them, you can actually get them to use for the next level, which is where you get to take out some of the most powerful unit. And you use them by dragging them in the slots in your fleets. (t: 10810) As you can see, I now have access to two fleets because we're at Purple Science. And these fleets will continue to grow bigger, larger, and in larger numbers (t: 10820) as you continue to progress through the technology tree. Now, there's really only two ways you can actually lose against the Dark Fog, assuming that you've actually built a couple of turrets to defend yourself. And that is either having not enough power or having not enough ammo. (t: 10830) Now, in terms of power, keep in mind that this shield that you build around your planet at some point is using a gigantic amount of power. (t: 10840) And especially if you get attacked from space, it will start to regenerate. And that will, in turn, take a lot of power again. Just keeping it active already costs a lot of power. Keep that in mind as well. (t: 10850) So, if you have your Dark Fog on your planet, you're defending against them actively, and you are being attacked from outer space, that's a recipe for disaster. (t: 10860) Especially if you're also trying to... expand your base. Because all of that will take a lot of power, and, well, it wouldn't be the first time my base gets wiped out just because I ran out of power. (t: 10870) Ammo is the other thing you should take care of. So, I've added in a little recipe to make more missiles, because there's no such thing as having too many missiles, (t: 10880) because you will use them for local defense as well as planetary defense. And also, don't forget that you can upgrade your basic ammunition as well. Especially the stuff that goes into the normal turrets (t: 10890) actually becomes really dangerous. And really powerful. If you compare the level 1 turrets, they do about 7 base damage, and you can upgrade that of course. (t: 10900) The level 3 one do 36 damage. So that's six times as much, almost as five times as much as the base level ammo. (t: 10910) Now, the 36 damage is actually comparable to one and a half missile. It doesn't do any AOE damage, so it doesn't explode or anything like that. But this is a massive amount of damage, and all you need is just some titanium to upgrade. (t: 10920) The copper ones, and then titanium alloy to upgrade them from titanium ones to the titanium alloys. (t: 10930) Super alloy ammo boxes, whatever you want to call them. This is actually super easy to defend yourself, and this is also very useful once you want to start taking over other planets. (t: 10940) Now, I'm going to demonstrate that by taking over the third planet in our system, Glenn H. And this has a total number of 18 bases on it. (t: 10950) So, it doesn't really get much worse than this. So, let's go. So, if my approach works for this, it should work for pretty much anything you get thrown at yourself as well. (t: 10960) Now, in order to make this episode, I cleared out that planet at least three times with different approaches. But honestly, this approach is the best way. So, remember this particular offense platform that we used earlier in the playthrough in order to take the second planet in our system? (t: 10970) Well, this is a little bit outdated at this point because you don't have to manually feed it and stuff like that. (t: 10980) And we have better ways to power now. However, the principle stays the same. Just rocket launchers along with some of these turrets in order to keep some more local defense. (t: 10990) And the big difference here is that there's now a different way to power it. They still have the batteries for the spike power, but we are now going to power them with thermal power plants. (t: 11000) Now, honestly, you could upgrade this even further. You could add some fusion plants in here or something else, whatever you want in order to get yourself some power. Honestly, it doesn't matter how you get your power as long as it's a reliable way and it is something you can match. (t: 11010) You can also add some fuel rods, but this is a little bit of a challenge. You can't do this in the middle game, so you can't do this in the mass produce. (t: 11020) In this case, I'm actually going to run this on hydrogen fuel rods because that gives you something to do with your hydrogen in the mid game, as well as it's a very easy thing to produce in high quantities. (t: 11030) And you're not really using this for anything else anyway, so that makes sure that it's not overlapping with something else in your production chain. Now, although I am using a layout that looks very similar to like the previous one, it actually works slightly different. (t: 11040) It's not like I'm using the same layout that I used in the first game, but I can use it for a lot of different things. For example, as you can see, there's no fuel output set on this belt. That means that once this is placed down, there will be no fuel going out on this belt just yet, which means this entire build will be completely unpowered. (t: 11050) You can see the power working here, but that's only because it's attached to the power grid I have on this planet. (t: 11060) If you build this on a new planet, it won't actually do anything, which is important because that also will make sure the dark fog is not coming for you the moment you start building this up. (t: 11070) Now. On the other hand, the ammunition is actually already set to this belt. This thing is entirely unpowered, so even if this build is actually built completely and it would be powered up, there is actually no power towers connected to this ILS. (t: 11080) You can place one down if you want once it's up and running, but you should initially not want to power this, (t: 11090) A, because charging this up costs a lot of power by itself and you want to use that power to destroy your enemy. But on top of that, this actually functions without being powered. (t: 11100) You can receive items from other ILSs that are powered. Only one of the two needs to be powered, so you can already start stockpiling your missiles as well as your other ammo and your power supply, even though you don't actually have any power on this planet just yet. (t: 11120) As you can see, I've also included space warpers just to make sure that you can use it throughout your entire galaxy without any issues. Now, on top of that, I set the minimum load of vessels to 1%. (t: 11130) This is just to make sure that if there's any issues, you can use it. Now, on top of that, I set the minimum load of vessels to 1%. This is just to make sure that if there's any issues, you can use it. So if there's any demand for missiles, ammunition or fuel, as soon as that's the case, a vessel will leave from one of my other planets and resupply this. (t: 11140) You will never want to run out of ammunition or fuel on your planet because if you do, you're going to get wiped out. Speaking about never running out of missiles, let's design a little build in order to basically mass produce missiles from raw materials. (t: 11150) So we can make sure we're supplying missiles to our planetary defense as well as our offensive capabilities. (t: 11160) Now I'm going to make... to make a design that produces two missiles per second which is plenty as long as you're not non-stop firing them away and even if you are non-stop firing them away when you're for example (t: 11170) trying to conquer a new planet it should be more than enough to make sure you don't run out while doing so. Okay so in order to get started with that we need six smelters making iron, we need (t: 11180) six of them making copper and we need three of them making some magnets and we need one assembler making magnetic coils and then next we need three assemblers making circuit boards and we need six (t: 11190) of them making engines. Now as you can see the copper is actually running past the circuit boards (t: 11200) because it needs copper and then it runs straight in here where it is also being used for the engines. Now the engines are being exported on the outside belt over here going to go all the way (t: 11210) around and then down here because of course we are not quite done just yet. We then need a total of 12 assemblers making copper and then we need six of them making engines. Now as you can see the copper is actually running past the circuit boards and we need six of them making combustible units and then we need (t: 11220) 12 more smelters making copper and that gives us copper, it gives us combustible units, engines and circuit boards which happens to be all that we need for the basic missile. So that's actually four (t: 11230) different resources in order to make these missiles so that also means that we are going to need to puzzle a little bit with our final assemblers. Which looks exactly like this when you're done. So as you can see I'm bringing the copper in the inside over here along with the combustible units and then the engines are coming in (t: 11240) all the way around here from the outside along with the circuit boards and then we're also exporting in the missiles from the outside with (t: 11250) a longer exporter that you can see over here and here from two sides and then that's going to be (t: 11260) combined in a single belt that actually flips around all the way back to the ILS that I have set over here because we only need three different types of resources in order to make basic missiles (t: 11270) so we actually have some room left for this and once you get access to it you can actually also set the warpers for this. Just make sure you set them to demand. (t: 11280) And now your missiles can be exported to anywhere you want in the entire galaxy. So now we have upgraded ammunition. We have upgraded offensive platforms. (t: 11290) And well, we also have 18 bases that we need to destroy on this planet. I'm calling this the planet of death and doom. Anyway, in order to make sure that you can actually do that. (t: 11300) Try to find the largest open space where there's no enemy defenses. And land there. Now remember that once you're approaching a planet. You can slow down. (t: 11310) And make sure you fly kind of around the planet. Without just ramming straight into it. Because if you do that. Then chances are you're going to land somewhere. Like let's say over here. (t: 11320) And then you are in trouble. So make sure you're safe and sound. And then find an open space. And start placing down maybe two or three times. The blueprint that I just showed you. (t: 11330) Now you don't need as many. If there's only a handful of bases on this planet. But it's better safe than sorry. One little trick when you're placing down these blueprints. (t: 11340) Is actually to place them down initially. Without any turrets in there. That makes it a lot easier to build them. And it'll make sure you can get these belts out of the way first. And it also makes sure you can start requesting some ammunition. (t: 11350) For your ILS's already. Without the... And get that stuff on the belts. Without anything interfering with your building process. (t: 11360) Now in order to do that. Remember that if you go to your blueprints. And for example you want to place this down. You can actually... Do so. But you can also just right click on some of the buildings. (t: 11370) That are in your build. And that will take them out. And you can have a build that has no turrets in there whatsoever. Place that down first. And then stamp the original blueprint over it. (t: 11380) Once you are done with this. Now once you do that. You don't actually need to wait for all of your turrets to build. Before you start powering things up. Although you can if you want. (t: 11390) It's a little safer. Just make sure you have a couple of these belt analysis bases up and running. Before you start powering. Anything up. And those will quickly finish whatever is left. (t: 11400) To build. Now as you can see. I'm still at almost zero threats. The only threat I've been gathering so far. Is the actual power I'm consuming. As Icarus itself. (t: 11410) But everything else is just sitting here on this planet. Like nothing is going to happen anytime soon. Which is exactly what we want. Now once you feel you're ready. (t: 11420) All you need to do is put the actual fuel on the belts. You should probably see nothing happening still. Because there's so much power. The system is actually overloaded. So all you need to do then. (t: 11430) Is just manually put in some power. Until the entire system starts up. Now that might take a few of these things to do. But once you have the first maybe handful set up. (t: 11440) As you can see everything else is now powered up. I'm gathering a lot of threat. And the first waves will be coming in really really soon. Now the enemy will come to you nonetheless. (t: 11450) But if you want an easy way of clearing out the enemy bases. Set the upper air active to something. That's lower than high. So for example low or balanced. (t: 11460) What this will do. This will actually make your missile turrets start firing. At some of the relay stations. And assuming you've already set up the shoots on the other planets. (t: 11470) Hopefully you did at this point. But you should see the relay stations being taken out quite easily. Now once the relay station is down. (t: 11480) This will actually draw power from the planetary base. As you can see the power level is slowly dropping down. Until this power is dropped down. This will still be active. So don't go rushing in. (t: 11490) But as soon as the power drops to zero. Nothing new will be produced within this base. As you can see my rockets are already starting to take out secondary relay station. (t: 11500) And any relay station that is in range. This one has already been taken out. Well it's going to be extremely easy to clear out from that point onwards. So here you can see an example of a power down an empty enemy base. (t: 11510) So at this point I can simply just walk in. And take it out with my little precision drones for example. The turrets will be offline as well. (t: 11520) So there's actually nothing in here that can still attack me. And like I said this is a very easy way to clear out the enemy bases. Now as you can see not all the relay stations are being attacked by my base. (t: 11530) So what I need to do in order to make that happen. Is actually just expand my network of signal towers. More further into the system. (t: 11540) It will actually help me take care of the enemy base as well. But as you can see. I'm taking out another relay station. And you keep just repeating this process until you're completely done. (t: 11550) Now I do want to emphasize that even though this is pretty straight forward. And once you get a hang of it. It's not really complicated either. It will take some time. (t: 11560) And getting attacked by in my case 18 bases at the same time. While I'm also trying to take them out. Is not trivial. So don't feel bad if you get yourself killed. Or if it doesn't work the first time around. (t: 11570) Just keep at it. And every base you take out will make this process easier. In the meanwhile. If you're attacking the relay stations. You will also get attacked from outer space. (t: 11580) So the hives will start sending fleets at your planets. So I hope you set up those planetary defenses. Those shields. As well as the missile launchers like I showed you. Because otherwise you are going to lose your other planets in the process as well. (t: 11590) So keep that in mind while you're doing this. Once you get some room on the planet. Make sure you also place down a couple more of these offensive platforms. (t: 11600) Closer to the enemy base. The closer you are the easier it is to take them out. It's also easier to get them in range of your missile launcher. In order to shoot the relay stations down. Things like that. (t: 11610) So it just makes the actual conquering of the planet that much more faster and easier. Once you cleared out the entire planet of enemies. It will probably look something like this. (t: 11620) You will have signal towers all over the place. Geothermal. Spell and filter analysis bases. A couple of these offensive platforms. All in all it will look quite messy. So what I actually recommend. (t: 11630) Although you do whatever you want. Is to clear out everything. From your planet. And then use this new planet wide blueprint. That I developed just for you. (t: 11640) So you can just copy and paste it down. And you should have your entire planet covered. So this includes the defensive platform. That I showed you in one of the previous episodes. That has all their rocket launchers. (t: 11650) And some power attached to it. It also has signal towers that spread all the way across the planet. As you can see. And I left a couple of open spaces. But honestly the main thing that you want. (t: 11660) Is have enough signal tower coverage. To shoot down any relay stations. That approach the planet. Not necessarily have them land. And then destroy them. But have them destroyed. Before they even touch the planet. (t: 11670) So far this particular blueprint. Has served me very well in that regard. There's not signal towers all over the place. (t: 11680) So you have plenty of room to build. But at the same time there's enough of them. To actually make sure there's no relay stations. Landing on your planets. At the same time what is also really convenient. Is that this actually has a power network. (t: 11690) Spread across your entire planet. Which makes it really easy. To set up your mining locations. And stuff like that. Because you already have the power close. So there you have it. (t: 11700) We have all three planets in our system. Completely conquered now. There's not a single dog fog on any of them. The enemy should not be able to land on them either. (t: 11710) Which means we can now slowly start draining out these hives. This will take a long time. But we're not in a hurry to do that. It's mostly about just being able to build in peace. While not having to worry about being attacked by the dog fog too much. (t: 11720) Now if this particular episode was a little bit light on building. For your taste. I have good news for you. Because in the next episode. We're going to do a lot of building. (t: 11730) Specifically we're going to add another color. To our nice little science build over here. Because of course we need to start conquering the universe. Now we have our entire solar system safe. (t: 11740) And that means we need access to warpers. Which is most efficiently made from green science. So green science allows us to get all the way up to the last part of the game. (t: 11750) To the last part of the technology tree. As well as start conquering the entire galaxy. So that's going to be extremely fun. I hope you enjoyed this one as well. (t: 11760) But I do definitely hope to catch you in the next one. (t: 11770) Hey hey CDA. And welcome back to this Dyson Sphere Masterclass. (t: 11780) This time. We are going to be building some of the most fun builds. I think you can make in this game. And it's also one of the most important transitions in this game. Because we're going to be producing green science. (t: 11790) And as the Dark Fog is doing feeble attempts in destroying my shields. Which they're definitely not successful at all. (t: 11800) Because as you can see the shields are holding just fine. I would like to thank Will Davis. Xi'an M. Patat Orlog. Fence Force Me. Elias Kota. MyNameIsPot39. And MyPos. (t: 11810) All becoming member of the channel. And supporting me in that way. So that's amazing guys. Thank you very much. Not only do you get early access to all my content by becoming a member. But you will also get your own star system in this universe. (t: 11820) Which will actually show up once we start traveling between the stars. We will have several pretty large builds in this episode. And there are some of my favorite builds actually in the entire game. (t: 11830) But before we get to that. I just want to mention that you should keep an eye on your supply chain. Because you will have to buy a lot of stuff. On your supply line. (t: 11840) So make sure you're mining enough to keep all your builds that you currently have up and running. Actually supplied with resources. Make sure you keep defending your planets. And also keep in mind that when you place these planetary shield generators. (t: 11850) They will take a lot of power. So make sure that you keep building your power supply. Either just because you have a lot of destroyed bases. (t: 11860) But you can do the same thing with solar panels. Or whatever you want. In order to make sure you keep power. If you have a lava planet like I have over here. You might even place some geothermal stations. to keep your power up and running. Whatever you do, just make sure you don't run low on power. (t: 11870) Making sure you have enough resources also extends to your gas giant, so the planet next to your starting planet, where you can place these orbital collectors to collect hydrogen and a secondary (t: 11880) resource from this planet non-stop, all the time, infinitely as well. As you can see, you can place (t: 11890) these orbital collectors down around the equator, and it's very easy to build them now since the update, because you can just left-click on a building to build it yourself, which is a lot (t: 11900) faster than having your drones do it for you. It used to be one of the most tedious things in the game, and it's now a lot faster, as you can see. Now, if you're on my seat, you will see that we're (t: 11910) actually producing fire ice on this gas giant as well, which is super convenient, but I'm actually not going to use it just yet, because I can be sure that if you're on a different playthrough, (t: 11920) you have access to that as well, because it's a rare resource. Now, before we get to the... new builds, I'd like to do a special call-out to Steve Bonds. Thank you for double-checking all my (t: 11930) blueprints and catching those tiny little mistakes that are in there that might not make them work optimally, which includes the missile blueprint from the last episode, (t: 11940) where he pointed out that there were two smelters for copper missing. That was actually a mistake in my own calculator, so that's a pretty important thing to find, so again, thank you for that. (t: 11950) So, I added in these two smelters over here. I rearranged the belts a little bit to make that work, and if you downloaded the missile blueprint from the last episode and you used it yourself, (t: 11960) you might want to re-download it and make sure it looks like exactly this, because otherwise, it will not be producing two missiles per second. It will only be producing 1.5 per second, (t: 11970) which is still a lot, but just adding two smelters gets you a lot more bang for your buck. As you can see, it's working pretty well, because I'm now sitting on 10,000 missiles, (t: 11980) so the Dark Hog has no chance. Okay, so first new blueprint is not a very special blueprint. It's just an addition to the mall. We're going to be making nanotubes in our mall, (t: 11990) and this is a bit of cheating, because usually I make a dedicated build for this, but we're not going to be producing nanotubes for very long like this. This is just to make (t: 12000) sure we can actually make some of these particle colliders, because in order to make particle colliders, let me show the recipe, we are going to need frame material, and in order to make frame (t: 12010) material, we are going to need nanotubes. You guessed it. So, all that we need for nanotubes are some graphics, and then we're going to need some graphics for the model, and then we're going to need some graphene, and some titanium, which we have plenty of, so you can see this already coming in. (t: 12020) We're going to make some, and this is going to be exported to a box over here. Next up on the list is another very tiny build in order to make some frame materials, so we can get those particle colliders up and running. This is the only thing that you're (t: 12030) going to need them for for a very long time, so you only need a tiny little amount of production. We're going to be making a lot more frame material later on, but for now, this will do. (t: 12040) Okay, so let's look at some recipes before we actually get into it, so you understand why we're going to be building the builds for green science, (t: 12050) rather than just making the warpers from lenses, because there's actually two ways to make those warpers. Now, as you can see, you get one warper for one lens, but you get (t: 12060) eight warpers for one green science. Now, green science itself is made from one lens, as well as one of these quantum chips, so you actually get two science for one lens, (t: 12070) and the two science then translates into a total of 16 warpers, so that one lens actually (t: 12080) doesn't translate into one warper, but 16 warpers, so it's extremely more efficient to make your (t: 12090) warpers from green science than making it straight up from lenses, so this is why we're going to take that one additional step to get our green science going, and we just siphon off a tiny little bit of (t: 12100) that green science, and we're going to have all the warpers that we're going to need for a very, very long time. Now, as you can see, I'm going to make these builds on our secondary planet, because we have (t: 12110) a lot of space to build there, and these builds are anything but small. I'm actually going to start out with the quantum chips build, and we're going to need four smelters making iron, six (t: 12120) smelters making copper, and then we're going to need, how many are these? I guess 16 smelters making silicon, and that's all going to be needed to make one quantum chip per second, and if that (t: 12130) sounds like not a lot, well, it's actually a lot, and that's going to be a lot of time, and that's going to be a lot of time, and that's going to be a lot of time, and that's going to be a lot of time, because we're going to need a lot of quantum chips for a lot of different things. It's actually one (t: 12140) of the most used items in the entire game, but we're going to start somewhere, so we're going to start out small, because as you will see, this build is already going to be pretty large, even if (t: 12150) we're just trying to produce one per second. This build is going to feel very familiar, because we're going to be adding two assemblers making circuit boards, and then eight assemblers making the (t: 12160) crystalline components, and then we can fit in nicely the six processor components, and then we can fit in nicely the six processor components, and then we can fit in nicely the six processor assemblers that we need over here, so we have a nice square looking thing. I really like marking (t: 12170) up all these belts so you know what goes where, because honestly, things are kind of like flipping around, turning around, flipping around each other. It can get really confusing, so I hope this (t: 12180) makes it easier for you to follow along. Then we actually have quite a few of the parts that we need for the quantum processors already, at least half of them, because that's the processors. (t: 12190) However, the other half, the plane filters, which is a new item that we haven't made before yet, is going to be a lot of time, and that's going to be a lot of time, and that's going to be a lot of work. First of all, because the recipe is extremely slow for the plane filters, so we need (t: 12200) six assemblers making the one per second quantum chips, but then we need 24 of these assemblers (t: 12210) to make the actual plane filters. Now, the plane filters itself actually require two new resources as well. We need to start making titanium glass, which is actually pretty straightforward, and then we're going to be making the large arc nemesis of the early end game, which is the (t: 12220) Casimir crystal. We are going to need eight of these assemblers making the Casimir crystal, (t: 12230) and if you're wondering what's the problem with this, well, first of all, it needs quite a bit of annoying resource, so we need graphene as well as the titanium crystals, but we also need a ton (t: 12240) of hydrogen, so you need 12 hydrogen to make a single Casimir crystal, and up until this point, you were probably drowning in hydrogen because, well, you didn't really have anything to use it (t: 12250) on. Well, don't worry. These Casimir crystals will be used for the next generation of the game, and we'll take care of that really, really fast. Another annoying item to make, at least in this (t: 12260) part of the game, is the titanium crystal, because you need the organic crystals for that, which needs a lot of other stuff again. So, the way I'm going to set up these builds is actually in a way that (t: 12270) will allow you to transition from where you are now, without access to the other systems just yet, to a different version of this exact same build, where we're actually going to be using all the (t: 12280) rare resources that you have in the game. So, for example, organic crystals can actually directly be mined on some planets, just not the (t: 12290) planets in your starting system. So, rather than making a build that completely depends on making these things themselves, which I am going to do initially, I'm going to make it sure that you can (t: 12300) actually cut these out later on. However, we're not quite there yet, because you will also need quite a bit of titanium to go on, so we need 20 smelters making titanium. As you can see, (t: 12310) I have two belts on the outside to supply, and then this single belt in the middle will be sufficient to transport a bunch of titanium crystals. So, we're going to make a build that will be part of that titanium to the titanium crystal production, and then this other belt is going to go along, because we also need to make titanium glass, which is where these 10 assemblers (t: 12320) making quantum chips come in. Now, by the way, we will also be able to use these quantum chips to (t: 12330) actually upgrade to Mark III assemblers as well, which is going to have an impact on these builds as well. And again, it's set up in such a way you can easily adjust this build later on. (t: 12340) But in order to make the glass, we will not only need titanium and glass itself, we also need some water. So, I have water coming in from over here. You'll see why in a moment. (t: 12350) And we have eight of these smelters making glass. Then you might have noticed that I just skipped the actual production of the organic crystal and graphene, because we are going to be making that (t: 12360) in this part of the build. And this part of the build, you can actually completely delete later on. But for now, we're going to have to make it the old-fashioned way. So, that means we need (t: 12370) 20 smelters making graphite. Then remember that we also need to make plastic in order to make organic crystal, which means we're going to need an amazing 12 of these chemical labs in order to make plastic, and another 12 to make the organic crystal. (t: 12380) Then we need six more chemical labs making graphene, and three more making the assets (t: 12390) that we need to make the graphene. As you can see, I've put the belts in such positions that we can usually benefit from both sides from making something. For example, the graphene over here, (t: 12400) a graphite, I mean, is going to be used by both. The haciendo us benefit from both, is going to cost us heat, which is also something that we (t: 12410) can use as the reflux, Also yes, we'd like them to be spiritual as long as they speak (t: 12420) English, we don't want to come across as wear and tear.And for now we cannot be bad, but rather and their customers will earn the quality they deserve, so, fault to the manufacturer for wasting 36, which, again, fell on our cards at this point. (t: 12430) replacing all of this just with a very simple setup. And then all we need to do is add in a lot (t: 12440) of these sorters and power towers in order to power everything up. Add in some ILSs to request the different materials that we need. This is actually going to be changing a little bit as (t: 12450) well in the final version of this build. But for now this is going to work just fine. We are importing raw oil. We're also exporting the hydrogen that we are making as a byproduct. (t: 12460) So that is going to go straight into this part of the build where it's also going to be recycled as far as we can for the Casimir crystals that we're also making here. All in all a pretty nice (t: 12470) looking build even though we are not really paying much attention to this half of it. And it's a (t: 12480) pretty large build as well. So you're going to need to be unlocking the purple science blueprint. Basically that means that you can make infinitely large blueprints in order to use this yourself. (t: 12490) Now we're going to be making the blue science blueprint. So we're going to be making the blue science blueprint. So we're going to be making the blue science blueprint. So we're going to be making the blue science blueprint. So we're going to be making the blue science blueprint. So we're going to be making the blue science blueprint. So we're going to be making the blue science blueprint. Now we're getting to the graviton lens. And the graviton lens itself is typically an item that a lot of people struggle with because suddenly you need to start making a lot of new materials. (t: 12500) One of which is strange matter. Which is honestly something that we are making from things that we've already been making. But because deuterium is actually added to the mix as well it can be really (t: 12510) complicated for at least new players to figure out how do I actually make all of this. So the trick here is to recognize that a lot of the base materials that you need for that are actually built (t: 12520) from materials that we've already been making before. However we need to start making especially the particle containers in a large much larger amount than we've been making it before. So we are (t: 12530) going to be needing 24 smelters making magnets. We're going to need 12 of them making copper. And (t: 12540) we're going to need 26 I believe it is smelters making iron. So I added in the two last smelters over here. It kind of sticks out. It's a little bit annoying me. But it's by far I think the clearest (t: 12550) way to lay this out. Then the next section is going to be dedicated to getting all the base resources we need in order to make the motors. Which means we need some more magnetic coils. (t: 12560) Which is where all the magnets are going to come in. We are going to need quite a lot of gears as well. And we need a total of eight assemblers making each of those items. Then we're bringing (t: 12570) in all the remaining iron on the belt over here. So we actually have surplus iron. We have the gears the coils and the surplus copper as well. Then we're going to need a set of metal parts. So we're (t: 12580) going to need 16 assemblers making the actual motors. Which is a lot of them. But make sure that you keep one of the input belts on the outside. Because remember three items means that you can (t: 12590) just have all the three input belts in between. Because you won't be able to reach them with the disorders. Now we're done with the iron for now. Well actually no we're not. We still need (t: 12600) the iron for some of the layer things that we need. So the iron is going to be used in a lot of places. The motors are going to go all on this belt over here. And then we also still need the (t: 12610) magnetic coils. Because we also need the iron for the metal parts. So we need a lot of metal parts. And then we also need to make turbines. You do got to love the symmetry of this entire build. Because once again we need eight assemblers making turbines. So this is where the motors (t: 12620) come in and the magnetic coils. Then we're done with both of those items. But we're actually going to be bringing in the surplus copper now. Because the copper goes into the particle container. And (t: 12630) then the turbines and copper along with graphene. Which we haven't produced just yet. So this is coming to go in from the bottom. Is going to be used in order to make the particle containers. (t: 12640) Then bringing in the remaining bit of iron that we have left along with some deuterium that is going to come in from the side over here. And bringing in a total of eight of these particle colliders (t: 12650) that we've been building from the start of this episode. We are now able to make strange matter. And the strange matter is going to come in this belt on the top. Flipping around over here going (t: 12660) down the bottom over here. Which then loops in very tightly to this set of six assemblers. Making the actual lenses. (t: 12670) the actual lenses. Now in order to make lenses we also need that diamond that I was just mentioning. We also need the graphene. In other words we need a lot of stuff that we can make from rare resources. (t: 12680) So again I made sure to leave them on the side. Now very similar to the actual build that we just did before for the quantum chips we need again 20 of these smelters making graphite. (t: 12690) We're going to use that slightly different in this case though because we need about half of that in order to make these eight smelters here making diamonds. The remaining bit is going to go into (t: 12700) the graphene production along with the acid production over here and as you can see we need (t: 12710) a lot less of these refineries in order to power all of this or supply all of this I should say mostly because we're not making organic crystals and plastic unlike in the previous build but we're (t: 12720) still using these six chemical labs for the graphene and three of them making acid. Now you can hook all of this up to power with sorters and things like that and you will have a nice mostly functional build. However there's going to be one (t: 12730) problem you don't actually have any strange matter being produced because we don't yet have the (t: 12740) deuterium which is going to come in from this ILS over here. So let's go and fix that shall we because that's again one of my favorite builds in this game. Now of course if you have a gas giant that (t: 12750) actually produces deuterium it might have already been working however there's another way to make deuterium and that is by well it's actually two other ways to make deuterium. One is by using the fractionator which is basically a building where you have a building going through (t: 12760) that translates hydrogen into deuterium just by having it go through the building. Now this is actually I think one of the best looking buildings in the game and I don't know it's something that (t: 12770) appeals to me but um I think it's a good idea to make a building that's actually going to be made of hydrogen and deuterium. So let's go ahead and make a building that's actually going to be (t: 12780) made of hydrogen and deuterium and I'm going to show you how to do that. So let's go ahead and the way it works you have the hydrogen coming in from one side and out on the other and then the (t: 12790) front port over here this is where the deuterium comes out. So we have two mirrored buildings next (t: 12800) to basically opposite each other next to each other whatever you want to call it and then we just have a big row of about 15 of these things on one side 15 on the other and then we might as (t: 12810) well duplicate that on the other side as well. The nice thing about this long stretched build is that you can build it closer to the poles as well so it doesn't interfere with some of your larger builds that you want to be building near to the equator. Now a lot of people get very much carried away by (t: 12820) optimizing this particular fractionator build but I can't blame him it's a lot of fun to do that but even with this completely unoptimized build I just have the belt running all the way around (t: 12830) without any interference whatsoever you can already see this is producing the deuterium at a (t: 12840) pretty decent speed so you don't really need to optimize this if you don't want to however of course we do want to do so. One of my previous builds I actually did this with the automatic piler so combining multiple belts stacking these things up because if you stack up your hydrogen on the belt this actually multiplies the output of deuterium as well. (t: 12860) It basically is just equivalent to if you get more hydrogen passing through the building you will have a higher output as well so you want to stack this up as high as you can. Now thanks to the sorter piler that we now have access to you don't actually need to use the (t: 12870) automatic piler the one over here anymore which is really inconvenient because it takes a lot of space now you can just have this looping sorter on this belt like here initially this will only stack twice or three times depending on how much you upgraded it just yet but the further you get in your technology tree the more efficient this will get and I actually have two of them over here just to restack the stacks a little bit but this is just to get it a lot more optimized because again we're actually moving a lot more hydrogen through the system this way and this second belt is actually looping in here at the front just to make sure we get as much hydrogen in at the front. (t: 12890) Now I'm going to show you how to do this with the automatic piler. So this is the automatic piler and I'm going to show you how to do it with the automatic piler. So you can see that the automatic piler is actually looping in here at the front and this is the way it's going to be when you stack it up. (t: 12900) This second belt is actually looping in here at the front just to make sure we get as much hydrogen in at the front and then the second one is flipping all the way around and is coming here from the side. (t: 12910) Now it's important that this secondary belt comes in from the side because you don't want to interrupt the flow of the belt that goes straight through. (t: 12920) So for example if I were to connect this like this what happens is this belt gets priority so this belt completely stops which means half of these fractionators are no longer working. (t: 12930) Which again is why you want to connect it like this so that doesn't happen. Same thing happens on the other side and you can take this a lot further if you want to. (t: 12940) I'm not going to do this because we already have all the deuterium we're going to need just from this single build. Keep in mind that we just built an equivalent size factory that's actually larger than I think all of the things we did in the first four episodes combined. (t: 12960) So on top of that these are upgraded from the factory that we just built. So this is going to have a huge impact on your power drain on your secondary planet if you're building it there but whatever planet you're building are building it on will require a lot of power. (t: 12970) So make sure that you build the rings of power or whatever you want but keep your power up and running because along with these shields you're going to need a lot of that. (t: 12990) This might also be a good thing because you're building a lot of power. So this is going to be a good thing because you're building a lot of power. This might also mean that if you're building some of these larger builds on a different planet than your starting planet that this planet might now be the one that is actually consuming the most energy which means this will be the planet where all the fleets are going to be aimed at. (t: 13000) Doesn't always happen like that it's a little bit fickle every now and then when you have two planets with a lot of production. (t: 13010) Just keep in mind that it's not necessarily the case that all the fleets will still be going to your main planet so make sure this one is properly defended as well. (t: 13020) So make sure this one is properly defended as well. Great news everyone we are now capable of producing green signs which basically means you've won the game. (t: 13030) Well not technically but honestly once you have green signs this is going to become very hard to actually lose the game because you cannot run out of resources anymore. (t: 13040) Now I did change a few things in this science hub layout. Not the layout itself actually but mostly the production locations of the different colors of science. (t: 13050) I flipped a couple of them around. So if you've been using the blueprint before. The green science wasn't actually in there. But I moved the yellow science from this location I believe to the smaller location over here because it's an easier recipe to make. (t: 13060) And I made sure that the most complicated recipes being the purple and the green science are now on the places with the most matrix labs. (t: 13070) Just makes sense. Another new addition to the blueprint are these six assemblers over here making warpers. I have a splitter to make sure that I'm not using all my green science in here. (t: 13080) But as you can see we're going to be using them. As you can see we're producing more green science than these six can handle anyway. But still we're producing a pretty decent amount of space warpers. Make sure you grab some for yourself. (t: 13090) You don't ever want to find yourself without space warpers on the other side of the galaxy. That's very inconvenient. And as you can see this is right now being completely drained. (t: 13100) But there is actually a huge amount of warpers coming in. The reason this is going to be drained is because a lot of my previous blueprints have already been set up to request warpers. Because I was anticipating the fact that we're going to be missing them. (t: 13110) So I'm going to be making these. In general there are very few cases I can think of where you would ever want to request more than 100 space warpers in your ILS. (t: 13120) So this is the minimum amount you can request. Simply because well it's going to take a long time for 100 vessels to fly up and down from this ILS. (t: 13130) So by that time you will be you have plenty of time to replenish them basically. So you don't want to have thousands of these warpers sitting around in each of your ILSs. (t: 13140) You can go through your base if you want. To find ILSs where you're not yet requesting warpers. Because we added in the ILS later before we actually researched the warpers themselves. (t: 13150) And you can just add them in from this point. Set them to 100 requirements. Ask from them locally or remotely. And voila this build will now continue to work until the end of the game. (t: 13170) Thanks for watching. Hey it's DDA and welcome back to Dyson Sphere program. (t: 13180) Today we are going to conquer the universe or at least start conquering the universe. We're also going to fix all our power issues forever. And we're going to get ready for the true end game. (t: 13190) And I'll show you exactly how to transition to that. In the meanwhile I would also like to thank Nick Eveson, Wally UK, Steve Bonds, an old gamer gaming. (t: 13200) Kiro Ivanovski and Sheef. Two for becoming members of this channel. You guys are awesome and your support is very much appreciated. In order to get all the stuff we need in order to expand efficiently. (t: 13210) We are going to need to expand throughout the entire universe. However this is one of the major things that will be different if you're playing with the higher combat difficulty setting. (t: 13220) Or with combat at all. You will find the enemy expanding to the other systems in your galaxy as well. So if you're playing without any combat you can just fan out across the entire universe. (t: 13230) Get the best of it. And you can get infinite amounts of materials really easily. And that's that. Not as easy if you're playing on a higher difficulty. And because I've been playing relatively slow. (t: 13240) Because I'm doing a lot of recording and stuff like that in the background. I will actually find a lot of the enemy in a lot of the systems. So this is one of the events if you play a little bit faster than I am. (t: 13250) However it's not a problem. You'll just need to kind of adjust to that. And I'll show you how as well. You know those annoying moments when you find yourself on a different planet. (t: 13260) And you suddenly find out you forgot power poles or solar panels or belts. Well that's infinitely worse when that happens when you're in a different star system. (t: 13270) So in order to make sure that never happens again. Or at least we have an easy way of solving that. We are going to need a way to easily access whatever we're producing here on our model planet. (t: 13280) In this huge model we have now set up over here. On any corner of our galaxy. And I'm going to use one of my. (t: 13290) Favorite builds. Definitely favorite builds. But also one of the builds that I've already shown a few times before. And I'm going to use a polar mole. (t: 13300) And this is it. And before you start typing in the comments that. Oh my god TDA why didn't you show us the actual build. Well that's because it's actually super simple. Although it's a little bit of effort to set it up the first time around. (t: 13310) Once you've done that it's actually super convenient to have this blueprint in your collection. So let me first show you the basics. We have a circle of ILS's with one in the middle. (t: 13320) Just because that fits. And the whole point of this is that every single ILS contains a different set of buildings. It's going to be set to remote supply. (t: 13330) So that means that it can send this to anywhere in the universe. Now in order to do that we're actually going to need the warpers. But the warpers are going to be brought in as well. (t: 13340) So one of these things. And it's always interesting to find out which one it is. But one of these things is also requesting warpers. Let me see if I can find it. (t: 13350) As you can see we have pretty much every building in the game in this thing. Oh there we go. So I'm actually requesting warpers here as well. And then I am exporting warpers on the belt below. (t: 13360) And because all of these ILS's are connected. The warpers will actually go from one building into the next. And automatically resupply each other. (t: 13370) Because I have a whole circle going on here. So the warpers are going to be on the belt in the circle here. Now you. Actually can see that there's no warpers yet on the belt. (t: 13380) And that is because I haven't actually unlocked the upgrade that we're going to need. In order to unlock the logistics vessels warp. (t: 13390) So we need level 4 logistic carrier engine. In order to actually allow our vessels to fly between systems. And that will also be the point where these belts will actually start moving. (t: 13400) Because as you can see right now there is no warpers in the inventory over here. If I check my own inventory. There's a little bit of space. There for the warpers. The ILS's have a similar thing as soon as you have that unlocked. (t: 13410) Now in order to make sure all the buildings actually get to these ILS's. We have the usual setup of these logistics distributors. (t: 13420) But these ones are set to request the item. So this does mean that this build will actually drain your mall. From all the buildings that are already in there. (t: 13430) Because these will all be delivered to these ILS's. But it's actually really easy that way. Because you can simply go to the planet view. (t: 13440) By pressing M. And then you can just click on it. And pick whatever you need directly from the ILS's. And put it in your inventory if that's what you want to do. Or just place it back in. It will mean that the automatic delivery to Icarus. (t: 13450) While you're on this planet will not be as efficient as it used to be. But I think that's a small thing to suffer. (t: 13460) If you take into account that you can now request that from anywhere in the universe. There's actually a couple of things in there. That we haven't built yet. Let me see if I can find them. (t: 13470) For example we have the launching silos as well. This entire build is actually already set up. It actually includes things like artificial stars already. (t: 13480) This is really set up for the end game. So you build these things once. And that's it. Everything we're going to be building after this will be requested. If there's any building in the game that I left out. (t: 13490) Because there's a few missing. I don't even remember which ones. It's only like two or three. But those are like corner cases that I never used. So I decided not to put those in. (t: 13500) I think for example that includes. I don't actually. Oh yeah it includes things like the jammer tower. I think the plasma turret might not be included either. (t: 13510) I'm not entirely sure yet to be honest. I only included the turrets I always use. But if you do use them. You can just kick one of the other buildings out. There's bound to be a couple of things in here that you're not using yourself. (t: 13520) Just adjust this as you see fit. There are a few new additions to this build. Because I actually included a shield generator on this side. (t: 13530) As well as in the opposite side over here. Just to make sure this thing never gets taken out. Because of course if this thing gets taken out. When you're on a different side of the galaxy. That's going to be a huge mess. (t: 13540) Honestly that's probably the point where you want to reload. But the shield generator should make sure these things are kept safe. And I also have a couple of signal towers in here. (t: 13550) Just for the sake of making sure there's a little bit of extra defense. And of course the solar panels are just here to fill up the space. Now you could probably fit in a couple more of these. (t: 13560) I didn't want to overdo it. And actually let me know. Do you prefer these end game builds to be on foundations? Like actual visible foundations? (t: 13570) Or do you prefer the more natural look like I'm going for right here? Let me know in the comments. Now if you're going to be expanding to different planets. You are probably going to want to bring a couple of these advanced mining machines. (t: 13580) If you can already build them. These are relatively expensive to make. We already made the frame materials in the previous episode. (t: 13590) We made all the other inputs as well. The only thing you're going to need in order to make these that we might not have yet. Are the grating crystals. Now as you can see I actually already have some of these. (t: 13600) Because they came out of the farm that I had over here. For the dark fog. Now it's actually a rare drop from the dark fog as well. So I'm able to build like a couple of dozen of these things. (t: 13610) That's one of the items that you are not necessarily going to need when you're expanding. But it just makes it a lot more convenient. So I included this little blueprint over here. (t: 13620) And if you have the grating materials. Or as soon as you find it somewhere else. You can build as many of these. And you will never have to bother with these normal mining machines ever again. (t: 13630) Case in point I just moved to my first system over here. And look there's three hives already in here. And by the looks of all this traffic over here. There's a lot of bases on these planets already. (t: 13640) Now I did happen to find one planet over here. That wasn't actually taken at all. So I figured why not just keep it safe. And use my planetary blueprint. With the defense hub on top of the planet. (t: 13650) In order to make sure there's at least never going to be anyone landing here. Just to make sure I can grab all this silicon. Because I of course have almost no silicon in my starting system anymore. (t: 13660) There's actually some coal over here as well. There's a lot of other base materials. Nothing really of interest on this particular planet. But you don't need to overdo it when you're first expanding. (t: 13670) Just grab a few planets. And then you're going to want to look for some specific types of resources. Now I'm not going to lie. Like I just pointed out. You're probably going to have to fight for some of these resources. (t: 13680) But if you're in a little bit of luck. There's only going to be a couple of bases on some of the planets that you need. I would actually recommend just going through a couple of the different systems. (t: 13690) And trying to find out which planets are easiest to go to. Don't complicate your life by trying to take hugely defended planets first. Unless there's super valuable resources on there. (t: 13700) For example this planet in particular. I have oil. I have organic crystal. And I have stalagmite crystal. Which actually covers almost all the rare resources that you're going to need. (t: 13710) The oil you want to grab. Because you don't have that much oil on your starting planet. The organic crystal is going to save you a lot of headaches. In terms of the more advanced builds. (t: 13720) The stalagmite crystal is also super important. Because this turns into nanotubes straight away. So that makes that super convenient as well. And then on top of these resources. (t: 13730) What you want to look for is kimberlite. It's actually not super necessary. Because that makes diamonds. And you can make diamonds from coal as well. Very easily. (t: 13740) But I'm just going to assume that you find kimberlite somewhere. Because it's pretty common. And then last but not least. You want to also find fire ice. So fire ice was actually already in our starting system. (t: 13750) You can also sometimes find it on gas giants. It doesn't matter where you find it. Just make sure you grab some of that as well. So again. Organic crystal. Stalagmite crystal. (t: 13760) Kimberlite. Fire ice. And then whatever other resource. Including oil. That you can find. Now there's one more thing. That if you can find it. That's super convenient as well. And that is the acid. (t: 13770) The yellow acid. That you need for a lot of the other components. Like titanium alloy. This is actually something you can find in lakes. (t: 13780) On some of the other planets in different systems. If you can find one of those. You're never going to have to produce acid manually ever again. Now don't worry. We'll get to the builds pretty quickly. (t: 13790) But I do want to make sure I leave you with all the tips and tricks I can give you. In order to efficiently expand throughout the universe. Because honestly. Again this is one of those moments where some people burn out in the game. (t: 13800) If you have to mine every single resource on a planet. That's actually a lot of work. So I actually don't recommend that you do that. Just grab whatever you need. However in order to keep in mind where you've already been. (t: 13810) And which planets you still need to basically explore further. I like marking them. So I put a little C on the name of the planet. In order to indicate that I've started mining this planet. (t: 13820) But it's still under construction. Hence the C. But you can use whatever you want. If it makes sense to you. This means that I still have more resources on this planet that I can mine. (t: 13830) I just didn't get around to doing all of them just yet. And don't forget. Now we've set up our interstellar mall. We can actually just request whatever we want. (t: 13840) And the nice thing about these hubs that I included in the blueprints earlier. Is that you have a couple of empty spaces here. So in this case I ran out of space. So in this case I ran out of solar panels. So now I'm just requesting solar panels from my mall. (t: 13850) And they will get express delivered to this thing. And I don't have to fly up and down between systems. Now don't forget that if you do this. Make sure you actually clear the settings before you leave the planet. (t: 13860) Otherwise there will be tons of resources stuck in here. That is not actually going to be used by anything anyone. You're probably not going to come back here and remember that these are even in here. (t: 13870) So make sure you do that before you leave. Now here you see the advanced miners in action. You can just put them on a node. I wouldn't worry too much about covering every single node. (t: 13880) Because some of these nodes are so spread out. You can't actually cover them entirely with one advanced mining machine. Some people really like to include some of the smaller mining machines. (t: 13890) To catch the things that are sticking out. In this case I think I actually caught them all. Oh no look there's one missing. So what you can actually do is place a manual mining machine on that. Make sure of course that it is powered. (t: 13900) And then put a belt straight back in to this thing. Like so. And that will actually make sure that this is all being harvested as well. (t: 13910) This is a lot of work so again I wouldn't worry about it. But it is a way to do this. And this advanced mining machine can actually be turned off in terms of collecting speed. (t: 13920) Now this costs a ton of energy. So I don't recommend doing that at this point. But it's important you know that you can. So it's a way to easily mine tons of resources. (t: 13930) Make sure you have an ILS setup somewhere. That is actually requesting these resources. So for example I have one over here. (t: 13940) And it's actually locally demanding the copper that I've mined. In the advanced mining machine that you just saw. And that's being flown in by the drones. So it can then be exported by the vessels. (t: 13950) You're probably going to be struggling for power on some of these planets that you're mining. And you could fix that by placing down a lot of solar panels or anything like that. (t: 13960) But I actually recommend just mining the things that you really need. Or just setting up the logistics. In order to be mining. But don't worry about the power just yet. (t: 13970) Because there's a lot easier way to solve this than by building like hundreds of solar panels. That I'm going to show you in a few minutes. But we're going to need a few builds in order to do that. So let's get to it. (t: 13980) So the first build I'm going to show you is extremely simple. It's just an ILS requesting the fire ice and stalagmite crystals. Putting them on a belt. And then a double line of chemical labs on each side. (t: 13990) Is going to produce either the nanotubes or the graphene. Depending on what's on the input belt. And then it's just being exported across this very long belt. All the way into this ILS over here. (t: 14000) And you can see I'm bringing in a ton of this stuff. So you're never going to be low on this ever again. As a byproduct we actually get a little bit of hydrogen as well. (t: 14010) Which is going to come in use. Because we're going to need a lot of those later on as well. I actually decided to add in a couple more things to this. So first of all I also started to request kimberlite. (t: 14020) And I just added a very long row of smelters. In order to make a lot of diamonds. And again just look at the speed at which these things are coming in. You're not going to be needing any more than maybe one. (t: 14030) Or maybe in the end later two of these builds. In order to satisfy all your requests. In terms of all of this stuff. Now as you can see I actually have a little bit of a proliferation belt going on here as well. (t: 14040) I figured why not. Because soon we are actually going to be proliferating stuff. And because these are rare resources. (t: 14050) They will eventually run out. Depending on how fast you play. And how fast you get into very late end game. So you might as well make sure you get the most bang for your buck. The next build I'm going to show you is again super simple. (t: 14060) And you're only going to be needing to build one of these for a very long time. That's a build that I was just going to request all that crude oil. That you're now collecting from anywhere in the universe. (t: 14070) And transforming that into raw oil. Sorry refined oil I should say. As well as hydrogen. Now I'm not going to show you these very simple builds in detail. (t: 14080) Because honestly there's not much to show. But of course there are blueprints included. In the description of this episode. So you can download these if you want to use them for yourself. Of course I don't know at what difficulty you are playing. (t: 14090) So you could get into a situation like I did over here. Where I could find all the rare resources I just mentioned. But I couldn't actually find a planet nearby. (t: 14100) That had acid lakes on it. I did find one or two of them. But they had like 18 bases on them. Defended by the dark fog. And I didn't want to spend a couple of hours plowing through them. (t: 14110) Well maybe not a couple of hours. But at least I didn't want to spend the time plowing through them. When I can also just build a very simple build like this. Get the acid going that way. And then upgrade my offensive capabilities. (t: 14120) And then get back to that later. And then just plow through them very easily. Whichever way you use in order to get access to those resources. (t: 14130) As soon as you do get access to them. You can now start really cleaning up your build. So this is the quantum chip build from last time. As you can see. Mark 2 assemblers and things like that. (t: 14140) We can now extremely clean up. Step 1. We can take out the entire right half of that build. Because everything related to the organic crystal and graphene. We no longer need. (t: 14150) We can just request the organic crystal and graphene. One from our rare build. And the other one just from the miners on the planet. Where this is actually on the planet itself. And we can just bring it in. So that means that the entire section related to oil here is now gone. (t: 14160) And we are left with pretty nice and tiny section. That is just going to be producing the quantum chip. And that is going to be the quantum chip. (t: 14170) And that is going to be the quantum chip. That is going to be producing the quantum chips. Now we are also going to be upgrading to Mark 3 assemblers. Which are actually a lot faster than the Mark 2 versions. (t: 14180) 50% faster to be exact. So we can cut down this even further. Removing one assembler for every three we have. That means that we can cut down all of these rows of six into rows of four. (t: 14190) You can see this actually starts to be a lot more compact and small now. All we need to do now is clean up the belts. (t: 14200) And then it looks like this. As you can see. A lot smaller and compacted than what we started out with. Now as you can see I am doing this remodeling in a sandbox mode. So you can do the same thing. (t: 14210) Just make a new game. Put it to sandbox mode when you save it. Find yourself a planet. Or just pave the planet with the button you have over here. That lays foundations across the entire planet. (t: 14220) Unlock all the science. And it allows you to easily fiddle around with your builds without having to do it real time. While being attacked with the dark fog. Now you have that blueprint. (t: 14230) You could actually completely remove this entire section from the old build. And just update the old build to the new version. It is completely up to you whether or not you want to do that. But it will save you a lot of building space on the second planet in our system. (t: 14240) However that was not the reason I wanted to update that build. Because as I just mentioned. We are using Mark III assemblers now. But we are not actually building them yet. (t: 14250) So let's expand a little bit further. I took out the entire farm for the dark fog. It was on the left side of our base. So we have some building space. And let's get going. (t: 14260) Now the first thing we are going to need in our mall. Is actually access to broadband. Because in order to make these assemblers. Where are they? Over here. (t: 14270) We are actually going to need some of that broadband as well. I decided to be lazy and just use the old build for the particle broadband as well. But we are actually going to be updating that later on. So if you do want to use a more refined build. (t: 14280) Then make sure you watch the entire episode. Because I will be showing the updated version of this build as well. However the main thing you need to remember. Is that by default the mall cannot access anything from an ILS. (t: 14290) So I just have an outgoing build over here. That goes into this box with a logistics distributor on top of it. To make sure the mall buildings can actually reach the broadband as well. (t: 14300) Then up next you can just place down the blueprint I just showed you. That's the updated version of the quantum processors. That is again going to be needed for the production of the assemblers. (t: 14310) As you can see you will get some of these white ghosts. That you can't actually build. But what you can do is you can temporarily downgrade these facilities. (t: 14320) To the lower version. And if you keep pressing shift while you hover over all this stuff. And you only select the facilities. You are not going to be downgrading your belts or your sorters. (t: 14330) Just build this first with the MK2 assemblers. And then as soon as you get your first 20 or so assemblers MK3. Just again upgrade them in the reverse way that I just showed you. (t: 14340) So again you just go to this. And you once again hover over all these. And they will be upgraded instantly. Don't forget to add in a little box for this one as well. (t: 14350) And then we should have all the resources we need. In order to start producing the MK3 assemblers. Now make sure you grab some of these. Or make sure you grab them from the ILS that they are being exported to. (t: 14360) And start upgrading your facility. It shouldn't take you more than a few minutes to upgrade all of these assemblers. And as you can see it's now humming along quite nicely. (t: 14370) Next on the list is proliferation. Because this is going to be super valuable. In order to scale up our science. So MK1 proliferators are made from coal. (t: 14380) Extremely simple to make. And actually super fast to make as well. Then the MK2 version is going to be made from the MK1. Along with diamonds. Which we now have an infinite supply. (t: 14390) Thanks to the kimberlite. And then the MK3 version is going to be made from the MK2 version. Along with the nano tubes. Which again we have an infinite supply. (t: 14400) Thanks to the stalagmite processing facility. That we now have up and running. Now all you need to do. Is put down a row of MK3 assemblers. Of whichever length you want. (t: 14410) And then add in two more rows. For the other colors of proliferators. And of course make sure that you import the earlier version. Into the building directly from the first version. (t: 14420) And make sure that you are requesting the other required materials. In order to put that on a belt. And as you can see it's a super simple belt. (t: 14430) So we have the coal in the back. We have the diamond going through the middle here. And then the nano tubes down here. And then the other buildings are directly connected. And we have the output of proliferators going here. However we are not quite done yet. (t: 14440) Because there is one very important trick to show you. And that is this. You want to actually proliferate your proliferators. And yeah that's a mouthful I know. (t: 14450) But it's actually super efficient. Because what this allows you to do. Is get proliferated proliferators. Which are going to allow you to not only spray 60 times per proliferator. (t: 14460) But 75 times. So by spraying a proliferator once. So you use up one charge. You actually get 15 charges back. (t: 14470) So that is super efficient. And that's just 14 free charges just there. Other than that. You don't necessarily need to go around proliferating. (t: 14480) Every single step in your production chain. It is actually cost efficient. In terms of the raw resources that you need. As well as the energy that you will start saving. (t: 14490) Especially in the longer production chains. But it's also a lot of work. I've done it in my proliferation run. So if you are interested to see a similar type of run. Where I actually proliferate every step along the way. (t: 14500) You can check that one out. But if you just want to be efficient. In terms of your time and effort spent on proliferator. (t: 14510) Just proliferate your proliferators. And then focus on the most important outputs. Now by far the most important places you should be proliferating. Are the in and outputs of your science production. (t: 14520) So you can see over here. I'm actually proliferating the output of the purple science. As well as the inputs. So that means that I'm not only producing 25% more output. (t: 14530) In terms of cubes. I'm also going to be having cubes that are proliferated. That again will produce 25% additional science. Once they reach the processing facility. (t: 14540) So that in all in all is actually going to give us. About 50% more science production. Simply by proliferating. Now all of this extra production that I just showed you. (t: 14550) And all these relatively simple build. Is actually going to request a huge amount of power. As you can see. I'm always getting almost getting to the point of requesting. A thousand megawatts. (t: 14560) And I'm only producing 700. Well 700 is quite a lot already. But that's obviously not going to cut it in the long run. So we need a more efficient way to generate power. (t: 14570) Than all these solar panels and turbines. That more efficient way is the Turin fuel rods. These are actually super efficient. If you carry them around with you. (t: 14580) As well they will power you for a very long time. But there's actually a very good way. To power your base as well. Because you can put them in these mini fusion power plants. (t: 14590) And as you can see. Each one of these will actually generate 15 megawatts of power. So just a handful of these will actually already produce. About 100 megawatts. (t: 14600) Which is a lot more than you can say. For these solar panels. So that's actually going to take a bit of work as well. And in order to be efficient. We're going to be starting out with a build we already have. (t: 14610) Because we have the build for the super magnetic rings over here. And if we check out the recipe for the fuel rods. They actually need super magnetic rings. (t: 14620) Alloy as well as the Turin. Now the Turin we're going to have plenty. And this build is going to supply us with the super magnetic rings. However this is an old school build. So we're going to need to upgrade this. (t: 14630) At this stage of the game. I really recommend that you don't try to reinvent the wheel over and over again. Because well we have a pretty surface of build for these. And we're going to need to make a super magnetic ring. (t: 14640) So might as well use it. As you can see all you need to do. Is delete one of the assemblers. In each row. Because I set it up in that way. And I actually reorganized this bit a little bit. (t: 14650) So it looks a little bit less messy. So I have the smelters now on this side over here. And I have the assembling machines over here. (t: 14660) Now we're going to be adding in a couple of new things. Because of course we also need the titanium alloy. And in order to get that alloy. We're going to need some additional iron. And look how nicely those three smelters that we need in order to make that. (t: 14670) Will fit in here nice and easy. We're going to need a few more smelters though. Because we need some titanium. We need some steel. And we need some titanium alloy. (t: 14680) So the iron is going to go over here. Then the steel is going to go back on this belt over here. The titanium is going to be delivered here. And then in order to make this alloy. (t: 14690) We also need the acid of course. Which is now going to be delivered straight from the ILS. And then a last bit. Not least we're going to be adding in a total of eight assemblers. (t: 14700) Mark three making the titanium fuel rods. This is actually going to be producing fuel rods. Enough for a huge amount of power production. (t: 14710) And it's very easy to replicate this build. So that's why I intentionally keep it relatively small. You could scale this up as well. But this is very practical and nice and square. (t: 14720) And well we're going to be adding some ILS to this to finish this off. Now I've added in another proliferating belt here. As well because proliferating these fuel rods. (t: 14730) Will actually make sure they give a lot more energy. So that makes it a lot more efficient. At the small price of just a small spray. And if you carry them with you yourself. Again that will hugely improve the amount of energy. (t: 14740) You're going to be able to draw from that. There's different ways to proliferate by the way. So I just proliferated it here on the incoming belt. (t: 14750) That goes into the ILS. But an alternative way of doing this. Is actually just have a belt go out your ILS. Flip back on in itself. And then by doing so. (t: 14760) Get the unproliferated stuff out. And get the proliferated stuff back in. And you will slowly see the amount of stacks. Ticking up over here. You used to see it flipping from one to two. (t: 14770) And as this continues to basically filter out the unproliferated stuff. This will turn up higher and higher. So just don't worry about having something proliferated. (t: 14780) At a specific location. You can do it pretty much anywhere you want in the universe. It will remember. Which things are proliferated and which not. (t: 14790) Even if you're transporting them to the other side of the galaxy. So do it wherever you want. And that's most convenient in your builds. Now of course producing all of these fuel rods. Is not going to do us any good. (t: 14800) If we don't have the actual fusion plants to put them in. And this is of course where this tiny little build for the mall comes in. In order to produce the fusion plants. Now you will notice probably at this point. (t: 14810) That we have quite a few buildings. That actually require these super magnetic rings. We also have a lot of buildings that require. Half the amount of the titanium alloy. (t: 14820) So I decided to upgrade the production of that as well. Now at this point you really want to start reusing. Your earlier builds as smartly as possible. So remember the build that we just made. (t: 14830) For the deuterium fuel rods. Well we are already making the magnetic rings in here. So all we need to do is again take out the elements. That are actually related to the titanium alloy. (t: 14840) And there we go. We have a build that's completely up and running. And completely modernized. To where we are in the game right now. In order to make these magnetic rings. Now of course again make sure you add in a little box. (t: 14850) To distribute those things. But other than that this should work just fine. Now you could argue that you only need to take out the deuterium. And then just produce a little bit of titanium along the way. (t: 14860) And that is perfectly a valid way to go. But I'm actually going to make a different build. For the titanium alloy. Because we need quite a bit more. (t: 14870) And the nice thing about the build for the titanium alloy. Is thanks to all the new stuff. That we actually have unlocked. We can make a very neat looking. Very simple build to do exactly that. (t: 14880) So we're going to start out with a total of eight smelters in the start. With a little opening in between. And then we have three rows of twelve smelters next to that. The first eight smelters are going to be dedicated to making titanium. (t: 14890) And then the remaining three lines of smelters. Are going to be dedicated to one resource each. So we need iron to make steel. And we need steel to make titanium alloy. (t: 14900) And all you need to do is add in another belt for titanium. For the acid. And then you have both the titanium as well as the steel. As well as the acid. And you can produce quite a bit of titanium alloy just here. (t: 14910) And then last but not least. I added in a little distributor here in the middle. It looks very cute. And it's very organized. And having that actually in the build itself. (t: 14920) Rather than just sticking out somewhere on the side. And this is going to make sure this actually reaches our mall. Now if you ever want to further expand your production of titanium alloy. You don't actually have to build this on this planet. (t: 14930) You could just build the same build on a different planet. As long as you make sure it has access to warpers. And then what you can do is just set this to remote demand. (t: 14940) That will bring that alloy back here. And then put that alloy in this box over here. So it can be distributed to your mall. It's a lot easier to expand your production on other planets. (t: 14950) And then just add whatever building you want to produce more of. Just add that to the mix over here. Rather than just duplicate everything on a different planet. And with the fusion plants now in production. (t: 14960) You can make a very simple build like this. Which actually will help you power both your existing planets. As well as any planets you want to expand to. In my experience one of these builds with 24 of these fusion plants. (t: 14970) Will give you approximately of course 360 megawatts of power. Which is actually more or less the number that you need for an average mining planet. (t: 14980) Sometimes you need two of these. That's fine as well. If you are making another production planet. With hefty production. Maybe you need three or four of these. (t: 14990) Just make sure that you don't overdo it. Because of course you do need to actually make the Deuterium fuel rods for these. So you actually need to make sure you have enough production of that. (t: 15000) If you don't then everything will grind to a hole. So keep that in mind. You don't want to just spam these all over the place. But Deuterium fuel rods are fairly easy to make sure. (t: 15010) You shouldn't run into too much issues. So basically now we've solved the power issue for the rest of the game. Because you can put this down on any planet. (t: 15020) And the warpers will make sure the fuel actually runs. And the warpers will make sure the fuel actually gets to this build. Then another thing I want to mention that might be obvious. But I do want to make sure you are aware of this. Is that you can now upgrade your offensive platform once again. (t: 15030) It's still the same layout with the missile launchers. And the turrets around with it. I'm still using the same inputs in terms of the ammunition. However I now switch to the Deuterium fuel rods. If you look at the amount of power this is going to generate. (t: 15040) It's actually a huge amount of power. So if you want you can actually do things like adding in a few more. And then you can actually do things like adding in a few more. And then you can actually do things like adding in a few more. And then you can actually do things like adding in a few more. (t: 15050) Actually do things like adding in. Let's say a planetary defense tower should be fitting in here. Let's say a planetary defense tower should be fitting in here. Let's say a planetary defense tower should be fitting in here. You can also add a signal tower to that. (t: 15060) This will have no issue powering all this and if you just duplicate with this a couple of times. This will have no issue powering all this and if you just duplicate with this a couple of times. This will have no issue powering all this and if you just duplicate with this a couple of times. On a planet you're trying to conquer. You should be able to conquer that really really really fast. (t: 15070) You should be able to conquer that really really really fast. So you could actually set the upper air already to. Something like balanced or low. So it actually starts attacking all the Relay stations on the planet. (t: 15080) it down and that just makes clearing out hostile planets a lot easier than if you would do this before you have access to all of this so at this point feel free to start clearing out the dark (t: 15090) fog for wherever they get in your way with the mark 3 assemblers unlocked as well as pretty much infinite power you might be wondering should i start redesigning my entire setup that i already (t: 15100) have and with that we've built over the last four or five six seven episodes the short answer is no well of course you can redesign everything if you want to but the reason i'm standing next to my (t: 15110) blue science production remember from the first few episodes the blue signs and the red science production as you can see it is still working the reason we set it up like we've done is so this (t: 15120) keeps being relevant even into the end game of course we're going to be producing more blue signs elsewhere later on but the blue science production that we already set up is still (t: 15130) plugging along is still working and the same holds for every single other build that we've made even this build over here that's using that's built on the dark fog and that's the same for every other build that we've made even this build over here that's using that's still working and the same (t: 15140) holds for every single other build that we've made even this build over here that's still working and and sorters is still being used because elsewhere in our mall we're using those inputs to build the mark 3 versions for example and all the other things like the simple steel and iron and copper (t: 15150) smellers that we have over here do feed into our mall over here so nothing is going to waste everything is still completely relevant so if you want to redesign it by all means go and do that (t: 15160) but you don't need it you will have no redundant facilities whatsoever but you don't need it you will have no redundant facilities whatsoever there is one facility that's not really doing much right now and that is the science facility we are (t: 15170) of course still processing science but we're not actually producing science in these facilities over here other than a little bit of green and a little bit of purple science but that is of (t: 15180) course the going to be the focus of the next episode in which we are going to use that upgraded platform that i just showed you to take care of a lot of different planets and get a lot (t: 15190) more resources so can really start scaling up our science production and we're also going to need that in order to start building our science production and we're also going to need that in order to start working on our dyson sphere because this is after all dyson sphere program so if you're still here (t: 15200) you're awesome and i do hope to catch you in the next one because it's going to be a lot of fun (t: 15220) hey hey cda and welcome back to this dyson sphere master class as you can see we're nearing the end because we are going to be doing a lot of science and science related stuff so we're going to be doing going to be producing all this juicy white science but in order to get to this point we're (t: 15230) going to need a couple more builds i would also like to thank brian herschel and petrus dragoa for becoming members of the channel you guys are awesome and your support is very much appreciated (t: 15240) now if we're looking at our pretty awesome starting planet as you can see we're making a lot of use of out of the room we have on this planet although we still have quite a bit of room left (t: 15250) honestly so you can expand this further if you want and we actually have a lot of room left to expand so there we havefriendly enough for a coursed look at that great aitors huh (t: 15272) now in order to take because of depension's the saudi arch świsp (t: 15280) um uh this way um like you're not afussia tubes as well as graphene in our builds we're supplying that to anywhere we need it in the (t: 15290) universe so you might as well just request it in just make sure you don't request too much you don't want to have 10 000 lenses stuck in here or anything like that and again make sure that if you (t: 15300) do it through a box like this you only limit the inputs to about one or two squares that's all you are going to need because honestly you don't need that many of them you just need a little bit so (t: 15310) we're not going to dedicate an entire build just to make a little bit more lenses because remember we already have a build like that now depending on how fast you're playing and what you're building (t: 15320) in which order you might feel like your mall is a little bit undertuned when it comes to certain materials or certain buildings for example i always notice that at this point in the game i (t: 15330) need way more drones than i'm producing from that single build that we made a couple of episodes ago unless you're playing really slow because then at this point you'll probably have produced thousands (t: 15340) of them but still i would recommend you to make sure that you have a build like that and make sure that you have a build like that and just building that same little build a few more times and also make sure that you check what (t: 15350) inputs you're low on and if you're low on anything just duplicate the builds you already have or you can make an upgraded version of that of course and stick that in the empty spaces you have remaining (t: 15360) on the planet in my case i just decided to take it easy and double up on the framework production a little bit because you need those frameworks in order to make the silos this was mostly to (t: 15370) speed things up you could just wait for a little longer i actually built a little bit of a build these a little bit too late i should have planned ahead a little bit more my bad um so this is why i'm trying to speed that up but other than that it's completely up to you just figure out what you (t: 15380) need and supply your income accordingly another addition that you might want to consider adding to your mall at this point are the destroyers and corvettes i especially like the destroyers (t: 15390) these are the the spaceships that will actually help you defend your planets against waves (t: 15400) you can actually also try to take on hives with that but taking on hives when they're still well defended is pretty much suicide we don't really have the the appropriate (t: 15410) mechanisms yet in the game to really take them on efficiently while they're still well defended but they are awesome when you're trying to conquer a planet because you will get attacked (t: 15420) your way you're wiping out the planet from space as well and a couple of destroyers will make short work of all those early waves that your enemy will be sending at you one thing worth mentioning (t: 15430) that apparently not a lot of people know is that when you go to a gas giant and you're building these um rows of orbital collectors you can just place them initially and then once you have them built (t: 15440) you can just go to the map view and you scroll around the planet and build them very easily like that so you don't need to fly around in circles around this planet you can just easily (t: 15450) build them alike so now before we transition to white science you want to make sure you have all the non-white science stuff done so especially anything that includes the colored stuff (t: 15460) the dark fox stuff you can do later i actually recommend that you do that later but you're going to need quite a bit of science in order to complete for example all (t: 15470) the license for your stress systems and don't forget to also complete all your upgrades basically you do everything that doesn't require white science now in order to do that we're also (t: 15480) going to scale up our colored science production although we don't want to overdo it because as soon as we're done with all the colored science research we are transitioning (t: 15490) into white science but the bottleneck will be the antimatter and as you can see we probably have quite a bit of science already in our inventory right now green science is the bottleneck because (t: 15500) that's some stuff that we started making last but still we want to scale up this a little bit so we don't take forever to complete that last stuff in our technology tree that said i am going (t: 15510) to show you very clean builds for every type of science because why not um that means you just have a set of blueprints ready to go and scale up whichever type of science you want this of course being a very tiny setup and that's all you need to start producing three (t: 15520) blue science per second why three well honestly it's not going to be your bottleneck you don't (t: 15530) need to produce huge amounts of blue science and this is all you need and if you need more blue science you just copy paste this tiny little build and you're done three smelters making copper five (t: 15540) of them making magnets three of them making iron and that's enough to fully supply one mark three similar making circuit boards and one of them making magnetic coils now you have those flipping (t: 15550) back in here and this is put in the um actual aisle s over here and you actually only need one ils including the demands for the oh i should actually (t: 15560) set this to demand including the demand for the warpers and there you go this is the blue science version well if you thought the blue science build wasn't easy well the red science build is actually (t: 15570) even easier because all you need is 12 smelters making graphite and that is enough graphite to make you three red signs per second now i can hear you going well tda shouldn't you also make some (t: 15580) hydrogen the answer is yes you do need hydrogen for red science as well but i actually think you have so many different sources of hydrogen from your gas giants from your oil production from your nanotube production (t: 15590) stuff like that that it's probably going to be sufficient to rely on that because honestly you (t: 15600) don't need that much hydrogen to make red signs it's just one per cube now you do need to keep an eye on that because hydrogen is one of those sneaky little items in the game that you initially (t: 15610) have way too much of and then as you start scaling up you're going to need a lot of hydrogen and then you will notice that you actually need quite a lot of hydrogen for example we're using a lot of that to make deuterium as well so again that's where your gas giants come in because a lot of gas (t: 15620) giants produce both deuterium as well as hydrogen so long story short keep an eye on that but as long as you do that then this build should be enough to scale up your red signs remember how (t: 15630) big of a pain it was initially to create that first yellow science well at this point in the game it's actually super easy to make a lot more all you need is some smelters in this case i have (t: 15640) a total of how many is this 18 smelters making titanium and then we have a total of eight (t: 15650) assemblers mark three assemblers to be exact making the titanium crystals and the titanium crystals of course need the organic crystals which was the painful part before but now we can just (t: 15660) mine that as a rare resource of different planets so we just bring that in bring in the titanium and export the titanium crystals now of course again you are also going to need diamonds in order to (t: 15670) make yellow science but should be making your diamonds from kimberlite or which is plentiful across the entire universe so there's no reason to make that manually however if you can't find it for whatever reason (t: 15680) then of course all you need to do is add in a couple of smelters that make graphene graphite i should say and then transform that into the diamonds themselves i just didn't add them in (t: 15690) because most of the time they will just be sitting there doing exactly nothing things get a little bit more interesting once we hit purple science however we already made very clean builds for that (t: 15700) so it's also a good idea to replace it with bullet нужно and (t: 15710) with bullet Geb unwrap add people to build the building all right so there you go that's the builds i've done let's add the general stator element in as well like here just to give a (t: 15720) timeline of the building and because everything holding those buildings is actually a bit more away from the actual building when we do the door of the building here so we're going to add moreнали gehe着 대해서 stuff and t-select also switching which i think is quite ideal of all (t: 15730) are twice when you're building the belts and you can make diagonal belts and this actually (t: 15740) connects really cleanly i have no idea why the other connection is still in the game because it looks really ugly um but yeah you do you just keep in mind that if you use diagonal belts like (t: 15750) this this can sometimes interfere with your blueprint working correctly so keep an eye on that but you do you next up is the redesign for the particle broadband build and this is where (t: 15760) the actual benefits of getting all those rare resources coming in is going to be super obvious because this is what you end up with if you take out everything that you no longer need because (t: 15770) you have those rare resources actually you could take out this part as well if you actually have access to the fractal silicon as well as i pointed out before it's not really a big deal (t: 15780) um and as you can see it's not much uh you only need the assemblers uh the nanotubes we are making those uh in the rare resource build the crystal silicon i am still making those here because (t: 15790) you you can the nanotubes that are used for the metal materials for the非常 you can also um Kenya we have any other materials to use and you only use the seminal panels you can have a benefit It's one of those resources that sometimes I find very early on sometimes I don't so I just don't want to bother about that I don't want to conquer a planet specifically just for that purpose (t: 15800) And plastic you do need to make yourself still but that only requires some oil which we're now mass-producing And a tiny little bit of graphite so that's all there's it to it (t: 15810) so now we get to clean this up and This is all we end up with as you can see. It's a tiny little build now rather than a pretty large one (t: 15820) And this is also a good moment to mention that as you can see I have four colors of science here now being mass-produced and we are barely using any footprint at all (t: 15830) We don't need that much building space So this is actually one of the reasons why I prefer just using the normal smelters rather than the planer ones Because you could actually start producing these already you do need quite a bit of expensive resources (t: 15840) so that would mean dedicating more production to frameworks, but especially the Plane filters are really annoying to make (t: 15850) So that's a lot of time and effort going into something that's only going to net you a slightly Faster smelter you're also going to need the unipolar magnet (t: 15860) So that's the last rare item that you can see in this list. You will find those usually around the neutron stars and black holes Those tend to be on the outside of the galaxy (t: 15870) So it's a little bit annoying to get to initially However, if you want to get those and upgrade all your smelters Basically, you can have all of the amounts that we have over here if you upgrade them That's completely fine (t: 15880) Keep it In mind though that even though this smelter is twice as fast it actually consumes about four times as much energy, so you're actually going down in terms of production efficiency in terms of the (t: 15890) Power used the same holds actually for the assemblers, but the assemblers You use in such large numbers that I think it's more worth it (t: 15900) but as you can see all in all the Smelters don't take up that much space, so it's completely up to you, but I don't recommend necessarily transitioning to plane smelters at all (t: 15910) So one more color of science to go in that is green and well We actually already made most of that because we already upgraded our constant ships builds Which is one half of the part that we need in order to make green science now again here (t: 15920) You can see we are using a ton of different assemblers We only using a fairly small amount of stuff for the actual smelter so reducing this by half would cut in (t: 15930) The size of the blueprint, but it's not going to be very significant if you compare that with using the lower tier of the assemblers (t: 15940) Then there is one more build that we need to upgrade and that is the one for the graviton lens So this is one that we made in the previous episode We can cut out the entire bottom half now because this is all related to rare resources (t: 15950) We're making the diamonds elsewhere We're making the graphene elsewhere, and we can also cut down a lot on the number of assemblers over here (t: 15960) And if you do that you end up with a very clean square looking build like this and again There's a huge amount of assemblers in here And you could cut down half of the (t: 15970) Of the smelters over here if you want to but I really like the looks of this build as it is now that means we now have a nice set of (t: 15980) Different blueprints that will produce all the different colors that we need it's interesting to see that the left half sorry the right half over here actually produced four different types of colors of science and (t: 15990) The left half is completely dedicated to the green sign so there you can see the complexity of green signs compared to everything else But yeah, this is all you need to scale up the science as much as you can (t: 16000) Keep in mind that there is a limit to where that is pointful because well You're going to need quite a bit of science in order to unlock things like the Dyson sphere stress system and a couple of the (t: 16010) Upgrades later on and the tree over here are relatively expensive as well but as soon as you complete all of that research you're going to hit the white science and (t: 16020) White science will require us to have a Dyson sphere which of course is what we're going to do next time And that is really really fun But as soon as you complete all the green signs (t: 16030) Related stuff in your onto white science it will actually take quite a while for you to scale up your white science production So don't overdo it in terms of scaling up your base production. Maybe just build every build (t: 16040) Once maybe twice, but I would stop there and then focus on getting your Dyson sphere up and running But all of those nice clean builds done (t: 16050) We might as well fill a couple of them on our starting planet because well honestly most of them don't take that much space I'm actually going to limit the purple and green science production (t: 16060) To the secondary planet because well that's where the rest of that production is as well just to keep myself sane Depends a little bit although Where you have most of your power because if you do have surplus power on one of your planets you might as well put that to (t: 16070) Use and by doing so we're going to scale up all this science production and make sure we have plenty of science waiting for as (t: 16080) Soon as we unlock that antimatter You know don't underestimate how much power all of this is going to cost you as you can see I'm sitting at almost two gigawatts of power production on our starting planet (t: 16090) At this point, but we're also consuming almost at the same amount so Between all the production that you're doing this planetary shield and stuff like that You want to make sure you have enough power because again if you're going to scale up your production (t: 16100) But you don't have enough power. You're not scaling up anything. You're just building buildings that are not going to add anything You're going to need quite a bit of resources in order to actually (t: 16110) Build all of the stuff that we're going to be building in this episode, so don't forget to expand your production as you go now (t: 16120) These offensive hubs that we built in the last episode are going to be super useful because as soon as you powered them on You should be starting to destroy the relays make sure you have a couple of these (t: 16130) Missile turrets set to attack upper air as well You could actually have a couple of them I don't recommend doing this with all of them but a couple of them set to prioritize this and actually set everything else at balanced which made sure that you also always have a couple of these firing at any relays (t: 16140) that that are in range and as soon as you take down the relays this stuff will stay (t: 16150) start powering down it makes it so much easier to conquer your planets however as you can see you will get attacked from space so this is why there is the planetary shields included in here (t: 16160) if you haven't done so yet at this point you really should start finding yourself a planet that has a grating crystal deposit on it so you can start mining that so you can start mass (t: 16170) producing those awesome advanced mining machines they just make spreading routes across the galaxy so much easier than doing it with all these manual miners setting up belts and stuff like that (t: 16180) so make sure you take advantage of those however don't worry too much about actually gathering a huge amount of resources because well you actually need the builds to consume those as well and (t: 16190) actually building a dysosphere takes forever so we are going to get started with that and we're going to do that on the third planet in our system so this is the one closest to the sun (t: 16200) mostly because that has an easier time to actually build the dysosphere because a it's closer and bigger and it's easier to build the dysosphere because it's closer and bigger and bigger and it's closer and bigger and bigger and bigger and bigger and bigger and bigger and bigger and bigger and it has a higher uptime because it has an easier time getting the sphere in range but there's also (t: 16210) a very practical reason there's nothing on this planet yet other than some mines at this point so we have plenty of room to build over here now in order to build a dysosphere we're going to need (t: 16220) a lot of solar cells which are pretty straightforward to make and we're also going to need rockets and especially rockets are unsurprisingly the most complicated item in the entire game (t: 16230) however we already have a couple of building blocks in order to build this because we are already producing quantum ships and we're already producing deuterium fuel rods so all we need to do now is start (t: 16240) producing dysosphere components that means that this is actually going to be a bit of a martyler build in order to make sure that we make full use of all the designing work that we already did (t: 16250) step one we are going to make a lot of solar cells specifically we're going to make a build that produces 12 solar cells per second if that sounds like a lot well yes it is but you're (t: 16260) actually going to need quite a lot of those and half of that is actually going to go into our rocket production in order to make a lot of solar cells and we're going to need a lot of those and to get that going we have a total of 18 glass smelters over here we're going to add in three (t: 16270) smelters making iron and two smelters making copper then we're going to add in a single mark (t: 16280) three assembler making circuit boards and then three more making prisms and we're almost done already because all we need to add in are six assemblers making photon combiners making use of (t: 16290) those circuit boards and prisms and then eight more assemblers making the actual solar cells now if you've done your math you'll notice like hey uh tda these are not actually (t: 16300) producing 12 per second this is only six per second also i want to point out that you're going to need to bring in those that graphene because you know these things actually need photo combiners and (t: 16310) graphene now you will be right but all we need to do is basically copy this flip it around you can actually do it very easily like this so just copy paste this flip it around place it down here (t: 16320) somewhere and try to align it a little bit so that the set of buildings is actually aligned and then place an ILS in the middle and there you go now we (t: 16330) have our entire build done and this is going to fill up really really fast when all these solar cells were ever going to need now the second thing we're going to need in order to make our rockets (t: 16340) are the deuterium fuel rods and of course we made a build for that in the previous episode already all you need to do is copy that in twice and of course make sure you have enough materials in (t: 16350) order to supply that similarly you need the upgraded quantum chips built in order to make enough quantum chips for the rockets themselves as well and again you're going to (t: 16360) need that build twice this is the exact same build that we made in the previous episode so just copy it in twice and if you look at it look at how nicely that actually aligns with the other builds (t: 16370) that we have so we have the solar cells over here the fuel rods over here and the quantum ships over here and it looks really nice and tidy now we're going to need to make the actual Dyson Sphere (t: 16380) components and for those of you that like using the planar smelters this will be the moment to get them out although I'm still going to use the (t: 16390) mark once as you will see in a moment we're going to need a lot of smelting. We're trying to make one rocket per second and in order to do that we're actually going to need 60 smelters making (t: 16400) silicon as you can see here so I have four rows of 15 smelters. Next up on the list are 30 smelters (t: 16410) for the iron and I'm actually going to leave a little bit of a line in between because we are going to need to bring in some other resources as well. For example we are going to need some (t: 16420) copper production and these are 18 smelters making copper ingots. The reason I didn't put them in a line with this is because these are rows of 15 and these are rows of 18 and we're (t: 16430) going to need some more of those. Specifically we are going to need 18 smelters making steel (t: 16440) and you guessed it we're also going to need 18 smelters making titanium. Now of course we also are going to need to produce the titanium itself. I'm assuming we're going to (t: 16450) build that over here and this just shows you how many smelters we're actually going to need. Keep in mind though that if you replace these with planar smelters you're actually going to use (t: 16460) twice as much energy than just using the mark one version so if you're short on energy make sure you're careful about that. We're not entirely done yet with the smelters yet though because we still (t: 16470) need 12 smelters making titanium and if you're wondering why I'm leaving empty space in the build I'm going to add in a lot more belts down here in order to funnel in the end product. (t: 16480) But before we can get to that let's continue up here because we are going to need to make some other basic stuff. This part of the build might seem really familiar to you because we're going (t: 16490) to add in four of these assemblers making circuit boards and then we're going to add in 16 assemblers making crystalline components and as you can see it all aligns quite nicely with those rows (t: 16500) of 18 of these smelters. It's not exact but it's close enough because these buildings are just a (t: 16510) little bit more wide than the smelters are because we're not actually leaving room in between. You can just build them as a row. It's close enough in order to keep this thing as square as we can. (t: 16520) Of course where crystalline components are the processes are not far behind so that's the next (t: 16530) thing we're actually going to be building. You're going to need a total of 12 and that makes sure we have a nice little square. Here producing the processors. Now did I start this build very close to a vault line? Yes I did (t: 16540) and I think we're going to add in some more assemblers over here. So whenever you get into a situation like this you have no choice but you just save the build, make a blueprint out of it, (t: 16550) then delete the build and then place it wherever you need to move it to because well you don't (t: 16560) want to build your blueprints across vault lines. If the sheer size of this build it wasn't clear to you just yet well wait until we start adding in 24 Mark III assemblers making framework. Well (t: 16570) that's a lot of resources that you're going to need to produce all of that hence all the smelters. So don't underestimate how much this is actually going to soak up in terms of raw ore. However with (t: 16580) all of that framework and production we can bring down the processors and framework down here to the (t: 16590) place where we're going to be building the Dyson Sphere components. Now as you can see I added a lot of the process and framework that we need to build. So we're going to add in a lot of the process and framework that we need to build. So we're going to add in a lot of the process and framework that we need to build. So we're going to add in a lot of the process and framework that we need to build. So we're going to add in a total of 11 of these down here and we're (t: 16600) also going to be bringing in the solar cells from the other build. Now I've already built a belt here but there's no ILS yet to bring them in. That will come in a second. Because first things (t: 16610) first we're actually also going to be producing the actual rockets in here. We're going to need four assemblers making the rockets and as you can see everything nicely aligns with each other. (t: 16620) So this looks like a pretty decently organized build. Now in order to actually build the rockets we're going to have to bring in not only the solar cells but also the fuel rods and the quantum (t: 16630) chips. So now we're going to add in some ILSs. And when I say ILS I just mean for the raw resources because you only need a PLS or a planetary logistics station in order to bring in those (t: 16640) three materials that we're making elsewhere in this build. Because remember this is a modular build. So what you want to do is actually set the receiving ILSs over here and set them to (t: 16650) remote storage. So not to remote demand or remote supply. Just set them to remote storage. This will ensure that this will actually not export off this planet. (t: 16660) You can still set this to local supply because well we actually want this to be locally supplied specifically to this PLS over here in the rocket build. But you don't want this to be starting to (t: 16670) flying all over the planets that you have elsewhere. Now this will not really happen with the solar cells to begin with. But for example with the where is it with the quantum (t: 16680) chips. So now we're going to take the solar cells from the rocket and we're going to (t: 16690) put them on the ground so that we can actually export them. So if you're using one of the four modules. Those will actually get exported because they're also part of your science production. But if you set it to remote storage it will produce a little bit and then just (t: 16700) stop producing until you actually need it. So if you want this to also be used towards your science production early on that's fine but as soon as this build is done I recommend (t: 16710) going you're going to need a launching facility in order to make sure you can actually use all those solar cells and all those rockets and it should look something like this i really like (t: 16720) building them at the poles because that makes just a very convenient location to make a build like this and let me walk you through what i have over here so first of all we have the real guns (t: 16730) that are set to launch our solar cells up into the sky as you can see it's only a couple of them actually working right now apparently there's some very weird angle towards where we actually need to (t: 16740) go but ideally they should all be firing at the same time um then second of all there is the array receivers that we have over here you can see that there is lenses on this belt over here so putting (t: 16750) a lens into a ray receiver makes it super more efficient and one lens will last you for a very very long time so even though lenses themselves are pretty expensive to make it's definitely worth (t: 16760) getting them into your array receivers just for the additional efficiency then on the outer ring over here we have a long range and we have a long range lens that is only available for the most of the time that we have over here so you can see this is the range and we have the long range lens (t: 16770) here and we have the long range and we have the long range and we have the long range lens so we have have the missile silos of course these are launching all the nicer rockets that we are now producing up into the dyson sphere and this is before the time where you could run belts through your building so even though it looks like you should be able to do this and (t: 16780) hopefully that is something that will change in the future for now we still have to manually get them in with a sorter then i also added some planetary defenses to this just to make sure (t: 16790) that this doesn't get blown up by the enemy and last but not least in the middle over here we have this facility with all these particle colliders now this ils in the middle is going (t: 16800) to request the critical photons so all of these ray receivers are not set to power production because honestly that's kind of a waste you can make power in tons of different ways but you can (t: 16810) only make critical photons in one way um and all the critical photons from the ray receivers are going to go into the ils over here now the ils is going to transport that into a circular (t: 16820) belt over here then the particle colliders are going to transform that into antimatter and hydrogen so remember you also need somewhere for the hydrogen to go and just to make (t: 16830) sure this doesn't actually get backlogged anytime soon i just put in some additional belts just in (t: 16840) case the hydrogen is not actually being exported it should be exported if it's not you're not making enough ethereum and you're not making enough deterring fuel rods which you should be making anyway to power all your planets i'm just saying make sure you have a backup system just in case (t: 16850) now that's pretty much all there is to it you could honestly remove probably like three out of every four of these particle colliders over here because (t: 16860) as you can see they're not really busy and it's a pretty fast recipe so you can see the antimatter (t: 16870) going in here or sorry the critical photons getting in here and they're not even reaching like the third or fourth particle collider so of course you are going to scale up your production in the end game if you're continuing to play so this is mostly meant for that but you're only (t: 16880) going to need like a handful of these particle colliders if you're once you're starting out in the beginning one trick that might not be obvious to everyone initially is that you (t: 16890) can actually proliferate your rockets and if you do that will actually allow them to be launched a (t: 16900) lot faster that's the only thing that's going to do just launch them faster but honestly launching rockets can be really slow especially if you already have a pretty solid rocket production (t: 16910) and you're starting a new sphere for example so just a little trick to be aware of that might be very useful if you're trying to scale up your production even further speaking of starting out small and scaling up i would really recommend that you start out with a (t: 16920) small sphere now the reason for this is twofold first of all it's going to be a lot more satisfying (t: 16930) to actually see your build coming together and if you make a large complicated build it's going to take hours upon hours for that to be completed especially considering we don't have a massive (t: 16940) amount of rocket production yet i mean one rocket per second is actually pretty good you can get me wrong but still it's going to take you quite a while to complete your build and you're going to need even a small sphere like this the second reason is that if you look at a lot of guides and (t: 16950) other content creators they they point out that if you make a large sphere and you make it large enough so you can have a planet within the sphere which our closest planet actually is you could make (t: 16960) a sphere that actually envelops the planet that we're currently on um sure that's a benefit but (t: 16970) you can achieve the same result by just making a large swarm so as you can see i have a swarm at the moment just to make sure that i can easily um get solar cells into the swarm and then the actual (t: 16980) sphere itself is just tiny and remember you can add more and more layers to this form you can (t: 16990) edit the layers that you already have you can do whatever you want with this and expand on that just start out small and scale it up later as you go also td8 from the future here i initially (t: 17000) forgot to actually proliferate the antimatter so make sure you don't forget to do that yourself then back at our science hub we're actually going to transition this science hub from normal science (t: 17010) production or i should say research to white science production itself and this is the beauty i think of this design because we have had this secondary or a tertiary belt i should say on the (t: 17020) outside from the get-go and we also have this empty belt sitting here uh on the on the inside (t: 17030) that we haven't actually been using so what we're going to be doing is we are going to add in and request the antimatter from here so we're going to place an antimatter here in search of the another planet (t: 17040) then we have some professed料 on here of course we have to ship this up later so we are just templating out in the wild lands and off oldest and then we are going to pack (t: 17050) this up so we have to get itvi this just apply for theadeckly to strange all this stuff into here as you can see we have four gorilla if quatro yo go are probably from the other planet (t: 17060) and we're also going to set a white science to be supplied here then on the outer belt we can have all the way around collect the cubes if you want or just delete them and put them back in the (t: 17070) actual inventory that might be a bit of an issue that by the way because you can see i have a lot of science production waiting for me already now make sure that before you do that you actually (t: 17080) completed the entire technology tree because you can always shift it back of course but that's just tedious so i would recommend that you make sure you complete anything that you want that actually (t: 17090) requires the colored types of science then next step we are going to transition the build over here on the side this is going to start actually collecting the science and that means that all we (t: 17100) need to do is put the white science here let's say over here and we are going to be requesting that (t: 17110) so either locally or remotely doesn't really matter and then put the outgoing stuff on a belt like this again make sure you proliferate that because if you don't (t: 17120) that's just going to waste you a lot of science and this facility over here it should be able to take care of all your technology resource for a very very long time and there you have a look at (t: 17130) all that white science now in production as you can see after the initial burst of collecting all (t: 17140) the antimatter that i had stored up and you can see it's slowing down slightly now and that means that we are now pretty much into the end game late game whatever you want to call it all we need to (t: 17150) do is put the white science here and then we are going to put the white science here and then we need to do is put the white science here and then we are going to put the white science here and then we now is scale up the dysosphere even further scale up our rocket production even further potentially find a nice high luminosity sun somewhere and actually build a another dysosphere (t: 17160) across that one basically you just keep repeating what you have already been doing and just scale things up even further now i have to admit that is not my favorite part of the game because it's (t: 17170) basically just repeating what you've already done but if you like seeing your factory grow then you can make completely planet-wide (t: 17180) um the blueprints with rocket production for example even more science production and take it from there all you need to do is make sure that you supply it with enough resources now (t: 17190) let me know in the comments is there anything else you would like to see in this master class i mean we can continue on if you guys want but this will allow you to pretty much complete the game (t: 17200) uh get the you win the game pop up um something fun that i recommend that you do at this point is making sure you unlock all the upgrades to your ground fleet and your space fleet (t: 17210) and you can do that by just clicking on the blue button and then you can see that you have the ability to do that and you can also do that by just clicking on the blue button and you can do that basically will turn yourself into a one-man army so that is something we could explore in one of the next episodes or anyway let me know in the comments what you would like to see if you're (t: 17220) still here you're awesome and hopefully by now you have also completed the game so that is very awesome and i also want to highlight all the people that have been joining a hdda and today (t: 17230) because several of you specifically asked for this i'm going to show you exactly how to design your own awesome dyson sphere that means that this will be a very interesting game for you guys to play (t: 17240) this will be a short episode which will basically serve as an addendum to the previous episode in which i showed you exactly how to produce massive amounts of rockets and solar sails (t: 17250) and how to make use of them if you missed that one don't forget to check it out as well now i'm going to start out with just a blank slate system so i can show you everything from the start and that allows me to give a shout out to some of our newest members of the channel so ryan herschel (t: 17260) tme789100 and luxan thank you very much for becoming members of the channel your support is (t: 17270) very much appreciated and i'll see you in the next one bye bye appreciated so let me walk you through the actual interface before we start building so you know what you're looking at first thing you want to do is probably press this button over here so you can (t: 17280) stop the time in your game it's a lot easier to design your dice where things aren't moving around then here over here you will see the name of your system now if you have multiple spheres in (t: 17290) different systems you can actually use this as a drop down and select whatever dice sphere you have in other systems as well i'm currently in an empty game so there's no other system showing up but this (t: 17300) will allow you to choose which system you want to use and if you want to use it in a different system then it allow you to check on your dice spheres even if you're on the other side of the universe because at some point your sphere might be done building you might want to add something to it it's really annoying if you have to fly up and down the entire universe in order to do that so (t: 17310) make use of that once you have more than one then over here you can see the list of your dyson swarms the dyson swarm are all those little solar cells you shoot up in the air and that start (t: 17320) floating around in one large swarm hence the name and as you can see you can define multiple orbits with it so you can actually have up to 20 different swarms and different angles with different colors (t: 17330) we'll get to that in a moment and you can use that to make very interesting nice looking designs if you want you can actually allow them to be shown in the editor you can turn this off if you don't (t: 17340) want to look at all the different orbits and you're going to kind of want to figure out what (t: 17350) you're doing with the one you're designing but most importantly you can also turn them off in game and this is especially important if you're trying to kind of save your own performance because with a lot of swarms and a lot of spheres flying around it does put a heavy toll on your (t: 17360) system so turning them off will get you a little bit further before your pc melts then over here (t: 17370) we have the dyson shells which are the parts of the sphere that are going to be built by your rockets so not the cells but only the rockets and again you can add up to 10 different layers (t: 17380) now again you have the option to show them in the editor in the game and especially the editor you want to turn off your layers every (t: 17390) now and then because it makes it very hard to see what you're doing if you have multiple layers going on the same holds for the back structure this makes it a lot easier to actually see what you're (t: 17400) doing if you're building something in the forefront because of course a sphere is around so once you've built one half of it you're going to design the other half while you're also looking at the back half so yeah just makes it a little easier to see what you're doing and then hide your rocket models (t: 17410) is not a problem at all so you can just add a little bit of a layer of the back structure and then you can add a little bit of a layer of the back structure and then you can not really something that's hugely impactful but because you will see those rockets flying around and actually start populating your sphere and turning these off will remove that but it's not (t: 17420) really a big thing then both of these have a button for a blueprint so once you make a design (t: 17430) that you really really like you can just copy and paste that to a different system makes it really convenient when you have to design the thing once and then last but not least we have some (t: 17440) interesting information on the right hand side so there's the name of the star one so you can see the name of the star one so you can see the name of the star one so you can see the name of the star luminosity and that is actually the strength of your star in the starting system it will typically (t: 17450) be about 1.0 just like you have over here but there's going to be stars that can go up as high as something like 2.4 2.5 so typically there will be about four or five typically blue stars in your (t: 17460) systems that will get a luminosity above 2.0 and that's going to mean you're actually getting twice (t: 17470) as much luminosity as you would if you were to have a star luminosity that's going to be about 2.5 much power from that star as you would get with a similar identical dyson sphere in a starting system (t: 17480) so if you want to optimize the output of your dyson sphere make it at a high luminosity star however i would definitely recommend just building a dyson sphere in your starting system as well (t: 17490) just because you're going to be spending a lot of time there and it's just a lot of fun looking at a nice looking hyacinth sphere so don't just optimize the fun out of the game make sure (t: 17500) you have something nice to look at as well while you're playing the generation capacity hopefully speaks for itself so this is the amount of power that your sphere is going to be generating you can make a blueprint for an entire sphere which is (t: 17510) going to include both the orbits as well as the shell and if you're playing on sandbox mode like i am currently now just because i wanted to show you these buttons you can actually just instantly (t: 17520) build an entire dysosphere kind of takes the challenge out of it but it's there if you want it if you're in sandbox mode then this information hopefully speaks for itself as well so i hope you (t: 17530) well but this is just the amount of solar cells that are flying around the nodes in total so this is the nodes that you're building with the rockets and then you're going to be able to see how much (t: 17540) power your swarm and shell are generating compared to how much power you're requesting so the (t: 17550) requested power comes from the ray receivers on your planets that are trying to draw energy from your dyson sphere and of course that's very easy to scale up it gives just a handful of ray (t: 17560) receivers fully powered will draw a lot of power from your sphere but you can see kind of the balance between the two typically you want to make sure that you're at least drawing as much power (t: 17570) as you're generating because otherwise it's just going to waste now we have a couple of buttons here that you're not going to be using that much again this is a internal solar cells button only (t: 17580) for sandbox mode basically make sure that your solar cells will never die but normal sermon (t: 17590) stands that you will have a graph showing here that will show you how long your existing solar cells have to live i'll show you a demonstration for a more advanced half partially built a dyson sphere later on where you can see that in the bottom over here there is (t: 17600) an optimized solar cells memory button which basically allows you to kind of make sure there's (t: 17610) no memory leaks going on with your solar cells this is only something that can happen in a very late game but you might want to press this button if you're having some optimization issues but (t: 17620) honestly just hiding this button is a good way to minimize the amount of power that you're generating and you can see that the battery is running out of battery so you can see that the battery is running out of battery so you can see that the battery is running out of battery so you can see that the dyson sphere is probably going to do a lot more for your performance uh the removing of free solar cells means that sometimes you will have these random solar cells floating around that (t: 17630) aren't actually part of your design for example because you removed a orbit from your design (t: 17640) but sometimes it also happens kind of randomly not entirely sure why that is uh this will just clean that up so this is a very convenient button to have and then if you're a maniac you can also just destroy all your solar cells i would not recommend doing that (t: 17650) okay enough talking let's actually design a dyson sphere so um let's start with a swarm you can add an orbit just like that and it will generate a random one for you just in case you couldn't think (t: 17660) think of any yourself now the orbit radius is going to determine how large your sphere is now (t: 17670) as you can see it will turn red every now and then at least if you have planets close to your sun uh you cannot have your swarm moving through your planet for obvious reasons ideally you would want (t: 17680) at least one of your solar cells to be moving through your planet and that would be the same as you can see here so you can see that the orbit radius is going to be larger than your planet because then if you build a planet on top of i'm sorry a ray receiver on top of that planet (t: 17690) it will always have contact with your dice it will never be out of reach so just 100 uptime (t: 17700) guaranteed if you do it like that so this is just a little trick honestly it's not hugely important that you make use of that um because honestly if you have a large enough dice of sphere and you (t: 17710) build your collectors on the outs outdoor planets of your entire system uh typically you will have a very high uptime anyway so don't fuss about it too much if you don't like a large sphere (t: 17720) but if you want to optimize things that's what you should be doing um so just put it at any size that you want the inclination is going to determine (t: 17730) the angle in which the solar cells are going to fly around so the circle that you see moving now is the path that your solar cells are going to follow around the sun now why is this important (t: 17740) because what what you're doing is actually makingbec use of this angle to say okay this (t: 17750) is what you're doing well you are going to be frozen and you're going to end up floating now let's say you have this path you're going to be frozen and you're going to end up like a zocalypse with your travaleno and you know okay you can basically turn that angle his or her (t: 17760) better he shape uh you have this interior and this тамẹo um or this ambial that you could use these ambial for right object espa and at the end and things like that so everything that we're (t: 17770) moving around if you turn them around this means that they will also be kind of moving clockwise or counterclockwise so this is one way to flip them around if you flip them around like this (t: 17780) they will moving be moving in exactly the opposite direction than if you're moving them like this just something that you want to take into account if you also care about the motion of your solar (t: 17790) cells once you've made your decision on your orbits you can always just edit them later on (t: 17800) so you can click these gears over here and you can change all the settings that you initially changed to whatever you want now this will mean that your solar cells will take a moment to (t: 17810) actually fly to the correct orbit once they're already up in the air so you can see your your solar cells moving across your entire galaxy uh if you start messing around with this it's actually (t: 17820) really fun to watch uh but it will mess up your design a little bit if you do that of course so make sure that you um take that into consideration it's better to do this correctly the first time (t: 17830) around the solar cell color is something that was added a little later in the game so some of you might not have seen this yet it's a really interesting way you can pick whatever color you want from the entire spectrum um and make your own rainbow set of orbits if you want or something (t: 17840) like that you can actually get really creative with that you don't need a dyson sphere to have something very nice looking and you can do this really early in the game as well just make sure (t: 17850) that you don't run out of things like silicon before you actually reach green science because that's going to be a bit of a problem but i think that's something that you can do if you're a fan (t: 17860) okay and then we get to the real meat and potatoes of the dyson shell itself so you can add a layer to that and it works exactly the same ways for the dyson sphere again you will see that you can't (t: 17870) put it too close to the planet orbits you also cannot put it too close to existing layers of the dyson sphere itself so that might turn red sometimes more often once you're building multiple layers (t: 17880) again if you build it very large you can always have direct contact with your sphere but it will take you a very long time to build it and you can't build it very large you can always have direct contact with your sphere but it will take you a very (t: 17890) long time initially to build a very large dyson sphere so if you like aesthetics as much as i do i would recommend starting out with something that's a little bit smaller smaller and more um humble basically before you start building huge constructs especially if you're building (t: 17900) something in the starting system you're going to want to expand to a high luminosity star at some point anyway so you might as well save your huge creations for that one (t: 17910) by that time you will also be producing a lot more things like solar cells especially but especially (t: 17920) so before you have a massive amount of rockets you start out small that's my recommendation at least you do you okay so how do you actually do this well first of all you again design a size (t: 17930) a inclination this is again going to determine the rotation of your dyson sphere so even though you can make an entire sphere the sphere itself will still be rotating and this is going to (t: 17940) determine how that is so this is going to allow you to flip that around etc just like the dyson (t: 17950) but take that into consideration when you're building your layers because it will impact the movement of the different layers compared to each other so yeah it impacts what you're (t: 17960) looking at basically so you create one then there is a huge interface that pops up where you can really go nuts in terms of designing your sphere so you can set this to different layouts which (t: 17970) can help you kind of uh figure out what type of design you want i really like (t: 17980) personally the the default one like this because this allows you to make easy circles um but yeah if you want to go for a bit more complex design this will really help you make (t: 17990) sure that you have like an even looking symmetric design if that's what you're going for um it basically helps you divide the the ball into different parts it looks kind of looks like (t: 18000) a tennis ball like this anyway pick whatever setting you like like i said i i like going for this one and then you can either start placing individual notes like this (t: 18010) which is really tedious but this is one way you can do that however the faster way is to make the actual frames now you can have the geodesic frames or the uh grach q frames and (t: 18020) the easiest way to look at how these work is just putting them down and seeing it like this so it's it's a little it's a little hard to tell but these ones are actually going to be straight (t: 18030) lines and this one will curve along the edge of your dyson sphere i i really like using these but these ones are actually going to be straight lines and these ones will curve along the edge of your dyson sphere i i really like using these (t: 18040) But there could be use cases for these as well. It's completely up to you what you like the looks of better. If you use this, this will actually connect the different nodes straight away. (t: 18050) So you don't have to fill them up manually afterwards. And there's different designs of these things. This is just going to determine how it looks. (t: 18060) If you zoom in a little bit, you can see there's a different look between this part and this part. It doesn't do anything in terms of the actual production value of your sphere itself. It's just about how they present themselves in the game. (t: 18070) So basically what you can do now is make a nice looking pattern across your Dyson sphere. Maybe work something with triangles. (t: 18080) That's kind of my go-to, honestly. But you can make all kinds of different interesting shapes. I'll show you a couple of examples near the end of this video. Now, set this up in whatever way you want. (t: 18090) Turn around the camera like this and stuff like that. Sometimes you have to kind of fight against the camera. Kind of get stuck sometimes, in my opinion. But you should be able to just turn it around. (t: 18100) And again, if you do it like this, for example, you say, hide the back structures. It makes it a little easier to work in the foreground. (t: 18110) If you don't have to look at all the nodes in the background as well. So this is just a way to make your life easier. Like I just said before. Then last but not least, in terms of actually designing the sphere itself. (t: 18120) You can fill up these holes if you have. You can make them too big. But as long as they're sufficient. Sufficiently small. You can fill them up with solar cells. (t: 18130) Now, all of these different designs are again just aesthetics. You can pick whichever one you want and then just fill these in and have the Dyson's forms populate them accordingly. (t: 18140) It's a little hard to tell that they're actually different. But if you zoom in close, you can see these are kind of the. The rectangular walls. (t: 18150) These are triangles and stuff like that. So. This will again just impact the visuals. But putting these. These areas in here kind of painting these in is super important because this is again. (t: 18160) Where your Dyson's forms are going to fill up and and that will make them permanent. If you don't do this, you're just going to have an empty frame. (t: 18170) And you're going to waste all your solar cells. So make sure that you fill at least a couple of these frames up. You don't need to fill all of them up. You can actually leave gaps in your Dyson's sphere as well. (t: 18180) Looks really nice. It's not necessarily full. But it's not necessarily a problem. So. It's not necessarily functional, but aesthetics matter too. (t: 18190) But make sure you don't forget about this. Now, the really fun part is that you can also give this colors. So you can pick again whatever color you want. And you can paint both the. (t: 18200) The frames as well as the actual construct itself. So you could make that in a completely different color and paint it like this. (t: 18210) You can paint individual notes. You can go completely nuts with this. You can make your own monstrosity in terms of colors. This is also where the blueprint function comes in. (t: 18220) Because if you make a design that you really, really like. Then by all means, copy and paste it to a new system as well. (t: 18230) So you don't have to do it all over again. Now, of course, there's also kind of a painting mode. This is this one where you can go even more nuts. (t: 18240) Again, use this in whatever way you want. You can actually increase the brush size. So if you have a very large area of your Dyson's face. (t: 18250) You just want to paint them one color. You can do that like this. The notes sometimes need a little bit of special attention. Makes it really easy. And now you might be wondering like this doesn't look anything like the sphere I just designed. (t: 18260) That's correct. But it will only apply this color to the parts of the sphere that actually exist. So this is just an easy way to fill in a large area. (t: 18270) But it's not something you would typically use if you want to make a more detailed, smaller design. Now, of course, you can also highlight this. So this is even worse. And make it like neon colors. (t: 18280) Really nice and shiny and bright kind of hurts your eyes. But it makes your sphere stand out a lot in the darkness of space. So use this at your at your own discretion. (t: 18290) But it's nice to know that this is here. So this really brightens up your your sphere. And then last but not least, of course, you can also remove all of this. (t: 18300) You're like, nope, nope, nope, nope. This is not what I wanted. And you can do it like this. And you can actually see that the underlying color. Show back up. So this is an easy way to get rid of the huge painting thing that I just did. (t: 18310) And here is just a couple of examples of my own spheres to give you some inspiration or at least some idea how this will actually work. (t: 18320) So what I've done over here is just have a couple of different orbits of five in total, which are all at the exact same radius from the star. (t: 18330) But all at a different inclination. And by basically spinning. And spacing them out at equal distances, you get a very nice looking kind of a sphere already. (t: 18340) Just give them all a different color and make your own rainbow. So very simple, but sometimes simple is very effective. Then in the middle over here, I actually have my one of my first spheres ever, I think that I designed over here. (t: 18350) As you can see, I went a little bit creative with leaving some open space. So there's a circle on top and there's kind of a row around that. (t: 18360) And there's kind of a zigzag going on in the in the middle over here. These areas have absorbed a lot of solar cells, so this is actually going to be generating a lot more power. (t: 18370) As you can see over here, the Dyson Swarm, even though there's tens of thousands of solar cells in there, the Dyson shell itself is actually generating far more power. (t: 18380) And this is kind of the core of my shell. Now, to make it a little bit more interesting, I added another layer to this and I'll show it in the editor now. (t: 18390) It's almost empty, but it's just mostly meant to make things look more interesting. So I'm going to show you a little bit more of this. It's also a very large shell. So what this does is make sure that there is a point of contact for my other planets around to make it really easy to connect to the power production of the shell. (t: 18400) You can imagine that if I filled this up completely, there will be a, I don't know, a million, but at least a lot and a huge amount of solar cells going into a huge sphere like this. (t: 18410) So that's a very ambitious project. (t: 18420) If you started, but if you actually want to see it completed in your lifetime, I would recommend keeping it a little bit smaller, but you can do whatever you want. (t: 18430) This is just a random design. So let me show you another one. And here you go. Another design that I really, really like. (t: 18440) Actually, it's kind of a flower design with all the different colors on top and spreading out as we go downwards. And we have this, this hugely bright solar storm over here in the middle. (t: 18450) This looks very zen. I don't know. It makes me very calm just looking at it. And as you can see, you don't need to make it complicated. You also don't need to make a hugely complicated large sphere in order to generate a lot of power, because as you can see, this one is already generating almost 22 gigawatts of energy. (t: 18460) So that's a huge amount. (t: 18470) And this is partially helped by the fact that this is on a star that has a higher luminosity. So 1.6, definitely not the highest you can get, but it's pretty, pretty decent. (t: 18480) And as you can see, we're generating quite a bit of power just from a very simple start. Just from a very simple sphere like this. Now, this might be obvious, but you don't actually need to use Dyson swarms. You can actually use just a Dyson shell, which is what I'm doing here. (t: 18490) I just have these empty frames floating around. And because they're all moving in different directions, because they're on different shells, it actually makes the design look pretty interesting. (t: 18500) Now, of course, this is never going to generate as much energy. And then if you make a more full design with solar cells and color, it's going to be a lot more efficient. (t: 18510) So you can actually get a lot more power with solar cells included. But as you can see, even though this is far from completed, it's already generating 324 megabots. (t: 18520) So you can see that with just shells and notes, you can actually get very far. And if you make a dense note shell, so basically put as many notes as you can as close together as you can. (t: 18530) That's actually kind of the optimal way in terms of late game optimization of your shell. In terms of the memory consumption CPU consumption. (t: 18540) Versus the output. So if you really want to min max your gameplay, you want to build a dense sphere with just shell. (t: 18550) Now, I've mentioned this before, but I do want to repeat it again. I would recommend that you start out with a small portion of your sphere. Make sure you have that completed. So you can see I started out with this area over here initially and then expand from that point onward. (t: 18560) So what I've done over here, for example, is built this first, then added this large circle to that. (t: 18570) Now, I started with a pretty ambitious idea. I started with a pretty ambitious project as you can see. But it's important to note that it's not efficient to actually start building too much things at the same time. (t: 18580) Because these areas will only start filling up as soon as there's enough notes completed. And if you have these rockets flying all over the place to different parts of your shells. (t: 18590) It's actually going to take longer for these areas to start being completed and be filled up. So you're actually decreasing the optimization of your sphere. (t: 18600) On top of that, you're going to be stuck with a sphere that has all kinds of gaps and partially completed areas. Well, if you at least have a part of it completed, it looks a lot better. (t: 18610) So again, aesthetics matter too. Now, you can see that plan in action in the actual Dyson sphere we're building in the current Masterclass playthrough. So you can see I started with this inner layer. (t: 18620) Then I made an outer one across that. There is actually a sforb hiding in the inner circle over here as well. And now I started on the third layer. (t: 18630) And if you look at that from real life. It looks a lot better than it does in the designer. Because honestly, it looks kind of bland in the designer. But this is the exact same sphere that I just showed you. (t: 18640) And as it is floating around my current system. As you can see, it's being built pretty rapidly. It's still under construction. There is a couple of parts missing apparently. (t: 18650) So I'm not entirely sure what's going on with this second layer. There's a part missing. Hopefully the rockets will arrive soon to fix that. And as you can see, even with just without using any colors. (t: 18660) You can make an awesome looking Dyson sphere. It also really helps to kind of fine tune the colors of your Dyson sphere. Based on the color of the star you're building around. (t: 18670) Building a Dyson sphere across a neutron star is going to be a lot different. Than building it at a default blue or yellow star. So keep that in mind as well. (t: 18680) Playing around with colors of your solar cells and so on. You can have so much fun with this. And it's really fun seeing it all come together as you're playing. I really hope you enjoyed this video. (t: 18690) I hope this was useful to all of you. It's not that short of an episode actually. And for those of you that were already familiar with the UI. Don't worry. (t: 18700) There is a lot more interesting stuff coming up in the next episode as well. But if you found this useful. Make sure you give it a like. And I do hope to catch you in the next one. (t: 18720) Bye bye. Thanks for watching this video. I hope you enjoyed it. I'll see you in the next one. Bye bye. (t: 18730) I hope you enjoyed this video. Like and subscribe if you haven't already. And if you want to watch more of my videos. Click the little bell icon. I'll see you in the next one. (t: 18740) Bye bye. Bye bye. And if you like this video. You can subscribe to this channel. Now in my case over here as you can see I actually added in a secondary line of real guns because I wanted to speed things up a little. (t: 18750) You can see the sphere is actually coming together really nice. It's starting to look pretty good I think. (t: 18760) And honestly the sphere itself is a bit of a problem because in the next episode I really need to start dealing with the dark fog. Because as you can see they're draining almost half the power from the sphere. (t: 18770) Which is well far from optimal you could say. Because this is basically halving our white science production. Because any energy that the sphere is not generating for us can't go to generating antimatter either. (t: 18780) So yeah that's something we'll need to deal with. But first things first let's take the fight to them in a smaller way. (t: 18790) I also want to just play around with some heavier ammunition. Because so far I'm only using the super elo ammo box as well as the normal missiles. The higher end missiles are really expensive and honestly kind of annoying to make. (t: 18800) Things like strange matter and stuff in there. But you can build them there's nothing wrong with that. But I do think they're pretty much it's a bit overkill. (t: 18810) What I do really like is the recipe for the plasma capsule. Because honestly we are getting the graphene from FireEye. So that's almost free. (t: 18820) Magnets are well pretty much free as well. Deuterium again we get that from the gas giants as well as from making it from hydrogen. So this is almost free stuff to make. (t: 18830) And actually these plasma capsules are really really effective. As you can see they do a base damage of 300. Which is actually almost 3.5. So that's pretty good. So that's pretty good. So that's pretty good. As much as the highest level of missiles. (t: 18840) It's not entirely fair. Because of course the missiles have 24 shots. And these things have only 10 shots. But you can use these for ground to ground. (t: 18850) As well as for space defense. Because as you can see. We have the short range plasma turret. Which shoots them at the ground. And then we have the normal plasma turret. (t: 18860) Which shoots them at the space target. So again. It's a super efficient thing to make. And that's what we're going to do. Now in order to do that. I actually need a lot more deuterium. (t: 18870) So I built another one of these builds. That converts the hydrogen into deuterium. So you can check that out in one of my previous episodes. But as you can see. It's nothing special. (t: 18880) It's just filtering them through one of these fractionators. Or actually 30 of them. And then turning the hydrogen that way into some deuterium. On top of that. (t: 18890) I made one of the rare builds again as well. So this is the build that has the graphene in there. The nanochips in there. And I'm just drawing that in for a while. Or wherever I am mining that. (t: 18900) As you can see. We have plenty of that in production. So we have a pretty much infinite amount of these resources. And then we can use that graphene and deuterium. Along with some basic iron ore over here. (t: 18910) As you can see. To make some magnets. And then turn all of that stuff into some plasma capsules. Now as you can see. These things are actually being produced at a pretty high rate. (t: 18920) This is actually going to churn out about two plasma capsules per second. And that's going to be more than enough. To do a lot of fighting. On the ground or in the air. (t: 18930) Wherever we need it. And if we need more than this. We can just copy and paste this build somewhere else. And we'll get more ammo to go and work with. Of course in order to actually make use of that ammo. (t: 18940) We're going to need some short range turrets. I'm not going to necessarily use them for the anti-air defense. Because I think our rockets are handling that just fine. But I do want to use them in the dark fog farm. (t: 18950) So hence the short range turrets to be built over here. Now the annoying thing is. The long range turret actually needs pretty complicated materials. The short range turret is really cheap. To make. So yeah. (t: 18960) You can always just add another build to this. For the plasma turrets. But remember that you are going to have to add in titanium glass. From somewhere. In order to actually produce these things. (t: 18970) And just a tip. It's always a good idea to add in some more ammunition builds. If you're going to actively farm the dark fog. But just don't underestimate how much ammo that actually takes. And you don't want to run out of ammo on any of your planets. (t: 18980) Because if you do. Then the dark fog is going to take the fight to you. And wipe you off the planet. So just be careful. (t: 18990) And make sure you skill up your production. As you are expanding to more and more planets. Okay so step one. Actually find a decent planet. Now I am here in the Will Davis system. (t: 19000) One of my members. Thank you for becoming a member of the channel. I pretty much picked a random planet. The reason I picked this one. Is because it has an almost 100% building area. (t: 19010) So we don't have oceans and things like that. Getting in the way of our building. But the most important reason I picked this planet. Is because there is only 3 bases on it. So it just makes it really easy to start building my initial farm. (t: 19020) Without having to worry about being attacked from like 16 different directions at the same time. So what we are going to do. Is actually set up a farm. (t: 19030) And not power it up just yet. So we can just build it. And kind of figure out what we are doing. Now you do want to be careful. Because you want to set it up pretty close to one of the bases. As you can see. (t: 19040) I just left the other bases intact for the moment. But if you get too close. They will start attacking you. And wipe you off the planet. So you might want to save every now and then. (t: 19050) And it is better to be slightly further away. Than you would ideally be. Because you can always creep a little bit closer. With another line of turrets or something like that. If you find that you are too far away. (t: 19060) Because you do actually want to be very close to one of the bases. Because what we are trying to do. Is get a constant flow of attackers from the dark fog. Basically running into our turrets. (t: 19070) And being destroyed. And that doesn't happen if you are too far away. Because then the threat will just do its job. And you get a wave every now and then. But if you are actually very close. (t: 19080) They will keep attacking you non-stop. And that is what we want. So as you can see. I am just starting to build. Nothing is powered. So we are not generating any threat. First things first. (t: 19090) I think we are going to start off with a line of just basic turrets. Because we have the third level ammunition. And actually that ammunition. Now we have all the upgrades. Is pretty strong. It is already shooting for 72 damage. (t: 19100) Every bullet has 30 shots. So it is a pretty efficient ammo to begin with. Then let's add in a line. I am going to turn off the blinking lights for you. (t: 19110) Let's turn a line of laser turrets. I actually figured out now what the front end of this turret is. I kept putting them backwards. And a lot of people were a little bit annoyed in the comments. (t: 19120) About that. So they are now facing forward. I think. Pretty sure. Right? This is the front right? It shoots out of this thingy. Yeah. (t: 19130) And then on the third row. We have some short range plasma turrets. As you can see these things have a pretty large range. So this is actually awesome. They also do splash damage. (t: 19140) So they do explosive damage. So that is pretty awesome as well. Yeah. Looking pretty good. So next up. And just because we can. I am also going to add in some missile launch. (t: 19150) Now these missile launchers are not actually going to be shooting at the dark fog. But it makes sense to have them here. Just in case we need them. And we are also going to need to make sure we can actually defend this turret. (t: 19160) And we are going to need to protect this planet from waves from outer space. Because if I am going to annoy the dark fog here all the time. The hive is going to take notice. (t: 19170) In order to actually make use of the dark fog farm. We are going to need some ILS's. So I have set up an ILS over here in the corner. That is requesting all the different kinds of ammo. That we need to supply with this stuff. (t: 19180) We are going to need to add in some belts. But I do that in a moment. The other thing I did was add in another couple of ILS's. Just at the minimum distance they need to be apart. And these are going to be able to request other stuff. (t: 19190) Now what I also want to try. And I am pretty sure this is not going to work. But I want to try it anyway. Is request some drones over here. Then put those on a belt like here. (t: 19200) You can see this going kind of round and round. So it is going to be able to reach all of these battlefield analysis bases. And then these bases are actually not going to collect any of the scraps from the dark fog. (t: 19210) They are just here to soak up these precision drones. And hopefully attack the dark fog with them. They might be a little bit too far away. Not sure. Let's find out. (t: 19220) And that's all they are doing. Well actually that's not all they are doing. They are also going to be able to repair all these buildings that are close by. Of course the other ILS's we can use to start soaking up some of the resources. (t: 19230) But that's going to be one of the next steps. Now this last ILS here in the corner. This is actually the only one that is going to be requesting space warpers. (t: 19240) We are exporting that here on this belt. And then we are exporting the warpers again here on this belt. So all of these four ILS's will actually have access to warpers. Without me having to request it for each one of them. (t: 19250) Now in order to actually get the resources from the dark fog up to our other planets. We are going to need to collect them. Now as you can see I have set up a very simple circular belt here. (t: 19260) To just demonstrate the idea that I am going for. So we have a belt that is just going to go round and around. So anything we put on the belt is going to come across all the different ILS's that we put connected to it. (t: 19270) And then every ILS is going to start soaking up some of the resources I want to collect. Now you do want to make sure that you set the filter for the ILS. (t: 19280) So you can set the filter for the dark fog drops that you can find over here. To the actual items you want to collect. So I recommend not collecting any of the more basic items. (t: 19290) And just focusing on the more expensive stuff. But again it is completely up to you. And by doing that we need an ILS with a filter for every single item that we are going to drop. (t: 19300) If you don't do this your belt will get full. Then everything will stop working. And you set a filter on every one of the sorters. So this means for example this sorter over here. (t: 19310) Is going to drag off all the antimatter that we are going to get from the dark fog. Now we are far from done because we actually need to add in a couple of other things. So I decided to add in a version of my offensive platform on the sides of this build. (t: 19320) On both sides actually. So one over here and one over here. Now this does a couple of things. (t: 19330) First of all it adds in a power production facility. Secondary it adds in a planetary shield generator. Just to be safe I put one here in the middle as well. But honestly I think we are just having one on each side. (t: 19340) Will actually do the trick. And it is going to add in more missile launchers to make sure we are able to defend against space. But not only that. This build will also help me actually defend from any enemies that will approach by land. (t: 19350) Because honestly all these enemies are going to zerk in here and just die. But there is more of these planetary bases. (t: 19360) So I just want to be able to place this down and start it up. Without having to worry at least initially about the other waves coming in from other directions. (t: 19370) So this will just help keep it safe a little bit more. Now of course there is also these battlefield analysis bases in here. That can start soaking up the actual drops from the dark fog. (t: 19380) So the ones over here I actually set a filter so they cannot collect anything. Because they are not connected to the circular belt. But these ones are. Now you do want to make sure that you have the incoming belt with new items. (t: 19390) In a 90 degree angle from the ongoing circular one. So what I mean by that is. Let's say I would connect this planetary shield generator. (t: 19400) Or the battlefield analysis base like this. This line has priority. So anything coming back around from this side of the circular belt. (t: 19410) Will be stuck here. If there is already items here. So this means this belt can actually stop moving. But if we do it like this. (t: 19420) This belt has priority. So everything that is already on the circular belt will keep moving. And new items will only be filled in if there is actually space to do so. (t: 19430) This line will back up. And this line will back up. But the circular belt will keep moving. And that is the most important thing. Alrighty. And of course we know that these turrets in the front are going to be toast. (t: 19440) If we don't repair them quickly. Whenever we are getting attacked. Which will be all the time. So I added in a whole line of battlefield analysis bases to here. Along with of course a belt that runs behind it. (t: 19450) And is connected to the battlefield analysis base with a piler. So that we can bring in even more resources to the circular belt over here. (t: 19460) Now which items do you want to export? Again I would recommend the most expensive stuff. But you are also going to want to grab some of the actual dark fog items. (t: 19470) So the dark fog matrix. And the matter recombinator. Are going to be the first two items that you unlock. And you want to make sure you grab some of those as well. If you want you can also start grabbing some rare resources. (t: 19480) But it is really up to you which item you want to export. Now I am not entirely sure if we actually have enough power here. So I figured why not just add in another ILS in the middle. Because we have this nice open area. (t: 19490) And add in some more power as well. Just to be on the safe side. Now I think we are pretty much ready to test if this thing works. Now I do want to point out that the configuration of what things are actually allowed to shoot at is really important. (t: 19500) So the missile turrets over here. The ones in the corner. Are actually set basically to the default setting. (t: 19510) So they will attack space as well as anything that is in the ground zone. They are not firing at the upper air. So the default. Offensive base that I have had in my blueprints. (t: 19520) Actually does fire at upper air. And that means it will start taking out the relay stations. But we don't want to do that in our dark fog form. (t: 19530) We want these things to stay alive. We want them to actively produce units. So leave them alone. We do want them to fire at space. (t: 19540) And we also do want these ones to fire at the ground. Because they might get attacked from the side from these other planetary bases. So we want them to fire at the ground. And we also want these ones to fire at the ground. (t: 19550) Because they might get attacked from the side from these other planetary bases. However the ones over here. Are actually set to only fire at space. So you could argue that we should actually move them somewhere else. I'm just going to leave them here. I think it's fine. But these are just meant for air defense. (t: 19560) So honestly you could argue that because we have quite a few of them now on the side as well. You can take them out completely. And then just move this line back in here. (t: 19570) I just think it looks really cool. To have all these different types of turrets in here together. But yeah. Again there's a definite argument to make to move these to somewhere else. (t: 19580) Maybe like to the back of this area for example. Or just to completely take them out to begin with. Anyway let's see if we can fire this up. (t: 19590) And get it to work. Everything is turning on. Yeah that looks good. That means these are going to turn on as well. Do we have enough power? Yes we do have plenty of power. (t: 19600) Although we are not actually being attacked just yet. So we don't know for sure. Let's see if we can tease them. I think we have enough power to come in. You start shooting at me. (t: 19610) That is fine. Then we are actually going to fly back. Because I want to see if this can handle itself. Yep. Do we have an infinite flow of dark fire coming in? (t: 19620) It does look like it. As you can see it says assembling. But it's still continuously sending units to me. It seems that some of the splash damage from the plasma cannons is actually damaging the turrets in the front as well. (t: 19630) So well. I think we're about as close as we can be. To the base without actually aggroing it actively. (t: 19640) By just building stuff there. As you can see we are now getting in some waves from the side as well. And the turrets are doing their job and taking that out. And as you can see my FPS is starting to struggle. (t: 19650) For the main reason that actively attacking the dark fog and the planet. And remember this is an end game save already. So I'm doing a lot of stuff in the background and on the planet as well. (t: 19660) Is very taxing on your system. So if your system is not able to handle that. Don't worry I have a pretty good one. And mine is already struggling with it as well. (t: 19670) Now I actually do want to take out the other planetary bases on this planet as well. And I think we should be able to do this by ourselves now. Because if you look at it I have a total of not four but actually eight of these ground squadrons. (t: 19680) So if I get close to the enemy base like this. Just getting in range so they are close enough to start attacking them. (t: 19690) But far enough away that the turrets don't shoot at me. I think we should be able to take out the first of all the units and then some of the turrets. (t: 19700) Now this is going to of course get some of my things destroyed. So make sure you bring enough of them. And also make sure you bring enough power with you. (t: 19710) Because if you run out of power while doing this. As you can see it's a pretty hefty drain on my power. Everything will stop working and they will start attacking you. And you'll be defenseless. So that's not a good thing. (t: 19720) But as you can see we can just pretty much vacuum up this level 12 base. Level 12 is definitely not the highest level they can get. But it's a pretty decent way of clearing a planet like this. (t: 19730) Without having to go through all the hassle of destroying things. Now of course the relay station is still here. So we do need to put in a geothermal. (t: 19740) There we go. And that should take care of that. And then we can take out the secondary one as well. Now speaking of levels of the planetary base. The level of the one that we are actively farming will shoot up a huge amount. (t: 19750) And for some reason it seems like. If you attack them with heavier ammunition like plasma. It actually levels them up faster. (t: 19760) I'm not entirely sure why that is. It might have to do something with the damage that these units are taking. Because these do such high damage per shot. Not entirely sure. (t: 19770) But anyway as you can see this base is already up to level 22. So we are getting access to more and more high tech dark fog. And they are starting to drop more and more impressive items. (t: 19780) We are already bringing in things like antimatter. Which is going to be massively useful. Because of course antimatter is pretty hard to produce. Especially early on in the end game. (t: 19790) So this is just free white science basically. We are also going to be able to farm things like antimatter fuel rods. Basically every end game stuff that you would want. (t: 19800) Is actually going to be free of charge right now. Just from farming the dark fog. Now as you level up the dark fog. There are going to start dropping more and more advanced items. (t: 19810) So as you can see we have the metagryph. We have the metagryph combinator. But we also have the silicon based neuron in here now. As well as the negentropy singularity. (t: 19820) Whatever that means. And we even have this item over here. The core element is one of the last things that will start dropping. And we are going to collect all of those. (t: 19830) In order to make use of the dark fog technology. Because as soon as those items start dropping. You will see new technology showing up in your technology tree. So for example we have the upgraded. (t: 19840) Matrix lab. Which is called the self evolution lab. Which you can only research by using the dark fog matrix. (t: 19850) Then we also have an upgrade for the planar smelter. So this is actually three times as fast as a normal smelter. As you can see this is part of the reason why I actually skipped the planar smelters. (t: 19860) Because you want to be using these pretty much at the same time. You get access to the planar ones. And then we have a similar version for the assembler as well. (t: 19870) This is basically a mod. This is a dark fog assembler. Twice as fast as the normal one. And then over here. We actually have access to an item that I've been asked about a lot actually. (t: 19880) This is an upgraded version of antimatter. It is going to contain ten times as much fuel or power. (t: 19890) As an antimatter rod. Which already has a huge amount of power in it. And it only takes eight of these antimatter rods. In order to make one of these. So as you can see this is a 20% boost. (t: 19900) Not just that. If you use them in your fuel chamber. They're actually going to be more efficient even than the antimatter as well. (t: 19910) So if you have this in your mech. It's pretty much never going to run out of energy. These things last for a ridiculously long time. So they're also really expensive to make. (t: 19920) But this is pretty much the thing we're going to be aiming for. Researching the dark fog research is really really quick. You just dump some of these items in your own inventory. Make sure you use the items in your inventory. (t: 19930) You don't need to export them to your matrix lab or whatever. As you can see it goes super super fast. So don't worry about that. Then once again I'm going to turn off the flashlights. (t: 19940) But I added in a couple of PLS's over here. As well as some more ILS's. Because we are going to need to produce all of these items. That we just unlocked by making this research available to us. (t: 19950) Now in order to do that. I also have a couple of assemblers over here. And I try to organize them in such a way. That they're nice and symmetric. (t: 19960) So we're going to need to make a smelter. We're going to need to make an assembler. We're going to need to make a new matrix lab. And we're going to need to make new fuel rods. We're also actually going to need to produce the planar smelters. (t: 19970) Because I haven't actually done that yet. So this is where these builds are going to come in. Okay so first things first. Let's start making some planar smelters. (t: 19980) In order to do that we're actually going to need. Frame material. Plane filters. As well as unipolar magnets. Now these two things actually drop from the dark fog. (t: 19990) So we have them available right here. Just because we're farming the dark fog. The annoying thing is frame material actually doesn't drop from the dark fog. For whatever reason. Everything else does. But this item doesn't. (t: 20000) So we're going to need to bring that in from the outside. Then we are going to be producing the upgraded version of the smelter. And then it's going to require a couple of the items that the dark fog drops. (t: 20010) Along with some quantum chips. Now quantum chips are actually going to be used in several of these recipes. So I think we're going to have to bring those in from the outside as well. Because we're not going to have enough dropping from the dark fog themselves. (t: 20020) Now of course in order to efficiently get the resources where they need to go. I actually am drawing things off the circular belt. (t: 20030) Into the ILS or the PLS right next to where I'm actually constructing the stuff. In order to get the items efficiently to where they need to go. It also means that if you're doubling up on the PLS and ILS. (t: 20040) Then you actually have additional storage. In case for example some of the items that are going to be dropping in quite large quantities. Are starting to overflow. (t: 20050) It takes a little longer before this becomes a problem. In a very similar way I've added in the other buildings as well. So on the right side here we are actually producing the assemblers. (t: 20060) We are requesting the Mark III assemblers as well as the Matrix Labs. Into this ILS over here. And then we're exporting the upgraded version. As you can see we already have some recomposing assemblers ready and good to go. (t: 20070) The main problem is as I said before that we're not getting in enough of these. Quantum chips so we're going to have to work on that. (t: 20080) Before we are doing that however I do want to show you the upgraded version of the fuel rods. As you can see we are going to need some frame materials for those as well. (t: 20090) Which we are not currently getting. So again this is a problem. We are actually getting in some strange matter. And I'm actually producing that at other places as well. (t: 20100) So this is not really a problem. But as you can see you do need to feed in some items from the outside. So let's affix the assembly. And affix the quantum chip problem that we just saw. (t: 20110) In order to do that we can just use the build that we've used in the previous episodes as well. This is going to produce one quantum chip per second. Which is going to be more than enough for the moment. (t: 20120) Just make sure that you delete the actual distributor that is part of that. Because we don't want to use the distributors at all. We're actually just going to use the vessels and the drones. (t: 20130) Something else that we need to do is make sure the setting over here is still set to remote storage. We don't want these quantum chips to fly off to different planets. We want to use them in the factory right over here. (t: 20140) In order to fix the problem with the frame materials. The problem being that we don't have any of the dark fog dropping this. (t: 20150) We're going to need some nanochips, alloy as well as silicon. Now we actually did already make a build for the alloy last time. This is actually going to produce a hefty amount of that. As you can see it's a very straightforward build that I showed you last time as well. (t: 20160) Make sure again that you remove these boxes. Because these are going to make sure that nothing really happens. And then we're going to need to add in some silicon in order to make the actual frame materials. (t: 20170) Which then looks very similar to something like this. If you do it like I've done it over here. (t: 20180) So I added in another 10 smelters making the silicon. It's the same number actually that we have in the titanium. But I just mirrored it to make the build look a little bit more symmetric. (t: 20190) I've added in the other ILS in order to make sure we have a way to get in and out of shape. So we're going to need to add in the nanochips and the silicon. As well as able to export the frame materials. (t: 20200) And then all we need to do is add in a total of 20 I believe this is. Let's count. Yep it's 20 assemblers. Mark 3 assemblers making the actual frame materials. (t: 20210) And as you can see we are now getting in a boatload of frame materials. So this is going to be super useful. And again make sure you set this to remote storage. (t: 20220) Because you don't want this flying off to your other planets. So the next one over here is actually adding another build for the quantum processors. Exactly the same as the one we already had. (t: 20230) So I have two of those. Along with the one for the frame materials. And then I think that should have fixed all of the production that we need. In order to get this going. (t: 20240) Oh look my belts are stuck. What seems to be the problem? Oh right. Do you remember where I was just demonstrating you that you shouldn't build your belts like this. (t: 20250) In a particular way because it will stop your belts moving. Well I actually was fiddling around with my belts. And apparently I made exactly that mistake. So do as I say not as I do please. (t: 20260) Another issue that you might run into is that some of these items actually are dropped in such high quantities. (t: 20270) That your storage will end up really quickly filling up. Now as soon as something like this fills up. That means that the belt still can get full. Basically polluted by more in this case dark fog matrices. (t: 20280) Making it hard for any of your builds to get filled. And you don't have any other items to get on this belt. And of course you do want to make sure you keep adding in new items and exporting them. (t: 20290) Because we're getting stuff like antimatter as well as antimatter fuel rods in here. So that's a super valuable stuff. And we don't want to make sure that the other stuff is getting in the way. (t: 20300) Now it's not a huge problem if something temporarily is backed up. Because there's a lot of room in your belt of analysis bases to soak up this stuff. But as you can see again that also fills up really really quick. (t: 20310) So keep an eye on your dark fog. And every now and then what you could actually do is just request some of these items from another planet. (t: 20320) Store them up over there or just delete them whatever you want to do. Or what you can do and you can do that from any planet in the universe. Is you can just go to the filter and then just say okay I know I have tens of thousands of this dark fog matrix. (t: 20330) So let me just turn a couple of these things off. And maybe in an hour or two I can turn them back on. (t: 20340) But it will take a very long time for me to actually burn through thousands of these things. Because remember you actually don't need that many of them in order to make a couple of these buildings. (t: 20350) And that's the only use you have for them. So it's only ten of them per assembler. So a thousand is going to make a hundred assemblers. And when you have ten thousand of them it's going to make a thousand assemblers. (t: 20360) So like I said you're not going to burn through those very quickly. One very important thing that you want to do is actually proliferate the fuel rods. (t: 20370) So let me show you. If I have the unproliferated fuel rods in my inventory. They have 72 gigajoules and 1100 fuel chamber generation. (t: 20380) If I put them on this belt over here. You can do that like so. Just click them on the belt and drop them in here. As you can see they will start being proliferated. (t: 20390) Now these items actually have 2300 fuel chamber generation as well. So that's more than twice what they've had before. (t: 20400) So this is an insane amount of power going into your mag. Just by proliferating these items. And proliferating these things is pretty much cheap anyway. Now there's no point in proliferating the inputs. (t: 20410) You won't get additional ones of these. Because the recipe itself only accepts the production speed up. Speeding this production up is not going to be worthwhile. (t: 20420) So you don't have to worry about proliferating anything else. The same holds for the production of the buildings. As you can see you can only speed up the production speed of it. You could argue that there is a lot of room for that. (t: 20430) There is some point in doing that. But honestly I don't think it's worth the fuss. So if anything just make sure that you actually proliferate the fuel rods themselves. (t: 20440) Okay so one last addition to the Dark Fog farm. There are actually a row of signal towers in the front. Along with a couple of laser turrets. The laser turrets are just here because I wanted to fill up the gap. (t: 20450) But the signal towers are really important because they have a lot of high health. And at level 30 I noticed that I was losing some buildings every now and then. And of course I don't want to babysit this farm. (t: 20460) And the signal towers draw the initial attention from the Dark Fog. As you can see this one is getting attacked all the time. But because these things have so much health. (t: 20470) And we have the battlefield analysis bases right next to them. They are pretty much going to be instantly repaired. While these turrets have a lot less health. So every now and then a couple of the units would get through. (t: 20480) Take one or two of these things out. And of course if that happens a few times. You are not left with any turrets. So this is just to safeguard the build. For the max level attacks that we are getting now. (t: 20490) Other than that. The build is working like intended. So we are producing a nice amount of smelters. We have I think quite a few of these. (t: 20500) Recomposing assemblers as well. Even if you only get a couple of hundred of these. Keep in mind that these are actually going to be twice as efficient. When we are talking about the assemblers. (t: 20510) And when we are talking about the smelters. They are actually three times as efficient. As the base level ones. So you don't need massive amounts of this. (t: 20520) Even to make use of them. I also made sure that the planar filter ones. The planar smelters I mean. Are actually being put in the ILS as well. Why? (t: 20530) Just because I can. Because as you can see we are not non-stop producing this. And the weird thing is the bottleneck is actually the base level energy shards. That we need from the Dark Fog. So this is one of the most basic items that drops from those units. (t: 20540) But because we are farming a level 30 base. And the base levels up automatically. There is not much we can do about that. We are actually getting relatively few of those drops. (t: 20550) Kind of a luxury problem I would say. Because well having these smelters is a pretty awesome thing. But the only thing we can do about that is basically. (t: 20560) Copy pasting the blueprint for this farm. And setting up some more farms like it. And honestly that is not a bad idea. Because we are getting a pretty hefty amount of antimatter. (t: 20570) Keep in mind antimatter once again is very valuable. So this is pretty nice to produce. And we are getting a ton of other materials as well. So there is nothing wrong with setting up a few more farms. (t: 20580) And at this point it is going to be pretty easy to do that. Now I just want to once again point out the awesomeness of these fuel rods. The base proliferated deuterium fuel rods have 750 megajoules. (t: 20590) And will get you a 400% bonus when applied to your fuel chamber. So this is why I use these in my fuel chamber. (t: 20600) Now these ones have 72 gigajoules. So that is almost 100 times as much energy as one of these deuterium fuel rods. (t: 20610) But on top of that that is going to be multiplied by 2300%. So basically times 23. Rather than times 4. So one of these fuel rods is hundreds of deuterium fuel rods. (t: 20620) And these things are already pretty awesome. So yeah this stack is going to last me for a very very long time. (t: 20630) And in the meanwhile of course we even get more fuel rods. Now you can also. Put these in artificial stars. And consume the power like that. Nothing wrong with that as well. (t: 20640) Again it is a huge amount of power. That you are going to be supplying in that way. But I would recommend make sure that at least you are using some of these. In order to supply your own power production. (t: 20650) Because this is basically going to give you infinite energy. Well I hope you enjoyed this little masterclass. About destroying the dark fog infinitely. (t: 20660) A number of times all over again. And well basically this is just us taking our revenge. At those pesky little robots attacking us all over the place. If you are still here you are awesome. (t: 20670) But make sure that you also tune in for the next episode. Because we are actually going to take the fight to the heist then. And getting all these materials in place is going to be really helpful in order to do that. (t: 20680) If you are still here you are awesome. And I do hope to catch you in the next one. (t: 20690) See you then. (t: 20720) Bye bye. (t: 20750) So the first thing I want to talk about is that warping around your universe becomes super easy. As you can see my energy is not even draining. At least you can see it draining. (t: 20760) And I am warping at full speed. So that is very efficient if you want to go from one side of your universe to the other one. Would you look at this quaint little red and blue science production that we built in the first episode. (t: 20770) Still plugging along. And look at this awesome mall. We are here to pick up some materials. (t: 20780) Specifically we are here to pick up a lot of this stuff. Destroyers and Corvettes. And honestly just pick up however many you have. Including of course the ones that are in our interstellar mall over here. (t: 20790) Because you are going to need a lot of them. Okay so let's take a look at our Dyson sphere at the moment. Right now the dark fog is actually taking 1.2 gigawatts of my energy that I am producing. (t: 20800) Which is more than one third of the entire energy I am actually producing. So that is a lot. And that is not really what we want. (t: 20810) So we are going to take out the hives. Now I actually have a little bit of time. I actually want to address a question that I got in the comments a couple of episodes ago. Someone was asking does your sphere fill up faster if you have multiple nodes. (t: 20820) And you can see the answer here. The answer is yes by the way. As you can see I have multiple places where the nodes can fill into the. Or the sills can fill into the nodes I should say. (t: 20830) And it gets a little bit messy with all these sills flying around. But yes they are filling up in all kinds of different places in my sphere at the moment. And my sphere is continuing to grow. (t: 20840) So the first way to actually start clearing out the hive is just to fly close to it. And as soon as you see these things starting to move like now. (t: 20850) You turn around and you basically hoof it back to the closest planet in your system. And you have the dark fog chasing you. (t: 20860) So as you can see they are all turned red now. The ships are still trying to fight them off. While we are flying back to the planet. (t: 20870) Now. They might not necessarily chase you all the way back to the planet. But what it will do is actually generate a lot of threat on the hive. So as you can see the threat is going up significantly now. (t: 20880) We do actually have full waves of vessels coming in. And this will allow you to basically draw the vessels to your planet. And get the vessels destroyed by your planetary defenses. (t: 20890) Now as you are doing that. What you are looking for to happen is to see these ships that are stationed around the hive. Slowly starting to be depleted. (t: 20900) So you can see a full squadron right there. But you can see some of the other squadrons here on the sides are actually partially depleted. Now. Don't underestimate how many ships there are actually in the hive. (t: 20910) There can be like up to. I think 1500 ships or something like that in there. So a couple of waves of 100 ships is not going to make a huge dent in this. (t: 20920) So you will have to do several fly bys in order to get all the ships from the hive. Completely away from that. And of course. As long as they still have resources. They will continue to survive. And they will continue to start rebuilding the ships as well. (t: 20930) But you can. If you do regular fly bys. You can deplete them faster than they can restore them. Now if you keep that up long enough. You will see that the hive is completely depleted from any enemies. (t: 20940) Or at least most of them. As you can see there are still some ships in there. And then you can just start attacking the hive directly. (t: 20950) Now I recommend if you take on any hive. Start out from the sides. Because that will make your life a lot easier. At least that there is a lot safer to do that. Because as long as there is still energy in the hive. (t: 20960) The turrets in the hive. Because the hive does have turrets. Other than just the ships. It also has manual turrets. As you can see I am actually getting fired upon a little bit now as well. (t: 20970) So just keep your distance a little bit. Make your ships do the most work. And as you can see. I am slowly taking down some buildings in the hive. (t: 20980) And well. Just whittle at it. And it will slowly go down. And the bonus is that. While you are doing this. You are actually generating threat on the other hives as well. (t: 20990) So any other hives that you are not currently attacking. Will get drained from ships while you are doing this. Now what you are looking for. Very similar to when you are clearing out the planetary bases. (t: 21000) Is the message core destroyed in one of your hives. So what you want to do is slowly. Go down the middle. From the side. Down to the middle of the actual hive. (t: 21010) Destroy the core. And then pretty much the hive is already dead. You just need to clear it out. It becomes a lot easier. Because as soon as the core is destroyed. (t: 21020) As you can see right now. There is pretty much no energy anymore in this thing. If you hover over some of the buildings. You will get a pop up showing you some details. (t: 21030) It is a little hard sometimes to get that to show up. But the energy is slowly being depleted. Now a different way to actually attack the hives. Is completely destroying them by yourself. (t: 21040) So not using your planetary defense to do the job for you. In order to do that. You are actually going to have to move up pretty close to the hive. Now I am doing it from the front. (t: 21050) But you probably want to do this from the side. And you are also going to want to be at a distance of about 0.2 AU. Now hopefully as you will see in a moment. (t: 21060) Once I press Z. And I reach that distance. I am creeping in. Because I do not want to fly in here at full speed. (t: 21070) And then get myself killed. I should be able to start sending them to the hive. And basically start them to attack. Hello hello. Yep there we go. So I am stopping my movement completely. (t: 21080) And as you can see. My ships have now engaged. And are attacking the enemy. And I am still out of range of the turret. (t: 21090) So I should be taking any damage. Well there could be some ships attacking me. So you do want to keep an eye on that. Because as they start swarming around. They can actually attack your ship as well. (t: 21100) And if there is a couple of thousand ships in here. Right now as you can see. You can actually see it when you press Z. So you can see there is about 100 units here. (t: 21110) Most of that is actually. I am not really sure if it counts the buildings. But it is probably just the ships. Maybe both. I do not know. But it is not that much. But there is a lot of different examples. (t: 21120) Because these are the hives that I was already taking out with my flybys. I will show you an example of doing this with a fully strength hive. In a moment. It is far from trivial. You are going to be losing quite a few ships. (t: 21130) As you can see on the right hand side over here. My destroyers are getting destroyed. My corvettes are taking out as well. However as you can see. (t: 21140) I am also already starting to take apart the hive. So once this starts up. It is a very easy way to clear out the hive. And it is by far the fastest way to do that. (t: 21150) However this is very fuel intensive. Now you cannot tell this. Because I am using the yellow fuel. That I showed you at the beginning of this episode. (t: 21160) But if you are still on a lower tier type of hive. You are going to be running out of power. And as soon as you run out of power. Your ships stop working. (t: 21170) So take that into account. You should also take into account. That this is going to burn through your ships. So make sure you bring plenty of them with you. If you are going to do it like this. (t: 21180) I also want to point out. That clearing the hives is a lot easier with upgrades. That is kind of obvious I guess. But take into consideration. That I am right now using a total of six space fleets. (t: 21190) And I am actually producing the other two space fleets. In a moment. I am also going to recommend to you. That you use destroyers mostly. (t: 21200) Destroyers seem to be a lot harder to destroy for the enemy. Kind of makes sense. Because they have a lot more hit points. But they seem to be far more efficient at taking out the enemy. (t: 21210) Because even though they get destroyed. You basically only use the fire power once they are entirely destroyed. Makes sense. While the corvettes are taking out at a pretty high rate. (t: 21220) So you can replenish them easily. But they just get taken out. Really quickly again. So long story short. I would recommend using the destroyers. Rather than the corvettes. (t: 21230) To clear out the hives. And of course the more you have of those. The easier this is going to be. However regardless of how you do it. If you keep it up. As you can see. You can easily clear out your. (t: 21240) Well maybe not easily. But you can clear out your home system. And take back full control of the power. That you are generating with your sphere. (t: 21250) As you can see. My sphere grew a little bit in the meanwhile. And as you can see. There is no more dark fog. So our entire home system. Including the space around it. (t: 21260) Is entirely safe now. Now I promised to show you what this looks like. When you are taking on a fully powered hive. And as you can see. There is a lot of ships flying around here. (t: 21270) If I go back into the menu. And actually show it to you. There is 900 ships flying around here. And there is even more over here in the hive. (t: 21280) So and actually I already took out about 60. Because this entire. I don't know. Piranha swarm of fish. Or whatever you want to call this. Actually started out. (t: 21290) At I think like 1600 ships. So you can imagine. It takes you a little while to whittle through this. And you are going to lose a lot of ships. Yourself in the process. As long as you don't get too close. (t: 21300) To this whirlwind of angry dark fog. You should be fine. You can just stay on the outside. And you will take a few hits every now and then. But it's not more than any. Just a couple of pot shots basically. (t: 21310) But it's not more than any. Just a couple of pot shots basically. Make sure you keep your distance from the hive itself. While you are flying and drifting around. As long as you stay away from them. It's just a matter of sitting here or waiting. (t: 21320) As you can see while I have been talking. We are already down to about 500 ships. And as soon as the other 500 ships are down. We can get working on the hive. Now I am in a different system. (t: 21330) As you can see right now. There is only two hives over here. So I can completely clear this out. In maybe like 20 to 30 minutes at the most. Maybe it's even faster. (t: 21340) But it always feels like it's very long at least. But it always feels like it's very long at least. Oh look! About 1600 ships to wipe out in another Dark Fog hive. Now I am not going to lie. (t: 21350) It's a little bit tedious to clear out the hives in your systems. And honestly you do not necessarily need to clear them out. And honestly you do not necessarily need to clear them out. As long as you keep your planets protected. The hives can't really do anything. (t: 21360) So I would recommend just taking out the hives in the systems. So I would recommend just taking out the hives in the systems. Where you are actually going to be building a Dyson sphere. Because otherwise. Unless you just like clearing out the Dark Fog. (t: 21370) There is nothing wrong with that. But there is not really any bang for your buck. In terms of the time investment that you do. That said. There is something that is a lot easier now. (t: 21380) We have access to spaceships. And we can easily defend our planets as well. So let's say you find yourself on a planet. And we can easily defend our planets as well. That has 20 plus bases on it. (t: 21390) And as you can see this planet actually has so many bases on it. And as you can see this planet actually has so many bases on it. I needed to find a moment to find a place to land. But I did. Normally you would plant a bomb. (t: 21400) And you would plant an offensive platform over here. Maybe a couple of them. And then start clearing out the bases one by one. It takes a little while. But it is fairly straight forward to do it. (t: 21410) However there is a way that is a lot faster. If you press Z. And you go to the overview of your fleets. And then you switch to the space version of that. You will find a button over here that says. (t: 21420) Attack relay station. Now what you can do is actually launch your fleets. Like this. It is a little bit finicky where you want to click. But something like that. (t: 21430) And then you can tell your fleet to attack relay stations. But something like that. And then you can tell your fleet to attack relay stations. I am going to have to be fast to show it to you. Because as you can see. (t: 21440) Do you see them disappearing? It takes like 2 seconds for your fleet. To completely clear out every single relay station. To completely clear out every single relay station. On a planet. (t: 21450) Still zero threats. So they are not attacking you. This is an easy way to clear them out. However the hives don't like this. So they will start to attack whatever planet you have in the system. (t: 21460) So basically start sending their fleet after you. Now keep that in mind. What you basically want to do. Is visit every single planet in your system. (t: 21470) While you have this option enabled. And honestly at this point in the game. You can just leave this option on. And destroy any relay station that you come across. Except for the one on your Dark Fog farming planet. (t: 21480) Except for the one on your Dark Fog farming planet. So make sure you turn it off whenever you visit that. So make sure you turn it off whenever you visit that. And then all you need to do. Is just mop up the Dark Fog bases that are left. (t: 21490) Is just mop up the Dark Fog bases that are left. Now remember that as soon as you take down the relay station. They will slowly start losing power. So this is why you first want to visit all the planets one by one. So this is why you first want to visit all the planets one by one. (t: 21500) Just take out the relay stations. Then fly back to the first planet. By then it will probably have lost most of its power. Or maybe all of its power. And then you can just walk in like this. (t: 21510) And then you can just walk in like this. Assuming you have all those bases. Then the mates that I've upgraded already They will take care of all the remaining units. They will take care of all the remaining units. (t: 21520) And you can slowly start wiping out the planetary bases one by one. And you can slowly start wiping out the planetary bases one by one. (t: 21524) It will still take you a little while. Of course you still have to actually destroy the bays. (t: 21530) But as you can see. It's definitely not hard. It's basically just a matter of walking around on your planet. Now unlike the space combat. Now unlike the space combat. (t: 21540) This is taking a pretty heay drain on my power. This is taking a pretty heay drain on my power. So you do need to keep an eye on this, especially if you're on a larger planet with a lot of defenses like I'm on now. (t: 21550) Make sure you don't run out of power basically while you're doing this. And if you are, just stand still for a moment. As long as you're not actively attacking a planetary base, you are not generating any threat. (t: 21560) And honestly, if the entire planet is already depowered, there's only so many units that they can send after you. So at that point, you should still be safe. (t: 21570) Now, last but not least, if you want to speed things up, you should find the Dark Fog communicator. It will be somewhere on the outskirts of your home system. (t: 21580) It's not going to show up on the map, but you can actually spot it from just flying around in outer space. It will show up like that. And if you fly close enough, like I've done over here, you can press E to interact. (t: 21590) And you can either make the Dark Fog more impressive or actually weaken them. You can actually set a truce as well. Not entirely sure. (t: 21600) I'm not sure why you would want to do this, but you can spend some metadata in order to basically just have a truce and they will no longer attack you. But especially if you're playing on a lower difficulty level, if you're at this point in the game, you are probably going to be just fine by amping it up a little bit. (t: 21610) You won't actually get any bonuses in terms of metadata from that. (t: 21620) So take that into consideration. This is not a way you can kind of cheat yourself into more metadata, but you can make the Dark Fog more aggressive. Now, the reason you might want to do that is because... (t: 21630) Once you start clearing out hives, you will actually make them more aggressive and they will start sending more swarms of vessels after you. And that basically just helps you clear them out faster than if they would just be sending waves every now and then. (t: 21640) It's not a huge difference, honestly. It's completely up to you whether or not you want to use it. But it's kind of like this niche thing that maybe not everyone is aware of. (t: 21650) So I figured I would want to include it in here anyway. Now, I hope you found this useful in order to kind of get a feel on how to clear out the waves. (t: 21660) Now, I hope you found this useful in order to kind of get a feel on how to clear out the waves. Now, I hope you found this useful in order to kind of get a feel on how to clear out the waves. Now, I hope you found this useful in order to kind of get a feel on how to clear out the waves. Now, I do want to mention that if you're going to do this, you might be seeing your FPS and your actual game rate starting to suffer. It's worth noting that you can switch these in two directions. (t: 21670) Normally, I would recommend that you keep this blue bar as high as possible. Your FPS will suffer for it as you can see happening right now. (t: 21680) But this will mean your game starts... ...keeps playing at the highest speed. If you turn this lower, your experience is going to be a lot better in terms of the FPS you're getting. (t: 21690) but the actual time passing in the game is going to be slower than it is when you have this put up to the maximum setting it's completely up to you whether or not you want to play it on one setting (t: 21700) on the other of course i can imagine that playing with a low fps is not very pleasant but if you want to for example wait for your sphere to be completed and things like that then putting this (t: 21710) up to a higher setting make sure that you're having a closer to one to one ratio between the (t: 21720) actual time passing in real life and in the game so be aware that that is what this says if you turn it down all the way all the way around your fps will be maximized but time will be passing (t: 21730) slower in the actual game so if you feel like things are taking a long time that's because they are okay so remember at the start of this episode when i mentioned that i had 43 (t: 21740) strange annihilation fuel rods on me well as you can see i'm still on 41 so clearing out a couple of systems from hives and actually a couple of planets along (t: 21750) with it only took two of these fuel rods and there's there's still one only almost completely (t: 21760) full so i just want to once again emphasize how much it's worth getting these up and running in your inventory because basically at that point you will never want for fuel again let's see (t: 21770) you there's 30 more already being produced in my dark fog farm and on top of that let's see we have a (t: 21780) thousand assemblers waiting for us we have a few hundred of these matrix labs as well how are we doing on the actual smelters um not that good the reason for that is that actually my production was (t: 21790) backed up here a little bit but as you can see it's now producing i do have 500 planar smelters as well and that's all from the materials in this farm along with of course a little bit of help (t: 21800) with quantum chips and frame materials from the support build that we built last time it's working very much like intended and in the next episode i want to have some fun with those end (t: 21810) game buildings if you're still here you're awesome and i do hope to catch you in the next one hey hey it's tda (t: 21830) and today we're going to do a bit of a bonus episode to the master class because honestly in the previous episodes you've already seen everything you need in order to complete the (t: 21840) game scale up to whatever your end game is in terms of white science production building building multiple dyson spheres and things like that but in this episode i'm going to show you how to set (t: 21850) up your true end game blueprints and as you can see it's going to be quite a big blueprint however it's going to be set up in such a way that you can easily kind of make it your own and and design (t: 21860) your own planet-wide blueprint so that's going to be the point now specifically what we're going to do in this particular planet is we're going to be producing a massive amount of science so let's get (t: 21870) to it now let me start with the general tip and that is actually that i would prefer to build my builds in the actual sandbox mode because the sandbox mode allows you to easily just flatten (t: 21880) the planet just build as you go you are not going to be interrupted by attacks and things like that it's just a very peaceful easy way to make larger builds (t: 21890) now the counter argument to this and this is a very good reason to build actual in your actual game is that it's going to take you quite a bit of time to make a larger build and during that time if (t: 21900) you're playing in your actual game time is going to progress your dyson spheres are going to be (t: 21910) built your science is going to progress so in terms of being efficient with your time it makes sense to actually build inside your own game so whatever direction you decide to go (t: 21920) just keep in mind that the sandbox is there if you want to use it another reason why i really like designing things in the sandbox is that it allows you just to create free stuff so you don't run into the issue that you're trying to build a couple of smelters but (t: 21930) you don't have those on and you need to collect them and stuff like that you can just build uninterrupted now just to show you how this actually works because i think not everyone (t: 21940) might notice um you can turn on this free toggle item you can invent empty your inventory just to make sure you start with a clean slate and then it's really easy to actually add things to your (t: 21950) inventory so you can either use the obtain common items things it's this kind of drops you a little bit of everything in your inventory but i don't find this particularly useful if you're building (t: 21960) endgame stuff because you don't need these mark 1 belts and sorters and you don't need these ships and things and stuff like that so what i actually would recommend doing is just building these items (t: 21970) and this happens instantly so you can just add whatever you need to the inventory the maximum production size is actually set really really high so you can easily add tons of different things to (t: 21980) your inventory and honestly all you need is a lot of items and you can build pretty much everything and honestly all you need is the production facilities some belts sorters and maybe the power stations when you are near the end of the actual design but other than that this makes it (t: 21990) super easy to just dump everything in your inventory that you might need another thing that i think is really important to keep in mind is that when you start building (t: 22000) i recommend starting from the upper portion of the middle area of your planet if that makes sense so if you look at your planet the middle area is the largest build area that you have it's most (t: 22010) convenient to set up your initial facilities and then when you once you get closer to the top of your planet you see these jumps in the grid now you want to stay away from these initially you (t: 22020) can actually really nicely utilize this space but if you're making larger builds you want them to stick in the middle part of your of your planet and they are basically around the equator and if (t: 22030) you start building from the top line this makes it a lot easier to make sure that you are basically building downwards now something else to keep in mind if (t: 22040) you start out from this top line then you will find the equator somewhere here it's the thick line in the middle of the planet so if you make sure that your build stays between this thick (t: 22050) line and the upper one that i just designated over here you know you can actually copy the top half of your equator design into the bottom half and you can duplicate your design easily so (t: 22060) you can really easily make sure that you can basically copy paste your design a couple of times around the planet last but not least if you're designing (t: 22070) your end game facilities i do recommend that you actually use the end game buildings as well so in the previous episode i set up a dark fog farm that will not only get you the resources you need to (t: 22080) both research these and construct these but it will actually also construct these end game smelters and assemblers now the matrix labs upgrade you don't necessarily need honestly (t: 22090) they look really cool but they're not particularly useful you don't really need it because they're not really useful but they're not really useful and you don't really need them because they're not really useful but they're not really useful and you don't really need them because they're matrix labs super efficient already but these two things will save you a massive amount of building (t: 22100) space and allow you to cram a huge amount of production into a very small space even without really optimizing for space specifically it's just really cool to use and you only need a couple of (t: 22110) hundred of these buildings to set up a massive facility so i highly recommend that you use these buildings people tend to get a little bit daunted by designing a massive facility because of the huge (t: 22120) amount of resources that go into that so if you're designing a building that's not really efficient your own planet wide designs then I would highly recommend that you cut them (t: 22130) up in smaller pieces so it becomes a lot more manageable this has a lot of advantages but it also has some disadvantages the main one being that (t: 22140) you're going to set up a lot of these ILSs and PLSs and that just makes things less space efficient however I never understand why people are really (t: 22150) worried about optimizing space because at this point in the game you have probably dozens of planets at your disposal and I've never run into a point (t: 22160) in the game where I actually ran out of space to build so not entirely sure why you would want to optimize that but I do acknowledge that this is not necessarily (t: 22170) the most space efficient way to build it is definitely the most time efficient way to build because you won't be wasting a lot of time trying to min max (t: 22180) where your belts are going and stuff like that it's also worth keeping in mind that when you're setting up a planet wide facility you do want to initially start with a design that has a little bit of a smaller skill for the (t: 22190) simple reason that you're going to bring in a lot of raw resources and this will easily become your bottleneck if you're trying to have like one ILS just (t: 22200) requesting in tons of resources that just won't work so the design I'm going for in this particular case is we're going to make a design that is going to (t: 22210) produce four science science per second of all the different colors and by colors I do mean actual colors so I'm excluding white from this because basically that's going to be produced (t: 22220) from the other colors anyway in order to do that we're going to set up a whole bunch of smelters over here just starting out with the basics here you (t: 22230) need a little bit of graphite you need some glass we are going to need quite a bit of titanium and we're going to need quite a bit of copper but as you can see (t: 22240) even producing the same amount of color and we're going to need about 20 science per second so four of each color this is all the base resources that we're going to need and (t: 22250) I'm not even proliferating and the reason for that is that these smelters produce work and where can you see that it's over here they're actually in a (t: 22260) different building tab by the way in case you kind of missed where they were but they're producing at three times speed so this is super efficient if you compare that to the the plane smothers it's 50% more efficient to that and the (t: 22270) assembly machines is actually twice the speed of the mark 3 one so this is just super efficient and will save you a lot of space and well it looks really cool (t: 22280) as well now in order to complete the rest of the basics we have 18 smelters making magnets 27 of them making iron we have 38 of them making silicon we need a (t: 22290) lot of silicon and we have six of them making crystal silicon now honestly these crystals you could leave out because you can make them from a rare recipe as well but because you only need so tiny amounts of these I just add them (t: 22300) into my design you can get a little bit creative about how you are actually setting up your ILS and PLS so as you can see because I know that all the resources I'm making (t: 22310) over here are actually going to be used just within this planet I don't need to export them off planet I don't need to bother with the ILS I can just use a planetary logistics station PLS and this takes up a lot less space so this makes (t: 22320) it easier to keep everything nice and tidy and as you can see I'm actually bringing in four different or three different resources in here I could (t: 22330) have added in the crystal silicon as well but it had a space left over here anyway because we're only having to bring in a couple of different resources (t: 22340) now when it comes to these resources don't underestimate how much you're going to be requesting into your planet so definitely another reason to start it with a smaller (t: 22350) skill design initially then scale it up as you are adding more and more resources to your systems now there is another trick going on here so I am actually requesting silicon for example over here (t: 22360) so I'm just going to put this iron over here and then I'm doing the same thing over here that actually allows twice in the amount of supply to fly into the planet without me having to add (t: 22370) additional ILSs to the system so just a way to make sure you're actually utilizing your production capabilities by a lot because don't underestimate how much resources are going to be flying into (t: 22380) this particular setup because of course we're producing these we're smelting these at three times the normal speed so this is a massive amount of stuff now you might be worried (t: 22390) about the supply on the belt but don't forget that these ILSs in the end game can actually stack up your resources by themselves so this is a really nice and efficient so you can have (t: 22400) up to 120 items per second on your belt and this is actually also what I designed this entire thing (t: 22410) with in mind because I don't want to have to fiddle around with belts requesting more than 120 items (t: 22420) per second just another reason to keep things smaller initially and then something else to keep in mind is that I'm actually requesting these resources but also having these set to local supply now the reason for this is especially if you're going to copy (t: 22430) paste a particular build several times on a planet this allows the overflow of one I one of these ILS (t: 22440) to go into the other one and that just makes it a lot easier to balance out your production across all your different facilities it's something to keep in mind when you're designing this of course (t: 22450) make sure you're fully stocking all of your shuttles and vessels and again make sure that your PLSs are set to supply rather than (t: 22460) demand because otherwise things will not actually be exported throughout your base okie dokie let's keep going we added six assemblers making gears five of them making (t: 22470) circuit boards six making magnetic rings and then 12 making micro crystalline components and that deals with everything we actually need for blue signs between the circuit boards and (t: 22480) the magnetic coils but of course we also have the Q5 and we have the K6 and Q7 so we have the Q7 and Q8 and we also have the Q10 and Q12 and we also have (t: 22490) the Q13 and Q14 and so on and so on so I'm assuming that you're going to get your hydrogen either from gas giants from your oil production or from the fractionator setup that I've showed you in one (t: 22500) of the previous episodes so that's two types of signs done and we're actually already a pretty good way underway for the next few sciences as well (t: 22510) I'm just using a PLS over here to feed those in. These PLSs are really tiny and small. They nicely fit into a closely related build. I wouldn't be able to put an ILS here, for example, because it (t: 22520) would have an issue with this PLS being so close. But PLSs themselves, you can fit very close together. So you can just use that as an easy way to get everything organized and look nice and tidy. (t: 22530) However, of course, you can be really efficient with how you put some belts in between, especially if you're producing things very close to where they're actually going to be used. So for example, (t: 22540) over here, I have six assemblers making turbines. We need the engines for that. So I'm just feeding in the engines over here. We also need the same magnetic rings that we already have over here. So (t: 22550) I'm borrowing them from the belt over there. And then I'm actually using these turbines just to make the particle container. So I'm actually feeding these straight into the assembler over (t: 22560) here. And that just means that we're sort of optimizing things without making too much of a fuss about making everything. Now, of course, I do recommend that you actually line up all your buildings, your production (t: 22570) buildings, at least as much as you can. I've actually also aligned a couple of these things. It's a little hard to tell, but I'm also trying to align these ILSs over here and things like that. (t: 22580) Similarly, I've done that over here with these ILSs. As you can see, they're in a nice little line. So are these two PLSs, things like that. Just make things look a lot tidier once you're (t: 22590) starting to look at the larger scale. Now I'm going to be adding in everyone's favorite crystals. So we have 12 of them making titanium crystals here, 12 of them making titanium crystals here, and 12 of them making titanium crystals here. And six of them making Casimir crystals. Now, I do want to point out that just like in all the (t: 22600) previous builds that we've made in the last couple of episodes in this masterclass, I am assuming (t: 22610) that you're going to get your rare recipes from other places, or you can put them on this planet as well, but you are making use of the rare recipes. So I'm not actually going to be producing (t: 22620) graphene or organic crystals anywhere in this particular planet, because I'm assuming you're going to get it from somewhere else. Similarly, over here, we have the hydrogen that I just made. And over here, we have the carbon and the carbon and the carbonite that I just mentioned just coming (t: 22630) in, exported or imported, I should say, to this planet. Another little trick that you can use if you want to keep these larger builds compact, especially if you're going to be using a lot of (t: 22640) different resources and therefore need a lot of ILSs, is to basically build under the roof of an ILS as much as you can. So for example, over here, I added in 12 assemblers making processors. We're (t: 22650) going to need all of those. And they're basically just built under the roof under the same place that we're actually producing these. So the circuit boards, as well as the capacitors, are going to be being built under the roof of an ILS. So for example, over here, I added in twelve assemblers making So the circuit boards as well as the components are being made over here. (t: 22660) I'm just exporting them from the same ILS. Now on the other side of that I am building the processors. And then I'm just feeding those back into this ILS because I can. (t: 22670) Now this does mean that this ILS won't have access to warpers unless I feed them in from a different ILS. Now honestly you could set this up in such a way that the ILS actually does need warpers. (t: 22680) However in that case you should probably be using a PLS instead. However remember we do need to actually export the magnetic coils and the circuit boards because of the blue signs. (t: 22690) And that's part of the thing we're trying to produce here. So make sure that you don't underestimate what you're going to be exporting. (t: 22700) Where on top of that remember that you don't necessarily need warpers to actually import stuff. So for example over here in these ILSs that I have here. (t: 22710) Even though I do have warpers set because I just like the fact that I can use these 10 vessels to help out with that. Every vessel on a different planet that has access to this resource that is exporting it. (t: 22720) As long as that planet has access to warpers there will be no issues getting all your stuff to this planet. So keep that in mind as well if you want to kind of min-max stuff. (t: 22730) Or you might basically run out of spots to place your warpers in. Okie dokie let's add in 10 more assemblers making particle broadband. A whopping 16 assemblers making planar filters. (t: 22740) And now we're getting to the main part. We're going to do the more high tech level stuff so you can get a little bit more creative. So for example for the quantum chips and the lenses over here. (t: 22750) We actually don't need that many of these assemblers. So what I've done over here is just place an ILS in the middle. So we can actually make sure that they have access to all of those resources. (t: 22760) Although you could probably set this up differently as well and just use belts instead. But we're going to need a lot more building still. So I figured I might as well use the space. (t: 22770) However I did make it so that I'm kind of building it all under the roof of this ILS over here. Now in order to kind of optimize my spots in the ILS. I am feeding in the warpers as you can see from this ILS into this one over here. (t: 22780) I'm using this as a supply depot. But that actually means I don't have any room to export both the lenses as well as the quantum chips. (t: 22790) So the quantum chips are actually going in here. And they're being supplied to the science location wherever that is going to be. And then the lenses are actually feeding in through this belt. (t: 22800) That I probably will have to move later on so it goes a little bit more on the outside. Because I do plan to use this space. And they feed into this remaining slot I happen to have left over here. (t: 22810) Getting close to the end but we still need titanium glass. So this is a very simple layout similar to the one that I just did. But then just dedicated to glass. (t: 22820) Now we actually need a weird amount. That's seven assemblers making these. And that's just annoying me. But if you want you can fit in the last one over here to make it completely symmetric. (t: 22830) But technically you only need seven of these. So you might have noticed that. I actually skipped the production of plastic that we actually do need. For some of the things that we are already producing. And the reason for that is I just hate these buildings. (t: 22840) And it's always kind of weird to fit them in. So I saved them more or less to last. Because we are now actually done with all the things that need smelters or assemblers. (t: 22850) So we're going to fit in the weird looking stuff. Now it is worth noting that if I build something over here. I am actually really building on the equator at the moment. (t: 22860) So this is why I keep going to the next line of. Production buildings. Because I want to make sure that I stay between the equator and the top line of this particular region. (t: 22870) Anyway we have six of these Mark II. Well technically they are quantum chemical plants. (t: 22880) I'm just going to call them Mark II chemical plants. But they are the faster version basically. And we need six of those otherwise you would need twelve. And they are just producing plastic. (t: 22890) So this is a really straight forward recipe. And I'm assuming the oil comes from somewhere else. So I'm using that as something you are going to be mass producing on a particular planet. (t: 22900) For example on one of the planets that I've been showing in the previous episodes. Now similar to how I actually fit in these chemical plants over here. Under this ILS to keep everything nice and tidy. (t: 22910) I actually tried to fit in these particle colliders in this build as well. And I managed to do that quite nicely. So there's eight of these particle colliders in between the other two builds that we just made. (t: 22920) And they are fitting in from this logistics station over here. Now I thought I was going to be smart and stuff. (t: 22930) And I figured well you know that golden rule where I always say you should build from east to west. I can skip that for this once and just kind of fit this in along this line from north to south. (t: 22940) Well of course then I immediately found out why this is always a bad idea. Because A this belt is not really looking straight. (t: 22950) So it's already bugging me just looking at it. Now you can actually make it straight. But the problem is that if you build a very long belt like this. (t: 22960) Let's see if I can replicate that over here. You might actually be able to see that some of these belts actually run towards each other in a little bit. And the buildings themselves also seem to be shifting a little bit on the grid. (t: 22970) So if I remove these belts for a moment you can actually see this happening. So these are actually built in a perfect line at least according to the game. (t: 22980) Hopefully you can see this if I put the lines on. These are actually a little further away from this line. So these are actually built kind of over the line already. (t: 22990) And these are standing a little bit away from the line. So that makes it pretty much impossible to connect the sorters to the belts. And we need three of these to connect. So long story short don't do this. (t: 23000) Doesn't work. Bad idea. And I should have known better. And maybe just as important it looks a lot better if you just fit them into the grid. (t: 23010) And then you can see the gap that I still had over here as well. So I should have done that from the get-go. For some reason it was under the impression that it wasn't going to fit. As you can see I'm actually building these particle colliders two on two. (t: 23020) So there is no space in between these sets of two. But we only need one thing to go out. So I'm importing three items from the belts over here. (t: 23030) And I'm just exporting the strange matter that we're producing over here. And it's then combining with this belt over here. And I'm feeding into the ILS over there. Now. (t: 23040) That makes for a pretty nice looking build if I say so myself. Now this little tiny compact build is actually going to be producing four signs per second of every single color. (t: 23050) Now the actual production of the signs is obviously going to be done in a science hub. And this is just assuming you're not proliferating anything. Because if you are proliferating in your science hub. (t: 23060) So you would be if you're using my version of that. You're actually going to be producing five signs per second from this build. And it's just a little tiny build. So again. (t: 23070) Don't worry too much about using a little bit of space. Because you can fit this several times on the planet. So to be exact. You should be able to fit this blueprint over and over again on this planet for at least ten times. (t: 23080) Assuming that you stick everything close together. Now honestly if you optimize the blueprint a little bit. You might actually be able to fit this gap as well. (t: 23090) So then you could move it up to twelve times. But at least if you just take it easy. And well just copy paste the blueprint like a clock. I've shown it over here. (t: 23100) You should be able to produce forty signs per second on this single planet. Forty signs per second guys. That's a lot of signs. And again that is not counting proliferation. (t: 23110) Because that would move it up to fifty signs per second. Not counting the proliferated signs itself. So that would actually lead up to a production of over sixty signs per second equivalent. (t: 23120) Anyway. Getting ahead of ourselves. Because we're going to need a ton of resources in order to do that. So you're not going to be slapping that away. You're going to be just going down this blueprint. (t: 23130) And then just getting all of that production going. And you're probably going to see that your FPS and play speed is going to drop immensely. If you actually do have all of that up and running. (t: 23140) But hey. We're here to break the game right? Now we do have a lot of space left on this planet. And this is intentional. Because you can use this for whatever you want. (t: 23150) For example those rare resources. You could be producing them on this same planet as well. So. You could just edit in the blueprint for the graphene as well as the nanotubes. (t: 23160) As well as the diamonds. And they're just going to be importing the rare resources over here. And now putting them on the other side. Now I just said you shouldn't be building from north to south. (t: 23170) I know. But the exception to that rule. There's always an exception of course. Is that if you put the building around the equator. The curving in the lines is actually symmetric. (t: 23180) And as long as it doesn't interfere with your actual sorders. Which it doesn't in this particular build. Then I think it's acceptable. So always be ready to break your own rules guys. Don't fuss about that too much. (t: 23190) What else are we doing here? Well we have. Let me actually fly over there. So you can see what I'm doing. I have added in some oil production. Because why not. (t: 23200) We actually don't need that much oil on this planet. We do need the hydrogen. We actually also need a little bit of oil. So we might as well just produce it over here as well. I added in a lot of power. (t: 23210) This is just the hydrogen fuel rod. Sorry the deuterium fuel rod. Power over here. And well there is a lot of power that you can fit around the circle. (t: 23220) To the north. Or south if you want. What else did I do over here? I had a fractionator set up. Because we do actually need quite a bit of hydrogen. On this particular planet. So producing that locally just speeds things up a little bit. (t: 23230) And then just because I could. I figured well you could also just build the actual science build. On this planet as well. Because well after all we are producing most of our science here now. (t: 23240) That would actually imply that you want to. Remove the science dome from the other planet. If you do this similar to what I have done over here. (t: 23250) Because otherwise your science materials are going to be spread. Between two different planets. And that's typically not efficient. Especially since you want all your antimatter. To be well localized as well. (t: 23260) You could of course opt to replace these matrix labs. With the upgraded version as well. Haven't actually done that over here. Mostly because it's a lot. (t: 23270) Well it's not that much work. But I kind of like the looks of these for now. But you can use the upgraded version as well. And especially if you're going to be producing. The actual 50 plus science per second. You are going to have to upgrade some parts of this. (t: 23280) Because otherwise it's simply not going to cut it. Or you can of course stack these things up a lot higher as well. Than I've done in the blueprint over here. So do that in whichever way you want. (t: 23290) Now similarly you could use the remaining space over here. To again produce whatever you want. Keep in mind that you do need to. Make sure that you have a lot of space. To be able to do that. (t: 23300) And that's something that you can do. If you want to. That you do need a lot of power for a planet like this. I mean I have added in a lot of this deuterium power over here. (t: 23310) But it's definitely not going to cut it. So you would need a similar line over here. Because remember the upgraded buildings. Even though it seems like we don't have a lot of them. (t: 23320) Well we actually did copy paste the entire blueprint 10 times. But remember that these things are actually. Requesting a lot more power than the base variant. (t: 23330) So they're a lot faster. But they actually require exponentially more power as well. So you might actually need to resort to putting some artificial stars here. (t: 23340) Or just put in a whole lot of solar panels. That actually goes a very long way as well. If you're on a planet like I am over here. With 136% solar energy ratio. (t: 23350) Don't underestimate the power of solar panels. Even late in the game. Long story short. Just make sure you have enough power on this planet as well. This is actually one of the things. (t: 23360) Why you could opt to not have the ILS's send out their own vessels. Because using warpers actually requires a lot of power. (t: 23370) And if you just have all the other places where you're mining that stuff. Import that to this planet. Or export it in that case I guess to this planet. (t: 23380) Then that's going to consume the power on those planets. But not here. So something to consider. If you're finding yourself low on energy on a high planet. (t: 23390) Or on a production planet like we have over here. There you go. If you want you can add in some foundations as well. To make it look like a whole flying billiard ball. That is actually covered with factories for some reason. (t: 23400) Anyway. Final notes about this type of build. I don't actually recommend to basically copy paste a huge factory like this. (t: 23410) From the get go. Just make a build like this. Again with a smaller set of requirements. In this case we're producing four science per second. In the initial design. (t: 23420) Make sure that you actually have all the resources required to get that up and running. And keep it up and running. Then place down a second version of that next to it. (t: 23430) Get more resources. And basically keep progressing until you have your entire planet up and running. The reason for that is that if you don't do that. (t: 23440) There's going to be a whole lot of inefficiency of bots flying up and down. Power might be low. You're actually producing a lot of power. But you don't need it. You don't need all of it. (t: 23450) Long story short. It's super inefficient to scale up from the get go. Compared to scaling up step by step. Basically making sure you actually have the resources and power to support all of that. (t: 23460) On the plus side. Because that is more efficient. You're actually also going to be progressing through your technology tree a lot more efficiently like that. (t: 23470) So again. There's a lot of reasons to do that. So this is why when you look at my blueprint collection. You will find only the design for this build. And only the design for this thing. (t: 23480) And all of the other things that I've just showed you. Are also in the blueprint designs. But are all separate blueprints. Don't make planet wide blueprints basically. (t: 23490) I mean you can. It's kind of cool to do so. But again it's not super efficient to actually start up your factory from the get go. With a huge blueprint. Also if you do place a planet wide blueprint. (t: 23500) And you don't have a top of the line system. Your game is going to show up. And it's going to show very low FPS. (t: 23510) And very low game pace speed. Because the game doesn't really handle super large blueprints very well. So it's actually faster to build it. Without placing everything down at the same time. (t: 23520) Compared to doing it step by step as well. So lots of reasons basically. To keep everything a little bit more modular than I've done. In this particular what I'm showing you right here. (t: 23530) Let me know in the comments. Is there anything else in this master class. That you feel we haven't really covered yet. That I should really cover. (t: 23540) Because I feel like this should probably be the last episode. But it's completely up to you guys. If there's some good ideas out there. Then I'm more than happy to hear them. (t: 23550) And of course make a couple of more episodes. If that's what you guys want. And if there's actually some interesting ideas. That actually add something to the master class as well. If you're still here. You're awesome. (t: 23560) And I do hope to catch you in the next one. (t: 23562) Bye bye. (t: 23590) Subtitles by the Amara.org community (t: 23620) Thank you for that. Okay so I figured this planet over here. Could be a decent place to start. (t: 23630) It has a pretty decent building area. So we don't need to pave over the entire planet. It has a huge amount of solar energy. Because it's very close to the sun. (t: 23640) So 148 is pretty decent I would say. And on top of that it also has some lava thrown in. Just because we can. Okey dokey. So I sent my fleets to clear out the planetary bases on this planet. (t: 23650) Which works really nicely. Now I just have to mop up all the remaining units here. And as you can see. I'm just standing in the middle of the planet. My units are actually fighting off all the waves that are incoming. (t: 23660) And I have no problems defending myself. Thanks to all the upgrades that we now have in this part of the game. Still takes a little while to actually clear out the planet. But it's not really a challenge anymore. (t: 23670) Now I do want to point out. That it's actually far more efficient to just orbit the planet. Like I'm doing now. Send your fleet to take out the planetary stations. Wait a little bit for the actual power to wind down. (t: 23680) And then start clearing out the planet. Just so you don't have to deal with. Dozens of people. And dozens of waves being constructed in the process. Remember if you just wait a little bit. (t: 23690) For the power to run out. You can just clear them up. You won't have any turrets shooting at you. They won't be rebuilding any units. And that just means you can just take them. And of course make sure you put some geothermal in them. Because we are going to actually need all that power. (t: 23700) Now I want to set up this planet with the minimum amount of work. That I can get away with. And that means that I'm going to reutilize blueprint 47. Which is the planet wide defense hub. (t: 23710) That also comes with some of these power grids. As well as some planetary defenses. That are basically intended to cover the entire planet. I'm not going to bother to actually clear the entire planet. (t: 23720) In terms of flattening it or anything like that. So there might be some resource nodes in the way. There might be lava in the way. It doesn't matter. (t: 23730) I just want to get this up and running. As easily and as quickly as I can. Now to show you why. Without even trying. So I just plopped down the blueprint. And I didn't try to optimize it in any way. (t: 23740) We're already producing about 450 megawatts of power. And that's actually with several of these geothermal not even being connected to the network. I literally did nothing to optimize. (t: 23750) Just plopped down the blueprint. So this is a pretty good place to start. Now the idea is that we're going to transport that power off planet. By means of the energy exchanger. (t: 23760) So I build a couple of them. I set them to charge like this. And then this splitter in the middle is going to make sure. There are empty accumulators going into the exchangers. And then the belt on the outside is going to in the reverse direction. (t: 23770) And those will be transporting the full exchangers backwards. Now full exchangers will move through this building without any issue. So that's not going to be anything that's going to cause any blockades. (t: 23780) Or anything like that. And by using a small section like this. I should be able to connect that up to the grid like this. (t: 23790) And then I can just go around the planet. And build an entire circle of this. Now if you're going to do something similar as this. Make sure that after you put down the first few sections. You actually copy those sections. (t: 23800) And then start using an extended amount of these things. To copy and paste. Because that will speed the process up by a lot. Okay well there we go. (t: 23810) We have everything set up. So let's put those on a belt. And now we should see everything starting up. And I honestly think this is one of the best looking buildings in the game. So there's something very satisfying about seeing all of these pop up one by one. (t: 23820) Would you look at that. All of these things turning on one by one. It just looks so cool. Now just checking the power supply. (t: 23830) So let's turn on our system for a moment. Let's see how much power we have going on in our network. Where's my network? Over there. So about 400 power. (t: 23840) We are actually accumulator charging 331. So that is not the entire power. But we do need a little bit to actually keep our base up and running. Supply the ILS and things like that. (t: 23850) But other than that. This is 300 power we can use wherever. But of course that is not going to cut it. Those are rookie numbers. So let's ramp that up a bit. Now I intentionally picked a system. (t: 23860) A lava planet. Just because I want to see how far I can get on my random lava planet. Without too much effort. Just randomly placing these things down. In a way that looks sort of optimized to me. (t: 23870) But without having to think about it too much. Now the reason why I didn't bother setting up the entire power grid from the get go. Was basically adding in all this power production. (t: 23880) It will easily fill up the gap. But on top of that. On the flip side of that. The signal towers will actually make it real easy to connect all of these random patches of lava. (t: 23890) Up to all of the stuff that I already have up and running. Okay. I think I got all the lava covered now. And I honestly forgot how much work it is to actually set up all these individual geothermal power stations. (t: 23900) One by one. In all the open spaces. It doesn't actually fit everywhere. So there might be some open spaces left. (t: 23910) But I think this is good enough for now. Okay. So let's do another power check. With just the geothermal as well as the couple of solar panels we had at the poles. And we are now at almost 2 gigawatts of power. (t: 23920) So far so good. But let's add some solar panels to it. Because remember. This planet actually has 148% solar energy ratio. So that is the difference. And this is where the literal power differential. (t: 23930) Between the solar panels and the geothermal power station comes in. Because I just added 4000. Actually more than 4000 solar panels to this planet. I covered about the top 1 third of the planet as you can see. There is a lot of planet left that is actually not covered. (t: 23940) And it makes sense. So let's add another solar panel. And then we are almost at the end of the video. So yeah. So now when I go and check the solar panels. This is my first time in country. (t: 23950) It's actually not covered in solar panels. And it would have been more solar panels if the lava wasn't in the way. So take that into account as well. And even with all of that and the fact that we have 148% generation of solar energy, (t: 23960) all of those solar panels added just about 1 gigawatts of power. Now, of course, the problem is that not all of these solar panels will be active at the same time. (t: 23970) So if you want to be more efficient with your solar panels, you are looking for a tidally locked planet, which will always be facing towards the sun in the same way. (t: 23980) So you can just cover that part of the planet and ignore the rest. But even if you take that into account, such a huge amount of solar panels is just not producing that much power. (t: 23990) It's just about 1 gigawatt. And again, I could cover about the other two thirds of the planet as well and add 2 gigawatts more to the planet. (t: 24000) But that's a lot of work and a lot of resources spent on just a minor amount of power. Well, 2 gigawatts of power. It's not a minor amount of power, but compared to what we were getting with just the geothermals from the dark fog, (t: 24010) as well as the ones on the lava planet itself, it doesn't really compare to what we're getting from the solar panels. And there's a couple of things to note when you set up a power farm like I'm doing over here. (t: 24020) First of all, you don't need to produce a massive amount of accumulators. Remember, these things actually get discharged in their destination station, (t: 24030) wherever that is going to be. And then you can fly back the empty accumulators, right back to your power station and start charging them up again. (t: 24040) So as you can see, I have 4000 waiting for me right now because I'm not actually using them. And as soon as a couple of these are going to be consumed, they will feed right back in. (t: 24050) So you only need a minor amount of production of this, and then you can just infinitely keep reusing those over and over again. Now you might be wondering, is it worth it to actually set up a power production planet like that? (t: 24060) And honestly, the answer is it completely depends on what you want to do, because if you look at the power production, you can see that the power production is going to be very, very low. And that's because you're going to be using a lot of energy. And that's because you're going to be using a lot of energy. And that's because you're going to be using a lot of energy. If you look at how much power that is actually going to be producing for you, (t: 24070) as you can see, for example, in our main planet over here, I'm only using about two gigawatts of energy on my core planet. So by itself, the planet that we just made in just maybe an hour of work (t: 24080) is actually going to supply an entire planet worth of production by itself, maybe supplemented with a little bit, depending on what you're actually producing there (t: 24090) and how much power you're required and whether or not you're proliferating and things like that. But it's going to go along. So that's why I'm saying that you can get away to fully supporting one full production planet. (t: 24100) If you're just looking at mining planets, you can probably power at least ten mining planets with just the single planet that we just made. So from that perspective, it's actually pretty efficient. (t: 24110) From a pure optimizing perspective, you could probably get away with just powering everything with deuterium fuel rods that are cheap to make, they're easy to use and produce, (t: 24120) and they actually do supply a lot of power as well. So. It's completely up to you what direction you go, but it's fun to maybe change things up every now and then. (t: 24130) And there you go, guys, an infinite power planet that you could use if you would want to. But honestly, you might want to consider whether or not your system can handle it, first of all, (t: 24140) because if you do fill an entire planet with solar panels, it is going to take a pretty heavy toll on your planet. But on the other hand, it is a quite fun project to undertake at least once in your Dyson Sphere career. (t: 24150) So consider that as well. If you're still here, you are awesome. And I do hope to catch you in the next one.

