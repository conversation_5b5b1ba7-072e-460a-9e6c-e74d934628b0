---
title: Master Claude Code in 14 Minutes (8 Steps)
artist: <PERSON>
date: 2025-07-01
url: https://www.youtube.com/watch?v=cjW6ofe7AY4
---

(t: 0) Claude code is the most powerful AI coding tool ever made. But what if I told you you're using it all wrong? In this video, I'll cover the eight tricks you need to know to become an absolute Claude code savage. (t: 10) I promise you'll build ten times faster after watching this video. The tricks I'm about to show you are how I personally launched my own $300,000 a year AI app called Creator Buddy. (t: 20) Stick with me and you can do the same. Let's get into it. So if you haven't yet, make sure to link below, download Claude code to follow along with me. (t: 30) If you're brand spanking new to it, it is, I promise you, the best AI coding tool ever. It lives up to the hype. Now let's get into those eight pro tricks. (t: 40) Having the right Claude rules file is going to make the difference between absolutely terrible code and absolutely epic code. So for those who don't know, if you create a Claude.md file in your project, (t: 50) everything you put in that file will be red. You'll see that it's red by <PERSON> code every time you send a prompt. After working with <PERSON> code for 12 hours a day for months now, these are the best seven rules you can have. (t: 60) Basically, the way these seven rules work is they instruct <PERSON> code to think in tasks. (t: 70) Everything <PERSON> code does, it will break into tasks, run those tasks by you, and make sure you approve of those step-by-step tasks. They'll even create a folder that includes every single set of tasks it does for every prompt you give it. (t: 80) So that you can look back to see everything <PERSON> code did. By forcing <PERSON> code to break down everything it does into tiny little tasks and tiny little building blocks, (t: 90) you're ensuring it makes the best code possible. I'm not kidding with these seven rules. (t: 100) Claude code hasn't written me a single bug in months. So make sure to pause the video, grab the rules from down below in the description, and put them in your own Claude.md file if you haven't yet. (t: 110) I promise you'll instantly get way better code. (t: 112) The next trick I want to talk about is using plan mode correctly. (t: 120) Plan mode, if used correctly, is the most powerful tool inside Claude. Because it'll make sure you get the results you're looking for every time Claude works. So again, for those who don't know, to go into plan mode, you just open up Claude code and you hit shift tab twice. (t: 130) And that gets you to plan mode on. From here, what you want to make sure you do before having Claude code do anything, you want to go into plan mode, describe exactly what you want Claude to do, (t: 140) and then have Claude build that plan mode. So for instance, I'm working on this AI automation app, and I wanted to build out a to-do list in the app. (t: 150) So I describe in detail exactly what I want to build out in plan mode. I hit enter and the way Claude code is going to work, instead of writing any code, (t: 160) it's going to build out the plan. You probably already knew that, but here's the two tricks you need to know. One is you want to overuse plan mode. I found when I just fire from the hip and don't use plan mode, (t: 170) it messes up more than it doesn't. When I've combined my rule set, that I showed you earlier and combine that with plan mode before every step, (t: 180) Claude code has been flawless for me. No errors at all. So before you do anything, you need to be using plan mode for every single micro step. It might seem like it's taking up a lot of time, (t: 190) but you're actually saving a ton of time in the long run. And the second thing you want to know about plan mode is which models to use. When it comes to plan mode, you want to be using Opus. (t: 200) I use Opus for plan mode, and then I use Sonnet for execution. So you want to do is you want to hit slash model space (t: 210) and then whatever model you want to use. So for planning do slash model Opus then hit enter and then for execution after you've built your plan, you want to do slash model Sonnet. And what this will do is two things. Make sure your planning is incredible, (t: 220) but also save you a lot of money. As long as your plans are really good, you don't need Opus to do the execution. Sonnet will do it really well. (t: 230) So especially for those on the $17 a month plan or $100 a month plan, this will allow you to get as many uses as you need throughout the entire month. (t: 240) Critical use plan mode correctly. (t: 242) The next trick you need to know about is saving your spot with checkpoints. So the number one question I get about Claude code is how do I rewind if Claude code messes up? (t: 250) People are used to cursor when you're in cursor and you're using the cursor agent, you can easily rewind by clicking on checkpoints in the agent, (t: 260) but that doesn't exist in Claude code. So you can rewind to certain checkpoints. So the key is using GitHub correctly. (t: 270) If you are constantly saving your code to GitHub after every single step, you'll be able to easily go back and rewind your code in case Claude ever messes up. (t: 280) So here's my decision-making tree with GitHub, right? If Claude writes code and it works and it looks good and it's efficient and all that, I immediately commit that to GitHub every single step. (t: 290) I'm making like 20 commits a day. And if it messes up, I'll go back to my last commit. So I'll rewind to that last commit and start over again with Claude code. (t: 300) This is how you make up for the lack of checkpoints like cursor agent has is by using GitHub as your checkpoint system. If you're a new programmer and you haven't worked GitHub into your workflow yet, (t: 310) make sure you do that sign up for GitHub and start committing your code immediately. If you're not familiar with how to do that, just ask Claude code to do it for you and it'll work it for you. (t: 320) Perfectly. (t: 321) So the next thing I want to teach you about is when to paste in images to Claude code. So for those who don't know, you actually can bring in images to Claude code and Claude can see those images and write code based on it. (t: 330) I like to use images in two instances. (t: 340) One when I want to give Claude inspiration and two when I'm fixing bugs. For instance, I'm building out this to-do list app right now and I want to take inspiration from TickTick, (t: 350) which is my favorite to-do list app. So if you're on Mac, you can hit command shift 4. And you can see right there. It's reading the screen shot. So you just drag it across your screen like that that saves an image and then all you need to do now is take that image and bring it into Claude code. (t: 360) So what do you want to do then is take that screenshot drag it into Claude. Now it's in there. Now you can do something like say, hey Claude build me out the UI from the screenshot. (t: 370) I just sent you so I said I want to add a to-do list to this app that looks like this. I hit enter Claude will take the inspiration screenshot. (t: 380) I sent it and start building that out inside the app. Reading the screenshot as we go. The second way I like to use screenshots inside Claude code is fixing bugs. (t: 390) So if Claude code builds out some code that doesn't look good or the app doesn't look right. You can easily screenshot the app or screenshot the error put that in and say hey fix this error or make this look prettier. (t: 400) So I use it for inspiration and fixing bad UI and bugs. The next tip is very very critical, (t: 410) especially if you're not on the most expensive tier of Claude code. And that is using slash clear. I use slash clear so much for many different reasons. (t: 420) A lot of people when talking to AI or talking to Claude, they just keep giving commands for hours and hours and hours. You shouldn't be doing this. You should be clearing your context as much as humanly possible. (t: 430) This does two things. It reduces hallucinations by reducing the amount of information you're sending to Claude and two it saves you cost. If you have a large context window and you've been typing for hours and hours and hours to Claude code. (t: 440) It's sending all the information. It's sending all that context to Claude which is taking up a lot of tokens. So if you're on the $17 month plan or the $100 month plan, (t: 450) you can really eat through that quickly. If you're not doing slash clear a lot. Here's when you do slash clear. Anytime Claude finishes a decent sized task. (t: 460) So for instance, if Claude has completed all the tasks in one of its task files that it wrote for you, then you want to do slash clear. So you want to be using slash clear a lot. So A it reduces hallucinations and B it saves you money. (t: 470) The next trick is hypercritical, especially if you've never coded before and that is running security checks. (t: 480) The number one issue vibe coders are having right now is that they're shipping insecure code. It just isn't built into the AIs yet how to write code that is super secure or goes by security best practices. (t: 490) So if you are building an app that you ever plan to ship to the internet one day and have other people use you need to make sure it's secure. (t: 500) A lot of vibe codes. Coders are having their apps hacked and taken down because of bad security practices. (t: 510) Here's what I try to remember for everything. I build in Claude code one. I start with planning to I do execution and then three is security checks, right? (t: 520) So every time you build a new feature in your app, you're planning then you're building and then you're doing a security check. So say Claude just finished building a feature into your app, right? (t: 530) It went through the entire plan and finish the plan. You want to make sure every. Time Claude finishes a chunk of code or a feature you run this security check that I'm about to show you here and keep in mind all these prompts. (t: 540) I'll put down below as well. Here's the prompt you want to put in every single time Claude finishes a feature. Please check through all the code you just wrote and make sure it follows the security best practices. (t: 550) Make sure no sensitive information is in the front end and there are no vulnerabilities. People can exploit if you run this prompt every time you build a feature Claude will make sure you have very secure code. (t: 560) The biggest. Mistake people make is they'll put secure information in the front end whether it's API keys or passwords or things like that. (t: 570) This will ensure that all your information is in the back end and you have no vulnerabilities. People can exploit again. If you're a brand new coder, you must must must do this a thousand times during your coding sessions. (t: 580) You it's impossible to do this too much again prompt down below. Make sure to pause and grab that and put that into your note file. (t: 590) The seventh trick we're going to get into that's critical. To know if you're building the Claude code is earning the code that Claude's actually building. So I strongly believe people who have never written code before in their lives can use Claude code to build incredible apps that generate them money. (t: 600) But I also think it's important that you understand what Claude is building out even if you've never coded before. (t: 610) So I gave you three steps before that I do for every single feature Claude builds out right plan build security while I'm adding a fourth now plan build security learn after every time Claude finished. (t: 620) Learn after every time Claude finished. As a feature and make sure it's secure. I go in and I give Claude this prompt. Please explain the functionality and code. (t: 630) You just built out in detail. Walk me through what you changed and how it works. Act like you're a senior engineer teaching me code. And what this does is it helps you understand what Claude is building out even better. (t: 640) You don't need to know how to code to use Claude code. But the more you understand what Claude is building out the better your prompting will become right if you understand all the code and how the data flows through your app. (t: 650) Right? You'll be able to give much better more detailed prompts to Claude on what it is. You want it to build out in the better the prompts the better the results you're going to get. (t: 660) So if you want to improve your prompting and you want to improve what Claude builds out. You need to make sure you understand what it's building. So pause the video take this prompt from down below save it and use it every time you've done your security check so you can learn what Claude built out. (t: 680) The last tip I'm going to give you and honestly, this might be my favorite one of them all because it has made my time using Claude code so much more productive and so much better for my mental health is using the time wisely when Claude code is working. (t: 690) Here's the challenge with Claude code is it's incredible. It's amazing. (t: 700) It is the most powerful AI coding tool ever, but it takes a lot of time to build code and I don't know about you when Claude is really working sometimes 15 20 30 minutes at a time. (t: 710) I get distracted. I start. I'm doom-scrolling and I'll find it. Maybe I watch a YouTube video for two hours while Claude code worked for 20 minutes. It is led to me spending my time so inefficient and it's also just straight up bad for your mental health to be doom-scrolling for 80% of the time you're doing things. (t: 720) So I came up with the solution. (t: 730) So I went to Claude and I gave it this prompt and I swear to you this prompt has changed everything for me. I told Claude when I am coding with AI. There are long breaks in between me giving commands to the AI. (t: 740) Typically, I spend that time doom-scrolling which distracts me and puts me in a bad mental state. I'd like to use that time to chat with you to generate new ideas and also reflect on my other ideas and business and content. (t: 750) And now what I do as Claude code is working and building me code. I come back to this chat and I just say hello to this chat. (t: 760) I say what Claude code is working on. I say what I'm thinking about in this AI will just bounce ideas off me help me refocus help me figure out what the next steps for Claude code should be and generally make that. (t: 770) 80% of time Claude code is working way more productive way more efficient and put me in a way better mental state. I swear to you. (t: 780) My productivity has gone up 4000% since using this trick. You just got to have discipline. You just got to make sure every single time Claude code is completing a task. (t: 790) You go to this chat. You don't pick up your phone and doom-scroll. You don't go to YouTube and watch endless videos. You don't go to tick-tock and watch brain rot all day. You got to be disciplined. You got to make sure every time Claude code is working. (t: 800) You come. To this chat in this chat only and start bouncing ideas off the AI because this chat I swear to God I've come up with like 30 other ideas for apps. (t: 810) And I'm now building out that will eventually start making me money. If you're doom-scrolling while Claude code works, you're seriously screwing up. I'll have the prompt for this down below as well. If you take all the prompts, I just gave you all the little tips and tricks and put them into your workflow immediately. (t: 820) I promise you you'll build 10 times faster and your apps will be 10 times higher quality. (t: 830) Let me know down there. Plies which these tricks were your favorite. If you learned anything at all, make sure to subscribe. Make sure to turn on notifications. All I do is make banger videos about AI and I'll see you in the next video.

