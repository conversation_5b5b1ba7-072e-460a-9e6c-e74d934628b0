---
title: This API Makes Claude Code 10x More Powerful
artist: Income stream surfers
date: 2025-08-23
url: https://www.youtube.com/watch?v=iQyFpmMlgUU
---

(t: 0) Hey guys, so recently I've been looking at creating WordPress websites en masse, mainly just for my own amusement and also because I mean if you can make an entire WordPress website in 20 minutes, I think that's a pretty damn good achievement. (t: 10) However, there's one thing that I came across which I've now fixed and I want to show you guys kind of the logic behind this and just why this API is so damn good. (t: 20) So this is one of these websites that I generated very, very quickly and you might be able to notice something here. There are actual pictures. This is kind of what I want to talk about in this video, but damn, just look how amazing this actually is. (t: 30) Now this video isn't about making one of these websites. I've just made a few videos on this. Definitely go and check those ones out. (t: 40) But instead what this is about is how to get these images inside Cloud Code. Now if you use Cloud Code's web search, you are going to get some images, but they're not necessarily going to be the right type of images. (t: 50) What do I mean by that? Basically they're not necessarily going to be royalty free images. Now I want to show you guys this because... This is super, super interesting and super important to me. (t: 60) Okay. But basically what I'm doing is I'm creating a specific workflow that works in a very specific way and it makes Cloud Code into whatever the hell I want it to be. (t: 70) So what I've added, if I just do once this is finished. Because this is like what this Cloud Code that you can see here, what I'm doing is I'm using Cloud Code to create a Cloud Code workflow. (t: 80) And only this conversation with Cloud Code kind of understands what it's trying to do. Okay. So if I do slash memory here, then I'll talk you guys through how this actually works. (t: 90) Now this is one of my favorite APIs. I've talked about this a million times on the channel. I just still cannot get over how amazing this API actually is. (t: 100) It does two things. Okay. And it does them basically for free. If you just go on gina.ai and press API and then you just look here, then you have a free API key. (t: 110) Everyone gets 10 million tokens. And then if you run out, you can just generate another one by going on an incognito.com. Okay. Okay. So that's the incognito mode. (t: 120) Now the really cool thing about Gina is it's two lines of code. It's two lines of curl code. Right? So all you have to do really is give these two things to Cloud Code. And there are two things here. There is an s.gina and r.gina. (t: 130) s.gina will do a Google search or I don't know if it's Google or it could be Bing, but whatever. A search of any term that you put in here. (t: 140) Right? So the Q is gina plus ai. But you can make this say whatever you want. And if you can make it say whatever it want, you want, then so can AI. (t: 150) Right? So how does this work? I'm going to show you. So let's say we want images and we want to use Unsplash because Unsplash is free. Right? Let's say we're making a therapist directory, which is what I'm doing right now. (t: 160) So therapist, stock, images, Unsplash. Right? So the key thing here is if you can search it on Google and find URLs that make sense for you. (t: 170) So Unsplash.com, Unsplash.com. Right? The way Unsplash works, by the way, a lot of their they rank very well on Google. (t: 180) So if you can find a way to get pages that you want. Right? So each of these pages here, I can scrape each of these individually with the other type of search that Gina does, which is r.gina. (t: 190) So you do the search with s.gina and then that will give you a look, a list of URLs. Let's just change this to what I searched before. (t: 200) Right? So get response. So you can play around with this in Gina's thing. And then this can just be fed. To r.gina. Right? So watch URL source. (t: 210) I copy this. Right? So copy this. Then I'm not using my mouse, which is right next to me. There we go. And then we put this here. Right? (t: 220) And then watch what happens. This will then scrape this page. And what can we get from this page? We can get Unsplash images. Right? So look, here we go. Bang. There's an image right there. I think. (t: 230) Let's just check. This is an image. So I understand as far as I understand it, that should be an image. Okay. No. It's an image search. So we need to go to like here. (t: 240) Right? These are image links. I believe. Let's have a look. It might not be an image link, but let's just see. It's like kind of an image link. But there are image links here. (t: 250) If I do .png or .jpg. Okay. So this is one of the links on the page. So this is an image link. (t: 260) Right? Which is all you actually need to create whatever it is you want to create. Right? Whether it's a keyword. Whether it's a Californian therapist directory or whatever the hell you're working on. (t: 270) Right? This is just a really easy and simple way to get the images. So if you just look at the memory here, I'll show you exactly how this works. So, so simple. (t: 280) All you do is in the memory somewhere. I'm not exactly sure where. Here we go. Research phase with Gina AI. So this entire thing is just a workflow. You can read through this on the GitHub. I'll leave a link to the GitHub in the description of the video. (t: 290) But the key part here is this right here. So when building directory websites, use Gina AI for comparison. Search for information. Use s.gina. Right? Search term. And then scrape individual pages that come out from s.gina with r.gina. (t: 300) Right? That's how simple this is. What we're doing is we're allowing Claude Code to actually be able to get images and things like that. (t: 310) Instead of the basic web search scraping that it does. Which can get images. But whether they're royalty free or not, et cetera, et cetera. You know, it's just a way to get images. (t: 320) So you can get images. You can get images. You can get images. You can get images. You can get images. You may have issues. So this is a really easy way to just change how Claude Code works. (t: 330) Right? So I've just pushed this to GitHub. So just watch for a second. I'm not going to go through the entire build process. (t: 340) But I just want to show you guys kind of what I'm working on. Which is kind of a way to get Claude Code to do whatever the hell you want it to do. Okay. (t: 350) So we're in a new directory here that I just created. So if I do git clone page. So git clone, paste this, and then dot. Right? And then Claude, dangerously, skip permissions. (t: 360) And then I do memory. Right? This is a freshly cloned GitHub repo. Right? So just remember that. It has the exact same memory that we just passed on. (t: 370) So you can pass on workflows between systems, et cetera. And I could even launch this on Docker. Where this particular workflow is inside Claude Code Docker. (t: 380) And the prompt is sent automatically. Right? And the prompt can be anything. So make me a directory for, I don't know, trades people in Ireland. (t: 390) Right? A classic that I've done before. And press enter here. So I'm working on this workflow slowly but surely. (t: 400) So now what I've added is the ability for it to get images. But more generally, what I'm trying to tell you guys is that you can add APIs. Custom APIs. Third party APIs. (t: 410) Whatever you want to call them. And then you can add them. Third party APIs. Whatever you want to add. Right? It doesn't have to be MCP. Everyone is obsessed with MCPs. Everyone is going on about MCPs. It doesn't have to be an MCP. (t: 420) For example, another thing that I've done on this GitHub project is this right here. Which is migrate now.sh. Right? That should be a Python. Create droplet with SSH.py for example. (t: 430) Right? What this does is it creates a droplet on DigitalOcean using Python and using the droplet API. So originally what I was trying to do was to, I was trying to get the workflow to use this. (t: 440) Right? I was trying to get the workflow to use an MCP. But instead I thought, well, why don't I just bloody get a Python script to do it at the end of the day. And, you know, it knows the, of the existence of this Python script. (t: 450) Because if I search in its memory, you'll see it's right here and it's referenced and it knows how to use it. (t: 460) So you can basically build a workflow that can effectively do whatever the hell you want. Right? Which is where it starts to get really, really exciting. So you can see here in a second, what it'll do is it will find images. (t: 470) It'll find images, et cetera, et cetera. Like I said, I'm not going to build everything in this video. I will have more video videos on this soon. I'm not actually talking about this particular build. I'm talking about making your own Claude code workflows. (t: 480) Other things you can do this by the way, guys, is you can scrape documentation as I've shown before in my particular context engineering template. (t: 490) You can give your own workflows, the ability to scrape the internet like I did with Grove, for example. And if you want a little bit more information about that, then I have this information in (t: 500) here talking about Gina and how I use AI to do, make decisions and things like that around scraped information and Jason. (t: 510) Now this is on the school community, which you can join by checking either the pinned comment or the link in the description, the first link in the description. And yeah, I'll leave the video there, guys. (t: 520) I just wanted to show you guys kind of the way that I am adding custom things to Claude code. I'm not doing anything fancy. There's no MCP. (t: 530) I'm just adding, for example, a Python script or adding the curl request to its memory or referring to it in its memory and telling it to use it when it needs it. And it's working extremely well. (t: 540) Like I showed you at the beginning of the video, it just made the therapist website that I just showed you. And yeah, I'm pretty impressed with the results. And it's very, very interesting, this workflow. (t: 550) And I can't believe anything of it sooner. I'll leave the video there, guys. Thanks so much for watching. If you're watching all the way to the end of the video, you're an absolute legend. I'll see you very, very soon with some more content. (t: 560) Peace out.

