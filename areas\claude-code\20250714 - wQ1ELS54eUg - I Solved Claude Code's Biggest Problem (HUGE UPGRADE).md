---
title: "I Solved Claude Code's Biggest Problem (HUGE UPGRADE)"
artist: Income stream surfers
date: 2025-07-14
url: https://www.youtube.com/watch?v=wQ1ELS54eUg
---

(t: 0) Guys, I'm just going to start recording here because I'm doing something that I think people will find interesting and also it's just a great use case of how I like to use AI. So I'm using ClaudeCode here. Basically what I'm trying to do is I'm trying to get an easy (t: 10) method for me to take a screenshot and then copy it to path straight into ClaudeCode. So I was just chatting with <PERSON><PERSON><PERSON><PERSON> here just to understand how I could do this (t: 20) and then I just basically fed this to ClaudeCode and now ClaudeCode is doing everything for me. I don't have to worry about any of it. Some of these things are actually more complicated (t: 30) than they might seem like the way to download Jerex for example. Okay, so it got me the wrong version because obviously I was on WSL so it got the Linux version (t: 40) but I'm on WSL so I need the Windows version. So you do need to be wary of things like this. (t: 50) So this just opened up the installer here. (t: 54) Again, you might be like, oh, I don't know what to do with this. Oh, fuck you. Now you're installing Windows apps with AI. (t: 60) But it actually does make things a lot easier. A lot of these things, they're powered by devs. Obviously devs have made them. (t: 70) And they're often not totally friendly to set up. (t: 74) Okay, so I'm just following the instructions here. Configure hotkey. I'll just show you guys this just in case people want to do this for themselves. (t: 80) I'm guessing it's this. Click add. Set task. (t: 91) Capture region. (t: 100) Set hotkey. Control shift S. (t: 102) Press okay. There is no okay. Okay, that's saved. (t: 110) Set after capture task. Go to task settings. After capture tasks. Okay, so I'm not exactly sure, but I think it's this one here. (t: 120) Let's just send that to Claude and see what it thinks. (t: 130) I can't. The ironic thing about this is I can't. Well, I can take a screenshot, but this is the whole reason I want this. So I can take a screenshot of something like this and send it to Claude Code. (t: 140) Oh, okay. So I mean, these. Are the options. I've got an improvement at least. So now when I. (t: 150) Take a screenshot. Oh, what? (t: 160) Okay, so if I do control shift S. It uploads it to Imgur. So at least I have a way temporarily to feed things to Claude Code. (t: 170) All right, it's here. It's in the. It's in the hotkey settings. Pressing this button here. Override after capture. After capture copy image to clipboard. (t: 180) Copy image. Where's path? Copy file path to clipboard. Hell yeah, dude. (t: 190) Okay, let's try that. So control shift S. Take a screenshot. And then. (t: 200) Okay, I mean, that wasn't right. Is that right? Yeah, but I don't want the path. I don't want the URL. (t: 210) Okay, so I can get rid of them by clicking them. So save. Copy image to clipboard. (t: 220) Same image to file. Copy file path to clipboard. Okay, there we go. So control shift S. Screenshot. Shouldn't upload it. (t: 230) I should just be able to say. Is the image beautiful? And then I should be able to hit enter. Let's see. If I can process that. (t: 240) File does not exist. It does do this sometimes. Because it's WSL. There we go. Read it that time. Perfect. So this hopefully will say this is okay. It works. There's just something wrong with cloud code right now where it thinks that every image is correct. (t: 250) I'm not sure what's going on there. But yeah, I mean, there's a lot of things that I can do. I can do this. I can do this. I can do this. I can do this. I can do this. I can do this. I can do this. I can do this. (t: 260) I can do this. I can do this. I can do this. And that worked. So if I go here, this should be a screenshot just took perfect. So let's just see if I can fix the problem. That was just trying to fix basically, I'm working on SEO Grove right now just trying (t: 270) to make it look stellar. And there are a few problems. So we take a screenshot. There we go. (t: 280) Please bro, look at this. This isn't right. Fix it. But let's see. (t: 290) So to summarize, what you need to do is you need to create a new hotkey on ShareX. (t: 300) And then once you've done that, you need to set the task here so that it copies the path (t: 310) to your clipboard, and then you can paste it in easily. (t: 320) Let's see if it actually can understand this image because I'm not sure how good cloud code actually is. So the reason why I'm not supporting it, but the chart is still showing very high spikes, (t: 330) indicating the chart data is still not properly beautiful. Okay and I have just transferred, transformed my workflow. This probably won't be like needed for very long, right? (t: 340) But it was just getting to the point where I just, I needed a fix for this. And when I make a fix for something, I may as well let you guys know as well. Thanks for watching. (t: 350) Guys, if you're watching all the way to the end of the video, you're an absolute legend. Check out the school community for more tips and tricks like this one. And if you're watching all the way to the end, you're an absolute legend and peace out.

