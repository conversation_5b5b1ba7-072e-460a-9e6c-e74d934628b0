---
title: Turn Claude Code into Your Own INCREDIBLE UI Designer (using Playwright MCP Subagents)
artist: <PERSON>
date: 2025-08-16
url: https://www.youtube.com/watch?v=xOO8Wt_i72s
---

(t: 0) I keep tweeting about the workflow I'm about to show you, and every single time I do, the tweet goes viral. Why? Because it unlocks the missing 90% of Cloud Code's incredible design capabilities. (t: 10) If you're using Cloud Code, Cursor, or any other coding agent for front-end development, you need to hear this. (t: 20) If you're anything like I used to be, you prompt for a great-looking modern design just to end up with the same generic Shad CN purple UI that you see all over Twitter. (t: 30) Getting pixel-perfect refinements feels impossible, and like you're just going around in circles trying to convince the model to please just do what you asked it. I hate to break it to you, but those cookie-cutter designs aren't the model's fault. (t: 40) It's the environment that you're placing those agents in. You're taking an incredible PhD-level intelligent... (t: 50) ...and you're forcing it to design with essentially blindfolds on. The models can't see their own designs. They can only see the code that they're writing. Or in other words, they're only using the tech side of their modality, not the vision side. (t: 60) What I'm about to show you is a massive unlock our team has found. It's a single tool that gives the AI eyes to see. (t: 70) The missing link in a workflow all builds around the Playwright MCP. Allowing these agents to control the browser, take screenshots, iteratively... ...and then use the AI to create a new version of the model. (t: 80) It's a great way to be self-correct on their designs and to do so much more. I'll take you through my exact workflows that will give you design superpowers. Including the setup for sub-agents, slash commands, the Playwright MCP config details... (t: 90) ...and also how I've customized my clod.md file. In addition to some other hard-won insights. (t: 100) I will also sneak in some of the most powerful mental models and tactics... ...that we've discovered for getting the most out of clod code along the way. Feel free to reference the description timestamps to skip around to what's most relevant to you. (t: 110) By the way, I'm Patrick, the CTO and co-founder of an AI-native startup... ...who's been using clod code heavily since it was first released back in February. (t: 120) This workflow is the single biggest front-end unlock that we've found. We have the honor of working with the world's largest brands... (t: 130) ...including Google, Coca-Cola, Disney, Nike, Microsoft and others... ...who expect world-class designs. So we're constantly looking for ways to get any edge that we can. (t: 140) And I hope you can benefit from these workflows too. So with that, let's dive in. Playwright is a framework developed by Microsoft that is actually more for web testing and automation... (t: 150) ...allowing you to navigate around the browser and take screenshots and do different end-to-end tests. But what's really, really powerful for our purposes is the MCP that they've released. (t: 160) So I'll go ahead and show you the Playwright. As you can see, we've got almost 76,000 stars at the time of this recording. (t: 170) They highlight Chromium, WebKit and Firefox as different browsers that you have access to. But if I navigate over to the Microsoft slash Playwright dash MCP repository... (t: 180) ...we've got a bunch of README items, including Quick Start Guide here for clod code and other agents. (t: 190) As you can see, it is really easy to add Playwright to clod code, but I will revisit this in a second... ...to give you a few more configurations. Now that you know a little bit more about Playwright, I want to introduce you to the key concept that I keep coming back to as I add new tools. (t: 200) And in this case, for a design workflow. And that is this orchestration layer. (t: 210) See, what we want to do is we want to put clod code in a framework to give it a foundation where it's able to have all the context it needs... ...all the tools to go out and take actions or get additional context and then clear validation state. (t: 220) Examples of good and bad outcomes. Style guides or anything else that can give it a definitive example of what is needed in terms of output. (t: 230) So if you have the validation, such as a UI mock or style guide, you have the tools such as, of course, Playwright in this case... (t: 240) ...and you have the context, well-written prompts and documentation, you will get so much more success out of clod code than it comes just out of the box. So in this case, we're focusing mostly on the Playwright tool and also the validation step, which is baked into some of my sub-agent workflows. (t: 250) The second key is the validation. The second key insight that really brings the 10x and the 10x design flow is this idea of an iterative agentic loop. (t: 260) Imagine as we get more and more capable models, what we want to do is we want to give them access to more and more of our workflow... (t: 270) ...so that they can not just run for five minutes, but they could run for half an hour or an hour or even longer than that. (t: 280) The huge unlocks that we get in productivity come from this iteration loop that allow these agents to not just run for longer... ...as I mentioned, but also to come to much better outputs. (t: 290) We need a fixed spec or validator in order to iterate against so that we can compare the output that clod code gets again and again until we get exactly the output that we're expecting. (t: 300) In this case, you can imagine clod code first looks at a spec, so a style guide, a UI mock, whatever you're providing in the prompt and some of these other bits of context. (t: 310) And then you allow it to go through the process. And then you allow it to go through the process. So we can go ahead and tool check or look at the playwright screenshot in our case and compare that iteratively to what it's building and back to the spec. (t: 320) So if it's able to go out, make some changes, take a screenshot, look at that and then identify, oh, shoot, this SVG is nowhere close to what the user asked me and then go back again. (t: 330) That iterative loop is what really gets us to these full agentic workflows and saves us a ton of time because we can kick off a process. (t: 340) Go work on something. Don't put him against the nem Рrelation look like a (t: 370) something else. of the training data. So this is books, just general stuff on the internet. But we also have code that these foundation labs are training more and more on. And then we have images and the (t: 380) multimodal models. Multimodal meaning, of course, you're bringing in all kinds of different modalities, including images and text in this case. The thing is, though, when we're typically using cloud code, (t: 390) we're not tapping into the images side or the visual modality within cloud code. Indirectly, we get a little bit of that benefit, but we're really just looking at code best practices (t: 400) and other design principles. But we're not allowing the model to use its intellect when it comes to understanding and looking at visual design. So we're missing out on all that (t: 410) intelligence in the model, all those neurons, if you will, or circuits that help the model parse visual bits. And that's where being able to provide a screenshot via the Playwright MCP (t: 420) unlocks all of that potential, which as you can imagine, when you're looking at designs, (t: 430) that is a huge, huge, huge, huge help versus not being able to think about things from a visual perspective, but more from an abstract or coding best practices perspective. (t: 440) So the Playwright MCP, getting all that additional visual context, unlocks a lot of intelligence as well from Cloud Code. So if I go back here, we can scroll down to a few of the Playwright capacities (t: 450) that are most helpful. The first is being able to automatically capture screenshots. So you can imagine allowing Cloud Code (t: 460) to open up pages that you're working on or to automatically trigger that through a Cloud.md configuration, sub-agent or a slash command that you can run, which I'll show you in a second here. (t: 470) This is the most powerful piece because it unlocks the vision modality within Cloud Code and allows you to enhance its ability to think critically through designs (t: 480) and also to see pixel perfect captures of different UI elements that need to change. The second one is being able to read browser or web content (t: 490) or console logs. So having access to both the browser console logs and the network logs in order to basically view them and automatically read and make changes as needed. (t: 500) We can also emulate different devices in various browser sizes. So you're essentially setting the Chrome or whatever browser window when it launches. (t: 510) And you can also emulate, for example, an iOS device. You can navigate around the browser. So you can click around, enter form field data, and Cloud Code can automatically view, you can automatically look at context (t: 520) and make the next step. Okay, so that's awesome. But what does it actually get us in terms of workflows? These are the best workflows that I've found. The first that is mostly the theme of this video (t: 530) is being able to agentically iterate on the front end using the screenshots and the logs that it gathers. And this is the key to really producing (t: 540) much better looking UIs. The second is automatically being able to fix any obvious UI errors or errors that are in the console. Then you have the ability, and this is really cool, to navigate the browser. (t: 550) You can imagine if you have a user spec of, I do X, Y, Z, and I get an error, or there's a visual error when this state happens, you can ask Cloud Code to navigate in that same method, (t: 560) click buttons, enter form field data, and navigate around in order to reproduce a certain state, and then grab the console logs or any other context that's needed (t: 570) in order to help solve your issue. Another cool workflow is being able to visually render and screenshot or scrape different reference URLs. So you can imagine if you put in a URL or a couple of them that reference (t: 580) a beautiful design or a website that you would like some inspiration from, you can include that in your prompt or spec, and then let Playwright go out, (t: 590) navigate that locally on your browser, and take a screenshot of those pages or get any other context. And then you have the original intent of Playwright, which is the automated end-to-end testing or any accessibility audits, (t: 600) being able to ask it to go and look for any accessibility issues. We have mobile responsive testing, which is really helpful, even if you just do a quick tablet, desktop, and mobile view port size, (t: 610) or of course, emulating like an iOS device to just get a quick gut check on if there are any mobile responsive issues. One kind of cool use case that I actually had Cloud Code come up with on its own (t: 620) is being able to scrape data. I was using Firecrawl to gather some data, which is another MCP from a few websites, and it got a 403, i.e. it was blocked on a couple of them. (t: 630) So it went ahead and spun up a new Playwright browser in order to load that same webpage, and then gather all the data on its own, (t: 640) which I thought was pretty clever and just cool to see these emergent properties. And on that note, what's so cool is that these MCPs can allow Cloud Code to do so much more (t: 650) than just in the coding modality or in how we typically think of, for example, Playwright. It really gives it full access to your browser to be a full browser-based agent. (t: 660) And you can imagine from the data scraping idea to automatically logging in and submitting data or getting to a certain end state in your app or just navigating a website (t: 670) to do almost anything, this is a really powerful unlock for Cloud Code. All right, I'm gonna show you a couple key installation details that you may wanna consider when configuring Playwright. (t: 680) In addition to my sub-agents, pod, MD file customizations, slash commands, and a few other details that have been a huge game changer for me. (t: 690) So with that, you can see we're able to configure different browsers, but this is done at the MCP config. So this is a big level. So with some MPC configurations, (t: 700) you'll define it in a JSON blob like this, and you can see you can supply different arguments, but that could alternatively be the browser that you want. (t: 710) So two I wanna call out are the browser that you're using, also a device if you want to emulate, for example, in the iPhone 15. And then another one that's really interesting (t: 720) is being able to run in headless or headed mode. I usually run it just in the default, which is headed, so I can see it up and navigating the browser. You can easily grab in the installation section (t: 730) for cloud code, just this one line here in order to install the MCP. I can just run the simple command. In this case, I've already got Playwright installed, (t: 740) of course, so I'll go ahead and fire up cloud. Then now if I type forward slash MCP, you can see that I've got it installed here, along with a couple other MCPs. (t: 750) This is just my personal website for demonstration purposes. So I don't have, I just have a couple MCP options here. Here, you can see exactly what configuration it's using (t: 760) and arguments that it's applying. And you can also view the tools, which show the many different tools that it gives cloud code access to. There is a vision mode for Playwright, (t: 770) which allows it to use coordinate-based operations instead of the default, which is the accessibility map, which is basically just a way to navigate the different elements within a website, (t: 780) which is a lot faster and easier. But for some applications, it can be better to use vision mode. So that's another argument you might wanna consider using. I know these config files like a cursor rules or CloudMD (t: 790) can sound pretty boring when you first think about it. But in this case, if you watch Anthropix, different YouTube videos, (t: 800) or if you read through all their documentation and guides, they really think about these CloudMD files as being memory for the agents. So everything you write here is basically put (t: 810) right after the system prompt when you start up any cloud code session. So any details that you want to be able to use, you can do that in the CloudMD session. So any details that you want to be able to use, can be brought into every single session that you're using. The shortcuts that prevent cloud code (t: 820) from having to go grip around and grab a bunch of context or any best practice or rules that you want it to follow, like a style guide or get styles. So like, how do you want commits and branches (t: 830) and PR structured? All that should live in something like this so that it's pulled in automatically. And what that means is just one less thing. It's essentially an automation. One less thing that you have to worry about (t: 840) every time you're using cloud code that just abstracts it away from your mind. And it's also portable. So other people on your team can take your code take that exact same cloud config file and move it around. So with that, one of the biggest lifts (t: 850) when it comes to Playwright in terms of getting that agentic loop that keeps moving is to add a configuration down here that speaks to that. (t: 860) So I've got this visual development section and in there, I've got a design principles spot here. This basically just points cloud to a few different documents (t: 870) that I provided in this context folder. I'm a big fan of doing this where I'll just drop a bunch of contexts in this folder. So in my case, it's like for a personal website summary (t: 880) of my LinkedIn, kind of my life story listed out a bit, but also design principles and a style guide that I want cloud code to follow. So if I open up the design principles file, (t: 890) you can see this is just a long list of a bunch of different principles I want cloud code to follow. In this case, I actually used Gemini deep research on all the best design principles (t: 900) for a specific aesthetic that I like a lot. I had it make that into a much more concise Markdown file, went through, edited a few things, and then I use that as my design principles MD file. (t: 910) I do find that doing the deep research approach for SEO best practices or design best practices or backend architecture principles is an amazing way to kickstart what you're working on, (t: 920) especially if it's a little bit out of your domain of expertise. Incredible way to use just this massive amount of knowledge and to make it actionable. (t: 930) Going from collecting that knowledge in a deep research platform into an actionable thing that an agent like me, like Cloud Code can take and run with. And then if I go down here, this is where I lay out specifically how I want Cloud Code (t: 940) to use the Playwright browser on a kind of a normal day-to-day level. Whenever you're doing anything that involves the front end, (t: 950) I want it to go ahead and navigate to the pages that were impacted by those front end changes, and then reference this verification step of the orchestration framework by using these Markdown documents. (t: 960) And then look for any acceptance criteria that may have been laid out in the prompt, and then look for any acceptance criteria that may have been laid out in the prompt, that I had written. So Cloud Code can look and see what exactly I supplied. (t: 970) So again, this could be a UI mockup. This could be just some text and other instructions that I gave it. A Figma MCP. There's all kinds of acceptance criteria that could have been brought into the prompt. And then I want it to just pull up the normal browser size (t: 980) that I've got here for the desktop viewport. You could also have this be a mobile or tablet size or include all three of them. But just for the sake of time, (t: 990) I just want it to quickly open up the window and look. And then of course, I also want to check for any console errors, because that's a huge time-saver. In addition to that, I want it to sometimes go into a comprehensive design review. (t: 1000) So this is where it will use the sub-agent that I created that I'll mention in a second here, in order to go ahead and do a much deeper dive (t: 1010) than just this quick test that I showed above. If I'm creating a PR, I want it to go ahead and do that, or any very significant UI UX refactors, and a few other key details that I want it to remember, (t: 1020) just to make sure that it doesn't try to bring in any new frameworks or libraries or anything. And then I want it to go ahead and do that, or any very significant UI UX refactors, and a few other key details that I want it to remember, just to make sure that it doesn't try to bring in any new frameworks or libraries or anything, One powerful trick I want to call out here, this is a huge tip, and I've learned so much by doing this, (t: 1030) is being able to go reference the examples that Anthropic gives when it comes to how to configure any sort of document, but especially things like sub-agents, the CloudMD file, (t: 1040) slash commands and actions that run in GitHub, for example. So the first thing I just want to point out is their GitHub. They've got a lot of great stuff in here, (t: 1050) some actual courses, the cookbook, which is a lot of different examples. of interesting ways to use Cloud, and then also the Cloud Code Security Review, repo. (t: 1060) It's a slash command. So just as a reminder, whenever you type forward slash, it's these commands that come up. And I've got a few custom ones that I built in accordance with this convention, (t: 1070) where you have a .cloud folder, and then in there, you've got a commands folder, and then you can create these markdown files. So what I'll do is I'll just look at, what is Anthropic doing? Like, how are they structuring these commands (t: 1080) or these sub-agents? And there's a lot of cool stuff in here. For example, in this case, this ability to basically just take your working or work in progress files (t: 1090) that are not in a PR yet. So this was a great little workflow I borrowed, and just looking at how they structure things, how they use capital, all caps in certain cases. Overall, it's great to learn (t: 1100) from exactly how they're building things. Another guide I would highly recommend, I've recommended this to so many people, is the Cloud Code Best Practices for Agentic Coding Guide. This does a great job, (t: 1110) especially for things like CloudMD, breaking out exactly how to structure things in the methodology behind it all. And then I would also recommend the documentation. It's very well put together. (t: 1120) A lot of great examples specifically for Cloud Code here. All right, so with that, I'll go ahead and show you my .Cloud directory. Then we've got an agents directory and a commands directory. (t: 1130) So in agents, you can see I've got the design reviewer. Just as a quick example, in order to invoke this, I'll just do at agent design review, and I could give it like a PR or more instructions like, (t: 1140) please review the last three commits that I've made. And then that's gonna go ahead and kick off the agent design reviewer. (t: 1150) You can see here, it's pretty intelligent. It's going ahead and using get to just grab the last three commits. And it's gonna go ahead and launch the agent in order to follow the exact workflow that I laid out there. (t: 1160) So while it's running, I'm gonna go ahead and show you that workflow. So I've got a name, description of what it's doing. You can see it's a design review agent (t: 1170) that is able to look at pull requests, or general UI changes. And then I give it specific access to different tools. So I'm able to explicitly ask it to use (t: 1180) just basically Playwright, context seven, which is for documentation and really great MCP as well. And then also the built-in tools that you typically give an agent. (t: 1190) I'm having it use Sonnet for this kind of work. I feel like I haven't noticed a huge difference or any difference between Sonnet and Opus. And of course, Sonnet's way cheaper. And then I just give it this description (t: 1200) of what I'm asking it to do. So in this case, I'm channeling, trying to get to the areas within its circuits or its neural net that are to do with design reviews (t: 1210) and principle level designers. So I'll usually give it persona to try to channel that. And then a few examples. So Stripe, Airbnb, Linear, being some cliche classics in Silicon Valley. (t: 1220) And then I give it a core methodology and mission to go on here for reviewing. And then I give it a step-by-step guide of exactly how to go about it. (t: 1230) So I'm going to go ahead and start out doing a robust design review, including looking for accessibility, code health or robustness. So you can see it goes through each of these steps. (t: 1240) And then I also give it a format for how exactly I want the report to look. And then just ask it not to do much more than that. So you can see the structure here (t: 1250) and then also the exact process for navigating like which tools to use and when. In order to come up with this, I actually used another deep research report. (t: 1260) And it was a very interesting one. And I think it's really useful. And I think it's really useful because it's really helpful to be able to go out and basically collect the best design review practices (t: 1270) from essentially all of the internet and had it all filtered through Claude Opus in this case, to shape it up using the agent creation tool in Claude code, (t: 1280) in addition to referencing different examples that Anthropic has put in their GitHub repos. So just to show that, you can go and open up by typing in agents. (t: 1290) So I'll start off by just creating a new agent. I usually just do the current project. I will almost always do the generate with Claude. I took the deep research summary. (t: 1300) I fed all that into here, along with like a paragraph descriptor of exactly what I want the agent to do. And then I let Claude's built-in process here, (t: 1310) go ahead and create the initial draft of the agent. And then I took that draft, which was just a markdown file in the .Claude agents directory. But then what I did is I asked, (t: 1320) Claude code to go ahead and take the documentation from Anthropic's website into review and edit the agent markdown file in accordance (t: 1330) to what the best practices layout and those examples of other ones that I had to work with in order to really get a concise, great looking document. (t: 1340) So I did that exact flow for the slash commands, the agent, as I just mentioned, and my Claude.md file. And I feel like that really helps you get concise, actionable workflows. (t: 1350) By the way, if you are finding value in this YouTube video, it means a ton if you would give it a like and subscribe, if you're interested in learning these new AI native workflows. (t: 1360) And as a thank you, I will include in the description links to all of these files so that you can download them and reference them and use them as you wish. All right, so let's go ahead and look at our sub-agent. (t: 1370) Could you please look at the homepage on my website, the main page, and give me a detailed review as outlined in the agent review configuration. (t: 1380) So I've got the agent, the agent, and the agent. So as you can see, that wasn't the best articulated prompt, but it's enough to get the agent to go ahead (t: 1390) and proactively work. So I've got here a window that was opened up. So it's identifying that we have the port in use. Okay, great. So I went ahead and loaded my personal website here. (t: 1400) It is adjusting the screen size and it's grabbing screenshots in order to collect some UI context that it can bring back to see what it needs to fix. It's pretty surreal watching these work. (t: 1410) And your mind just starts to go a little bit more down the line, a little wild thinking of all the applications that you could use for this to automate different parts of your workflow. I'm just thinking of all the time I spend doing mobile responsive testing. (t: 1420) And also the times I forget to do it in that ends up in a bug where this can just solve that because cloud can come back to us with a fully baked version. So in this case is asking if it wants to go ahead (t: 1430) and try submitting the email signup form that I have on my website. So I'll go ahead and say, yeah, you can see it. It went ahead and typed in and subscribed to my newsletter just to get a sense of, (t: 1440) the context there for what it looks like, which is just fantastic. Also every time I click yes and don't ask me again, it will save that into my local file (t: 1450) so that cod code knows it can just go ahead and do these things. Another cool element with these is you can have the sub agents call other sub agents, which is really helpful when it comes to creating a network (t: 1460) of almost like conditional logic. So you can have one design reviewer invoke like a mobile designer, for example, and other reviewers. (t: 1470) And then in aggregate, people can have a lot of work done, all of these have their own context and they use their own models. You can specify if it's Sonnet or Opus or whatever model you want to use. And because of that, you don't cloud the main context (t: 1480) of your main thread in cloud code. So you can have a bunch of sub-agents go do a bunch of work and summarize and bring back the executive report. For example, a list of to-dos that need to be (t: 1490) changed, or in this case, a design review report. If you look over here, we've got the official report back from the design review agent, and it's got some constructive feedback and A-minus, (t: 1500) I'll take it. I don't claim to be an amazing designer. It's got here a few strengths that (t: 1510) list out some high priority issues to work on. Multiple image preload warnings affecting performance. Okay, that's helpful to know. Third-party script error from ptxcloud.net (t: 1520) and misconfigured meta tag. More subjective things too, like newsletter iframe needs better integration is a great example of where it's using the vision side of its neural net. Now, of course, this is a really basic example. Typically, (t: 1530) we're using this on much more meaningful or like new development. You can imagine how getting this (t: 1540) feedback and then having the model actually address the feedback, creating that iterative loop is really powerful. Automatically addressing any errors or issues is helpful. But in this case, (t: 1550) what I would typically do is I would just invoke it saying like, hey, could you please address the above issues? You know, everything within high priority or maybe I'd outline, you know, just a couple (t: 1560) specifics that I want here. But you can also chain these in different loops, as I mentioned before, with the subagents that can call each other. Very powerful way to iterate agentically and (t: 1570) automatically to a much better end result than what you get on just a first pull of cloud code. One extra bonus I'll throw in for you is this idea of get work trees. So a big concept within (t: 1580) using cloud code that I found to be an extremely big unlock is doing multiple things in parallel. You can work on different projects that (t: 1590) don't interact with each other. That's nice. But I think another strategy that has been really (t: 1600) helpful is trying to change my perspective to have much more of an abundance mindset, not feeling like I'm wasting Claude's outputs by scrapping what it comes up with. Because these models are stochastic in nature, you get varied outputs upon each poll or each time you prompt it. (t: 1610) That can be an issue, or you can use that in your favor, but you can also do that by running multiple of these at the same time. In order to do that, (t: 1620) what I typically will do is I will use get work trees. So in this case, I've got another repository. If I go back here to Patrick, to the root, I've got a second one, which is two, (t: 1630) and that is using a get work tree, which is a way to very easily set up essentially a copy of your (t: 1640) repository. So you can create multiple work trees and it's almost like you have three separate versions of your repository, but it's all within one.get folder in your main repo. And each one of them has its own branch. So in my case, I've got Patrick Ellis IO two, I could kick off a Claude (t: 1650) code in both of them and have them both iterate on some front end changes and look at the two to kind of AB contrast, which one I think looks the best and go from there. Typically I'll do this with (t: 1660) like three different prompts if I want to, or the same prompts, but kicked off in three different work trees in order to help get a variety of outputs. One of my friends took this to another (t: 1670) level. It'll actually kick off three different processes, including the GitHub workers using headless Claude codes that will come up with three different (t: 1680) outputs. And then he'll use another model, another Opus to judge which of the three is the best. Another thing that I find is really powerful with these workflows is the ability to package up these (t: 1690) processes that are team members use. So maybe we've got an excellent designer or an excellent engineering manager. Who's really good at code review or backend architect or anything else. (t: 1700) What's really neat is being able to package up their expertise into. Something like a sub agent or a slash command or even an MCP and distribute that across the team (t: 1710) so that you can benefit without even knowing the nuances of the workflow from an expert designer. (t: 1720) When it comes to providing the models context in a prompt, I would highly recommend also including is many visual design elements as you can as screenshots. So dragging in things like even (t: 1730) a low fidelity sketch, kind of a UX wire frame that you want or references to other designs. Obviously a style guide if you have it or a collection of different inspiration or kind of (t: 1740) design board elements that you want to bring in anything that you can use to channel the visual modality of the models intellectual capacity. In addition to all the coding details, such as being (t: 1750) able to specify front end frameworks or best practices, things like hex codes for colors and (t: 1760) typography and everything else. Those two combined are very powerful. At the end of the day, the performance of these models comes down to context. So I hope that this overview of the tools and the validation side has been really helpful and (t: 1770) actionable for you. Thank you so much for watching and stay tuned for more videos like this. In the (t: 1780) meantime, if you enjoyed this video, I think you'll really enjoy my recent deep dive with my friends Anad and Galen at a Seattle founders group on all of our best practices for cloud code. At least all (t: 1790) that we could fit into our presentation. You can find that video above along with my most recent video. Thanks for watching.

