{"main": {"id": "d9bdb4666a055b90", "type": "split", "children": [{"id": "adc5da8f34612398", "type": "tabs", "children": [{"id": "782c0798a5c81404", "type": "leaf", "state": {"type": "markdown", "state": {"file": "areas/claude-code/20250722 - wYWyJNs1HVk - Wait.. Claude Code is MADE Slow on Purpose_ Heres How to Fix It.md", "mode": "source", "source": false}, "icon": "lucide-file", "title": "20250722 - wYWyJNs1HVk - Wait.. Claude Code is MADE Slow on Purpose_ Heres How to Fix It"}}, {"id": "0ccf4c9f5a93f62e", "type": "leaf", "state": {"type": "markdown", "state": {"file": "areas/dyson-sphere-program/20240719 - 0Kec4_zYaJo - Dyson Sphere Program - Masterclass 2024_   The Movie.md", "mode": "source", "source": false}, "icon": "lucide-file", "title": "20240719 - 0Kec4_zYaJo - Dyson Sphere Program - Masterclass 2024_   The Movie"}}], "currentTab": 1}], "direction": "vertical"}, "left": {"id": "4780c63af1a6b7be", "type": "split", "children": [{"id": "29127a141fb0bc20", "type": "tabs", "children": [{"id": "10a694ff40f65760", "type": "leaf", "state": {"type": "file-explorer", "state": {"sortOrder": "alphabetical", "autoReveal": false}, "icon": "lucide-folder-closed", "title": "Files"}}, {"id": "2fadbc727e65a705", "type": "leaf", "state": {"type": "search", "state": {"query": "", "matchingCase": false, "explainSearch": false, "collapseAll": false, "extraContext": false, "sortOrder": "alphabetical"}, "icon": "lucide-search", "title": "Search"}}, {"id": "f0327e7368e0fab8", "type": "leaf", "state": {"type": "bookmarks", "state": {}, "icon": "lucide-bookmark", "title": "Bookmarks"}}]}], "direction": "horizontal", "width": 887.5}, "right": {"id": "47ae080d9fa490b1", "type": "split", "children": [{"id": "a97e19bcdb27cc64", "type": "tabs", "children": [{"id": "fbab9ad66ddc6271", "type": "leaf", "state": {"type": "backlink", "state": {"file": "Dyson Sphere Program - Part 1 - In the Beginning.md", "collapseAll": false, "extraContext": false, "sortOrder": "alphabetical", "showSearch": false, "searchQuery": "", "backlinkCollapsed": false, "unlinkedCollapsed": true}, "icon": "links-coming-in", "title": "Backlinks for Dyson Sphere Program - Part 1 - In the Beginning"}}, {"id": "9e2335c6017f395f", "type": "leaf", "state": {"type": "outgoing-link", "state": {"file": "Dyson Sphere Program - Part 1 - In the Beginning.md", "linksCollapsed": false, "unlinkedCollapsed": true}, "icon": "links-going-out", "title": "Outgoing links from Dyson Sphere Program - Part 1 - In the Beginning"}}, {"id": "57e04d6d03c4a727", "type": "leaf", "state": {"type": "tag", "state": {"sortOrder": "frequency", "useHierarchy": true, "showSearch": false, "searchQuery": ""}, "icon": "lucide-tags", "title": "Tags"}}, {"id": "2fa8c2e07fb31df6", "type": "leaf", "state": {"type": "outline", "state": {"file": "Dyson Sphere Program - Part 1 - In the Beginning.md", "followCursor": false, "showSearch": false, "searchQuery": ""}, "icon": "lucide-list", "title": "Outline of Dyson Sphere Program - Part 1 - In the Beginning"}}]}], "direction": "horizontal", "width": 300, "collapsed": true}, "left-ribbon": {"hiddenItems": {"bases:Create new base": false, "switcher:Open quick switcher": false, "graph:Open graph view": false, "canvas:Create new canvas": false, "daily-notes:Open today's daily note": false, "templates:Insert template": false, "command-palette:Open command palette": false}}, "active": "0ccf4c9f5a93f62e", "lastOpenFiles": ["areas/dyson-sphere-program/playlist.md", "areas/dyson-sphere-program/20240719 - 0Kec4_zYaJo - Dyson Sphere Program - Masterclass 2024_   The Movie.md", "tmpimk6rivy.tmp-Frag1", "tmpimk6rivy.tmp", "tmpp3n2vkzs.tmp-Frag1", "tmpp3n2vkzs.tmp", "tmpyhbgo7w_.tmp-Frag1", "tmpyhbgo7w_.tmp", "areas/none/playlist.md", "areas/claude-code/20250722 - wYWyJNs1HVk - Wait.. Claude Code is MADE Slow on Purpose_ Heres How to Fix It.md", "areas/claude-code/20250721 - AXz6TMAwqnY - The Complete Claude Code Workflow (90% Skip This).md", "areas/claude-code/20250720 - SsBsmhy24hM - Claude Code ПОЛНОЕ ВВЕДЕНИЕ!.md", "areas/claude-code/20250717 - 8T0kFSseB58 - <PERSON> Code - Getting Started with Hooks.md", "areas/claude-code/20250714 - wQ1ELS54eUg - I Solved Claude Code's Biggest Problem (HUGE UPGRADE).md", "areas/claude-code/20250714 - imz5WWVpkRc - ULTIMATE Claude Code_ UPGRADE Your CLAUDE CODE NOW! with these 15 TOOLS!.md", "areas/claude-code/20250714 - FVedw8QGqq4 - This will 10x your Claude Code setup (TaskMaster AI + Claude Code guide).md", "areas/claude-code/20250714 - 82aRn5-ZF7I - Playwright MCP + Claude Code = The END of Manual Debugging (INSANE).md", "areas/claude-code/20250713 - yAslhELtKEg - 5 simple Claude Code workflows you must have - beginners guide.md", "areas/claude-code/20250713 - Y4_YYrIKLac - Agentic Coding with Claude Code.md", "areas/claude-code/20250713 - TSSntdDNcjY - RIP Context7_ Try This Context Engineering Method Instead.md", "areas/claude-code/20250713 - 8Yc62ADKByM - <PERSON> Code Meets MCP_ Building an AI-powered movie app.md", "areas/claude-code/20250713 - 4jD69eHZK8Y - Master AI Powered Project Management with Claude Code (Full Guide).md", "areas/claude-code/20250712 - Q4gsvJvRjCU - How <PERSON> Code Hooks Save Me HOURS Daily.md", "areas/claude-code/20250712 - 0Zr3NwcvpA0 - Your LLM Framework ONLY Needs 100 Lines.md", "areas/claude-code/20250711 - n7iT5r0Sl_Y - How I use Claude Code (+ my best tips).md", "areas/claude-code/20250711 - e4h7Lw-s1fI - I Was Using Claude Code WRONG Until I Learned These 8 Hacks (Steal them... ESPECIALLY 6 & 7!).md", "areas/claude-code/20250711 - 6Rg5M69bMgQ - <PERSON> Engineer is INSANE... Upgrade Your Claude Code Workflow.md", "areas/claude-code/20250709 - _GMtx9EsIKU - We Made Claude Code Build Lovable in 75 Minutes (With No Code).md", "areas/claude-code/20250709 - d7Pb73dbcIM - How To 10x Your Notes_ Obsidian + Claude AI Agents.md", "areas/claude-code/20250708 - ODYkRzYYx2k - Unlock Leverage In Claude Code Using Sub Agents + Gemini MCP.md", "areas/claude-code/20250707 - T_IYHx-9VGU - I Went Deep on Claude Code—These Are My Top 13 Tricks.md", "areas/claude-code/20250707 - J5B9UGTuNoM - I’m HOOKED on Claude Code Hooks_ Advanced Agentic Coding.md", "scripts/__pycache__/3_create_transcripts.cpython-311.pyc", "scripts/3_create_transcripts.py.tmp.7180.1756548169281", "scripts/versions/3_create_transcripts_202508300559.py", "scripts/versions/3_create_transcripts.py"]}