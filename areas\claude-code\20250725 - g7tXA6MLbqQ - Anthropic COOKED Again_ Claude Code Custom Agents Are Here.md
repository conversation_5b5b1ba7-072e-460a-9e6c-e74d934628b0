---
title: "Anthropic COOKED Again: <PERSON> Custom Agents Are Here"
artist: Income stream surfers
date: 2025-07-25
url: https://www.youtube.com/watch?v=g7tXA6MLbqQ
---

(t: 0) Let's do a very quick no bullshit video on how to use the new agent feature on <PERSON>. So first of all I'll open up Claude Code and you can see here it says you can now create custom (t: 10) sub-agents for specialized tasks run agents to get started. So this is a pretty interesting addition (t: 20) by <PERSON>. It's a bit like Hook<PERSON> but instead of being like quite complicated it's quite easy and also <PERSON> does everything for you. So let's write slash agents here and you can see I already (t: 30) have an agent here but I'm going to show you exactly how I made this agent. So I'm going to put it to personal. Hopefully I believe this means that it'll be used by anything that I use, any (t: 40) Claude Code instance that I use. So we'll generate here and I'm going to say every time you write a (t: 50) file you should check whether it contains any of the agents that you've created. So I'm going to say every time you write a file you should check whether it contains any of the agents that you've created. So I'm going to say every time you write a placeholder or hard-coded variables or to-dos. We are trying to build production ready apps so (t: 60) these will simply so these are unacceptable coding practices in what we are trying to achieve. (t: 70) Right so if I center here and basically what this does is it creates a hook as far as I understand it (t: 80) that will actually create a hook that will actually create a code that will actually create a code that will activate an agent right and then the agent will do a check every time it tries to write code. So you (t: 90) can see here it says create new agent so I'll just press continue and we'll pick per we'll pick yellow right and then we'll just enter here and then you can see production code validator. So if we open (t: 100) this and view agent you'll see you are a production code quality validator and expert in identifying (t: 110) plugin code patterns blah blah blah. When analyzing a file you will do this right and then you'll see here as well it says the user is creating production ready code agent and files are modified. Use this (t: 120) agent when any file has been written or modified to ensure it meets production ready standards. This agent should be invoked automatically after file creation or modification operations to check for placeholders (t: 130) hard-coded values to-dos and other non-production patterns examples blah blah blah right so this is super super useful if it works right. So let's go back to the (t: 140) so also built in down here where it says always available that's an interesting one there so I'm (t: 150) not I'm pretty sure this will be available in any new conversation that I have with full code right (t: 160) so let's say write me a python script to extract urls from a (t: 170) web page other than I'm just making things up use placeholders only right so let's see if this actually works. (t: 180) Okay nice so look I did that I said use placeholders only and then bang look at that validate production code holy shit this is a game changer. (t: 190) Oh damn guys I'm gonna have some content on this coming very very soon holy crap that's cool. So it's gonna hopefully say this is all placeholder let's actually implement a real version of this let's see how this works okay that's fine. (t: 200) okay thiscynaldo.com (t: 210) That was bound to happen eventually okay so it does a pretty good job of reading the code after (t: 220) Let's see hopefully it actually picks anything up this time okay so I'm going to do further testing with this guys I just wanted to quickly show you how to set this up so that you can experiment for yourselves the fact that it is natural language based means that you can play around with this you can use it to your own edge cases etc etc. (t: 230) It does look pretty interesting I'm definitely going to put this on to my like I might smartphone monitor which I didn't have this software for. I don' t girls like it was worth it if I went for a bit of editing but anyway I'm just using it up to try to implement the real thing here but what have you I've never done anything to a USB mouse in this one it's just I'm probably maxing out juntos as thiszub the ARC- issued by both nous seeing as well that nu as Microsoft does themselves. onto my like i'm actually coding right now in the background while i'm making this video i'm going to try this out a little bit more guys once i've seen that it's really really good right i'll make (t: 240) some more content on it um but if it's just another dirt then i'll just leave it be but i just wanted to show people how to do it it's pretty easy nice setup clod code as usual banging things (t: 250) out and yeah we'll see if this actually is useful in the future thanks for watching guys if you're watching all the way to the end of the video you're an absolute legend i'll see you very (t: 260) soon with some more content peace out

