---
title: Build Anything with <PERSON>, Here’s How
artist: <PERSON>
date: 2025-08-14
url: https://www.youtube.com/watch?v=hq8J-nj_Sr0
---

(t: 0) With Cloud Code you can build pretty much anything. Whether it's a mobile app, an AI agent, a website or a complex 3D animation. Cloud Code can do it all. It's the most powerful coding agent ever created. (t: 10) And in this video I'll show you how to build anything with it. Even if you aren't a professional developer. My name is <PERSON> and I've spent well over 300 hours inside of Cloud Code. I have five developers on my team who use it every day and together we spend well over a thousand dollars a month on Cloud Code. (t: 20) So I know the tool pretty well. Now, to successfully build anything with Cloud Code there are four main pillars. (t: 30) Number one is your Cloud Code setup. This involves your hooks, your commands, your prompts, your sub-agents. Anything you set up to make your Cloud Code faster, more powerful and more reliable. (t: 40) Number two is about having a clear idea. What is it that you're actually trying to do with Cloud Code? The more clarity you have the better. Pillar number three is your skill. How good are you at software development? (t: 50) Do you have a good understanding of computer science? All these technical skills compound on top of each other. And pillar number four. Pillar number five. four is your own prompting and context engineering knowledge. How well can you guide AI tools to do (t: 60) what you want? So these are the four pillars that determine somebody who's failing and building complete slop with cloud code and someone who can use it to build five times, 10 times, 20 times (t: 70) faster than he normally would. So now let me show you how to actually become a 10x cloud code developer. Someone who can use this tool to build pretty much anything. So I'm going to open an empty (t: 80) project right here and the fastest way to open cloud code, command escape. Boom, just like that, cloud code is open. Now, if you still don't have cloud code installed, just type in cloud code into (t: 90) Google, click on the first link and run this command in your terminal. This will install cloud code globally on your machine. So the very first thing you should do before you do anything with (t: 100) cloud code is to create the main markdown file. So I'm going to create a new file, name it project description.md. So you can easily reference this one file and any cloud code instance will know (t: 110) this is what the project is about. This is what the project is about. This is what the project is about. This is what the project is about. This is what the project is about. This is what the project is about. This is what the project is about. Now, if you remember the four pillars of cloud code success, one of them is a clear description of the project, right? What is the main idea that you're trying to build? So let's add that into (t: 120) this markdown file. So here's my idea for this video. Build a simple web-based CRM powered by AI. Basically cursor, but for CRM. But essentially it's going to have a left side chat bot where you can (t: 130) talk with it and right side CRM where you can do all of your customer data, you know, sheets, contacts, whatever you would do in a CRM. But another benefit of using cursor with cloud code (t: 140) is that you have the cursor tab. So whenever you're doing your context engineering, cursor predicts the next word. So this just saves you even more time. All right. So now let me click into cloud code. I'm going to press shift tab to switch the mode (t: 150) to plan mode. And I'm going to say read project description. I'm going to tag the file and help me think through what the ideal tech stack should be. This should be a quick and dirty prototype. (t: 160) And make sure to end your prompts with think hard, answer in short. This actually directly impacts the reasoning effort of cloud code agents. Later, I'll show you how (t: 170) to automate that so you don't have to type it always. And actually the way to do it is with your own custom cloud code setup. And there is a reason I put it in number one, because this is (t: 180) the difference between people who use cloud code casually, right? And people who use it all day to ship things way faster. I'm talking about production ready code, not some vibe coded slow. Now, since I already have my own optimized setup from using cloud code for hundreds of hours, (t: 190) I'm going to utilize that and say, create a new .cloud folder in our root and inside copy the (t: 200) contents of this .cloud. So I'm using cloud code to copy over my entire setup from my main repo, which again, I've spent so much time optimizing for efficiency, for reliability, for consistency. Well, let's approve (t: 210) this command to create the .cloud folder. And this is really the main folder where everything from cloud code lifts. Boom, there we go. So we have my hooks, my commands and my sub agents. By the way, (t: 220) if you want me to make you a custom personalized cloud code setup that involves hooks, commands, prompts, sub agents, everything you need to be a top 1% cloud code user, then pay attention because (t: 230) the month of August, we're running a special offer inside of the new society. Anyone who joins the new society will receive their own personalized cloud code setup. All you need to do is tell me what you (t: 240) want to build, what use case or goal you have, and you will get your own cloud code folder with everything inside. Plus inside of the new society, we do multiple calls a week where you can talk to (t: 250) me or my developers directly. And on top of all of that, we offer unlimited technical support. So if you get stuck on anything, we will help you out. However, this offer is only available during August. So if you want me to make your own cloud code setup, then you can go to cloudcode.com and (t: 260) click on the link in the description. And if you want me to make your own cloud code setup, then you can click on the link in the description. And if you want me to make your own cloud code setup, then you can click on the link in the description. And if you want me to make your own cloud code setup, make sure to join the new society. It's going to be the first link below the video. Anyways, back to cloud code. Let's see what it cooked up. Again, we were in plan mode here. So I asked it (t: 270) about the tech stack. So let's see what it suggested, right? And this is one of the most important decisions. So it says Next.js, Tailwind, Supabase, and then OpenAI for the model and Vercel AI SDK. So this is interesting. This is going to have natural synergy with Next.js because obviously (t: 280) that's developed by Vercel. So this will simplify the tech stack quite a bit. Obviously, if you're building some kernel or operating system, you will have to have a kernel or operating system. So if (t: 290) you're building a tech stack, you wouldn't go with this tech stack. But I was very clear that we're building a quick and dirty prototype, just basically an MVP, right? So we want to use a tech stack to support that. If you're building something that once it goes into production, it needs to (t: 300) work for 30 plus years, then you should probably use a lower level language like C and you should probably think hard about the architecture. But if you're building a side project that you don't know (t: 310) if it's going to continue, you don't know if it's going to work, use a tech stack like this one that allows you to work fast. So actually, I'm going to say no. I'm going to say looks good. Document this tech stack. That's it. So I'm going to say no. I'm going to say looks good. Document this tech stack (t: 320) decision in the core.md file. And this is the core importance. Using this file to build up the understanding of the project to add core details, right? And again, this is the whole purpose of (t: 330) this main markdown file to build understanding of the project, keep extending it. And anytime you can tag it, anyone, whether it's a human developer or an AI agent knows, okay, this is (t: 340) what the project does. These are the core features. This is the tech stack. And here is the timeframe. Here are the resources that the user has. So just tagging a single file allows the LLM to understand (t: 350) everything it needs to make meaningful work. So if we open up this file a bit more, you can see that Cloud Code has documented the tech stack in a nice and concise way. By the way, a quick pro tip, every single morning, what you should do is open up the global terminal on your computer and type (t: 360) in Cloud Update. This will ensure that you have the latest and greatest version of Cloud Code because this tool keeps evolving nearly every single day. Sometimes they do multiple updates (t: 370) a day. It's pretty impressive what Anthropix is doing, but you know, just to make sure you have the latest version of Cloud Code, type in Cloud Update into your root terminal and run it every (t: 380) morning. Now, one thing you might notice is that when I'm switching between these modes, the model I'm using is changing, right? So maybe thinking, how are you doing that, David? This is some sort of magic. Actually, it's not. This is a new feature inside of the latest Cloud Code update. That's (t: 390) what you need to upgrade. That automatically switches the model based on the mode, right? So as I said, Shift Tab is perhaps the most important keyboard shortcut inside of Cloud Code. (t: 400) It allows you to switch between the three modes, the default mode, the auto accept, and the plan mode. The main two modes are the accept. So this is the kind of the YOLO mode, right? Where Cloud Code (t: 410) edits, doesn't care, you know, works fast. Plan mode is the one you should start with. It's the one where like you need to gain clarity about your project and where you really need to squeeze the maximum performance of the model. So that's why this new option when you type in slash model is (t: 420) super useful and it's called Opus Plan Mode. This means that it uses Opus 4.1, the latest model from Anthropix, only in plan mode. Why? Because this consumes your credits way faster, right? So (t: 430) basically what you should be selecting is Opus Plan Mode. It just simplifies your decision making. You don't have to worry about it. You have Opus in the plan mode, (t: 440) if you switch to auto accept mode, you have Sonnet 4. Now, if you're wondering, David, how is it showing the model and the git branch and the time, there's actually a status line. This is another new feature. If you type in slash status line, you can have Cloud Code set up (t: 450) custom status lines, right? So some people want to know how much they spend. You can use like packages like CC usage for that. But whatever you want to see, I think the model is the most (t: 460) important and the branch. But whatever you want to see, whatever you want to have knowledge of, you can set it up as a permanent status line to be shown. (t: 470) B gider εδώ uno bom aulaç sus (t: 490) so (t: 500) names or file names to avoid errors like this. So this is where I'm going to show you how you can use cursor plus code together. So I'm going to tag the file fix settings.json boom and actually (t: 510) I have a second one settings.local.json. This one should not be in a gitignore so right now we haven't set up a git project yet but the difference is that one you can have personally and one with (t: 520) your team that way you keep them separate right. But anyways I'm going to give a description of the error to cursor and we'll say do not do anything else just fix this. Now as far as the model you (t: 530) can use either gpt5 or gemini 2.5 pro both are fine I mean this is a pretty simple operation. Personally I like to use gemini 2.5 pro I think it's very underrated and I like to use gpt or okay (t: 540) I'm going to save this for the next video because a lot of you guys have been asking for a dedicated video comparing cloud code to gpt5 so I'm probably going to make a video on that. Most of gpt5 (t: 550) versions are complete trash but there is one that's amazing that I use every single time. I'm going to show you how to do that. So I'm going to go to my gpt5 and I'm going to go to my cloud code and I'm going to create a new cloud code. So I'm going to go to my cloud code and I'm going to create a new cloud code. So that should be the next video so if you don't want to miss that make sure to subscribe. Subscribing is completely free just go below the video click (t: 560) subscribe takes two seconds appreciate it. So this should fix the issue with our cloud code hooks because of the file location. Let me see if this fixed it. I'm going to copy the prompt I'm going (t: 570) to open new cloud code boom there we go and I'm going to send it again and we didn't get the bug so that's amazing. So now I'm going to click close others is what I use all the time. Close (t: 580) all the other cloud code instances and we can close the cursor chat. Anyways now Opus 4.1 the most powerful software engineering model is thinking how can we design (t: 590) a simple and clean code base for this project right and again notice that I haven't rushed into it. This is what a lot of beginners do they just want to get you know the agent to writing (t: 600) lines of code as fast as possible. That's a huge mistake. The more time you spend up front the faster you can execute after. So if you actually properly plan out the code base choose the right (t: 610) tech stack think about what are the primitives what are the core objects what are the core features of the code base. So if you actually properly plan out the code base choose the right tech stack. So if you actually properly plan out the code base choose the right tech stack. Of this project you will be able to move so much faster than if you just you know act like a vibe (t: 620) coder and jump right into it. So don't do that. All right let's see what Opus is suggesting. Okay so we have a main root project folder this is what we have then app components lib hook store types. (t: 630) Okay I think this is pretty solid but we can make it even simpler so I'm gonna say looks good whoever make it even simpler and then I'm gonna use dash d if you don't know what dash d is well (t: 640) it's because it doesn't mean anything unless you have my custom hook. So this is the hook appends default and anytime I end a prompt with dash d it appends this right so earlier I explained if you (t: 650) add think harder it increases the reasoning effort of cloud code if you add answer in short obviously it makes it less verbose it makes it give concise answers so this is personally my favorite prompt (t: 660) and then keep it simple this is just general right so this hook is something that I found myself adding at the end of 90 percent of prompts I was sending so I just turned it into hook and all I (t: 670) have to do is do dash d and it gets automatically appended after every single prompt I send into code forcing it to think hard and give me short answers and again stuff like this adds up right (t: 680) maybe this doesn't save me multiple hours but it saves me 20 seconds there 30 seconds here and if I do that every single day for months I gain an unfair advantage over people who do not have any custom hooks. Okay so here's our simplified code base looks much better so I'm going to (t: 690) approve it which then switches us into auto accept mode switching the model to sonnet 4 and that's perfectly fine because not only is it five times cheaper but it's also faster. All right next (t: 700) cloud code wants to create the next js app so let's allow that by the way you should always understand what it wants to do yes lint. Okay so yeah luckily we don't have to decide all the (t: 710) details cloud courses overtook that but basically anytime cloud tries running sensitive commands such as here creating a new directory it gives you three main options if you want to proceed yes (t: 720) yes and don't ask so this will actually add it to the settings.json or no. So for this command since it's a pretty safe command I can give it the permission to just run it every time without (t: 730) asking you know hey David can I run it and that's how you build your settings.json right. You're permission to run. Now while Cloudcore is cooking let me explain two more essential commands. (t: 740) Slash clear. This completely resets the conversation history and frees up the context window. You want to do this when you're switching topics. When you're switching to a completely unrelated part of the code base or maybe even different project you want to do slash clear. So to avoid context (t: 750) rot. However perhaps more often you will use slash compact. Slash compact is another context (t: 760) engineering command that allows you to summarize whatever history you have. So let's say you have 100,000 tokens. It summarizes it maybe to like 5,000 or 10,000. That way you can keep working (t: 770) on the same thing without losing essential context. Now in theory you don't have to worry about either of these because Cloudcore automatically runs auto compact when it reaches the limit. So when it reaches the 200,000 tokens it will auto compact because obviously it cannot (t: 780) go beyond its context window. But sometimes it's good to do slash compact when you start to notice (t: 790) like okay there's been some unrelated info we want to summarize. So we don't poison the context window with unnecessary stuff. Now while I was explaining this Cloudcore is already hard at work designing the AI module. So this is the file interactive (t: 800) open AI anthropic. We have the database. So we're using super base JS. We have the some of the types here. It's set up chat CN. So that's for components for UI. And we already have the next JS project (t: 810) set up. So it's right here. Yeah Cloudcore is doing a lot here. And the reason that it can (t: 820) execute fast is because we gave it a clear description of what project we want to do. We decided that it was going to be a project that we were going to do. So we decided that we were going to do a text tag and we decided the code base architecture. And by the way that's something we should add to our MD file. So what I'm going to do is I'm going to copy (t: 830) this and just add it there. Boom. At the bottom of the file right below the text tag. And what we should do is we should actually wrap the text tag inside of XML tags. Boom boom (t: 840) boom. I'm just pressing tab. This is the power of cursor. And then I'm going to paste in the simplifies every structure and I'm going to highlight it all and format it nicely with (t: 850) inline editor. Clean up the formatting. Do not change anything. Boom. So this will nicely format it so that it fits the structure of the markdown. There we go. We can accept this and now save. Okay let's see what cursor tab is suggesting. Hopefully (t: 860) adding the XML tags here. File structure. Boom. Boom. Let's wrap it. Anyways let's see where (t: 870) Cloudcore is at. It's been doing a lot. So the project structure looks complete. Basically it did like 80% of things. We just need to provide it with the stuff it cannot do itself and then decide on the database tables. All right so type in anthropic console into Google. Click on the first (t: 880) one. Log in with your anthropic account on the left. Click on API keys. Then again API keys. If you don't have any don't worry. Just go top right. Click on create key. Let me select the vector (t: 890) workspace. I'm going to name it Cloud Code demo. Add. Copy this key. Now do not share your API keys with anybody. I'm going to delete mine before uploading this video. Let me jump back into (t: 900) cursor. I'm going to tell Cloud Code to create the env file in the right location and pre-fill the (t: 910) anthropic API key. So obviously we need to paste in the actual value right. But this will just save a bit of time. So I have to type out everything. Okay there it is on the right. So we will use anthropic. Let me replace this. Boom. Okay so these are super based stuff. (t: 920) But I'm gonna say let's not use OpenAI for now. Let's switch all of our AI functionality to only (t: 930) use anthropic API. And I'm going to switch to Plot Mode. The fewer lines of code changed the better. (t: 942) Click Savedonplay and you will see that all my agents are set to sync. And in which case my (t: 950) waves are the Yeonk across the UI and my 걸� financials exactly what we actuallyaw almost 200% of which should be in run or (t: 960) learned. And that with this amount of time should work with threshold. We'll say that I javascript back is setup. Let's try Look and then if there is the default inventory right right now. Let's add that here as well. Okay going to from zero that old app on this is now (t: 970) except Cloud Code doing this. I'll say, be detailed and thorough. Tell me everything I need to know (t: 980) about getting AI chat completions with the Anthropic API, okay? Let's run that. That's going to be a deep research on this. (t: 990) It's going to check like 30, 40, 50 different sources to give us the latest docs because the problem with using Cloud Code or any AI tool, the LLMs, the large language models, (t: 1000) they have a knowledge cutoff, right? They only know information up to a certain date because they have to be trained. If you train a model, you have to feed it the data. (t: 1010) But that means you cannot feed it everything. So here, for example, it was showing GPT 3.5 Turbo, right? It's terrible. You would never use this model. It's like ancient. Same with Cloud Free Haiku. So that is the problem of using the models (t: 1020) without web search. So combination of Cloud Code and perplexity is really OP. And by the way, that's why we built perplexity into Vectal. So inside of Vectal, you have Perplexity Pro built in, (t: 1030) which is a side note. But yeah, using these two tools together is really OP, especially when you're doing something with the latest documentation or packages (t: 1040) or libraries that might be updated. You absolutely always should do a perplexity research. Copy the output just like this. Boom, copy. I'm going to say docs. Paste it in. (t: 1050) You know, again, wrapping in XML tags. Docs. And I'll say above are the official anthropic docs. Are we following these docs properly? (t: 1060) Or are we? I'm going to say, oh, we are. We are using the Vercel AI SDK. If that's the case, I'm going to do another web search. (t: 1070) Wait, actually, I should probably check. Oh, yeah. So we are using the Vercel AI SDK. So these anthropic docs might not be exactly relevant. Let's see. In the meantime, I can run the same query. (t: 1080) So let me just run the same query. Boom, let me copy this. New. I'm going to say anthropic models through the Vercel AI SDK. (t: 1090) Which will be obviously JavaScript TypeScript. AI Chat completions with anthropic models through Vercel AI SDK. (t: 1100) So while Cloud Core is responding, we're going to run this. Let's go to the bottom. Let's see what's happening. Okay. So I'm going to ask. (t: 1110) So the docs I pasted are not really relevant? Or are they? Answer. Okay, boom. I think our answer is short. (t: 1120) I just want a clear response. Sometimes it does this like, it creates a plan when it shouldn't. Like here, I don't want any plan. That is the downside of Cloud Core. In the meantime, perplexity, DB research is generating the Vercel AI SDK docs. (t: 1130) Not directly relevant. Okay. So that's fine. I'm just going to correct that with the latest documentation of this one. (t: 1140) So I'm going to say Vercel. Boom, paste it in. Your task is to deeply investigate our code. (t: 1150) Figure out. Whether we are following the docs properly. And I'm going to do answer in short. Dash U. Dash U is another hook. (t: 1160) Let me show you. This is the ultra thing. So this is the most powerful version of thinking, of reasoning efforts for Cloud Code, is to say ultra thing. So what this does, anytime I end my problem with dash U, it appends this. (t: 1170) Use the maximum amount of ultra thing. Take all the time you need. It's much better if you do too much research and thinking than not enough. (t: 1180) So you'll notice, boom, you can see how much Cloud Code is thinking if I use that hook. So that's really good for squeezing the maximum performance out of Cloud Code. (t: 1190) All right. So it is running to check if we're using the latest docs correctly. Now maybe I can show you how to create your own commands. Right? So in the .cloud folder, you have the commands subfolder. (t: 1200) And in here you can see a bunch of different markdown files. And all of them are pretty simple. For example, we can do like create PR. Create a pull request. And it's basically a prompt. This is a, this is a, this is a, this is a, this is a, this is a, this is a, this is a, this (t: 1210) is a, this is a set of instructions you want Cloud Code to follow. The only, you know, tricky thing is the arguments. So this is the stuff we provide after the command. (t: 1220) So if I do slash create PR, here, I can provide arguments after. Right? So here I put it as a branch. (t: 1230) But honestly, it doesn't matter. This is, this could be like a custom prompt. Cloud Code is smart enough to follow it. And then it follows these steps. This is for the repetitive actions you might do it. Right? comments I have, explain pull requests, create PR, file review, header comments, push to get up refactor security, and honestly (t: 1240) I would add way more like this is just a it's just the beginning like my Cloud Code setup is literally improving every single week and (t: 1250) That's another benefit of being in the new society because I'm always sharing the latest and greatest practices Anyways, let's see the outcome here. So we're using multiple issues, right? I'll say good now (t: 1260) Let's fix all of these issues Like a senior developer would. The fewer lines of code changed the better (t: 1270) Do not do anything else. Yeah, so this is the issue if you just use Cloud Code without any (t: 1280) Grounding in reality, right? You need to make sure, by the way Cloud Code has a web search, but it's not as good as perplexity So you need to make sure anytime you're implementing something that you go with the latest documentation (t: 1290) To not run into any issues, right? So all you need to do is to create your own commands, it's literally simple You create a new markdown File right here boom new file. Maybe this could be like a folder (t: 1300) restructure or whatever whatever type of workflow or process or Set of actions you find yourself doing or in a set of prompts (t: 1310) series of problems you find yourself sending to Cloud Code just create a new markdown file boom and and (t: 1320) .md and Put in the instructions in here and then you can just do slash folder structure and it will call that file Into Cloud Code following the instructions and if you put the arguments dollar sign arguments, you can insert any custom variables (t: 1330) after the main command, you know, so slash file review boom and this will be the path to that file and (t: 1340) Whatever you find yourself repeating there's no need to do that You can just create a new command for it and that's an entire workflow (t: 1350) List of steps that Cloud Code will execute speeding you up even more. All right, so it seems like Code Code has fixed all of the issues we had so We did put in the in Vicky. So I'm gonna ask great work. I did (t: 1360) update the Envy file by adding the actual and tropic API key what remains for me to test our (t: 1370) App maybe we need the super base stuff as well, but maybe we can do that with like SQLite or something All right, I'm gonna check this plan. I will say let's go with a simpler way. Can we just use? (t: 1380) SQLite we've got a quick and dirty prototype. So I could set up super base, but honestly for this (t: 1390) I don't think we even need a Postgres. Let's see. This is why it helps having some knowledge of (t: 1400) just basic technical knowledge, right different database types different frameworks different programming languages, let's see. Okay, so Seems like Cloud Code agrees this would actually simplify things a lot. Yeah (t: 1410) So that's good. So since we're using these two packages I'm gonna do another Probex research for them. (t: 1420) Boom. Give me the latest documentation for using these packages or libraries, whatever. And I'm gonna say specifically how to integrate them into a full stack AI web app in JavaScript and TypeScript. (t: 1430) Be detailed. Boom, that's running. (t: 1440) So we need to uninstall SuperBit.js package. Let's confirm that. So this is NPM uninstall. It's probably something you should not add to your allow list because it's a risky package, you know. (t: 1450) So you really want to be careful how you curate your .cloud folder, your Cloud Code setup. If you add the wrong commands to your allow list, maybe you can go faster. (t: 1460) But then Cloud Code uninstalls serious important dependency. And again, you waste an hour. So this is why setting up your Cloud Code correctly is essential. (t: 1470) And even though I have hundreds of hours in it, I still improve my setup. Every single week. Because you can never have enough automations or enough commands or enough hooks, or enough prompt templates. (t: 1480) The moment you see, like, okay I've used this prompt four times in the past, boom. Turned it into hook, or turned it into a command, turned it into a subagent. (t: 1490) Anytime you find yourself repeating the same workflow, the same list of steps, it's time to automate that, to template that and allow you to move even faster. (t: 1500) And especially if you have a team. Right? especially if you have a team right because then you can share prompts one person finds a really sick prompt and shares with anybody else one person creates a nice hook shares with others (t: 1510) that's how you can compete with big companies you become an ai first team if you're an ai first developer you can absolutely outcompete anybody even people who have way more years experience (t: 1520) so let's see we have successfully switched to sqlite remove superbase added sqlite reroll okay so now i'm just gonna double check this and this is why sometimes going slow is the move you know (t: 1530) doing extra deep research to double check this so i'm gonna say dogs boom here are the latest dogs (t: 1540) for the packages we are using take a deep breath and double check the implementation and tell me are we following the official documentation or not are there any serious issues or is it basically (t: 1550) correct and we should not overthink this think hard answer in short boom switch to plan mode shift tab and now we're using all the tools we have in the sqlite and we're going to do a post 4.1 to ensure that everything's correct see like you can see all the sources at the bottom of (t: 1560) the perplexity search here did 31 okay implementation is basically correct nice and by the way the most important part of my problem is this are there any serious issues (t: 1570) right because you can ask the models and they will always find something that could be more you know written more cleanly or documented better whatever but then i say we should not (t: 1580) overthink this so this is like really important prompt engineering how to actually find serious problems you know security flaws or loose circular dependencies or loops or bad time complexity whatever something (t: 1590) serious that is not just a you know spelling issue or suboptimal code whatever you get the (t: 1600) point so this prompt is really important they steal this and use it a lot okay so then i'm gonna press the up arrow a few times i'm gonna yeah this i wanted to use the same problem to (t: 1610) see like okay is there anything else missing we've switched to sqlite to simplify our database greatly can we test the app is there something critical we have to do or it's just we need to (t: 1620) just run npm run dev so we need to probably locate the crm okay crm app so let's do that let's open a new terminal and again we can use cloud code to run it but it's good to understand the basics (t: 1630) of a terminal so we need to do ls here we can see all of the things in the root obviously we want to go into the crm app folder so i'm going to cd crm dash app we're here and then we can do npm (t: 1640) run dev so we need to do npm run dev so we need to do npm run dev so we need to do npm run dev from here there we go we're on localhost 3000 so let me open that up and the next thing okay (t: 1650) so we have error that's that's fine for front-end debugging i'm going to show you really op mcp that (t: 1660) all of you need to have mcp server is actually crazy so before we debug this let me open the global terminal and all you need to do is run this command to install the playwright mcp (t: 1670) cloud mcp at playwright npx at playwright mcp at latest playwright is a open source framework from microsoft for testing front-end end-to-end (t: 1680) for end-to-end tests basically interacting with websites programmatically so we already have the this mcp server so we can close the terminal and we can test actually inside of cloud code by doing (t: 1690) slash mcp we don't have it hmm okay so maybe i need to install it locally here boom so we do have it now let me open new cloud again i should use the command escape keyboard shortcut slash mcp (t: 1700) okay we have playwright amazing so we will we will switch to this new window but whatever first off we need to debug this right so module not found you should (t: 1710) actually start learning the skill of reading error traces right because it can be uh kind of lazy just copying stuff and pasting into llm and say like okay explain this error right so i can (t: 1720) just paste this in and i can do four dashes and then do dash e that's another hook i have for (t: 1730) explaining error traces this one is more advanced and again this is why you should join the user because during august we will give you all the hooks all the commands all the sub-agents for your (t: 1740) specific use case so maybe you want to build a sass maybe you want to build an agency whatever you want to do with cloud code or just want to become better developers speeders of up just tell (t: 1750) me what what's your use case what's your goal and we will design a custom cloud code setup for you including all the hooks including the prompts including the commands sub-agents settings that json everything you need to become a cloud code power user so this hook anytime i end with dash e (t: 1760) appends this right and we have multiple techniques we have think harder so that's like the third level (t: 1770) you have think if you say think that's like a low reasoning effort if you say think hard that's a medium reasoning effort if you say think harder that's high reasoning effort and if you say ultra (t: 1780) think that's the highest for a cloud code so you can see do not jump to conclusions this is so that it doesn't say oh you're right or i see the issue now stuff like this terrible habit hopefully (t: 1790) anthropic fixes this that's one of the worst parts about cloud models to be honest so yeah this uh this prompt is quite old optimized after a lot of testing anyway so here let's see the explanation module not found okay (t: 1800) okay ai package doesn't export react path so here's the plan i'm gonna accept that and we're gonna slowly switch to this new window where we have the mcp for playwright we might need it we might not (t: 1810) need it but this is very op for debugging front end now we have a different build error so i'm gonna use this i'm gonna again switch to plan mode to use opus i'm gonna paste this in do four dashes (t: 1820) and just dash e see how much faster i can do this so i'm gonna go ahead and i'm gonna go ahead and work with somebody compared to somebody who doesn't have my hook who has to say like okay (t: 1830) explain this error to me what does it say how can we fix it properly no i have this hook so all i do is dash e two characters and i have an optimized prompt so let's see first uh okay so that should (t: 1840) fix this this error and then uh let's let's have this cloud code so you need to this is like a (t: 1850) skill developing uh understanding where you should switch to new cloud code right so here we already were chatting for quite a while and i'm going to show you how to do that so let's go ahead and start a bit you can see we have lots of messages and a lot of it is redundant because we were just implementing the fundamental or not redundant deprecated outdated irrelevant we were implementing (t: 1860) the basics of our code base right so now we're going to be more focused on like fixing specific (t: 1870) bugs or polishing the ui stuff like that so anytime you kind of switch the type of work you're doing you should switch your cloud code now you could use slash clear to reset the context window (t: 1880) absolutely the problem is that sometimes the terminal bugs and you still see the older messages which can be quite annoying if you're not sure what you're doing you should switch the code base if you're working on the same thing you should do slash compact and continue in the same conversation (t: 1890) actually we can probably run slash compact here because even though we're not near the limit we're probably like 60 70 i would say we still want to compress a lot of that unnecessary info to avoid (t: 1900) context rot and you can even do slash compact and give it a instruction after it right so maybe next we're going to do ui changes remove everything else so then it would focus on that (t: 1910) in the compacting conversation summary anyways what is going on here what is going on so let me see just getting built error module not found what i don't like is that it's doing way (t: 1920) too many changes here maybe it will fix it let's see oh there we go app is running this is huge (t: 1930) all right so let's see what's going on we did see the ui for a brief second i mean what what are you doing here i'm gonna say typescript error okay so it ran npm run oh no so i will say revert the (t: 1940) changes you did after npm run build so one thing we haven't done here is create a global.cloud md file so i'm going to do that right now i'm going to say create a (t: 1950) cloud.md file in the root folder boom so again we can have multiple cloud codes running so our app used to work the problem is that there's a next js bug so this is something you will learn (t: 1960) as you go so anyways uh here let me kill this terminal this is our from the terminal so i'm gonna do ctrl c to interrupt it i'm gonna clear and npm run def again if you do npm run build (t: 1970) it will mess up the server so you need to just restart it that's a bug in xjs it's nothing to do with our app let's (t: 1980) see what we have so we have okay we can ignore this we have a simple ui okay so let me open the cloud md so this is a this is going to be instructions about the project right and the (t: 1990) reason we should have done this earlier is because you can control how cloud code behaves so here i can say npm run build never ever run npm run build unless (t: 2000) the user asks you to do so okay and here again it started by basically saying similar stuff we (t: 2010) have in the project description but the difference is that this file is basically cloud code looks at this file every time right so every time you use cloud code it looks at claw.md this is the main (t: 2020) system prompt so you want to keep building this over time and keep extending it and by the way as a bonus i'm just going to throw this into your cloud code setup if you join the new society (t: 2030) you'll also create a claw.md file optimized and specialized for you all right now let's go ahead and just start building this on the cloud that's the main system prompt and i'm going to set this up for you all right so let's see what happens i'm going to say the server is running the server (t: 2040) is running i can see the front end tell me how to test it and we'll dash d boom let's see what's happening so try ask okay we already have some problems show me all these creating contacts i (t: 2050) create a new contact as a prompt let's see what's happening we have contacts we have tasks leads contact tasks ask me about crm data so since we don't have any crm data we need to make sure (t: 2060) the ai can create crm data all right so this text field is bad because it's light gray or white so (t: 2070) we can barely see it but that's a small ui change i'm gonna say we need to first of all we should probably check the terminal there's no errors okay i'm gonna say we need to make the ai agent (t: 2080) in our app work when i say create new contact right now this year right now my crm is empty so (t: 2090) investigate how the ai behaves to create new contacts and i'm going to say ultra thing basically dash you invoking our (t: 2100) hook so we have three different categories leads contacts tasks and if you're like okay david but i haven't built a crm i don't know how a good crm should look like well guess what neither do i so (t: 2110) what we do is we launch a deep research i'll say i'm building an ai powered crm it should be a full stack crm with all of the main features however i will have an ai agent on the web and i will have (t: 2120) a full stack crm on the left just like inside of cursor that helps me interact with all of the ui elements inside of the crm your task is to find what are the essential features what are the core (t: 2130) functionalities of a crm app what's all the things we have to do what are the main problems people (t: 2140) using crms are solving and how can we build them into a simple clean and powerful ai powered crm app give me a detailed spec so basically you research (t: 2150) right what is the target avatar like how does the typical user use a crm what problem are they (t: 2160) solving with a crm what are the core features what is something that should be in v2 it's very important if you're building something what is the v1 feature and what is the v2 feature you will realize a lot of the stuff you want to add is actually a v2 feature and should not be in the mvp (t: 2170) so again anytime you're building some project you need to educate yourself this is even more important than educating the ai right a lot of people talk about context engineering which is (t: 2180) like creating and managing context and you're building a context engineering which is like creating and managing context for the ai agents but even more important is you yourself like as the human understanding what you are building what is part of the scope what isn't part of the scope (t: 2190) what is the v1 feature what is the v2 feature like what what does the target avatar look like the typical user of a crm what are they using it for right how are they using it are they using it (t: 2200) on a phone or a computer this is all the things you have to deeply understand as the human as the founder and that's why ai as belagi says by the way shout out to belagi watch my podcast (t: 2210) ai is going to make smart people get smarter but it also will make dumb people get the dumber people who ask google stupid ass questions are going to ask it to chai gbd and they're going to outsource their thinking (t: 2220) and those people are lost those people are completely lost if you outsource their thinking to ai and you ask even the simplest questions like what's the next feature i should add no you need (t: 2230) to make that decision yourself you need to be in charge of the product not cloud code not chai gbd however if you use it in a clever way like learning documentation or you know figuring out what's the (t: 2240) best database type of thing you're going to use it for and you're going to use it for a lot of other things thoughviamente i'm going to add the content labour a little more times but i still need to figure it out like this is basically an AI space i can use the cloud programs its nothing in there to actually change it is very simple okay asking people to ask a question like this using things to clear up (t: 2250) purpose because you know your data and if there's like i'm not the oily l meetings you know or you know like any conversation with like a Shanghai (t: 2260) ler you know you go search forPR and i'm example you're gonna neatly within 70 minutes detect any that type for this type of application or how does a typical customer it use my type of product right okay essential said features contact management it is open to copy this block i'm gonna added to our project description and the file boom essentially own features (t: 2270) brand Er King code to do a web search even though it's not gonna be like a deep research it's still fine the problem is clear they're creating an entropy client channel they (t: 2280) trying to call the a is only client or function all we're using bad model okay oh no no no no we want to use we want to use a solid or opus so let's create a (t: 2290) new chat here I'm gonna say what is the official API name of Claude for sonnet in the anthropic API when using it via Vercel AI SDK and then I reuse slash WS (t: 2300) this is actually a shortcut I had in sort of complexity that inserts browse (t: 2310) the web answer in short and yeah because we do not want to use the haiku that's very outdated so okay it's just this okay I'm gonna copy that I'm gonna interrupt I will say boom that is the official model name use that I think (t: 2320) we're just using it outdated so I'm gonna copy that I'm gonna interrupt I will say boom that is the official model name use that I think we're just using an outdated model that might be one issue but we will add debug look statements because (t: 2330) we will get this running right I want to start adding contacts and I want to make sure we have these core features it's actually what I can do right here (t: 2340) is I can go into the other cloud code the first one the old one I would say a read project description and tell me whether we have domain CRM features (t: 2350) implemented or not answer in short and dash you to ultra think it let's go back to our new one let's see what's happening yeah we need (t: 2360) to change from the topic okay okay looks good plot for sonnet should be that one again we're gonna add a bunch of debug statements so we can see like in the console right here what is going on and (t: 2370) we will use actually playwright as well to debug the front end we had this one I think we put it in the older cloud code so we probably just we should create a (t: 2380) new file so okay I'm gonna say create a new slash Docs folder where we will put the most important documentation for our app okay start by (t: 2390) creating just one file named for a cell dash SDK also AI SDK dot MD so we will (t: 2400) start building internal documentation and this is important for things that (t: 2410) you need to reference often or things that other developers should know and if you find yourself you know copying something multiple times again create a markdown file for it and you can easily tag that file so let's (t: 2420) see okay we created the Docs folder inside right here it's empty it will create the Vercel Docs we will just replace it with the the ProbeX research (t: 2430) deep research output that way we can tag it at any cloud code instance right so here boom it already put it there but I'm gonna just replace it with these sources and it's already 300 lines of up-to-date documentation so the beauty (t: 2440) of that is I can go here I'm gonna say okay it's created a new app why did it start a server so okay I'm gonna give you (t: 2450) a tip if you do a hashtag you can add to memory I will say do not start any servers unless the user says so I mean actually it's kind of fine because then (t: 2460) cloud code can debug it faster whatever we're gonna use 3001 it's fine I'm gonna I'm gonna instead cancel it out I'll say read Vercel the SDK and investigate whether we can use it in the next step (t: 2470) we're gonna use 3001 it's fine I'm gonna I'm gonna instead cancel it out I'll say read Vercel the SDK and investigate whether we can use it in the next step we are following the latest documentation properly dash d I'm gonna (t: 2480) turn this into plan mode and you can see that the one bash running here this is showing that we have something running so we have the frontend running and that (t: 2490) allows the cloud code to debug it faster so let me say create a new contact this is very bad you I'll fix it create new contact about a real estate developer whatever send that's right and if we have that then we have to update the server and we have to select the server but we won't do that so this is the right way we will try to get it to work in the next step (t: 2500) See what's happening. So it goes refreshed, but we didn't get an AI response. So let me open the console (t: 2510) Honestly, we should probably just you use the playwright MCP to save ourselves some time here Because we're debugging this (t: 2520) Manual way and it's kind of slow. So okay. We have some conflicting output. So net, okay, whatever I'm gonna ask it. I want to use (t: 2530) plot for a sonnet through the Vercel AI SDK, what is the proper? API name of this model (t: 2540) slash WS We need to we need to double check this like this is we might just open this up to be honest So let's open up one of these dogs Read it (t: 2550) Here it says it's this one one Okay, so Yes, this should be good 25 oh 514. So that is the correct one. Okay, so (t: 2560) That's good Let's see what the other code code suggested here on these issues (t: 2570) We say spam plan activate the tracking basic born. That's fine. We don't need these we just need to get the We need to get the main Main output working right now (t: 2580) The AI is not generating stuff and actually while this guy is not doing anything I can activate this cloud could say update the UI of the chat so that there is proper contrast (t: 2590) When typing some characters right now, it's light gray on white and overall improve the UI of the left side of our app (t: 2600) don't over engineer it the fewer lines of code (t: 2610) The better boom so we can have one plot code improving the UI. The other one is fixing the AI functionality And then I'm gonna switch to plan more here and say how (t: 2620) when you contact how Can I test the create new contact? functionality of our app I want the LLM owned AI to (t: 2630) properly Save this into our DB and for me to start using the CRM So the refresh might be too fast, but even if we reload we still have no contacts (t: 2640) So then we will have to debug what's happening to that data. Alright, so let's accept some of these front-end changes UI changes Let's see what's happening here though. So open the app (t: 2650) Chat panel type something command. Ok, so let's let's test this prompt Let's see if we even get a I response that should be the first thing and we have the nice contrast now beautiful (t: 2660) So we fix that issue We're not getting a response at all. That's the problem. I'm gonna say no I'm gonna say use the (t: 2670) play right MCP to debug the front end right now We are not getting an AI response at all (t: 2680) So, no way bad anyways. Right? When debugging, you want to isolate the bug. Instead of trying to one-shot fix it with AI, you want to get closer to figuring out what it is. (t: 2690) So a lot of the times, adding debug statements is the move. So I'm going to say, when I send a prompt like boom, nothing happens. (t: 2700) The AI does not respond, and the CRM does not save the contact. Okay, then we'll switch to plan mode and say, (t: 2710) your task is to figure out the optimal three places where we can add a concise debug log statements so that we can get closer towards fixing the issue. (t: 2720) We don't want to try to fix it in one go. We just want to proceed like a senior developer would, strategically isolating the problem (t: 2730) by adding debug statements in the most important pieces of code so that we can know when things break down. Take a deep breath and analyze the entire front end (t: 2740) and suggest the best way to fix it. So I'm going to say, I'm going to do a test three or four places for us to insert the debug log into, and I'm going to do answer in short and dash U (t: 2750) to ultra-fang this. Let's go back to here. So we need to approve the playwright. I'm going to actually add it to our allowed list of commands. (t: 2760) So it's going to open playwright. Boom, there it is. And now AI is going to interact with our website, right? So we can put this on the side. (t: 2770) We can approve some of these commands. Actually, let me switch to auto accept mode. So let's see how Cloud Code is going to debug this. (t: 2780) Again, I have my hands lifted up. It's all Cloud Code. Well, I just need to approve these commands, right? So it wants to click. Boom, so it sends a message. Nothing happened, right? (t: 2790) Okay. List network requests. So it's getting some info. Oh, there's an error. Interesting. Issue found. Front end test reveals a problem. (t: 2800) No API call. Ooh, that's really bad. Okay, here's the plan how to debug this. So by adding the playwright MCP, you can have Cloud Code literally interacting (t: 2810) with the front end, testing it, sending the prompts itself, clicking buttons, interacting with the UI, and finding and debugging it itself. It's really like the next level of AI. (t: 2820) And you see like all that you need to do is add a single, single line for the playwright MCP. Actually, OP. Oh, we have error. But let's see if, I mean, since this is the playwright instance, (t: 2830) Cloud Code should find it easily. Have we get to URL? Boom, it should notice this error. Let's see. It has import path. Okay. (t: 2840) It fixed the import path. There we go. So this is beautiful. We're watching an AI agent literally debugging front end in front of our eyes. It typed in the prompt. I'm not typing, guys. (t: 2850) It's doing it itself. It typed text, playwright MCP, click it, click it. And let's see what happened. So now it's listing out the requests, reading all shell output. (t: 2860) Okay. We have another error. Let's see. I see the issue error shows the file. This is just allowing you to debug way faster. And again, if you're a professional front end engineer, (t: 2870) maybe you would do it yourself faster, right? Maybe you wouldn't even run into these issues, but for all people who are like junior developers or no, not even developers, people who just like, you know, (t: 2880) you're maybe you're an entrepreneur. You want to build a internal software for your team. You can use Cloud Code to build anything. I mean, I really believe that you can do stuff that otherwise (t: 2890) would not be possible. This is the biggest power, right? You have like senior developers hating on AI or it's writing slob, it's not following best practices. Sure. Maybe that is the case, (t: 2900) but you have also millions of people who would not be able to build software who now can, and you can use these tools to also debug it to also run tests, to make your app more secure. (t: 2910) It's all about the intentionality, right? If you're a dumb ass, you can only use these tools to build quick MVPs and not add any security, not, you know, (t: 2920) improve your UI, not improve the customer experience, not add observability, track your endpoints, not add any tests. That's your problem. That's because you're not a clever individual. (t: 2930) If you are a clever individual, you can use these tools to upskill yourself, to educate yourself. So let's see what's happening. Test endpoint, CD curl command. (t: 2940) Okay. It looks, it looks good. So yeah, if you are an intelligent person, you can use these tools to upskill yourself way faster and achieve things that otherwise you simply could not, or at least achieve them (t: 2950) way faster, right? So it's running some curl commands to test specific parts of the code. Here's the streaming response. All right. So now it's going to run another test. Navigate to URL. There we go. (t: 2960) It's going to write the prompt. There it is. As you can see, the text was automatically inserted there. Going to click the send button. Reading shell output. We might need to interrupt it here. (t: 2970) It's running for a while. Still no API requests. For now it's not working. Let me, okay. So I'm going to say, take a deep breath and investigate. (t: 2980) We should probably add these. Yes, so say add them. We should add these debug log statements to see whether we're even, I mean, this is not going to help it because it seems like the issue is that we're not hitting (t: 2990) the API. Investigate why are we not making the API chat request? (t: 3000) Where exactly is the issue? And what is the next small step we should take to get closer to solving this? Think, (t: 3010) like a senior developer would dash you. We don't want to like one shot fix it. We want to get closer. So here we had some console logs. That's going to be valuable either way. (t: 3020) That's fine. But the issue is that we're not even hitting the API. We need to get a response from Anthropic and then we can parse that response to add some contacts. (t: 3030) Once the bedtime. Okay. So playwright evaluate JavaScript. Okay. Let's see. Interesting playwright tool right here. Console. Let's see what's happening. (t: 3040) Is anything happening here? Again, you, this is like very important to have some logic skills, right? So, okay. So, and Cloud Code is also confident that it found the issue. (t: 3050) If you have a logical skills, you can proceed like, okay, how should we debug this? Okay. First, let's make sure the button is working. Is it sending to Anthropic API? Okay. It is. Are we getting response? (t: 3060) Okay. Are we parsing correctly? What are we doing with that parse data? You can move way faster if you have the ability to think logically. And again, that's like the core of being a programmer. (t: 3070) It's not about memorizing syntax, right? A lot of the people who have been programming for years, the reason why they resist AI, because they just memorize a ton of syntax and they created personal attachment to that (t: 3080) and they didn't want to let it go. However, the skillset is changing. What is going on? This is going in circles for my taste. (t: 3090) Say also add three more debug log statements right after clicking the start button. Okay. (t: 3100) So we're going to send button. We need to know whether we are even hitting the Anthropic API via Vercel SDK. (t: 3110) And I'm going to also say add colorful emojis at the start of each debug statement so that we can easily see them. (t: 3120) Yeah. I don't like this. This is going in circles. It's going in a lot of circles. Okay. It says it's found the issue, but it said that multiple times already. So let's see. (t: 3130) Let's change the approach. Let's go our own way. So let's go once at a time. New cloud code. Move this one to the side. The old one is still pretty good. (t: 3140) We're just adding it for debug logs. That's fine. And close some of these. We're going to use the new one to actually get this up and running. So let's see which of the servers (t: 3150) we have running right here. I'm going to kill this. I'm going to restart this. So again, ls cd crm. (t: 3160) App npm run dev. Okay. So I'm going to ask a very dumb question here. Is npm run dev all that I need to do to launch our app? (t: 3172) Or do I have to start another server? (t: 3180) Okay. Simple question. And this is the benefit of using AI. You can ask many dumb questions without feeling any shame. You know, if you had like a, imagine learning, you know, learning a new language, right? (t: 3190) And you buy a tutor or like learning programming and you pay a human tutor, human teacher to do lessons, programming lessons. Maybe you ask a question once, twice, maybe three times, (t: 3200) but then you don't want to seem stupid, right? There's the social dynamic. There's the inter human personal dynamic that makes it weird. With AI, that's not the case. (t: 3210) You can ask 20 times, 30 times and without any shame, you know, and AI will happily respond, explain it differently. So yeah. Here, this is all, we have to do. So yeah, here, this is all, we have to do. This is all we need. (t: 3220) So we have our server running here. Let's watch out for errors. And we also have the front end console. We need to check. We're running on 3000. Okay. (t: 3230) So we were on the different one. Close some of these. Okay. Create a new contact named John. Send. (t: 3240) We have, we've seen some error. Let's open the console here. Okay. Why is it reset? (t: 3250) Yeah, that's very sketchy. That's very, that's very bad for debugging. The whole console resets when I sent something right now, (t: 3260) the whole front end console resets the moment I sent any prompt into the chat (t: 3270) window, investigate why that's happening. And think hard about how to fix it. And think hard about how to fix it. (t: 3280) The problem is this. We cannot really debug if we don't see the logs, right? So let's see. What should be the first step in us debugging this issue? (t: 3290) Give me step-by-step. Sometimes it can finish it with the play, right? But here it ran into some loops. (t: 3300) So it's valuable to also have some screws. Okay. We have it, we have it. If it proves, go to console tab, clear console. So we have the console. It's clear. It's clearing itself, right? So that's that send a message. (t: 3310) So let's again, like this sent. Hmm. The problem is that when I click send the app reloads and the console resets. (t: 3321) This makes debugging very difficult. Okay. We have, we should have this. (t: 3331) So here, okay. This one wants to play, play an MCB. Say, do not use the, do not use the MCB server. (t: 3340) Explain instead, investigate the code base, figure out why this is happening and how we can fix it. (t: 3350) A dash D what I'm going to do is I'm going to open up cursor on the side with Jim and I Jim and I, um, with five pro. And again, this is a, (t: 3360) the reason why this is valuable is that I'm back is that you can have multiple powerful AI agents working together, right? (t: 3370) So I'm going to describe the same issue. Boom. Say do not do anything yet. Just answer, just analyze the files. Answering short, (t: 3380) sometimes using a model from different company can be all, all that's needed to solve it. Right? So obviously close cause and throw pick. (t: 3390) Sometimes using a Google model like Jim and I to one five pro is, it's going to give you a fresh perspective. Sometimes open AI model, sometimes X AI model on issue form is zero. (t: 3400) Okay. Page reload. Okay. Is the Gemini actually reasoning or is it stuck? Let's see. So now console shouldn't reset. So I'm gonna say, Hey, hello. (t: 3411) Okay. Console did not reset. That's massive progress. I'm going to take a screenshot of this. I'm going to save it to documents. (t: 3420) And what you can do is you can drag in files into cloud code. Hold shift boom. I'll say good. Now the console does not reset. (t: 3430) However, we still run into errors. I'm going to dash E to explain it plan modes to get Opus the latest model. Okay. (t: 3440) So it found the same thing basically. Um, that's fine. Let's put it aside. (t: 3444) So it wants to read, I'm going to add this working directory. (t: 3450) So we can read screenshots faster here. Uh, I really like the, that, you know, we haven't got success with that MCP sometimes like it can debug it really fast. Sometimes sometimes you need to get your hands dirty is what I'm trying to say. (t: 3460) And you shouldn't be afraid of it, right? No matter what bug happens, whatever happens, you should not be afraid. You should not take it personally. (t: 3470) You can solve it. So let's see inputs undefined. So we have a handle. So many is another function of submit. Okay. Let's see. Let's show the debug log. Right. Okay. So I agree with that. Add the debug log. You can close this for a bit. (t: 3480) If you want to have success, we've called code again, going back to the four pillars, you need to be willing to improve your skill. (t: 3490) You can get very far by having to write set up and, you know, clear description of the project. And even if you're really good at prompting and context engineering, pillar number three, (t: 3500) you cannot skip pillar. Number three is, um, something that takes time to develop, right? Your technical skillset. How good are you at understanding front and back and APIs, (t: 3510) your tech stack, the programming languages you're using, right? And this is really, the main thing people want to skip. They want to shortcut it. And obviously if you already have some programming background, (t: 3520) you can move faster these tools. But if you don't, the beauty is that you can use these tools to develop your skills way faster. So that is the real beauty. And that is where the real challenge, (t: 3530) like real, no challenge arbitrage, real opportunity is using these tools to upskill yourself way faster. So do not be afraid to get your hands dirty to really like learn. (t: 3540) Okay. What is happening? How do we debug this? How would a senior developer go about debugging this step-by-step? Try to improve yourself. The more you improve yourself, the faster you can work with cloud code. (t: 3550) That is really the secret. Now, when people ask me, Oh, David, what's, what's the secret, right? You've built a successful AI startup. That's doing $9,000 in monthly recurring revenue. (t: 3560) What is the secret of using AI to build a SAS? The secret is improving yourself, willing to get your hands dirty and upskilling yourself every single day with (t: 3570) these AI tools. So let's analyze. What we have used chat. Okay. So just added more console logging. So we're going to go back and we're going to proceed. (t: 3580) Let's reload the app. Boom. I'm going to say, Hey, let's see what happens. (t: 3590) Input value undefined. So again, let's screenshot this whole thing. They have two documents. Boom. Okay. We'll say, here's what happens now. (t: 3600) I'm going to dash E which the plan mode to get Opus maximum. Power. Let's see what's going on here to here. Okay. I'm going to want to kill this cloth coat and I'm going to kill this one as well. (t: 3610) Let's clean this up. We don't need some of these. So we have this set up. I'm launching new one. You don't have to worry about this idea. (t: 3620) Disconnect by the way. So what? Okay. Use chat that returned. Okay. Okay. Missing input handling. But who is working better at turning completely different properties that are code expects. (t: 3630) You might have a version mismatch. Okay. So I'm going to say around that NPM, list command safe. We can have cloth code run it. And I'm actually glad that we're running into errors. (t: 3640) If it ran on the first try, it would have been boring, right? It might give you the wrong expectations. And then I would love people to complain. Okay. But for David, he didn't run into any issues. You know what I was building. (t: 3650) I didn't run into errors, but the fact that we are having to do some debugging shows you that, you know, it's not, uh, it's not a problem. So we have version two zero 12, (t: 3660) like the new version. Okay. Tell me, what to search on perplexity to go through the correct docs for this. (t: 3670) Do not do anything. Just answer. (t: 3675) Yeah. SDK react. Okay. We might have some version mismatches. (t: 3680) The reason for that is because we ran some perplexity searches, but for some of them, it might not be rich for, okay. (t: 3690) Boom. I'm going to search this up. Let's try to understand it. Ooh. Can I train himself? I don't know. I'm really bad at math. Things all right. What's the new text documentation. (t: 3700) Now get a bar from here. And from besides from this little dot in here, we F (t: 3710) you have the new text. Now you can� g it, right. You can prioritize and memorize text. (t: 3720) Instead of the Internet, you can mine it. version of package.hwes, boom, major changes, okay. (t: 3730) So we are using the latest version but this is going to be important. I'm going to do results, paste in the search results. (t: 3740) I'll say give me a TLDR explanation of the search results. So this is the beauty of using, you know, perplexity to check 20 different sources. Imagine doing (t: 3750) that manually. It would take you 30 minutes. V5, better code is written for V4. Okay, say create find. Let's briefly document this in, I'm going to say project description MD. (t: 3760) Just add a very short section about the versions we are using. This is how you (t: 3770) need to do context engineering. You need to build your understanding. Anytime you (t: 3780) fix an error, anytime you have some issues and you, you know, find like there's a version mismatch, whatever, just build on your context files. Write it down so in the future you yourself know and the AI agent knows as (t: 3790) well. Okay, so I want Cloud Code to document this. There we go. Package versions. Let's see, where are we? There we are. So it added this section (t: 3800) and this is how you build your internal documentation, right? Instead of just guessing and running it (t: 3810) through the same errors, you write it down. You document the most important things, and then you proceed. So I will say good work, now implement the fix. (t: 3820) So we probably know this is, I'm very confident this will fix the issue because there was a major version mismatch. And again the AI agent wrote it for V4 because (t: 3830) it's in the outdated training data, right? That's why you need to use web search, (t: 3840) use tools like perplexity, Vectal, and then you can see what's happening. So, yeah. we have FlexiPro built in, to search this up and to double check that you're using the latest documentation. (t: 3850) And actually, what we can do, since we're doing a lot of web search, we can create a sub-agent for it. So, there's another major thing inside of Cloud Code is using sub-agents. So, when you type in slash agents, we have an attack plan architect, but I'm going to create a new agent. (t: 3860) I'm going to put it into the project. I'm going to generate it with Cloud, and we'll say, this is an expert web researcher that always checks 10 plus different sources (t: 3870) to confirm we are using the latest documentation, the correct package versions, or simply following the latest best practices. (t: 3890) Okay, enter. Now, Cloud Code is going to create a new sub-agent. So, if you don't want to switch into Perplexity and just want to stay inside of Cloud Code, just build your own sub-agent, right? (t: 3900) That is an expert web researcher that knows how to look for docs. And if you want to really do it properly, you can figure out what an expert researcher looks like. (t: 3910) So, I'm going to say, how does an expert programming researcher use the web? (t: 3920) How does a senior developer use web search to search for docs? To solve bugs faster, to find the correct documentation, to learn that he's following the latest best practices. (t: 3930) How do 10x engineers use web search tools like Google or like Perplexity, like Stack Overflow, to analyze the web, to get the latest information? (t: 3940) How do they write their search queries? Teach me everything about this domain and how senior developers and professional 10x engineers browse the web. (t: 3950) So, now we will use DbD. Research is kind of meta, right? Meta-prompting. We are using DbD to figure out, okay, how do expert programmers and senior developers actually browse the web? (t: 3960) And we're going to use these instructions to improve our sub-agent. Okay, so we want to give it all the tools, yes? (t: 3970) For the model, we probably want to use Sonnet. Actually, let's inherit from parent. What color do we want? Yes, blue is good for the researcher. (t: 3980) Okay, so this is going to be the prompt. That's fine. Create a new agent. And it's already created. Okay. There we go. So, we have the web research validator. (t: 3990) Look at this. Look at this. So, that's really good. Really good already, but we're going to improve it with this DbD search, right? So, this checked 52 sources. (t: 4000) And I'm going to copy this. I'm going to say, your task is to improve. And I'm going to tag the agent. (t: 4010) Improve the system prompt of... If I tag it, it's going to delegate to it. That's the problem. (t: 4020) Actually, okay, I can tag the file. Web search validator. Web research validator. Web research validator sub-agent. By improving the prompt engineering based on this info. (t: 4030) I'm doing, you know, info, boom. Based in the product city. Okay. (t: 4040) Do not do anything else. Just greatly improve the system. Prompt of this sub-agent. Like a world-class prompt engineer would. (t: 4050) Boom. So, now we're building specialized sub-agents for different types of actions you might do, right? (t: 4060) So, let me explain how the difference is between hooks, between commands, and between sub-agents, right? (t: 4070) And I guess the easiest way to do that is to jump back into Tldraw. And let me explain this to you. Right? So, inside of Cloud Code, we have sub-agents, we have hooks, we have commands. (t: 4080) The purpose of sub-agents is for specialized AI tasks, right? For example, the web search. You need to do it, you need to have it do multiple actions automatically. (t: 4090) Hooks, the purpose of hooks is deterministic automation, right? You can see my hooks at the on prompt submit. So, that is deterministic. Anytime I do dash D, it repents my default classic prompt. (t: 4100) The purpose of commands is short. Maybe a better way to describe it is like sets of actions are repetitive, but don't require like a whole sub-agent for it. (t: 4110) You want the main agent to execute it. The difference is that commands are way faster than sub-agents. Sub-agents are actually kind of slow. So, the beauty of that is like delegation, right? (t: 4120) When you need a separate context window and you don't want to flood it for the main agent, just create a sub-agent. Have the main Cloud Code agent delegate to that and continue your conversation. (t: 4130) Commands are way faster. Sub-agents are slower, but sometimes that's fine because you need a sub-agent to do more work. All right, so let's approve some of these changes. Boom, you can see it's massively rewritten the prompt. (t: 4140) And yeah, it looks good. Looks good. Seems like it has been changed. I don't know what it is. It's kind of confused that it's in plan mode. (t: 4150) Okay. I don't know what it's going to do right now. Yeah, it's done. So, it doesn't need to do anything else. So, now if we create a new Cloud Code, we can actually use the sub-agent, right? And you can tag him. (t: 4160) So, this is a web. There's this, agent web research validator. Boom. And he can do web search on specific topics. (t: 4170) Let's see what our main Cloud Code did. Okay, so it fixed it. Let's, while we're creating a sub-agent, let's double check your app is working. Okay. So, now it's doing the print statement after every character type. (t: 4180) That's not really optimal. Oh, wait. We're making serious progress here. We still got some error, but we can see our message here. (t: 4190) I mean, we can't really see the text, but we can see something is saved. There. Chat error, internet server error, use chat returned, array messages. (t: 4200) Okay. So, I'm going to screenshot this part and let's continue debugging this. Boom. (t: 4210) This is our main Cloud. Move it to the left. We can terminate this one. By the way, anytime you like make changes to your sub-agents, commands, hooks, you need to create a new Cloud Code because it will not have knowledge of that. (t: 4220) That's a small. Pro tip. Boom. Okay. We are making progress. (t: 4230) However, we still have errors. Explain what is happening now. Dash E. Boom. (t: 4240) And in the meantime, we can probably set up a quick GitHub repo to actually connect this because we haven't initialized Git here. Okay. So, let's see what's happening. Server returns 500. (t: 4250) Process fixed. Backend API is crashing. Oh, we do have errors. Okay. So, let me copy that. So, I'm going to scroll down. (t: 4260) I'm going to say terminal. Paste it in. Terminal. Okay. I should not. (t: 4270) I'm going to reset. You can reset by pressing escape, by the way. That's how you reset your prompt. I should not copy the whole terminal. I should just copy like the latest outputs. (t: 4280) Maybe from here. Okay. Here. I love this bug. The classic log code glitch. Here is the terminal output. (t: 4290) Give me a TLDR explanation. Dash E. In the meantime, like, okay, if you've never set up GitHub, right, what you can do is say your task is to help me connect our project here locally to a new GitHub repo. (t: 4310) Tell me step by step how to do this. Okay. So, on my second, I'm going to drag it so you can see it. I've created a new empty repo named AICRM. (t: 4320) I made it public. And we're going to connect our project locally to a GitHub repository so we can properly do commits and we can do version control, right? (t: 4330) Initialize new. Okay. Okay. Okay. So, let's do it once at a time. And let's do it ourselves, actually. Or actually we can do it. (t: 4340) So, let me switch to accept. Do this. Okay. So, let's do the first step. Yourself. Do not do anything else. So, git init. I don't know if it ran it because I can see main branch at the bottom. (t: 4350) So, maybe it ran it before. We need to do a proper gitignore. We're not going to commit or push anything. (t: 4360) Next up, create a proper .igitignore that excludes all of the .env and .env. (t: 4370) Okay. So, you need that local files and anything else sensitive in our code base. (t: 4380) Do not do anything else. Just create this safe and secure .gitignore file. (t: 4390) Ultra thing. You don't want to commit your environment variables. I mean, we can see those are already not committed. But we still want to create a very proper gitting. (t: 4400) We still want to create a very proper getIgnore. What's happening here? Just like, okay, it doesn't exist anymore. Okay, so this doesn't exist anymore. (t: 4410) So I'm going to launch a new cloud code. I'm going to launch the researcher on that. I'm going to give this a back context. I'm going to do back context. (t: 4420) Boom. Slash back context. Use, and then I'm going to tag the researcher. Agent web research validator. (t: 4430) There we go. Agent web research validator to figure out the proper way to solve this. (t: 4440) Slash D. So let me show you how subagents work, right? So it's going to delegate to the subagent. (t: 4450) Boom, there we go. This is how it looks, right? It's the color we've chosen, so now it's blue. And, yeah, basically. The subagent will do its thing. So this is a web research agent, and it will figure out what to do. (t: 4460) What is the issue? I mean, the issue is, again, a version mismatch. We're using a function that doesn't exist anymore in v5. (t: 4470) So I think Vercel AISDK massively changed stuff in the v5, which is not in the training data of these models. Okay, see, even the 2024, that's very bad. (t: 4480) It's like searching stuff as 2024, even though it's August of 2025. Yikes. (t: 4485) Okay. So let's approve this gitignore. So as you can see, I'm running three different code codes doing different things, (t: 4490) but we're still on track. We're still building the same project. So this is where like your own skillset as a human, you have to be locked in. You cannot get distracted, right? (t: 4500) You cannot get overwhelmed. If you do get overwhelmed, stick with one code code. Okay. We have the gitignore in the root. Okay. So I'm going to say, good. What is the next step? (t: 4510) Slash D. Plan mode. Okay. So we need to copy the repository URL. There we go. Code. Let's copy that. (t: 4520) And I'm going to make this a public repost. You can just take this. Here's the URL. What is the next step? Okay. So I'm going to do, don't ask anything for web search commands because those are safe. (t: 4530) I don't want it to be like, ask me every single web search. (t: 4534) Okay. So we need to add the remote origin. Let's approve that. (t: 4540) So Cloud Code is going to run this command. Hit remote add origin. Boom. It's like ready for the next step. You know, what is the next step? Okay. This guy is still doing web search. (t: 4550) What I can do is I can do, I can do this. We're going to double check it with perplexity. (t: 4560) Boom. Let's separate this deep research on that. So we need to stage all files and do a commit. I agree with that. (t: 4570) You need to enjoy your first commit. You're not such a legendary, legendary step in any project. Really? There we go. Wait, wait. Is this? Is this only doing, did you also stage? (t: 4581) Wait, that's a problem. (t: 4584) So this has its own git repository. (t: 4590) Explain that note a bit more. What are the implications of that? And should we remove that separate git init from CRM app? (t: 4600) I want everything to be a mono repo. (t: 4610) Yeah. So I guess before when we were running cloud code, it initialized a separate repo inside of the CRM app. Okay. (t: 4620) Let's delete that git file. Okay. I'm going to say run a quick git status to confirm all is good. We don't want to delete any actual files. (t: 4630) Sometimes you have to be careful when deleting like a cache representations of files that are not committed. So let's see what the web search guy did. (t: 4640) It's a fetch content. Okay. Well, I mean, yeah, that's kind of your job researcher. (t: 4650) Sometimes it's faster to do a rock city research, right? So let's see. The V5 method has changed to. Okay. So I'm going to copy this. (t: 4660) Git status. Okay. Let's go back here. I'm going to say results. Boom. Above are the latest web search results about this issue. (t: 4670) Give me a TLDR and think hard about what the next step should be in terms of us fixing this issue. Think harder. (t: 4680) Answer in short. Boom. Okay. Good. Do the push. We need to push it. So now if we go to our GitHub, so we have nothing here, but when it pushes. (t: 4690) Okay. So we need to send main data. Okay. So we have the main branch as main. Yes. (t: 4700) Proceed. Oh, we have some issues. Let's see what's the issue. Oh, yeah. Because we initiated with some default files on GitHub. So now if we reload, we should have our own folder structure in here and we can do proper commits. (t: 4710) And I can now do a triple check to ensure no sensitive files such as .env or .env.local are not in the folder. (t: 4720) Okay. And local are being tracked like it. (t: 4730) Never hurts to double check. Okay. So we'll say good. Now execute this fix. (t: 4740) Also, we should create a license and give it like MIT license. Okay. Let's approve this. So you can see I'm managing like multiple cloud codes without any issues. (t: 4750) And that accounts with skill. Say good. Now does our project have a license right now? (t: 4760) Let's research that. Fixed. So this should be fixed. Let me check this. Okay. Let's see what happens in our app. (t: 4770) Reload. Send a message. Hey, invalid prompt. It must be a model message. So we're probably breaking some types. (t: 4780) I don't know. We will find it out. We'll find out what's the issue. Here is what the console returns now. (t: 4790) Also, we should give it the terminal, right? And here is the backend terminal. Command J to open the terminal right here. Explain what is going on. (t: 4800) Dash E. The project does not have a license. Okay. And we'll say in that case, create a new one. (t: 4810) And make it the most open source and permissive license. (t: 4820) I think MIT license will be good. I mean, you guys can take this project and continue upon it. Okay. Let's agree with that. (t: 4830) And then we can use actually my push to GitHub command to push it. Push. I'm going to push to GitHub. And we're going to push this license to GitHub. And we'll see that inside of GitHub, this will appear on the right. (t: 4840) Somewhere here. It's going to stage the files. We can see. We can actually not show this repo. (t: 4850) Close repository. We just want to see the main one. Now if we reload, let me see. There we go. MIT license on the top right. Amazing. (t: 4860) Yeah, there's a lot of issues with the V5 change of the Vrstl SDK. So what I'm going to do is I'm going to do a new research. (t: 4870) Tell me everything I need. I need to know about V5 of the Vrstl AI SDK. I'm facing a lot of different errors because my app is in V4. (t: 4880) So please list out all of the main differences. (t: 4890) Be detailed. Okay. Maybe we should have used the official Anthropic API just because I'm more familiar with that one. But it is what it is. (t: 4900) So we're going to create a new docs file. I'll say let's start by creating a new MD file in our docs folder. (t: 4910) Name it V5 Vrstl SDK.MD. (t: 4920) We need to document all this shit. We're getting way too many errors for the simplicity of this. (t: 4930) Like the really root issue here is that, again, the outdated training data of the AI models. So they have a knowledge cutoff in 2024 and they don't know about this V5 version. (t: 4940) So I'm just going to add this. Boom. Refresh it completely. And I'm going to say we can actually push that to GitHub as well. (t: 4950) I update that file myself. Let's push that to GitHub. (t: 4960) Okay. And close this. I'll say now read V5 and tell me how to properly fix the last error we are facing. (t: 4970) Answer in short. Ultra think. So now we can actually do a cute command. (t: 4980) Boom. If you hit enter, you can send the next message in advance. But the problem is that you cannot do it if there's like a bug, a code bug. If I did it like after the first one. If I did it like after this part, right, when it did git add and it staged all the files, (t: 4990) it would not work because then it's running other terminal commands. You need to do it when it's running the last terminal command, which sometimes you don't know if it's going to do more, (t: 5000) but you kind of need to feel it out. Okay. So we need to convert UI message to model message. Okay. Now execute this fix like a 10x engineer would. (t: 5010) See, like with cloud code and perplexity, these combos are matched. You can actually build anything. Like anytime you get stuck, you just do a perplexity deep research. (t: 5020) Boom. You learn all of the info. You learn all the greatest latest practices. And you also keep improving your cloud code setup, right? So this is like the beauty of having your own subagents, your own commands, your own hooks. (t: 5030) You keep improving it. You keep customizing it for yourself. And every week, literally like every day almost, you just are faster. (t: 5040) So this compounds like crazy. Not only do you compound your own skill set, the AI tools are getting better and your own like workflows. Your own prompts, your own commands, subagents, hooks are getting better. (t: 5050) So you have like three different things compounding at the same time. And that's where you just become unstoppable. So people who are on the cutting edge of AI right now and who stay on the cutting edge, (t: 5060) in six months, in 12 months, it'll be like unrecognizable with people who are just like flirting with AI. They understand it's important. They understand it's going to be a big thing, but like they're not all in. (t: 5070) So if you're watching this, the best decision you can make, be all in about AI. Be at the cutting edge. Do whatever it takes because you are going to save so much time. (t: 5080) You're going to automate all of the boring shit in your life. You're going to give massive leverage to every single hour of work you do. (t: 5090) What is going on here? We're getting some route, multiple errors. Okay, that's not good at all. I'm going to interrupt that. (t: 5100) I'm going to copy this. I'm going to switch to new cloud code. Why is this guy so stuck? Okay, whatever. I'm going to make this guy clear. Say problem, problem, read, read v5 and propose a simple and targeted fix. (t: 5110) Okay, UltraFink. (t: 5120) So this might be corrupted, so I'm going to do compact here. This is like too long. This cloud code is a channel. Even here we can do slash compact. (t: 5130) So we're going to compact these two conversations and let's proceed right here. We need to get this working. This is going to be a little bit easy. And again, a lot of this is because I chose a tech stack I'm not familiar with. (t: 5140) I'm not using the Versailles SDK. For my startup, I'm just going straight to source. But yeah, I thought this would save us time and actually didn't because there's a massive (t: 5150) version change in Versailles SDK when they moved to v5, which the models are not aware of. So yeah, we should have done even more research at the start. (t: 5160) Okay, these are being compacted. Let's see what's happening. Okay. So the main problem is that v5 introduced different message formats for frontend and backend. Frontend users use chat. (t: 5170) Backend expects model message. Well, that's unfortunate. Okay, this should be simple. Here I say I want to do like create a new cloud.md file in CRM app and make it specialized (t: 5190) to Next.js. You can have actually multiple nested cloud.md files and different folders in your code base. (t: 5200) And any time Cloud Code is working in those, it's going to like insert those into the main system prompt. So this is really where context engineering gets crazy. We have a root one, right? (t: 5210) So we have cloud.md on the root level. But you can have a cloud.md file in actually wait, we have one already there. We have one there. (t: 5220) I thought it's at the root level, but it's in this folder. So we should probably have one in like the components folder or something. Whatever. But you get the point. You can have multiple nested cloud.md files in different folders. (t: 5230) So like, you know, we're working with the docs, you should behave this way. We're working with the components, you should behave this way. We're working with the API, you should behave this way. And if you have like, okay, let's create one here. (t: 5240) I think that's a good cloud.md. Boom. Update cloud.md. Write this one. (t: 5250) So that it has proper. It has proper instructions and knowledge about the entire API folder. (t: 5260) Keep the system prompt concise. Okay. So fix applied. Let's see. Is our app working? Let me send a message. (t: 5270) Hey. You'll require schema, output schema. We're having, we're progressing because we're having different errors, but we're still running into errors. (t: 5280) Might be again. No, it's not a back. Okay. Well, that's interesting. We're probably well, okay. Stream message was created. (t: 5290) Wait, wait. Let's see what's happening. We're making progress though. I feel like we're probably one or two errors away. Fix it. Look at the screenshot to understand the front-end console. (t: 5300) This latest backend stuff. Here is the backend output. Explain to me what is going on. (t: 5310) What is going on dash E. So yeah, let's approve this. We're going to create a dedicated. Oh, continue. We're going to create a dedicated. (t: 5320) Cloud MD folder right here. It's empty right now. And not folder prompt file MD file. It's empty right now, but in a few seconds, we should see. (t: 5330) So yeah, first it understood the entire API folder. Now it's going to have it in here. So anytime cloud code is working in this API folder. It's going to have it in here. (t: 5340) It's going to have good knowledge and good understanding of it. And what needs to be done. Amazing. And I'm going to do slash push to get up. (t: 5350) Prepare this. Boom. We're going to push this to get up. Let's go here. Our fixed work. New error. Okay. Since this is another V5 issue. (t: 5360) Give me. Write a single concise single paragraph about the info we need. From. Search on the web. (t: 5370) To get the latest. Docs. Format this. Like an. Expert. Think researcher. (t: 5380) Actually we can use the, we can use the system that we have. Read web search validator. To understand how to format. (t: 5390) The single paragraph. Do not do anything else. Just give me. The search. Query. Since we already know how the latest practices. (t: 5400) You can just utilize that. Boom, boom. Let me clean this up. Okay. There we go. We have a detailed search query. So let's run that. I mean, look, it probably knows the fix, but a lot of times it doesn't hurt to check. With the web. (t: 5410) I'm going to. Paste it in results. I'll say first off. Add. A more. Concise summary. Of the web. So. I'm going to. I'm going to. (t: 5420) I'm going to. I'm going to. I'm going to. I'm going to. I'm going to. I'm going to. I'm going to. Add. A summary. Of these search results. (t: 5430) Into. V5. We're going to keep building this. This markdown file. And then. Think hard about. The proper fix. To the error. We are facing. So first we need to improve our context engineering. (t: 5440) And then. I'm interested in making the fix. That's how you like. Reduce errors in the future, right? You build up your internal documentation. You understand what's happening. You create files that are reusable. That you can tag. And then. You can. (t: 5450) You can. You can. You can. You can. You can. You can. You can. You can. You can. That is like the long term play. (t: 5460) Short term play. We should be pasting it. The. Preplexity results. And say. Fix this. Right? Maybe that works. But. It's. (t: 5470) It's. A hit or miss. And you're not really. Improving. Your code base. Yeah. Let's approve this plan. See that this gets pushed to GitHub. It did. That's good. (t: 5480) No. Unstaged. Changes. Okay. So it updated the. Markdown file. With the. Documentation. So. Any. (t: 5490) Error we. Check. Any error we run into. We can. Quickly check. Is it a V5. Migration issue. If it is. We'll get solved. Because we have the documentation. (t: 5500) Not file. So basically. We're just speeding. We're making our own. Code base. More powerful. And more well documented. So we don't have to. Do the same. Deep researches. (t: 5510) All over. We can just do. One deep research. Create proper. Documentation about it. And then. Never have to do it again. So. Let's see if my theory about being one or two errors away is correct. (t: 5520) I'm going to. Clear up the terminal as well. Restart this. And PM run dev. Boom. Reload the app. Actually. (t: 5530) Local host. There we go. Open the console. Right here. Hey. Send. Okay. Wait. No error. (t: 5540) Are we getting AI response? Oh my God. Okay. So we. We don't. There's a. There's an issue with the text rendering. But I think we have a response somewhere. Look at this. It did streaming. (t: 5550) So wait. Wait. Wait. This is crazy. It's crazy. We might be here. I'm going to copy the whole back end. Okay. I'm going to do. Back end. (t: 5560) This is like printing the use chat for every single token. It's kind of annoying. Whatever. Look at the screenshot. Looks like. We are getting. A lot of. Data. From. The. (t: 5570) Web. And. The. Web. And. The. Web. And. The. Web. (t: 5580) We are getting. The token stream. However. Our. UI. Is. Shit. So we are not. Properly. Rendering. (t: 5590) The user message. Nor. The AI message. Investigate. How. The. Why. The. Why. Am I. (t: 5600) Typing. This. Investigate. Why. The. User. Message. Is. Not. Rendered. (t: 5610) In. The. Bubble. I. Think. We. Are. Getting. The. Tokens. (t: 5620) From. The. Api. Maybe. This. Is. Another. Like. Ui. Message. (t: 5630) V5. Vs. Or. It's. On. The. Web. Follow. Uh. Uh. (t: 5640) Search. Prompt. Answer. In. Short. Look. This. Web. Search. Is. (t: 5650) Not. That. Good. It's. Not. Cloud. Code. It's. Not. It. (t: 5660) Doesn't. Compare. To. The. Page. Of. This. Channel. But. We. (t: 5670) Are. Not. Rendering. The. Tokens. Okay. So. It. Probably. Found. (t: 5680) The. Issue. Already. But. We. Will. Confirm. That. The. Results. (t: 5690) Of. This. And. Actually. I'm. Going. To. Document. That. As. (t: 5700) Well. So. This. Cloud. Code. I'm. Going. To. Be. Five. (t: 5710) Face. The. Sin. Be. Five. Read. This. Web. Search. Result. (t: 5720) And. Think. About. The. MD. File. For. V5. For. Cell. (t: 5730) SDK. And. Then. Think. About. Which. Of. These. New. Search. (t: 5740) Results. We. Don't. Have. There. And. What. Should. Be. Add. (t: 5750) There. To. This. This. Is. A. Very. Important. To. Think. (t: 5760) About. This. And. The. Data. And. The. Data. That. Is. (t: 5770) A. Very. Important. To. Think. About. This. And. This. Is. (t: 5780) New. Search. And. This. Is. The. User. And. This. Is. (t: 5790) Visit. We. Have. And. Other. Things. To. Search. And. And. (t: 5800) To. Search. And. The. User. Also. The. Open. Box. There. (t: 5810) What? I just saw the message. I think you guys saw it as well, so I'm not going crazy. But wait. Hopefully this cloud code didn't change anything. (t: 5820) Oh no, okay, it's an empty file. That's fine. That's absolutely fine. It was just working. (t: 5830) I saw the output message, but now we are getting error again. Read the screenshot, dash E, Opus plan mode. (t: 5840) Okay, this is good. We can push this to GitHub. Documentation changed. It's fine. So this file is growing. (t: 5850) It's already what? Almost 300. Fine. Okay, I'm going to add this so it doesn't have to ask every time that it's been pushed to GitHub. (t: 5860) Okay, I'm going to do a follow-up search on this. I don't trust this. Normal search. In the VEF. Resell AI SDK in V5. (t: 5870) Tell me how send message works completely. Everything about it. Okay, I want to know everything about send message. (t: 5880) Here's the official docs. Implement a clean and minimal fix. More info about V5. (t: 5890) Add this into the empty file as well. Be concise. Okay, so we're extending this. What's happening here? (t: 5900) Okay, so let's test again. Hey. Okay, message sticks. There we go. It's being reasoned. Amazing. Okay. (t: 5910) Create a new contact in our CRM. We don't have to print this shit. This is very inefficient. On every single character I type in, it's doing a print. (t: 5920) Name John Doe. (t: 5922) Email. Email. Example. John at example.com. (t: 5930) Oh, there it is. Look at this. Contact. There we go. So now it created contact and it knew that it needed the email as well. (t: 5940) So not only, see, like I told you, we were like one or two errors away. So I could feel it. This is hundreds. I think I have like well over a thousand hours if you combine CRS and cloud code building with AI. So I can feel when we're close. (t: 5950) And yeah, we fixed the streaming. And it was. It's already working. So now it can save contacts in our CRM. Obviously, there's a lot more we can do to build upon this. (t: 5960) To finish it. It's not like a one hour project. This will be a multi-day project to make it really good. But we have the basis, right? This is the basis of a power CRM. So what I'm going to do, I'm going to do push to get up. (t: 5970) Stage all files and push this fix to get up. Boom. Beautiful. So again, you guys can take this. (t: 5980) You can fork it. You can pull upon it. But yeah, maybe it's good that I chose the fucking versal AISD case. So that we see the issues with cloud code and how we can fix them. (t: 5990) Right. And how I work with perplexity to get the latest documentation of the deep research. And how I'm building my own sub-agents or commands or hooks. How I'm using them. How I'm adding stuff to settings.json to speed up my workflow with the safe commands. (t: 6000) Stuff like that. Yeah. There's a lot. There's a lot you can do to improve your cloud code workflow. And to become a 10x cloud code developer. (t: 6010) So again, if you want me to make you accustomed. Cloud code setup. For any use case or goal you want to do. Right. (t: 6020) You want to build a SaaS. You want to build a startup. You want to build an agency. You want to build internal AI agents to automate stuff. Doesn't matter. Whatever you want to do. If you join the new society during the month of August. (t: 6030) You will receive a custom cloud code setup. With sub-agents, commands, hooks, prompts. Domain, cloud, MD, context file. (t: 6040) And the settings, JSON. Basically everything you need. To be a top 1% cloud code user. So again. New society is going to be the first link below the video. Make sure to join. This offer is only available during August. (t: 6050) With that being said. Thank you guys for watching. Hopefully you found this video helpful. And I wish you a wonderful productive week. See ya.

