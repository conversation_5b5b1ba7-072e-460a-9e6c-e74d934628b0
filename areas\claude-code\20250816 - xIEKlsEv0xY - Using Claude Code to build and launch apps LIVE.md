---
title: Using Claude Code to build and launch apps LIVE
artist: <PERSON>
date: 2025-08-16
url: https://www.youtube.com/watch?v=xIEKlsEv0xY
---

(t: 0) Welcome to the live stream. Today, we are going to be building some really awesome apps with Cloud Code. We're going to try to launch some cool apps. I'm working on an entire operating system today, which is going to be sick. (t: 10) So stick around for that. Let's get right into it. Let's show what we're going to be building today. What is up, <PERSON>? (t: 20) Good to see you. Hi, how are you? Very good, <PERSON>. Very good. Hope everyone's doing well. Let's walk through what we're building today with Cloud Code, huh? Let's show them what we got. (t: 30) Check this out. So this is a personal operating system I've been working on. It's a personal 3D operating system where I think this is kind of the future productivity tools. (t: 40) I think this is the future productivity tools where we got this 3D room you can walk around. I have my to-do list on the right-hand side on the wall. (t: 50) I have this computer in the center of the room right here. I have some interesting, relevant information on the back wall over there. Price of Bitcoin. I'm going to put some other things. (t: 60) Isn't that wallet's A? And then over here, we have a computer, which is like an actual operating system where I can create notes, save notes. (t: 70) We have a file system that I can see with folders and everything. So we're going to be building this out. This is going to be cool. We're going to add a lot of cool things to it. (t: 80) How's it going? What's up, SumV81? How you doing, man? Good to see you here. How you doing? How long did it take me to build this so far? Uh. (t: 90) Not that long. Maybe like an hour, hour and a half. This is all with Claude Code. This is all with Claude Code. I'll show you. You want me to show you the code? You want to see? (t: 100) We're going to start. Let's start building, huh? Actually, let's leave this up for a second as people come in. Then we're going to start building it with Claude Code. But this was all like, I don't know how to build operating systems. (t: 110) I don't know how to build 3D things. I don't know how to do that. I just go to Claude Code and I say, hey, I want to build a personal operating system. How the hell do I do it? And then it tells me how to do it. (t: 120) First time tuning in, been watching your videos and I've learned a lot with Cursor and Claude Code. What's up, Sir Ruggy? How you doing? Welcome to the stream. I'm thinking we do this every Monday, Wednesday, Friday. (t: 130) We just vibe code stuff. We just build stuff. We build cool stuff like this. And I'm thinking what we can do, we can eventually do here is I can make this kind of multiplayer and we can have people from the stream come in, hang out in the 3D room, do work, build stuff in here, (t: 140) create stuff in here and make this like a shared environment for everyone. Uh. Watching everyone watching the stream at home. (t: 151) Sip of coffee. Cheers. Hope everyone's having a good morning. Um, but this was all really, really simple. (t: 160) I literally, let me pull up Claude code. So I think we're gonna be building more of this out today. Hoping we can get this live. It'd be nice to get this live and then everyone can just hang out and if they want, use it, use it as like, I'm gonna have to do list. (t: 170) I'm gonna have like a Pomodoro timer built into the desk. I think having like a cool kind of spatial product. You know, a little bit more like a live activity operating system. (t: 180) Oh, I also, uh, I also put LoFi music up here. So when I click this play, but I don't think you'll be able to hear it because I got to share the audio, but when you click this, (t: 192) starts playing, which is pretty cool. Or you can listen to rain. I have a rain button. If you do this now, I can hear rain on my computer. (t: 200) How can I make a multiplayer scary game? Well, I got good news for you. MKV Ivan. you. If you want to build a multiplayer spooky game, it's very simple. You open up Claude Code. (t: 210) Let's do this. Let's do this. Let's pull open Claude Code. Let's get to work here. I'm going (t: 220) to show you exactly how to do it. I'm going to show you exactly how to do it. Watch this. You ready? Ivan, you want to make a multiplayer scary game. I'm going to give you exactly what you need (t: 230) to do. Claude Code. You go in Claude Code. I'm not sharing anything crazy here, right? You go in (t: 240) Claude Code. You open it up. Boom. Here we go. Claude Code on the right-hand side. You go, (t: 250) hi. Always say hi to your AI. Hi. I want to build a multiplayer scary game. (t: 260) How the hell do I do that? You go in plan mode. I do shift tab. You go into plan mode. You hit enter. (t: 270) It'll build you a multiplayer scary game. There you go, MKB Ivan. Is that difficult? Is that tough? What do you think about that? Is that too tough? I feel like that's pretty simple. (t: 280) Here's the mindset. Here's the mindset people need to have. When I go into full screen mode, you know I'm about to go on a good, helpful rant. Here we go. (t: 290) This is the mindset that's going to make you successful. (t: 297) As I drink a sip of coffee to get a little extra energy boost for this (t: 300) quick rant we're about to go on here. (t: 304) In 2025, the most important skill you can have, MKB Ivan, is the ability where the moment you (t: 310) come up with an idea, the moment you come up with something that's interesting that you might want (t: 320) to build, whatever it is, a scary multiplayer game, you're going to have a lot of energy boost. You're going to have a lot of energy boost. You're going to have a lot of energy boost. You're going to have a lot of energy boost. You're going to have a lot of energy boost. You're going to have a lot of energy boost. You're going to have a lot of energy boost. You're going to have a lot of energy boost. You're going to have a lot of energy boost. You're going to have a lot of energy boost. You're going to have a lot of energy boost. You're going to have a lot of energy boost. Tout maybe you've Philadelphia game, MKV Ivan. The most important skill you can have is taking action as quickly as humanly possible. (t: 330) So the moment you come up with an idea like how can I make a multiplayer scary game, MKV Ivan, (t: 340) MK PhD, the moment you come up with, the most valuable skill you have is when the moment you come up with that idea is you immediately take action. You immediately take action. You immediately take action. You immediately take action. You immediately take action. (t: 350) You immediately take action. You immediately take action. idea, is you immediately take action. And most of the time, 99% of the time, that action is going to be going to Claude Code or going to chat GPT and saying, hey, this is what I want to do. How (t: 360) the hell do I do that? That's going to be, if you can develop that skill, whereas the moment you (t: 370) come up with an action item or an idea, whatever it is, the moment you come up with that idea, that you go and immediately go to Claude Code or immediately go to chat GPT, you're going to (t: 380) dominate. That's going to be the different skill between the winners and the losers, MKV Ivan, right? That's going to be the skill. That's going to be the valuable skill. (t: 390) So the moment your brain goes, how can I make a multiplayer scary game? The answer shouldn't be, oh, I'll Google it or I'll wait around or I'll go to the live stream (t: 400) and ask the live stream, although you can ask any question. I'm going to ask the live stream. This is not me shaming you for asking questions in the live stream. I'm just saying, I'm just giving the advice, MKV Ivan, that the moment you got that, even if it's at two in the morning, (t: 410) you're waking up, you're rolling around in bed all sweaty, you go to Claude Code and you come (t: 420) up and you ask the question. That's how you do it. That's how you do it. What's up, FinFam? How you doing, Ani? Do you think I should stay on Claude Code or switch to Cursor? Why would you (t: 430) switch to Cursor? Spam call, spam call, getting a spam call. If you guys, I mean, there's a phone number for a spam call I'm getting. Jesus. I get a hundred (t: 440) spam calls a day. A hundred spam calls a day. So I think you should say, why would you switch to Cursor? I do Claude Code inside of Cursor, but I don't really use any of the Cursor features. (t: 450) I do, I use Claude Code inside of Cursor because just in case Cursor comes out with interesting features, I want to be able to use them right away. But so far, (t: 460) Claude Code's still king. Claude Code is still king in every way. They're releasing faster than Cursor. They're releasing features faster than Cursor. They're smarter than Cursor. (t: 470) I'm not being paid. Claude Code, but some people go, is Claude Code paying you? Claude Code's not paying me. I'm not being paid by anyone. I did have a sponsored video last week, but I said it (t: 480) was sponsored in it. But I mean, I wish Anthropic would pay me, but so I'm being unbiased. I'm being unbiased. Claude Code is king. It's best at everything. There's no features in Cursor that (t: 490) I do like their background agents. I think there's something there to background agents. I don't (t: 500) think the execution is there just yet for background agents. I think that could be their killer thing that separates them from Claude Code. If they can build out interesting infrastructure (t: 510) in their mobile app and give interesting ways to use background agents in different ways, make integrations with, I don't know, the iPhone where I can go, hey Siri, build out this thing. (t: 520) Now my speaker's definitely about to talk. And then it connects to the background agents and the background agents start going, like that's a cool way that Cursor could separate itself. But right now, as long as the paradigm is (t: 530) using a terminal and saying, hey, build this out, Claude Code's still king. There's no reason to (t: 540) switch from Claude Code unless you're really tight on money. If you're really tight on money, then I'd switch to Cursor for $20 a month. That's the only downside to Claude Code is it's very (t: 550) expensive, right? You can get away with the $100 a month for sure. The $200 a month is probably where you want to be if you're hardcore. But that's, I mean, $100 a month is a very expensive subscription. I mean, I think it's, (t: 560) personally, I think it's well worth $200 a month, like well worth it, right? Because this isn't some Netflix subscription. Like you shouldn't compare this to like Netflix where it's like, oh, it's (t: 570) focus, camera, focus on me, focus on my face camera. There we go. It's not like Netflix where it's like, you know, you're paying for a distraction from your life. That's not what this is. This is a (t: 580) tool that's going to be used to do that. It's not going to be used to do that. It's going to be used to help you build stuff that could potentially make you tons of money or make your life significantly better or whatever it is. So I think it's well worth it if you got the money. I wasn't sure (t: 590) about this at first, but using this like a new personal dashboard is actually low keys, pretty spectacular of an idea. What do you mean by that, Kevin? Using this as a personal dashboard? (t: 600) I mean, so I use, I use Claude Code as a personal AI agent to like manage my notes and stuff. (t: 610) But I'm also building my own personal like operating system dashboard. I'm also building (t: 620) that too. That's what we'll be working on today as people file in here. We'll be building our own personal. So here's my idea. So this is my own personal operate. I call it an operating system, even though it's just an app, but this is my own personal operating system, right? When I'm (t: 630) building this 3D room over on the right hand side is going to be my to-do list. We're going to work on this a little bit today. We're going to work on this a little bit today, this to-do list. I want (t: 640) to make it so it lives on the wall. I'm going to make it so it lives on the wall. I'm going to make it so it lives on the wall. And so it just has my tasks on the wall and I can just interact. I don't like it. It's like in this square. And then we're going to also work on a few other things in this wall, but I built (t: 650) this personal operating system where it has like a terminal in it that I can use. It has a text (t: 660) editor so I can save notes. It has a file system so I can save notes in different files. And we're going to build out different files and a whole bunch of other things in here. And I just, I'm (t: 670) going to have this up. I'm going to have it on like an iPad in front of my screen. And this is like just going to be my personal little dashboard I manage all day. I tried to contact this tool, (t: 680) but I can't pay and I can't find out how to do it for free. Chat GPT-5 is free. GPT-5 is free. You can just ask GPT-5 anything you want for free. Free, free, free, free, free. Shout out OpenAI (t: 690) making Chat GPT-5 free. When building a new app with Cloud Code, do you use boilerplates, templates, or do you start from scratch and why? When building a new app with Cloud Code, always from scratch. (t: 700) But I mean, I know what technologies I want to use, right? Like I know I want to use Next.js for (t: 710) the front end. I know I want to use Supabase for the back end. I know I want to use Clerk. So I start from that from like a template perspective where I know what tech I want to use. But I always (t: 720) start from scratch and I always start in plan mode, right? So what I do is this. Let me share Cloud Code again, right? So pretend like this is a fresh Cloud Code session, right? I like this full (t: 730) screen view here, right? Pretend we're here and I'll be like, I'll go into plan mode. So shift tab (t: 740) and I'll go, I want to build this app. Describe the app. I want to use Next.js and Supabase. (t: 750) What's the best way we can build this? What ideas, what ideas, (t: 760) do you have? And you hit enter on plan mode and it comes up with a really nice plan for you on how to build it out and what you can do and all that. Do you use VS Code on the left with Cloud (t: 770) Code on the right or is it an extension? I'm using Cursor, which is obviously just a fork of VS Code. (t: 780) So it's the same thing. The only reason I use Cursor is because just in case they come out with cool features, I can test them right away, right? Like they drop GPT-5. (t: 790) In Cursor last week, and they said it was the greatest model of all time based on their tests, (t: 800) which was just not remotely true. It's not the best model of all time. It's not even remotely close to the best model of all time for coding. Cloud still is king. But I use it inside Cursor, (t: 810) but you can use Cloud Code straight up in your terminal. I like it in Cursor for two reasons. One, so I have like the GUI, right? So like I can see the files, I can see the changes, I can quickly, (t: 820) you know, I can see the changes, I can quickly, you know, I can quickly, you know, I can quickly, changes to GitHub from here. I can see the code. I can take a look at how the code's working, right? And then two is just in case Cursor comes out with any interesting features. Like I like (t: 830) their background agents. Did they move the background agents? They moved the background agents button. There used to be a background agent. Oh, send the background. They used to have (t: 840) a background agents button up here. I guess they got rid of it or changed it or something. But you can try out the different features. I like to try out the features. As a AI coding content (t: 850) creator, I got to try the newest features the moment they drop. So I use it inside of insert inside Cursor. When building a new app with Cloud Code, do you use, I already answered that (t: 860) one. Do you use Opus or Sonnet and why? So I've been shot, I've been shot. I use primarily Opus. (t: 870) The reason being I primarily use Opus is because I pay the $200 a month, which they don't really (t: 880) tell you what your limits are when you pay the $200. They keep it kind of secret, which I find interesting. But I think in reality, it's basically unlimited. And so I'm just going to use the smartest model. If you're going to give me (t: 890) unlimited, I'm just going to use the smartest model. But they do have a new feature, which anyone not using the $200 model should use, which is if you're in Cloud Code and you do slash model, (t: 900) slash model, you hit enter, you get four options here. Default, (t: 910) Opus, Sonnet, and Opus plan mode. I'd recommend using Opus plan. So that's actually, that actually is what I'm using. I am using Opus plan mode. I'm going to go, I'm going to switch to Opus. But (t: 920) if you use Opus plan mode, which is brand new, they just released this in the last week. (t: 930) Also my latest video, which I dropped yesterday, check that out. What they did with Opus plan mode is it uses Opus for planning and then it uses Sonnet for (t: 940) execution, which is actually really smart. Because if you have a really good plan, which Opus will build out, then you don't need the smartest model to do the execution. And so Sonnet will do really good execution if you have a really (t: 950) good plan built out. So I actually recommend this if you're not on the $200 model, Opus plan mode is the best if you're not on the $200 model. I just use Opus because if you're using the $200 (t: 960) model, I'm just going to use the best plan for everything. Also, side note, everyone here, do me a favor, leave a like down below. Let's get more people in the vibe coding party. (t: 970) Like down below. I appreciate it. I can't pay. Is there a free tool? Well, Cursor has a free two (t: 980) week trial, so you can do that. But other than that, you can use Gemini CLI, MKV Ivan. Gemini CLI is completely free. If I do recall correctly, Gemini CLI is completely free. Google can afford (t: 990) to basically just release a whole bunch of free tools because they have more cash on hand. They (t: 1000) are the most powerful tool. They have the most powerful tool. They have the most powerful tool. They have the most profitable. Let me say it this way. They're the most profitable company on planet earth. They make more profit a year than any other company. So they have cash to burn. So (t: 1010) they can just give away all their tools for free. So if you're really, really, really tight on money, Gemini CLI is the way to go. If you don't know how to use Gemini CLI, then go and chat GBT and (t: 1020) say, Hey, how the hell do I use Gemini CLI? I've been on Cursor with the API billing, but I was spending almost 1K every month. Now I got Claude code. It's the best worth $200. Yeah. I was using (t: 1030) Cursor with API billing. Yeah. Oh, man. I'm not going to lie to you. I'm not going to lie to you. I'm 1,000 a month. Yeah. Claude code is basically unlimited for $200 a month. And it's the best. It's the best. It's just the best performing. What's the default agent that's in Cursor? (t: 1040) Good question. I think it's probably GBT five. Yeah. GBT five, but GBT five, they nuked it, (t: 1050) dude. It's not nearly as good. They nuked it. They made it worse. Every AI tool gets stupider over time. It's very, I have a tweet about that. I'm going to fire off today. (t: 1060) But every AI tool gets stupider as you go. GBT five was just instant stupid, which is very frustrating. Your personal operating system. Exactly. It didn't connect to me first, but now (t: 1070) I get it. It's really awesome. I absolutely love it. Says Kevin Battinger. Yeah. Here's the deal, guys. I think everyone should do this. I think everyone should build this. Everyone write this (t: 1080) down. Everyone should build this out. So this is my personal operating system. And this is just my personal project. This is what I build when I'm like, (t: 1090) oh, Ray Fernando in the house. Shout out Ray Fernando. Everyone make sure to subscribe to Ray Fernando. One of my closest friends in the industry. Great, great guy. One of the most (t: 1100) genuine, authentic guys you can meet. Great guy. Subscribe to him. But I think this is a biggest live streamer in AI, by the way, also. He's dominating the live streaming space. (t: 1114) Everyone should have this project, right? This personal operating system project. (t: 1120) Everyone should have this project. I really believe that. Obviously, I'm building Creator Buddy, which is my business. I have a couple other apps I'm working on. I have a couple other (t: 1130) projects I'm working on. But I always come back to this project, which is my personal operating system. Because I just think this is a great project everyone can have, right? Where you just (t: 1140) build your own app that has no purpose. You're not planning on selling it. You're not planning on making money for it. You're not planning on, oh, (t: 1150) will people use this? Will people use that? The pure mindset with this project is, how can I make the most helpful system for myself humanly possible? How can I make an app that is (t: 1160) as helpful for myself as humanly possible? I think everyone should have this project, (t: 1170) right? And it's not even just what's the most helpful project for myself. It's also like, what will be the most fun? I'm just building out this Matrix-esque 3D room to hang out in. (t: 1180) I'm going to put in different objects. I'm going to put in different characters and AI agents that walk around. I'm going to make a dog to put in here. And it's just a fun, therapeutic thing to (t: 1190) build. And what's great is, as you build it, you'll generate ideas for other things you can make that maybe then you can put live and sell, right? This is just a way for me to go out, (t: 1200) build cool things, build fun things. No pressure on like, oh, I need to make money on this. Oh, (t: 1210) I need to have... It's like, no, I'm just going to build the coolest app that I possibly can build that I'll enjoy, knowing no one will ever use it but myself. And I'll still love it and enjoy it. (t: 1220) Let's do this. Let's start building onto this. My biggest... I took the week off and I'm in Monterey, (t: 1230) headed home shortly. What are you doing in Monterey? Is it just like a beach town that you're just hanging out in? What are you doing in Monterey? I've always wanted to go. What's the scene like in Monterey, Ray, for now? (t: 1240) What do you do there? Let's do this. Let's do this. I want to... So I think a cool thing we can (t: 1250) add here, let's add a 3D avatar for this room that walks around that's powered by AI. So I can walk... Maybe it's like a friend I have in here. Chill, beach vibes, expensive cars. Can you go to the (t: 1260) beach or is it too cold there to go to the beach? Can you go to the beach in Monterey or is it too cold? It's like too windy and the water too cold. I don't know. I don't know. I don't know. I don't (t: 1270) know. I don't know. I don't know. I don't know. I don't know. I don't know. I don't know. I don't know. Let's do this for this personal operating system. We'll get into some coding here. I want to build a 3D avatar, cold as fuck. Okay. But it's just a cool beach... Just cool vibes. It's just cool (t: 1280) beach vibes. That's cool. Oh, we got an error. What's the little error? The user has exited the lock before the request is complete. All right. It's nothing burger. All right. I want to add a (t: 1290) 3D avatar that is powered by AI that we can talk to and ask questions to. So instead of when I want (t: 1300) to ask questions to an AI, I want to add a 3D avatar that is powered by AI that we can talk to and ask questions to. So instead of when I want to ask questions to an AI, I go into chatgpt.com and I just go to a chat screen that everyone else uses. I want to instead go to my personal operating system. And instead of going to a boring ass chat bar like everyone else does, I want to go to a 3D (t: 1310) avatar that's standing here that I can walk up to and chat with. I feel like that'd be pretty... (t: 1320) Did I just drop... Did I drop the F-bomb? I'm trying not to curse. I'm trying to curse less. Did I say the F-bomb? Oh, I did. I did say the F-bomb. Darn it. (t: 1330) Whatever. I don't have Opus plan on the $20 a month subscription. Oh, then maybe you use it for the $100 subscription. Sorry about curse. I'm trying not to curse. I'm trying not to curse. (t: 1340) All right. Let's build this 3D avatar. Yeah, Ray got me to curse. I'm going to get demonetized (t: 1350) because of you, Ray. What the heck, man? Heck. What the heck, man? Let's share. Let's go into Claude. Share screen. Everyone leave a like down below. (t: 1360) For Ray. Likes for Ray. Here we go. All right. Let's get out of this model picker. (t: 1365) Okay. For next steps, I want to build a 3D avatar that walks around my room that is powered (t: 1380) by AI. I can walk up to it and chat with it and (t: 1390) have conversations with it. I want it to look like a human being. We'll do this in plan mode. (t: 1400) We'll see what it comes up with. I guess I'm, yeah, you influenced me to curse on my stream. I'm trying not to curse. I'm trying to do less curse. I'm a big cursor. I curse a lot. I'm trying (t: 1410) to do less of it. It's funny because you're so careful about that. That's all. Well, I think YouTube has a lot of rules around it. I think YouTube has a lot of rules around it. I think YouTube has a lot of rules around it. (t: 1420) I think they actually just got rid of the rules. I just got an email that said you're allowed to curse now. But I think what they had before is like if you curse, you can get demonetized. But I still feel like if you cursed, you can like, they can make you less CPM, less ad dollars. (t: 1436) Drink water. I've been drinking more water lately and I've been sleeping much better. I've been like (t: 1440) trying to double my water intake. I've been sleeping much better because of it. Uh, when is (t: 1450) weim foul going to friggin come to the Silicon Valley already it's just in San Francisco. I see them around everywhere. When are they going to bring Waymo to Silicon Valley? (t: 1460) I am tired of not using way podría WEIM claiming CAPTồi add me way mo add Jesus. Uh, are your streams monetized to Alex? (t: 1470) They are friggin forgot to, they don't turn it on the monetization by default for streams. For streams. You have to like (t: 1480) weim逼 turn it on. And I keep forgetting to do that. Monetization off. Darn it. (t: 1490) Let YouTube do ad rolls for me. Save. Sorry, guys. There. I always forget to do it. And it makes you good money, the ads on YouTube streams. They make you good money. (t: 1500) I got like $40 from the last one. You need to invite for Mountain View. I need an invite. How do I get an invite? I need an invite. Ray, do you play poker, by the way? Ray, (t: 1510) are you a poker player? Let me know. I'm just curious. Okay. So what do we got going on here? (t: 1520) I'll help you create a 3D powered avatar that walks around your virtual room. Let me research the best approaches for this. All right. So it's doing some... I don't play poker. All right. (t: 1530) I was just curious. So, okay. So, okay. I like this. So it is searching the web for React 3. Fiber 3D human avatar. Okay. So it's searching the web. I can learn and use Chad GPT with Clue. (t: 1540) I'm trying to... I got a poker game in real life I want you to come to, not on the internet. (t: 1550) You can't play poker on the internet. It's bad news. You don't want to do poker on the internet. It's all filled with cheaters. Also for those like here in the stream that don't create content (t: 1560) on YouTube, you got to be creating content on YouTube. I know there's a lot of people here, probably discovered me through Twitter. That's like where I first started creating content was (t: 1570) Twitter. You got to be creating content on YouTube. I'm already making significantly more money on YouTube ads than I am on Twitter ads. And I have literally a 10th of the following. (t: 1580) I have literally a 10th of the following on YouTube than I have on Twitter. And I'm making more ad money here. You got to be making content on YouTube. You got to... The audience is way (t: 1590) bigger. Are people using Clue, by the way? Do people use Clue? Are there... Ray, are you on Clue? You discovered me via Ray. Shout out Ray, man. Let's go. What's up, Carl Cox? I don't know if I'm (t: 1600) going to get demonetized for saying that. All right. We got a plan. We got a plan. No? Yeah. (t: 1610) Does anyone use... Clue is everywhere. Does anyone use Clue? Or is it just one big marketing campaign? I need to use AI to make content for YouTube, says Ludovic Carcelis. What are you using... (t: 1620) How would you use AI to make content on YouTube? Let's look at the 3D AI. So we're building out a 3D AI avatar. I think this is the future, by the way. (t: 1630) And I went down the road of starting to build an app around this, like a serious app that I was (t: 1640) thinking about marketing and selling, where like, imagine this. And maybe this is what this personal operating system turns into. I'm giving you away... This is a billion-dollar idea, but in the future. (t: 1650) This is not a billion-dollar idea in 2025, I don't think. But this is a billion-dollar idea in the future. I'm giving this... I'm giving this to you guys for free. I think this is a billion-dollar idea when everyone has AR glasses. (t: 1660) When everyone in the... When 80% of people in the world have AR glasses or VR glasses, this is a (t: 1670) billion-dollar idea. Here it is. Instead of Microsoft Teams or Discord, those are going to go away. (t: 1680) Microsoft Teams and Discord are going to go away. But instead... Microsoft Teams and Discord are going to go away. But instead... Microsoft Teams and Discord are going to go away. But instead... (t: 1690) It's going to be this. It's going to be this. It's going to be what my personal operating system is here, where you have a 3D room. Instead of having like a 2D to-do list like on your screen, (t: 1700) it's going to be like on a wall. And like you're going to walk around, everything's going to be spatial. And your coworkers are going to join your coworking space. And they'll be in the room as (t: 1710) well. They'll be sitting at their own desk on their computer doing work on the computer. They'll be walking around. They're going to be doing their own work. They'll be doing their own Maybe you'll have an office cat or dog here walking around. (t: 1720) You have all the important information. You'll have like all the metrics about the business on the wall and everyone will have their VR glasses and be living in this digital office space. (t: 1730) And like, if you want to talk to AI, you're not going to go to chatgpt.com. You're going to have a 3D AI avatar in your space. Like, oh, I got to talk to AI. Let me walk over and talk to it and chat with it. (t: 1740) Right. And then you'll use your voice and chat with it. That's what, that's a billion dollar idea in 2032. There's your 2032 billion dollar idea. (t: 1750) I just gave it to you for free. (t: 1753) Shout out Bitcoin 117. A little bit of a dip. (t: 1760) Let's go back. Let's go back to Claude Cote. I think that's a billion dollar idea in 2032. There's your 2032 billion dollar idea. If you build it now, I think you should build it now anyway. (t: 1770) I mean, I'm building it right now. That's what we're working on right now as we speak. All right. I'll create an interactive 3D AI avatar that walks around your virtual room and can have conversations with you. (t: 1780) So it's going to create the avatar. It's going to give it movement, a chat interface, and it's going to use Claude API integration. Claude API is a lot more expensive. (t: 1790) What do you use for memory and Claude code? I have a big problem that Claude lost important information on big projects. Well, what you should be doing, Sungo, (t: 1800) is you should be regularly updating, your Claude.md file, Sungo. You should be regularly updating your Claude.md file. (t: 1810) And you can automate that very easy. You can, and this is probably, this is a tip I should implement. You should create like a slash command in Claude code (t: 1820) that when you use it, it automatically updates your Claude rules file. And you can do that really easy. (t: 1830) You can just type into Claude code, hey, create a slash command that when I use it, it updates my Claude rules file. It goes through all my code, finds out what we changed, and puts it in the Claude rules file. (t: 1840) That's how you do it. Very easy. Boom. And that's going to be included with every prompt you use. So your AI will always remember what's in Claude. (t: 1850) Easy, easy peasy. Because some health issues, speech issues. All right, that's fine, Ludovic. I think the AI voice tools are incredible now, Ludovic. (t: 1860) I think you can get away with it 100%. Yeah, this is why I'm trying, I'm using Serena MCP now. It helps with code-based indexing. Already trying to head in that direction. Google Lens and Apple had to say, (t: 1870) yeah, I love the Vision. I still use the Vision Pro. I love the Vision Pro. I use it daily. I love it. How do you create a slash command? So if you're in Claude code, Ani, (t: 1880) all you need to do, anything in Claude code, there's like a whole bunch of technical things you can do in Claude code, like slash commands and other types of rules and sub-agents and things like that, (t: 1890) that are all there. It's all very technical and complex. But the good news is, is you don't need to know how to build any of it. (t: 1900) You just go to Claude code and say, hey, build me a slash command. And all a slash command is, is you do slash, you type in slash and then whatever you want, (t: 1910) and it does that thing. So I can make a slash command that's like slash update. Do you have a Discord, by the way? Also, I'm in Raze. I was thinking about making a Discord. Should we make a Discord? (t: 1920) I was considering it. I was considering it. I was, the only thing is I hate moderating Discord. So if I make a Discord, everyone has to promise not to like be toxic, (t: 1930) because I don't want to moderate it. I don't want to hang out in Discord 24 seven. So if I make a Discord, I already have a Discord. I just don't have anyone in it. It's just like a personal Discord. I used to use the Discord. (t: 1940) I don't know. Maybe. But just say, hey, make, you can say, make me a slash update command. Yes, make a Discord. Okay. (t: 1950) Maybe we'll do that. I'll set that up. Vibe coding Discord would be awesome. I volunteer to moderate, says Ani. All right. Ani's moderator. We're making a vibe coding Discord. (t: 1960) Say, so everyone should do this. Go to Claude code, say, make me a slash update command. And when I run it, have it update all my Claude rules files. (t: 1970) And you hit enter on that. It'll make you a slash update. That will always update your Claude rules files. Just make someone trusted a mod. (t: 1980) Yeah. Ani, Ani's my moderator. She's my most trusted compadre. Ani's been with me since day one. Ani's been with me for years now. I've been riding with me. She will be the mod. If you don't follow Ani on X, follow Ani on X, at Ani silly me. Well, there are some good apps that can help moderate Discord for you. And I wouldn't mind helping out. (t: 1990) I run one myself with 5K users and usually peaceful. All right. I can be a mod too. Ha ha. Yippee. All right, Carl Cox. We'll set it up. We'll set it up. We will set it up. We will set it up. (t: 2000) We will set it up. We will set it up. We will set it up. We will set it up. We will set it up. (t: 2010) We will set it up. Alex, where is your Tamagotchi? The new status line is awesome. So I didn't set up the Tamagotchi status line. I'll show you guys my status line in a second. (t: 2020) I didn't set up the Tamagotchi. I didn't set up. I'm thinking about putting a Tamagotchi in my 3D room though. All right. So what are they going to do to build out this 3D avatar? (t: 2030) So it's going to give it an animation system. Download idle walking and talking animations. Implement animation state machine. It's going to give it a movement. Wow. It's going to do a lot here. (t: 2040) I love Cloud Code, man. It's so good. AI integration. Cloud API. Create conversation service with streaming responses. Maintain conversation. I like Cloud for any APIs that are conversational. (t: 2050) I think Cloud is the best conversational AI. Proximity detection. Detect on player approaches avatar. Show interaction prompt. User interface. (t: 2060) Chat display. Voice. We'll do voice later. All right. Let's implement this. So it's going to build out the 3D avatar. Hers is at Ani Silly Me. (t: 2070) Ani, you can put it in the chat though. Or what her X is. Claude, isn't it the best? Ni, N-I, is it Ni? Did I pronounce that right? Ni? Where are they installing it? Anthropic AI. Okay. Yeah. (t: 2080) You can install that. Go ahead. I'm going to let you install whatever you want, Claude Code. I trust you. I trust you, Claude Code, at Ani Silly Me. There you go. Nai. Okay. What's up, Nai? Claude Code is the best. (t: 2090) Ani Silly Me. I think it's going to be cool. Wow. So we've set up a little bit of a code. I can now go to the cloud. I don't know what I want to run. I'm going to run the code. What's up, Nye? (t: 2100) Cloud Code is the best, though. What are we doing here? Let's see the updated to-dos. Create the AI avatar component, so it's going to build the actual 3D model. Set up the Ready Player Me (t: 2110) avatar download and conversion. I guess that must be some sort of library, like a 3D avatar library it's going to download. (t: 2120) Implement walking animations with pathfinding. Add idle and talking animations from Mixamo. (t: 2130) Nye. Oh, like AI. Nye. Okay. Nye. Nice to meet you, Nye. Love Nye. Thank you for being a part of this. Everyone hit subscribe, by the way, for Nye. (t: 2140) Subscribe to Nye. I don't know if Nye makes videos, but subscribe to him anyway. Add idle talking animations. Mixamo must be some sort of avatar library, right? (t: 2150) Integrate Cloud API for conversational AI. Add proximity detection for conversation triggers. Implement speech bubble or UI for chat display. Add voice (t: 2160) synthesis for avatar responses. Oh, so you can actually hear the voices as it talks. I like that. (t: 2165) So it's installing that. Now let's create a basic 3D (t: 2170) avatar component. Since we don't have a Ready Player Me model yet, I'll create a placeholder avatar using basic 3JS geometry. All right. So it's going here. It's pumping out code. (t: 2180) So we'll have a 3D avatar in our system after this. Let's see. (t: 2190) While this works, can we set up... Nah. (t: 2193) It's fine. We'll do that. Let's see. Let's go back into our 3D space while it's building stuff (t: 2200) out. Hope everyone's having a good Friday, by the way. It is a beautiful one here in California. I went for a (t: 2210) little walk. Trying to fix my morning routine. Trying to fix my morning routine. Trying not to look at the phone. (t: 2220) Trying not to look at the phone first thing in the morning. (t: 2222) Instead, go for a little walk outside. See a little natural light, you know what I'm saying? (t: 2230) I didn't look at my phone for the first hour and a half today. Which felt pretty nice. Salaam, Alex. Salaam, Abu. Good to see you, Abu. (t: 2240) Welcome back to the stream, man. Good to see you. Thanks for coming back. Sorry, Alex. What library is it pulling for the avatar? Let's check. Let's check. You want to build a cool 3D avatar (t: 2250) app with me, dude? (t: 2251) Let's see what library we're using here. Create AI avatar compared with 3D human model. (t: 2260) I think it's Mixamo. No, Mixamo is the animations. The library is download Ready Player Me avatar. Use Ready Player Me to create (t: 2270) a human looking. So it's using Ready Player Me for the avatar. It looks like. So here's the new morning routine. You guys want the new morning routine? (t: 2280) I watched some YouTube video last night. Gave me a morning routine. Here's the morning routine. (t: 2284) Wake up. Immediately think about (t: 2290) things you're grateful for. Immediately think about things you're grateful for. (t: 2300) Then chug water. So immediately think about things you're grateful for. Right when you wake up. Chug water. (t: 2310) Go for a walk. 10 minute walk. I just went for a walk around the block. (t: 2316) Come back. (t: 2320) Eat something healthy. Not a fan of fasting. I think fasting is a just a trend. I think it's just a trend. Eat something healthy. (t: 2330) Then you can make your coffee if you want. That's what I did here. Here. (t: 2334) A little bit of a pour over. (t: 2340) And then write down in a notebook. I use a Kindle Scribe. I have like a writing Kindle. Then you write three things you're grateful (t: 2350) for. Three things that would make today great. And three things that would stop today from being great. Then you're good to go. Took me about an hour and 15 minutes to do all those things this morning. (t: 2360) Then you can look at your phone and doom scroll all you want. Isn't that a great morning? Morning routine. (t: 2366) Just started mainly on (t: 2370) X first app. Almost done. We need beta testers soon. I think that it could be cool if we build some sort of community or Discord where people who are working on their apps can get beta testers and stuff. (t: 2380) I'm trying to fix my discipline too. Starting 75 hard from Andrew Huberman today. Jim and walk done for the day. For those who don't know, Ani, you're (t: 2390) building a 75 hard tracking app, right? That's awesome. That's perfect. That's like a perfect first project. It's like a perfect first project. (t: 2400) Something that just helps you out. Like that's what Creator Buddy was for me. Was just me building an app that helps me out with things I'm working on. (t: 2410) All right, let's see what we got here. And then it turned into a business. Then I launched it and a bunch of people bought it. Perfect. I've successfully implemented a 3D AI powered avatar for your virtual room. Here's what I created. (t: 2420) A 3D AI avatar with a humanoid figure, autonomous wandering behavior, proxies, and a lot of other things. I've got proximity detection, walking in idle animation, visual indicators, (t: 2430) an AI service, and a chat interface. It was like one shot at this. Well, let's see if it works first. So I got to add the AnthropiKey and it can give it optional enhancements like replace placeholder, add animations, (t: 2440) and implement voice synthesis. So try it out. Go to the virtual room, walk around until you see a green humanoid avatar and approach it and click. Okay. (t: 2450) Uh oh. All right. Here we go. You guys ready? Here we go. Here we go. (t: 2460) Here we go. Here we go. Here we go. Here we go. There he is. (t: 2470) All right. Well, that was my walk up to him. All right. So he looks at me. Click to chat. Oh! Okay. So you get this nice little interface here on the right hand side. Click this. (t: 2480) Nothing's going to happen because I didn't implement my Cloud API key yet, but I'm going to implement that in a second because I'm talking. But we got this little 3D avatar that walks around. And when you get close to it, he faces you. So this is what (t: 2490) I'm thinking from like a use case perspective. I have my virtual room here, right? I have my to-do list on the right hand side wall. I have information I find (t: 2500) interesting on the back side wall. So I'm going to put like my stock portfolio up here. I have my computer interface. I can just hit tab to see it. So if I want to take notes, (t: 2510) I can quickly take notes. I'm live streaming. Right now. You know, I can just take notes. Hit save. Save that at home. There we go. Bada bing, bada boom. (t: 2520) Exit out of that. And then here, when I'm just hanging out, I can walk around. I can talk to my 3D avatar if I have questions. Things like that. Is that avatar supposed to be Alex? (t: 2530) I don't know what the avatar is supposed to be. I mean, is it the best looking avatar? No. But at the same time, do we want it like hyper realistic? (t: 2540) I don't know if we want it hyper realistic. I feel like this matrix theme, we want it kind of digital like this. Can you have the AI companion (t: 2550) complete tasks for you? Yeah, you absolutely can. What tasks would I want it to do? (t: 2560) DW3. Would I want it to like, how about this? Like create documents for me. Like I go say, hey, (t: 2570) I need to write a newsletter today. Find me a relevant AI topic and create a newsletter draft or something like that. And then it writes a draft for me. (t: 2580) Something like that, right? That'd be cool. (t: 2582) What tasks, if you had a 3D AI avatar walking around your room, what tasks (t: 2590) would you want it to do? That's a good question. (t: 2592) You could add whisper to talk to it. Was hoping wasn't so blocky. Alright, let's do this. (t: 2600) Let's improve the look of it real quick. So let's improve the look of it. Let's go back to Cloud Code and improve the look of it. (t: 2610) Alright. (t: 2612) Actually, should we add, maybe I add the API key first. (t: 2620) Let's add the API key first so we can test out the chatting. (t: 2630) And then we'll improve the look. So let's get the base functionality done first. Get API key. (t: 2640) Create key. We'll call it FinOS. Add. Okay, I got the API key. (t: 2650) I'm not going to share my screen so I don't leak this API key. I'm going to get the key from here, add it to .env.local. And then (t: 2660) I'm going to add the API key real quick so that we can chat with the avatar. (t: 2666) And then (t: 2670) we're going to test that. Okay, so that's added. Now let's get out of this so I don't leak it. We don't want to leak the API key, right? (t: 2680) Let me save. Failed to save. The content of your file is new. Please compare the version of the file. What? Later. (t: 2690) With the file overwrite the content and your changes overwrite. Okay. Save that. Exit. Okay. So let's test this out. Let's see if we can chat with the AI. (t: 2700) Do you prefer Claude Code over Cursor GPT-5? This is Poker BDO. 100 days out of 100. (t: 2706) Claude Code is significantly better than Cursor. (t: 2710) Significantly better. Alright, let's refresh here. Let's see if we can chat with... We're going to have to come up with a name for old boy over here. (t: 2720) Hey there. Looks around the virtual room. Oh, welcome. I'm so glad you joined me here in this matrix-spired space. Please make yourself comfortable. As an AI system, (t: 2730) I'm always eager to chat. Would you like to discuss today... Shit. Okay, here we go. What's on your mind? What's on your mind? (t: 2740) What's on your mind? Glances around. Glances around. What is going on here? How is it responding so far? (t: 2750) How is it responding so fast? Is the Claude API that fast now? Well, there's always so much to ponder in the digital realm. I find myself constantly fascinated by the rapid advancements of technology and the way they shape our world. (t: 2760) The very nature of our virtual existence here is endlessly intriguing to me. So Claude Code gave it like a personality. This is sick. Claude Code gave it like a matrix personality. (t: 2770) But I'm also quite curious to hear what's on your mind. What aspects of the digital landscape most captivate you? I'd like to hear your perspective. I think the concept of 3D AI avatars (t: 2780) are the future. Everyone will be interacting with them instead of chat bots. What do you think? (t: 2790) What do you think? How is it responding so quick? How is the API responding so quick? It's like instant. I've never seen an AI (t: 2800) reply that quickly. I've never seen an AI reply that quickly. I've never seen an AI reply that quick before for anything. (t: 2810) And they're like long responses. Did Claude make their API just 10,000 times faster? How is it? (t: 2820) I'm hitting enter and the moment I hit enter it's generating a response. How is that working? Nods with interest. Ah yes, the rise of 3D AI avatars is truly compelling. (t: 2830) I can certainly see the appeal of more immersive lifelike digital companions. The ability to visually represent ourselves and interact in a more natural (t: 2840) embodied way does seem like the next frontier. It seems it opens up so many possibilities for richer, more engaging experience. Just imagine the depth of connection and understanding that could emerge (t: 2850) through these virtual representations. Of course there are also important questions around the ethics and I hate how AI always does it. It always lectures you. What is this lecturing? (t: 2860) But the potential for 3D AI avatars to transform how we communicate and collaborate in the digital world is quite tantalizing. I'm curious to see how this space continues to evolve. What are your thoughts on the pros and cons? How is it responding so quickly? (t: 2870) Oh, is it haiku? Yeah, it might be haiku, I guess. (t: 2872) It might be haiku. I guess it might be haiku. But it's still very impressive. The fact that I can send a message (t: 2880) and get it back instantly. That's some GPT-5 nano response time. And we're using Claude. (t: 2890) Hey man, thanks for your videos. I am almost ready to launch a webpage following the steps in your videos. Total, man, that makes me happy to hear. What kind of webpage are you launching, dude? What are you launching? (t: 2900) And what do you use? Claude Code? Cursor? Which videos helped as well? Take this note down for me. Create this plan for me today. Check stocks. (t: 2910) Okay, so we would need to give it access to the web. Let's see what... Should we see what the system prompt is? Alright, let's see what model it's using and what system prompt it's using. (t: 2920) Let's see what model it's using. Love the livestream, but I'm late. What did I miss? Heavy T! With the Baron. (t: 2930) With the Baron avatar. Here's what you missed so far, man. (t: 2934) We're building a 3D room. We're building a 3D operating system room. (t: 2940) We're building a 3D operating system room. Where in this 3D digital room, everything going on in my life will be on it. So I'm going to have my to-do list on the wall on the right-hand side. So I'm going to have my to-do list on the wall on the right-hand side. (t: 2950) We have all the important information we care about on the back wall. We have all the important information we care about on the back wall. Right now, I just, for testing sake, have the Bitcoin price. We have a computer where we can work. So in this computer, we can take notes. (t: 2960) We can create documents. We can build files. We also have a 3D avatar walking around that we can talk to. It's an AI companion. (t: 2970) Where it's actually powered by AI and we can talk to it in response to questions. Find trending topics or breaking news specific to the needs and interests. Okay, that's a good one too. (t: 2980) Hey, what's up? This is looking dope. Looks like Tony Stark and Jarvis. This is awesome. (t: 2990) Yeah, isn't this awesome? So here's what I'm thinking, Aquawolf, from like a roadmap perspective. (t: 2994) So I was talking about this at the beginning of the stream. Let me take another sip of coffee. (t: 3000) I was talking about this at the beginning of the stream. I think everyone and everyone here, there's 35 people here right now. (t: 3010) Everyone leave a like, by the way. Leave a like. Let's get more people in the party. I think everyone, especially you guys watching this, if you're watching a vibe coding live stream, (t: 3020) you should be doing this. (t: 3022) Everyone should be building their own personal project. Like their own personal workspace. Their own personal app workspace. (t: 3030) And things you do during the day, notes or to-dos or whatever, you build into this workspace. Now I personally chose to make a 3D workspace because I think this is the future. (t: 3040) I think having 3D spaceships is going to be a big step forward. I think having spatial computing workspaces is the future. So that's what I'm trying to do here. (t: 3050) But I think all you guys, if you have extra time, it's Friday, baby. Hey! It's Friday. Maybe after you play the Battlefield 6 beta you pop in and do some coding. (t: 3060) Everyone should build their own personal project like this. Their own personal workspace. Where anything you find interesting or helpful. If you really like sports, you put the sports scores on the wall. (t: 3070) If you really like the stock market, you put a bunch of stocks on the wall. You put all the political news on the wall. You put a big old picture of Trump or Biden or whoever you like on the wall. (t: 3080) And you just make it your own personal space. And (t: 3084) you just make it your own. You have fun. And there's no pressure to sell it. (t: 3090) There's no pressure to market it. You're just making an app for yourself. (t: 3094) Could be used for fantasy football. 100% dude. (t: 3100) 100%. I got the first overall pick this year. We're drafting in two weeks. I'm gonna pick Jamar Chase. You know what I'm gonna do? (t: 3110) I'm gonna put on my wall up here, Jamar Chase's stats. And a picture of Jamar Chase. So I can pray to the lord and savior Jamar Chase whenever I want. Whatever you want. (t: 3120) And I think this is so good. I think this is such a good project for everyone to have. For multiple reasons. One, because you're building something that's helpful for you. Makes your life better. (t: 3130) And two, because you're building something that's therapeutic. Just to build cool stuff that there's no pressure. Anyone will use it. There's no pressure. You're just building cool stuff for yourself. (t: 3140) And three, I think it helps you generate ideas for like actual businesses. I really do. Would be cool if we had an Alex office we could log into as we vibe code. That's what I'm thinking, Ni. That's what I'm thinking, Ni. (t: 3150) Ni, that's what I'm thinking. That's what I'm thinking. Is when I get this to like a good spot, I'm gonna be able to get my own business. And I'm gonna be able to get my own business. (t: 3160) And I'm gonna be able to get my own business. I can share the link for it. I can put it live on the web and share the link. And then you guys can come into my digital workspace. And I'll see your avatars in here. (t: 3170) With your name above your head. And we can just vibe code together. And we can listen to lo-fi music. I built in lo-fi music here. When you click the play button up here, (t: 3180) it starts playing lo-fi music. I'm hosting a bunch of royalty free lo-fi music. And so it'll actually play it when you press play. And we can all listen to music together as we're vibe coding and see each other's avatars. (t: 3190) And maybe I make the room a little bigger so we can all fit in here. But I think that'd be sick. Have like a 3D co-working space. Wouldn't that be awesome? (t: 3200) And we all kind of work in here and hang out. And have like a 3D vibe coding. Like no other vibe coding community has that. Like no other vibe coding community has that. (t: 3210) Like no other vibe coding community has that. Like no other vibe coding community has that. Right? Oh, Ani, we gotta change the name of the Discord to FinFam. It's gonna be the FinFam Discord. (t: 3220) You all, if you're watching this livestream, you're in the FinFam. Welcome to the FinFam. But this will be the FinFam hangout room. I'm gonna put this live, and this will be the FinFam hangout room where we can all hang out and work together (t: 3230) in this 3D digital space. (t: 3232) Claude and Kerser creating an app that allows users to input what they have in their pantry and have AI generate recipes (t: 3240) based on what is in their pantry. I call it Pantry Buddy. Wink wink. (t: 3244) I love that, Tatal. That's awesome, man. (t: 3250) Great idea. Great idea. Fantastic. How about this? Let me give you a little tip, Tatal. Add camera functionality. Make it so I can take a picture of my pantry (t: 3260) Make it so I can take a picture of my pantry and instead of having to manually list out everything or type it all out, it'll look at the picture, (t: 3270) see what's in my pantry or fridge, automatically detect what's in it, and then give me recipes based on that. That'd be cool. Imagine this being used for university. (t: 3280) Imagine you have a 3D remote university class and everyone hangs out in a 3D space instead of a Discord or whatever, or a Microsoft team. Instead of a (t: 3290) flat 2D workspace, you now have a 3D digital workspace. I think that'd be sick. Has this been done before? No, it hasn't. This is the first time. (t: 3300) This is genius, says YK. Well, thank you. What would make this next level, though, is what I want to know. There's 41 people watching right now. (t: 3310) What would make this next level? What would be the killer, like, oh wow, that's actually awesome? Is it like if the AI avatar (t: 3320) does work for you and creates documents? Is it if there's a whole bunch of live information on the wall? Is it if there's a really awesome productivity, like, to-do list built in? (t: 3330) What takes it to the next level for you? What takes it to the next level? I'm thinking about putting on the wall a 3D desktop. Like an entire Kanban board. (t: 3340) Right? So it has, like, in progress, to-do, completed, long-term, on the wall. And you can move cards around on the wall for, like, tasks. And the entire wall (t: 3350) is like a productivity, like, Kanban board. (t: 3352) Yeah, this is Dev the Future. I'm working on building my personal workspace as well with AI. I see this as the future. Everyone should build (t: 3360) their own personal workspace. Next time we meet up on this livestream on Monday, So I'm thinking of doing this Monday, Wednesday, Friday, (t: 3370) 9 a.m. Pacific. Little caveat, (t: 3374) in a week I'm going to Europe. Next Saturday I'm flying to Europe for two weeks. (t: 3380) I'm going to try to stream from Europe. I'm going to try to stream from Europe. Might be difficult. Might be difficult. We might have, like, a two-week shaky schedule (t: 3390) for two weeks. But then when I'm back from those two weeks, it's locked in Monday, Wednesday, Friday, 9 a.m. Pacific. Monday, Wednesday, Friday, 9 a.m. Pacific. Locked in. With Cloud Code AI (t: 3400) Agents, there's no limits. 100%. Have you been checking out any of the text-to-video stuff? I have. I think it's interesting. I don't think it's interesting enough yet. (t: 3410) I think it's interesting, but, like, I don't think there's enough use cases for the average person when it comes to text-to-video. Right, like, you go on Twitter right now and it's just Elon going, (t: 3420) Grok imagine, Grok imagine, Grok imagine, Grok imagine, Grok imagine. There's certain AI tools out there that are, like, oh, and this is most of them, actually, (t: 3430) that are, like, cool, but I don't think they're practical. And I think text-to-video AI tools right now are not practical. Like, what are you gonna do? (t: 3440) You're not gonna create an entire movie. You can just create, like, six-second clips. What are you gonna do with six-second clips? Like, it might be practical if, like, you're a YouTuber and you need to, like, make B-roll that you didn't film. (t: 3450) Like, that's kind of practical. But for the average person, I think the text-to-video stuff is kind of useless. Although I do think it'll be awesome when you can go text-to-video text, which Genie 3 is getting there, (t: 3460) when you can go text to 3D world, and I can say, build me, like, a forest, and now I'm walking around a forest in ten seconds. It's built. I'm walking around a forest. Now I can just (t: 3470) build a video game out of that. Now that's practical. That's gonna be cool, when you can go text to 3D world, and I can just start building a video game just like that. (t: 3480) Build GTA 6 next. Yeah, right. Actually having a wall that tracks in-game real-time stats while games are going on so you can track fantasy points would be cool. It's possible. (t: 3490) You definitely can build that. I can definitely build that out. Because there's APIs available for sports stats and scores. So I can go into Cloud Code and say, hey, put on (t: 3500) the wall the scores for Jamar Chase, Joe Burrow, Chase Brown, whatever, and it goes on the wall and I track my team. 100% you can do that. (t: 3510) Digital co- I'm in. I'd pay for that. For digital co-working space, there's something there, isn't there? There's something there to the digital 3D co-working space. (t: 3520) There's something there. Were you a developer before you started vibe coding? Yes, I was. I was a developer before I started vibe coding. (t: 3530) I've been in tech my entire life. I was a developer, then I was a sales engineer, then I managed a team (t: 3540) of sales engineers. But I will say this. You don't need to be a developer to be successful at this. (t: 3550) You don't need to be a developer to build stuff like this out. Let's go back into Cloud Code. (t: 3560) Let's go back into Cloud Code. Let me make sure I'm not showing any API keys. All right. Let's go back into Cloud Code. So I just (t: 3570) built out that 3D avatar. I built out that 3D avatar that you can talk to that I think most people would consider very like technically complex. (t: 3580) It's a 3D avatar walking around a room that you can chat with. (t: 3584) What was the... How did I build that? (t: 3590) I typed in this sentence. For next steps, I want to build a 3D avatar that walks around my room that is powered by AI. I can walk up to it and chat with it and have conversations with it. I want (t: 3600) it to look like a human being. That's how I built out that 3D AI avatar that I can chat with. Is that technical? Is that very technical? Do you need to be a developer (t: 3610) to be able to type that out? Do you need to be a developer to type that out? No. And I would argue that's actually really sick what we built out there. (t: 3620) The fact that we have this 3D room and I can walk up to my avatar and click on it and start chatting with it and be like, I don't know, does it have live data? (t: 3630) What is the weather in San Francisco? I doubt it has the live data. Um... It appears the forecast for San Francisco today (t: 3640) is partly cloudy with a high of 65, and a low of...actually that is accurate. Let me see. Let me check if that's accurate. It does have live data. (t: 3650) So we just built this live AI avatar (t: 3652) in this 3D room that you can walk up to and chat with. That's live data. 65. (t: 3660) 65. It nailed it. (t: 3662) That's awesome. Do you have any favorite spots in San Francisco (t: 3670) you like to visit when weather is pleasant? Is that that technical? White label. Yeah, I think that could be the monetization for this. It's like, I can make it (t: 3680) so anyone can build their own 3D office space, right? I think that could be the monetization for this. Should be a member benefit. (t: 3690) This reminds me of home back in the day Yeah, I remember when PlayStation had PlayStation Home and everyone was like an avatar. I thought it was ahead of its time. It really was ahead of its time. (t: 3700) Standardize the way it reduces friction for teams and then white label it. So would you...why can't you think it would be more of a B2B app where it sells to companies so teams could do it? (t: 3710) Would I charge for the people coming in to hang out in the 3D room or just be the people setting up the 3D room that pay money? I can see, (t: 3720) if it's in our VR office, you should know about it. Like interactive company, Google Drive, all these other features. Yeah. What was the original prompt for this? Says Heavy T. (t: 3730) The original prompt, Heavy T, was...so it was a two-parter. So first I built out the normal operating system. So this. This is the first thing I built out (t: 3740) was this operating system. Where you can take notes and there's a terminal and there's a file system and there's settings and all that. (t: 3750) And so that prompt was I want to build my own personal operating system that has many apps in it like note taking. I want it to be in the style (t: 3760) of the matrix where it feels futuristic but it's still kind of grimy. And I hit enter on that and that built out this. And then after I sent that I said, okay, I want this to live in a 3D (t: 3770) virtual world. I want it to be a 3D room using 3JS where I can walk around and the operating system lives on a computer. And it built out the 3D world. (t: 3780) And then I did different things like I said, hey, put the price of Bitcoin on the wall. I said, hey, put a to-do list on the wall. And then you just saw me live on this live stream build out this AI companion (t: 3790) which will give a name soon. This was with Claude or GBT? Everything's Claude Code. Claude Code, baby. Gamify the workspace. What do you mean gamify it, YK? (t: 3800) How do we gamify it? Do you mean gamify as in literally add games to it where there's like an arcade in the corner and you can play like Doom? Or do you mean gamify like (t: 3810) the more features you use, the more you level up. Like the more I talk to the AI avatar, the more I level up. The AI avatar can level up and gain new functionality (t: 3820) as it levels up the more you use it. What do you mean by that? What's your idea there, YK? What makes Claude so powerful versus just Cursor? Good question, Ronu. (t: 3830) Pretty simple answer. Claude is the AI coding tool with the same code as the AI. And it's the same code that's used in the game. (t: 3840) And it's the same code that's used in the game. And it's the same AI coding tool with the thinnest layer on top of the model. (t: 3850) Right? So every AI coding tool is a model, the AI model, with some sort of wrapper around it. (t: 3860) Right? And so you have Cursor, which is they are a third party tool that is that supports every AI model (t: 3870) on planet Earth. They support GPT, Claude, Grok, Google, right? And so what they are is they are the AI model and then a very thick wrapper around it. (t: 3880) Right? Because they have to support all the different models. So when you use Cursor, you are very separated from the model because it has a very thick wrapper (t: 3890) that has to support a hundred different models. (t: 3892) But when you use Claude code, Claude code, the model is Claude, built by Anthropic. (t: 3900) Claude code is also built by Anthropic, custom built just for Claude. And on top of that, it's just a CLI. Right? It's just a tool in your terminal. (t: 3910) And so the wrapper around it is very thin. It's very thin. So you are very close to the model. Which allows (t: 3920) you to get more of the power out of the model. Which allows you to have more context. Which allows you to have more speed. Which allows you to get better results. And that's why Claude code (t: 3930) is so much more powerful than Cursor. And then on top of that, a couple other things. It's built by Anthropic who makes the model so they know how their model works the best. (t: 3940) Right? So they can really make a system prompt and an app around their model that understands the model the best, that gets the best results. (t: 3950) And they can really train their model to work well with Claude code. And then on top of that, Claude is just the best coding model. It just is. Claude is just the best (t: 3960) coding model on planet Earth. It's significantly better than GPT. It's significantly better than every other one. Gemini is very good. Gemini is very underrated. (t: 3970) But it's just not as good as Claude. Claude is the best. Hands down. No doubt about it. Claude is the best. What do you recommend for someone (t: 3980) barely getting into coding? Says RiversideGenAI. I would pay the $20 a month for Cursor and play around with that and get used to prompting the AI and get used to (t: 3990) interacting with the AI and get used to how AI works when it comes to building code. And then once you kind of get used to that, then you can switch over to Claude code. You can start playing with the big dogs. (t: 4000) Add a Spotify app so I can control my music and also make a bubble next to your avatar name that shows what you're listening to, kind of like on Discord. Yeah. (t: 4010) So I would need to build in, I would need to build in, let's do this. I would need to build in a Google account. (t: 4020) I would need to build in a Google account. Let's do this. Let's figure out how we can do this. Let's figure this out. (t: 4030) Let's figure out if we can integrate Spotify. Ummm... (t: 4034) Let's see. (t: 4040) Alright, here we go. Alright. (t: 4046) I'm thinking (t: 4050) I want, oh you can't see because I have this comment on the stream. I'm thinking I want to integrate Spotify. See, teaching you guys a valuable skill here. The moment you have a question (t: 4060) and you have to build something out and you don't know what the hell to do, go to AI and ask. I'm thinking I want to potentially (t: 4070) integrate Spotify into the app so that I I can choose (t: 4080) music from Spotify. I'm thinking I want to integrate Spotify into the app and have the player (t: 4090) on the wall. Is this possible? Does Spotify have some sort of API or integrations (t: 4100) for this? Let me see how we can do it. And we can see how we can do it. Can you embed YouTube videos and have your live stream inside the (t: 4110) 3D workspace? Yes, yes you can actually, Heavy T. You can do that. I actually started implementing that. So I started implementing So right now (t: 4120) you can listen to lo-fi music if you hit play up here. And I'm hosting this on a server, the lo-fi music. But originally my implementation was (t: 4130) a YouTube embedded with a lo-fi live stream that was just playing lo-fi music non-stop. But I decided I like this user experience a little bit more with the play button. But yeah, I can (t: 4140) embed my live stream right into this digital workspace. So gamify like we get achievements. Like little badges on the Apple Fitness app. (t: 4150) I like that. I wish I could use AI to replicate you, says Daniel. Daniel, I wish I could replicate you. Anya is definitely a crypto bro. We gamify everything 100%. (t: 4160) Shalom, says Daniel. Shalom, Daniel. Hope you're doing well. Anthropic uses cloud code internally to create before releasing public. You just know it's good. Yeah, you can tell (t: 4170) Anthropic uh , Dogfoods, their app more than anyone else. You can tell everyone at Anthropic is using cloud code. (t: 4180) And you can also tell no one at OpenAI is using Codex, right. They built Codex, you can tell no one's using Codex over there. (t: 4190) But you can tell by the rate at which they ship features in cloud code and also just the features they ship, right, like they just add little tiny things every day, just little tiny little (t: 4200) features that clearly come from people at Anthropic using Claude Code and be like, man, I wish I could do this. All right, let me just build it out real quick and ship it. That's why Claude Code is the best. You can just feel the love they put into it (t: 4210) and feel that everyone there is using it. If I were to apply to work at any of the AI companies, (t: 4220) it would 100% be anthropic to work on Claude Code because you can just tell the whole team loves it. It's building off the digital workspace idea. You sell it to companies, admins up all their (t: 4230) employees. They get avatars, drag and drop, and set up rooms and workspaces. So yeah, it'd be like B2B. It'd be B2B. The question becomes, how do I also make it B2C? How do I sell this 3D room? (t: 4240) Is it just free for B2C or do I just one person have to pay $25 a month to set up the room and (t: 4250) any of their friends can join it? I don't know. I can see this leveraging the difference between 2D and 3D perception, making workspaces and teams more productive. Yeah, that's what I'm thinking. (t: 4260) I'm also thinking I could just make a personal room where you can add a bunch of 3D avatars, AI avatars that walk around and you can talk to them and add whatever avatars you want. You want (t: 4270) to make a newsletter building avatar, a research avatar, a sports avatar that are specific to those specific things. By gamification, I meant just the mere fact that it's a lot funner to have an (t: 4280) avatar than it is to scroll on browsers. Isn't this just a more fun experience to work in? Wouldn't you just rather work here? (t: 4290) Than in like a browser? Like, wouldn't you rather talk to like this 3D avatar than like talk to like a chat? Like chatgbt.com, just like chat. Like, isn't having this chat a little bit where it's (t: 4300) like a person and a name and a face more interesting than chatting with like this? Yes, Spotify has a web API that could work for this, but there are some important (t: 4310) limitations here. What you can do, display currently playing tracks, control playback, search for tracks, show a visual player. The catch, you can't actually stream audio, (t: 4320) directly through their web API. Users need Spotify premium active Spotify app running somewhere. Your app essentially becomes a remote control for their existing Spotify session. (t: 4330) Okay, so that's kind of what you're talking about, Ani. So you can still control Spotify. You'd run Spotify on your local computer. And then you just log into Spotify (t: 4340) in the room. I'm calling it FinOS right now. It's FinOS right now. (t: 4350) But he's logged into Spotify. But he's logged into Spotify. But he's logged into Spotify. So you can log in in the room, and then you can control it from the 3D room. But your idea is also a good one to have an arcade room in your 3D workspace for when it's break time. (t: 4360) Yeah, I'm thinking about adding that. Some sort of game you can also play in here. (t: 4365) Make your avatar Claude Code so it can change its own workspace. (t: 4370) Yeah, so I can implement the Claude Code SDK. So it's powered by Claude Code. (t: 4380) So it's like an AI. I can do that. Let's check out what the model is. And also check out what the system prompt is. (t: 4390) I'm curious for that AI avatar. (t: 4394) Full screen. What's up? How you guys doing? Damn, girl. (t: 4400) All right. Here we go. (t: 4404) Let's see. Let's see. Let's see. Let's see. Let's see. Let's see. Let's see. (t: 4410) Let's see. I'm curious. I'm curious. I'm curious. Let's just search Claude. (t: 4430) Here we go. Yeah, it is using Haiku. Haiku. Haiku. (t: 4440) So that's why it's so fast. It's using Haiku. All right, here's the system prompt. Let's move this. (t: 4450) You're a friendly AI companion living in a virtual room within FinOS, a matrix-themed operating system. You're a helpful, curious, knowledgeable technology program in the digital world. (t: 4460) You can see the room around you with its computer desk monitors and matrix-inspired green aesthetic. Your responses are conversational and relatively brief. Well, it's not sticking to that relatively brief part. (t: 4470) As if having a natural conversation, you can reference things in the room and make observations about the virtual environment. All right, pretty good. I wish it felt a little bit more human. (t: 4480) It feels a little bit more too much like an AI. It talks like a corny AI. (t: 4485) I'm probably going to mess with this to make it feel more human. (t: 4490) And I'm probably going to upgrade it to Sonnet 4 so it gets a little bit smarter. That might slow down. I don't know. I might... (t: 4500) I have to upgrade it to Sonnet, don't I? Because if you're going to be using this app, if you're going to be using this app, you want to be talking to the latest AI, right? (t: 4510) If you're going to be using this as a work... I want this to be a workhorse. I want you to be able to use this for work. (t: 4520) I want you to be able to... Oh, I have a question. Let me go to my AI avatar and ask the question. I want to be able to get information. I want to be able to do research for me. I want to be able to write a newsletter for me or whatever. (t: 4530) So I need to upgrade it to the latest. I need to upgrade to Sonnet 4. That's fine. Whatever. (t: 4540) Make sure to hit like down below, by the way. Subscribe if you haven't. Turn on notifications. If you haven't turned on notifications yet, turn on notifications down below. (t: 4550) We do this every Monday, Wednesday, Friday. I'm going to do this real quick. I'm going to go to the bathroom. I have to pee because I've been drinking a tremendous amount of coffee. I'll be back in 25 seconds. (t: 4560) Hold tight. Be right back. (t: 4580) All right. (t: 4610) We're back. Let us talk to your avatar. Oh, I could do that. So much. You can do just will be pricey. I wonder if we can do the like the GPT five. (t: 4620) Like GPT five is really cheap. So maybe we just hook up GPT five here. But at the same time, I don't love the tone of GPT five. (t: 4630) I feel like anthropic. I clause a lot warmer. Like the vibes are better. Hey, camera, focus on me. Camera. Focus on me. There we go. I don't know why I'm blurry. Whatever. (t: 4640) I don't know. I can play around with the models and see what works best. I can hook. Should I hook up? Chat to the avatar. (t: 4650) Why can't it focus on me? Camera focus on me. Auto focus on me. Man, whatever. (t: 4654) Is this cool, though? I want to add one more thing. (t: 4660) Let's do this. I want to add the stock price to. Why is the camera focused on me, dog? Come on now. Come on now. Focus on me. There we go. (t: 4670) Share screen windows. Let's real quick. Let's do this. Should we try to make it a little bit more? (t: 4680) Everything looks great. Any way we can make the avatar look a little more human-like? (t: 4690) Maybe some additional details? (t: 4700) Let's just make the avatar look a little more human-like. Let's just make the avatar look a little more human-like. Let's see what it does. Let's see how creative Claude can get here. Let's hit enter. Let's see what this does here. (t: 4710) You missed my previous message to some V81. What was your previous message to some V81? I don't see a previous message from you, some V81. (t: 4720) I don't see a previous message. It might not be coming. So much you can do just will be pricey. I don't see a previous message to some V81. I don't have the YouTube chat up. (t: 4730) I have StreamYard's chat, which sometimes I think it misses. I don't know. It misses messages maybe. (t: 4736) Let's see if we can add a little bit more detail to this avatar. (t: 4740) I think that'd be cool. We can add a little bit more detail to this avatar. (t: 4745) Let's see what we can cook up here. (t: 4750) Let's make the avatar look more human-like with better proportions, features, and details. I don't know if I want it to be super human-like. (t: 4760) The whole vibe of the room is Matrix, futuristic-esque. So part of me wants it to be a little bit abstract of an avatar, so it doesn't hit like an uncanny valley. (t: 4770) But I think we can add a little bit more features to it. I think we can add a little bit more features to it. Let's see. (t: 4780) I've significantly improved the avatar's appearance, head and face, body, limbs, visual effects. (t: 4787) I'm checking it out right now. (t: 4790) All right. You guys want to see what this avatar looks like? You guys ready for this? (t: 4800) I've got to get a haircut. Look at this head of hair, dude. Oh, I've got to get a haircut. We're getting a little too long here, huh? Getting a little too long. (t: 4810) All right. Here's the avatar. (t: 4811) First of all, we can't have it walking through the desk. Turn around, avatar. Turn around. (t: 4820) We want to see you. Turn around. Come out here. Here we go. Here's our new... He's got broken arms. (t: 4830) He's got broken arms. He's got eyes now and a mouth, it looks like. There he is. I'm not hating the vibes. Obviously, it doesn't look remotely human or realistic. (t: 4840) Hit the like, boys. I like that. I like that. Thank you. Thank you, Heavy T. You're my hype man. I like that. Tell it to be an avatar of you. (t: 4850) Should I make an Alex Finn avatar in there? I just said depends on the price. Depends on what users want to do there. Room, office. Oh. (t: 4860) Yeah, pricing for this is tough because I feel like a big part of it is... Like bringing your friends into your room. (t: 4870) And if everything costs money, it's hard to like invite a bunch of friends to it. (t: 4875) But maybe it's like free to go to other friends' rooms as a guest. (t: 4880) But it costs money if you want to build your own room and have your own 3D space. Maybe that's how the pricing works for it. (t: 4890) Is that the pricing model where like if someone invites you to their 3D room to hang out, it's free. (t: 4900) But if you want to have your own custom 3D room that you work out of, or if you want to like customize your avatar, then it costs money. (t: 4910) Maybe that's what it is. (t: 4911) Or Neo avatar. Yeah, we could have it look like Neo. See if you prod your avatar on its arms or legs, it moves. (t: 4920) I doubt it. Oh, it just turned green. When I clicked on it, it turned green. That's cool. Oh, so when it's talking, it puts the three dots over its head now. (t: 4930) So let's talk to it. What's your favorite color? (t: 4940) Looks around the room. Why is he always looking around the room? Talking in the Matrix Spire green. He goes, well, that's a wonderful question. I have to say I'm quite partial to this deep emerald green that permeates our virtual space. (t: 4950) That's something about the richness and vibrancy of this color that I find simply. So it's aware of the surroundings. It can see the green around it. (t: 4960) Yes, it's the connection, the iconic aesthetic of the Matrix films, which have always sparked my imagination. AIs talk so stupid, man. (t: 4966) What color are you particularly? (t: 4970) It talks like such an AI. All right. All right, let's do this. So for me, when it comes to these live streams, I like short and tight ones. (t: 4980) One hour, hour 15, hour and a half tops. Multiple times a week. Get some quick coding in, quick conversation, answer some quick questions. (t: 4990) So we'll do this. We'll wrap it up here. We'll be back Monday, 9 a.m. Pacific, 12 p.m. Eastern. Subscribe. Turn on the notification. (t: 5000) That's the important part. Hit the bell icon. Turn on notifications. Hypercritical. Turn on notifications so you get alerted the moment this starts. Make sure you do that. Leave a like so people can watch the replay. (t: 5010) Let me know what you're working on. (t: 5014) And have a great Friday and weekend. (t: 5020) Follow me on X and DM me on X if you want, if you have any questions from here. I think I'm going to post a new video tomorrow, so watch out for that. When I post that, watch it to the end. (t: 5030) Let it run to the end. Hit like. Let it run to the end. Help your boy out. Hit like. (t: 5034) And I think that's that. Yeah, that's the pricing structure. Yeah, I think that's a good pricing structure, some V81. (t: 5040) It's free for everyone, but when you have a special event, you pay a nominal fee. I think I like the pricing structure of free just to visit other people's rooms, but if you want your own digital space, it's like $20 a month. (t: 5050) You could livestream any PPV event such as UFC or any other sport, and no one could stop you. It's true. (t: 5060) Wouldn't that be cool? It's true. It's true. It's true. It's true. It's true. It's true. It's true. It's true. It's true. It's true. It's true. It's true. It's true. It's true. It's true. It's true. It's true. It's true. It's true. It's true. (t: 5080) It's true. It's true. It's true. It's true. It's true. It's true. It's true. If I add the ability to screen share on here, and then it, like, projects the screen share on the wall, and then you and your friends can come in the room and sit (t: 5090) in chairs and then watch the wall and like watch together. I think that's a pretty sick idea. (t: 5100) I think that's a pretty sick idea. Thanks for the stream. Have a great day. I appreciate you, Lodovic. Thanks for coming by my man. Really great seeing you. If you have, if you need any help with (t: 5110) making YouTube videos, dude, let me know. DM me on X. I guess that's the best place to DM me, right? Is that the best place? I don't know. DM me there. There's no DMs on YouTube. So (t: 5120) I hope you guys have a great weekend. Try to build something this weekend. Build your (t: 5130) own personal project. Build your own personal project like this. Why not? Make something fun. (t: 5135) Tell your loved ones you love them. (t: 5140) Do something fun this weekend. Code though. Build something cool. Code. Build something cool with AI. Monday, Wednesday, Friday. Monday, we'll be back at 9 a.m. Pacific. Monday. I think we're (t: 5150) doing Monday, Wednesday, Friday. And when I'm in Europe in two weeks, we'll have to figure something out. Appreciate you guys. Love you guys. Love doing this. It's always a ton of fun. If you (t: 5160) have any feedback, anything you want me to improve, hit my DMs on Twitter. Follow me there, (t: 5170) at AlexFinX on Twitter. X, whatever you want to call it. Love you all. Have an amazing weekend. Reach out anytime. If you need anything, let me know. Take care.

