---
title: Why Tiny Experiments Might Be the Key to Sustainable Success
artist: Duct Tape Marketing
date: 2025-05-15
url: https://www.youtube.com/watch?v=HBsaJO1kthk
---

- [00:00:00](https://www.youtube.com/watch?v=HBsaJO1kthk&t=0) - The big difference between a goal and an experiment is that when you pursue a goal, you have a very binary definition of success. This is what I want to achieve, and either I get there, and this is success, or I don't, and this is failure. When you conduct a tiny experiment, you're a bit more like a scientist, and you start from more of a hypothesis, a research question, something that you're curious about. And that means that whatever the outcome, as long as you learn something, that is success.

- [00:00:30](https://www.youtube.com/watch?v=HBsaJO1kthk&t=30) - curious about. And that means that whatever the outcome, as long as you learn something, that is success. So welcome to the show. Thank you so much for having me, <PERSON>. I didn't even butcher your name, did I?

- [00:01:00](https://www.youtube.com/watch?v=HBsaJO1kthk&t=60) - So welcome to the show. Thank you so much for having me, <PERSON>. I didn't even butcher your name, did I? Actually, it was pretty good. My mother's maiden name is <PERSON>'<PERSON>essandre, so I had a little bit of that. Very French. So did you wake up one day and say, you know, I'm tired of this Google global marketing lead. I'm just going to go get a PhD in neuroscience. I wish it was. It was that simple, but it wasn't. I actually really enjoyed my work at Google. I love the projects we were working on. I love my team.

- [00:01:30](https://www.youtube.com/watch?v=HBsaJO1kthk&t=90) - I'm just going to go get a PhD in neuroscience. I wish it was. It was that simple, but it wasn't. I actually really enjoyed my work at Google. I love the projects we were working on. I love my team. Very, very smart people. But I realized after a while that I knew exactly where I was going. I knew exactly what success was supposed to look like. And especially at a place like Google, where, you know, it was a company that was founded by engineers. So they literally had a rubric telling you, if you do A, B, C, this is how you do it. This is how you get promoted. This is how you become successful. It was too early in my career for me to have such clarity and certainty in terms of where I was going.

- [00:02:00](https://www.youtube.com/watch?v=HBsaJO1kthk&t=120) - So they literally had a rubric telling you, if you do A, B, C, this is how you do it. This is how you get promoted. This is how you become successful. It was too early in my career for me to have such clarity and certainty in terms of where I was going. And so this is really what encouraged me, make me think about leaving. And I worked on a startup, which didn't work out. And it's only after that startup didn't work out that I decided to go back to university and study something I had always been fascinated with, which is the brain. And I realized that there was no room orfleet left anywhere in the world for me to go back. It was an crit point I had always had as a kid in the IEP.

- [00:02:30](https://www.youtube.com/watch?v=HBsaJO1kthk&t=150) - And it's only after that startup didn't work out that I decided to go back to university and study something I had always been fascinated with, which is the brain. And I realized that there was no room orfleet left anywhere in the world for me to go back. It was an crit point I had always had as a kid in the IEP. I studied itung Portugverb , a language teacher of an institution I graduated with but I don't think I'll ever be able to talk in a navy world again, where I don't have a car, where I don't need a car, a car to write to, what I want for the rest of my life, where I don't have the Пом tag, in my arm where I basically, you know, I basically started questioning, you know, when you have a health scare that is so bad. Can I go back to this? Because I'm like, and for me, that trigger

- [00:03:00](https://www.youtube.com/watch?v=HBsaJO1kthk&t=180) - what I want for the rest of my life, where I don't have the Пом tag, in my arm where I basically, you know, I basically started questioning, you know, when you have a health scare that is so bad. Can I go back to this? Because I'm like, and for me, that trigger came in the form of a blood clot. So what happened is that one morning I was brushing my teeth, ready to go to work. And I noticed that my arm had turned purple. And so I went to the Google infirmary because they have that on campus. And they sent me to the hospital. They said, you need to go and see a doctor straight away. And the doctors told me, we need to perform surgery as quickly as possible. There's a blood clot that is threatening to travel to your lungs.

- [00:03:30](https://www.youtube.com/watch?v=HBsaJO1kthk&t=210) - infirmary because they have that on campus. And they sent me to the hospital. They said, you need to go and see a doctor straight away. And the doctors told me, we need to perform surgery as quickly as possible. There's a blood clot that is threatening to travel to your lungs. And so when can we do the surgery? And this is when my response was, instead of saying, yes, as soon as possible, I said, let me check my calendar. And the reason why I did that was because I wanted to make sure that I would schedule a blood clot. And I wanted to make the surgery at a time that would not disrupt any of the product launches and projects that I was working on. And this is when I started questioning my sense of priority in life. You talk about a goal-obsessed world. I mean, it's in the subtitle. I mean, I'm assuming you're not anti-goal. It's really just the way that people have been taught

- [00:04:00](https://www.youtube.com/watch?v=HBsaJO1kthk&t=240) - the surgery at a time that would not disrupt any of the product launches and projects that I was working on. And this is when I started questioning my sense of priority in life. You talk about a goal-obsessed world. I mean, it's in the subtitle. I mean, I'm assuming you're not anti-goal. It's really just the way that people have been taught to use them. Yeah, absolutely. Goals can be, and the way we normally use them can actually be very helpful when you know exactly where you're going. So they give you a destination and then you have a care plan. A clear vision. And then you just need to work hard and you'll get there eventually. The problem is we apply this linear definition of success to almost everything, even when there is high uncertainty. And so what I encourage people to do is to stop using goals for absolutely

- [00:04:30](https://www.youtube.com/watch?v=HBsaJO1kthk&t=270) - A clear vision. And then you just need to work hard and you'll get there eventually. The problem is we apply this linear definition of success to almost everything, even when there is high uncertainty. And so what I encourage people to do is to stop using goals for absolutely everything. But whenever they are in a situation where they're facing a challenge, where the outcomes are unclear, where the parameters are uncertain, to instead design tiny, expensive goals that are not going to be successful. And that's when you need to start experimenting. So at Google, were you, and I may have this wrong, but I know I saw something about the Fit project at Google. Is that the Fitbit and the health aspect?

- [00:05:00](https://www.youtube.com/watch?v=HBsaJO1kthk&t=300) - So at Google, were you, and I may have this wrong, but I know I saw something about the Fit project at Google. Is that the Fitbit and the health aspect? Yes. So that's obviously where a lot of people set goals, right? Actually, that's, yeah, that's actually very interesting because we have found, and there's a lot of research showing that when you set goals, when it comes to your health and you announce them to other people, you're not going to be able to do it. You're not going to be able to do it. You're not going to be able to do it. You're not going to be able to do it. You're not going to be able to do it. You're not going to be able to do it. You're not going to be able to do it. You're not going to be able to do it. You're actually less likely to compete them because you get a little bit of that dopamine hit from just telling people, I'm going to do this. I'm going to reach that weight. I'm going to run this marathon. So instead, what's really important is to design a process where you keep on showing up

- [00:05:30](https://www.youtube.com/watch?v=HBsaJO1kthk&t=330) - You're actually less likely to compete them because you get a little bit of that dopamine hit from just telling people, I'm going to do this. I'm going to reach that weight. I'm going to run this marathon. So instead, what's really important is to design a process where you keep on showing up and you build routines and habits and rituals around those aspirations that you have around your health, instead of having that linear goal, which you announce to people. So there is actually a lot of research that's going on around health. And I think that's a really good way to start a little bit of overlap in terms of the science that I use in the book in Tiny Experiments and the science that I was using when I was working at Google on digital health. You know, that's interesting because there's a lot of advice out there. I've read people say,

- [00:06:00](https://www.youtube.com/watch?v=HBsaJO1kthk&t=360) - a little bit of overlap in terms of the science that I use in the book in Tiny Experiments and the science that I was using when I was working at Google on digital health. You know, that's interesting because there's a lot of advice out there. I've read people say, set a goal and then tell a couple people because that'll hold you accountable. But you're saying that's actually kind of counter to actually meeting the goal. Well, just look at the number of people who announced their New Year resolutions in January. And now look at the number of people who actually maintain those resolutions and actually complete those goals. And we know that this is a very, very low number. So this is just a really good example. But in general, the research shows that there is a big difference between having an accountability group

- [00:06:30](https://www.youtube.com/watch?v=HBsaJO1kthk&t=390) - number of people who actually maintain those resolutions and actually complete those goals. And we know that this is a very, very low number. So this is just a really good example. But in general, the research shows that there is a big difference between having an accountability group where you say, for example, we are going to meet every week to run together, or we're going to meet every week to write together versus announcing the goal of I'm going to run a marathon or I'm going to write a book. So one of them is... Process-oriented, where you have this process that you do with other people and you have this motivation and accountability as part of a group. The other one, again, just gives you that dopamine hit of feeling proud that you're going to do the thing and then you don't do the thing.

- [00:07:00](https://www.youtube.com/watch?v=HBsaJO1kthk&t=420) - Process-oriented, where you have this process that you do with other people and you have this motivation and accountability as part of a group. The other one, again, just gives you that dopamine hit of feeling proud that you're going to do the thing and then you don't do the thing. Yeah. So let's, I mean, it's the title of the book. So we probably ought to spend a moment defining what's a tiny experiment. A tiny experiment is basically following a protocol for personal experimentation. The big difference between a goal and an experiment is that when you pursue a goal, you have a very binary definition of success. You say, this is what I want to achieve. And either I get there and this is success or I don't. And this is failure. When you conduct a tiny experiment,

- [00:07:30](https://www.youtube.com/watch?v=HBsaJO1kthk&t=450) - The big difference between a goal and an experiment is that when you pursue a goal, you have a very binary definition of success. You say, this is what I want to achieve. And either I get there and this is success or I don't. And this is failure. When you conduct a tiny experiment, you're a bit more like a scientist. Then you start not from the outcome, but from more of a hypothesis, a research question, something that you're curious about. And that means that whatever the outcome, as long as you learn something in the process, that is success. Yeah. I think that that whole idea of people setting goals and then just immediately feeling like, oh, it's too big or I can't make it. And it just really makes you, because the only measure

- [00:08:00](https://www.youtube.com/watch?v=HBsaJO1kthk&t=480) - Yeah. I think that that whole idea of people setting goals and then just immediately feeling like, oh, it's too big or I can't make it. And it just really makes you, because the only measure of success is, did I reach the end that I said I was, right? So can you give me, I don't know, common goals that people have is I want to lose 10 pounds. Can you give me, what would be an example of a tiny experiment that would maybe, you know, be a little bit more successful? Put me on that journey. Yeah, absolutely. So linear goal, I want to lose 10 pounds. Tiny experiment could be, I will meal prep every Sunday for six weeks, or I will take a daily walk for two months, or I will, and a tiny experiment is always in this format. I will

- [00:08:30](https://www.youtube.com/watch?v=HBsaJO1kthk&t=510) - Put me on that journey. Yeah, absolutely. So linear goal, I want to lose 10 pounds. Tiny experiment could be, I will meal prep every Sunday for six weeks, or I will take a daily walk for two months, or I will, and a tiny experiment is always in this format. I will action for duration, which is also the big difference with a habit because the habit doesn't have the duration. You commit to it for the rest of your life. Whereas with an experiment, you say, it's just an experiment. I'm going to do it for this duration and I'm going to see if it works or not for me. I wonder how many people go, oh, okay, yeah, I can do that. And then before they know it, they've tricked themselves into meeting their

- [00:09:00](https://www.youtube.com/watch?v=HBsaJO1kthk&t=540) - Whereas with an experiment, you say, it's just an experiment. I'm going to do it for this duration and I'm going to see if it works or not for me. I wonder how many people go, oh, okay, yeah, I can do that. And then before they know it, they've tricked themselves into meeting their goal. Yes. That's why, that's why it's very important to always pair experimentation with self-reflection. It's very important to question the why behind your experiment. Is that because you're copy pasting? Is that because you're doing it for the best? Is that because you're doing it for the best? Is that because you're copying something from other people? Is that because you've just been doing this thing in this way forever and you're not even questioning that assumption? Ideally, your tiny experiment should be around something that you want to do a little bit differently, something that you're curious about and something that might fail. That's very

- [00:09:30](https://www.youtube.com/watch?v=HBsaJO1kthk&t=570) - you've just been doing this thing in this way forever and you're not even questioning that assumption? Ideally, your tiny experiment should be around something that you want to do a little bit differently, something that you're curious about and something that might fail. That's very important. You want to start from this place of, you know what? I don't know if this is going to work out, but I'm going to find out. And if I don't get the outcome I expected, that's okay. Now I know. We're primarily talking about this in the context of personal development, but, you know, marketers have long done tiny experiments, right? I mean, we kind of were like, I don't know if this is going to work, but let's try these three things and see which one does. I

- [00:10:00](https://www.youtube.com/watch?v=HBsaJO1kthk&t=600) - that's okay. Now I know. We're primarily talking about this in the context of personal development, but, you know, marketers have long done tiny experiments, right? I mean, we kind of were like, I don't know if this is going to work, but let's try these three things and see which one does. I mean, do you, does the framework particularly, I'll let you explain what PACT is. Does the PACT framework really apply? Do you see it applying to business situations as well? A hundred percent. I used to work in marketing at Google. And so obviously I have lots of ideas about how that could work. And I've been doing a lot of research on that. And I've been doing a lot of work specifically even for marketing. Anything that you're curious about, even in a professional environment and that you want to try without the pressure of achieving a specific outcome can be turned into a tiny experiment. And so you could say for your social media, I will post this kind

- [00:10:30](https://www.youtube.com/watch?v=HBsaJO1kthk&t=630) - work specifically even for marketing. Anything that you're curious about, even in a professional environment and that you want to try without the pressure of achieving a specific outcome can be turned into a tiny experiment. And so you could say for your social media, I will post this kind of format for six weeks every day. And I will, very important, I will, I will withhold judgment until I'm done. That's the very important part too. I'm just collecting data. I'm giving it a try. You could say, I will write a weekly newsletter for the next six weeks. You could say, I will film a very short video reel every week for the next two months. Or that could be also internal experiments. If you work with a team and you want to encourage shared learning and

- [00:11:00](https://www.youtube.com/watch?v=HBsaJO1kthk&t=660) - a try. You could say, I will write a weekly newsletter for the next six weeks. You could say, I will film a very short video reel every week for the next two months. Or that could be also internal experiments. If you work with a team and you want to encourage shared learning and collective curiosity, I will give a short presentation about a new AI tool I discovered every two weeks for the next quarter. So always, I will, action, for, duration, and always withholding judgment. And then the little bonus, completely optional, but I highly recommend doing it. The bonus thing that you can do at the end is learning in public. So when you run tiny experiments as part of a business, instead of keeping for yourself what

- [00:11:30](https://www.youtube.com/watch?v=HBsaJO1kthk&t=690) - duration, and always withholding judgment. And then the little bonus, completely optional, but I highly recommend doing it. The bonus thing that you can do at the end is learning in public. So when you run tiny experiments as part of a business, instead of keeping for yourself what you learned, take a moment, whether that's a quick email to your team or as part of your team meeting to just share, hey, this is what I experimented with the past four weeks. And then you can do that. And then you can do that. And then you can do that. And then you can do that. And then you can do that. And then you can do that. And then you can do that. And then you can do that. And then you can do that. And then you can do that. And then you can do that. And then you can do that. And then you can six weeks. And these are the results. And sometimes you will have successful results. But also, there's a lot of value in saying, hey, everyone, don't try this. I did it. It doesn't work. And

- [00:12:00](https://www.youtube.com/watch?v=HBsaJO1kthk&t=720) - six weeks. And these are the results. And sometimes you will have successful results. But also, there's a lot of value in saying, hey, everyone, don't try this. I did it. It doesn't work. And not everybody knows. What aspect does fear, fear of failure, I guess, play in? I mean, I have encountered people over the years. I do a lot of consulting with businesses that are just getting started. And there's a lot of mindset issues and fear of failure. in that. And sometimes you almost get the sense that they don't, they're actually more afraid to succeed, you know, than really to fail because succeeding might upend what they know. So how

- [00:12:30](https://www.youtube.com/watch?v=HBsaJO1kthk&t=750) - in that. And sometimes you almost get the sense that they don't, they're actually more afraid to succeed, you know, than really to fail because succeeding might upend what they know. So how does, what have you found in your studies that role play? That's the great thing about an experimental mindset is that it completely decouples your exploration of what might work or not work from a sense of success or failure. So if you look at the way scientists relate to uncertainty, whenever they don't understand something or when things don't go as expected, they don't say like, oh, shame, I'm such a failure. I'm such a bad scientist. Why didn't

- [00:13:00](https://www.youtube.com/watch?v=HBsaJO1kthk&t=780) - uncertainty, whenever they don't understand something or when things don't go as expected, they don't say like, oh, shame, I'm such a failure. I'm such a bad scientist. Why didn't things work out the way I thought they would? No, instead they look at what happened again, without self-blame, without self-judgment, and they wonder, huh, what happened here? What can we learn from this? And this is really what's amazing about learning from that experimental mindset that scientists have is that you can almost create a little bit more distance between those outcomes, whatever they are, and your sense of self-worth. And this is what I've seen with all of the students I worked with. It really creates this distance and they

- [00:13:30](https://www.youtube.com/watch?v=HBsaJO1kthk&t=810) - that experimental mindset that scientists have is that you can almost create a little bit more distance between those outcomes, whatever they are, and your sense of self-worth. And this is what I've seen with all of the students I worked with. It really creates this distance and they can look at the data and make decisions without blaming themselves for whatever outcome they had. I've long decided. Well, nobody's really. Validated this, but I've long decided that curiosity is my super business power. And, you know, I just can't stand to not know how something works and why it works that way. And you talk a lot about curiosity-driven intelligence. You want to explain how that concept comes to play?

- [00:14:00](https://www.youtube.com/watch?v=HBsaJO1kthk&t=840) - you know, I just can't stand to not know how something works and why it works that way. And you talk a lot about curiosity-driven intelligence. You want to explain how that concept comes to play? Yes, it's basically what you just described. I love that this is what you're doing, but it is really the idea that almost any challenge can be better navigated when it's approached, from a place of curiosity. What does that look like? And I apply curiosity-driven intelligence, not only to external challenges, but also to internal challenges, emotions, mental health, any kind of thoughts that you might have that might get in the way of you making better decisions.

- [00:14:30](https://www.youtube.com/watch?v=HBsaJO1kthk&t=870) - not only to external challenges, but also to internal challenges, emotions, mental health, any kind of thoughts that you might have that might get in the way of you making better decisions. And so whenever you experience something difficult, instead of having the more traditional approach, which is how do we get rid of this thing that is uncomfortable, practicing these principles of curiosity-driven intelligence, you can apply it to external challenges, but also to internal challenges, instead of having the more traditional approach, which is how do we get rid of this thing that is uncomfortable, practicing these principles of curiosity-driven intelligence, is asking, huh, what's going on here? Being curious about it and trying to understand the why and the how of the experience, even and maybe especially so when it's uncomfortable. So a lot of using your scientific research analogy, a lot of scientists have a hypothesis

- [00:15:00](https://www.youtube.com/watch?v=HBsaJO1kthk&t=900) - is asking, huh, what's going on here? Being curious about it and trying to understand the why and the how of the experience, even and maybe especially so when it's uncomfortable. So a lot of using your scientific research analogy, a lot of scientists have a hypothesis and are completely blown away by what they actually discover. It's not even what they were looking for, right? How do we develop that mindset? Because I think a lot of scientists are not scientifically trained. We set a goal, it veers off from where we thought it was going, and we see that as a failure rather than a new path. I think just designing all of those kinds of explorations as an experiment in the first

- [00:15:30](https://www.youtube.com/watch?v=HBsaJO1kthk&t=930) - are not scientifically trained. We set a goal, it veers off from where we thought it was going, and we see that as a failure rather than a new path. I think just designing all of those kinds of explorations as an experiment in the first place makes it a lot easier than trying to reach for fit and experimental mindset on a linear goal. So if from the get-go, you're saying, this is an experiment, I'm kind of expecting to get this outcome, but also I'm very open to the possibility that I might get something completely different. This is what is going to help you really cultivate this acceptance of whatever outcome that you get. And something that can help is to experiment with other people,

- [00:16:00](https://www.youtube.com/watch?v=HBsaJO1kthk&t=960) - something completely different. This is what is going to help you really cultivate this acceptance of whatever outcome that you get. And something that can help is to experiment with other people, because there is some excitement to sharing with someone else when something happened that was very different from what you expected. You maybe designed your social media, but you didn't get a lot of the information that you expected. So you're not going to post in a certain way expecting to get this, but you got something completely different. And so if you're designing the experiment and collecting your data with someone else, that can actually be part of the fun. You can even say, okay, what's one unexpected thing that happened this week that you didn't see coming? Yeah, right. That actually needs to become ingrained as part of the process, doesn't it?

- [00:16:30](https://www.youtube.com/watch?v=HBsaJO1kthk&t=990) - the experiment and collecting your data with someone else, that can actually be part of the fun. You can even say, okay, what's one unexpected thing that happened this week that you didn't see coming? Yeah, right. That actually needs to become ingrained as part of the process, doesn't it? I mean, because I think we get very focused on, no, here's the destination. We're not supposed to veer from the destination. And I think you're right. If you get in the habit of setting up a goal, you're going to get a lot of the information that you need to get the most out of it. And so I think that's a really important part of saying, okay, what did we learn? Or why didn't this work? Or is this actually an opportunity rather than a failure? Absolutely. I often talk about how I imagine a workplace of the future where it's completely normal to walk around and ask people, so what was your latest experiment? And what did you learn? And what was your latest failure?

- [00:17:00](https://www.youtube.com/watch?v=HBsaJO1kthk&t=1020) - you're going to get a lot of the information that you need to get the most out of it. And so I think that's a really important part of saying, okay, what did we learn? Or why didn't this work? Or is this actually an opportunity rather than a failure? Absolutely. I often talk about how I imagine a workplace of the future where it's completely normal to walk around and ask people, so what was your latest experiment? And what did you learn? And what was your latest failure? So I want to bring AI into the conversation because we have to. Yeah, absolutely. Yeah, absolutely. Yeah, absolutely. Yeah, absolutely. Yeah, absolutely. Yeah, absolutely. Yeah, absolutely. Yeah, absolutely. Yeah, absolutely. Yeah, absolutely. And one of the things that I've done is I've tried to use it to challenge me. My thinking is like this. And so I'm actually trying to push it to say, what would be other opportunities? What would be other ways to look at this? What if we didn't have this? Or this was a constraint? How do you see AI actually helping us design maybe better tiny experiments?

- [00:17:30](https://www.youtube.com/watch?v=HBsaJO1kthk&t=1050) - And one of the things that I've done is I've tried to use it to challenge me. My thinking is like this. And so I'm actually trying to push it to say, what would be other opportunities? What would be other ways to look at this? What if we didn't have this? Or this was a constraint? How do you see AI actually helping us design maybe better tiny experiments? That kind of connects back to what we were just talking about in the sense that I really… Yeah. Yeah, exactly. I think we haven't really seen AI as a thinking companion. Yeah. We're not looking at AI as a thinking companion. Yeah, I think we are. I think we are. Yeah, I think we are. Yeah. Scientists are mostly working as part of a research team, where they have access to other smart people who can actually challenge their own thinking, tell them when they have blank spots, and ask them questions about the way they've been collecting their data and analyzing it. And so AI is giving you 24-hour access to that kind of brain that is going to do all of these things for you. And so it would be a shame not to use it.

- [00:18:00](https://www.youtube.com/watch?v=HBsaJO1kthk&t=1080) - Yeah. Scientists are mostly working as part of a research team, where they have access to other smart people who can actually challenge their own thinking, tell them when they have blank spots, and ask them questions about the way they've been collecting their data and analyzing it. And so AI is giving you 24-hour access to that kind of brain that is going to do all of these things for you. And so it would be a shame not to use it. Yeah, yeah, absolutely. And so I think AI is really really, really good at that. Yeah. And so I think AI is really really good at that. And so I think AI is really really good at that. use it. Yeah, absolutely. So I'm going to shine the light on you for this last question. Do you have any stories, personal stories, examples where tiny experiments kind of led to long-term change in your life? Well, I mean, my entire current life. The whole thing. The whole thing. I started

- [00:18:30](https://www.youtube.com/watch?v=HBsaJO1kthk&t=1110) - have any stories, personal stories, examples where tiny experiments kind of led to long-term change in your life? Well, I mean, my entire current life. The whole thing. The whole thing. I started my Nass Labs newsletter as an experiment and it then turned into a book. And this is why we're having this conversation today. I also use experiments for a lot of areas in my personal life. This is how I started meditating. I thought I was really bad at it, but then I said, I'm just going to do a tiny experiment. And it turns out I'm not great at it, but I'm not terrible either. And this is also how I have let go of potential paths that were not for me. So YouTube, for example, is something that a lot of people around me are doing. And I wondered for a very long time,

- [00:19:00](https://www.youtube.com/watch?v=HBsaJO1kthk&t=1140) - going to do a tiny experiment. And it turns out I'm not great at it, but I'm not terrible either. And this is also how I have let go of potential paths that were not for me. So YouTube, for example, is something that a lot of people around me are doing. And I wondered for a very long time, do I need to do YouTube? And so I conducted a tiny experiment and it turns out I don't like recording videos of myself. And so now I know, and I'm not wasting all of that time and energy thinking about how can I launch a YouTube channel and all of that. So tiny experiments have also been great for me in terms of deciding what not to do. That's such a great example too, because I do think that there's just as you, you know, I'm an author now, right? And so I need to do author things. And so you look at what everybody

- [00:19:30](https://www.youtube.com/watch?v=HBsaJO1kthk&t=1170) - and all of that. So tiny experiments have also been great for me in terms of deciding what not to do. That's such a great example too, because I do think that there's just as you, you know, I'm an author now, right? And so I need to do author things. And so you look at what everybody else is doing. I think that's a great filter really for what, you should be doing. I tell people all the time, I mean, if you don't enjoy something, you're not going to do it and you're not going to do it well. I mean, but I think what your tiny experiment does is because a lot of times people might think, oh, I'm, I'm no good on camera. I don't want to do YouTube. And so they don't do it, but then they, you know, they do a tiny experiment and find, actually, I kind of enjoy this. Exactly. That's great. Yeah, that's great. Well, again, I appreciate you taking a few moments to stop by and talk about

- [00:20:00](https://www.youtube.com/watch?v=HBsaJO1kthk&t=1200) - don't want to do YouTube. And so they don't do it, but then they, you know, they do a tiny experiment and find, actually, I kind of enjoy this. Exactly. That's great. Yeah, that's great. Well, again, I appreciate you taking a few moments to stop by and talk about tiny experiments. Is there someplace you'd invite people to connect with you and find more about your work in the book? People can go to naslabs.com to subscribe to my newsletter. And if you search tiny experiments anywhere books are sold, you can order a copy and let me know what you think. And again, appreciate you spending a few moments with us and hopefully we'll run into you one of these days out there on the road. Thank you, John.

