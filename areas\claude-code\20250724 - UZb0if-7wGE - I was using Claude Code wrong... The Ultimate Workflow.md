---
title: I was using Claude Code wrong... The Ultimate Workflow
artist: AI Jason
date: 2025-07-24
url: https://www.youtube.com/watch?v=UZb0if-7wGE
---

(t: 0) For the past few weeks, I have completely switched from Cursor to Cloud Code and learned a ton of things that made my Cloud Code super effective. And today, I will take you through how do I use (t: 10) Cloud Code, how did I bring Kiro type of spec-driven development process, as well as tips and practical examples of how I use features like hooks, custom commands, and a list of tips that made myself much (t: 20) more effective. So without further ado, let's get started. But before we dive into that, I know many of you are just getting started learning programming. And one question I got asked a lot is (t: 30) what does a roadmap look like for learning code effectively? That's why I want to introduce you to this free ebook made by Google's principal analytics lead and data scientist, where she (t: 40) wrote down all the secret tips and methodologies that she used to learn coding with Cloud and ChatGPT, especially how she gathered output to personalize the learning roadmap based on her (t: 50) specific situation. It also covered all the absolute fundamentals and basics of coding, like how to use the Cloud Code to learn code effectively, and how to use the Cloud Code to learn code effectively. So let's get started. So how do you choose the right coding language to (t: 60) start with and best practice prompt for different coding scenarios like debugging and optimizing the code, as well as a detailed roadmap of how to master a language like Python in just four months? (t: 70) There's even a custom GPT that has bacon in all the core knowledge of latest package and learning resource that you can talk to, alongside detailed video tutorial showcasing her step-by-step work. (t: 80) So I definitely recommend you go take a look if you're just getting started with your coding journey. I put the link in the description below for you to check it out. And if you're interested in learning more about the Cloud Code, I'll put the link in the description below for you to download for free. And thanks HubSpot for sharing this awesome material with us. (t: 90) Now let's get back to how do I use Cloud Code. So first thing first, you want to install this Cloud Code extension. And this will allow you to deeply integrate into your current ID like (t: 100) Cursor, VS Code, or WinServ. So now I can click on this Run Cloud Code button, and it will automatically open Cloud Code in my current ID. And on the bottom right, you should see that it is detecting (t: 110) the specific file I'm in. And if I select some lines, it will also automatically detect it. You can still use the terminal here as well. And you can just do slash IDE to choose the specific IDE (t: 120) that you want it to connect to. Next, the first thing before you do anything is that you should run this init command line. So what this init command line do is that it will get Cloud Star (t: 130) analyzing your code base to learn about what already set up, what are the kind of dependencies, components that you should be using. It will also ask your permission to do something. For (t: 140) those type of commands, like CD, I just don't care. I will just click on always auto approve. You will see this thing is automatically saved as permissions. I know some of you actually want to (t: 150) get Cloud Code always automatically run tools, and there's a way you can run that as well. You can do this dangerously skip permissions. And this will show you that it is bypassing all the permissions. (t: 160) So now the tool will ask you for permission. Though I wouldn't actually recommend this because from my experience, one of the key advantage of the Cloud Code UX is that allow you to interact (t: 170) with Cloud much better. And I will explain what I mean. All right. So after it scan finish, it will show you the Cloud MD file. If you're using cursor, that's basically your cursor rules. (t: 180) If we click inside, you can see that it automatically detect what are the kind of text stack, what are the run build commands, the architecture, the project structure, (t: 190) all the useful stuff. So this give Cloud a very good base to continue developing new things on top of this repo. And this Cloud MD, as I mentioned before, is pretty much the cursor rules. (t: 200) The only difference is that in cursor, the rules are injected more programmatically, but for Cloud is much easier. Basically, single run this text you define here is appended on top of system message. And I know there are a (t: 210) lot of people have different sorts of like a cursor rules to enhance the workflow. For me, it normally is pretty simple. One problem I do add in is this plan and review mode. This basically (t: 220) tell Cloud Code that before we start working on the project, always in the plan mode to make a plan first. And for the plan, we want to save to this .cloud slash task slash task name to MD so (t: 230) that we can keep track there later. And inside this task, we should kind of break down into tasks. While Cloud Code is doing its job, it should also update the plan as we go and (t: 240) append what it does into the doc. And this is really useful because it is almost always better to just align with Cloud Code what exactly do you want. And this kind of also similar to Amazon's (t: 250) new Kiro workflow as well, where they call it like spec driven development. And what it does is kind of similar. It will ask it to go through the requirements and also kind of go through the (t: 260) architecture design, come up. It's basically the same thing. And obviously you can go in, as deep as like Kiro, where you will break down this three steps process. But I often (t: 270) find it's effective enough to just do one kind of PRD. So our save this. Now we can get Cloud Code to start implementing our feature. Normally the first thing I will do is make it plan. So I (t: 280) will give prompt, we're building a beautiful online ID front end. Help me break down into key components and put them together in the end. And here I'm going to do shift tab. It refers to (t: 290) getting into this auto accept edit mode, but you want to shift tab again into plan mode. This plan mode is a really, really useful feature. When the agent is in plan mode, (t: 300) it has this special system prompt and limit access to tools. So it will focus on doing things like web search to understand the latest like tech stack or documentations and also planning (t: 310) the architecture. And in the end it will generate final reports. And this planning sometimes will take a while. For any feature that is semi-complicated, I would always do this plan (t: 320) mode first, back and forth a few times to align the plan with Cloud Code. And only after I was getting it. So I will do this plan mode first and then I will do the plan mode. And only after I was getting it. So I will do this plan mode first and then I will do the plan mode. And only after I was starting implementing the feature. So here you will see that it is showing task. (t: 330) When you see task it, it basically means Cloud Code is calling a sub-agent that is specifically doing this planning and research. So at default Cloud Code has 17 different tools (t: 340) that can do things like run command line, read and find files, file operations and web search. And this task tool basically means it will launch a new agent for keywords and file search. When (t: 350) Cloud Code agent is calling this task tool it is creating another agent that has almost all the tools accept those planning related tools like tasks to do and this agent will receive a well-defined (t: 360) tasks from the parent agent do a list of things in the end say here are the findings only the last part which is summary of the findings will be sent back to the parent cloud code agent and this (t: 370) is one method they have to really save the token consumption for the parent agent because otherwise the main agent might be flooded with all sorts of contacts and knowing this you can actually utilize (t: 380) this task to a lot better for example you can actually prompt cloud code to use task to to set up multiple parallel agents to do different tasks at the same time but also if there's a task that (t: 390) you know already it's going to read some very large files try to prompt cloud code to just use task to to do this because that will help you save the token a lot on the main agent so now (t: 400) i finished the planning and come back with a plan about what are the layout systems should look like file explorer which makes sense the code editor component it decided to use existing library and (t: 410) then it will break down into different phases the first one is just install everything and phase two is implement file explorer code editor terminal integration (t: 420) and the sound advanced feature as well as the project directory plan so this is a pretty good plan and obviously i can keep planning but here i can also ask it to start do this plan and next is (t: 430) that it will create this detailed implementation plan in dot cloud slash tasks and i will click yes (t: 440) so it will create this task folder and create this online id front end with the detailed plan and i can ask it to let's do phase one and or create it to do based on the phase one requirements so (t: 450) while it is running i'm just going to quickly talk about this to do so i was quite curious how did cloud code actually handle this to do do they handle into a different like planner agent to (t: 460) just specifically come up with plan and programmatically get agent to do one task after another so i did some investigation what i found is the entropy team actually took the (t: 470) the simplest setup possible uh but it's really effective it has this tool called to do right the uc description is basically uses two to create a manager structure task list they have a very (t: 480) specific prompt about like when to use this tool when to not use this tool some example as well so every time when agent runs this tool it will try to come up with this to do and each to do will (t: 490) have the content id priority and status it is as simple as this so now if we come back here it finished all the to do it also going back to our doc and adding details about what it actually did (t: 500) so in market says completed and also documented so it's really cool to see how it can implement all the things that it does cool so it has this (t: 510) component implemented that it has this file explorer resizable it has terminal i can add multiple different terminal as well nice obviously you can see i can continue going because it already (t: 520) has a trace of the overall plan so every time it just need to focus on one specific piece of work and if there any time when the plan change we can just prompt it to update the stock so this (t: 530) is kind of my like doc or spec focused workflow with cloud code but what's really cool about cloud code is that you can customize cloud code in very deep manner so one super interesting (t: 540) feature is hooks so hook is a feature that allow you to define things to happen programmatically when cloud code takes certain actions one of the most basic and common hook that i use is this stop (t: 550) hook so i can define a rule here when cloud calls stop which means it finished the task you try to run this command which will basically play the system sound to notify me that the task (t: 560) has been completed so with this one if i just send a message to cloud it'll play this notification sound after the task is finished so this is basically a simple concept (t: 570) of hook but the interesting thing is that the customization here can go pretty deep cloud could allow you to define things to do before or after certain tool is wrong or when user try to (t: 580) send a new message to cloud code so that you can inject some additional contacts in as well as when cloud try to compress the conversation history or when a sub-agent finishes tasks here's a more (t: 590) sophisticated example that i found actually pretty useful so i can define this post to use hook that cloud code run a edit multi-edit or write tool which means it modify or create some new files (t: 600) it will run this python file i defined here called type check so this feature that i probably missed the most from cursor is that in cursor it has this automatic linked arrow detection and this (t: 610) is really useful contact so that cursor can capture those arrows even before you run the code and proactively fix issue but for cloud it doesn't have this and this hook will basically (t: 620) replicate that functionality directly so inside this python file it might look a bit complicated but i will quickly explain to you what that means so we'll firstly try to get input data each hook (t: 630) comes with a list of different inputs that you can use for example for the post to use input it will automatically give you list data that you can use in this like which tool it is what are the input (t: 640) agent generated for this tool and what's the output of tool and in our case we want to get the file so we know which file agent just created or modified then we'll get a file pass and if this (t: 650) file extension is ts or tsx which means it's typescript we're around the type check and if we input this kind of Hermkum circuit that we want to use we can ask the wiring unit cost committee (t: 660) to send signal to cloud to find the ideal price way if we can send output feedback back to cloud code this is a part where we can send feedback back to cloud code so you will do the print and (t: 670) the file would be system cd arrow here i define the excel code to be two so한다 code2 means that (t: 680) it is a blocking arrow and sd dárierärödo be fed back to cloud code so that it can use that to define the next actions but you can also define other assets called that we're still send message make it call the script and return back an error message if there has any type so cloud (t: 690) code can try to practically fix the issue so this is how hook works and there are a lot of potentials for example you can even define some critic agent after cloud code write any code it can write a test and validate it or if it write an API doc you can automatically (t: 700) update the documentation I have included a few common hooks that I use a lot in AI Builder Club so you can go and grab if you guys want a more deep dive or hook feature please comment (t: 710) below let me know I'm happy to do another one on the outside cloud code also have this commands feature where they do come up with a list of predefined command like check cost (t: 720) set up MCP memory models and review PR commands even but they also allow you to define custom (t: 730) slash commands for example I can create a commands folder under cloud and just create something called joke md and make a joke in all caps once I did this I can do slash and (t: 740) search for joke then this command will come up and I can just create a joke and I can just create a joke and this command I define here will be showing up if I click on that whatever you define here in this command will basically be sent to cloud code almost as a prompt then it will (t: 750) start behave based on the rules you define there I have another video where I talk about different commands that I predefined that can help you extract specific styling from (t: 760) a screenshots as well as getting cloud code into design mode to design multiple different UI iterations and you can even have a command to get a cloud code to set up multiple different (t: 770) Git work tree to have sub-agents working in parallel so you can go check out this video if you want to learn more and there's a one package I thought is pretty useful recently called super cloud it's an open source package that comes with a bunch of commands that they (t: 780) predefined and built for example I can do slash command sc analyze this will trigger that command that take cloud into a much deeper code analyze mode it will create a list of (t: 790) to do to look through the whole code base and come back with a kind of architectural review there are other useful things like you can run workflow to look at a PRD doc to get (t: 800) cloud into kind of step-by-step implementation process as well as build command that will help bundle and npm build your projects as well as troubleshoot if you have some weird (t: 810) bug that you don't know how to fix so this is a really useful package even though the installation is not that straightforward we need to open in a folder to UV init first (t: 820) to set up a python project and then do UV as super cloud this will add this super cloud package and in the end do UV run python super cloud install then it will take you through (t: 830) the step-by-step installation process but once you install it will be installed at global level so you don't need to keep doing this and all the file will be basically saved to your dot cloud settings at user level (t: 840) if we open it it will basically add stuff into the cloud.md which link to all the different files that contain more details inside this command folder you can see all the commands (t: 850) that they define here so this kind of quick example of how far the customization you can do with just command feature meanwhile the list of very useful feature and shortcuts (t: 860) that cloud code has that I didn't know initially. Firstly, I can do slash resume to jumping back to a past conversation history and continue the conversation there this is kind of similar to cursor where you can choose a past conversation (t: 870) but on the other hand they also introduced this export command so this feature will allow you to copy the whole conversation history with cloud code so you can jump between cloud (t: 880) code cursor win serve or any other coding IDE because you just need to paste in conversation history and no need to worry that cursor don't have the context about what has been done (t: 890) and quite often I will also go to cloud where I have unlimited amount token to do some early exploration apart from that another really useful feature is that you can double (t: 900) tap exit and this will allow you to revert back conversation history to a past point so you can just click that and continue the conversation from that point this is really (t: 910) good because sometimes cloud code will make mistakes and this can avoid it the only downside is that if you're in cursor when revert back to a past conversation it will automatically (t: 920) revert back all your files as well. But cloud code didn't keep up with the current situation. So you can see that the code is still working and it's still working. So you can see that the code is still working and it's still working. So you can see that the code is still working and it's still working. So you actually need to use some external package to do the snapshot and versioning. One of them is called CC Undo. (t: 930) So this is a package that will automatically detect all the changes that cloud code has made to your file and allow it to roll back. So I can do CC Undo and list. (t: 940) This will list out all the changes that your cloud code has been made and you can do CC Undo preview to see the specific change that cloud code has been made. And then once you confirm you can do CC Undo, Undo and select the change you want to revert. (t: 950) So this package can work with cloud code. And this is actually it. So tw parental sketch. This is it. So maximum change was line assembler million times. So it has to be even 60% of all this change in this module will say Ver concerto 0 so (t: 960) that you can go home. So 10% more change after I forget the class. And let me repeat it. So with any changes, as you end up with youropers transfer, an example is that the capture (t: 970) is not actually covered. But if I add a new state, so Marqu Caitlin , or it's auto save definite version, then basically it will nearly account for 60% of all the changes it's been made by I (t: 980) stored copy Birds at all time. really fast but also the more important part is that this context would be part of conversation history so that cloud code will know what are the actions that you've been taken and similarly you (t: 990) can also do hashtag which will activate the memory mode so here is where you can type in things that you want cloud code to memorize like i'm jason we're building an online id using chassis and (t: 1000) component and then it will ask you to choose where do you want to save this memory it can be project level or can be user leveled across all the projects and once you confirm it just save that (t: 1010) information to the cloud.me and the last part i also want to show you the easiest way for you to connect cloud code to kimi k2 model if you don't know kimi k2 model is a new open source coding (t: 1020) model that has similar performance like cloud 3.5 to cloud 4 but 80 cheaper so it is really good one to experiment if you run out of credits and the easiest way to set up is that you can open terminal (t: 1030) and do code open this dot zshrc file if you're on mac like me you probably just do this but if you're on windows you might need to change to bash (t: 1040) this will open a file like this so this will basically control your terminal behavior so here i will define the kimi api key but don't worry i already disabled this key so it's not going to work for you guys anymore and i can define kimi where it will export entropy base url (t: 1050) to moonshot using this api key and run cloud so with this one i can just go to my any terminal (t: 1060) and then do kimi this will open cloud code with the special model that i defined there so if i type in hi here it is actually going to talk to the kimi k2 model (t: 1070) and see if it works or not so i'm going to open the kimi k2 model and i'm going to open the kimi k2 model and see if it works or not similarly you can also define things here like cloud bash maintain project working directory to be one what this will do is that basically every time when cloud is running it will always append the current working directory into the prompt so it (t: 1080) will always remember where it is instead of running command in wrong place so here's a quick overview of how i use cloud code if you want to learn more you can join ai builder club where we (t: 1090) have weekly sessions to talk through the latest workflow and tips for ai coding and build large language models as well as all detailed rules who will be using the cloud code and how to use it (t: 1100) and how to use the cloud code and how to use the cloud code and how to use the cloud code and how to use the cloud code and how to use the cloud code and how to use the cloud code and how to use hooks and commands that i personally use so you can copy paste directly in upcoming weeks we'll have more detailed breakdown of how does the cloud code actually work behind the scenes and try to (t: 1110) rebuild cloud code from scratch so that we can learn the best practice of building effective ai coding agents i put a link in the description below for you to join i hope you enjoyed this video thank you and i see you next time

