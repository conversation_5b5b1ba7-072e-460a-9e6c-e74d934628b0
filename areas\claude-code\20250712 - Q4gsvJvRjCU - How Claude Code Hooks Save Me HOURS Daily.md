---
title: How <PERSON> Hooks Save Me HOURS Daily
artist: Better Stack
date: 2025-07-12
url: https://www.youtube.com/watch?v=Q4gsvJvRjCU
---

(t: 0) Hooks are hands down one of the best features in Clawed code and for some reason a lot of people don't know about them. Sure, spawning parallel sub-agents is cool but creating a custom hook (t: 10) for linting notifications and blocking specific commands is so much cooler. Let's go through how to do that and before we do don't forget to hit that subscribe button. To create your first hook (t: 20) use the hooks slash command which shows this scary looking warning because hooks are commands that run without confirmation so if you destroy your own code with them don't cry to Anthropic. There (t: 30) are five types of hooks pre-tool use, post-tool use, notification stop and sub-agent stop. For now (t: 40) we'll go with pre-tool use which runs a script before tool execution. We'll add a bash matcher but you could choose from any one of these and then for the hook we'll just write ls and save (t: 50) it as user settings. At the moment this isn't really useful but if we go to our Clawed settings and click on the hook we'll see that we've got a new tool that we can use to run our code. If we can edit the hook here as well let's change the ls command to this which will save the bash command (t: 60) to a file so whenever it uses the bash tool we'll be able to see whatever command is used by the tool. These values here are part of the hook input which is JSON data that a hook receives so instead (t: 70) of doing this we could write a script in any language like python that takes the hook input (t: 80) and creates a more detailed JSON log that looks like this which could be used for auditing, editing and so on. If we want to use this as a script we can also use the hook input to create a code that's not just editing or just improving the use of the tool. But this is where things get (t: 90) really interesting because in a script if we trigger an exit code of 2 the error gets shown to the model meaning it can adjust its process. So we can see Clawed is creating a node project (t: 100) and by default it's going to use npm but I have a use bun hook that will stop it from using npm and tell it to use bun instead. So the hook triggers this error and instead it uses bun. (t: 110) But you can also do cool things like show Clawed linting errors or block it from accessing sensitive files. And hooks don't need to have a matcher like for this (t: 120) notification and stop hook which plays audio whenever it needs my input. Task completed successfully. Or I could even send notifications to my phone with notify. The level of control you get with (t: 130) hooks is insane and I've only just scratched the surface. I didn't even go through using hooks with MCP tools. Anyway check out the link in the description to see some of my examples. Again (t: 140) don't forget to hit subscribe and then I'll see you in the next video. Until next time, happy coding.

