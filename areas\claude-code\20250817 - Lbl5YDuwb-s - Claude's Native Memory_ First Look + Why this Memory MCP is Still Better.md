---
title: "<PERSON>'s Native Memory: First Look + Why this Memory MCP is Still Better"
artist: <PERSON><PERSON><PERSON><PERSON>
date: 2025-08-17
url: https://www.youtube.com/watch?v=Lbl5YDuwb-s
---

(t: 0) This week, Anthropic and Google both announced that <PERSON> and <PERSON> can now look at previous conversations. They have memory. Very similar to the way ChatGPT can look at your past conversations. (t: 10) I asked for my favorite food. It looked at relevant chats. It said hamburgers, which is easy. In-N-Out burger, obviously. But it has my specific order. Double meat, animal style, extra toasted bun, mustard, raw chopped onions, animal style fries, no cheese. If you know, (t: 20) you know. So in this video, I'm going to talk about AI memory and how it's both good and bad for context. Focusing specifically on <PERSON>, but also talking about ChatGPT and Gemini. (t: 30) Then I'm going to show you my favorite memory, MCB server. And this feature is quite controversial. I'm excited about it, but at the same time, I'm wary. Let me explain why. First of all, how many times have you had in conversation with <PERSON> that you had to start over because of a rate (t: 40) limit? You had to explain what you learned in the past conversation. It's really annoying. And the ability for it to look at past chats to get context is huge. It will help us a lot. It will save us a (t: 50) lot of time. But there's a flip side to it. And no, I'm not talking about privacy. If implemented or used incorrectly, it could actually make us a lot less efficient. So Anthropic released it for <PERSON>. So to get memory, (t: 60) you have to go to your settings and you have to enable it. But it's only available for people in the Max plan right now. It's called conversation preferences. And you turn on search and reference chats. And basically the way it works, you could ask it things about yourself or about your past (t: 70) conversations and it could recall them. I could say something like, what kind of glasses do I have? And let's see if it remembers. Looking for relevant chats. Based on our previous conversation, (t: 80) do you have the Ray-Ban meta glasses? It remembers that I originally had the Ray-Ban meta size 50, which were too small. (t: 90) Ordered the Ray-Ban meta size 53, which are these ones, the bigger ones. It also remembers that I got prescription lenses. I got these ones on Black Friday. So we're talking about a conversation from more than half a year ago. And while this feature is still in beta, there's two implementations about (t: 100) it that I really like. One, it gives you visibility. It tells you when it's referencing relevant chats. And then on the web, you can even click through to these conversations. So in a way, it's a feature (t: 110) within a feature that improves Claude's search and helps you find exactly what you're looking for in your previous chats. The main thing that's missing for me is I wish I was able to turn it on and off from this search and tools button. (t: 120) And it's not an MCP server, but it's a setting like extended thinking that I think should be easy to toggle on and off. Now I was looking through the documentation and what's interesting about it here (t: 130) is what Claude could actually look at. You can prompt Claude to search conversations within these boundaries. All chats outside of projects or individual project conversations, searchers are (t: 140) limited within each specific project. So I think that's a really cool use case. And it's similar to ChatGPT in the sense that ChatGPT has had memory for a while, but ChatGPT has two different types of (t: 150) memory. If you go into personalization, you can either reference saved memories, which is what Claude and Gemini just thought, or you can manage memories where you can add or delete specific memories that will always know about you. (t: 160) And I haven't gotten access to Gemini's version yet because to be honest, I don't pay for Gemini, but I think it should be coming to everybody regardless. Rolling out to 2.5 Pro users today, and they already have this feature built in. So if you (t: 170) don't want it to save sensitive information, you could turn on temporary chats, which is kind of like incognito mode. That's the other thing. I use ChatGPT, I use Claude, I use Gemini. I use the Meta AI sometimes. And basically our memories are being siloed. They're all being saved separately. And it (t: 180) would actually be really useful if we're able to share memories. That's another reason I like MCP because essentially (t: 190) when MCP is fully adopted by all these companies, we'll be able to connect our own tools or in this case, our own memory. I've been using basic memory for a while. This is the one I've stuck with. What I like about it is it stores your memory as a (t: 200) markdown file. And then you could essentially edit it yourself. It's really easy. So I edit and manage my memories with Obsidian. And the benefit of using memory via MCP is you can just go into your settings and just turn it on and off. (t: 210) Basic memory has a bunch of different tools like delete note, read note, build context, canvas, list directory, move note, sync status, list memory (t: 220) projects. There's so many tools here and it keeps getting better. And by the way, I connected to Claude desktop, I connected to Claude code, I connected to cursor, I connected to Gemini, I've connected it to Kiro.dev. Pretty much everywhere I'm working with MCP, I'm able to connect my (t: 230) basic memory. So I'm talking about something I'm going to build with Claude desktop. And then when I'm building Claude code or in cursor or both of them, they're both able to access the same memory bank and see my progress based on what was saved in the memory. (t: 240) And that's a differentiating factor between these native memories that are built into these apps. But most mobile clients don't have MCP support, at least local MCP support, standard IO. (t: 250) So my phone doesn't have access to basic memory. That's the one downside of using MCP based memory. But obviously, when I'm on my computer, I'd be very happy to use the native built in memory via Chagi BD, Claude, Gemini, etc. (t: 260) So I'm going to talk about these built in memory features, MCP implementations, the benefits of them, but it's actually a double edged sword. There's a flip side to this. And it all comes down to context. So let me give you some context. (t: 270) Memory is one of the key components, in my opinion, to reaching AGI. Not that we're going to get AGI in Chagi BD or Claude. And the memory we're getting here and the memory for AGI are different types of memory. (t: 280) But this is a huge first step. Because to reach AGI, we're going to need to get past the short term memory limitation. And we've talked about this a lot on the channel, the limitations of the context window. (t: 290) So essentially, you're given chat short term memory, the longer your chat gets, the context window gets smaller, and essentially dumber and less efficient. And over the last year, we've seen several AI companies release models with increasingly larger context windows, like Gemini now has a million token context window. (t: 310) Cloudforce on it also has a million token context window in the API. Chagi BD five has I think 400,000 token context window right now. So as context windows get bigger, it kind of solves that problem, or so you would think. But as it turns out, just because we give it longer short term memory, larger context windows, they don't actually perform as well. (t: 320) And this is where the term AGI comes in. So the term context engineering becomes really important. Essentially, context engineering is being mindful and managing your context. Just because you could hold a lot of context doesn't mean you should use all your context, you should be very selective about it, because context is the most important thing with LLMs. And what I'm getting at is as great as it is that now Chagi BD, Cloud, and Gemini could look at past conversations, those past conversations and whatever finds gets entered into context. So on the one hand, this is very beneficial. This is very cool. I'm excited about it. But on the flip side, what this essentially does is it allows you to use all your context. And that's what I'm getting at. And I'm going to talk about that in a little bit. But before I do that, I'm going to talk about the context. So let's get started. (t: 350) So let's get started. So let's get started. So let's get started. So let's get started. So let's get started. (t: 380) So let's get started. (t: 410) So let's get started. (t: 440) So let's get started. (t: 450) So let's get started. So let's get started. So let's get started. So let's get started. So let's get started. So let's get started. So let's get started. Let's get started. So let's get started. So let's get started. OK, really, let's go ahead and, let's just do this. (t: 460) Maybe robbed your old blend into delegate Oh, you're hitting R used in the YouTube development Mm-hm came to the command, you're using good marca link. (t: 470) You're not just Major lakh of Is this a good map for you? Is this a good map for you? Isisch��� military? Is thiscupl parking in Antarctica? If this is? watching have a great day

