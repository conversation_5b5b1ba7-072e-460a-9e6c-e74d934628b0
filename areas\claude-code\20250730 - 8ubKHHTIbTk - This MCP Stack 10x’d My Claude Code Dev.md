---
title: This MCP Stack 10x’d My Claude Code Dev
artist: Income stream surfers
date: 2025-07-30
url: https://www.youtube.com/watch?v=8ubKHHTIbTk
---

(t: 0) Hey guys, so I'm actually on a different version of my Windows, so forgive me if some things aren't on dark mode. But I wanted to give you guys a complete setup of everything that I've been talking about in recent videos. (t: 10) So this is going to include the Playwright MCP, Docker, and Cloud Code used in combination to basically be able to code anything. (t: 20) Okay, so let's go through this process. The first thing I'm going to do, I'm just going to go on Google, type in Docker, and then go on the second one, which is install Docker, and then download it for Windows. (t: 30) So we'll just click this one here. That's going to start downloading. (t: 40) The other thing we need is Playwright MCP, so I'm just going to go on Google, type in Playwright MCP, and I'm going to go on Google and type in Cloud Code install. Now, I'm going to pretend like I'm doing everything from absolute scratch. (t: 50) I could just go on ChatGPT and get my command. But instead, I'm going to show you... I'm going to show you everything I do. So I'm going to press copy page here. (t: 60) Let's try to find a free LLM that we can use without logging in. Let's see. ChatGPT should accommodate us. Thank you very much. Okay, so what we do is we give the page about how to install MCPs, and then we give the Playwright MCP page itself like this. (t: 70) And I'm just going to say to the free version of ChatGPT, right? (t: 84) And I'll just click on this to see Like this. I think this is gonna go bad, though. (t: 90) But really, it is the best I can get. And now we're actually going to go back out to our main copy. A really good tip I'd like to give you guys. So the one in the comments is just like in the description below is how to make a Cloud Code MCP command and also the MCP that I want to add. (t: 100) I need the command or WSL for Windows 1 to, right? (t: 110) So actually another thing here is also WSL, right? So we'll start on that as well. I'm doing many things at once. Guys, this is just how I personally do things. I understand. If you don't do things like this. basically. So let's see, we need to install WSL. That's fine. (t: 120) We'll go through that in a second. So let's go terminal here. And then let's go on another version of chagi BT. And (t: 130) we'll say can you help me install the WSL dash the Ubuntu to on my Windows 11 machine. Okay, so let's just do exactly (t: 140) what it says. So it says PowerShell, right click Run as administrator. So we'll do that. And then we'll run this. Let's (t: 150) see if this command works. There we go. It's downloading Ubuntu. Perfect system reboot if prompted. I hope not. I'm making a video so please don't make me do that. Oh, I actually already (t: 160) have it. Because it's already on the system. So that should work for you guys. And then once you've done that you can run (t: 170) that's actually just exit out of this. Once you've done that, you can run WSL dash the Ubuntu like that. Okay, that didn't work. Okay, let's try (t: 180) and install it here instead inside this PowerShell. So this is inside Hamish two, I believe the other one was trying to do it system wide, which is why it freaked out. So let's see this (t: 190) should install it. There we go. Now let's run this WSL the dash the Ubuntu. It's creating a user just put, I highly recommend (t: 200) just putting 12345. As you're probably using a password. And now we're inside. We're inside WSL, right? (t: 210) Okay, so now we need to install Claude Cokes. If I write Claude, you'll see Claude command not found. So again, just I Google Claude code install goes the free version of chat GPT help me (t: 220) install Claude code on my WSL the WSL the machine I might not have node (t: 230) etc. install. Okay, so let's run this one. Okay, so this is from a fairly clean WSL setup. So let's just run this one at a time. So there we go. That should start downloading some stuff. Perfect. Let's get node, I'm going to run through all of this guy's so that you just you don't have to worry about this ever again. Trust me, this system will be worth you guys learning. So if you're new to core code, or you can just skip this part if you've already got installed, of course, okay, so while we're doing that, we're going to run on top of that just to make sure that we're running this one. If all goes well, then this one should be working. How about that? So the top one should be working, okay, you're looking at the bottom, you're looking at the bottom, that's the bottom, let's get into the top one. So let's just run this to our left, the top one needs to be running on top of it. Now (t: 260) let's start installing stuff so we'll get docker here i did say i had docker but i guess because i'm on my second system it doesn't actually exist so let's just get going with docker here and i'll (t: 270) show you the docker specific settings um let's just say yes i guess i'm not really sure what's (t: 280) going to happen here um i really hope this doesn't just delete my entire docker setup on my other one but the things we do for youtube content guys okay so now let's continue so let's do this (t: 295) uh that should start downloading (t: 300) uh did that work it looks like it worked so now let's install it there we go then we need to do node version to see to make sure it's installed i'm really not sure what (t: 310) this is doing i'm very worried about that but it is what it is so i will already show you how to do that so let's just go ahead and install this so we'll just go ahead and install this and we'll just go ahead and install this and we'll just go ahead and install this and we'll run node v and then npmv perfect we should now be able to install cloud code so let's just do (t: 320) these commands that it's given me one by one this is a completely free version of (t: 330) uh chat gbt so i mean anyone can do this so now let's install cloud code there should be no issue with this as far as i understand it perfect that's now installed so now it's from (t: 340) claude and we're inside cloud code perfect so now let's log into cloud code real quick okay so we're now inside cloud code let's just quit out we know that that works now perfect that (t: 350) was very painless to install now let's start working on playwright now a lot of people have told me playwright works out the box it doesn't for me on wsl i'm really not sure what the issue (t: 360) is exactly but um let's just try this so we'll run this command here okay no option no sandbox (t: 370) so it's just adding as usual for no reason yeah i knew that was going to happen so let's just do this and we'll see what happens okay let's see if we can do this and i think that would be the case (t: 380) okay let's see there we go now let's write claude and then let's do dash mcp it's connecting (t: 390) connected perfect so let's say test the playwright mcp by going to example.com this will most likely not work if it does then fine um it's probably because i had it set up properly on my other (t: 400) system but we'll see in just a second so it should be able to just run its own commands here okay here we go perfect oh i have to restart windows for docker okay so this is the problem i always get (t: 410) maybe it won't make me in restart let's see so it says here um that you need to install (t: 420) chrome right so let's just run this let's exit out actually and let's say give me all the commands to (t: 430) get this to work for example i will run them with two as needed uh okay this is this is gonna work yeah okay looks like it worked so while we'll (t: 440) finish this up first i don't want to get too confusing doing 10 things at once like i normally (t: 450) would so let's just let this finish real quick because this is quite an important part of the process so you need to get it working so let's do that so let's say yes that was the command (t: 460) it originally told me to run right whatever okay and then we'll install chrome in just a second (t: 472) this is why i told people people keep leaving me comments saying what do you mean it doesn't work but what i mean is it doesn't work out the box right okay so it says chrome is already installed (t: 480) sure let's install the depths oh i need to i just realized i wasn't running them in the right place (t: 490) so this is the main problem it's the it's the depths thing i think depth is dependencies yeah it is so it's missing some key dependencies as far as i'm aware (t: 502) i just normally just run through whatever claude tells me to do um i believe this is actually needed too but we'll see in a minute (t: 512) so we'll let this finish this shouldn't take too long okay so a little like that worked so (t: 520) i'm just going to say test it again and see what happens i might have to run these two i'm not sure see in just a second okay it didn't work did i not already run that (t: 533) oh okay it works there we go so yeah it needed to install those things before (t: 540) we now have the playwright mcp set up perfect okay so now let's start setting up docker you don't need to sign in you don't need an account just go to settings here go to i believe it's resources and then wsl integration tick the one that you just made uh and then it says apply (t: 550) and restart i really hope that doesn't mean restart my computer (t: 560) okay so there is a possibility that docker won't work fully but that's okay because i'll just show you how we use it and how to set it up so there's actually one more thing that i completely forgot (t: 570) about which is uh github right so i'm now going to exit out of this we have the playwright mcp i'm going to say i have docker installed on my system i need you to be able to run docker commands (t: 580) in the cli please set this up please also set up um github with a pop up oh so i can easily switch (t: 590) accounts as needed right so the three things kind of the base of this is the playwright mcp docker (t: 600) and then uh github as well so github is also an essential part of having an amazing setup so we'll (t: 610) it's doing Docker whatever, Docker info. (t: 620) Okay, so I'm just gonna say, it's given me all the commands basically, so let's just run that quickly. So normally I would let it do itself, but let's just do this quickly. (t: 630) Okay, that worked. I think I can just copy all of these, right? (t: 640) So this is for GitHub, this is to add GitHub to the computer as well. Or as if this were a new computer or if you're a new user to this. (t: 650) What do I wanna do? HTTPS. (t: 652) No, I wanna use OAuth, I'm not sure which one to pick here. I'm just gonna say yes. (t: 660) Okay, so press Enter to open GitHub on your browser. Okay, so then you would log in here. (t: 670) Wait, I need to close, what's this? What's this called? (t: 675) Okay, let's do this again. So HTTPS, yes. (t: 680) Press Enter. It opened up on me. Here we go. Okay, perfect. So I just ran through the login here on the browser, (t: 690) so it now says that I'm connected. Okay, so you can see here, GitHub is working, Docker's not, most likely because either A, permissions, or B, I need to restart my computer. (t: 700) Okay, so now we have Docker set up, we have Playwright set up, and we have GitHub set up. So now let me talk to you a little bit more (t: 710) about what the entire point of this is. Now, Playwright MCP is amazing if you wanna just leave it to go and make things (t: 720) and process JavaScript and process CSS and make sure there are no mistakes. Docker is really, really good for one very specific reason. The main reason I actually use Docker is because it uses the same environment as (t: 730) where you will host, right? So most likely, you'll be hosting on a Linux system, right? (t: 740) So even WSL, there are differences between an actual Linux system and how WSL works. So if you've built your entire system on WSL or on Windows or on Mac, when you go to launch the website, (t: 750) it's gonna not be on the same environment as where you will host it. That's the main reason I use Docker, because I had to spend like two days going from a WSL setup (t: 760) to a DigitalOcean setup. Cloud Code is obviously just the coding tool that we're gonna be using. WSL is just so that we use Linux, (t: 770) but then GitHub is the way that we do version control. Now, this setup is so much better than just diving into Cloud Code and just coding, right? (t: 780) It's so much better to have the setup that we talked about above. Now, a couple more things to mention, right? (t: 790) You've really gotta think about the fact that you're gonna be doing development on local host. You're gonna have a dev or staging website, and then you're gonna have the main website. (t: 800) Now, the easiest way to split these three things is to actually use a .env file, right? But the cool thing about that is you don't really have (t: 810) to worry about the .env that much. When you can use DigitalOcean API, sorry, DigitalOcean app-wide environment variables, (t: 820) it's gonna be a lot easier to do that. So what does that mean? It means that if you don't even have to worry about having a different .env file for local host, (t: 830) local host can work on .env, right? Because it's just for coding and dev and everything. The dev can work on one set of DigitalOcean (t: 840) app-wide environment variables, and the main branch, the live website that people are using will also have its own set (t: 850) of DigitalOcean app-wide variables, right? So let me just show you what that actually looks like. If I just go on DigitalOcean, just log me in, please. So the way, for example, Stripe is set up, right? (t: 860) This is so important to understand with Stripe. Locally, we have local host running, right? But then on dev, we have one set of variables. (t: 870) Let me just check if these are, yeah, you can't see them all. So these are one set, and then if I go on prod, (t: 880) there is another set. So that's actually how you split, how does it know which Stripe API key to use, right? (t: 890) So the key thing is while you're coding, the entire time that you're making this, every time there's a variable, make sure to ask it to use .env. (t: 900) And then at the end, you'll have a huge .env file, right? You can have 30 fucking points in this .env file. All you need to do is grab that .env file (t: 910) and put it into DigitalOcean. Copy and paste. There's a bulk editor. So if I just go on app level environment variables here, bulk editor, this is one that's empty, obviously. (t: 920) You can just paste here, right, your entire .env file. And then obviously you have one for dev, right? One for the dev site where you then, you know, (t: 930) you check if Stripe is working, et cetera, et cetera. And then once you're satisfied, you push to prod, and you already have a different, slightly different, not all of them will be different, like open AI key (t: 940) will be the same, right? It's just things like Stripe, you know, maybe database. You have a prod and dev database, that kind of stuff. (t: 950) This is where you make the split. So you have .env, you have your local stuff. On the dev, you can kind of share between these two. (t: 960) It's not too much of an issue. They're pretty much the same thing, right? Although Stripe is different. And then finally on main, you have whole different env variables here too. (t: 970) So this is kind of what I wanted to talk about. In this video, I just wanted to show you how you got everything set up. And then once you are there, this is how you do it. So you make everything on your local host. (t: 980) Once it's ready to, you know, have a look what it looks like and whether it works actually online, you push it to a staging branch, which is dev on your GitHub. (t: 990) And then once you're ready to launch the product, right, and have people start using it, then you have a main branch, (t: 1000) which is connected to your host. And then once you've done that, you can go back to the prod instance. This way, you don't have to worry about, oh, okay, so I'm making something new on local host, right, for Stripe. (t: 1010) But how do I then test it on dev? And then how do I get it on main? Like this was confusing me for ages. (t: 1020) But as long as you use OS for environment variables and you just threw out like every time there's a Stripe price ID, right, (t: 1030) instead of having, instead of putting the price ID as a hard code variable, if you put it as a .env, you can have, you can use Stripe's system, (t: 1040) their new sandbox system to make one price ID, right, which is just for practice. And the reason that you do that, by the way, (t: 1050) so you don't have to keep paying money to your own tool to test something. You can just use 42424242, which is a test card on Stripe. (t: 1060) You don't need real card details anymore, right? So you have one price, which is a sandbox price on Stripe. And then you have the real live price. (t: 1070) And they're all split with .env variables. From here, once you've got Cloud Code installed, I've got so many videos on what to do next. I would highly recommend watching the streams that I did while making SEO grow. (t: 1080) I'm going to keep making content like this, guys. But this is my kind of setup that I use. I use it. (t: 1090) I'm not really using context engineering. It was fun. And I must say it's pretty good at, you know, making kind of replit level apps in one shot. (t: 1100) But I'm kind of just using this to vibe code my own stuff. I'm still a vibe coder through and through. I'm going to leave the video there, guys. Thank you so much for watching. If you're watching all the way to the end of the video, (t: 1110) you're an absolute legend because that was a pretty long video. And, yeah, I'll see you very, very soon with some more content. Peace out.

