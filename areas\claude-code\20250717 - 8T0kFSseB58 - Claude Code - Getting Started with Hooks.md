---
title: "Claude Code - Getting Started with <PERSON>s"
artist: <PERSON>
date: 2025-07-17
url: https://www.youtube.com/watch?v=8T0kFSseB58
---

(t: 0) When Anthrop<PERSON> announced hooks for Claude Code, there was this comment on the Claude AI subreddit. I'm going to use this to make <PERSON> meow. I've always wanted a cat, but I'm allergic. That felt like as good of a use case as any to get started with. (t: 10) So this is what your Claude Code can sound like when you're running it in parallel. (t: 20) Or maybe you prefer the old school beeps. Or maybe you want Claude Code to talk to. (t: 31) Let me quickly walk you through how I built this. I think this is a really great project for getting started with Claude Code and hooks, (t: 40) because when you assign sounds to all of the different events, you will start to understand when they're getting triggered, which will then help you come up with new ideas for a bit more pragmatic ways that you can use hooks, (t: 50) which is an incredibly powerful. Addition to Claude Code. The general idea here is that <PERSON> has a whole bunch of different types of events and you can set up hooks to run custom commands before or after on those different events. (t: 60) So some of those events would be a pre tool use. So before it uses a tool, the tool includes bash, which you'll find out if you do. (t: 70) This is a lot of Claude Code use. It is also editing files. It is reading files. It is fetching stuff from the Web. (t: 80) You can also do post. It's a tool use. So this is letting you know after it's finished that task. There's notification. There's always been the notification with Claude Code that you could set up with iTerm, for instance, to beep when it's waiting for your approval on something. (t: 90) But I found that it was easy to miss this and didn't always work great. And I really enjoyed being able to customize that. (t: 100) There's stop. So this is when Claude Code is completed as its task. I actually use this a lot in lieu of the notification. And then there's pre compact. So something that is going to do before it auto compacts all of your history. (t: 110) So you can set up Claude to run custom commands before it does any of these actions. And the way you're going to set that up is in your settings. (t: 120) Dot Jason. Now, anytime you're editing your settings, not Jason, you got to figure out which one do you edit? And I think this is a little confusing for folks and probably worth a deep dive later on. But generally for this use case, I've been doing it in my settings. (t: 130) Dot Jason in my project. This way it is committed to my repo and it will be included on all of my projects. (t: 140) So I'm going to go ahead and do that. I'm going to go ahead and do that. I'm going to go ahead and do that. So I'm going to click on all of the work trees that I'm running this on. I also have created inside my dot Claude directory a hooks directory where I'm storing all of the files associated with these hooks here. (t: 150) And I suspect that I might end up moving some of this stuff out to my user settings. (t: 160) My user slash dot Claude directory. So this is included in all of my Claude usage across all my projects. But right now I'm just doing this on a single project. (t: 170) I did not want to do it in my settings. dot local dot JSON, because then it's not committed to the repo. And then I don't get all of this across all the different work trees. You've edited your settings dot JSON before. You've probably (t: 180) just looked at the permissions. And this is a lot of permissions that I've given Claude now. We'll minimize those for now. And now we have hooks. So we're going to define the type of event that (t: 190) we're going to trigger a hook on. Hooks are a list and then a list of dictionaries. And there's (t: 200) really, I think actually command is the only type of hook that you can run. And for now, the only thing I'm doing is running a custom Python script. And so I'm running Python 3 and then I'm passing (t: 210) the full path of that script. So you can see that here in my project, I have Claude, hooks, and then the script called hook handler. And so I'm doing Python 3 users, Greg code, (t: 220) YouTube tracker, Claude hooks, et cetera. All right. So this means that anytime that Claude (t: 230) code, needs to run the notification is running hook handler. It means that anytime it's running stop, it triggers a stop event. It's running hook handler. Anytime it's running pre-tool use, (t: 240) it's running the hook handler. I tried this a few different ways. I tried writing different scripts for different events. I have also tried using the same script, but passing different arguments in (t: 250) like command line arguments. And I found that difficult to debug. I found that what I want to do is keep my settings dot JSON. I'm going to keep my settings dot JSON. I'm going to keep my settings (t: 260) as simple as possible, and then handle all of the logic in the Python script because there I have much better debugging, et cetera. All right. So let's take a look at the Python script, (t: 270) this Python script, by the way, uh, I generated with Claude code. I basically passed it the URL for the hooks, uh, documentation and told it what I want to do. I went through several different (t: 280) iterations and then I had it, um, uh, refactor it several times to make it as legible as possible. And you can find this script, uh, in the description below. So I'm going to do a little bit of that. (t: 290) at hihi.ai slash hooks. But basically what's going to happen here, first and foremost, let's just run and log the data that Cloud Code is passing into this script when it gets triggered, (t: 300) because that data is what will help you decide what you want to do. You can see under hook input, (t: 310) hooks receive JSON data via standard in containing session information and event specific data. And it goes through some of the specifics of what that looks like. Another little tip that I found (t: 320) for working with Cloud Code is you can just say to Cloud Code, do some stuff to test out my hooks integration. And then it will just do stuff. And I think right now the (t: 340) beep's turned on. So you can hear the various beeps that it's doing. And then as that runs, you can see that it was updating its to-dos and it's reading a file now. So I'm going to come over (t: 350) here and I'm going to look into my hook handler.jsonl because I have a function in my hook handler called log hook data. It's right here. Cloud's going to keep making sounds in the (t: 360) background as we go, which I might need to turn off. And so this is basically just writing out (t: 370) the data that's coming in via standard in. So this is what that looks like. You can see there's a session ID. You can see that we have an event name. We have the tool name. (t: 380) So in this case, it's pre-tool use. So we have the tool name and then the tool input. So this gives us all the information we need to know about what Cloud is doing in order to branch the logic to (t: 390) do the various stuff. So let's come back in here and we'll look at then what my hook handler is (t: 400) doing. Basically, I have a... (t: 404) I have a few different directories here. I have a sounds directory and I have beeps and I have voice. (t: 410) And I think on a different branch, I had my meow sounds, although I may have actually accidentally reverted that out after I recorded the demo. But OK, we'll go dig through Git for that later. (t: 420) The way that I got these is I use epidemic sound. I use it in my various YouTube videos. (t: 430) They have music. And then I also they also have a sound effects section here. And so I just came in here and I got there was a section for beeps. And so I just... (t: 440) All sorts of fun beeps that you can play with. Oh, my God, that one's... Let's make that one stop right now. Sorry about that. (t: 450) They've got one for, you know, they've got cats. (t: 454) That's kind of nice. All right. (t: 460) I feel like I only use sad meows in mine. I should change that. I'm not sure. I did some construction equipment once for the tool use. They also have this new feature called voices, which I played around with. (t: 470) So you can have these voice actors who have donated their voice to the AI and then you can make them say things. (t: 480) So I used Sean here because he's British. Seem like Claude might be British. Say committing and you can create the voiceover. (t: 490) Commiting. Commiting. Okay. I guess I did spell that wrong. Let's try that again. (t: 500) Committing. There we go. That's better. So you can see how I did those. If you do end up using these, one just tip is say they often will cram a whole bunch of different sound effects on the same one. (t: 510) And so you can see it here. (t: 520) All right. So then if you go to download, you can come to segment. And you can select just the segment of the sound effect that you want to download. So I'll take that down. (t: 530) You can get just that. So I did that. I played with a different a few different ways of doing this. The first iteration I did, I just threw a bunch of random sound effects into a folder and then I just let it pick randomly. (t: 540) I do like assigning specific sounds to specific actions because I do find that it helps me understand when Claude is doing stuff. (t: 550) For instance, I didn't realize how often Claude is updating its to do list. Which I thought was actually kind of cool. I didn't realize initially before I started doing this how many of the commands that Claude runs is a bash command. (t: 560) And so initially I just had it playing a sound for bash and then it was the same sound over and over again. And then I realized that I needed to do a pattern match on, you know, the specific commands such as editing files or reading files. (t: 570) Or even, you know, it runs bash for using the GitHub CLI. And so doing all those different things, I think it just helped me understand how to do that. (t: 580) So that's the first thing. It just helped me understand Claude code and how it worked better. So I have here, I've renamed these various sounds to the command. (t: 590) So here you can see with speaking. Or I have one for bash. (t: 600) Or testing. There you go. Just the PR. (t: 610) See? So pretty cool. I thought it was great. And I mean you can imagine what this Python script is doing here. So basically we just play the sound. I have a little toggle up here. (t: 620) Sounds type that I can set this to meows or voices or beeps. And then I just have a map telling it to map the different event to the various sound files. (t: 630) And then it will match, go in depth on the different bash commands to figure out what it's doing. (t: 640) And then it will just go in depth on the different bash commands to figure out which sound it should play for those. And this was really fun and pretty easy to create with Claude. And I think that this was a really cool foray into using hooks because it helped me understand the different event types by assigning the different sounds. (t: 650) Which has then given me a lot more thoughts about different ways that I can use hooks. (t: 660) I've seen a lot of folks use them, for instance, to prevent some of the commands that they're most concerned about. So for instance, you could set up a hook to look for a bash command that's running rm-rf, for instance. (t: 670) And to have it not do that, you could, a lot of folks are using it to run linters. (t: 680) A lot of folks are using it to ensure that the test suite is being run before you open up a pull request. (t: 690) And so there's just a lot of really interesting things you can do here that gives you a little bit more deterministic control over Claude code. But I did find that getting started by doing the fun thing with sounds was a great way to get started here. (t: 700) If you want to see all this code, you can check out hihi.ai slash hooks. And I put the code up there and would love to hear what you're using hooks for.

