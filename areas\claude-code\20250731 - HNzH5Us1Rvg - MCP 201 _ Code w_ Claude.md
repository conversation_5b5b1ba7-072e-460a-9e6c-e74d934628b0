---
title: "MCP 201 | Code w/ Claude"
artist: Anthropic
date: 2025-07-31
url: https://www.youtube.com/watch?v=HNzH5Us1Rvg
---

(t: 0) <PERSON>er Reviewer's Name Reviewer's Name (t: 10) Well, hello. My name is <PERSON>. I'm a member of technical staff at Anthropic (t: 20) and one of the co-creators of MCP. And today I'm going to tell you a little bit more about the protocol and the things you can do just to give you an understanding of what there is more to the protocol (t: 30) than what most people use it for at the moment, which would be tools. So, really the goal today is to showcase you (t: 40) what the protocol is capable of and how you can use it in ways to build richer interactions with MCP clients that goes beyond the tool calling that most people are used to. (t: 50) And I will first go through all the different, like, what we call, primitives, like ways for the servers to expose information to a client (t: 60) before we go into some of the bit more lesser known aspects of the protocol. And then I want to talk a little bit about, like, (t: 70) how to build a really rich interaction before we take a little stab of what's coming next for MCP and how we bring MCP to the web. (t: 80) But to just get you started, I want to talk about one of the MCP primitives that servers can expose to MCP clients that very few people know. (t: 90) And those are called prompts. And what are prompts are really are predefined templates for AI interactions. (t: 100) And that's to say it's a way for an MCP server to expose a set of texts, you know, like a prompt in a way that allows users to directly (t: 110) add this to the context window and see how they would use, for example, the MCP server you're building. (t: 120) And they're really the two main use cases here is for you as an MCP server author (t: 130) to provide an example for, that you can showcase to the user so that they know how to use the MCP server in the best way. (t: 140) Because realistically, you are the one who has built it. You are the one who knows how to use it in the best possible way. And probably at the time you would release it, are the one who has used it the most time. (t: 150) But since MCP prompts are also dynamic in a way, they're just code under the hood that are executed in MCP server, (t: 160) they allow you to do even richer things than that. What you can do, and I want to showcase this in this scenario, is an MCP prompt that a user invokes (t: 170) in this ZX, and I'm going to show you a little bit of the editor here, that will fetch directly GitHub comments (t: 180) into my context window. And so what you see me here doing is just basically put into the context window (t: 190) the comments from my pull request that I've written so that I can go and interact with it and have then the model go and help me apply the changes (t: 200) that has been requested to me or whatever I want to do. And so this is really a way for exposing things (t: 210) that the user should directly interact and the user should directly want to put into the context window before it interacts with the yellow lamp. So it's different from that from tools (t: 220) where the model decides when to do it. This is what the user decides. I want to add this to the context window. And if you look carefully, (t: 230) there's one additional thing that very, very few people know that you can do, and that is prompt completion. So if you have looked carefully, (t: 240) there was a way where it showcased quickly a pop-up of me selecting the pull requests that are available to myself. And that is a way that you can... (t: 250) That is a thing that you can provide as an MCP server author to build richer parameterized templates, for example. And this is exceptionally easy to do in the code. (t: 260) Like if you were in TypeScript, building a prompt that provides users with such a template and have parameters for it and auto-completion, (t: 270) it's nothing more than a few lines of code that Cloud Code, together with Cloud 4, can most of the time write basically for you. (t: 280) And it's just that simple. It's a function for the completion and it's a function for generating the prompt. And so this is already one of these primitives you can use to build an interaction (t: 290) for users with an MCP server, which is just a little bit more richer than a tool call. And a second one of these is something that we call resources. (t: 300) It's another primitive that an MCP server can expose to an MCP client. And while prompts are really focused on text snippets that a user can add (t: 310) into the context window, resources are about exposing raw data or content from a server. And why would you want to do this? (t: 320) There are two ways why you want to do this. One thing is most of the clients today would allow you to add this raw content directly to the context window. (t: 330) So in that way, they're not that different from prompts. But it also allows application to do additional things to that raw data. (t: 340) And that could be, for example, building embeddings around this data that a server exposes, and then do retrieval-argumented generation (t: 350) by adding to the context window the most appropriate things. And so this is an area that, at the moment, I feel is a bit under-explored. (t: 360) And I just want to quickly showcase you how resources work. In this case, this is again one of these ways (t: 370) where an MCP client exposes a resource as a file-like object. And in this scenario here, we are exposing the database schema (t: 380) to a Postgres database as resources. And then you can add them in Cloth Desktop just like files. And that way, you can tell Cloth, (t: 390) this is the tables I care about, and now please go ahead and visualize them. And so in this scenario, what you're going to see is Cloth is going to go and write a beautiful diagram that visualizes the database schema for me. (t: 400) And I've exposed the schema via resources. (t: 405) There's a lot of unexposed space still here. If it doesn't take the data, (t: 410) we're going to quizzes and textualize all this data so we can interface SATA and other data (t: 430) As we all know, a нашей Linux file-like solution we have billed it for exposing a tool. (t: 440) And so tools are really these actions, of course, that can be invoked. That's like one of the, I think, most magical moment, I feel, when you build an MCP server (t: 450) is when the model for the first time invokes something that you care about, that you have built for, and has this little impact on, you know, it might be like firing a database (t: 460) for you or whatever it might be. But this is, again, the thing that the model decides when to call to an action. (t: 470) And so these are three very basic primitives that a protocol exposes. (t: 480) And if you think carefully about these three primitives that I just showcased to you, there's a little bit of overlap about, like, how do you use, like, when do you use what, really? (t: 490) And so there's some kind of overlap. And something that we don't talk enough about, and it's somewhere buried in the specification (t: 500) language of the Model Context Protocol, is what I call the interaction model. And I think showcasing it hopefully makes clear when you use what. (t: 510) Because the interaction model is built in such a way that you can expose the same data in three different ways, depending on when you want to have it show up. (t: 520) And prompts, again, are these user-driven models. It's user-driven things. It's the thing the user invokes, adds to the context window. (t: 530) And the most common scenario where how you see these pop up is a slash command, an add command, something like that. Resources, on the other hand, are all application-driven. (t: 540) The application that uses the LLM, be it cloud desktop, be it VS Code, something like that, fully decides what it wants to do with that. (t: 550) And then lastly, tools are driven by the model. And between an AI application using a model and a user, we have all three parts that we (t: 560) eventually cover using these three basic primitives. And that allows you already to go to a little bit of a richer application and experience (t: 570) than what most people can currently do with tools. Because you just have a way to interact with the user a bit more nuanced than if you just (t: 580) wait for this model to call the tool. But we can even go beyond that. (t: 590) Because while these basic primitives get us a little bit further than what we see most MCP servers do at the moment, there are even richer interactions that we want to enable. (t: 600) And to make this a bit more understandable, here's really an example I want to give you that showcases this problem. (t: 610) So how can you build an MCP server, for example, that's simple, but that's also very, very easy to use? And how can you do that? Well, the first thing is that it summarizes a discussion from an issue tracker. So on one side, I can build an MCP server that exposes this kind of data very simple. (t: 620) And that's quite clear. But how do I do the summarization step? Because for the summarization step, I obviously need a model. (t: 630) And so there, one way to go and build this is you can build an MCP server that is this issue tracker server. (t: 640) And you have a choice here. You can bring your own SDK. You can call the model, have the model summarized. But then there's a little problem to that. (t: 650) And the problem is that the client has a model selected, be it like Cloud or whatever else. But the server, the MCP server that you've built, it doesn't know what model the client (t: 660) has configured. And so you bring your own SDK, like of a model provider, and be it the Anthropic SDK, you still need them, like an API key that this user needs to provide. (t: 670) And you can do that. And it gets very quickly very awkward. And so MCP has a little hidden feature or a little primitive called sampling that allows (t: 680) a server to request a completion from the client. What does this mean? (t: 690) It means that the server can use a model independently from, like, don't having to provide an SDK itself, but asks the client which model you have. (t: 700) You have configured. And the client is the one providing the completion to the server. (t: 710) And what does this do? It does two things. First of all, it allows the client to get full control over the security, privacy, and the cost. So instead of having to provide an additional API key, you might tap into the subscription (t: 720) that your client might already have. But it allows also a second part. Which is that. (t: 730) If you take multiple, if you chain MCP servers in an interesting way, it makes this whole (t: 740) pattern very recursive. And what do I mean by that? It's a bit abstract. You can take an MCP server that exposes a tool, but during the tool execution, you might (t: 750) want to use more MCP servers downstream. And somewhere downstream in this, like, system, there might be then your H.T. (t: 760) Tracker server that wants to go and have a completion. But using sampling, you can bubble up the request such that the client always stays (t: 770) in full control over the cost of the subscription, whatever you want to use. It stays in full control of the privacy, over the cost of this interaction, and basically (t: 780) manages every interaction that an MCP server wants to do with a model. (t: 790) And that allows for very powerful security. And that's what we're going to do. So let's go back to the demo. So let's go back to the demo. So let's go back to the demo. So let's go back to the demo. So let's go back to the demo. So let's go back to the demo. So let's go back to the demo. So let's go back to the demo. So let's go back to the demo. So let's go back to the demo. So let's go back to the demo. So let's go back to the demo. So let's go back to the demo. So it's coming back. So let's go back to the demo. So let's go back to the demo. So it's coming back to the demo. So let's go back to the demo. So let's go back to the demo. So let's go back to the demo. (t: 800) Then we go to that О fur and cover just kind of the depth of targeting that I just gave we were trying to reach in the Mercosur game, and we have a level here also that allows (t: 810) For our first-party products, we will bring sampling somewhere this year. (t: 820) And so then you can hopefully start building more exciting MCP servers. (t: 830) And then there's the last primitive that I want to touch on that's also a bit more interesting. And it's one of these things that, in retrospective, as one of the person who has built the protocol, (t: 840) I probably name terribly, to be fair. I'm not very good at naming. You will see this throughout the talk probably. But there's a thing called roots. (t: 850) And roots is also an interesting aspect. Because let's imagine I want to build today an MCP server that deals with my git commands. (t: 860) I don't want to deal with git. I don't want to do source control commands. I don't remember any of that. I want to have an MCP server deal with this. So now I'm going to hook up an MCP server into my favorite IDE. (t: 870) Okay. But how does the IDE know... How does the MCP server know what are the open projects in the IDE? (t: 880) Because obviously I want to run the git commands in the workspaces I have opened. Right? And so roots is a way for the server to inquire from the client, such as VS Code, for example, (t: 890) what are the projects you have opened so that I can operate within only those directories, (t: 900) that the server has opened. And I know where I want to execute my git commands. And this, again, is a feature that's not that widely used. (t: 910) But for example, VS Code currently does support this. And so these are, you know, just all the big primitives that MCP offers. (t: 920) So we have five primitives. Three on the server side, two on the client side. But how do you put it all together to actually build a rich interaction? (t: 930) Because that's what we want. We want to build something for users. It's a bit richer than just tool calling. And so let's take a look at how you will build a hypothetical MCP server that interacts with (t: 940) your favorite chat application, be that Discord, be it Slack. You could use prompts to give examples to users, such as, like, summarize this discussion. (t: 950) And you can use completions with the recent threats, users, or whatever you want them to expose. (t: 960) You can have additional prompts, like, what's new? What happened since yesterday? And so that's one way the user can just kickstart right away into using the server you provided (t: 970) and get the ideas that you, how you intended it to be used. And then you can use resources to directly list the channels, to expose recent threats (t: 980) that happened in the, you know, chat application. Such that the MCP client can index. (t: 990) You can index it, deal with it in ways that it wants. And then, of course, last but not least, we still have our tools. (t: 1000) We have search. We have read channels. We have reading of threats. And we will use sampling to summarize a threat, for example, and really expose it. (t: 1010) And that's a way to really build a much, much, much richer experience with MCP to use the full power that the protocol has to offer. Thank you. Thank you. (t: 1020) Thank you. Thank you so much. Thank you, Gilles. Thank you. Thank you. But this is just the beginning, because most of these experiences, if you build MCP servers, so far have been experiences that stayed local. (t: 1030) Out of the 10,000 MCP servers the community has built over the last six to seven months, the vast majority are local experiences. But I think we can take the next step, and I think this is MCP's really big next thing, (t: 1040) is bringing MCP servers away from the local experience. And I'll take that into account. (t: 1050) And I think it's a very important thing. And it's a very important thing. to the web. And so what does this mean? It means that instead of having an MCP server that is, you know, a Docker container or some form of local (t: 1060) executable, it is nothing else but a website that your client can connect to and expose this MCP and you talk to. But for that we need two critical components. (t: 1070) We need authorization and we need scaling. And in the most recent (t: 1080) specification of MCP we made a ton of changes towards this from the lessons we (t: 1090) have learned and the feedback we honestly got from the community as well as like key partners. And we work closely for example with like people in the (t: 1100) industry that worked on WoF and other aspects to get this right. Thank you. And so with authorization in MCP, what you can do is you can basically provide the private context of a user that might be behind an online account or something directly to the LLM application. (t: 1120) And it really enables MCP authors to bind the capability of the MCP servers to a user, an online account, something like that. (t: 1130) And in order to do that, the way this currently has to work in MCP is that you do this by providing OAuth as the authorization layer. (t: 1140) And the MCP specification basically says you need to do OAuth 2.1. And that's a bit daunting because very few people know what OAuth 2.1 is. (t: 1150) But OAuth 2.1 is usually just OAuth 2.0 with all the basic things you would do anyway. All these security considerations that people that have done OAuth. (t: 1160) It's telling you any way to do. So it's just OAuth 2.0, a little bit cleaned up, and you're probably already doing it if you're doing OAuth. (t: 1170) And if you do implement this OAuth flow, you get two interesting patterns out of that. And the first one is the scenario of an MCP server in the web. (t: 1180) And a good example of this is if you, for example, are a payment provider, and you have a website, payment.com. (t: 1190) And I. As a user, have an online account there. Now I, as the payment provider, can expose mcp.payment.com that the user can put into an MCP client. (t: 1200) And the MCP client will do the OAuth flow. I log in as my account, and I know this is payment.com. I know this is the person that is my online account with the provider that I trust. (t: 1210) I don't trust some random Docker container running locally built by a third-party developer anymore. (t: 1220) I trust the person I already trust with the database. I trust the person who is already using the data anyway and their developers. And on their development side, they can just update this server as they want, and they don't have to wait for me to download a new Docker image. (t: 1230) And so this, I think, will be a really, really big step for enabling MCP servers to be exposed on the web and MCP clients to interact basically with all the online interactions that you already have. (t: 1250) And here's just a small little example. Okay. Okay. So here's just a small example of this. In this scenario, we use Cloud AI integrations, which we launched earlier this month, to connect to a remote server and use this OAuth flow to log in our user to then have tools available that are aware of my data, that I care about, that it is for me. (t: 1270) But it enables another aspect. It enables enterprises to smoothly integrate. (t: 1280) It enables them to integrate MCP into their ecosystem, how they usually build applications. And what does this mean? (t: 1290) It means that internally they can deploy an MCP server to their intranet or whatever, like, they use and use an identity provider like Azure ID or Okta or whatever that central identity provider that you usually use for single sign-on. (t: 1300) And you can have that still exist. (t: 1310) And it will be available. It will be the one that gives you the tokens that you require to interact with the MCP server. (t: 1320) And that has a lot to say that what it ends up with is a very smooth integration. You're, as a development team internally, you're going to build an MCP server that you control, that you can control the deployment. (t: 1330) And the user just logs in in the morning with their normal SSO like they always would do. And any time they use an MCP server from them on out, they will just be logged in and have access to the data that they need. (t: 1340) And that's what we're going to do. Okay. Okay. Okay. Okay. Thank you. Thank you. (t: 1350) So, to sum up, really on the core centro, how we're really thinking about our applications and what other components that we need to avoid, one is, what is the data around the Alberta's cloud base? (t: 1360) And isn't there always some supposed to be a providers called a mass server that is a Microsoft cloud server? What else is there around the factor that we're hoping to see? (t: 1370) What do we expect to see? This is me. What I've already seen. You know? Scaling and we just added a new thing called streamable HTTP Which is just to say it's a lot of blood of words to say basically we want MCP servers to scale similar (t: 1380) To normal api's and it's as simple as that you have as a server author you can choose to either return (t: 1390) results directly As you would be in a rest API except that it's not quite just rest or if you need to you can open a stream (t: 1400) And get richer interactions. So in the most simple way you just want to provide a tool call result You get a request return application JSON and off you go end of the story (t: 1410) you close the connection and the next connection come in and You know get served by yet another lambda function But if you need richer interactions such as notification or features we talked about like sampling a (t: 1420) Request comes in you start a stream the client accepts the stream (t: 1430) And now you're being able to send additional things to the client before you're returning finally the result and those authorization and Scaling together is really the foundation to make MCP go from this local experience now (t: 1440) To be truly a standard for how LLM applications will interact with the web and just to finish it all up (t: 1450) I just want to show you quickly about like what's coming for MCP in the next few months of some of the (t: 1460) Most important highlights and the most important part is that (t: 1465) We are starting to think more and more about agents and there's a lot to do there (t: 1470) There are a synchronous tasks that you of course want to run things that are longer running that are not just like a minute long But maybe a few few like hours long (t: 1480) Tasks that an agent takes and that eventually I want to have a result for so you think a lot about that and we're gonna work to build primitives for that into MCP in the near future. (t: 1490) The second part of that is solicitation, so really MCP server authors being able to ask for input from the user. (t: 1500) And that is something that's going to land just about today or on Monday in the protocol. And then we're doing two additional things. We first and foremost are going to build an official registry to make sure there's a central place (t: 1510) where you can find and publish to MCP servers so that we can really have one common place (t: 1520) where we're going to look for these servers, but also allow agents to dynamically download servers and install them and use them. And then of course we're thinking more about multimodality, and that can be, for example, streaming of results. (t: 1530) But that can have other aspects that I just don't want to go into details yet. (t: 1540) And that's just the specifications. On the ecosystem part, we're going and having a lot of more things to go that we're doing at the moment. We're adding a Ruby SDK that is donated by Shopify in the next few weeks. (t: 1550) And the Google Go team is currently building an official Go SDK for MCP. (t: 1560) And so I just hope that I was able to give you a bit of a more in-depth view of what you could do with MCP if you used the full power of the protocol. (t: 1570) And with that, I think I'm a bit low on time. So I can't ask questions. We can't ask questions. We can't do Q&A. But just grab me afterwards, and I'm happy to answer on the hallway any questions you might have. (t: 1580) So thank you so much.

