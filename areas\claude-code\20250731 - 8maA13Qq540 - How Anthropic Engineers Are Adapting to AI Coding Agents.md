---
title: How Anthropic Engineers Are Adapting to AI Coding Agents
artist: Coder
date: 2025-07-31
url: https://www.youtube.com/watch?v=8maA13Qq540
---

(t: 0) Well, great. Welcome, everybody. (t: 10) We have a lot to cover, so we're just going to get right into it. My name is <PERSON>, and I'm going to be kind of emceeing this session. I'm with Coder. A few logistics before we get started. (t: 20) We have a chat and a Q&A section that our team will be monitoring. We do have a pretty kind of packed agenda with things to discuss with the Anthropic team, (t: 30) but we will be surfacing some of those questions throughout the session, but we also have a dedicated Q&A time towards the end, so we're aiming on leaving about 15 or 20 minutes to cover your questions as well. (t: 40) So I'll start by introducing myself, and then we'll just go around the horn. I'm <PERSON>. I am the VP of Product at Coder. I have had the pleasure of working with <PERSON> and the Anthropic team (t: 50) for the last year or so, and I'm really excited to kick this off and learn more about Anthropic's environment. (t: 60) I'm <PERSON>. I'm co-founder of Coder, and I'm very obviously a huge fan of Anthropic and probably in the top percentile of users of Sonnet. (t: 70) Hi, I'm <PERSON>. I'm an engineer at Anthropic. I've been here for about 10 months and working with <PERSON>r, having a great collaboration, (t: 80) and I'll pass it to <PERSON>. Hey, I'm <PERSON>. I'm the product manager for Quad Code, and really excited to share how we use Quad Code and Coder. (t: 92) Awesome. So for some of you who are in the waiting room, we kicked off this with a poll. Now is a good time to fill out the poll if you haven't already. (t: 100) We're really interested to kind of understand what different people's familiarity with Quad Code is, how often you've used it, things like that. (t: 110) So I guess while folks are filling this out, Kat, how would you describe Quad Code? Yeah, so Quad Code is an agentic coding tool that for now lives in the terminal. (t: 120) The reason that we designed it this way is to make it really extensible, customizable, hackable, (t: 130) so that you can give the agents more information about your unique dev environment and any other inputs, and just make sure that you're able to customize it. (t: 140) We also designed it this way, so that you can flexibly run it in your own environment, such as on Coder. And yeah, we're excited to continue to make sure this is possible. (t: 150) We think that this is one of the most flexible ways to bring a coding agent into your workflow, because it runs in a terminal. It's compatible with most dev setups. (t: 160) You can run it within the terminal section of your IDE provider or your IDE of choice. (t: 170) Yeah, we think this is one of the best ways. It's a great way to meet the majority of developers where they are. (t: 175) Yeah, something I'm showing now is the Quad Code terminal. (t: 180) I asked it to walk through a code base, and you can see that it's able to split up tasks into several to-dos, and it's essentially a great onboarding tool. So this is something we're now recommending to all new employees at Coder, (t: 190) as they're getting familiar with the code base, is essentially to chat with it through a terminal instance. As Kat mentioned, though, Quad Code is extremely flexible, (t: 200) and there's a number of different ways to run it. One of which is through Coder. So we sent out another poll. I think there's a button that you can press to get to the second poll. And we're just looking to understand the audience's level of familiarity with Coder. (t: 210) And to show kind of what Coder is, Coder is a platform for remote developer environments, (t: 220) and it can run a suite of tools, one of which being Quad Code. And you can see here, it really lets you start to use these agents in a composable way, (t: 230) meaning it's running entirely in the background. I could close my laptop, and I could run it in the background. I could close my laptop, and I could run it in the background. I could close my laptop, and I could run it in the background. I could close my laptop, and I could run it in the background. I could close my laptop, and the agent continues to work. And it's running in a fully isolated environment. It also can give developers access to an IDE, (t: 240) so that they could kind of develop alongside Quad Code, whether that's in the browser or desktop. And we're really excited about this, because it's just one way to run Quad Code in multiple instances at once. (t: 250) So as you can see on this screen, we actually have three agents all running in the background, working on various tasks. (t: 260) So with that, let's get into the questions. Jacqueline, before Coder, you were working on developer experience in dev environments (t: 270) at companies like Robinhood and Meta. What brought you into developer experience in the first place? And what are some of the projects you're working on at Anthropic? Yeah, I honestly have been enjoying developer experience. (t: 280) I didn't know exactly what to expect, but after I entered the field, I never felt like I want to leave, (t: 290) usually because I really enjoy building things that people I know love using. And I'm generally very interested in productivity space as a whole. (t: 300) I think the proximity with the customers matters a lot. I love just being able to gather feedback and improve workflows for people who sit next to me. (t: 310) As to what I'm working on at Anthropic, I have been focusing on remote developer environments, majorly leveraging Coder as the control plane and configure environments for people (t: 320) to have one click, everything setting up all at once, ready to go kind of experience. And recently been pivoting more into the agent tech environments, (t: 330) configuring environments for agents to run. Very cool. Yeah, it's great to have a customer that sits next to you, (t: 340) or you can just tap them on the shoulder and ask them for feedback versus having to go through hoops to talk to them. So this is a question both for Jacqueline and Kat. Let's start with Kat. (t: 350) So developers can sometimes use Coder to run their own events, developers can use Coder to run their own events, but some developers are probably not the firstimon, so I think we need to look at that question closely. So how do you gracefully introduce obJS into the existing CMS environment, (t: 360) and now, with the application-centric development approach and the Broadway program being slowly ending and the Apple ad being eventually probably starting to get major Reliant MAN of people who can now experience development, since the C송 and the ADMIS programs were established (t: 370) in Railway Ahrefs via Clean Bitcoin doesn't sometimes be particularly opinionated about their existing tools and workflows. How do you gracefully introduce new tooling and environments to developers (t: 380) common with everyone is that almost everyone uses a terminal. And this means that Cloud Code, because Cloud Code is a terminal interface, (t: 390) it's compatible with VS Code, it's compatible with all the IntelliJ IDEs, it's compatible even if you don't use an IDE. (t: 400) And so, yeah, I think the form factor was really important for just making sure we could meet every developer where they already are working. And this has made the adoption curve a lot easier. (t: 410) I do think that there's even more room to make Cloud Code more accessible to a larger (t: 420) range of engineers. I think right now it resonates particularly well with infrastructure and backend engineers. And increasingly, we do want to evolve outside of strictly a terminal just to make sure that (t: 430) people who are less comfortable in a terminal, you know, are more comfortable in a terminal, and they're more comfortable in a terminal, and they're more comfortable in a terminal, and they're more comfortable in a terminal, or people who are more tech adjacent are able to also get value from it. (t: 440) Yeah, that absolutely makes sense. Go ahead, Jacqueline. (t: 450) Yeah, I also want to double tap on just meeting people where they are. Oftentimes, I found the best change is making changes to the implementation and leaving the tools the same. (t: 460) So if it's a CLI tool, I will often try to... add additional functionalities or features in the CLIs that people already use in the less intrusive way as much as possible. (t: 470) Yeah. That's great. (t: 480) So what we're curious about is, like, what does development look like at Anthropic? Like, if I start at Anthropic, do I get, like, an IDE with Cursor? (t: 490) Or am I, like, given an infinite Cloud Code budget? Or, like, what is, you know, what is the flow look like? Yeah, so internally, I think most people are using a combination of VS Code and IntelliJ. (t: 500) Every engineer does have access to Cloud Code. (t: 510) I believe most people also have access to GitHub Copilot, and some people have access to other IDEs that startups are building. (t: 520) In general, we pretty much encourage people to, like, use a cloud-based cloud. Yeah, I think that's a great point. I think that's a great point. And I think that's a great point. Yeah. Yeah. Like, we sometimes don't think of AI as aggressively as possible. (t: 530) Because I think through the process of experimentation, through giving the AI agents really hard tasks, that's the only way for you to actually push the boundary of what the AI agents are capable of or not. (t: 540) And oftentimes we find that people are surprised by what agents are able to do. (t: 550) Even people who are really in the weeds on training these models are sometimes surprised at, like, oh, wow, like, I gave it a stack trace. and it immediately understood this obscure GCP error and how to fix it, and it saved me four hours as a result. (t: 560) So, yeah, I would say we encouraged a lot of exploration. (t: 570) And then there's also this question of how do you now review code now that AI generates so much of it? (t: 580) And I think this question varies a bit between teams, but ultimately we find that the responsibility falls on the author of the PR. (t: 592) So it definitely doesn't fall on the PR reviewer, it falls on the author. (t: 600) And the things that Cloud Code does to make this easier on the author is a lot of folks do test-driven development so that you have more confidence in Cloud Code's changes. (t: 610) Before you make a PR, submit for review, and there's other kinds of verification. For example, if you're making a front-end change, you can tell Cloud Code to take a screenshot of the resulting app, (t: 620) save the screenshot and show it to you. That way you can visually verify, which is sometimes a bit easier as a first pass than reading the code. (t: 630) Nice. And then I'm curious, how many people at Anthropic primarily use Cloud Code as their workflow tool? (t: 640) And then how did the dog-fooding start of Cloud Code? When it was incepted, was it given to the team before it was released broadly? (t: 650) Or how did it get rolled out? Yeah, so Cloud Code was a project that Boris on the team started just to become more familiar with our public APIs. (t: 660) And when he shared it, he actually realized that he was using it a lot. (t: 670) And then it kind of spread. And then it spread in these concentric circles where he was on Labs at the time, and then his team was using it within Labs, and then Labs was using it, (t: 680) and then it spread across the product-engine team and then into the research team. By the time we decided to launch it externally, most technical ants were DAOs already of Cloud Code. (t: 690) Yeah, I think the original question was, what is developer workflow like in Anthropic? And as you can tell, the journey has changed at a very fast pace. (t: 700) When I first joined, we are so highly dependent on AI systems within the ITE. But towards the end of last year, Boris started Cloud Code, (t: 710) and that has become just the most common tool that we've been using within the company. So I would foresee the rate of change continue to sustain. (t: 720) And next time when we talk about this, it might be different. Very cool. (t: 730) So, yeah. So a question that we get a lot is, what are some concrete use cases for coding agents in the enterprise? And a lot of platform engineers that we talk to kind of struggle to find clear ways to bring Cloud Code into the software development lifecycle. (t: 740) There's a great single-player mode, right? You install the CLI, you use it, and it's magic. (t: 750) But I think for a lot of enterprises, that's less secure or appealing in some cases. So I guess, what are some cases? (t: 760) Where you've integrated Cloud Code maybe deeper into the software development lifecycle, whether that's in CI or planning or tests or something like that, where it feels a lot more approachable for someone rather than trying a new tool on your local machine? (t: 770) Yeah. For CI, we definitely have integration. (t: 780) With the externally launched Cloud, people can add Cloud and interact with Cloud on GitHub interface. (t: 790) Internally, we have some code review assistant tools that we can run a slash command to ask Cloud to help review a PR, for example. (t: 800) We also have something that we leverage Cloud to see whether, given an improved PR, whether we should ask for another human approval. (t: 810) Just to make sure that there is no additional change after the PR is stamped. And of course, fixing CI tests is another common use case that we leverage Cloud to do. (t: 820) And past the whole development lifecycle reaching to when the code is actually running, we also leverage Cloud to help debug jobs, (t: 830) trying to look at Kubernetes logs or trying to run a set of CLI tools to check the GCP or AWS resources (t: 840) and see if they're working. But we also leverage Cloud to actually test the client's employees to see if they're actually still running as intended. (t: 850) And usually, Cloud is very good at finding the root cause and helping to steer humans to the right things. (t: 860) It sounds like you all are putting Cloud everywhere. I'm curious if you have any cases where you put Cloud somewhere and then took it out because maybe it wasn't working (t: 870) or you had to kind of redefine the workflow a second time or a third time. In our case, we had Cloud Code creating a lot of pull requests. Yeah, we have a lot of pull requests. creating a lot of pull requests, basically without any developer review, and it just created a bunch (t: 880) of spam for us. So that would be maybe one example where we saw and were like, well, now we need to kind of adjust the constraints or readjust the workflow. I'm curious if you all have any examples (t: 890) of that. Yeah, internally, we have still been pretty intentional on triggering cloud. I would (t: 900) say most of the cloud workflows are triggered by humans rather than triggered by some automation. So we actually have not seen as much of these. I don't know, Kat, if you've seen more of these in (t: 910) the open source or external land. Yeah, we've also like, there are a lot of features that we (t: 920) build in cloud code that we don't actually end up shipping, because we feel like the success rate isn't high enough. And I agree that developers are very, very, very, very, very, very, very, very, (t: 930) very sensitive to spam, because once you spam them, they're like, okay, turn this whole thing off. This is terrible. And so there's a long list of features that we've tried out that we just felt (t: 940) like, hey, the models aren't quite there yet. And so let's not do it. But I do think eventually, (t: 950) we do want cloud code to be quite proactive. So we want it to be able to like, be able to read across all your previous sessions and extract insights for future sessions. And then beyond that, (t: 960) extract insights from all your teammates sessions as well, to, for example, keep a cloud MD up to date. This is like definitely the direction that we think models will head in. And it's like an (t: 970) area where I think the models will improve a lot. Some other areas where I think cloud codes, (t: 980) particularly, I would say the three main areas where we found that cloud code is very successful is one, it's really great for onboarding. I think you guys mentioned that you do this at Coder. (t: 990) It's one of the first things that we introduce to all new hires, because it is like, the fastest way (t: 1000) for you to get up to speed on a new code base. And it also means that new hires can get work done whenever they want to work and not just when they're like mentor is online. And it just makes (t: 1010) the feedback loop so much faster. The second thing is, it's really great for on call triage. I think this is very similar to what Jacqueline was saying. (t: 1020) Like if you have an on call playbook, you can just put that into your slash command, like an on call slash command, share context with these are the tools that like the and then give (t: 1030) cloud code access to the tools that are mentioned in the on call playbook. And then cloud can (t: 1040) normally just like take a stack trace and roughly root cause it whether it's finding like the corporate GitHub PR or just like proposing a change off of main. (t: 1050) The third thing, the third transition that we've been seeing is a move from Docs to prototypes. (t: 1060) With cloud code, it's much easier to get a working prototype quickly. It might not be mergeable and a one shot way. But, at least you can, you can build something (t: 1070) that works, that you can interact with and give your team a sense of Hey, what would this feature feel like, and I think that's a lot more visceral of an experience than writing docs. So we've been (t: 1080) the shift both within Anthropic and with a lot of the customers we talk to. We call it Docs to Demos. (t: 1090) I have certainly experienced this in the past hackathon. I was contributing to a new codebase, new language, which I have no context of at all, but I was able to leverage Cloud and actually get (t: 1100) something working, a demo working within the day. That's cool. Anthropic said previously, (t: 1110) I know we talked about this earlier, and the numbers are a little who knows, but Anthropic has previously stated that 70 to 80% of Cloud Code is written by Cloud Code. That number aside, (t: 1120) what do you feel like the models need to get better at for us to hit 100%? (t: 1130) I actually think a lot of this is just the way that we're working. I think a lot of this is just the way that we're working. I think a lot of this is just the way that we're working. I think a lot of this is just the way that we're working. I think a lot of this is just the way that we're working. I think a lot of this is just the way that we're working. is like product overhang. Some of it is model, but a lot of it is actually product overhang. (t: 1140) A lot of times when the agent stops working, it's not because, or at least... (t: 1145) To step back, Cloud Code is actually a smaller codebase. Naturally, agents are able to do a lot (t: 1150) more in it because it's pretty constrained. One of the things that we've noticed is that when (t: 1160) the agent finishes, it doesn't always know how to fully verify its work. We think that adding more (t: 1170) affordances for the model to be able to, for example, spin up its own version of Cloud Code (t: 1180) with the local code and test it out and actually get signal on whether or not its code change had the intended effect would be really important because unless the model is able to understand (t: 1190) whether it's changed, it's not going to be able to do the work that it needs to do. So I think that's a big thing. So I think that's a big thing. worked or not, it doesn't really know whether it should stop or what the error is. And right now we're kind of in this phase where the agents are good enough (t: 1200) that now they're making end-to-end PRs and it's on us to give the model helpers so that it can actually test those PRs. And then once these helpers exist, then the agent can iterate against (t: 1210) these helpers and give you much more high quality results. I think the other dimension that (t: 1220) the models need to get better in is just general code taste. We find that the models sometimes just make the change at the wrong level of abstraction or the implementation is far (t: 1230) too complex. Sometimes it's far too simple and it's just an if statement. And there's just this (t: 1240) level of human judgment that we are going to make the models a lot better at. Nice. Do you think that's just maybe more abstract? Do you (t: 1250) think that's on whatever the raw quote unquote intelligence is for it to explore more? Or do you think that's maybe limited by context window and only reading the right parts of a file? (t: 1260) Or where do you think that sits in the model improvement out of curiosity? The code taste part? Yeah, yeah, the code taste part. (t: 1270) Yeah, I don't think it's context. I think it's just (t: 1280) seeing... It's like making... Sure, the model sees a wide variety of code bases that are similar to the ones of our users (t: 1290) and having it understand what is good code. I'm not really sure how to... I don't think it's as (t: 1300) simple as just making sure it can reason across a long context. Because sometimes you see these shorter transcripts where the model still makes mistakes about how to implement a change. (t: 1310) Yeah, I think it's a hard to pinpoint skill. (t: 1320) Yeah, I agree. I mean, I couldn't explain how I do it. So I definitely can't explain how the models do it. I completely agree with that. So we saw Anthropic use Cloud to build a vending machine. (t: 1330) You guys wrote a fun blog on that. I know it's in your office on the lunch floor. And I'm curious if there are any other creative examples of Cloud entering the real world. (t: 1340) Because right now we kind of live in this software world of AI, where it doesn't... I don't really interact with AI when I'm driving to work. But have you guys experimented with any other (t: 1350) things in the real world? (t: 1354) Hmm. I'm very focused on coding. (t: 1360) No Cloud car that we can expect anytime soon. Yeah. Oh, if only. Cloud. Yeah. We have a partnership with Alexa. If you want to talk to... (t: 1370) Yeah. ...Claude. Yeah. Yeah. Yeah, Alexa from Amazon. Cool. Well, we talked about this a little when you were talking about how (t: 1380) Cloud Code excels with a smaller code base like Cloud Code. From your perspective, what is the perfect repo set up for Cloud Code, both in terms of maybe the language and tech stack, but then (t: 1390) also what are things that you can do with Cloud Code to make it more effective inside of a given (t: 1400) repo? Yeah. I think a good general principle is if a human can understand the code base and it's well-documented and variable naming is clear and the abstractions are clear, then Cloud will tend (t: 1410) to do better. If there are situations where your code base is littered with gotchas that require (t: 1420) knowing historical context, Cloud will be as confused as a human who's dropped into that code base would be. Yeah. (t: 1430) Yeah. Yeah. Yeah. Yeah. Yeah. Yeah. Yeah. Yeah. Yeah. Yeah. Yeah. Yeah. Yeah. Yeah. Actually, there is lots of new stuff out there that can go a summit. Easily. Yeah. Okay. This is fun. Yeah. And I'm able to talk about Cloud Code now with you. (t: 1440) That was good to hear. I roughly see almost a half a million people 저�ing. Yeah. Thank you. That is great. That is great. Speakers wonder if there's anything you would care to add? (t: 1450) Yeah, of course there is. Just gonna check in on everyone's likes for our listeners, macht und be expense aseguren, and if some folks like this, thank you for sharing. If we know someone who doesn't speak plumon parl boarding German, we would love to hear more about (t: 1460) their knee-deep battle andarto Great, good stuff, guys. Yeah. a question in the chat around how is the developer workflow with Cloud Code different than the researcher workflow? And maybe as well, besides just Cloud Code, Jacqueline, curious kind of on (t: 1470) the developer environment and infrastructure side as well. Yeah. Generally, we have more repo with (t: 1480) some caveat, like we have smaller repositories, but all of us, engineers and researchers contribute to the same repository and the same environments, which means that we have access to the same tools. (t: 1490) The things that could be a little bit different is just the specific kinds of tools and jobs that gets run. So for example, I don't run the training job where I'll run. But in terms of the AI (t: 1500) assistance power kind of tooling, this is very similar. We leverage Cloud Code and we just tell (t: 1510) Cloud Code the tools that I use and the tools that I use to run the training. And so, I think that's really important. And I think that's really important. And I think that's really important. And I think that's really important. And I think that's really important. And Cloud Code is able to reason very well around that and be able to help troubleshoot as much as possible. So I think one (t: 1520) key point here is having one single abstraction layer for both engineers or researchers or (t: 1530) having one single abstraction layer or repository or tool sets for all of the different teams to use (t: 1540) is going to be key because now we can grow through the economics of scale. We can all invest and make the Cloud IMD for our monorepo better. It has benefit everyone. (t: 1552) Nice. I'm curious what kind of tasks have y'all found Cloud Code is just like bad at? (t: 1560) Maybe it's like, you know, an example is like, if I write sloppy code, it will definitely produce me more sloppy code. It's kind of like an amplifier. (t: 1570) But like, for example, Cloud Code is really good for me at like making website changes. But if I I ask it to write a database migration that does some kind of gnarly stuff, it starts to get a little (t: 1580) trickier, unless I do a lot of specific setup. So I'm curious if there are any tasks that you're like, we don't do this with Cloud Code intentionally, (t: 1590) because it's not there yet. I think there's a lot of tasks that are at the boundary of what Cloud Code is currently capable of. I think with enough handholding, it can pretty much do any task. (t: 1600) And it's just a matter of whether you prefer to prompt an agent versus doing it yourself. I think there's a lot of tasks that are actually at the boundary, though, of what Cloud Code is capable of. (t: 1610) And this includes things like large refactors and to improving test coverage across your repo. (t: 1620) I think the thing that the difference between success and failure in a lot of these cases is giving Cloud Code very specific directions (t: 1630) on what your expected outcome is. And how Cloud Code should verify its work. And then, especially if you want to, for example, (t: 1640) update 100 test files or refactor like a large number of directories, it often is good to just start with one or two, (t: 1650) see the ways in which Cloud Code fails, iterate on these instructions, and then slowly scale up over time. It's definitely an iterative process, though. And definitely recommend trying it, because Cloud Code is a (t: 1660) very, very good way to do that. And I think that's why Cloud Code often can succeed. But it takes a few times of giving it (t: 1670) maybe less specific instructions. And then you're like, OK, it doesn't realize this thing that I know is common sense, but the model just doesn't know this about our code base. (t: 1680) Adding it, trying again, and then eventually getting to a set of instructions where Cloud Code clearly understands what you want and then is able to act more autonomously. (t: 1690) CHRISTIAN MCCARTHY- One other question, Evan. So I'm just wondering if you could talk about what you're seeing in the output. I mean, I think it's a little bit more random. I think it's a little bit more like at one by default. (t: 1700) Yeah. CHRISTIAN MCCARTHY- Yeah, for Cloud Code? Yeah, OK, I thought so. Just for everyone watching, if you increase the temperature, it's kind of more randomness almost in the output. And so I kind of assumed that it would be at one. (t: 1710) But that's cool. This is a good segue into some more advanced workflows with agents. I've heard of the term multi-Cloud before. (t: 1720) Kat, can you kind of walk us through what multi-cloud means? KAT WOLFE- Yeah, so multi-cloud, what this means is running multiple Cloud Code instances at the same time on your local machine. (t: 1730) So there's a few different ways that people do this. One way is a user will run Cloud in multiple different repos (t: 1740) that are non-overlapping. The reason people will run Cloud in multiple repos is to make sure that Cloud doesn't make changes that confuse other Cloud instances. (t: 1750) For people who only work in one repo, we find that people use GitHub work trees, or they just pull multiple copies of the repo (t: 1760) and instantiate one Cloud in each. I'd probably recommend this over running two Clouds (t: 1770) in the same repo at the same time, because each Cloud might change the same files that the other (t: 1780) Cloud is working on. And that would confuse both agent trajectories. So the TLDR is a lot of people love to run multiple Clouds at the same time. (t: 1790) It's best to instantiate them in non-overlapping repos so that they don't accidentally step on each other. How many multi-Clouds do you think (t: 1800) you can cognitively handle at the same time? (t: 1803) KAT WOLFE- I believe that somebody could handle up to six or so. (t: 1810) OK. I think more common is two to four. Yeah, three is my sweet spot. KAT WOLFE- And then we've seen a lot of videos of 12. Jacqueline, what about you? (t: 1820) Do you multi-Cloud? I do multi-Cloud, but I try to not babysit them as much as possible, because, yeah, I will say three is my max. (t: 1830) I cannot have more than three Clouds. And so sometimes I would trigger a Cloud through Slack. It will go off asynchronously, run on its own, which means, (t: 1840) like, there is no Siri involved. Also means that the results is going to be with a higher variance of whether it's working or not. (t: 1850) That being said, that allows me to have more Cloud running in parallel. (t: 1855) BENJAMIN LIMAGEONISKI- Ben, should we just (t: 1860) hop over to talk about security, just so we can answer a lot of questions in the Q&A? BENJAMIN LIMAGEONISKI- Yeah. Yeah, I think this is a great segue to security, actually. So with Cloud running on your local machine, or in the cloud, (t: 1870) in the background, without a lot of human intervention, security often becomes a big topic. How do you think about security, especially as these agents (t: 1880) begin to run more independently with less human approval and intervention? And then I guess the second point to that is, even when I do run it on my local machine, sometimes I'll just blindly accept (t: 1890) whatever it needs to do, rather than actually looking over it, which is not necessarily a great security model. So how are you thinking about security with things like Cloud Code as they start to run more headless? (t: 1900) BENJAMIN LIMAGEONISKI- Yeah. Security is super important to us. So we've built it with security as a foundational principle. We also use a very similar version (t: 1910) of Cloud Code internally. The only difference is that we have some new experimental features that we're not sure about launching yet. And so what we release externally (t: 1920) is, from a security perspective, the same as what we have internally. The first thing that we've built is a very similar version of Cloud Code. (t: 1930) So the first thing that we've built is the first thing that I think is important to know is how we handle our permission system. So out of the box, Cloud Code only has read-only permissions. (t: 1940) Any additional edits require approval from a human. So that includes editing files, running tests, executing any bash commands, any non-read-only bash commands. (t: 1950) And users can control whether they approve the action once or approve them always in the future. (t: 1960) And then we also, like, yeah, so this is quite important because this means, like, by default, the user has to approve everything. We also restrict what folders Cloud Code can access. (t: 1970) So Cloud Code can only read files within the directory that you initialize it in. (t: 1980) So it's not able to, like, escape your repo and then suddenly see everything in your file system. (t: 1987) This. This. (t: 1990) This at least scopes Cloud Code's access. And we also warn users to only use Cloud Code in directories that you trust. This just helps mitigate prompt injections (t: 2000) and other kinds of risks. Prompt fatigue is a real concern. The main way that we've been working around prompt fatigue (t: 2010) is making it really easy for you to allow this commands that you know are safe. And so the more that you allow this known safe commands, (t: 2020) the fewer prompts you see and the less fatigue that you get because it's fewer prompts. (t: 2030) We also design our permission system so that it's really easy to allow this commands. If you really think about it, like, every command, (t: 2040) every tool that Cloud Code has could have been a bash command. The reason that we actually separate it out into, like, edit file, read file, search, (t: 2050) is so that you can grant permissions at a more granular level. We also have some protections against prompt injection. (t: 2060) So for example, when you use web search or web fetch, we put all the results into a classifier model (t: 2070) that just understands, hey, is there prompt injection here, before passing it back to the main agent loop. There's a lot of things that we can do to make sure that we're able to do that. There's a few more things that we do. (t: 2080) We have pretty comprehensive security docs online. But yeah, just want to reiterate, this is very important. And we're still iterating on how best to make sure (t: 2090) this is safe for everyone. That's cool. Super hard problem. I always run, Cloud would dangerously allow everything, (t: 2100) which I know that I shouldn't. But it never tries to do anything bad for me. So you know. It's at least a good model. At least a nice model. (t: 2110) I'm curious how you're thinking about isolation on the environment layer versus on the agent layer. (t: 2120) What would be some takeaways for enterprises who are looking to run hardened Cloud code infrastructure (t: 2130) for agents? Yeah, I can take that. There's this lethal traffic guideline. I think you can take it and use it for other purposes. (t: 2140) But you can use it for a lot of different reasons. And for me, first and foremost, the thing that has been proposed by Simon Wilson, I've been kind of following, which is basically saying, there are three major properties. (t: 2150) The very first one is access to private proprietary data. The second one is internet access. And the third one is whether it has access to entrusted data or not. (t: 2160) Oftentimes internet access to entrusted data go hand in hand, but not necessarily. If, let's say, you have web crawled a bunch of data, The guideline is to make sure that you never have all three in your environments because of two major risks that we want to prevent. (t: 2180) The very first one is data extortation. For example, if you have your social security number or your proprietary enterprise information on your file system, that cloud code has access. (t: 2190) And cloud code has access to entrusted data, meaning that it could get prompt injected. And cloud code has internet access. (t: 2200) If we just allow permissions for all of the tools, then there is a risk of getting data extortated externally. So that is certainly something that we always want to prevent. (t: 2210) As Kat mentioned, configuring tool sets is a great way to achieve that. But if we want to allow more agenticness to cloud code, then I think hardening the environment is a better way. (t: 2220) So we want to have a more efficient approach to let cloud code have as much freedom as much as possible. (t: 2230) So that is a big risk. The second risk we definitely want to also prevent is the destruction of any kind of infrastructure. (t: 2240) For example, production databases, or anything that you deem as crucial to your own business. (t: 2250) It could include PII of customer data. It could also just be your own cloud. So we want to make sure that cloud code can never destruct these kind of critical infrastructure. (t: 2260) And to do that, the best way of doing this is actually having a very mature infrastructure in general. (t: 2270) To make sure that no single actor, whether it's internal employees or agents, can do egregious behavior like such. (t: 2280) However, I think it's very hard for any enterprises to achieve this in one single step. It takes many, many years, even decades, for a lot of large companies to achieve that. (t: 2290) Therefore, one thing that we have been implementing within our agentic environment is removing credentials as much as possible. (t: 2300) And injecting agent identities that has very limited access to our infrastructure. So I would highly recommend everyone to think along the sides of, (t: 2310) Okay, what are the access that cloud has if we were to give cloud all the toolsets? (t: 2320) Yeah, I remember some of those feature requests. So, I mean, it seems like cloud code and Anthropic in general has a pretty solid focus on enterprise use cases. (t: 2330) With stuff like organization-wide policies for cloud code. As well as support for things like AWS Bedrock. (t: 2340) If folks want to run their models in Cloud. And AWS. Was this enterprise support out of kind of an internal need? Or has it always been a goal from the beginning for coding agents like cloud code to be used by some of the largest enterprises with these security concerns in mind? (t: 2350) Yeah. (t: 2360) Because Anthropic takes security so seriously, we felt like regardless, we would have to build a tool that is secure. (t: 2370) And when we looked at, okay, who else would this resonate with? A lot of the potential other people who would care about this are larger enterprises. (t: 2380) And a lot of those folks really strongly prefer to use Bedrock or Vertex to access Anthropic APIs. And so out of the gate, we felt like it was really important to have first class Bedrock and Vertex support. (t: 2390) Which we launched with and which we continue to support. Okay. Thank you. (t: 2400) Thank you. Thank you. Thank you. Thank you. I'm curious. What are the most common ways that enterprises are adopting cloud code right now? And what kind of use cases do you all like really promote if someone comes and they're like, we want to adopt cloud code for the enterprise? (t: 2410) What are you like, here's the first thing you should do it on? Or is it more so kind of just organic because it's all kind of new? (t: 2420) Yeah. It's actually it's pretty bottoms up currently. So a lot of times. (t: 2430) It's like an individual developer who's been trying cloud code on the weekends and then really likes it and decides to bring it to work. And then we'll help. (t: 2440) We'll help them get like through procurement and to set up a pilot with a small group. We're not that opinionated about who should be in this pilot group. (t: 2450) We try to just like pick a few folks from across the org. Sometimes it's like two teams or. We're pretty flexible because cloud code is a really general tool. (t: 2460) And we found that it works well for pretty much every engineer background. (t: 2470) Yeah. So the kinds of use cases that we encourage people to start with are things like asking like code based understanding, solving like constraints. (t: 2480) Each like building constrained features or solving like well defined bugs. And then we encourage people to branch out after that. (t: 2490) So it's about like. Starting small and like getting a feel for what cloud code can handle. And then as you understand the quirks of cloud code, like where it's really good, where it fails, then you can invest in a called MD to give it more organizational context and code based context. (t: 2500) You can invest in building building scripts and other automations and other tools that cloud code can call. (t: 2510) Customizing your script. Yeah. That's I think what that is the one thing that I find like really great is you can go into the store and find all your user related stuff. (t: 2520) And you can buy services for負 some of that stuff. This more state is like sort of like a cliental platform. (t: 2530) And it's like our afterlife. You get to have it all for free. Uh, we can alsolarly like having like Houdini. Your customers look at it so Perché nel (t: 2540) Hedini. seen in some of your materials that you actually refer to (t: 2550) Cloud Code as kind of two separate products, right? There's the CLI that you can run and then there's the SDK that you can use to extend. How are people using the SDK and how do you see that evolving over time? (t: 2560) Yeah, so the initial so there's like maybe three categories of ways people use the Cloud Code SDK. The first one is to actually just make like a (t: 2570) wrapper around Cloud Code like a light wrapper, like a GUI or something. And this is just because, you know, a terminal interface is like hard for some (t: 2580) folks to use. It's like very different than if you're used to using web apps. And we found that some of our customers just want to wrap that into (t: 2590) something more friendly before distributing it. So that's one case. The second case is Cloud Code is a very general coding agent, but a lot of people (t: 2600) have, want to make it more specific to one use case or one role. So we've seen people customize the Cloud Code SDK to make (t: 2610) a security engineer, a site reliability engineer, an on-call assistant. And the Cloud Code SDK lets you bring your own system prompt (t: 2620) and your own tools. So it's pretty straightforward to add like the specific instructions and additional tools (t: 2630) that you would need for these roles. Increasingly, we were really, really excited about people using Cloud Code. And so in a sense, this is really where we're (t: 2640) going to go into a little more detail on how to use Cloud Code to build general agents and Yeah, so right now the Cloud Code SDK lets you bring a system prompt in (t: 2650) tools. What you get out of the box is the core agents loop, the permission system, prompt caching, like a lot of the nice-to-haves are handled for you. And so we've seen (t: 2660) people experiment with making this into illegal agents, making this into a finance agent, making it into like, an agent that powers user-facing features that are not coding related at all and this is a direction (t: 2670) that we're going to continue to push in because we're really excited about people building general agents on top of the quad code sdk that's super cool um yeah we have a lot of people in coder who (t: 2680) are not even in like software that end up using a lot of these agent things to automate parts of their life and i am very bullish on on outsourcing my intelligence i have been for a long time now (t: 2690) and so it's great um last question and then we'll do q a it is what's next on the roadmap for (t: 2700) cloud code yeah um we would love for quad code to be available on like more surfaces that engineers (t: 2710) are on so right now it's in the terminal and you can invoke it on github but there's so many more places that you do work day to day and so that's a direction that we're going to push in the second (t: 2720) thing is uh cloud code is more than just a tool it's a tool that's going to be able to do a lot of things and it's going to be able to do a lot of things and it's going to be able to do a lot of things and it's going to be able to do a lot of things and it's going to be able to do a lot of things and it's going to be able to do a lot most successful one has access to all most successful one has access to all most successful one has access to all the tools that developer has the tools that developer has the tools that developer has so think like being able to understand (t: 2730) so think like being able to understand so think like being able to understand the discussions on slack and being able the discussions on slack and being able the discussions on slack and being able to pull in to pull in to pull in the um prd uh product review docs and the um prd uh product review docs and the um prd uh product review docs and like engineering specs and like engineering specs and like engineering specs and um uh jira tickets customer support (t: 2740) um uh jira tickets customer support tickets stuff like that and so making sure cloud code has information to all access to all the information that developers do we're also really excited to push on the sdk so just making sure (t: 2750) that it has feature parity with cloud code the cli experience and then please keep feedback coming (t: 2760) on github issues where we're really investing in making sure the sdk is powerful and flexible and supports building arbitrary agents awesome that's a great segue to our first question (t: 2770) um which is how does the team solve for um tightly coupled systems when dealing with large code bases (t: 2780) or even in some cases multiple repos and then the second part of the question is how does coder help with those constraints i'm happy to take that one unless you all have any any thoughts there too (t: 2792) um for the former i think it's mostly about one making sure cloud code is it's not just a simple code that you're going to use with a lot of code and then you have to make sure the code has access to all the relevant repos. So for example, if you have like five repos that (t: 2800) cloud code needs to edit to make a change, then you should probably instantiate cloud in (t: 2810) a folder above those repos so that's able to access everything. Or you can use add dir, (t: 2820) which is a new feature that we have to give an existing cloud code section access to more repos. The second thing is, I think it's mainly about making sure cloud code has the right context. (t: 2830) So the same way that you would onboard an engineer to making changes across multiple repos, you should make sure that you share that context with cloud code, maybe in the form of cloud MD or (t: 2840) custom slash command. At the end of the day, cloud code only knows what it's able to read in your code base, what it's able to fetch via MCP servers, and what you tell it. And just like I said, (t: 2850) making sure that all the context about how you would like the change to be made is there, (t: 2860) I think will get you a long way. Cool. I also saw a couple of questions about... Oh, go ahead, Jacqueline. Sorry. I was going to touch upon your second question. (t: 2870) The thing which we do allow people to configure a code or environments with is (t: 2880) having multi-repository support, which allows people to say, oh, I want to work in these set of repositories and drop cloud into the environment. And in that (t: 2890) particular setup, cloud can do a very good job. Cool. Great. I saw a lot of questions about testing as well. (t: 2900) One question was around, do you use cloud code to generate tests? And the other question was actually more on the other side, which was, do you ever prevent cloud code from editing tests so that (t: 2910) it always has to conform to a test and maybe doesn't make changes there as well? (t: 2920) Yeah. We find that with the quad four series of models, it's a lot better at not editing the (t: 2930) tests. I suppose you... I haven't experimented with actually hard preventing cloud code from editing the tests, though cloud code is extensible enough that you can (t: 2940) test things and then make changes as well. I think there's a lot of things you can do that cloud code can do that you should be able to. For example, you could at the beginning have (t: 2950) cloud code write the tests. You could have it copy the tests outside of the repo to some other (t: 2960) file that cloud code isn't able to edit. And then you could add a hook at the very end when cloud code tries to make a PR that copies the test back, runs them, and that way, you know, (t: 2970) that the tests weren't modified. I don't know if it's worth it to do that. I think it's worth it in the end. to go that far but cloud code is customizable enough with hooks that you should be able to (t: 2980) actually force the tests aren't changed. Generally adding a prompt will be sufficient to tell cloud code to not change the test. (t: 2990) One of my favorite use cases is I actually sometimes I'll use AI for it but sometimes not. I'll like write a function and then I get AI to write just like a filthy amount of tests for it. (t: 3000) That's one of my favorite use cases and I'm just like never edit the source file but always create a whole swarm of tests around it because you don't really care about what (t: 3010) the test code looks like as much. So that's kind of a nice use case. (t: 3020) Great so that's testing. (t: 3023) I see some questions about sandboxing and (t: 3030) kind of running isolated cloud code instances. I'm curious maybe if we could we talked about it (t: 3040) a lot but we could just drill like a level deeper kind of Jacqueline maybe this is for you how maybe on an infrastructure level how do you leverage kind of cloud codes existing (t: 3050) sandboxing features and then what else do you do to like sandbox your environments like what does a what does a cloud code environment look like at Anthropic for one that runs more agentically? (t: 3060) As you put it. Yeah we certainly have some cloud code instances where we run in environments that is a lot more (t: 3070) permissive and we configure tool sets to limit read-only access to our Kubernetes cluster for (t: 3080) example. But to your question of if we want to even enable beyond read-only and we want to enable (t: 3090) cloud to do as much as right as possible we have configured environments where there is no (t: 3100) internet access except a very few allowed domain and on top of that we allow all of the tool usages (t: 3110) because in this particular case it doesn't matter if it tries to run curl it will still fail because it doesn't have internet access to a lot of the websites. We do disallow web fetch. (t: 3120) We do disallow web fetch. Just to avoid confusing cloud as much as possible and on top of that (t: 3130) because like limiting internet access does decrease the capabilities of the agents we do add MCP servers with the various specialized configurations like for example maybe read-only (t: 3140) across all of the GitHub issues or GitHub PRs. We have exposed these as MCP servers so in this particular case I wouldn't (t: 3150) necessarily need to give the agents a full GitHub credential and allowing github.com as a domain to avoid cloud to push sensitive code to a public PR for example. (t: 3160) DR. BEN MI스� зас (t: 3170) CURRIER Кол-INE Excellent question. I see you talked a little bit about this in your last question but someone else is asking about just baseline recommendations for using MCP DR. BEN MIST såsodeja to augment cloud code I've learned a couple of things. 975 00 would be on slide (t: 3180) Whensoleuv dizemez o這邊looking asus romariyu wild 얘기 suas kod gebeut pehleang raserat abister இngve mel行baek ed prepares sniffat scary idea Аw of things as I've started using MCP, which is there's a such thing as too many tools, and then the agent gets overwhelmed. But I'm curious, how would you recommend people go from (t: 3190) a more vanilla Cloud Code instance to begin adding MCP, and what are some of the things they should begin? What are some things they should consider as they're going through that? (t: 3200) Yeah. While registering MCP tools, definitely make sure that the tool descriptions are clear. Avoid collisions as much as possible because as you mentioned, Cloud can get very confused (t: 3210) if there is, not knowing which specific tools to use. But also, Cloud is very good at figuring out (t: 3220) the tools just through its own memory. For example, if you tell Cloud through CloudMD that there is this particular CLI tool that it can use that calls into a particular service (t: 3230) that you have spun up internally, Cloud will actually do that very well as well. So it doesn't necessarily need to be MCP. I think MCP is a great abstraction, (t: 3240) but if MCP introduces more overhead of friction, just talk Cloud the set of tools that you (t: 3250) normally use. Cloud is very good at just coding up like a human. (t: 3254) Nice. Someone asked, which I don't know if you can answer this, (t: 3260) Kat, but I'm curious if native support for parallelism is coming. It's something that Nathan asked. Maybe like, I know you all have some docs on like get work trees and things of that nature, (t: 3270) but I'm curious if anything native for that is coming into CloudCAD. We don't have a shift date yet. (t: 3277) That's a great answer. (t: 3280) That's all of mine. (t: 3281) Let me see what else. I guess kind of on the parallelism topic, I saw a question about the differences between (t: 3290) kind of using a sub-agent versus multi-clotting and how to think about those two things separately, I suppose. (t: 3300) Yeah. So I think one of the biggest benefits of sub-agents is that it will speed up any given task by doing parallel actions at the same time as much as possible. But sub-agents are still like, (t: 3310) you should have multiple clods if you have different tasks. You should have one called per (t: 3320) like independent task. Sub-agents still work on the same task. So it's not like on the same task as the main agent. It just speeds it up a bit more. (t: 3330) Or like if you have custom sub-agents, it can bring like a security perspective and a design perspective and product perspective, but it's still on the same core task. So that's how I decide whether to use sub-agents or multi-clod. (t: 3343) I see one question that I have to shamelessly plug, which is, do people (t: 3350) leverage Coder to do multi-clod and avoid raise conditions? Yes. That's one of the top use cases. You can have a bunch of cloud instances running. They're remote, they're sandboxed, and you don't (t: 3360) have to worry about disrupting your local branch or repo or any type of kind of security risk. So that's a very common use case. Oh, here's a really interesting question. How do you evaluate (t: 3370) new models and gain more confidence in exchanging them over time? Is that something you can speak to? (t: 3381) Jacqueline, do you want to? New models? Well, I think generally we do a lot of very extensive validations and testing. (t: 3390) We also work with a lot of the institutions to make sure that there is alignment and as much as possible. So it's very safe to interact with. So I think you can trust Anthropiq that the newest (t: 3400) models is always safe to use and it's going to be better than the previous ones. (t: 3411) So, I think that's a really good question. I think that's a really good question. I think that's a really good question. I think that's a really good question. So, I think that's a really good question. I think that's a really good question. I think that's a really good question. So, I'm going to get someone asked is, will Cloud Code introduce a robust undo feature that doesn't rely on LLM calls? (t: 3420) Presumably, like some form of, I think what is classically called snapshotting. (t: 3430) Sorry, I didn't catch the question. Oh, sorry, sorry. I said, will Cloud Code introduce a robust undo feature that doesn't rely on LLM calls? Yes, yes, we are working on that. This is like the top requested feature. (t: 3444) So, I think that's a really good question. I think that's a really good question. I think that's a really good question. I think that's a really good question. So, I think that's a really good question. I think that's a really good question. I think that's a really good question. And then for isolation, I know a lot of people just asked about like (t: 3450) generally using Docker or it being like on a system level. And I'm curious what y'all are thinking about that, like for even more enhanced isolation. (t: 3460) Yeah, we've been debating Docker versus WorkTrees. We haven't made a decision yet, but we're prototyping both and we'll probably just release the one that feels better to use. (t: 3470) That's great. Great. We only have two minutes left here. Jacqueline, Cat, is there anything else y'all would like to (t: 3480) ask or add or plug or anything like that? (t: 3490) Yeah, we're really, really excited about Cloud Code being able to do longer Horizon agentic tasks over time. And I think that's a great opportunity to run Cloud Code in remote environments like (t: 3500) Coder. Since right now, the models still need a bit of guidance, which is why the local interactive terminal interface (t: 3510) works well. But over time, Cloud Code will be able to do like 30 minutes, an hour longer of work (t: 3520) productively. And in that case, you probably actually don't want to have to manage a bunch of WorkTrees and Docker containers locally. And so I think the more future looking thing to do (t: 3530) is to set up your remote infrastructure so that when the models are ready, you already have that set up to go and your engineers are able to kick off (t: 3540) many Cloud Code runs at the same time on different tasks. (t: 3543) Yeah. And for the shorter Horizon, I think it's just great to get hands-on experience using Cloud (t: 3550) Code as much as possible. I think that's the best way of knowing where the limitation lies. And more than often, I would find that the limitation is actually coming from the lack of (t: 3560) tools or the lack of the tools that are already there. So I think that's a great opportunity to build on that. And I think that's the best way to do that. And I think at the same time, I think it's a great opportunity to build on the lack of knowledge of Cloud, knowing which tools I can use. So just configuring those will already provide a lot of value for (t: 3570) Cloud powered workflows and for the future agent and workflows too. (t: 3580) Awesome. Well, thank you both so much for joining us in the webinar. And if anyone has any follow up questions, you can feel free to ask or email us. You can just email me too. I'm just kyle.koder.com. (t: 3590) So yeah, thanks everyone for tuning in. Thanks everyone. (t: 3620) Thanks, oldest. (t: 3630) Thank you for joining the Puisque society. Then next month. (t: 3640) Pause That'll cut a lot. That'll cut a lot. (t: 3650) I'll certainly help you with that. Enjoy the next session as much as you would like. Thanks, whites. Bye. Bye. Bye. Bye. Yes, though. Bye. Bye. Bye. Bye. Bye. Bye. Bye.

