---
title: I did not expect <PERSON> to release Custom Sub Agents...
artist: GosuCoder
date: 2025-07-25
url: https://www.youtube.com/watch?v=X1QVgg9nR34
---

(t: 0) So Cloud Code has had kind of a rocky week or two. If you look at the subreddit and you look at my discord and you look across like the internet as a whole, you see a lot of negativity. (t: 10) They have a lot of API outages and they also seem to be kind of variable. A lot of people are complaining about getting less than stellar results from Cloud Code, (t: 20) Cloud in particular, and people are even seeing in other AI coding assistants like Augment Code. Now whether that is actually happening, I actually think it possibly is, especially when (t: 30) you're doing a little bit more complex stuff. It feels like Cloud has gotten to the point where (t: 40) it's taking shortcuts on things, but that's not the purpose of this video. I am calling that out as like I am aware of that, keeping an eye on it. I'm doing some tests to see if I can figure out (t: 50) if it's true or not, but I do think there's been something that's changed or updated with Cloud 4 in particular. Cloud Code, I'm not totally sure yet, that has made it just feel different and it requires a (t: 60) different level of prompting. But today, about four hours ago, someone at discord posted, (t: 70) hey, did you know there was a slash agents command I just found? I'm like, what? I had no idea that existed. So I really appreciate you sharing that first off. But what it is, is it allows you to (t: 80) actually create custom agents, custom sub agents. I have a million ideas for how to actually go and start making these sub agents. But let me show you some of the things that I've worked on already. (t: 90) If you don't recall, back in the days of like when boomerang mode first came out, I was a huge fan of (t: 100) that. I still am. I love the orchestration framework that root code has, but it is hard to pass context properly between all these modes. And as much as I like my micromanager mode, and I still (t: 110) use it from time to time, actually, I think it's really cool and allows me to use like lower cost, models pretty effectively. It still does have some downsides because you are relying on the (t: 120) context being passed correctly to the mode and then back to the orchestrator. But really what (t: 130) sub agents are is what root code is doing with boomerang mode. It allows you to create these sub modes, which from what I can tell based on the documentation actually has its own context (t: 140) window. So this potentially will help lower cost, maybe. I mean, we'll have to do some testing on this. I have actually set up a couple. Now, I want to show you a few. This is pair programmer. (t: 150) It's one of my favorite modes, my absolute favorite modes from root code that I've ever built because I've always felt like code mode in root code, pretty much any of these tools, (t: 160) really, unless you prompt it very specifically, we'll just jump in and start coding right away. And sometimes I actually want options. So the idea here is that the agent will actually come (t: 170) back with some options. I won't be able to use a specific code. I'm just going to use a specific code. I'll just go ahead and run this. And then I'll just go ahead and run this. And then I'm going to go ahead and run this. I will say I'm not happy with this one yet. I need to tweak it. It's only been a couple hours. (t: 180) The problem I'm having, and I'll show you here some examples, this is a very simple one that I actually set up. I have a slash command that I created with the idea of like, hey, can I send (t: 190) a new feature through a workflow? And I think this will work. So I have this workflow where it says (t: 200) start with a pair programmer agent to review this. Then confirm with the user what approach to take. So it should take the feedback from the pair programmer, talk to the user, and theoretically, (t: 210) if you have feedback, it should go back to the pair programmer. I don't know if that'll happen or not. But then you will proceed with implementing it using just the general purpose agent. I say (t: 220) multiple general purpose agents, hoping that if it can, it will split out multiple. Once it's done, you'll actually use my code reviewer mode that I've created. (t: 230) And that will actually give you give any feedback. And then when that comes back in, what will happen is we'll have more general agent to go through and implement that feedback. And (t: 240) then the results are there. Here's what that looks like with a very incredible, simple, making a page mobile responsive. This is the simplest task I could think of just to see if (t: 250) the flow would work. It triggered the pair programmer. It identified five different approaches. But this is the flaw that I need to work on. (t: 260) It only gave me approach two. I want to actually see all five approaches here so I can kind of think through what's happening. But honestly, this is the way I would have done it anyway. So I just (t: 270) continued on. And I did hit some rate limits here, which I was actually thinking that my whole test was going to be gone when that happened. Then it completed it and went to the code reviewer, (t: 280) which was great. Fixed the issues and it showed me the results. So worked just like I wanted. (t: 290) And to show you what this looks like, I'm going to go ahead and do a little bit of a test. So this is what it looks like. I know this is kind of basic, but it's pretty decently mobile responsive now. This did not work this well before. I should have showed you the before, but (t: 300) you can imagine the text boxes getting skewed when it got to a certain size. So that works great. It (t: 310) did what it needed to do. I have not reviewed the code yet, so I'm not going to commit that. Now, the other thing that I've been working on is a little bit of a bigger task. This one, I think it's going to take a little bit of time. (t: 320) You can see here that I was also getting API errors on this one. It has done the pair programmer. It did the analysis. It actually gave me three refactoring approaches. So this time (t: 330) it worked. It gave me the options I wanted. I do, again, I want this to be more consistent. (t: 340) And then it asked me and I said, sure, let's try number one. It created the to-do list and it's updating it. And now it's thinking, I don't know if this is actually, I feel like it might be (t: 350) hung here. Actually, I just saw it. I saw it go offline. There we go. It's coming back on. We'll see what this ends up triggering. But regardless of that, let me show you how to actually set up a new sub agent. Remember, (t: 360) if you do want to make a custom slash command, it's very easy. Just in your .cloud folder, create a commands folder, make a MD file. Whatever you name that MD file, (t: 370) it's just going to show up as a command. So here you can see new feature. I probably won't actually keep this one, but I do want to play around with things. (t: 380) Like this to see if I can get this up. Like I want to see like, Hey, can I meet a, can I make one that goes and adds a controller for me, adds the repository, (t: 390) adds some of the base CRUD APIs, et cetera. That'd be really sweet. If I could do something like new API dot, dot, dot, and it just goes through and puts everything where I want it. And I have (t: 400) a nice like workflow on that using sub agents that are tuned to like do that particular thing. I like the ideas are just spinning all over. To be honest, it's very exciting. (t: 410) But let me say you want to actually create your own. You go to agents. I would recommend, unless you're like really, really sure what you want to build, (t: 420) like the ones in root code. I spent many, many, many hours kind of building those out. And I actually had people in discord refined them for me. And I've used, I think my latest version I'm (t: 430) using a pair programmer was actually one that somebody helped me refine. So I appreciate that kind of feedback too. And I hope to see that with these types of sub agents as well. So I'm going to (t: 440) create a new sub agent. You can choose between, personal, which will put it kind of at the global level on your computer, or you can put it at the project level. I'm just going to put it at the project level here. And (t: 450) then I'm going to say, I want to generate this with cloud. Now, one of the things I was thinking about, it's like, what's a good agent to create here. And I was thinking, why not just make a backend developer? So I'm just going to say expert backend developer that excels at creating API data (t: 460) models, authentication breaks out code to manageable services and utility classes follows all loopback, (t: 470) for best practice, and then I'm going to create the new sub agent. And I'm going to put it in the backend developer. So I'm going to go ahead and create that. Now this will take a few seconds. So what we'll end up being able to do now is it'll actually generate a, it'll generate something that (t: 480) you can review and you can kind of, kind of choose to, to change or save, but ultimately it will (t: 490) land up in a markdown file. So you can go and manually create one of these. If you want, you can go in here and manually change them. So this one that I just created, what I'd probably do is (t: 500) actually add a bunch of additional sub agents to it. So I'm going to go ahead and create a new sub agent as well andцен it so that the centraladio, central manager Iggy can see. Look at how I got here with my control panel. I have here actually a code application, adı but there's a lot of different functions that you can change when Perd just вс that's the module that was created for (t: 510) the server. So it's going to help me prepare a list of those. So I'm just going to sign in here and make my code all the way here. Now let me show you how I could basicthat that he can do this. This should work anyway and then it should work. Like I said, if you go to HUD (t: 520) Now I don't know, what is a good color for back end engineer? Maybe we'll make Cancer maybe we'll (t: 530) him maybe we'll make him pink so we'll have him be pink now this is basically what it gave me I can't see all of it the description is very long it says (t: 540) over 400 characters I can hit E to edit it in my editor so it was loaded up in here or I could just hit s or enter to go ahead and save it and then over here (t: 550) I'm just gonna go over and refresh and look at my loopback backend architect interesting what it named it but we'll have to kind of give this a try and see (t: 560) how it works okay so what I have here is kind of a complex prompt the idea is I want to add a new LLM tool called railing on help that will take (t: 570) basically a query so if someone in the chat says hey I don't know how to do XYZ it'll take that question send it to another it sends it to a function call (t: 580) I'll use another LLM that LLM will take a bunch of context I could probably do even like some rag stuff in the future it'll take some (t: 590) some context and then maybe I'll add a function to it like I'm going to edit another functionality and then maybe I'll add a function to it like I'm going context about our product, probably start lightly, and then it will return an answer back that'll go to the original LLM. This is a model I'm actually using quite a lot in my product. It works incredibly (t: 600) well, which you can kind of think of as subagent. So yeah, I have a lot of subagents in my project. I've asked it to start with pair programmer, implement with the loopback backend architect, (t: 610) and then basically go to the code reviewer. And we'll see what happens here. I'll fast forward through some of this. So what's interesting here is it actually did a bunch of research before (t: 620) using my pair programmer. So I wonder what kind of context it's passing to the pair programmer going into this, because it's a pair programmer going to have to do all of that again. So we'll (t: 630) just have to kind of see what happens. Awesome. Look at this. So pair programmer went through. (t: 640) Oh, wow. See, this is a problem that I have. It didn't actually confirm with me. And that might be because I... I need to wrap that workflow in front of it. Because if you notice, when I actually did the (t: 650) new feature workflow, it did actually confirm with me. In this one, it used the pair programmer to confirm with what the LLM thought it was. So it's like a pair programmer for the LLM. (t: 660) I definitely got to tweak that some. But man, just think about the options here. That actually was a good change. That's exactly what they needed to do, by the way, (t: 670) what I just changed. The loopback backend architect is running now. I like the way it's colored. It's just really slick. I think there's just so many neat things that we can do now. (t: 680) There's a lot of tedious things that I actually want to go through, create subagents for that are highly tuned. I don't know. I think I'd be very curious what (t: 690) you guys think about this. If you have ideas for different subagents that we should build, post them in the comments below. My mind is just spinning. I probably have about 10 that I really (t: 700) want to set up. But don't forget, the workflows, I think, are going to be the top priority. I think we're going to have to do a trick to this to where we can actually have new feature (t: 710) workflows, bug fix workflows, probably new API workflows, things like that, merge conflict workflows. Oh, Claude, why are you trying to query my database here? So my data tool is very, (t: 720) very simple. It's simple, but it's very easy to actually read that. I guess I'm going to have to (t: 730) be a little bit more specific on what it needs to do here. But it should not want to actually use (t: 740) P-SQL here at all. Well, again, I'm absolutely right. And I don't know if you guys feel the same way, but man, AI thinks I'm an awesome programmer because it tells me I'm right all (t: 750) the time. If only my wife would do the same. All right. So it looks like we're going to actually go to the code reviewer now to review it. So what's it trying to do here? So in this case, (t: 760) I do not think it's getting the content. Text of the code that was edited because it's trying to edit something that was written before, (t: 770) but it is not wrong. This is functionality that I removed at a certain point. So I think we can (t: 780) go ahead and approve that. But I need to make the code reviewer not have access to these tools. So that is a mistake on my part. My code reviewer definitely wants to write a little bit too much (t: 790) code. Like now it's trying to create like a chat tool. Service file. All right. It's going to make another edit here. I feel like everything that (t: 800) it's doing for the most part is actually valid. It did change that one thing that it shouldn't (t: 810) have touched. And it did create like a help document, but that help document looking at it is actually related to what we're doing. So it like generated some documentation about my product (t: 820) for me. So it finished, it ran through, did the loop, back architect. It ended up using the pair programmer, but incorrectly for it. I wanted (t: 830) loop back architect and the code reviewer. I think with the combination of custom slash commands or some way to like specify a workflow combined with these new agents, you're going to end up (t: 840) with just some really cool like scenarios. I honestly am very excited about this. (t: 850) It's going to be a lot of tweaking to it. It's no different than what it was with root code, but it does open up the code. So I think it's going to be a lot of tweaking to it. It's going to open up a lot of customization, which is what I freaking love. I love this stuff (t: 860) so much. I love taking like little things and kind of like optimizing it, like having a researcher sub agent or something like that. I'm a big fan of this. So anyway, I just wanted to share this (t: 870) with everyone. Hope this has been helpful. And if it has in any way, it would mean the world to me. It'd be amazing if we could get 10 or 20% of people that watch this video to actually give (t: 880) it a thumbs up to help kind of boost it. Anyway, until next time, everyone have a great day. Peace out.

