---
title: We Made Claude Code Build Lovable in 75 Minutes (With No Code)
artist: <PERSON>
date: 2025-07-09
url: https://www.youtube.com/watch?v=_GMtx9EsIKU
---

(t: 0) Many people think that ClaudeCode is better than Cursor. So I brought on an AI coding legend, <PERSON><PERSON>, to see if ClaudeCode can actually vibe code full applications. (t: 10) And so to test this in one sitting, <PERSON><PERSON> is going to try and build Lovable with ClaudeC<PERSON>. In this video, we show you how to set up ClaudeCode, we talk about how you can build ClaudeCode wrappers with their SDK, (t: 20) and by the end of the video, you will see if we're successful in building Lovable with ClaudeCode. And this thing also just finished generating a website. (t: 30) Bro, ours looks better. So cool. I'm super excited. This was a fun one. Let's dive in. So <PERSON><PERSON>, what are we doing today? (t: 40) Today, we're going to be building a website that builds websites with ClaudeCode SDK using ClaudeCode. (t: 50) So it's tough to wrap my head around this. Do you want to like maybe break that down just a little bit for those who are like, VibeCoder native? (t: 60) Yeah, sure. So ClaudeCode is a tool that lives in your command line interface, also known as CLI. It's this big, scary black box normally near the bottom of Cursor, (t: 70) and it also exists separately as like a terminal. It's the same thing. And it lives in the terminal basically. And so this is a software that Anthropic has released that uses Claude to help you develop software like kind of like Cursor. (t: 80) Like Cursor and Windsurf, except it lives in your terminal, which makes it different. Yeah. It's a little more technical. It's a little more technical. But I also think it's a little more powerful. (t: 90) And the cool thing about it, right? Like Anthropic released it for you to use as a tool as a general consumer, but they also used it for you to use as a developer. (t: 100) So I can develop tools that use it as well. So like similar to the way people are making money with chat GPT wrappers or like replicate wrappers, you can create a ClaudeCode wrapper (t: 110) with something called an SDK. Yeah. And so the way that kind of works is like chat GPT is a very generalist LLM. Like it can answer and do a variety of things. (t: 120) ClaudeCode is definitely like, he's the nerd in the class. He's like, oh, I'm only good at coding. I don't care about anything else, but he does coding really well. Yeah. And so you can definitely build wrappers around ClaudeCode, but you'll definitely (t: 130) be in the industry of like vibe code, lovable, bolt replet. Like you're going to be building a code gen tool. So if you wrap ClaudeCode, it has great capabilities of writing code, but just (t: 140) know that the products that you can make are kind of limited in the scope that you're probably going to be using it to write code. Right. Right. I mean, it is called ClaudeCode. Yeah. Yeah. So today what you're saying is we are going to use this new tool ClaudeCode (t: 150) to use the SDK of ClaudeCode, which will power the app that we create called lovable. (t: 160) We're going to create a lovable clone powered by the SDK. Yeah. Yeah. Yeah. All right. A really basic one. Yeah. Let's do it. How do we get started? In our last episode that I did with Mickey, where he broke down ClaudeCode, (t: 170) he says he uses it in cursor. And so you do the same usually, right? Yeah. So here's my experiment actually, but we can just create straight up a new one. So we're starting. We're starting a new fresh project in cursor. (t: 180) We're going to go ahead and create a folder called lovable clone. Amazing. Yeah. And we're just going to go ahead and open it. So now this is by the books, a very standard, you know, cursor code development environment. (t: 190) I can create a terminal by either doing this or using the shortcut. And so I have a terminal now and I can go ahead and run ClaudeCode like I did before in the terminal. (t: 200) And it's asking to. Like. Read your stuff. Sure. (t: 210) Go ahead. And we can actually configure this to do a lot of things without asking. Normally you'll see that. Oh, okay. No, I actually don't want you to do anything. So it's already like trying to start coding. (t: 220) I can tell it to stop. It's eager. Yeah. It found out that I wanted to make a lovable clone because I named the. It looked at the name of your file. That's crazy. It started like telling you like, oh, I'm going to set up a next JS project type script. (t: 230) I'm going to create a landing page. I'm going to implement. Claude's like, I don't need you. Just going to go. Do everything. Honestly, it would be a super fun experience to kind of see if it can do it just right off the bat by itself. (t: 240) But we have some custom demands. We want to use Claude code SDK to go ahead and build this whole thing. (t: 250) So there are a few things that I guess I could show you first. So like a shortcut that you can do is you can actually do looking for it on the keyboard. (t: 260) This pound sign allows you to kind of create like Claude MD files, which access persistent. Man. Memory for your Claude code session. (t: 270) So I can say like, I am building a lovable clone, but I want to use the Claude code SDK and I can create that and it'll ask me, okay, do I want it in my user memory or my project memory? (t: 280) Well, user memory would mean for every single instance I use Claude code, it will try to think that I'm building lovable, which is not like the right thing. (t: 290) Yeah. That would probably be more something like my name is Kihon. I could. Save that forever as my user memory project memory. (t: 300) This is where it belongs because I am building a vibe coding, a lovable clone right now. So I'll go ahead and create that. I guess if, for those of who have used cursor one, the bottom one would have been like your cursor rules. (t: 310) Yeah. And then the top one would be like a read me file in that project. Yeah. And as you can see, it's actually very similar. Like Claude literally just spun up a read me basically an empty file is a Markdown file and it's basically an upgraded TXT file. (t: 320) So this is basically a read me. And so it's noted down. I'm building a lovable clone. And so that's really cool. (t: 330) Another quick feature I wanted to showcase before we dive into using it is you can shift tab to kind of show auto edits on. So this is really cool. (t: 340) And I can also shift have again to show plan mode. So I guess I would actually begin by saying, like, do some research on it because that's what I'm going to do. And one really interesting trick about anthropics Claude code is that it uses the web and it does a lot of research, but you kind of have to push it in the right direction. (t: 350) So here I have the Claude code SDK documentation. (t: 360) I found it by saying Claude code SDK. And then if you go to Google, it's the first link. So I'm gonna go ahead and grab this link and I'm gonna just give it to Claude on plan mode and be like, okay, look, so it has the memory that I'm building a lovable clone, but I want to use the Claude SDK. (t: 370) Let's just tell it like, let's focus on using the Claude code SDK to power my code. (t: 380) Generation agent capabilities, code gen features for my lovable clone bubble loveable clone for now. (t: 390) Let's scope things down. (t: 400) So lovable is a website that builds websites for now. I'm going to build my background is mainly in back end engineering. Right? I'm definitely more on the infrastructure and back end side of things. (t: 410) So we're going to stick to that since this project is pretty orientated around that. And we're just. Yeah. I'm just gonna go ahead and ask it, you know, I want a function that builds something for me. (t: 420) Okay. And that something is gonna be a website. Should translate. So function that you need to execute for those of you who aren't as technical, you wanna create a place that you can type your app idea. (t: 430) Yeah. And it will execute it or do it and create the code for that. Yeah, exactly. Yeah, exactly. So we're gonna go ahead and tell that we just want the raw piece of code that you can execute that will build something for me. (t: 440) And so I'm gonna translate that to a little bit more tech lingo. Let's scope things down. I want a function that lets me input a prompt and uses cloud code to build it. (t: 460) Let's just see what it does. Amazing. And so I have it on plan mode. I don't need to tell it specifically like, hey, I don't want you to edit code. I just want you to come up with a plan. Right. It's currently asking, can I read this? (t: 470) Yes, you can. And the cool thing about this now, I can take this time to explain to you that cloud code will generate this directly. It's a directory called .cloud. Inside of it, it has settings and it just keeps track of things that you allow and disallow. (t: 480) Okay. And so we have to configure this when we build software that uses cloud code to make sure that the SDK cloud code must also have the correct permissions so that it can write and read and do things that we kind of want it to do. (t: 490) Let's pause there for a sec. So the same way that cloud code asks you permission, right? (t: 500) We are building an app that uses cloud code. Mm-hmm. So we need to basically enable the app that we create to do the same thing. So we have to have the same permissions that we're granting as we build mobile. (t: 510) Exactly. Exactly. Okay. And so it's giving me a lot of things. Once again, it's giving me like build simple UI, build with next project, implement service class, like all of this, you know, we're trying to scope down. (t: 520) This is probably a good engineering practice. We should be having service classes. We should have simple UIs, pretty simple. But yeah, like we're going to just say no. (t: 530) Yeah, yeah, yeah. I like it. I like it. I just want a simple TypeScript function. That takes in a prompt and tries to build it with cloud code. (t: 540) Don't worry about the website yet. (t: 550) We want to prove to ourselves that we can accomplish the main functionality first. (t: 560) And so show me. Koh try some of this to see if you can learn to do that. (t: 570) Oh, (t: 580) there it is. nes. (t: 590) Sund acted! It's super, super easy. BLACK Practice is really easy to following. Yep. This works is that cloud releases, I think cloud code came out before four, but four might have some documentation on cloud code, but obviously the. (t: 600) Probably they might've even trained cloud for on the implementation of cloud code, but they definitely don't. (t: 610) They're keeping that closed source. So it's knowledge. Isn't that great. It's also like a needle in the haystack one part of its training data, and it's not going to like know exactly how it's implemented and be able to remember that from its training set and just know about it instantly. (t: 620) And also this way it kind of has a better control of like what is released to the public. And so whatever it finds online, it can know that like, okay, I can like definitely use this. (t: 630) So that's probably all baked into like the model that they trained itself, but let's see, it kind of has us given me some pseudo code about, you know, looks, this looks like, okay, it's giving itself tools to use max turns 10. (t: 640) I'll get more into that later, but it kind of looks like, you know, it's looping through some messages and it's looks like a function to extract code from messages. (t: 650) Like. That's interesting. It seems like this could work. So I'm just going to go ahead, get off of plan mode and get on auto accept edits mode and say, okay, implement this feature. (t: 660) And I want you to test it, try and make a simple tick tack toe game using the function that calls Claude code SDK. (t: 670) Let's go, baby. Hmm. (t: 680) Make this in HTML for now. And so. HTML is kind of like super bare bones. I don't need any setup around it. I can just open it in a browser. (t: 690) And so that's why we're asking it to build an HTML first. Later, we can change that to a next or like a react native website. So, okay, well, sure. (t: 700) Nevermind. It wants to build a website. Why not? It will go ahead. I'll just let this thing run for a sec. And so this thing will always ask you, there's a way to make this, not ask you actually similar to Yolo mode on cursor. (t: 710) And I can talk about. that a little bit more in a second, because if we want to take this to the next level, we're actually going to have to make it such that currently it's going to just write code (t: 720) cloud code, which I'm using right now is going to just write code in my workspace. When we want cloud code SDK, which is our lovable coding agent to write code, we're going to want (t: 730) that to happen in an isolated environment where we can let it do whatever it wants. We're just going to like put it in a bubble and whatever happens in that bubble is fine. It can blow up, (t: 740) it can break, it can like do anything. And it won't affect the main. And it won't affect like my, yeah, the damage will be contained. That is the point. Yeah. And so if we have time, (t: 750) we can get to that. But for now we're going to scale things down. We're going to let cloud code write something that writes code on my local machine, which probably isn't like too good (t: 760) either. Honestly, I should just spin up a Docker instance, but okay. Simplifying down. Let's see. We have a main function that wraps cloud code SDK. (t: 770) And a test script that uses the generate how to test it. First use your Anthropic key in my .env file and then run it. I'm going to go ahead and put a key here and we're going to make sure (t: 780) you don't steal that key because this key is very important. Okay. So I just put the key in my .env file. I've closed it and I will never open it again because I don't want you to have my secrets, (t: 790) but it's telling me I can just, you know, run this after I put my key in. Let's see if it works. (t: 800) So you can just run it in a new terminal. Yeah. So this terminal is housing closet code. So it's busy. This one is a new terminal. I'm in the same directory. I'm just going to try this. Okay. Let's see. It's planning right now. So we (t: 810) can kind of see that this is a system message from cloud code. We told it to generate me a tic-tac-toe game. Cloud code is able to use these tools. It's thinking, I guess. Okay. It's using (t: 820) Opus. Wow. That's really cool. Cloud is going to run up my bill with cloud. So that's cool. (t: 830) We're going to see. Now what we had is if you notice, I actually had to run a script. This is the function that I was talking about. The super bare bones, like I just want a piece of code that uses cloud code, the (t: 840) right code. So I executed that piece of code and now cloud code is writing code. So all these green and blue and yellow and complicated system messages, assistant messages are actually, (t: 850) it's cloud trying to write code right now because we ran the code. Right. So let's kind of see if it ever creates me anything useful. All I really needed to do is write me an HTML file. I can tell (t: 860) you that I'm still thinking, honestly, that's a little concerning because it shouldn't think too hard code generated successfully. I need permission to write this file so we can see that it doesn't (t: 870) have permission to write files. And so I'm going to go ahead and flame cloud code because I explicitly told it to give it permissions. As you can see the function you wrote does not allow (t: 880) cloud code to write. (t: 890) Okay. Please make sure. Okay. Okay. Okay. Okay. Okay. Okay. Okay. Okay. Okay. Okay. Okay. All permissions are granted to the cloud code SDK such that it writes files. Okay. Here are the (t: 900) docs again. Just make it work. I'm going to tell it to fix itself because we've identified the issue. (t: 910) The issue is. That it basically gave it not enough permissions to do what it's doing. That's probably why it (t: 920) for longer than you were comfortable with. Yeah. Let's see if the test that Cloud Code wrote to test this on code is working. I think in this case it's actually better than Cursor because it's really annoying to (t: 930) use the terminal that Cursor's agent uses. Right. But it's pretty easy to see what the terminal is doing here because I can click (t: 940) control R and I can see everything that's going on. I definitely get confused using Cursor's terminal thing. Yeah. What's amazing is we actually already have, (t: 950) I see here tic-tac-toe.html. So it built it and put it in the code base. Yeah. So we can go ahead and open tic-tac-toe and it opened tic-tac-toe. (t: 960) There it is. So our lovable clone, which we haven't built a front end for yet, built this tic-tac-toe game. Let me play you. (t: 970) Hey, you want to play? Yeah, let's play. Let's make sure this thing works. Okay. Wow. I like it. You win. Fuck. What are you doing? (t: 980) What do you mean? You could have gone there and won. Oh my God. I told you to let me win and I let you win, bro. Then nobody won. Let's see what happens. Is there an animation? (t: 990) It's a draw. Okay. Pretty basic. But yeah, we now have a function that writes code, specifically HTML code. (t: 1000) So let's talk about what we must do now and reason through it actually with Cloud. So this is actually one of the best parts. We have a proof of concept working, as in we are now able to use AI to write code for us. (t: 1010) There's a few things once, as you mentioned, that differentiate this from lovable. First of all, we don't have a landing page, (t: 1020) a real website that houses this. Right. And second of all, lovable can make cooler things than a single HTML script file. So what do you want to start with? And so there's a few ways we can upgrade this to become more like lovable. (t: 1030) We can build a website around it. We can make this such that the user can type in whatever prompt they want, and it'll try to build it instead of hard code. So we can start coding a tic-tac-toe simple game and a single HTML file prompt, right? (t: 1040) Cool. We can do that. We can build a website around it. Or we can take this. We can start containerizing things and making the back end more powerful. (t: 1050) So if you don't mind, since I love the front end, I think vibe coders love making sure the front end actually looks like a wrapper. (t: 1060) We can take a screenshot. Wait, can you throw it on dark mode at least? I think the dark mode version of their site is better. There we go. OK, great. You want the dark mode clone. We got the dark mode clone. (t: 1070) What I was saying was you can grab this screenshot. It can actually digest images as well. If we want to clone the dark mode of lovable, we can go ahead and screenshot this. (t: 1080) And we can go ahead and copy this screenshot and go back to Cloud Code. And we can just paste it in. And so we can say something like, let's continue our project by building out the UI. (t: 1090) We have tested it. And our function works. (t: 1100) For now, let's focus on building a website that mirrors the current lovable website. (t: 1110) I want the soft gradients and a text input. I noticed that there's a lot of buttons and tabs here. (t: 1120) I don't really care about this. I just want this to work, like the nav bar on top, the hero of the website. So let's kind of see what it does. (t: 1130) There's a few ways it can take this approach. It can build a Next project. It can build a React project. I've seen it. Yeah, it likes to default to Next. I'll just let it do its thing. I'm not a really good front-end engineer, so I have some idea of what's going on. (t: 1140) For the most part, I'm kind of fumbling through the dark, too. I noticed it ran the project starter script, which will give me a lot of lovable UI. (t: 1150) Title, lovable clone. OK, so it's laying out the websites. Websites have metadata. Honestly, this is taking it a little too long. It's taking it a little too far. (t: 1160) I don't really need this stuff. OK, but it seems like it's running things fast enough. It's telling me to change directories and run the dev server. It's figuring it out. (t: 1170) I need to go into this directory and run the dev server to see it. So I can go ahead and run this. It tells me it's running. And now we can click on localhost. And now localhost will be here. (t: 1180) Oh, OK. It's kind of there. I don't have the gradient. That's why I wanted to snag the gradient. I don't have the logo. I mean, I didn't expect it to build the logo. (t: 1190) But you want to test it? We can try giving it a better prompt. I don't see the gradient. I would like a gradient similar to lovables as in an orange circle. (t: 1200) Into pink circle. Into pink. (t: 1210) Blue. Into black. Black. Give that a try. And then I think you should do one other thing, which is make the input area by default three. (t: 1220) By default, three lines high. You like lovables? Also, the input area is a little small. (t: 1230) Lovables is around three lines high by default. Three lines height. By default. (t: 1240) Ours is one. And then also make the generate button an up arrow instead of the generate thing. Oh, yeah, sure. Also, make the generate button an up arrow instead (t: 1250) of the colorful button as right now. (t: 1260) I think we just give it the photo again. OK. OK. Yeah. I dig it. I dig it. I dig it. (t: 1270) Now let's talk about some more problems that we're going to face kind of immediately down the line. Number one is we have a few options. We can either allow it to continue writing directly (t: 1280) to our local machine. And by that, I mean it's just going to write like a folder in here or something with like, let's slash project one, right? (t: 1290) Or I guess currently it just kind of like writes HTML files in my root directory, which isn't exactly how we want things to go and isn't very easily like productizable or scalable (t: 1300) at all either. And that plays an issue when you try to route into the results. We can try to get that, but there's going to be some, some clunkiness in like getting the website to open the website (t: 1310) it just built. That's going to be one issue that we can solve pretty easily actually. So we'll probably tackle that next. (t: 1320) Can you hook it up such that the prompt inside the text input is sent to the function we wrote that uses (t: 1330) Cloud Code SDK to generate code? So yeah, let's go ahead and tell it to hook everything up. Oh yeah, that's great. That's amazing. Because before it tested itself, it made tic tac toe and it worked. (t: 1340) And we also created this front end. The problem is that they're not connected. We need it to be connected. So the function that we made is a standalone function that lives right here. (t: 1350) It's going to go ahead and move that into our website. Amazing. I see it doing right now. And then that way, when the website sends that, it will be able to make an API call to that. (t: 1360) Yeah. So update the front end to call API to display messages, add message display components. So yeah, it's got a few things to build out. This is actually, it's called a device. It's called a device. It's called a device. It's called a device. going to earlier into this session, we saw all of these, it's going to try to (t: 1370) parse these and render these as well as something ideally more readable than this, this is pretty ugly. So I kind of gave it that task to kind of hook up the first thing that we (t: 1380) built, which was the function that, you know, we've tested and proved can build tic-tac-toe and hook that up to the button inside of here. Right. Yeah. (t: 1390) Amazing. And then, so what I understand is this becomes like chat on the left and then it becomes the website preview on the right. Correct. Okay, great. Yeah. We can tell to do that too after this. (t: 1400) That is so cool. So once again, it was trying to go into the website and run the dev server. And once again, that has an issue because it's terminal is shorter lived than itself. (t: 1410) So every time it tries, I try to open it after it runs it, it's not actually open. Could you put like a rule in for your, like whatever the longer memory is called to that says like always just tell me when to run the dev server instead of doing it. (t: 1420) Yeah. Yeah. That would probably, this is a great example of something that would actually belong in like the longterm. So like if I want to do it in the longterm, I could just save that to user memory. (t: 1430) Okay. I could definitely save that to user memory. You're right. I'll just save that to longterm memory. Good to know. Great. Thanks. I'm gonna restart this dev server. I don't think it needs to, but we're just gonna see if this is still up and running. (t: 1440) It's done. It's done. So it, it was trying to start it earlier. So make me connect for, let's see what it does. It's currently loading. (t: 1450) I'm expecting like some user messages or, you know, at least some logs on the backend generating code for prompt. Make me connect for. Amazing. It's seeing some indication of life. (t: 1460) Okay. Um. And it's not immediately failing, so that's good. It's not immediately failing. I have a theory that it will just make me another HTML file. (t: 1470) We could probably embed HTML as an iframe. But what we truly want is like probably for it to go and write its own next website. Oh. (t: 1480) No way. Let's go. Okay. So it actually did run. And it has. It's whole. Like thought process here now. Let's kind of digest what it's given us. (t: 1490) Okay. So it's given us, you know, it's calling tools like reading files. It's reading a bunch of stuff that it shouldn't be reading. Right. So for example, it's reading all of the code for the website when instead I asked it to make me connect for, and then it's, I've created a connect for, for you, but where, so let's see, let's look through the code again. (t: 1510) Do you have connect for anywhere? Uh, let's see. Connect for. We do have a page with connect for now. It's inside of my lovable UI app. (t: 1520) And so this is kind of what I was mentioning before. It's about where the code that is being generated lives. Right. This is about like, this becomes a question about scalability environments and where you want to run code, where you want the code to be created, to be housed and how can you orchestrate everything such that it comes back and shows up on your website. (t: 1540) This is actually like great and all because. I'm pretty sure. I'm pretty sure that we can access this connect for website that we just generated by. (t: 1550) Could you just go to local hosts slash 3000 or connect for maybe or something? I think so. Literally. Yes, probably. Let's try that slash connect for, and we have to, wow, let's go. (t: 1560) That's absolutely correct. It did just go and put a little guy in there or a place of peace. Oh yeah, sure. Nice place. (t: 1570) Another one. Nice. Can you make one of them win? I just want to see what happens. That's cool. Let's go. Oh, yellow. Player two wins. There. Let's go. All right. So it works. It just doesn't. (t: 1580) And it's just basically, it creates a new file in your actual code base, which is not what you think we should do. That's really bad on multiple fronts. You know, I can make it such that some bad code, I can make it say not nice things and (t: 1590) it would just be on my website as a URL. I think there's a lot of other improvements we can make on the middle ground as well, such as telling it, Hey, I want real time messages to be like shown. (t: 1600) Cause you know, what we saw was after its whole thought process, it showed all of its thought process. We want this in real time. (t: 1610) We could say something like that. All the messages shown to us were computer speak. We can make it not computer speak or like friendly, more friendly. We can render more friendly text or only strip out the important parts. (t: 1620) We can also make it open the page that it made. Yeah. So we can do all of that or we can start putting things into Docker images and hosting isolated environments. (t: 1630) Where do you want to take? I don't have an idea of how long either of those would take. I think I will. I think I will defer to you. We should make this as close to lovable as we can in the next hour. (t: 1640) And under that frame, you decide. Cool. I'm going to go ahead and I really hated the process of not knowing what's going on. So I'm going to do a little bit of house cleaning with this code base. (t: 1650) And so a lot of what that means is okay. Great job. Cloud code is a little child. You got to tell it good boy all the time. Great job. It actually does, I think respond better because you positively enforce that what it has done is in the right direction. (t: 1660) So as long as, whatever it's doing, you want to generally follow it up with something to indicate to the LLM that whatever it did was in the right direction of what you're trying to build. (t: 1670) And so that's why I kind of begin this with great job and I'll say exactly what it did right. In this case, you made it such that the website builds a page. (t: 1680) Those a page within itself based on the prompt. (t: 1690) And we noticed that because when we go to slash connect for, it will it's in it's within itself. Yeah. (t: 1700) So we're going to reiterate to itself what it did. This is in the right direction. There are a few problems though. There are no logs in the back end except until it successfully finishes. (t: 1710) And when it begins, I want more logs throughout the code gen code generation process. (t: 1720) Similarly, the messages don't show up until the whole conversation is complete. (t: 1730) I would like these, I would like real time logs. (t: 1740) Ideally, you should just display the user agent message, assistant message, (t: 1750) assistant message, and maybe tool called names. Make sure not everything comes. At the end. Also, I would like you to try and open the website that was created from the prompt. (t: 1760) Not, I guess not the website, but the page that was created from the prompt. (t: 1770) IE last time we asked it to make connect for apps. I created a Git repo. (t: 1780) I want you to create this as a PR. Now we can run this. We'll see if it cooks. And then after that, we get into some more really nitty gritty technical things. (t: 1790) Like we're going to find a way to create an isolated environment. We can do that locally first on our machine with Docker to create this little bubble where all the damage inside can be contained. (t: 1800) And then we can run a bunch of things in that bubble. And then we can put that bubble on someone else's computer using putting up base. But that's like putting on the server, (t: 1810) putting it right. And then we can run this on the cloud. And then the good thing about that is now, if you imagine this, this bubble as a separate new next JS project, (t: 1820) that way it's a full standalone website instead of a page inside of my website. Makes sense. And you can add many files to it in theory. I mean, I think you could technically nest a bunch of nonsense. (t: 1830) That would be insane. But yeah, it would be bloating my website. I don't actually want the lovable website to be changed every time someone comes on and writes a prompt. That's a terrible design pattern. (t: 1840) We'll try to vibe code it all, but. We're going to vibe code it. It's currently doing some Git stuff, making me a Git ignore. But definitely this is more of the, since we're live on podcast and I have previous experience, (t: 1850) I don't need to do research, but this would be a deep question that I would research. Like how do I make it stop editing my code base directly and go create another code base? (t: 1860) Where does that code base live and how do I port it? In what? Yeah. So off the top of my head, I know there are many software companies that provide isolated environments. (t: 1870) I think they're, that's a pretty hot space. They're like the people selling shovels to companies like us who want to build AI, AI, agents that run code gen because it is a problem that we need to solve. (t: 1880) That's definitely like another software like E2B or E2B is a great example. Daytona is another one. They're both kind of built around giving you this bubble that you can do whatever you want in (t: 1890) and then letting you also peek into that bubble and see the code output of the agent. And so that's kind of what we're going to do next. We're going to go and spawn a little bubble using one of these isolated environment providers. (t: 1900) And we're going to go and tell Cloud Code to write the stuff in there and then show me what's in there instead of directly editing my website. Run the NPM run dev. (t: 1910) Okay. Let's see if it has real time better messaging now. So back to UI. Amazing. Make me, (t: 1920) I want this to like run for as minimal time as possible and get into like the like bubbling stuff. So I'm just going to say make me a screen that says hello world. Very basic. (t: 1930) Amazing. Okay. So we're seeing AI assistance doing some action. A lot of things already. Okay. Yeah. So as you can see, Oh, I see. Okay. So as you can see, it's already like, so this is a lot better display. (t: 1940) Let me check if the existing project. Okay. So once again, it told me that it's going to check the existing structure of your project, my project to understand. (t: 1950) Now it's going to go and read through all this junk. I mean, by this junk, I mean like this website itself, which is the exact problem that I don't want it to have, which is why I have no doubt that it's going to go and make me a screen that says hello world, (t: 1960) but I'm going to go and make it such that, instead of on my website, it goes and opens a bubble in the cloud and writes it there instead. (t: 1970) And so to do that, I'm going to go and yeah, you can see that it's talking to itself, but we have an animation. We have some sort of indication that something's going on. I'm going to go do a little bit of research on E2B's docs, (t: 1980) E2B.dev. I think I do have an account here. Okay. We used to use E2B for our vibe code, (t: 1990) but not anymore. I will have to build a new template for it with docs. I'm going to go to Docker and everything. Let's go to docs for E2B docs. (t: 2000) And then I have a rough idea of how this works, but I'm going to like try to pretend like I don't. I'm going to just go and tell Cloud Code to research this. (t: 2010) So once again, the big idea is we need to create these bubbles such that whatever is created there is isolated. It also can peek into it. We can see it happen on our end. (t: 2020) It's exploring. Sometimes it's like Cloud Code really likes to explore a bunch of stuff. So it's like, look, as we can see, it just made hello world, which is this Cloud Code over here actually just made that. (t: 2030) Oh, and it's done. So now if you just do go to localhost slash hello world, probably. It told us this right now. If you can, you can start the dev server and navigate to localhost 3000 slash hello world. (t: 2040) If we do that. Wow. Great. They gave us a gradient. It works. The point is once like it's pretty good because it actually went ahead and told us which website to go to. (t: 2050) And so that's a super small change from it, just making it a link that's clickable. But this is something else. (t: 2060) Cloud Code likes to do a lot of dilly dally searching. It makes it very powerful, but I don't want to wait for all this junk to happen. I'm just going to tell it what to do differently and I'm going to tell it what I need it to do. (t: 2070) So it was trying to gain some context on what this project was. I can go ahead and tell it or I can just put it into my cloud that MD file. (t: 2080) It should have this knows that I'm a lovable clone. Okay. I'll just go ahead and actually save this to memory. So it doesn't need to search too much. We have so far a website that takes in a prompt uses cloud code SDK to write code, (t: 2090) but currently it stores it in our code base. (t: 2100) But currently it's directly modifies my websites code by adding it as a page page. (t: 2110) The next task. We are going to work on going to work on is making the code. (t: 2120) Chen happen in an isolated environment and opening the dev server there. (t: 2130) We're going to keep this in local. So now we're going to tell it e2b. We can use Daytona as well. Honestly, that would be probably pretty interesting because I've used e2b before. (t: 2140) Let's just do a fresh. Like use something you've never used something. I've never used just purely vibe code this thing. All right, let's purely vibe code it purely vibe. (t: 2150) So we're going to be using Daytona to create these little bubbles that people's code will be created and run. And that is the code that will show on the site. (t: 2160) Yeah, exactly. Okay. And so, oh crap. I need to get an API key. So let's get an API key. So I just put Daytona in my Env key and my environmental fireball. So I'm going to tell it let's talk a little bit about how I arrived here to reiterate. (t: 2170) The main problem was that. We have an agent that writes code, but it writes code directly on our website. Instead. We want that code to be somewhere else and we want our website to be able to show that code some written and housed somewhere else. (t: 2180) And so the way that we need this is we need someone else to house the code that our agent writes. (t: 2190) And so we're using Daytona. Daytona is a provider of these little bubbles and we're going to so I'm going to go ahead and tell it. I want to instead make. (t: 2200) The code. Jen happen in an isolated environment. (t: 2210) I have made. I have given you a Daytona key. Inside the dot Env file. (t: 2220) I want you to read the docs on date. Daytona verify that it can do what we want. (t: 2230) And. And. And the end goal is to have the Claude. Code SDK. (t: 2240) Create a new. React next. Yes. Next JS website inside of the isolated environment. (t: 2250) Don't worry about connecting the text input for now. I just want to validate that we can run the. (t: 2260) At. It India even is related to. (t: 2270) Other provides. Is it. To don't have host names. (t: 2277) If you do not know what it is. (t: 2280) Cloud or something. So what. So this. (t: 2290) Is tuner build from that. Stuff like this. So. So. Just. and find which part of the docs it needs, navigate to that and then search that one. Yeah, I'll just turn on plan mode and see what it says. All right. (t: 2300) Big prompt, big task at least. A lot of buzzwords have been going around about prompt engineering. I think a lot of prompt engineering is being clear about the end goal, what to focus on now (t: 2310) and all the things you need to get to the current goal of what you're focusing on now. I think it's good to give some indication of the direction of the end state, but it's most important to frame and iterate to the agent (t: 2320) that we're working on a smaller chunk, but to still have its eyes set on the end goal. So because you see a lot of times when you tell Cursor to do something (t: 2330) that's apparently obvious in your brain and it does it completely wrong, it's because it doesn't share the same end goal as you. And so that's why I like to always give it a little peek (t: 2340) of what the final goal is. But I like to, if you give it like too much to work on, it'll obviously get lost in the sauce. And there's also a difference between the final end goal and then the iteration end goal (t: 2350) or the current task end goal. I want to instead make the coaching happen in an isolated environment. This is the end result, or I guess this is actually, to create a Next.js inside of the isolated environment. (t: 2360) Don't worry about connecting the input for now. Just I want to validate we can run. So this is what I mean by scoping down. The end goal is to have the whole Next.js website (t: 2370) inside the isolated environment be spun up by cloud code. I told it instead, just work on being able to run cloud code inside of the isolated environment first. (t: 2380) Okay, it gave me a plan. Let's see. It's telling me to install the SDK, create an integration, integrate with, initialize the client with the API key. (t: 2390) Yeah, we gave it the API key. Implement Next.js project creation in sandbox, create a new sandbox generation function. That's true. We're going to need that. Update the API routes, test the script, (t: 2400) and see once again, it's telling me to do, this is the end result, right? This is actually really good. Test validation, create a simple test that creates a Daytona sandbox, (t: 2410) runs a modified version, and creates a basic Next.js app. Yeah, and we return to preview your. This is perfect. So we're going to go ahead and say, the plan is great. I want you to go ahead (t: 2420) and focus only on the test validation for now. I also did some research (t: 2430) and found out that you probably want a custom Docker image so that cloud code can run. (t: 2440) So protect your embedded in voorga for the test test. Yeah, we powered up someags (t: 2450) with load days, but let's take this to the CPU, for free for you. So you won't have too reduce theience if you could'tCIHK against ESP duplicated blev by a test with 만sel às (t: 2460) else but if available for our Obama SUS SS OS SK (t: 2470) R isolated environments, which more technically takes the form of a Docker file. So I kind of gave it some direction. I said, Hey, look into this Docker file thing for me. This is what cloud (t: 2480) is telling me that it needs to run cloud code. You should probably like think about that a little, at least. And I want you to start showing me your thought process in code. Whenever it's coding, (t: 2490) I'm reviewing what it's coding. If I see it's going off track, I'll stop it. And at the same time, I'll myself as a human gain more context on the code base. And this is also another reason why being technical is an advantage is because you can run a prompt like this. I can't run a prompt (t: 2500) like this and look at the code and be like, that's good. We agree on good code. I don't know. But the, what's really powerful is as I mentioned before, I've never used Daytona before, (t: 2510) but I, through my technical background, I can get a kind of an idea of what's going on. And if something looks outrageously wrong, I can stop it. So I see it, you know, (t: 2520) creating a sandbox in JavaScript. I see it passing down some variables. Okay. I see it starting the sandbox. I see it testing. If cloud code exists in there, I see it trying to (t: 2530) make a directory for the generated app. And then I see it writing exactly the first thing that we (t: 2540) have written, which is the first thing we did was make it run, right. A single piece of code that can generate code. So it actually copies that, straight into the sandbox and tries to execute it, which is (t: 2550) what I see here. And it's starting to generate a welcome page. Yeah. So it basically creates a script that says, generate me. And then it's going to generate a script that says, hello world. And then it'll generate it. (t: 2560) And then let's see, check what was created. This is pretty good. Would you like to proceed? Yes. Let's just do it. I want, ideally it's doing both of these because I do want, (t: 2570) I think both approaches could work. And this is another good thing. It came to me with two things, test the script directory, create the test Daytona integration script, run the test scripts to (t: 2580) validate Daytona, verify. Great. Yeah. Pretty excited actually, because this is getting into, like, this is really cool. This is getting into like some like senior architect, like senior (t: 2590) engineer, like system design architecting. So did you want to try with cursor real quick? The screenshot? Yeah. Yeah. Let's give it this copy. I just want to make sure that this is (t: 2600) actually the page that I wanted to edit. Yeah. Handle generate. I want you to make this landing (t: 2610) page look more like lovable actual landing page. And so. Let's just give it a real landing page. I see that it is having difficulties with Daytona. We could (t: 2620) intervene here and tell it to scope down even more. I see it failed to start a sandbox. So we (t: 2630) could say, Hey, I want you to just focus on starting a sandbox. It should be pretty easy. We'll see if it continues to fail. You know, it said the test failed. It sent three agents in (t: 2640) parallel to use a bunch of my money to go and figure out the issue. And then now it's removed my friendly comment. Fine, whatever. And then it's adding some more code. So great. It's finally getting back to work (t: 2650) after making me wait for a good five minutes. Meanwhile, 03 has also promised me that we have (t: 2660) a better landing page now. So I'm just going to go accept all on here. Close this thing again. And then actually see if we do have a better landing page. Where are you local host? What the oh my (t: 2670) God. First of all, this is a war crime to have this emoji here. Okay. Thank you. Enter thumb below the text input. (t: 2682) Okay. I like it. And then can you make the, you just make the other one tiny, like one X, (t: 2690) which one is very small actual sites. So it's telling me that time that the core functionality works generated a sandbox with this. So tell me how I can preview (t: 2700) the dev project inside the sandbox with the ID. Oh, not lovable. Sorry with this ID. (t: 2710) Cool. And let's just accept all of O three's changes again. Be optimistic. Okay. Like, (t: 2720) yeah, it just needs to be like 10 times larger, but we, okay. We're getting there. Good job. Lovable design is actually pretty nice. This is this very depressing, but we got the gradient. (t: 2730) Actually the gradient is the same. It's just so small and there's just random grading here. And here, it's trying to run it and it is failing. Well, let's see, so it's telling me it worked, (t: 2740) but I have no way to confirm. And this is, apparently cloud code is telling us that we have a bubble and inside of that bubble, (t: 2750) we do have a next JS project. The only thing is I can't view inside of that bubble. And so now we're trying to figure out how to view inside of that bubble. So currently we're debugging a lot (t: 2760) of things about does the bubble actually get spawned? Is there a next JS project in there? And How can I. at that next JS project. (t: 2770) After we get that sorted out, we're going to go back, inject our agent again, which is actually just Cloud Code SDK, and tell it to build what website we want. And then after that, (t: 2780) we're going to hook it back up to our website. And then we're going to have a website that creates a bubble with Cloud Code inside of it. And Cloud Code can go crazy and code up our website. It will expose that website (t: 2790) so that people can see it from the outside. And then we'll be able to see the issue. Oh, not the issue. Do you want to check your text? I sent you a gradient (t: 2800) that you can use as the background. I think it'll look really good. Can you please make this image the background of the web app that we've created? This is going to be behind the text input field. (t: 2810) And then for the text input field, keep it exactly the same. I just want you to make it really dark, like almost black, so that you can hardly see through it. (t: 2820) So this image should be the background. And then, yeah, make it darker behind the input field with white. And then make it as white text as you type. Nice. So I'll let O3 cook on that. (t: 2830) But let's get back to the main project. I see it, and this is an issue that it continues to like to do, where it tries to run it, but obviously its shell inside of itself doesn't live long. (t: 2840) So I'm going to tell it to stop, and I'm just going to run it myself. And then, okay, it failed because it cannot find it. So where is this? (t: 2850) npx-tsx-scripts. Scripts. Where's the script? So. I actually have to cd into lovable UI and then run this. (t: 2860) And so it's created a sandbox. Okay, great. It's creating a Next.js project. Let's see if it actually does that. And so I'm running the script that Cloud Code wrote me to see. (t: 2870) We can look for public. Create sandbox preview. I think it does have public true. Yeah, it does. Create next Daytona.work. (t: 2880) So, I mean, this actually does look pretty much like. Let's see. This is saying, and I'm going to use this as a text input. (t: 2890) But let's see. It's saying, what is our sandbox ID? Important sandbox ID is this thing. (t: 2900) So sandbox that doesn't look like a sandbox ID. Sandbox ID. Okay. Maybe it is. Is there any way I can SSH into this sandbox? So to do some more complicated debugging. (t: 2910) So we have a bubble now. Okay. Okay. Okay. Okay. Okay. Okay. It does seem like we are spinning up these bubbles. (t: 2920) Inside of the bubbles, there do seem to be some resemblance of a website. I'm not very familiar with Next.js. I guess we can see the app and see like what's in here. Okay. (t: 2930) We have page.tsx. Yeah. Okay. Yeah. I think there is indeed a website on the bubble inside of the Daytona server. But the problem is we're struggling peeking into it. (t: 2940) Yeah. Okay. So this is the sandbox running. This is now. Okay. We want to peek into this bubble. So we do have confirmation that there is, you know, okay. (t: 2950) I still can't open the websites. I am getting 503 service temp unavailable. (t: 2960) Can you do some deep research on why this is the case? I am running. (t: 2970) What script am I running? Test Daytona. Simple. Daytona. Simple. I noticed that inside. (t: 2980) What did I notice? I noticed this. The sandbox ID seems to be different from the ones we are getting. (t: 2990) For example, on this website, the sandbox says that this is the ID, which the ID seems to be something like this from the docs. (t: 3000) But we are getting. You you IDs like what am I getting? I'm getting something like this. (t: 3010) Oh, wait. Yeah. So yeah, that was an observation. I made. Please search the web. Think. Oh, yeah. (t: 3020) Cloud code has specific reserve key terms for how hard you want it to think. I'm pretty sure the highest level of thinking is literally ultra think. I don't know. Maybe they were just like having fun. (t: 3030) I'm pretty sure cloud code. Ultra think. Yeah. Yeah. It's actually like the way to tell it to use the most juice. So I'm just going to tell it. So basically $10. (t: 3040) Yeah. Basically, it'll just use as much tokens as it can possibly use basically and try to figure out the issue. So if Daytona's documentation is right, this indeed should be something that gives us a URL that we should be able to see inside the bubble. (t: 3050) So we were able to confirm that a bubble was created. Code was being written there. (t: 3060) But we can't see inside the bubble. And that was because cloud code did not read enough documentation. Just like the average engineer. Right. (t: 3070) Right. Right. Right. Right. So I had to read some documentation myself and I found out that it's not using the preview link. It is not using get preview link, which conveniently returns to you a URL and the token to access it. (t: 3080) Let's run that test preview URLs. Okay. Now it likes to break things down. It broke itself down into smaller tests. (t: 3090) So now it's creating another test to test if this preview URL thing works as it promises. Right. But once again, it's running this thing inside of the bash of itself. (t: 3100) I'm going to run the script myself. Creating sandbox, creating next JS app. Okay. Done. (t: 3110) Let's just read what the script does. So now we created another bubble. The sandbox is the bubble that we were talking about. Right. The isolated environment. Right. So now we have a new app. (t: 3120) We're going to have it create me a next JS app inside of the app. It's going to go and create a custom page. I preview. It's going to install dependencies and then it's going to start the dev server, which is what we're trying to peek into. (t: 3130) Right. Amazing. And then we'll get the preview link to that server. Yes. And that's what we'll be able to see. And ideally we could render that in the site itself in the front end of the lovable site. (t: 3140) Yes. Okay. Amazing. And that's probably like something similar to what, how they do it. I mean, I don't know how they do it, but. Oh, it's here. No way. This is the website. (t: 3150) And so they're telling me that this dash preview is a website that it built. Preview works. Let's go. Okay. So now we can finally look into the sandbox. Amazing. So we have all of our pieces together. (t: 3160) Now we have a way to create bubbles on someone else's cloud. We have a way to write code inside of that cloud and we have a way to trigger it through our website. So let's just add a new one. We're going to add a new one. I think we're going to add a new one. It's going to be a new one. It's going to be a new one. It's going to be a new one. Let's see. Okay. (t: 3170) So now we're going to add a new one. We're going to add a new one. Ideas for this. I'm going to go with cloud. So I'll show you how to do it right away and then give you some advice, keep the lines clear. Okay. Let's go ahead and hit save. So you can see this has been edited out of your program. So we can see there are a number of neuron helper habitat problems and issues that we (t: 3180) want to cover. So we're going to delay the restoration of the neuron helper habitat. Everyone in the school voice is still asking us what a neuron helper habitat is. It's only been two or three months now. earn, this will be up to a hundred and maybe again, 50 million. Yes, please send that tocekos perspective and this is what's on our website. So that one should be up to 150 million. I have set this up here to the right amount of toxin. The second is we have to recommend forários. (t: 3200) look at the next JS project. And so actually, hold on. (t: 3210) Okay, well, anyways. So to reiterate what just happened, we finally were able to peek into the sandboxes, which is the bubbles that we were talking about this whole time. (t: 3220) And you blamed Daytona when it was probably Cloud Code's fault, maybe? Yeah, yeah, yeah. It was probably Cloud Code's fault. Daytona actually had decent documentation. I found it pretty quick. There was a function that Cloud Code refused to use, (t: 3230) probably because it did not find the right thing to read. So once again, that's, you know, Context management. A context management mistake on my end, to be honest. So we found that though, we did some digging. (t: 3240) We gave Cloud the context and it fixed it immediately with the last script that it ran, which was, what was the script? Oops. The script was mpxtscripts.testpreview. (t: 3250) So I'm currently updating the markdown of cloud.md. Remember, this is the kind of memory that it has (t: 3260) attached to every project. So it's a prompt that it kind of gives it structure. And this is how earlier we were mentioning how it's good to give the agent a picture of the final output (t: 3270) and kind of what has happened so far. This is kind of like a conversation history. I'm updating it a little bit so that we can type less every time we submit a prompt. (t: 3280) So I said, the goal is to make a lovable clone. We want to use Cloud Code SDK. We have a website that takes in a prompt. It uses Cloud Code SDK to write code, but currently it modifies the website (t: 3290) by adding it as a page. We want to make this happen in an isolated environment. So now we have created a way to create sandboxes, which is the bubble, using Daytona to preview them and preview them. (t: 3300) Get preview. What is it? You have it on your clipboard too. Yeah. Using the get preview link function, (t: 3310) the script confirms this. OK. So this is our progress so far. I'm going to save it to its memory. I'm going to go back to good old Cloud. (t: 3320) I'm going to say, wonderful job. We now have a successful, we cannot successfully look at the Next.js project. It does have a preview page. (t: 3330) Scripts.js worked perfectly. Oh, yeah. And this is another thing. We kept on noticing that it tries to run the script, (t: 3340) but it can't do anything. So don't try to run the script with your own bash tool. OK. (t: 3350) Write the scripts going forward. And this is probably project, like, let's see, what is this, preferences. (t: 3360) Don't try to run the script on your own bash. Write the script and tell me how to execute it, asking me for its outputs instead. (t: 3370) So let's just leave that there. It does have a preview page, scripts. The script worked perfectly. (t: 3380) Now. I want you to go back and make it such that I can create a Daytona sandbox and, using Cloud Code (t: 3390) SDK, build a custom website based on a prompt that I pass (t: 3400) in. Let's keep this as a script for now, but we will implement. It's into our website as soon as we confirm that it works. (t: 3410) Amazing. Great. It's created a script that creates a Daytona sandbox, (t: 3420) installs Cloud Code, generates a website, installs the dependencies, and gives me the preview link. That sounds perfect. (t: 3430) That supports Markdown support and dark theme. That's a great idea. Let's just do this. Basic usage. And then notice that it wants me. I told it that I want to run the script myself, so it gave me some options to run the script. (t: 3440) And that's a great idea. Thank you, Cloud Code, for that. I will go ahead and do that. OK. Create a blog website with Markdown support and a dark command is required. (t: 3450) Let's see. Cloud Code. Running Cloud Code. Command is required. Sandbox is still running for debugging. (t: 3460) This is going to be a hassle to debug because, oops. Wait, is that permissions issue or no? So we don't know. Because this is a. The only error that is thrown, command is required. (t: 3470) We can go ahead and run this. I have a good feeling that we can get through this, though. (t: 3480) So I tried to run the script asking it to build me a. What did I ask it to build? (t: 3490) A blog website. This is the output. How can we debug? The issue. (t: 3500) Remember, the sandbox, which is the bubble, is still alive. And I can SSH into it with a terminal. (t: 3510) Oops. Paste that. OK. So now it has the error. It will either give me directions to debug, or it will try and solve the issue. (t: 3520) So the error is command is required suggests there's an issue with how we're executing the command. Let me do a debugging script. OK. I think I know the issue. Once again, I might have to pass down this to Cloud Code. (t: 3530) Let's actually just tell it. I don't think you need to debugging script. First, I want you to read this documentation (t: 3540) and make sure you are using Daytona properly. And so this is actually a common issue with the fact (t: 3550) that the LLM can browse the web. But since Daytona and all these new companies, we're not going to be able to do that. I guess it's just a timetable thing. (t: 3560) I don't think it's going to be easy. It's probably not going to be as easy as we're used to idea writing at this point. Working on (t: 3580) the formula, definitely. It can be challenging. And also, I want you t to see how many of these JIM and S, even nascent, they've made a lot are custom-made. Okay? Our hypothesis was correct. (t: 3590) It was not doing execute command properly, which makes sense because our error was actually something like command is required, so hopefully this will fix it and we can run this again. (t: 3600) We can check on the design now. Accept these changes instantly. And drumroll, please. Oh, oh, God. Nope. (t: 3610) 404, gradient.png. Can you please, let's see if it's telling me to do anything. Do I need to restart you? No, no, no. (t: 3620) Because it serves everything in public, the background should not automatically load once it's there. Create a public folder. Okay. It doesn't exist already. Drop the provided image. (t: 3630) Okay. So it's telling me to create a public folder in here. New folder, public, and then move this into here. (t: 3640) Okay. Now will you work? No? Okay. Drop the provided image. Because the next JS serves everything in public on the site folder, (t: 3650) the background will now load automatically. It's not. Oh. It is not. Oh, wait. Gradient is in public. (t: 3660) Yeah. I still don't see it. Slash public. At slash public. I put the image in. (t: 3670) Yeah, yeah. I really don't see it. Okay. Let's see. Maybe the gradient design is over the image? Oh, yeah. Oh. Oh. Oh. Oh. Oh. Oh. Oh. Oh. Oh. Oh. Oh. Oh. (t: 3700) Oh. but it says installing Cloud Code SDK. (t: 3710) Let's see exactly how it is it's installing Cloud Code SDK because something is definitely not installed properly. Generate website simple and generate website in sandbox. (t: 3720) Which one am I running? Generate website in sandbox. Undefined. It definitely failed to install this somehow. Install Cloud Code? (t: 3730) Yeah. Interesting. So let's see. This is one way we can debug this quite simply by going into here. Oops, I mean, I guess. (t: 3740) Okay, let's see. Sandbox ID is this one. We can go ahead actually and go into Daytona. Sandbox started with F9, right? (t: 3750) Nope, F5. So we can just go connect here, and we can see what happens when we try to run the command that failed, which is sandbox. (t: 3760) Oops, no, it's going to be here. Let's see. Let's see if this works. Okay. I'm on the sandbox. I'm in the bubble right now. I only have a terminal, and I'm going to go ahead and feed Cloud Code some more context (t: 3770) on what exists inside of the bubble to hopefully help it debug what is going on. Amazing. Okay. I'm just going to go and take all this stuff and give it to Cloud Code as well. (t: 3780) This is the error. It seems like Cloud Code is not being imported or downloaded properly. (t: 3790) I also SSH'd. Okay. So let's see. Okay. So if I go to the sandbox and did some snooping, (t: 3800) this is some more context to help you debug. So this is telling Cloud Code that there's more information available. (t: 3810) We got a little further, right? No, I still think it's the same Cloud Code. Okay. No, no, no. We now have Cloud Code process exited. So we have Cloud Code now. (t: 3820) I think this might be because it's sandbox is blocking out. Perhaps. Right. Yeah. I think there's something we can do to debug this. How can I easily check if a sandbox has access to the internet? (t: 3830) I can just ping Google. Oh, no, it does have internet access. Okay. So my initial thought was that maybe the bubble doesn't have internet access. (t: 3840) It needs internet access if you want to run cloud code. But you ping Google and it does have access. Yeah, I can also ping api.anthropic.com and see if this one. (t: 3850) Yeah, this one is getting a response. What is ping? Ping means just like check if it's running? Yeah, it just pokes the website and sees if it gets a response. Cloud code exited with error one. (t: 3860) And let's see. Nope. Wow. Now, so this is the drawback of getting AI to do everything for you. (t: 3870) I have no idea what is wrong. Or I can only like believe that. Only context managed. I can't create my own context. I told it, look, use the right documentation. (t: 3880) And it's reading the web. Again. So hopefully this time it'll figure something out that actually works and we can have lovable clone. (t: 3890) Cloud code has actually used 70% of its context. I'm going to go ahead and compact this since I don't care about the specifics. Because if you remember, we are trying to create a proof of concept script that uses Daytona to spend up an isolated environment where we want to run cloud code to create a next JS website. (t: 3910) So there's a broad stroke. Currently, we've been failing and a lot of the time is because we are not implementing code execution properly inside the sandbox properly with Daytona's SDK. (t: 3930) Let's create another script. But going forward, I want you to create the script inside of Daytona. I want you to execute it. (t: 3940) And I also want you to. Install and log if you installed it by checking using LS in terms of where it should be installed and do not install dependencies globally. (t: 3950) Also, I want the script to be able to take in a sandbox ID. (t: 3960) So if it's already spinning up a sandbox that exists, don't create a new one. Just use the sandbox that we already have. And so I'm going to go ahead and once again, feed it some Daytona docs. (t: 3970) Process execution running commands. Yeah, running command the code execution. Let's see. Run code. (t: 3980) Run commands. I am going to feed it these docs. I'm going to say. I want you to actually create the file inside of Daytona. (t: 3990) And not just pass in the whole JS as code to execute. (t: 4000) Okay. Let's see. Let's see. Let's see. Let's see. Let's see. Let's see. Let's see. Let's see. Let's see. Let's see. Let's see. Let's see. Actually, I actually think maybe it would be a bit better. So now, I'm just going to try again. I want you to make, what's a good website idea? Website idea, please make a site that lets me upload an image. (t: 4010) And I can resize it, or crop it by adjusting a slider or some shit. (t: 4020) Let's do it. Make me a site that lets me upload an image and allows me to resize it. And so great. Currently, we have a script that runs generate website (t: 4030) and now it's going to try to generate that website. Good. So this is super exciting once we confirm that this works We're just gonna hook it up so that our lovable clone website runs this will look like lovable. Yeah. Yeah, where's our lovable website clone? (t: 4040) We haven't seen this in a while this bad boy that we spent two hours on we're good, dude We're hitting the homestretch here. That's I currently (t: 4050) don't See the background image. I think This might be because you are covering it with other stuff (t: 4060) The image is replacing that other stuff Yeah, make sure that the image is shown and nothing is on (t: 4070) top of besides the UI of the The text input field right text input field (t: 4080) Amazing okay great and this thing also just finished generating a website and so now it's Creating some link here. Ideally. Yeah. Yeah. Yeah amazing. Ideally ideally. Yeah (t: 4090) I'm gonna go ahead and install dependencies After that it should spit out a link. This is crazy, bro. This is actually insane Man, once again, I think it's caught on the server (t: 4100) So it never fixed the issue, but we do conveniently have a script to just solve that issue right here. Oh, no. No it did (t: 4110) amazing Let's go. All right. So now this is no way. Let's go. Let's go. We can go and resize this image Oh ingredient. Oh my god. It just works (t: 4120) Wait, what happened? I'm recently. Oh, I see. I see. I see. Let's go. Oh, okay I can make this like I don't know like what was the original image size? (t: 4130) But yeah, no, we have an image resizer website now. Let's go. Let's go. Okay So now the main functionality works. We just need to put it on our app. Yeah, go see if the styling work (t: 4140) Local oh, yeah. Yeah. Okay true. Let's try this thing except right? Let's see switch the background div to be in line Yeah, the only element there above do I need to accept you? (t: 4150) or anything Maybe cool Hey, no way. That's our website. No, it's not. Oh, that's ours. Let's go. Yeah, it is. All right (t: 4160) All right. Can you can we let's do one more styling prompt? We need to get rid of that weird thing on the left there what this thing? Yeah, that thing's nasty. Just get rid of it the (t: 4170) public loss and public button on the left of the text input Yeah, and then make the background of the text input black. Just make it fully black (t: 4180) Make the text and put Background fully black. Yeah, like opacity zero. I mean like what (t: 4190) Open up lovable. It's down below bro. Ours looks better. Oh, they have the like the nice typing. That's okay We don't need to edit that now, but like all right, come on son. Yeah, this is my in you asked Claude to (t: 4200) Connect it to the front end. So like in theory it might work, right? Oh, I've not asked it Oh, you haven't done that yet. So let's go and see that (t: 4210) This script was generate in Daytona dot TS So now we have a script which is the piece of code that we can run that spins up an isolated (t: 4220) Sandbox now isolated environment or sandbox or bubble whatever we want to call it and then inside of that bubble (t: 4230) Claude code SDK will go and create a next.js website and then that website we can peek at using the link that we get From here preview URL great (t: 4240) This script is working great As you can see if we open the script, we actually have the image resizer that works we can write it like a graphic It's a little ugly, but it's okay. I mean MVP. We just wanted something to work. We have something working now (t: 4250) We have this script now we can go back to Claude and we can say great job (t: 4260) this script successfully ran a function that generated a next.js website and (t: 4270) return a preview URL after starting the dev server I want you to make the button make the text input submit the prompt and (t: 4280) hook it up to our lovable clone after the generation is complete I want you to display the preview URL and I want you to display the preview URL (t: 4290) and I want you to display the preview URL and I want you to (t: 4300) display the preview URL and we can specify where or actually it doesn't matter we can resize it later we can just say like open the preview URL for now and then okay and then we can (t: 4310) Yeah, we can definitely try to do that too Now let's update the front end to use the new API to handle the preview URL Great. Now, let's update the page to use the new Daytona API to handle it. Okay, great. So cool. I'm super excited (t: 4320) This is honestly a pretty challenging task Cloud Code did stumble along the way (t: 4330) I think there's a lot of fun in it I think there's a lot of fun in it I think there's a lot of fun in it I think there's a lot of factors that went into it We did manage to course correct it but Daytona is super new technology Test the full integration (t: 4340) No stop I'ma just stop it. Don't test that I want to test it myself Okay, so this is the real lovable So it's ready to test (t: 4350) Wait, so it's ready to test Yeah. Yeah. Make me a modern blog website that supports markdown support Oh, wow Let's go. Okay. (t: 4360) Okay, all right, all right, let's go. Please. It's verifying, it's generation script, generation script verified, create a modern website that, (t: 4370) a website with Markdown support. So this may take several minutes. Oh, running Cloud Code. So yeah, now it's running Cloud Code generation, waiting for a server to start. (t: 4380) I think we're actually really close. Oh, look, now it opened. Oh, it failed to do a prompt. All right, that's all right, that's all right. Auto prefix it, okay, it definitely failed, (t: 4390) but the idea is there where we have a sandbox. Cause if it's a preview, I mean, think about how many times when you use these vibe coding tools where you ask it to make something and then that is the view on the right side of the screen, (t: 4400) which is that preview error. That is true. That happens all the time. That is true. So we can ask it for something simpler, maybe like make me a landing page. (t: 4410) Just make me a landing page for my banana company. Make me a landing page for a company. That sells bananas. (t: 4420) Okay, I would like to see the Cloud Code agent trajectory. (t: 4427) I'd like to see the Cloud Code agent trajectory. (t: 4430) Currently it just says this may take a few minutes and goes silent. (t: 4440) I would like to see which tool calls are being used and here is an image. Oops, yeah, I'll just give you them all actually. (t: 4450) All right. Yeah, okay, yeah, that works. Go cook. So now that guy's cooking. (t: 4460) Now at the same time, we are also cooking here. I see. Oh, see look, oh, world's. All right, making progress, let's go. The world's finest bananas. You know why? (t: 4470) We sell the classic yellow bananas for $5 a pound. The platinum premium for $4 a pound. The platinum premium for $4 a pound. Why does that get cheaper? Okay, interesting. (t: 4480) 100% organic fast delivery, sustainably sourced. Wow, you know, I'd like more graphics, but that's fine. Honestly, this looks like not a terrible landing page. (t: 4490) I mean, it's a pretty bad landing page, but at least it's a landing page. It's a pretty bad landing page, but it's pretty impressive that, you know, we did it. (t: 4500) Okay, let's see. Do the image resizer one, but like make it like adding border around the image. Website that. That allows me to upload an image and it adds a border. (t: 4510) And I can change the thickness of the border. I should be able to change the thickness of the border. (t: 4520) Or and the rounded edges. Rounded edges. Corners, corners. Rounded corners and the color. (t: 4530) Okay. Oh. No way, bro. That's crazy. All right. It's it. Let's go. Yeah, yeah, yeah. So definitely like a little janky. (t: 4540) Yeah, yeah, yeah, yeah. Like what the hell? That is awesome. Let's go. Yeah, you can see exactly what the agent is thinking now. (t: 4550) You know, it's, oh, actually, no, no, this is the prompt. Okay, it goes silent again. But this is kind of the prompt. Make me a website that allows me to upload an image and add border to it. (t: 4560) I should be able to change the prompt. I don't know if it managed to successfully catch like the messages that are being. That's okay. That's okay. If it renders on the side, I'll be like super hyped. (t: 4570) I definitely know. What does it go? Scroll up slowly on the right side. Yeah, yeah, right there. What is that? Oh, ending up preview. Okay. It's just centered in the whole thing. (t: 4580) Yeah, we definitely need to tell it to like keep this max height. I can't fix that. (t: 4584) It's still working. (t: 4590) And then. Oh, it should automatically. Render in the right side. Ideally, the chat should take up like 30%. And the website preview should. (t: 4600) So please also fix that. After these fixes, we can generate a cool website. (t: 4610) I don't know. Cool website. Little app. Think of a little fun website. Fun app that will render on the screen. It looks pretty good. And it is stored not in your own code base. (t: 4620) It's stored in a container. Or a isolated environment. Let's see if it works. Yeah, all right. Generate something really cool. So let's build something with our lovable clone that we created with (t: 4630) cloud code that uses Claud code. Should we tell it to generate and create lovable builds. Yeah, let's tell it to do exactly what we did today. (t: 4640) So was something with lovable no avant with Cloud code. All right. Build something with lovableabe We can put that in the subtitle and we can move this down into like (t: 4650) H3 Build a website You can think about this A Pomodoro timer (t: 4660) What about A link tree Yeah link tree I like it Alright let's do that Build me a personal link tree (t: 4670) Personal link tree That allows me to upload my links With name And I can name each link And name each link (t: 4680) Make it sleek Sleek Make it sleek Modern and beautiful (t: 4690) Alright Build me a personal link tree That allows me to upload my links And name each link Make it sleek, modern, beautiful Cool Amazing let's try it Let's run it Alright our lovable clone is going to work (t: 4700) Nice work good job There it is And so can you scroll on the left side right That's how it should work Yep So cool So now, build me a personal link tree (t: 4710) That allows me to upload my link And name each link Amazing And this is a Wait wait run it on lovable Run the same Copy the prompt Run the same prompt Oh you probably don't have a lovable account (t: 4720) I don't But I mean Do I have to pay Do I not get a single free one Yeah you get a free one There you go Face it Great Okay so we're running it on lovable (t: 4730) Now go back to our other one So this is the lovable that we made You know Similar Pretty similar I'm surprised they chose Such a (t: 4740) Gray Yeah like Maybe there's something there though Like they want you to like Get bored by it And then like your sight captivates you Maybe (t: 4750) Yeah maybe Something like that Yeah I do have to say Ours is better Yeah No I like a lot of what lovable has done Me too It doesn't feel very crowded given how many (t: 4760) I know they have a lot of features though Like oh okay These are cool These are cool quality of life things We failed to get our agent to properly speak to us That was something (t: 4770) It's not too difficult But I don't feel like reading anymore Daytona docs today This is cool Let's go back to ours and wait for it to load Our project is nearly done (t: 4780) Okay Yeah Oh Oh Okay so we have these links (t: 4790) You can paste a link here You can manage links Yeah so wait can you paste can you add one Yeah Like manage links What does this part me do (t: 4800) Oh it took you to the link How did it know what my No no there's a manage link button on it So Manage links Okay so you can manage the link Okay And (t: 4810) No this is pretty cool We can edit our own links So I can go ahead and grab my github actually Yeah just grab anything Let's grab this Young Kihan So this is lovable's and this is ours (t: 4820) All right you know what They look I mean the gradient's like somewhat better I mean fine you know it's harder to read their text But yeah I didn't want to manage one of the links here (t: 4830) I mean I'm just genuinely shocked at like how similar they are Yeah I mean we've only been working on this for two hours So Yeah I know I mean crap (t: 4840) Is this lovable's Oh this is lovable's Yeah yeah Yeah hub link Yeah Paste the URL Oh I mean that's it I guess Sure (t: 4850) You know what they're similar they're similar But the point is in two hours we created like a respectable clone I mean go if you want to go over to ours Yeah Hit add link Oh yeah (t: 4860) Wait how do you reset it so it doesn't look Oh yeah yeah back Back to links Yeah yeah yeah So I mean yeah we created this link tree This is great We also created on lovable Oh it does It takes you to the link (t: 4870) Let's go Very neat Let's see if lovable's takes us to the link Yeah it does Cool cool Amazing Yeah And yeah that's what we created in about two hours using Claude code (t: 4880) So in summary within cursor we ran Claude code Like we used Claude code in cursor and we used Claude code to build a lovable clone that used the Claude code SDK (t: 4890) So Claude code built Claude code which we wrapped and called it lovable clone And then we used lovable clone to build an app (t: 4900) Build a link tree app That's what we did I didn't write a single line of code Yeah you didn't If you think about it I just vibe coded this whole thing Yeah you did vibe code I mean there was there was invest you looked through code and documentation (t: 4910) But like soon you know Basically the limitation was Claude code not looking through the docs well enough (t: 4920) Yeah And as soon as that gets better it probably would have cut a lot of time out of this I think honestly this project and you know I kind of did say this going forward that it is a little bit more complicated to have separate systems and spin up your own isolated environment and have code development be done there and then port that over to our website (t: 4940) That is definitely more complicated It is like a pretty powerful thing that we built Yeah That we managed to accomplish without me writing a single line of code I think you're absolutely right about how once you know these models are going to be better at searching for docs online themselves this could definitely be a problem that it solved (t: 4950) Currently I always do think the skill of navigating the LLMs to go in the right direction will be a persistent skill as well as finding projects that people will actually like users would actually want (t: 4970) If we clean this project up we will actually throw the link on GitHub We will put it in the description down below if you want to like test it out yourself and yeah we will see you in the next video

