---
title: Spec-driven Development with <PERSON>
artist: <PERSON>
date: 2025-08-20
url: https://www.youtube.com/watch?v=nkDfUobnxH0
---

(t: 0) Have you vide coded and ended up spending more time debugging than actually coding? Today I'm going to share my solution using spec-driven development with Cloud Code. (t: 10) According to <PERSON>, a spec file is a blueprint for software that describes what you want to build. Think of it like one abstraction layer above code. So here's an example of a spec file (t: 20) that contains an overview at the top and then basically a series of steps that we want to (t: 30) accomplish for this specific feature. So when we create a spec file, it's best for us to split it into separate independent testable phases so that when we're going through the implementation, (t: 40) we don't have to just implement it all at once. We can take checkpoints along the way to make sure (t: 50) that everything is in order. So that's a good way to start. So let's get started. So you need to tell <PERSON> to do this. A couple other things I tell <PERSON> is that (t: 60) create a file for each phase and name it accordingly. And then also in the phases, they should be in dependency order. So if you complete phase one, it shouldn't rely on any of (t: 70) the other phases so that you can test it. So I'm going to show an example of an authentication (t: 80) refactor that I'm doing for my portfolio site. So I actually already went through and created these spec files with <PERSON> yesterday. So I'm going to pick up from there. So you can see it created like a (t: 90) high level readme file that points to the other files. One thing I noticed is that this (t: 100) implementation order spec file contains some duplicate information with all the phase files. (t: 110) So I went ahead and asked <PERSON> code basically like, do we need that file at all? And then it identified some content that overlaps and then came up with the recommendation to (t: 120) simplify that file. So I'm going to go ahead and do that. But as you can see, it's a paradigm shift (t: 130) from actually coding to clarifying and polishing these spec files, which are going to make the (t: 140) implementation seem much more complicated. And then I'm going to go ahead and create a new gamemode. seamless. But right now, it's the coder or like the software engineer has kind of like, (t: 150) taken on a new hat of becoming more of a designer and less of an actual coder. Okay, so I just finished those changes. And now this file is a lot shorter and easier to follow. (t: 160) Another thing I'm going to do before I start reading the spec files is ask cloud to perform (t: 170) an audit of the spec files to make sure that nothing is missing or if anything, if anything needs to be changed. It made a few suggestions. So one is adding a current status (t: 180) to the main readme. One is adding a current state markdown file that contains everything (t: 190) that currently exists in the code before this spec is to be implemented. (t: 200) And then some other information to implementation order. So yeah, it cloud is basically just coming (t: 210) up with a plan to further polish its plan. After reading through the phase one spec file, I'm going to get started. So first thing is I'm going to clear this session because (t: 220) once I start on the implementation, I want to keep the session as (t: 230) much in context as small as possible. I'm going to implement each phase one at a time, starting with phase one. While phase one is implementing here, I've got a separate tab over (t: 240) here that I'm using to ask questions about the next spec document. After going through phase two (t: 250) spec file, I found something that could be changed that uses something that I already built. (t: 260) So this is just an example of editing a spec file on the fly while waiting for another one to complete. (t: 270) Phase one has now been implemented. But after running the test suite, there are a lot of failures. So I'm going to have Claude fix the failures. But first, I'm going to clear the session again, (t: 280) because I think it's going to be more successful if I do. (t: 290) So I'm going to ask why the test suite is not working. I'm going to ask why the test suite is not working. So I'm going to ask why the test suite is not working. I'm going to ask why the test suite is not working. If the tests are failing, if it's due to our recent changes, and then I'm just going to remind Claude that we just completed phase one, so that this new session has the context that it needs. (t: 300) The test failures are directly related to the phase one implementation. Looks like there were some imports that were left out. So we're just going to have Claude fix these. (t: 310) My guess for why the phase one implementation broke these tests is that the context, (t: 320) from running the phase one, was too large. Because after I had completed all the steps in the spec file, then I asked it to fix all the back-end tests, and I think at that point the (t: 330) context was just way too large. So it's always a good idea to clear the session whenever you can. (t: 340) And when you do, to remind the next session of what was completed in the previous session. (t: 346) I realize that some of these changes, (t: 350) that Claude is making now to make the tests pass might already be planned for future phases. So I just asked to make sure that the changes it's making now are not planned in the future. So (t: 360) it looks like some of the changes are. So now Claude is going to reconcile what he's doing now (t: 370) with any future phases. So I guess what I learned is that whenever you make changes that are not (t: 380) strictly part of the phase spec document, you need to make sure that those ad hoc changes are (t: 390) not already covered in future phases. After implementing all the different phases, I'm going (t: 400) to open up a pull request in GitHub. And then I'm going to go ahead and open up a pull request in GitHub. And then I'm going to go ahead and open up a pull request in GitHub. And then I'm going to perform one final pass through to make sure everything looks good. And all right, that's it. (t: 410) Thanks for watching.

