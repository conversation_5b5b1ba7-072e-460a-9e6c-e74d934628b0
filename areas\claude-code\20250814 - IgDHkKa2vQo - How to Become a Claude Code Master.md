---
title: How to Become a Claude Code Master
artist: <PERSON>
date: 2025-08-14
url: https://www.youtube.com/watch?v=IgDHkKa2vQo
---

(t: 0) Over the last few months, ClaudeCode has added a ton of new features that aren't talked about or documented anywhere. In this video, I'm going to go over five of those features that will (t: 10) immediately start giving you value. They'll help you save money, get better performance, and create way better applications. If you stick with me until the end, (t: 20) you'll be using Claude<PERSON><PERSON> better than 99% of people. Let's get into it. All right, so let's go over a really simple one that's going to bring you value immediately, (t: 30) and that is the new model picker you can use that will automatically choose the right model based on what you're doing. As you know, right now, you choose your model by doing (t: 40) slash model and then typing in Opus or Sonnet or whichever one you want to use. If you choose Opus, yes, that is the best model, but it's going to eat a ton and ton of credits (t: 50) from you. If you use Sonnet, that's also a really good one, and you're going to save money, but it's not going to be as good at certain tasks. So get this. You can now make it so it automatically switches based on what you're doing. You're going to type in slash model (t: 60) and hit enter, and then now you're going to use Opus plan mode. That's this brand new mode right here, number four. You're going to scroll up to it and hit enter, and basically what that's going (t: 70) to do now is whenever you're in plan mode, it's going to use Opus, and whenever you're in execution mode, it's going to use Sonnet. If you're doing your plan modes with <PERSON>, you're going (t: 80) to get incredibly good detailed plans that will make it so you only have to use Sonnet for when (t: 90) you do execution. This is going to save you a ton of credits and get you the exact same results you were getting before. If you use Opus for everything right now and you're not on the $200 a month plan, (t: 100) you're going to eat up your credits so unbelievably quick. But if you use Opus just for planning and Sonnet for execution, you'll get the same results as using Opus for everything, but you'll save a (t: 110) ton of extra work. So do that, do slash model, hit enter, and then move to Opus plan mode. This (t: 120) will get you great results and save you tons of money, and you won't have to think about it ever again. The second tip I'm going to show you might be my favorite of the bunch because it's going to give you a whole bunch of cool new information right in your Cloud Code UI. Check this out. (t: 134) So see this down here? This is a status line in Cloud Code, and this is brand new. (t: 140) And what you can see here is a few things. One, the weather of where I'm currently at. Two, the time of where I'm currently at. Three, the model (t: 150) I'm currently using, so I can always stay on top of what model is being used, so I know I'm not using too many credits. Next, I can see what branch I'm on in GitHub, so I can always make sure I'm (t: 160) committing to the right branches and I'm writing code in the right branches. And the last thing here is a tomato that says done. What this actually is is a Pomodoro timer. I can just start at any (t: 170) time I want, so I can stay focused. Let me show you. So if I type in slash pomo, and hit enter, it's actually going to start the Pomodoro timer right here. Watch this. Start a (t: 180) 25-minute work session. Your 25-minute work session has started, and now there's a timer in my status line that's counting down from 25 minutes, so I can make sure I stay focused during (t: 190) those 25 minutes. I don't know about you, but I get constantly distracted when using Cloud Code. It's so good, and it works so independently. I am constantly distracted and doom scrolling as (t: 200) it's working. This Pomodoro timer is going to make sure you stay focused. So how do you set this up for yourself? Well, it's really simple. In Cloud Code, if you type in slash status line, (t: 210) and then type in what you want in your status line, you could put anything. You can completely customize this. So you can say show the weather in San Francisco. Show the current model I'm using. (t: 220) Show which branch I'm on. I'd say those two are probably the most important ones you (t: 230) definitely want to use. So I'm going to type in slash status line, and then type in what you want to add, which is which model you're using and which branch you're on. Being able to see which branch I'm on at all times has been extremely helpful for me. But you literally can add (t: 240) anything else you want. So you can also say things like how many messages I've sent this session. So (t: 250) you can track how long your session's been. And then if you want to set up the Pomodoro timer I showed you here, then you can say also a Pomodoro timer that I can start and stop through slash status. (t: 260) And then if you hit enter on that, Cloud Code will set up all those status lines for you so that it (t: 270) sits under your chat bar at all times. I love the Pomodoro timer. It's kept me so focused. But you can do anything you want. I've even seen someone say also set up a Tamagotchi. And it actually put (t: 280) like an AI buddy under the status bar that you had to constantly feed and interact with also, which might be a little distracting, but it just completely changes your experience with Cloud Code. (t: 290) So much more enjoyable and making your Cloud Code experience enjoyable is going to be something that gets you to use it way more, which is going to get you to build a lot cooler apps. Definitely (t: 300) set up your status line today. Feel free to add the things I showed you, but also add anything else you want. If you want my command and how I use status lines, I'll put that down below. So you (t: 310) can just copy it if you want to set the exact same thing I did, but feel free to customize it any way you want. The next tip is probably the simplest one, but it might be the most important. It's going to (t: 320) be a really awesome, really easy way to set up your status line. So if you want to set up your status line, you can do that by clicking on the top right corner of your browser. And then you can also set up your office address, which I will share with you in just a second. And it's not just a simple way of doing it. It's a really awesome way to make sure your code stays secure in Cloud Code. (t: 330) So take a look at this. We're going to go back in here. We're going to clear this message out. I'm going to show you a brand new slash command they just added. So check out this brand new slash command. If you come in here and you do slash security review, you'll see this brand new default (t: 340) slash command that's in here called security review. You hit enter on that. And what it's (t: 350) going to do is spin up a sub agent that now will do a full security review. And it's going to do review of your entire application by default and Claude set this up so you know it's gonna be the highest quality sub agent with the best commands in it (t: 360) now it's gonna go through and it's going to search through my entire code base and check all the changes in my github as well and see if there's any security (t: 370) issues at all that I might not have thought about this has been a major issue for a lot of people that have been vibe coding for their first time is they (t: 380) don't understand best practices when it comes to security for building applications and there are so many ways you can screw up security and applications you can lose API keys you can put vulnerabilities all over the (t: 390) front and there's so many different things you can do that will degrade your security and so what Claude has done which I think this is awesome is built (t: 400) in this sub agent the security review sub agent that will now by default check over your application and find any sort of (t: 410) security issues that you need to solve and then you can go ahead and do that and you can go ahead and do that and then you can go ahead and do that so this is interesting it actually has found vulnerabilities in the app I'm building and is now going back to validate which one of these were false positives so it's doing its it's reviewing its own work to find those so (t: 420) here's the deal here's how I'd use this I wouldn't use this after every single prompt you do but I would use this before anytime you push code to (t: 430) production you use a security review sub agent that's now built in you make sure everything in your code is kosher then you can push to get up and push it to production but this is really awesome to think about and I think that's what I'm (t: 440) doing the next hidden trick I'm gonna give you is awesome for giving Claude context that's gonna make it way way more powerful and that's a new way you (t: 450) can use Claude rules check this out so right now obviously if you create a claude.md file in your main directory it'll create a Claude rules file that (t: 460) gives context to Claude on everything going on in your application which is good but say for a second you wanted to go back into your Microsoft cloud base and get a full package of rules and you're in your cloud you want to be able to get at (t: 470) to go more in depth and you wanted to give even deeper context about how specific parts of the application work. If you put it all in your normal clod.md file, you're adding a ton of context that will make your prompts (t: 480) slower, that might make your output worse. So here's a new way to give really in-depth details to clod without clogging up your entire clod.md file. (t: 490) What you actually can do now is go in here and you actually can add multiple clod.md files to different directories. (t: 500) So for instance, I'm building out a 3D simulator right now. So I have a virtual room in this 3D simulator and say I wanted to add new context and rules (t: 510) just to this virtual 3D room that clod only knows about when it works on this specific component. I can right click on virtual room, click new file, (t: 520) say clod.md like I did in other parts of my application. And now the rules I put in here will only be read when clod.md is added. So I can just go in here and create a file (t: 530) that does work on the virtual room component. And this is huge, right? This is huge because before you just have to put all your rules in your normal clod.md file that's at the top of your directory. (t: 540) And that adds tons of needless context that can actually make your outputs way worse because there's just too much context and it's just gonna use your window up too fast. (t: 550) But now instead of putting all of your rules in one file at the top of your directory, you can go in here now and create a clod.md file that has no context at all. (t: 560) So that way you can actually go in and create account components. So maybe you have a backend directory that has your database in it. Now you can go in and you can have a rules file (t: 570) just for your database and just for specific components, which frees up your context. So clod doesn't use up as much of your context window, but to make sure clod understands (t: 580) every single component in your app, so it never loses that context. Really easy way to set this up is you can just go in, instead of having to manually go one by one I just created a Claude.md file in the virtual room directory. (t: 590) Please go in and fill it out for me and hit enter. And instead of you having to go and make all your own rules for you, you just have your AI agent going and do it for you and save you a ton of work, right? (t: 600) And so now it's going to go in, read all the code inside of virtual room and create the entire rules directory for me. So Claude always has that context. (t: 610) This is going to make it so you don't use up as much of your Claude context window. And that's going to make it so you get way better results. And see, boom, there you go. (t: 620) It has all the rules on the virtual room in here. Look, it has about 100 lines of rules that I got done in about three seconds. I asked Claude to do it itself. Amazing. I got another one for you. (t: 630) So just to recap what we did so far, we made it so that the model picking is dynamic. So you're always using the right model and you're saving on cost. (t: 640) We set up a custom status line for you that always gives you the information you need when you need it. We went over an automated security review that's going to keep your code super secure at all times. (t: 650) And we went over custom Claude rules files that will make it so you don't use up your context window quite as fast. So you get way better results from Claude code. (t: 660) But let's go over one last pro tip here, which is going to be background tasks. You now can run background tasks in Claude code (t: 670) that will make it so you can run multiple processes at once and makes Claude code so much smarter. Watch this. So I'm back in Claude and I'm going to ask Claude to start the development server. (t: 680) So we're going to go in and say, please start the development server. And typically what would happen is Claude will start the development server (t: 690) and then it'll get tied up. You can't really do anything with Claude code while it's running the server through Claude code. So I'm going to hit enter on this. But now what's going to happen is I'm going to be able to hit control B (t: 700) and run it in the background. So if I hit control B here, this is now running in the background. I can run it in the background. I can run it in the background. I can run it in the background. I can now do anything I want while it's running. (t: 710) And Claude's going to have full context of everything going on in the background. So here we are in my 3D room operating system simulator where I can go in here. (t: 720) I have like my tasks on the wall. I have this computer I can use here if I want to use this operating system. Say I'm in here. I'm using around. Maybe I'm creating a new note and something crashes. (t: 730) So I'm creating a new note here. I hit save and something crashes. What I'm able to do is I can actually go back, go back into Claude, come in here and say, (t: 740) hey, please debug what just happened. And I can hit enter. And now Claude will be able to read all the logs and understand what errors occurred. (t: 750) Before you'd have to go into the logs yourself, highlight, copy and paste 100 different lines, paste it in, say, hey, what's going on here? (t: 760) But now Claude can work since it's running your server in the background and it can read the logs. And it can read the logs. And it can read the logs. As it goes to tech crashes as they happen (t: 770) and read the logs for you. So no more copying and pasting all the errors as you go. It's just hooked into everything that's happening already. This is going to save you so much time. (t: 780) Any process you typically ran through the terminal before that you had to wait for and watch, you can now send to the background through Claude code and it will monitor it for you, run it for you. (t: 790) And you won't have to worry about it again. You can keep your workflow moving seamlessly. This is really, really helpful. We'll save you a ton of time. So we went through five new features that Claude code just released (t: 800) that they don't really document anywhere. To be honest, I think the status line one's my favorite because that Pomodoro timer has kept me so focused since I started using it. (t: 810) I really recommend everyone go in and use it. All the prompts I used are down below so you can steal them, implement them yourself. If you found this helpful, make sure to leave a like. Let me know in the replies which of these were the most helpful for you. (t: 820) Make sure to also subscribe. All I do is create awesome videos about AI that make it way easier to vibe code. And I'll see you in the next video.

