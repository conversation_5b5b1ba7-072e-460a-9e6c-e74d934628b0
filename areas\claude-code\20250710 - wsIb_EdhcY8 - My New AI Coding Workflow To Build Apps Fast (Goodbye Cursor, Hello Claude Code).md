---
title: My New AI Coding Workflow To Build Apps Fast (<PERSON> Cursor, Hello Claude Code)
artist: Your Average Tech Bro
date: 2025-07-10
url: https://www.youtube.com/watch?v=wsIb_EdhcY8
---

(t: 0) I stopped using <PERSON>urs<PERSON> as my dedicated AI coding agent, and instead I've moved over to using Cloud Code instead. And in this video, I'm gonna be detailing why I migrated from Cursor (t: 10) all the way over to using Cloud Code. The way that I'm gonna format this video is that in this first part, I'm gonna give you a quick walkthrough about how I use Cloud Code in my workflow. And then in the second part of this video, (t: 20) I'm gonna give you my explanation and rationale about why I prefer using Cloud Code over Cursor. All right, let's get into it. All right, so let me give you a quick walkthrough about how exactly I use Cloud Code. (t: 30) And first, let's just start off about what exactly is Cloud Code. So Cloud Code is a coding agent that just lives in the terminal. It's not tied to any IDE, and they actually have direct integrations with JetBrains and VS Code, (t: 40) as well as any forks of VS Code like Cursor, which is what I previously used and I currently use as well. So I'll show you what exactly the integration does. But essentially, this is something that just lives in the terminal, (t: 50) and it's essentially what you would typically use Cursor chat for or Cursor agent mode. You ask it to do things, you ask for clarifications, ask for explanations, ask for code changes, and it'll do all of that. Now, in terms of the pricing, it can vary a lot. (t: 60) It starts as low as $17 a month, all the way up to $200 a month. For me personally, I actually shelled out for this crazy max 20X plan. (t: 70) I'm paying $200 a month. I know it's crazy, but honestly, I'm probably gonna keep the subscription because I think it's that good. The big difference is just the rate limits you get for the access to the various models. (t: 80) And in terms of how Cloud's models are working right now at the time of filming, which is June 19th, 2025, Cloud Opus 4 is their top of the line model and their most expensive model. And Cloud Opus 4 is specifically designed (t: 90) to be a really good coding model. Cloud's on a 4 is a little bit more general purpose. And the big difference with the max 20X plan versus this max 5X plan and the pro plan (t: 100) is you get way more access to Cloud Opus 4 for coding. And to get Cloud Code to work in your workflow, you first just have to install this little NPM package because Cloud Code is essentially a node server (t: 110) that runs and executes commands in your terminal, in your code base. Then when you do that, you authenticate with everything, connect to your Anthropic account, and then you go over here personally using Cursor. (t: 120) And you can see I have Cloud Code here, or you can activate in a terminal just by typing in Cloud. So Cloud is completely based out of the terminal. So like I mentioned earlier in the video, Cloud Code does have a JetBrains integration (t: 130) as well as a VS Code integration, which also works with other VS Code forks like Cursor, which is what I'm using right here. And at least to my knowledge, maybe there's more integrations that I know of (t: 140) that I don't know of. But what I've seen is the furthest extent of what this integration does is it makes Cloud Code aware of what file you're currently working. So you can see right here, I have the recording view.swift file open, (t: 150) and you can see that Cloud Code is able to detect that. If I switch files over here to this audio recorder service.swift file, you can see that Cloud Code is able to detect that change as well. (t: 160) Now, if you're also using Cursor, you're probably familiar with highlighting certain lines and adding that into your chat. Cloud Code does the same. So if I were to highlight all of these lines of code, you can see that Cloud Code (t: 170) is able to detect 25 lines selected. And then if I wanted to add this context into my Cloud Code instance, I would do command option K. And then you can see that it adds those lines of code (t: 180) into my Cloud Chatting Terminal instance. So Cloud Code, like I said, it just works out of the terminal. And there are a bunch of different commands that you can run, some slash commands, like choosing which models you want to do, (t: 190) resuming certain conversations, logging in, logging out, installing a GitHub app, yada, yada. There's so many different commands that you can do. But probably one of the most important commands (t: 200) you want to do right when you go into a code base is the slash init command. And when you run the slash init command, this essentially creates a cloud.md file which will essentially be an overview file (t: 210) about what exactly your code base is about. It's kind of like a cursor rule. So that's what cloud init file does. Actually takes a lot longer for this to run. For example, it's been pretty normal where I run a certain command with Cloud Code (t: 220) and it could take three to four to five minutes to run until completion. And at first I was a little bit annoyed of the speed aspect of it. But I found that the speed trade-off actually comes with a better performance trade-off. (t: 230) So Cloud Code is not afraid of taking a long time to complete certain actions. So in terms of how to make changes with Cloud Code, there are two. Number one is the way that we're all pretty familiar with (t: 240) is use enter in some command and it'll automatically make those changes for you. And if you press shift tab, you can then do auto accept edits on. (t: 250) Basically, when you turn this feature on, cloud will make edits to any file without asking you for your permission. Then if you do shift tab one more time, there's actually a plan mode. With Cloud Code's plan mode, (t: 260) this is basically where you feed it all the context that you possibly want and then it'll devise a plan and not make any changes to any file. This is just to make the initial game plan for what changes to make. (t: 270) So let me give you a quick run through about how exactly Cloud Code works specifically with plan mode. So first, I'm going to give it a little bit of prompt of what changes that I want to make. So one thing that I want to do is (t: 280) this is a small little Swift app that I'm working on and I want to build a meeting recording tool, right? A desktop app meeting recording tool that can read in, that can listen to all of the system audio input (t: 290) as well as my microphone audio output and combine it into one final audio file to record any meetings that are out there. So for plan mode, I'm going to enter in a quick prompt to tell it to build that out. So I just wrote out a quick prompt saying, (t: 300) add Swift code to support recording system audio output as well as user audio input and then save this combined audio file into one single file. So when I press enter, (t: 310) Cloud Code starts in plan mode. So it's just going to do a bunch of research on its own, make the entire coding plan and then afterwards when I click apply, it's then going to apply the changes (t: 320) kind of in a manner that is very similar to what you would see in Cursor's agent. Now, I'm not going to make you see this entire demo because if you use Cursor before, and you've seen an agentic coding experience before, you're going to be very familiar with this. (t: 330) But while this is running, I will talk a little bit about other things about Cloud Code that I like and I don't like. The number one thing that I like is Cloud Code actually provides you with a little bit of context about saying how full the context window is. (t: 340) And there's actually a dedicated command that you can use to summarize that context and truncate it so that you can continue working in that particular conversation. And in my experience, at least, (t: 350) usually starts showing like how full your context is once it's like 20 to 30% full. And I actually really like this little UI indicator. I don't have a lot of experience with this, but I'm going to show you how it works. So I'm going to show you how it works. I have it right here, or actually usually shows it right here where this little green dot is (t: 360) in the bottom right-hand corner. As your context window gets bigger and bigger and more full, it'll show you how full that context window is. So you're aware about whether or not you need to truncate it (t: 370) and maybe start a new chat or not. One downside that I do see with Cloud Code is I do find the fuzzy searching not as good like file searching, because you can still add certain files here and there (t: 380) for Cloud to get additional context. If you want to say, look at this file specifically, I haven't found it as good. For example, sometimes I'll type, I'll type out a little file name. And for some reason, I know that the file exists in my directory, (t: 390) but Cloud Code just can't pick it up for some reason. And I don't understand why. That has been a little annoying when I'm trying to add certain files. But at the same time, Cloud Code is good enough where if I just enter in a generic name, (t: 400) it's usually able to find the right file name that I'm looking for. But definitely, I think Cursor has a better file searching, fuzzy searching experience. So as you can see right here, it just asked me for permission (t: 410) whether or not I let it read a certain directory and I'll say yes. And as you can see, this is running for quite a long time. Kind of what I said earlier, about this speed off of time and performance. So Cloud Code regularly runs for a very, very long time (t: 420) before executing any type of action. But I do prefer that because I've actually found the quality of Cloud Code's output (t: 430) to be significantly better than Cursor. So as you can see, the plan mode just finished up and it gives me an entire overview of the plan that Cloud is going to take. And then when I do press proceed, (t: 440) what ends up happening is they turn this plan into a step-by-step checklist. So you can actually review the checklist as a developer saying what are they going to do step-by-step and then Cloud goes ahead and executes each task (t: 450) one at a time sequentially throughout this checklist. So I'm going to stop this process right here just because you don't need to see these random code changes being made. I'm going to zoom out and kind of just talk a little bit more (t: 460) about why I decided to use Cloud Code instead of Cursor. Now, if you're someone that's interested in using Cloud Code by default, you're going to be someone that lives in your terminal because that's where Cloud Code lives. And if you're looking for the best terminal experience (t: 470) that also offers an agentic coding experience inside of it, you should check out Warp, which is the sponsor of Terminal.com. And that's the title of today's video. They just released a brand new agentic coding experience (t: 480) directly inside of their terminal that is currently rated number one on Terminal Bench and in the top five on SWE Verified Bench, which shows that they are one of the top performing AI agents available right now. (t: 490) Warp has built in support for running multiple agents at once so you can have your own literal army of engineers making various code changes for you. And you can monitor their progress via a dedicated agent management panel, (t: 500) which is something I desperately wish Cloud Code had because it is a bit hard to keep track of all the changes going on purely via the terminal. Even better is the fact that within Warp, you also have the ability to choose which LLM (t: 510) you want to use for various tasks. I'm a really firm believer that picking the right LLM for you is a very personal experience because what works great for one person isn't necessarily guaranteed to be great (t: 520) for everyone else out there. You have to find the LLM that suits your work style, coding style, communication style the best. So I highly recommend going around, trying all of them out and seeing which one is the best fit for you. (t: 530) Also, because Warp has built out their own dedicated terminal app from the ground up and now offer their agentic coding experience inside of it, you can also use the code that you've created in the terminal. Their agentic coding experience isn't just a simple CLI tool, (t: 540) but instead it is a fully powered AI application that provides you that familiar terminal feeling that us developers love and are used to, but offers just enough customization to make the UX better. (t: 550) I mean, look, graphical interfaces were built for a reason. We should use them instead of shunning them. If you want to try out Warp and their new agentic coding experience, use the code YourAverageTechBro to claim two months of Warp Turbo for free. (t: 560) Now let's talk about why I decided to use Cloud Code over Cursor's agent coding experience instead. Really, I don't have one answer. I just think that Cloud Code has significantly better output (t: 570) than Cursor's coding agent. And look, I have no idea how to quantify this. I don't have a rigorous test case scenario. This is all just based off of my personal anecdotal experience and just like my gut feelings. You know, I think now that I use Cursor for, (t: 580) I think almost a year at this point as my main coding editor, I have started to develop some type of intuition of what I think will work with Cursor and what's not going to work with Cursor. And I would say my intuition is relatively correct. (t: 590) Once again, not super scientific, not really quantitative at all. This is truly just based off of my own personal experience. And when I've used Cloud Code, I've thrown a lot more complex tasks at Cloud Code (t: 600) with much less context and like adding certain files or adding certain folders and directories. But I found that Cloud Code is consistently able to make higher quality changes and results in code (t: 610) that is significantly less buggy compared to Cursor. Aside from the pure output of code that Cloud Code is able to produce, any other metric, Cloud Code is a worse user experience. (t: 620) I do wish it had a graphical interface. I don't love living in a terminal. I wish that it can live as an integrated tab within my code editor. I wish that I didn't have to pay an additional subscription (t: 630) to Cloud Code when I already pay for Cursor. But at the end of the day, the only thing that matters to me is my developer velocity and the model performance. And at least for me, I have found Cloud Code's performance and code output (t: 640) to be significantly better than Cursor's. I think a big lesson to learn from all of this is that now with a crazy development of AI right now and AI software in general, it is never a wise plan to pay (t: 650) for a yearly subscription to something because what's hot one month could be completely cold and lame to use. And the next month, I am no longer gonna be paying yearly subscriptions moving forward. (t: 660) I'm only gonna be doing everything on a month to month basis so that I can always work on the cutting edge of what is the best. Because right now, at least at the time of filming this in June, 2025, Cloud Code is in my experience, (t: 670) the best AI coding agent experience out of anything else that I have used. Now I'm still going to be using Cursor as my main IDE of choice, just cause I'm already on there. And I do find Cursor's tab to complete code experience (t: 680) to be really good, but I only pay for the base cursor, which is 20 bucks a month. And I'm happy to pay $20 a month for just that tab to code complete experience (t: 690) within the IDE. But I'm no longer using the Cursor chat or Cursor coding agent. And instead, whenever I have any agentic coding that I'm actually gonna do, I'm always gonna be defaulting to Cloud Code from now on. (t: 700) When I say that Cloud Code works better compared to the Cursor agent, I think the big crux of it is the fact that I can write less of my prompt or better output performance from Cloud Code (t: 710) compared to Cursor. I think with Cursor, I had to really guide it to look at certain very specific files, reference so many things at so many different folders and files. But with Cloud Code, (t: 720) my prompts are significantly shorter. I have to provide it with significantly less context. And despite that, it is still able to produce higher quality code output compared to Cursor. And I think that is the big reason (t: 730) why I am personally switching over to Cloud Code. And I will say, if you are gonna go try out Cloud Code, I am personally using the most expensive subscription, the Cloud 20X Max subscription, or whatever Cloud subscription that's 200 bucks a month. (t: 740) It's expensive. And I never thought that I was gonna be paying this much for it, but the performance has pretty much blown my mind of how good it is. And at least as of right now, I'm gonna be continuing to pay that $200 a month subscription (t: 750) to the absolute maxed out tier for Cloud to get access to the most Cloud Opus 4 that I can. If you end up trying Cloud Code, let me know your experience with it. (t: 760) Do you like it? Did you not like it? Do you prefer the terminal experience? Or do you prefer that it had a dedicated graphical interface that you can interact with? Let me know in the comments down below what you think of Cloud Code. That's it for today's video, and I will see you in the next one. (t: 770) Peace.

