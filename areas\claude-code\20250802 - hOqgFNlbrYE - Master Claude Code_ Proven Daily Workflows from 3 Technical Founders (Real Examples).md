---
title: "Master Claude Code: Proven Daily Workflows from 3 Technical Founders (Real Examples)"
artist: <PERSON>
date: 2025-08-02
url: https://www.youtube.com/watch?v=hOqgFNlbrYE
---

(t: 0) If you're using Cloud Code by just typing in prompts as though it's another chat GPT, you're missing 90% of its value. Cloud Code comes off deceptively as just another lightweight (t: 10) command line tool. But really, under the hood, it's much more than that. It's the first in the coming wave of highly powered AI agents. Understanding how to harness that power is (t: 20) critical. And I think what might be holding you back from being fully blown away by Cloud Code's capabilities. My name is <PERSON>, a <PERSON><PERSON> and co-founder of an AI native startup who's been (t: 30) using Cloud Code since February. Earlier this week, I spoke to a group of founders in Seattle alongside my friends <PERSON><PERSON> and <PERSON> about the core principles to employ and the tactical (t: 40) frameworks and tools to get the most out of Cloud Code. We will cover a range of what we feel are the most valuable topics, including using MCPs to give eyes to Cloud Code, using the double escape (t: 50) method and resume method, and using the double escape method to give eyes to Cloud Code. In order to fork Cloud Code context and spin up multiple instances, one of my favorites and (t: 60) extremely helpful automated code review, a quick state of the union of the top code gen tools, including Codex and others, the my developer prompting trick that <PERSON> demos towards the (t: 70) end of the talk, that was actually one of my biggest takeaways, how we structure validation steps to ensure Cloud knows what is good and what is bad output, and many others. In the description, (t: 80) I've greatly detailed each section and the topics that we spoke through, so if you're already familiar with the topic, I'd highly recommend just skipping through to what's interesting and relevant, as there's a lot of good gems in there, in my unbiased opinion. (t: 90) And I've also linked the slides from all three of our talks, so you can reference that. I hope this helps you unlock the next level out of Cloud Code. And with that, (t: 100) here's Anad to kick us off. This is the main question that I get asked quite a lot. What's the difference between Cloud Code and Cursor? I think it sums up to this. (t: 110) Cloud Code is really good at multi-step processing. So you have a large task you want to get done, you can break it down into subtasks, execute them one by one. I use it for starting projects (t: 120) constantly. I post new projects every other couple of days, just because I'm able to create a really, really good spec, really good planning document, which I'll get into later, give it to Cloud Code, (t: 130) and I just let Cloud Code run freely, which is probably not great to do, but there's no stakes to these random side projects, and it's really good at doing that. And it's, again, because of (t: 140) that reflective loop. If there's a lot of complexity, this is what I mentioned earlier, just a minute before, where you have to pull in a lot of things from different files, (t: 150) it's good at doing that as well. And if you have a very long running process, I tend to prefer Cloud Code. Cursor is still really good, though, at solving specific problems, (t: 160) addressing very specific files or lines of code, because you can select those things very easily. Let's go into the Cloud MD file. So this file is your main context file. So when you run (t: 170) slash init, and we'll get into what that means in a sec, it will generate an overview of your code. So if you're going to do a lot of work, you're going to want to do a lot of work. So it'll go through all your files, figure out how everything is set up, make detailed notes about, (t: 180) you know, the startup processes, where everything is located. And I like the quote from the documentation, which is that your Cloud MD files become a part of Cloud's prompts. I mean, that's (t: 190) what literally happens. So they should be refined, like any frequently used prompt. So effectively, if they think of it like a readme for specifically built for Cloud Code, the init function is great. (t: 200) But I have my own set of commands that I run, where I ask it to do a lot of work, and it does. So it's a little bit more complicated to go through every file. And first of all, I'll extract the file structure and the folder structure. Then for each of those folders, I have a new Cloud MD file. So (t: 210) in each subfolder, I have a Cloud MD file. And effectively, you can create detailed notes for every single detailed readmes, you know, for every single subfolder, to the point where you can track (t: 220) every single function and file. And then when Cloud needs to do an operation, it's no longer, you know, grepping like crazy, you can have it just look at those Cloud MD files, as long as (t: 230) they're updated, it will drastically reduce, you know, how much cognitive load it's taking on. (t: 240) It's kind of a mix. It's more like the, it's like a, you know, cursor rules are effectively prompts (t: 250) that go into cursor, right? So it's, but it's also a readme of your entire code base. So it can act as both. But here's a good example of what I have in my actual Cloud MD files, right? So I have, (t: 260) this is like the main, you know, I have a backend and a frontend. So I said, Okay, here's how it's set up, I have my frontend has this kind of setup, my backend is this kind of setup. And I have it covered the frontend structure, (t: 270) which actually then also has a much more detailed list in the frontend folder itself. Then I have (t: 280) my backend structure, which is in the backend folder, but you know, in more detail, but I can extract those up into my, you know, project folder or company folder. So it's easy for me to also see (t: 290) if I have to run things cross code, code base or cross repo, it's a lot easier to manage them. And (t: 300) some other MD files that are not as talked about, I feel that are very useful, are things like the change log. If you have a good change log, it's easy for cloud to over time, realize what were the (t: 310) changes that were made and why. So every time you make a change, just asked it to update the change log separately from the Cloud MD file. And that just gives you a gives it a good understanding of (t: 320) Okay, here's why I changed it. And here's why we shouldn't have changed it. And here's why we shouldn't go back to doing this. I use a plan MD file for every new project that I start or every new task that I start. It's effectively the list of things that I actually want to get done in a single (t: 330) document. All right, now, here's the one major thing that I want to touch upon that I think is really interesting, you can attach cloud code into your GitHub repo. This basically replaces well, (t: 340) effectively it is Devin. But using your, you know, your anthropic key, it's super easy to set up, you (t: 350) just run this command, they automate everything for you. And then you can go in and create an issue like I had some project I wanted to do is create this component for the (t: 360) library and I tag Claude, like I would tag some developer, and it goes in, it creates the issue, it creates this (t: 370) to do list, this checklist executes that. And later, when I actually am like, you know, hey, this was like not that great, I can just tag it again, in the in the PR like I would when I'm doing this. And then you can go in and create (t: 380) with an actual developer. And it'll go through and do that again, I can do way more things using this process than I can (t: 390) just on my own computer running one agent or even multiple agents at the same time. It's extremely convenient. It also has (t: 400) cloud code has built in commands like review PR comments that allow you to effectively automate the review process from your console, you know, fetching the comments so that you can then it can then operate in your local environment. These commands are (t: 410) built out. I would really recommend exploring them. And I could just run 30 different commands at the same time I use cloud code to say, here's a list of all the (t: 420) features I want to build generate PRs for every single one of these and make sure you tag Claude at Claude at the end of it so that it will, you know, spin off the job. And it was able to do that this is in my phone, like by the time I got home, it was all complete. It was just amazing. (t: 430) With the integration of GitHub or Yes. (t: 440) No, from the CLI, you can integrate it into GitHub. Yeah. So it's effectively an app that running, it's running there, but it is the cloud code. But and now we'll get to commands. This is a, this is what a command is. Command is just a prompt. (t: 450) It's just a prompt that you can save in a file, share amongst projects, share amongst your team. You can develop these like very comprehensive step by step things. And you can actually run it just like you would the PR, the install GitHub that were built in from cloud. You can, you can write your own and why this is great is because you might have specific things you want your team to know or somebody on your team to know. (t: 470) Yeah. Yeah. Yeah. So if somebody on your team is just a cloud expert or a or a I prompt expert, they write something amazing. You can now share that with the entire team. It's super easy to use. You should really look into using commands which allow you to create these comprehensive workflows, just using a prompt that clock and then easily follow. (t: 490) This is, for example, the code base analyzed prompt that I can use to set up my really, really comprehensive analysis in a cloud MD file. And you see like it's super long, but it works. (t: 500) I even made a GitHub. So I can actually see like a command that's going to run right into the code base and then create this action that can then be run right right right from your GitHub interface. And these are this is just all the commands that I that I've collected. (t: 510) Anyway, here's the website and QR code, if you're interested. I've just made it really easy to add this disclaimer is that I made this, but I really do believe it's a really good way to share those ideas. (t: 520) You can also launch sub agents if you're just starting off with cod code. Don't you think about this, but just like you can basically run two things at the same time. It's pretty cool. So the analyze it's about was it's what you're talking about to generate the code of these files. (t: 530) Yeah. Yeah. Yeah. Yeah. Yeah. Yeah. Yeah. Yeah. Yeah. Yeah. Yeah. That's one I use to make a more comprehensive CloudMD file. It can just be to analyze the code base in general. Hey everybody, my name is Patrick. (t: 540) I have been using Cloud Code since it came out back in February 24th, which feels like forever ago. But it's been amazing to see just the constant evolution of new features. (t: 550) One of the coolest things I feel like with Cloud Code is how the Anthropic team very obviously works closely together on the ML and AI side. (t: 560) So the actual machine learning researchers that are doing the post-training and the fine-tuning and everything, along with the actual product team. So you see this close coupling with Cloud Code that I think really sets it apart from any other experience. (t: 570) I was just listening to the client founders on the Latent Space podcast. Excellent recommendation by the way, that podcast episode and just the podcast overall. (t: 580) They were talking about how Opus 4 and Sonnet 4 just so badly want to use bash commands to grep around as Anad was speaking to. (t: 590) So there's a whole realm of preferred bits of what Opus 4 and Sonnet 4 try to do that fit really, really well with Cloud Code, (t: 600) given that the application and machine learning teams are speaking closely together. So that's one reason why Cloud Code is fantastic to use. But I'll speak a little bit to the fundamentals of Cloud Code. (t: 610) So what makes this more exciting and interesting and thus all the hype recently over a cursor or other platform? (t: 620) From my perspective, the biggest pieces here are we're actually, we're doing much more than just CodeGen. We're really working with one of the first in production, (t: 630) like agentic tools that can do multi-step processing on the order of roughly an hour or so. You can think a lot broader than just CodeGen in terms of applications for this, (t: 640) which I'll get to a few of my favorite non-coding workflows in a second here. But I think just feeling the character, the nature, (t: 650) what helps us to do this, and then, you know, I think that's really what makes this so exciting. So I think that's really what makes this so exciting. I think that's really what makes this so exciting. I think that's really what makes this so exciting. I think that helps these agents run for longer and to get more accurate towards what we're actually trying to execute with them. All of these factors are really, really helpful lessons for us to be learning and internalizing now as we're (t: 660) building agentic workflows in other domains in our own products or using tools such as Gemini to summarize YouTube videos or whatever other workflows you might have. (t: 670) So that's one amazing part of Cloud Code. Cloud is also fine-tuned for, as I mentioned, tools that Cloud Code takes great advantage of. (t: 680) So you've got the bash type, type commands, so being able to grab your code base and use the GH or GitHub CLI tool. But we also have the native tooling, so web search, file search. (t: 690) One of my favorite bits is this to-do list. Back in the day, I'd always create these PRDs, which is still a helpful workflow, (t: 700) but for most things, I can just defer to Cloud Code doing the Shift-Tab-Tab to get it into planning mode, think through and iterate on the spec of what we're trying to accomplish, (t: 710) and then allow it to create a little to-do list to kind of keep it on track, especially when it's handing off between different steps and using sub-agents to summarize different parts of the code base or think through and do research. (t: 720) Being able to pull that back and keep grounded in the to-do list, even its little six-bullet to-do list, is super, super helpful. (t: 730) A few other tools, such as its ability to reflect on what it's outputting, is an absolute game changer. I see this happen quite a bit, where it'll work through something and be like, wait a second, this actually isn't (t: 740) the best approach to this, or this assumption was mistaken. And that ability, as you can imagine, when you're trying to let it run on a task and come back in 15 minutes or whatever to verify the output, it's super helpful. (t: 750) It's just one less touchpoint, and usually multiple less touchpoints that you're having to go in and babysit the model for, in addition to the output just being much better with that reflection piece. (t: 760) So there's a number of reasons why that pairing of Opus 4 specifically, but Sonnet 4 as well, with Cloud Code is a really incredible and productive workflow. (t: 770) One note, too, when you're using Cloud Code, if you run forward slash model, you can choose Sonnet versus Opus. So just in case you're not aware of that, the default is Sonnet. Opus is four times more expensive. (t: 780) But if you're on the max plan, which is $100 or $200 a month plan, which I'd highly recommend, the amount of inference we get is ridiculous. I mean, I would estimate if I'm fully using Cloud in a month, (t: 790) it's in the order of 3 to 5K in terms of API costs, but it's $200 a month flat. So I don't know how long this is going to be around, or if they're going to try to water it down, (t: 800) like Cursor or others. So I wanted to mention the different types of agents to just give a quick overview. We've got, of course, chat-based agents, which we're all familiar with, ChatGPT, Gemini, et cetera. (t: 810) We also have these CLI and IDE-based agents, Cloud Code, of course, being an example, Cursor, Windsurf, the brand new Kiro from AWS or Amazon, Klein, et cetera. (t: 820) And then we have background agents, which are just starting to kind of roll out over the last couple of months. So Codex, which also has a CLI tool, but of course, they've got a lot (t: 830) of different things that they can do. So with OpenAI's ProPlan, at least, you can kick off agentic processes that will run anywhere from one to four different instances of O3. (t: 840) And then I would also loop the GitHub integration, which I won't belabor, since Anad talked about it, but it's incredible. One of our friends, Sam, just walked me through this absolutely mind-blowing workflow (t: 850) that he's got. He basically took the integration that you can build with Cloud Code with GitHub, and then he modified that YAML file, because basically what it's doing is it's just creating a YAML file. (t: 860) It's just creating a configuration file. And then you can add additional details. So you can modify the prompt that it's running. You can, I would highly recommend, uncomment the model it uses so that you (t: 870) can use Opus instead of the default, which is, it's in there as a default. You just have to uncomment it. With this, you can add additional parameters, for example, basically sneaking MCPs into your config file, (t: 880) and also give it permission to use different bash tooling, and then give it access to other configuration files, like markdown files. So all this to say, just through that GitHub integration, (t: 890) there's a lot you can really squeeze out of it to essentially create, as Anad was saying, a Devon-type experience, but with much more control and with better models. What's also so cool about this is, as Anad was saying, (t: 900) you can embed any process that you have internally around amazing ways to go about code review. And also, based on the user that's submitting the PR, (t: 910) you can change things up as well. So you could really get these parts of your workflow embodied within these commands or these. (t: 920) These runners. And that's one thing I love about Cloud Code and also MCPs, is being able to encapsulate these different workflows that we have internally, which even just as one dev, (t: 930) it's helpful. But across the team, it's incredibly helpful to embody that knowledge and that ability to be super productive and hand that off to less junior folks that don't have to understand all the underlying details. (t: 940) So we have background agents. Again, GitHub integration being what I would consider part of that. And then kind of connected, but separately, a little bit more (t: 950) advanced, is our agent swarms. These are really cool. It's basically spinning off a bunch of containers. Codex is essentially this, where you can go from one to four. (t: 960) If you have four of them running at the same time, you've got all these agents running. And then they're coming up with a solution. And then you can compare, either manually or through LLM as judge. (t: 970) My friend Sam was walking me through this workflow, as I was mentioning, where he's got three Opus instances that kick off. And then they've got acceptance criteria that they can look at for what good code looks like, (t: 980) different style guides, examples of API documentation, and API spec standards. And it will compare outputs against that. And then LLM will choose which version of those three outputs (t: 990) it likes the best. And then we'll automatically merge that in, build a CI CD pipeline, and then he can review it at that point. So you can get pretty sophisticated with the swarm (t: 1000) idea. That's a more basic version. And then at the AI Engineer World's Fair that a few of us went to down in SF about a month ago, we saw examples of hundreds of these containers. (t: 1010) And we're seeing a lot of these containers being kicked off. Now, of course, that would bankrupt me with Opus 4. But it's exciting to think about. And then, of course, non-engineering agents as well, such as Manus and Deep Research (t: 1020) and others we're familiar with. My favorite, Cloud Code and the CLI and also in headless mode. They've got an SDK for TypeScript, Python, (t: 1030) and then, of course, just in the CLI as well. You've got this little intelligence that you can pipe things into in your terminal. You can put it in your build pipeline. (t: 1040) You can review and build all kinds of different stuff that's right where you're at or within your application. And I think thinking about Cloud Code not as just a code gen (t: 1050) tool, but as this agent that you can deploy in a bunch of different contexts is really powerful. I also love Gemini CLI. Very similar to Cloud Code. Doesn't have the magic. (t: 1060) But for other tasks, one of the coolest ones I found was somebody using Gemini to basically watch a YouTube video, which they can see one frame a second, and they have (t: 1070) a course with a transcript as well through Google's first party integration with their YouTube tool. I do this all the time. Even before I watch an hour talk, I'll just summarize it, gemini.google.com, and we'll basically (t: 1080) get a sense of what it's talking about. Or for this talk, there was one detail I remembered from a Cloud talk. But I didn't want to go through the entire hour to try to find it. I just quickly asked, hey, I remember this point. (t: 1090) Roughly speaking, where is the time code for this? And it pulled it up. Super helpful. And this is just one workflow with YouTube, but super helpful. Another cool one, though, is with the Gemini CLI, (t: 1100) you can take a tutorial and then have it try to execute and build that locally on your computer if it's something that would be doable from a command line or using a different tool that you expose to it. (t: 1110) So very versatile. So what agents need for great performance? Context. Context is everything. Context truly is everything. As you guys probably know, prompt engineering (t: 1120) just got rebranded to context engineering, given that what we fit into the model, what we give them. The analogy I pulled from, I believe, was the Anthropic CPO. (t: 1130) The guy who's also the Instagram founder. But the way he was talking about it is imagine you're a Cloud Code. You wake up. You're in this box. And all you have is what some person just handed you, i.e. the prompt. (t: 1140) It's going to be extremely hard to do anything productive with that if you've got limited context, limited tooling. So giving the context of the code base architectural style, (t: 1150) what our preferred libraries are, different UI mocks and style guides, anything that can help it understand examples of good output and bad output, along with evals or ways to evaluate the output. (t: 1160) So again, examples of good and bad. Linters are super helpful. I mean, I just have it run ESLint every time it's doing anything because that just saves me a ton of time. (t: 1170) You just want to keep that agentic loop going as long as you can and give it as much feedback in real time as possible. Any standards, so like around commits (t: 1180) and branch naming, for example, acceptance criteria, automated tests, and then also tools. So different MCPs are the easiest way to expose these, but also the built-in web search and bash, (t: 1190) GitHub CLI. There's a lot of other tools you can give these models to perform and do much better. There's a lot beyond engineering too (t: 1200) that these agents are great at. Second brain, which is basically like a methodology around personal knowledge management and like note-taking, which can be really helpful along with different computer administrative tasks. (t: 1210) So like naming screenshots, space health content and what's in there, organizing files automatically, of course, pipe operator. There's a MCP, which is really fun to create 3D models. (t: 1220) I haven't used it myself, but I've seen some amazing demos. Getting close to time, so I'll just really quickly go through the rest of the slides here. Different types of MCPs that can be really helpful. These are the main categories of functioning, (t: 1230) the type of MCP. These are some of the best registries of MCPs where you can find them. There's like behaviors with React that are terrible for users that end up happening when you kind of (t: 1240) just throw a lot of code together. Yeah. And fixing it is hard. And I figured there's some way, there's some way to do it if you have some MCP that's going to load it (t: 1250) and then kind of output, you know, the progress to some format that can be read by an LLM. But I don't know what that is yet. You know, I don't have a good solution to this. (t: 1260) One thought though is maybe having it input breakpoints to get it to kind of pause at different UI states and then take a screenshot is maybe one interesting approach just to throw out there. But it's a great question. (t: 1270) All right. I'm out of time, unfortunately. I'll share these slides though. There's a lot of stuff in here that I'm really passionate about, but I want to make sure we have time here. (t: 1280) So big picture, I can't believe we didn't cover this yet. This is what you want to do every time you are using Claude code on the command line. How many of you actually use Claude code? (t: 1290) Have used it? You've all used it. Okay, so you know this. So hopefully I'll give you something a little more interesting. But explore, plan, execute. If you jump straight to execute, I do this sometimes. (t: 1300) I'm like, this is going to be so easy. Claude is dumb and it will screw it up. (t: 1304) I actually find that what I'm on it for with thinking hard is better than Opus for a lot of tasks (t: 1310) and it's faster. So, but you know, your task complexity may be different than mine. So my goal is to make, (t: 1320) here is to make Claude spend tokens to build up context. You can read the markdown file. I don't, mine's never up to date. Or it reads it and it starts, you know, (t: 1330) imagine you read 300 lines about like how someone's code base works and they're like, now build this. You're going to mess something up. So prepare to work on this. (t: 1340) Claude starts with an idea of what it's going to work on. And it's like, okay, it's just like you, like all of you. You're like, okay, you start reading and then you're like, okay, I know how to build this. And you stop reading and you're like, (t: 1350) I'm ready to build. If you're like, read the code, it will read a little bit more. But if you're like, prepare to discuss how our front end works, Claude will spend 50,000 tokens (t: 1360) over seven minutes just being like, okay. And then it'll give you a nice overview of how it works. And when you do that, Claude is much smarter. And if the overview is wrong, (t: 1370) escape, escape or slash clear, start over. Don't try to correct. You can try to correct it. I do it sometimes. Sometimes it works. But you're just basically chewing through tokens (t: 1380) in your context window, trying to push back on somebody who, on a bad contractor. Just fire the contractor, get a new one. If it is wrong, what else do you put in there to make it right the second time? (t: 1390) You just like rerun it and see if... Just rerun it. If it's going to reuse a bunch of subagents, it's going to get it right. It's going to be right nine out of 10 times. This is a great, like this is a great gambling game. (t: 1400) And you just, when you lose, you're not like, oh, why did I lose? You're like, no, I win almost all the time. I make just Markdown files. I have Claude write them. So like talk about how our architecture works for, (t: 1410) you know, and then make a checklist of like, this is what we're working on. This is like an old one, obviously. Don't write any code. This is like, maybe if you have a PR, (t: 1420) consider the next one, review, read relevant. But I actually think this is a lot better. We're going to work on the document identification part of the app. Dig in, read relevant files, prepare to discuss the ins and outs of how it works. Sometimes I'll follow up with questions (t: 1430) just to make sure it actually has the context. And often I will double escape to remove that from the context if I think it's doing a good job, just because I burn, (t: 1440) like I don't want to, I like a lot of room in the context window. So double escape. How many of you use double escape with Claude? Okay. You should use all, you should use this all the time. So I just spent seven minutes building up context. (t: 1450) This person's, this, this contractor's really good at this. I can double escape and just fork the conversation. Like I can have it do a bunch of work, (t: 1460) double escape and go back to this same point where they have all this context. Saves me money and time. Like mostly like I won't get kicked out of my max plans as soon, (t: 1470) as quickly. And mostly just like, I don't have to sit there and wait and maybe get the, get a bad gamble. If you get a smart Claude, you should keep it and reuse it over and over and over. (t: 1480) So this is what it looks like. You double escape and you can just go back to any previous conversation. This is a crazy branching multiverse. So you can open up a new tab. You just built up a bunch of context, (t: 1490) open up a new tab, hit resume, and you get all that context in the new tab in terminal. So you can do like five terminals all with all of that exact, amazing front end or backend or API context. (t: 1500) You can, you can ask a couple of questions and start there, whatever you, wherever you want. Just don't do this and then start like having it right. Three different things on the front end. (t: 1510) Go. I get work trees or just different directories or how do you go? I prefer to not work on more than two tasks at a time because my brain gets (t: 1520) fried. I end up with 15 tabs open. I go back to a tab. I'm like, wait, what's that tab? And I'm like, Oh my God, I cannot make this decision right now. This is like, why did I even start down this path? (t: 1530) I just have like two work trees, which was just like your entire get library, like in a parallel case. And I will just like merge them into master. I just keep them open. They're just sitting there. (t: 1540) Cause I don't care. Uh, I don't, I don't use the same thing as get appropriately. Um, so plan, I don't use plan mode. The three or four times I've tried it, like didn't do as good a plan as me asking it to do a plan. (t: 1550) I like to think hardest. This is where you really have to think. Claude needs to think hard to plan. Um, so this is my like generic instructions. (t: 1560) I really like this. Write the function names in one or three sentences about what they do. Write the test names, five to 10 words like about the behavior they cover, but really the shorter overview. (t: 1570) If like, cause Claude's default for plan is often like, here's a bunch of code that I'm going to write. And you're like, no, I want you to think higher level than that. (t: 1580) I want you to tell me conceptually what you're doing. Cause when you start doing code like that, you're starting to get into the weeds and you're not thinking architecturally. So this is like a, (t: 1590) this is a different example. I have like, I actually have built up this whole system for adding new PDF types. So I have like a, like a whole system where I like basically take a PDF and I throw it at Gemini. (t: 1600) But I have different types and I have different verifications I want to run on them. I just have it read a couple of guides and then I just let this run. So there's no context in this. I can just put this into GitHub and then I go to Claude and I'm like, (t: 1610) do GH issue 140, close it when you're done. And then I just hit like auto accept. Goodbye. (t: 1620) Risk-based planning. If it's small, don't overthink, just write the code medium to large. You've got to break it into like testable deployable PRS. And that's, I think of this in terms of context, right? (t: 1630) It's Windows, you know, and that's like about a PR size for me. It's about a PR sized chunk of work. And then high risk. I'd take, I think you should take three shots at the plan, two or three shots. (t: 1640) You should really work over it. Like with Claude, I'm not making the plan again. Like I'm just looking at its plan and I'm like, this smells bad. Like this is a terrible, like if an engineer came to me, you are an engineer. (t: 1650) You're coming to me with this. Like, I'm like, this is really complicated. It's going to be like, you're going to screw it up. It's going to mess up the code base. So, once I've done the plan, (t: 1660) I open up a new tab, pull up that same amazing context, but don't dive into the plan. Don't, don't, don't get it. Like once it, (t: 1670) once it's made the plan, it's not going to critique itself. But if you go back to the amazing context and you're like, yo, my developer came up with this plan to do this. Claude's like, yeah, all right, (t: 1680) let me tell you about this plan. I'm with you. I'm on your team, not on your developer's team. If you're like, I came up with this plan. It'll like tell you a lot of nice stuff. It'll be like, great job. You did a great plan. (t: 1690) Here's a couple little things you might do differently. But this case, it's going to be like, yeah, your developer, you know, like, I don't know. (t: 1700) I wouldn't have done it that way. And try to get specific. If you're just like, they made this plan, it's like, it's not going to do a good job. But so ask the questions you would ask yourself. So get feedback on the plan. (t: 1710) You can make, have two Claudes make the plan. You can have a third Claude decide between them. I tend to put them in the middle. I put them in a markdown and have Claude work on them. (t: 1720) And then I have it break them up into PR sized chunks. And then we execute. And those PR sized chunks, you might as well use that same context that you've already built up. (t: 1730) Because it's so valuable to have those 50,000 tokens about your database, or sorry, your app in the context window. It's going to write much better code than if you just like, (t: 1740) bring up a blank Claude with 200 lines of the Claude MD. So pull up that 50,000 token context window. Say, work on PR one. (t: 1750) This is my example prompt. Think hard, write elegant code that completes this. This is a real big one. It loves backwards compatibility, which I don't. I'm like, (t: 1760) no, I ended it like, it's like, we'll have graceful fallbacks. And I'm like, no, that's just junk that will break. And then we will gracefully fall back. When you say that, that means to me that the app is going to silently fail. (t: 1770) And I will not know about it because it will just start leaning on some old code that you should be deleting. This is, you can tell where I get frustrated with Claude. (t: 1780) So I try to, this is a little overkill, like the testing and sometimes, but I think linting, compiling and writing corresponding tests is good for really simple stuff. I actually just say like, do TDD. (t: 1790) And it writes the test. It writes the failing test. It writes the code that makes the test pass. It does a great job. It's really good at TDD is terrible. When I did code, I remember trying it for like a week and being like, Hey, TDD, this is just like, (t: 1800) this is just like, this is just like, this is just like, this is just like, this is just like, this is just like, this is just like, this is just like, this is worse than writing tests. (t: 1810) So I like thinking hard or think for this. I write, have Claude write lots of scripts to check its own work. So like I gave it a script to call Gemini with PDFs, (t: 1820) or I had it write that script. And now I'm like test to make sure that like when you verify it, like you created a new Markdown file that verifies PDFs, make sure it actually works and it verifies with this one. (t: 1830) Or if you need to view a PDF file, Claude's terrible at that. It can't do it. Ask Gemini or ask TDD. Ask Unstruct. It will give you a Markdown file. Go, (t: 1840) you know, look at that. Then you can read it and understand what's going on and you can like figure out what, what to do. This is a big question to watch or not to watch. Do you like, cause Claude will make in my case, like one out of 10 to 20 times, (t: 1850) it's going to start copying code and just doing some dumb stuff. And I'm not going to look at the commit. I'm going to watch it as it goes pretty much, (t: 1860) or I'm not going to watch it at all. I'm going to be like, commit it. It works. It's good. So I've seen 200 lives of copied code go through. I've seen 200 lives of copied code go through. I have a weird config that's in five different places in my app. I have a weird config that's in five different places in my app. And I'm just like, (t: 1870) every time I'm like, Claude, can we just, can we just put this config into one place? And it's like, Oh yeah, here's a plan. And I'm like, all right, you're stupid. (t: 1880) Like this, okay, this is harder than it looks, I guess. Return true was a 3.7 problem. You will not get that anymore. But usually if you just hit go, like you kind of get a feel for it. I have a feel for it now where I'm like, this isn't easy enough thing for Claude just to do. (t: 1890) Go. Shift, tab, you know, puts it on auto complete. I don't know if you all have heard how this came to be, (t: 1900) like why we have amazing coding agents now, but it's because of RL. And it's because once the models, once you're, once you move up the tech tree enough where models can write good, compilable code, (t: 1910) you can actually then start to have, give them coding problems and then, and figure out like basically they have to be able to create the solution like 80% of the time, or they have to be able to create the solution like 80% of the time. (t: 1920) And then you can actually start to have, you know, like a lot of these other tasks, like the way that they did thinking was they were just like, write a bunch of stuff. And at the end, (t: 1930) if you get the right answer, you get a cookie and we're going to reward that circuit. And if you don't, you don't get a cookie, right? And if it like, so we got to like GPT four and Claude three, (t: 1940) five level models, and you could start actually like turning thinking on, but because the models were good enough to get all the way through. But, uh, the problem is like you're creating software engineering problems and (t: 1950) you're just like, oh, I'm going to write this code. And they're verifiable. So like write this code. Does it compile? Does it answer the right question at the end? (t: 1960) Very easy to test back. Right. But does, does that make for good edits? No, that makes for really good writing, fresh, new codes, new methods. (t: 1970) Claude prefers that. I don't know if you've all noticed that, but you're like, in my case, you're probably, if you're editing stuff like cursor style, it doesn't matter. But in my case, I'm like, (t: 1980) write this, edit the code, figure out where you can edit because you really have to be able to edit because you really have to prompt that because Claude is still really tuned into like, okay, I'm going to write some new code. (t: 1990) This is gonna be fun. We're going to do a new method guys. Uh, so, uh, sorry, it got cut off here, but yeah, Claude three seven was like over RL on just like completing tasks and they dialed that back. (t: 2000) That's where the return true came from. But we still have this problem where Claude is like just trying to finish, like it's trying to finish tasks and get its cookie by writing new (t: 2010) code, not by editing or elegantly integrating code. So, then I go back to the developer thing, right? I lean on the developer. So I go back to that, the planner. So I have my planner tab open and I'm just like, yo, (t: 2020) my developer just finished step two, give them low level, like feedback and high level feedback. If you don't say that, it's like they did a great job. Um, so, and then I get feedback and then I go back to the developer and I'm like, Hey, (t: 2030) I got this feedback. What do you think? And it's like, well, that's good feedback. Yeah, I'll do it. Um, and this is the problem. I'm like, (t: 2040) I'm like, I'm like, and this is the problem with like Claude. I don't know if you've hit the slash review on Claude's own code. It's like this code's great. Review doesn't know the cloud likes like Claude's code. (t: 2050) Um, I use this sometimes, but, uh, prepare. So I'm like at the end as I'm running out of my context window or we're finishing up the poll request, (t: 2060) like pay it. Like I say to Claude, like tell some, give the neck, you're not working on the next step of this. Give advice to the next developer, put it in the Markdown file. And Claude is usually like you, (t: 2070) you're off to an awesome start. Excellent start here. But, uh, it can be helpful. Uh, context window management. I'm sure. Do you all get this? (t: 2080) I like, I never compact anymore. Compact is a waste of time. It generates like a page and a half and it tells Claude to read four files. And you're like, you, you end up with a very like kind of off, off kilter, dumb Claude. Uh, (t: 2090) so I just try to rewind. I try it once I get to 5%, I'm like, document what you've done. And we're rewinding back to 40% and we're going to, we're going to, we're going to, we're going to, we're going to, we're going to, (t: 2100) we're going to, we're going to, we're going to, we're going to, I'm going to be like, here's what, here's what I've done so far. Continue. Yeah. I hate compact. (t: 2110) I hate starting with clear. Yeah. I mean you could use clear, but then you don't have any context. And so I like, and you can use resume to get that context back or double escape. So like why not use that? I mean, it is more expensive cause you're, you already have all that. (t: 2120) Like you're, I don't know that you're actually getting charged for, you only get new tokens, right? So you're not, you're just, you're just, it doesn't make a difference. It's easier to, for you only get new tokens right so you're not getting charged it's much (t: 2130) more expensive for them but for us it's just new tokens so it's great so you jump straight to execution go ahead oh yeah yeah other tips and tricks I get (t: 2140) merged that's what the problem with work trees is I'm like all right we're gonna do this over here we're gonna do this over here and then we're merging and I'm (t: 2150) like oh Jesus now we have a merge conflict I'm just like cloud there's deal with git and it's like okay and it gets it right every time I'm like oh (t: 2160) this is should I like trust it and I'm like no every time it works I skip the ceremony for simple tasks just like do it (t: 2170) Claude loves to be enterprise ready just you have to fight that because like it's you know built by an enterprise for enterprises so so this is one of my like if if it gave me a plan that's too bulky I love I love this and it's just like (t: 2180) totally right and it cuts through the whole thing and it cuts through the whole thing and it cuts it in half and it makes it a much better plan for me explore plan execute resume my developer and then Claude made up this joke at the (t: 2190) end for me I didn't add this but I like it all right so that's my talk I hope (t: 2200) you found our talks helpful and if you did I'm sure you would enjoy one of (t: 2210) these two videos on how to become a I need of as a software engineer and a founder specifically within code gen tools like (t: 2220) Claude code and with that don't forget to subscribe for more content like this thank you

