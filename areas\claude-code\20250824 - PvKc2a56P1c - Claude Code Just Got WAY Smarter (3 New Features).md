---
title: <PERSON> Just Got WAY Smarter (3 New Features)
artist: AI Unleashed
date: 2025-08-24
url: https://www.youtube.com/watch?v=PvKc2a56P1c
---

(t: 0) There's been a flurry of new releases in AI coding lately. From GPT-5 to new versions of Cursor, new versions of the Gemini CLI, it's all good. I keep finding myself coming back to Cloud Code. (t: 10) Find the way it interprets the prompts and the output it gives me, it's just a little bit better than everybody else. And the Cloud Code team is shipping. Almost every day they have a new release with bug fixes and some new features sprinkled in. (t: 20) Hey, my name is <PERSON>. In this video, I'm gonna show you three new features in Cloud Code that really helped me in my workflow. Let's go. (t: 30) One feature I think is underutilized in Cloud Code is the planning mode. And that feature just got a new upgrade. And to get into planning mode, you just need to shift tab until you see plan mode on. (t: 40) What planning mode is, is it just does planning. It does no coding for you. So it just lets you iterate your idea. So you have the right technologies, the right requirements, all kind of ready before you actually start the coding. (t: 50) What that means is you can put in some general bullet points of requirements, some ideas you have on technologies, and then Cloud Code will actually just go ahead, flesh those out and give you more detailed requirements. (t: 60) In this case, let's just say, build an embedded Shopify application using React Router and Neon for my database. Then we use GPT-5 to analyze merchant data and recommend customer targeted bundles and sales offers. (t: 70) And why planning mode is so important if you don't use it and you just start with kind of vague prompts like this, what ends up happening is you got to get down a wrong path and then it becomes hard to get out of it again. (t: 80) Like anything else, a little preparation goes a long way. Then we see here what Cloud Code did was actually give us a breakdown of our project structure it's gonna build, gives us details on the requirements, (t: 90) the technologies it's recommending, all this before we start coding. And now we can iterate on that plan from here. And because that planning work is so important, I'd recommend using as much horsepower as you can in that planning phase. And Cloud Code is a new feature to do that. (t: 100) So if you do a slash command for model, there's a new fourth option now. So this is actually more applicable if you have the lower plan, like the pro plan, the $20 a month one, (t: 110) because you don't have that many credits. Oh, to stay within your usage limits, you wanna really use Opus, which is the higher end model for planning. And so this fourth model lets you do that. (t: 120) So what it says is use Opus 4.1, which is the latest one as of this recording, in plan mode, and then Sonnet 4 otherwise. So basically what this will do is give you all the horsepower upfront in your planning mode (t: 130) so you're happy with the specifications. Then when it starts coding, it's gonna switch to Sonnet 4. You do that more efficiently and not use up all your usage limits. So like I said, if you have the max plan or higher, (t: 140) it's probably not that big a deal. You can probably keep it on Opus or default. But if you are on that pro plan, I'd recommend looking into this one and setting this one as your default. So that's the first one. (t: 150) The next new feature is a little one, but it actually really improves your user experience as a developer in Cloud Code. So if we look at my Cloud Code window right now, I don't actually have much context (t: 160) about what model I'm using, where I am, what application I'm in. If I just took a break and I kind of forgot what application I was working on, I don't really have much in the UI that's easily available. (t: 170) I actually know what I'm doing right now. So this new feature actually adds a status bar underneath your command line to give you some of that information. And you can customize this a lot. So if you do slash command status line, (t: 181) just let it cook there on the default. It's gonna actually just add some default things that it thinks will be useful for you to see. You do have to restart Cloud Code (t: 190) after you run that status line command. But now that I have restarted it, now you can see underneath here, it shows me the model it's using. So it says Opus 4.1. It gives me the full directory of the current project I'm in. (t: 200) From here, you can also customize it. Like I'd say, make the two values different colors and also add the git branch. So I can see the git branch and then the git branch on. (t: 210) Now if I restart it again, now I actually see the model, the full directory to my application and also the branch I'm in and git, all in different colors, which is pretty cool. And there's many customizations you can do here. You can add things like the time, (t: 220) how long your Cloud Code session's been, even stuff about your build status or the status of your test suite, all kinds of different options. That's actually a really good document Cloud produced for this. (t: 230) And I'll put that in the description of the video so you can read that. It's a small one, but it's a nice upgrade to your user experience inside Cloud Code. Cloud Code is a different kind of code. It's a different kind of code. It's a different kind of code. I'll just introduce the concept of agents (t: 240) and what those really are, you can think of them as a process that runs, that just does very specific tasks and has very specific instructions. I think the most important thing to remember of an agent (t: 250) is it maintains its own context. So you can have a front end agent that does all the design work and the UI work. And then you can have a backend agent, which knows all about the database, (t: 260) has access to MCP servers for database manipulation, and you can keep those kind of separated with their own context windows for both. And then Cloud Code orchestrates them and gets the job done by using whichever one's appropriate (t: 270) for their current job. Let's look at an example of one. So if you use the slash command agents, then it'll give you a list of all the agents you have. I don't have any right now, so let's say create a new agent. (t: 280) And I'd recommend just starting off with a project agent. Once you get that done and it's working well, then you can move it up to your personal. And the personal one will apply to all the projects you have on this system. (t: 290) Let's say generate with Cloud. So this will help us kind of refine our agent. And then at this point, it's actually kind of like planning modes. You just put in your points for what you want the agent to do, and then Cloud will enhance that from there. (t: 300) My case, I want this to be a Shopify app UI agent. And I want it to use Shopify's Polaris React component library. I also want it to follow Shopify's app design guide. I think this is a good example of how the agent would work (t: 310) because when I do the backend agent, I'm not gonna put any of this information. It doesn't really care about the app design guide. That's more of a layout and UI design. (t: 320) It's gonna ask you what type of tools you want it to have access to. I'm gonna say all tools, but you could have agents that are just read-only, for example. They just did like planning work. You could have one that just did execution, (t: 330) maybe like a test suite execution agent. But for this one, I'm just gonna say all. And then you can say what model you want the agent to use. I usually just say inherit from the parent. That's gonna be whatever model you're using in Cloud Code (t: 340) that orchestrates all the agents. That's gonna use that one. So I'm gonna say that. Then just give it a color and we're ready to go. If you see here, it actually just creates a Markdown file (t: 350) that has a whole definition of this agent. And you can see the location here. So for future agents, you can actually just copy this Markdown file if you want and create another agent. So you don't have to go through that whole wizard again if you don't want to. And now that I have that agent, (t: 360) if I just put in the prompt, build an embedded Shopify application in React Router, give it some more requirements from my application, it should know to go to that agent and utilize that to build with. (t: 370) And you see that right here, is to pick that up. Now that it's installed some of the dependencies, it says, now let me use the specialized Shopify players UI agent to create the app structure (t: 380) with proper Shopify components. And then we can see the color blue here, which is the color I have for that agent. Calling that. And using that to do the coding. So now that's going to keep its own context window (t: 390) and do its work. And this is the user interface it came up with. It's a really good first attempt because I actually use the Shopify app guidelines and how it wants to do application structured. (t: 400) And I use the Polaris UI components pretty well here. I'm still experimenting with these agents and still trying to figure out when's the right time to use them and when you don't really need to. But if you have any advice on that, make sure you add it to the comments below. (t: 410) But at the very least, they're at least handy for having those Markdown files. You can think of those as kind of like a prompt template. Just being able to use those as somewhere to keep putting in new information and getting better prompts is a good thing. (t: 420) And also I like the idea of having the context just scoped to that particular agent. I think that's another big improvement. Definitely more to come on the agents. There's a lot going on in AI software development right now. (t: 430) So if you want to stay up to date, make sure you subscribe to the channel and like the video. Hope you're having an amazing day. I'll talk to you in the next one.

