---
title: Unlock Leverage In Claude Code Using Sub Agents + Gemini MCP
artist: <PERSON>
date: 2025-07-08
url: https://www.youtube.com/watch?v=ODYkRzYYx2k
---

(t: 0) So every day I'm busy working with <PERSON> and I had this interesting idea today which is that when we're working with agents, the most important thing that you can learn as a developer is how to maximize the output with a single prompt. (t: 10) So how can you get the agents to do more with less effort from your side, essentially creating leverage for you as the developer. (t: 20) And I have an experiment that I've started working on today and I wanted to show you kind of the process. So what I've done is I'm busy building a SaaS right now and one of the things that (t: 30) I'm working on is optimizing the database for the read and write queries. So I went into ChatGPT first and she just asked it to give me a breakdown of how I can optimize this and I pasted this into Claude Code with the following prompt. (t: 40) So what I said is I would like you to make a very detailed plan going through all of this, but I want you to act as the operator. (t: 50) And as the operator, I want you to create five sub-agents and for each sub-agent, I want you to use the data. So I'm going to go ahead and create a sub-agent. I'm going to use Google Gemini's MCP server, which is what I'm going to show you how to (t: 60) set up to ultimately make the agents more efficient. And this is quite an interesting workflow because not only are you getting <PERSON> to (t: 70) work with sub-agents, but the sub-agents are also working with their own Gemini API calls and so they're also more efficient. (t: 80) And so just with one single prompt, I'm able to go through the entire code base. It launches five different sub-agents. You can see like all of these different tasks that it launches over here, and then it plans (t: 90) out everything that needs to happen. And it asked me if I want to proceed with that. So it's just a simple example, but I think this is something that everyone needs to start (t: 100) learning is like, how can you take one single prompt and maximize it using these AI agents? So to set this up real simple, all you need to do is go to this GitHub page, consult seven, (t: 110) which I will link to in the video description. And you want to get. You want to basically find this part over here where you can add your MCP server. (t: 120) So the one that I used is this one that's highlighted called MCP add, and then you just put in your API key for Google Gemini. So if you want to get your Google API key, you just have to go to Google AI studio, just (t: 130) type that in, and then you would find your API key there, paste that over here. And now you've got Gemini that Claude code can call on to make your coding more efficient. (t: 140) So hope you enjoyed this video. Let me know if you have any questions. And yeah, I'm learning a lot of stuff, so I'm looking forward to sharing more and I'll (t: 150) talk to you soon. Take care.

