---
title: 🤯 Claude Code + 1 Million Context = INSANE GAME CHANGER
artist: Income stream surfers
date: 2025-08-14
url: https://www.youtube.com/watch?v=fyjZD6BZxzA
---

(t: 0) Hey guys, today I'm going to be testing out the new Sonnet 1 million model inside Cloud Code with my context engineering template. Now the idea here is that this has such a ridiculous (t: 10) amount of context I'm really going to push Sonnet to just keep going. I think I will eventually make an MCP for this but I'm just going to test this with prompting for now. So you've probably watched (t: 20) these videos before with context engineering intro so I'm not going to go into too much detail. I'm on my Mac so apologies if the sound quality is not as good as normal. (t: 30) But yeah basically this process is pretty damn simple so let's just start terminal actually and we'll say cd dot dot cd dot dot and then all the mkdir context engineering (t: 40) and then git clone paste it and then put full stop that's not full stop. (t: 50) Okay that's a cd first so now we have the context engineering template. If you do ls and you see these files then it should just work straight out of the box here so let's just quickly open this up so (t: 60) you can do code dot but if it doesn't work for you then just open up visual studio code. Okay so what you want to do is you want to press new and then write initial.md and then gets the (t: 70) example here. Ctrl a, Ctrl C, Ctrl A, Ctrl V. Paste this in here you can save it so it's saved. You (t: 80) need to understand what Visual Studio Code is. There is a little white circle here it means that it's not saved so you have to do Ctrl S. for the file changes you make to actually happen basically. (t: 90) Okay, so let's use OpenRouter. So let's just say I want to build an HTML, CSS, JavaScript, OpenRouter AI project. (t: 100) Use FastAPI as the backend. Use Docker to create the project. (t: 110) So you can test. Create a simple logical keyword tool that uses, let's say, GPT-5 (t: 122) and an input of a business URL. (t: 130) There are two main prompts. It's a logical SEO tool that generates a business summary (t: 140) based off web search. Use OpenRouter. OpenRouter's web search feature. (t: 150) And then generates pillar pages that would logically make sense for that business summary. And then generate sub-pillar pages (t: 160) that make sense for that pillar page. And then display the sub-pillars as blog post topics. (t: 170) And then this is the important part. I want you to create a stylistic, unique theme (t: 180) that isn't just the same as all of the other bootstrap (t: 190) and boring projects, but instead has more about it and looks stunning. (t: 200) And I want 30 SEO pages that makes sense. And then create a new theme. And then create a new page that makes sense for this tool that can bring organic SEO to this site. (t: 210) You absolutely must create 30 detailed pages and not just create one or two as an example. (t: 220) Okay, so because we're using a new model here, I'm also just going to change a few things about inside the Cloud MD. So I'm just going to say context window. (t: 230) And then I'm going to change the name of the page. And I'm going to say, you have 1 million tokens of context. Do not feel rushed. (t: 240) Do not feel like you have to complete things by half, but instead complete the entire task at hand from start to finish (t: 250) without placeholder code, creating an entire workable and production-ready project. (t: 260) Full stop. Okay, so we'll just put that in Cloud.MD. And then we need to put documentation here. So we need to put documentation links. (t: 270) As far as I can see, there's only one documentation link, which is OpenRouter. So we'll do OpenRouter docs and we'll find that. There we go. (t: 280) And we'll just put that here. And then we'll tell it what database to use. So use, what's it called? MySQL as the database. (t: 290) Allow people to register with their email and password or username. Let's just say it's just easier. (t: 300) Username and password. And then we do actually need to get the Playwright MCP here as well. Okay, so there is this document, guys. You can find this document in the description of the video completely for free. (t: 310) I'm going to use my school community just because it has the most up-to-date prompt. So let's go classroom. I think it's here. (t: 320) Concepts engineering template. Yeah, perfect. So this is my most up-to-date prompt here. And also, I do need to install Playwright. So let's just do that as well. Okay, so let's run the command to install Playwright. (t: 330) You can find this in the document for your system. And then let's just save this. Okay, do I need anything else? I can get rid of that. (t: 340) I can get rid of that. So save that. Okay, that should be enough. I'm going to go to my school community. I'm going to go to my school. I'm going to go to my school. I'm going to go to my school. I'm going to go to my school. (t: 350) I'm going to go to my school. Okay, that should be good to go. So let's write claude dangerously-dangerously-skip-permissions. Okay, nothing pops up that the MCP is not working. (t: 360) Perfect. Go back to the school community. (t: 370) Okay, let's run that. Okay, wait. The API key for Gina, I just forgot about that. (t: 380) Okay, I'll just go ahead and run that. I'll go ahead and run that. I'll go ahead and run that. Okay, so file, new private window. Go to gina.ai. I use Gina, just because it's so simple, like, and you don't even, like, the use click (t: 390) API here and then copy here and you have an API key. Sorry, you're wrong with API key. (t: 400) This one and then also I actually need an open router key as well. So let's just grab that. And then one actual one thing that we've got to do is let's do slash logout here. (t: 410) log out here and then claude press up so i'm going to log in here i'm going to use api so anthropic console account so i'll select the account i want to use and then authorize this (t: 425) okay press enter here press enter uh sure okay so this is we should be able to model (t: 440) um what is it and then we run slash model sonnet one mill is i'll show you that again wait so it's model sonnet bracket one mil and then if i say hi this should respond to me (t: 450) and it should be using the latest model okay so let's put the first prompt here this has a (t: 460) million context so my idea here is just to just let it go absolutely crazy and just let it code for like an hour to several minutes an hour to a quarter of an hour or something like that and (t: 470) and just let it code for like an hour to several minutes an hour or something like that and let it ct it cv the second sign and start putting the code into a schedule is a little bit crazy and and then its just how it works there's three words in this right so if you want code in a module this that's a speech and two hours that's kind of my plan whether it'll do that or not that's kind of what i'm trying to work out whether that's prompting or mcp like what i'm thinking is every time or maybe a hook or (t: 480) something when it tries to deliver the final project i want it to like reread the initial.md or the prp or whatever and then just decide based off that whether it's actually implemented (t: 490) everything that it said it would implement okay so it's finished reading the instructions now open new generate dash prp initial.md and then hit enter so this will then actually start to (t: 500) do the research needed okay so as you can see here it's now scraping each of the pages of (t: 510) the documentation that are required specifically the web search is important and actually puts these files here for later use if i go to web search okay so that one didn't work it should (t: 520) work soon there we go so yeah as you can see here it now knows that you can put online after any model (t: 530) and the model becomes an online model okay so i skipped a lot that research because like i don't think claude code really needs to research docker like it just doesn't need to it knows how to use (t: 540) docker it knows how to use fast api it knows how to use mysql this one might have been useful but like i'm sure it's fine okay so the prp was actually extremely quick like i'm moving at (t: 550) lightning speed here okay so the prp was actually extremely quick like i'm moving at lightning speed here it says gpt5 perfect so i'm going to do uh what's that execute (t: 560) dash prp and then the name is here so i'll just copy this and i'm going to add some custom instructions here just because like i've talked about before i like to remind it one more time (t: 570) you have one million context window you have the context to create a full production ready app as (t: 580) well as full end to end application and then you can add custom instructions here so i'm going to that's a key feature here that i know that i really must use i see that we'll not be able to (t: 590) finish this it will taste just like a Länder tart i'll just imagine that i sent this to my team and they'll be able to see it my screen will be better looking they'll think whether my (t: 600) screen is in the right environment so i hope that they'll understand how amazing this is into like the camera display course and probably pick it up over there next time i'm using it (t: 610) and all i'm likely to do is egg this way you know i'm not going to go to a tv production job right but i'm just going to do now i'm just going to task seule one by one i'm going to do this so i'm interesting to see to be honest with you now i'm not going to record a lot of this i'm just (t: 620) going to come back when it says it's done and we'll see how done it really is right that's kind of what i want to know here okay guys so we have our first little look that's (t: 630) league of legends in the background you can ignore that guys don't worry about that we have our first little look at the website it looks good it looks uh different like compared to a lot of normal stuff (t: 640) that's created let's see if everything actually works right so okay well that was quick what the fuck just happened there do you guys see that i just used the end all the mcps at the same time (t: 650) it's filled everything in at the same time that's pretty crazy okay so we have a user we're logged in let's see if it actually works it's taking loads of screenshots this is crazy okay so it's (t: 660) given us a pretty good website um but obviously there's some issues here it didn't actually test (t: 670) if it worked but it does look really really nice okay so we actually have a fully fledged working tool that's actually crazy oh it just got rid of (t: 680) that which is kind of annoying um but yeah it works which is super super nice this is the first (t: 690) time that i managed to just run it and not had any real issues now yeah i mean i don't even know what to say guys it's getting to the point where you can one-shot basically anything especially (t: 700) with this million context window i haven't had to run it in a while but it's kind of nice to run a slash compact once i'm still using the exact same context as i was at the very very beginning of this video right so i'll just run this again and yeah to be fair to it it has given (t: 710) me a full production ready application that works um can you hook up the seo pages you said they (t: 720) weren't hooked up so i think once it's uh hooked up the seo pages i actually think it might have (t: 730) made not only the seo pages but the seo pages that are actually running on the seo pages so i've got my own two seo pages that i've got my own two seo pages to go through and so i've got a fully functioning app but also a fully self-promoting app using organic seo that's (t: 740) kind of what i'm going for here and yeah just the whole project looks really really good that original prompt i used where i said make it look a little bit different to the normal bootstrap that (t: 750) it uses i just wanted it to kind of be careful and make it not look exactly the same but the actual ai part of the tool works now it's restarting the back end again so this is going (t: 760) to fail we can maybe have a quick look here but the actual ai part of the tool works now it's restarting the back end again so this is going to fail we're going to maybe have a quick look here I'm going to have a look at the other side of this and then i'm going to go through the rest of it look here okay interesting yeah there we go this is what we wanted beautiful look at that here's (t: 770) the keywords oh my god it works perfectly it works better than i expected honestly oh look at that as well oh that's so good oh man it's crazy okay it has the seo pages they have no styling but it's (t: 780) pretty much done everything i asked it to do just would need a little bit more work a little bit (t: 790) more prompting but yeah overall incredible update from claude i still have enough context to you know build a whole another one of these which is the crazy thing but i'm gonna leave the video (t: 800) there guys thank you so much for watching thank you so much for tuning in if you're watching all the way to the end of video you're an absolute legend i'll see you very very soon with some more content peace out

