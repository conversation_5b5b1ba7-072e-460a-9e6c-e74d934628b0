---
title: "How to give <PERSON> a better \"memory\" of your project"
artist: <PERSON>
date: 2025-06-23
url: https://www.youtube.com/watch?v=higAxJk_zig
---

(t: 0) If you've been using Claude code a lot, and if you're watching my videos, you probably have been, you may have come across this slash command called, (t: 10) let me just reduce that down, called compact. Compact basically takes the entire conversation history, all the context that you have, and it creates a summary of it (t: 20) so that you can start a new coding session. It happens when you get towards the end of the context window, and you can type it and add some optional instructions (t: 30) at the end of it. But the problem with compact in my experience is that it doesn't actually give a very good summary of what happened. So when you start the next chat, it actually doesn't really help that much. (t: 40) And it still seems to forget a lot of things that have been done and then try to do things in a very strange way. So I had to think about this (t: 50) and I built a better system for me. So in the project, right here we have <PERSON>, this Claude folder, and we have a set of commands. (t: 60) This is where you can add your own custom commands to get <PERSON> to do things that you want it to do. So we have a list of them here, and we're gonna go through each of these, starting with slash session help. (t: 74) So it runs this command here and it checks this file over here (t: 80) to see what it needs to do. And then it explains exactly what we can do here. It's a session management command. And we have a bunch of available commands (t: 90) like starting a session, updating a session, ending a session, listing the sessions and checking what our current session is. All pretty straightforward stuff. (t: 100) So what we're gonna do is run through a quick example here. And I'm gonna publish these. If you check below this video, you'll see a link to GitHub where you can access all of these (t: 110) and add them to your own system. So this project is on Next.js 14. And I wanna upgrade it to the latest version. So I'm just gonna ask Claude to do that for me. (t: 120) And first of all, I'm just gonna do forward slash session start. And then I'm gonna add, (t: 130) upgrade Next.js to latest version, 15 something. I don't know what it is. (t: 140) So now we're gonna start this session and it's gonna go ahead and create the sessions folder. And I'm gonna do that here because it doesn't exist yet. So I allow it to do that. (t: 150) Now we can see we have a sessions folder over here. So we can set all of this up for us as we go. Let's get rid of the file explorer there. (t: 160) And so we're gonna have a look at this. So the goal of this is to upgrade Next.js to the latest version. We're gonna update any dependencies, fix any breaking changes, (t: 170) ensure that all the features work. Also can test the development and production builds. And then we've got this progress. So we're gonna do that. So yes, we will allow it to do that. (t: 180) And you can see it's made a nice file name here with the date and the timestamp with a descriptive slug of what we're trying to do. (t: 190) So now I'm just gonna ask it to research how to do this and what the latest version is. (t: 200) Deploy sub-agents if you need. Deploy sub-agents if you need. Deploy sub-agents if you need. Deploy sub-agents if you need. Deploy sub-agents if you need. Okay, so Claude deployed three different sub-agents. (t: 210) The first one to check our current version. The second one to check how we upgrade to Next.js 15. And the third one was analyzing the code base compatibility. (t: 220) It's done all of that. And now it has a report for us. So we're on 14.2. The latest stable version is 15.1. (t: 230) That's not true, I don't think, but it's okay for now. And required React is version 19.1. And required React is version 19.1. So now it's gonna go through the breaking changes. Yada, yada, yada. None of this is really relevant to the video. (t: 240) It's just that when we come to save the session later, you're gonna see that it's gonna hopefully log all this stuff. Yes, please proceed, but also don't hard code (t: 250) to version 15.1 because I'm pretty sure there is a later version than that. We just want to upgrade to the very latest stable version (t: 260) of Next.js and make sure this all works properly. Okay, so Claude has done a lot of things (t: 270) to upgrade this project to Next.js 15. So now it's given me a bunch of testing items that I can go through. It wants me to check all these API endpoints (t: 280) to see if I can generate open graph images, whether newsletters can subscribe or be unsubscribed, whether we can refresh Google Sheet data on the homepage. (t: 290) Bunch of other things here, it says we've changed it from Next.js to Next.js 15.1. So we've changed it from 14.2 to 15.3. We've migrated all API routes from Pages router (t: 300) to the app router. TurboPack is enabled now for faster development. We've switched our email newsletter SDK to ListMonk. (t: 310) TypeScript issues, we're adding suspense for search params. Now a bunch of performance benefits as well. (t: 320) So at this point, we could, if we wanted to continue working on this project, we could do that. So if we wanted to continue working on this project and changing things, update our session by going to slash session update, (t: 330) something along the lines of update the session with a detailed log of everything we did so far. (t: 341) And then when you've done that, you can then continue to code. For us, because we're finished, we can just do session end. (t: 350) Now, what this is going to do is go through the rules inside that file and create the entire summary for us. (t: 360) And we'll see what that looks like in a sec. The session has ended and it's been documented to the correct file. Let's hide Claude. Go into the sidebar over here and we'll check out this file. (t: 370) We will get rid of that. And we can go through the entire session. So this is what we're doing. We're upgrading it to the latest version. Start time, focus. (t: 380) Here are the goals. We can ask it if we want to check these off as well so that it knows that they're complete. We probably shouldn't do that. But we can go through everything that's been done. (t: 390) The session lasted 20 minutes. Number of files were changed. Here's all the files. Here's what we did to each file that was modified, deleted, new files, commits made, (t: 400) because we haven't done that yet. But if we had made some commits to GitHub and pushed these changes, they will be there as well. And we now have a final Git status (t: 410) that the files are ready to commit. All of the items have been completed. So I guess we don't need to do that because they were the goals and this is what's been done. Completed tasks, key accomplishments, (t: 420) exactly what we did in a huge amount of detail, exactly what Claude changed in his project as we were going through it. This is one of my favorite sections here, (t: 430) the problems encountered and solutions. Because a lot of times with AI, you will fix the problem and leave, come back, and build new features. (t: 440) It will encounter the same errors over and over again. So for example, here, there was a frame of motion type incompatibility when it tried to run it. And the solution was to update that to the latest version. (t: 450) So we can keep a running log of everything that we change in this project in these individual session files. And then we can add them as context later if we need to. (t: 460) And then what I can do is just open this file here. And now we can see that Claude Code knows (t: 470) that we're in that file and I can just ask it questions. It knows that we upgraded it. We enabled TurboPack. We migrated a bunch of API routes. (t: 480) We fixed some TypeScript errors and the key learnings that we can use to go forward. And so Claude knows now that the next step really that we need to do is commit these changes before moving on. (t: 490) Hopefully this gives you an idea of how you can use Claude Code to save memory for each session that you code with to give you a much better history that you can go through (t: 500) and add as context as and when you need. I guess the next step for me with this would probably be to categorize them by feature (t: 510) or some way that Claude could automatically look in a specific place and try to find session overviews that he might need for a given project that we're working on.

