---
title: I was using Claude Code Wrong... Until I STOPPED Doing This
artist: Income stream surfers
date: 2025-08-20
url: https://www.youtube.com/watch?v=T8IxvrEBYVc
---

(t: 0) Hey guys, welcome to this video. This video is going to be more of a theory-based video than anything else. And let's just get straight into it. I want to talk about two different ways to create a project. (t: 10) The first is one-shot. One-shot. Now this is kind of the prevailing logic behind building anything with Cloud Code right now. (t: 20) Basically, one-shot would be something like context engineering, Archon, or Cloud Flow in order to just build the entire project one step. (t: 30) In one prompt, right? Now this is what most people are doing. And I actually just wanted to make this video to say that this is actually wrong. (t: 40) So for something simple like a service website, or, I don't know, like just an SEO website, this stuff is fine. (t: 50) You can just one-shot a website like this. Easy peasy, lemon squeezy. However, the actual best way to build a complicated project is step-by-step, right? (t: 60) Now, what do I mean by step-by-step? So if you try to one-shot a prompt or one-shot a SaaS, this is specifically for SaaS and apps and complicated projects, right? (t: 70) So SaaS, apps, complicated projects, etc. This is actually the best way to do it, step-by-step. (t: 80) Now a couple of things that you need to know before you do this is you need either, well, not either. You can use MCPs if you want, but you don't actually need MCPs. (t: 90) What you can actually use is CLI tools. So for example, Superbase, Docker, GitHub, (t: 100) all that good stuff. All of these are really, really good CLI tools as well. Stripe as well. You don't, you don't actually need to use MCPs. I think this is a common misconception. People are so obsessed with MCPs because of like the hype and stuff. (t: 110) 99.99% of MCPs can just be replaced. So, so, I'm going to show you how to do that. So let's start with the first one. So the first one is the MCP tool. (t: 120) And the MCP tool is actually, you know, it's actually better to use the CLI tools just because, you know, whatever you're using is more used to using CLI tools. Now, what does that mean? A CLI tool is basically where it runs a command in the command line interface, (t: 130) right? Instead of using an MCP tool call instead, what it does is it just runs the CLI tool. (t: 140) Okay. So instead of doing everything in one shot, the best way to actually do it step-by-step. So the way that you do that, you're going to do it step by step. Is I'll just spread out. You open Claude code. (t: 150) You tell it to add any CLI tools or you manually add MCPs. You tell it to build you the framework of the project, (t: 160) right? Then you build the functionality. (t: 170) Let's say backend of the project. Once the backend is done, then you build the functionality. You build the front end of the project. (t: 180) And at all times you have to be testing at all times. You need to be building systems, not one offs. So what I mean by that is tell it to build a theme or something instead of (t: 190) just saying like, I'll make this look nice or change this, change that at this, that instead, what you need to be doing is you need to be building into this systems. (t: 200) Now, a few more tips here definitely would be that you should, you should tell it to build services. (t: 210) So what I mean by that is service files, because that's a really, really, that's like the way that you should be building stuff, right? You need to choose a framework. I like to use HTML, CSS, (t: 220) JavaScript with a fast API backend. However, if you want security, then use react or next JS instead of HTML, CSS, JavaScript, and then just literally step by step, (t: 230) always give it the documentation it needs. It needs more access to the internet. Don't just let it web search. (t: 240) Don't let it web search. The web search stuff is not very good. I can tell you right now. And then another part is constantly have flawed code, (t: 250) update the memory as it's going, get it to store the overall implementation, dime the file or whatever. (t: 260) And, and then you can just go back and do it. And then you can just go back and do it. And then you can just go back and do it. (t: 270) And then you can just go back and do it. So basically what that means, if you tell it to, when you're first planning the entire project, right, then tell it to create an implementation, (t: 280) dime the file of the entire project, and then reference that in the memory file. So inside the memory file, reference the implement. Tation by MD, (t: 290) inside the. The memory file. Connects to your DB. Use super base CLI or MTP to do this. Have a different set up for dev and prod. (t: 300) Push your code to GitHub on a dev branch and a main branch. Push your code to doc, (t: 310) not Docker, Digital Ocean, or AWS with a staging and prod side. (t: 320) Do all your build, locally on Docker with the same set up as Digital Ocean or AWS. (t: 330) AWS. And only when you're pretty much at the MVP should you launch. (t: 340) Then if you need to make a change, pull from dev, (t: 350) make the changes. Push to dev, test, then push to main. This entire process here, this is how I built SEO Grove. (t: 360) If you don't know, SEO Grove is my SAS project that we created. This is it right here. This is the sponsor of this video as well. SEO Grove. If you have a Shopify or soon to be WordPress website, then sign up now. (t: 370) It's your last chance to join the wait list where you get a discount. SEO Grove is the best way to get started. I have a lot of other great tools, but I'm gonna talk about them in a bit. But, if you're just starting out, I would recommend that you go to this website. It's called SEO Grove. (t: 380) It's an online store. SEO Grove.ai. Check it out guys if you're interested. But basically, this is the entire process that I followed to build SEO Grove. I'm gonna be really honest with you. (t: 390) The one-shotting thing, all it really does is just create like a lot of, a lot of crap and a lot of mock and a lot of whatever. (t: 400) I think if the models were better, or if like we use GPT-5 or something, maybe, then it would be a bit better. But, there's definitely an issue. (t: 410) Right now with all of these things, and this is from all of my testing, right? I like to test a lot before I make videos. I've been testing these three things for the last month or so. (t: 420) I would say for building a service or SEO website, they are easily the best. If you wanna create something really cool and amazing that actually ranks on Google, (t: 430) then use these. And you could probably use these for your front end on your SaaS as well. But when you're building a complicated SaaS that has like 10 different service files, and they all need to work together, (t: 440) and they all need to have a very specific result, I'll tell you right now, you will waste more time trying to one-shot this with AI than you would just simply kind of doing it step by step, (t: 450) creating one at a time, looking at the output in the terminal of every single run, making sure AI is working, (t: 460) make sure that each step is working. You will go a hundred times faster doing this step by step than you would attempting to one-shot such a cloud. So, I'm not saying that this is a complicated project. (t: 470) I am speaking from experience here, and I'll tell you right now, if you build one file with AI integration, what I mean by that is like, you have something in the service like Claude (t: 480) or ChatGPT, the processors, or, you know, sorts out data, or gives an opinion, or makes a decision, (t: 490) or all of these amazing things that you can do with AI. If you try and build a one-shot of this, it just won't work. I've never, ever, ever had it work. (t: 500) The only time I've had it work is when it's a very simple thing, like I did a keyword tool, right? Keyword tool. This can work because it's one file. It's quite simple. (t: 510) There's only one or two pieces of AI, or one or two prompts, or whatever. It can handle that. For sure, it can handle that, but it cannot handle a complex AI application in my experience. (t: 520) Feel free to comment, prove me wrong. Let me know how you do it. If you can show me how you do it, I will try any methods, that you leave in the comments. I am completely open-minded, (t: 530) and I am willing to change my mind immediately if someone can prove me wrong. I have never been able to one-shot a very complex AI app (t: 540) with third-party APIs, AI app, this, that, the other, you know, API key here, integration here, blah, blah, blah, blah, blah. (t: 550) It's just too complicated, in my opinion, for AI at the moment, even for Cloud Code. I'll leave the video there, guys. Thank you so much for watching. If you're watching on the right, I'll be posting the video. You're an absolute legend. And I'll see you very, very soon (t: 560) with some more content. Peace out.

