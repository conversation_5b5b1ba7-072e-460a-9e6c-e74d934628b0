---
title: "Context Engineering 101 - The Simple Strategy to 100x AI Coding"
artist: <PERSON>
date: 2025-07-17
url: https://www.youtube.com/watch?v=Mk87sFlUG28
---

(t: 0) Context engineering is the new big thing for AI coding, not because it's brand new, but because people are finally acknowledging that putting the time up front to provide extensive context to AI (t: 10) coding assistance is what separates real results from vibe coding and basic prompt engineering. And most people are still experimenting with these ideas without a true systematic process. (t: 20) And so in this video, I want to dive into using context engineering for real production builds, essentially taking my template that I covered on my channel recently, but going much deeper for you. (t: 30) Now here's why context engineering is such a big deal. We know that vibe coding builds prototypes that break when you try to scale and prompt engineering is all about word tweaking, (t: 40) working with specific phrases to get single better outputs from LLMs. In fact, the way I've heard it recently is context engineering is a super set of prompt engineering. (t: 50) It's a part of it, but context engineering is so much more. It's providing all the right tools for you to use. So if you're looking for a way to get the most out of your code, then context engineering is the right place to go. It's a part of it, but context engineering is so much more. It's providing all the right tools for you to use. It's providing all the right tools for you to use. It's providing all the right tools for you to use. Information, examples, best practices, constraints to AI coding assistance upfront. (t: 60) It certainly is a time investment, but man, will it 10x your process for building literally anything. And I mean that. Now there are many ways to go about context engineering, but the strategy that (t: 70) I keep coming back to time and time again, that my template for you is also based on is the PRP framework from Rasmus. And this is just a genius. And Rasmus is actually a guest (t: 80) for a part of this video today. So I'm really excited to get to know you guys. So I'm really excited for you to see that he's going to dive into PRP, his philosophy behind it, (t: 90) why it works so well, how you can get started with it right now. And then what we've done together is we have actually taken my base template that I have in the context engineering repo, (t: 100) and we've made one that's specific to building MCP servers. So you can use the PRP framework to build any production ready MCP server that you want, just as a super practical use case. (t: 110) We're actually going to go through and build one together in this video using this exact blueprint. So let me show you what I mean. If we go back to the root of the context engineering (t: 120) repo, which this is the template that I'll have links for you in the description of the video, by the way, everything that we have here is a generic template for using the PRP framework. (t: 130) Like if we go into our slash commands here, these are just very generic slash commands that would work no matter the application that you want to build. And then if we go back out and we look at (t: 140) our PRPs, we have the template here. Again, just very, very generic. This applies to any kind of software, but there's certainly an opportunity to make a template for you that's already crafted (t: 150) to be much more specific to a certain kind of use case. And that's exactly what Rasmus and I have (t: 160) been working on for you. So we actually took the production ready Cloudflare MCP server I showed in the last video on my channel, and we built a template around it. So we're going back here to (t: 170) the use cases MCP server folder in my template. We've taken the MCP server that I built my last video. As an example, we've taken the MCP server that I built in the last video, and we've taken the example built into this template. And if we look at the slash commands, for example, we have (t: 180) made things much more specific to building MCP servers. And even more importantly, we did the same thing with the base PRP. This is the reference that we use to create that full PRP. And (t: 190) now everything is very specific to building MCP servers. And so now you can use this as a launching pad, no matter the MCP server that you want to create. And you're going to get much better results (t: 200) because things are so hyper tuned to building and using the MCP server. So if you're looking at a MCP server, and by the way, going through this process yourself to build an MCP could not be (t: 210) easier. And this is what I'm going to show you how to do after we have our chat with Rasmus. So literally, you just have to go into this initial.md file, which I talked about initial.md in my first (t: 220) video on context engineering, you fill out the information for the MCP that you want to create, and then you generate your PRP with this slash command, which can just be a prompt for any other (t: 230) AI coding assistant besides cloud code. And then it's important to validate the PRP and be a part of this process. Don't trust the AI blindly. But then you can execute your PRP. And (t: 240) this is going to build your MCP server. And you can just iterate from there. It is that easy to get started. And a lot of this is revolving around the power of this MCP PRP based template that (t: 250) we've crafted for you. And this my friend is just the beginning actually a huge project that I'm starting in the Dynamis community is essentially building out a massive repository of all of these (t: 260) use cases. So no matter the programming language that we're using, or the type of application that we're building, there's going to be a template here using the PRP framework, (t: 270) but much more hyper tuned to what we want to build. And that's exactly what I'm showing you here with MCP just as a super good example. And by the way, if you're interested in this, (t: 280) and just taking your AI coding to the next level in general, definitely check out dynamis.ai. There's so many things happening there, like building out all of these PRP templates. So I'm (t: 290) about to show you a live example of the MCP server that we built based on this template. And we did it in two shots, there was one follow up prompt that we had to do to fix two bugs. But after (t: 300) that, we have 18 perfectly working tools and this very non trivial MCP server. It's so cool. This is (t: 310) the PRP Taskmaster MCP, you give a PRP and it'll extract tasks based on that and help you manage them. It is very similar to Claude Taskmaster, but for PRP s instead of PRDs. Now it's not the most (t: 320) practical MCP, but it's a very simple one. And it's a very simple one. And it's a very simple one. And it's a very simple one. And it's a very simple one. And it's a very simple one. And it's a very simple one. I'm going to show you how to build a new MCP server, though I will probably be building it into (t: 330) Archon at some point. So stay tuned for that for Archon version two. But I wanted to give a nod to Claude Taskmaster because it really is an incredible MCP for context engineering. And I thought this (t: 340) would be a good example, showing how we can replicate something that's already out in the wild. That is really incredible. And like I said, not trivial. And so I've got a prompt pre crafted (t: 350) here. And this is what I love doing when I'm testing an MCP server for the first time, I just say we've got this new MCP, I tell it what its response is. And then I'm going to go ahead and tell it what it's responsible for doing. And then I just say, I want you to test all the (t: 360) functionality of this server with a flow that actually makes sense for starting a new project, adding tasks and dependencies documentation, we have the tools to manage all of these different (t: 370) things. And I'm just saying don't go overboard, we just want a simple demonstration here, and then create a sample PRP just to start things off. So I'm just using the mock data here. And (t: 380) I've got my database already set up. So we have our projects table tasks, task dependencies, there's quite a bit that's going on behind the scenes to manage everything with this MCP. (t: 390) And so yeah, let's just go ahead and kick things off here. And so it's going to start by mocking a PRP, sending that in to extract the tasks from it. And so I'm going to pause and come back once it has (t: 400) done this, because this part takes a little bit, it's actually using the anthropic API under the hood here. And there we go, our project is created, we can actually verify this. So if I go to the (t: 410) projects, yep, we got a simple to do app that we've have added to our database. And now it wants to list projects. So we'll go ahead and do that. And then we're going to go ahead and create a new project. And then we're going to go ahead and create a new project. And then we're going to go ahead and do that. And then we're going to also have the tasks already in here. So if I go into my tasks, (t: 420) we can see that we have a few already. And so now it'll just go through list the tasks, and just kind of go through a mock flow, I assume of marking some tasks off. Yep, (t: 430) so getting the project over, he was probably going to add some documentation as well. But you can see how it's just knocking out one tool at a time, testing everything for me, so that (t: 440) I just know that my MCP server is working correctly. And especially when you are using something like the PRP, you're going to want to make sure that you're not just going to be doing the same thing as you're doing the same thing. So if you're using the PRP framework, and you're coding something with the help of AI pretty heavily, it is important to validate everything like you do want to test (t: 450) every single tool in your MCP server, for example. And that's a big thing that we'll see in our conversation with Rasmus is validation is still super important. Like when you generate your PRP, (t: 460) you still want to make sure that you're analyzing it being a part of the process, not just generating the PRP and then executing it, right. And it's the same thing with the output as (t: 470) well, like you want to understand the code that it output, validate that, test things like I'm doing right here and have the AI coding assistant create tests as well. (t: 480) And so yeah, I think that's enough of the testing here. Everything is looking really, really good. I don't want to bore you with the rest of this demo. But yeah, that is our MCP server at a high level. And like I said, we can use this template to build literally any MCP server and it's going (t: 490) to be production ready. And there we go, we have all of the core functionality tested for (t: 500) our MCP server, things are looking really good. So here's what's coming up next for you. First, a quick word from our sponsor, then we'll dive into the conversation with Rasmus. And man, (t: 510) that is one that you don't want to miss. It was a pleasure having him on the channel. And then last but not least, I'll show you my full replicable process for building this MCP server with the PRP template that I showed you already. This my friend is peak AI coding. (t: 520) The sponsor of today's video is Lindy. Think of it like if AI and Zapier had a baby. (t: 530) But what really makes their platform stand out to me is their feature for parallel agents, called agent swarms. Take a look at this. So you build your workflows just like you would with (t: 540) something like n8n. But you can create these loops here, where it's going to spin up many different agents to knock out a bunch of tasks for you at the exact same time. So this workflow specifically (t: 550) is for deeper research. And so I can have many agents spun up at the same time, you could change this to the top 10, the top 20. So to research all these subtopics at the exact same time, so it's (t: 560) much faster than something like opening eyes deep research. And so just to go along with the theme of our video here, we're going to go ahead and create a new agent. And we're going to go ahead and create a new agent called action. And then in the end of the video here, I'll ask what are the best strategies for using a cloud code. And we'll see in a second, once it enters the loop, it's (t: 570) going to create a multiple different agents all at the same time spun up to research different angles for my question. And so it'll still take some time because it does pretty deep research here. (t: 580) And so we'll see in a little bit, all the loops will complete and then it'll give us our final response. And there we go, all the loops have completed, it's done the deep research for (t: 590) each of my different angles. And then in a second, here, we'll see the final response. summary spit out. So this is the research that was done for each of these agents. And there we go. (t: 600) We now have our full deep research. Also, Lindy has partnered with Pipedream to give you more than 5000 integrations across 25 different apps, and 4000 web scrapers through their partnership (t: 610) with Appify. There is not a platform with more integrations than Lindy. So I'll have a link in the description to Lindy, you also get 400 credits for free to try out the platform when you use that (t: 620) link. So definitely go ahead and check them out. All right, we have the man himself here, Rasmus, the creator of the PRP framework. And I'm just really excited to bring you in, (t: 630) talk about the framework, your vision for it, your philosophy behind it, because there are a million ways to context engineer. It's a pretty new phrase overall, (t: 640) but it's not a new idea, right? But the PRP framework is just what I've been going to time and time again recently. And it's just made my AI coding that much better, like 10 times (t: 650) 100 times better. So I'm going to go ahead and check that out. So let's get started. 100 times better. And so I just absolutely love it. And I know that you've been developing out this framework for over a year now, which is insanely impressive. And so I just wanted to (t: 660) hear from you what inspired this and where you're going with it. So yeah, if you want to feel free and just introduce yourself and kick things off telling us about the PRP framework. By the way, (t: 670) guys, we met in the Dynamis community. And when he was first sharing this PRP framework, it just stuck out to me immediately. Like I knew it wasn't just another one of the 1000 strategies I'd heard (t: 680) like, this is the PRP framework. And I was like, I'm going to check that out. And I'm going to So yeah, let's hear from you, Erasmus. Thanks for that intro. So yeah, first of all, since you also featured the PRP framework in your (t: 690) context engineering video, I just want to say thanks to everyone who has embraced it and sent me all of the amazing projects that you have built on top of it. I've seen some really awesome stuff (t: 700) there. But yeah, so what PRP is, it's a product requirement prompt. I've spent many years as a (t: 710) product manager, working with a lot of technical documentation and PRDs in particular. So PRP is really my attempt at building a prompt framework around product management. So like bringing the (t: 720) context needed for a coding agent to build the feature or the product that I wanted to build. (t: 730) So that's the basic idea. In short, I would like to just describe it here. (t: 740) So it's a PRP is a PRD plus curated code base intelligence plus agent runbook. And it's aiming (t: 750) to be the minimum viable packet an AI needs to plausibly ship production ready code on the first pass. So as you said, as well, I've been developing this for kind of over a year. It has had in that (t: 760) year, it has many shapes and forms. And it started out using like Aether and Klein. (t: 770) As the main drivers. And yeah, it's very inspired by in the depth on the Klein community and the (t: 780) memory prompting framework that that a lot of people use over there. But yeah, that's how we got started. I needed it for for an existing project that I was building at the time evaluation (t: 790) engine. So yeah, I used PRP or the very baby version of PRP to build that out and ship it to (t: 800) production. Yeah, that's really cool. Thanks for sharing. Erasmus. And the thing that sticks out in this read me here is the industry mantra garbage in garbage out applies doubly to prompt engineering. And that (t: 810) is so true. And it also speaks to the fact that like, prompt engineering very much is a part of context engineering and the PRP framework. But really, those are a superset. There's so much more (t: 820) that goes into how we're engineering the context here, especially with the PRP framework and how far we can take things with it. And so speaking of that, actually, (t: 830) let's hop over to the template now and GitHub. Specifically, what we wanted to give you guys with this template is just a clear example of how you can apply the PRP framework to build something new. (t: 840) And so this is very much based off of my last video on context engineering, where I gave more of an intro. But now we're showing you a real example with a template that you can use literally to take (t: 850) this use a PRP framework to build any MCP server that you want. So it's also based on my last video where I showed building production ready MCP server. And I'm going to show you how to build a (t: 860) PRP server with Cloudflare and TypeScript. But now we've put everything related to PRP on top of it so that you can go through and build your own MCP. So Razzis, I'll just let you do the explaining for (t: 870) how this works and how people can get started with this. You're definitely going to do that better than me. Yeah, so that's exactly what you just described is what we did, right. So we took what (t: 880) you did in your past two videos, context engineering with the PRP framework, and also the MCP server starter template. And so we've got a lot of stuff that we've built into the PRP framework. (t: 890) So we've got a lot of stuff that you've built. And we put together specialized PRP prompts and templates that will help you build any MCP server that you can build. And I can pretty confidently (t: 900) say that this is probably the easiest way to build an MCP server. So what you really need to do (t: 910) is you take your plan, whatever you want to build, and you define it in initial.md. So this can be (t: 920) as good as you can, like all your tools you want to have, your business logic in as much detail as you can. And then you will take that plan and then you will run that through the PRP framework. (t: 930) You can use LLMs to start planning your work and everything. So I highly encourage you to use LLMs (t: 940) also in your planning and preparation. Then you will run the first slash command, which is the base create or MCP create. (t: 950) and you will pass your plan as the argument. And what this really does is this is what the this is where the context gathering part happens. So it will pull in all of the context that it (t: 960) thinks it's that it needs to to finish your work, like all the context that the AI itself thinks (t: 970) that it will need to complete the work of your plan, and it will pull it into this base template. If we take a quick look at this base template, though, we have already done a lot of that (t: 980) context engineering for you. So a lot of the references that the AI will need is already in here. So it will only build on top of what we have already created for you here. (t: 990) So this base template already has a lot of references to the template that Cole built last week. So it references all of the patterns, references the tool, the tool registry, how to do (t: 1000) that database. And it really references a lot of things here that comes from that template. (t: 1010) So when you run the first command, it will pull your idea and add more context really to this (t: 1020) template as needed. The beauty of that is we have this template already made for you. So there's a lot of context engineering that goes into the PRP framework, but there's just a lot less when you (t: 1030) have this as a starting point. And that's actually something that Rasmus and I want to start up really soon here is working on a repository of all of these base PRP (t: 1040) templates. So no matter the programming language you're using or the specific type of project, like an MCP server you want to make, there's going to be some template to give you this (t: 1050) massive launching pad. So you can create all of this context, but not have to do it all from scratch. Like you have the starting point. And then also there's a lot of like manual work that (t: 1060) goes into really making these PRPs robust as well. And I know that you wanted to touch on that too, Rasmus. Yeah. I mean, you can just take the example that me and Cole has built here. Like we spent a lot (t: 1070) of time like manually or semi-manually, I would say, because we took a lot of help from Cloud Code, of course, to prepare the context for this base template. So whenever you have created your (t: 1084) base template, you always should read through it and make sure that it actually references (t: 1090) the things that you want and that you need it to reference. So that it doesn't say something weird. I spend a lot of time actually like reading through these (t: 1100) and making sure that they are good before I execute them. Because especially when I execute things in parallel, I need to make sure that they are not (t: 1110) tangled too much. So even if I run them in different worksheets, I don't want to have the conflicts when I try to merge it later on. So if you want to scale this up, especially you (t: 1120) need to make sure that they are not tangled, but you also might need to make sure that they are actually following the business logic that we're working on. So that's really important. So feel following the business logic that you intend them to follow. Read through your PRPs before you run the execute command, (t: 1130) which is the last step. Yeah, definitely. Yeah, validation is always important. And that's the big thing that is so wrong with vibe coding, right? (t: 1140) There's just never that validation. And we need that even for the context that we're engineering. And I think the last big piece that we haven't hit on here is our claw.md, (t: 1150) or just whatever your global rules are for your AI coding assistant, which, by the way, you can use this whole process with the Gemini CLI or Cursor or WinServ. You don't have the idea of slash commands like you do in claw.code, (t: 1160) but you can just take these slash commands and then use them as regular prompts, right? And so, yeah, diving into global rules now, I want to dive into this with you, Rasmus, (t: 1170) because I think a really big distinction we need to make here is when do you put context in the global rules, like claw.md, versus when does it belong in the PRP? (t: 1180) Yeah. So, yeah. I like to think of it like this. Like, I treat my claw.md as where I put, like, the constant rules that will very rarely change. (t: 1190) So things that will be true forever in my code base and things that will be added to my code base that also will be true forever or for a very long time. (t: 1200) So whenever you add, like, a new principle, like a new naming standard or maybe a new function that is really, really core to your code base, (t: 1210) also you can add it there. So, yeah. Things that will stay the same for a long time, but also append new things that keep coming in. You can also put claw.md files in specific folders (t: 1220) that references specific things in that module or folder. That's also really powerful. Then I like to think of the slash commands as really where they should just be, like, drop-ins (t: 1230) for whatever thing you're working on. So they shouldn't hold any specific... So the create command is just the planning. (t: 1240) It's just, like, a huge... It's a planning workflow, really. Like a research workflow that has really nothing to do with anything specific. It becomes specific because you pass in your specific plan. (t: 1250) And the same goes with execute. Execute command is just a prompt chain or a prompt that will tell the AI how to execute your PRP. (t: 1260) So it has nothing to do with the PRP itself. And then the base PRP is really where you put your specific, (t: 1270) context to that specific piece of work that you're doing right now. Yeah, that's really good. (t: 1280) And I just like the phrasing there. Like, claw.md is where the things go that aren't going to change. Because really, when you start to work on an existing code base, you'll have kind of a mini PRP for each feature that you want to develop. (t: 1290) Because there's always going to be slightly different ways that you want it to go about implementing a feature. But claw.md, that's the things that are the same between all of those features. And actually, that kind of leads to the next question (t: 1300) that I wanted to ask you, Raz. And this is, how well does the PRP framework work on existing code bases? I know that's something that people have been asking quite a bit. (t: 1310) Yeah, I mean, I think it's purposely built for working on existing code bases. So that's the use case I needed it for when I started building it. (t: 1320) It definitely wasn't perfect when I started building it. But I bet that I made a quite big bet that it would soon work well with better model. And I think especially with cloud code, (t: 1330) we have seen that. It's a very good example of how it worked. And I think it's a very good example of how it worked. So even the difference between how it worked with cloud 3.7 and cloud 4 is insane. (t: 1340) I used to kick off a 100-line PRP, and it would run it reliably nine times out of 10, maybe seven times out of 10. And after cloud 4, I can kick off a 500-line prompt reliably, (t: 1350) a 1,000-line prompt semi-reliably. And I have kicked off 1,500-line prompts reliably. (t: 1360) Crazy. Yeah. Not 10 out of 10, of course. But experimentally, I try a lot of things. Yeah. (t: 1370) And it's important to say, it ain't perfect. It definitely isn't. But it feels close. It's just incredible, the kind of things that we can build with the PRP framework and just using that as our way to context engineer. (t: 1380) And the last question that I want to ask you, Rasmus, actually the one that I've gotten the most ever since I made my context engineering intro video, (t: 1390) is why put this much time into it? Why put this much time into it? Why put this much time into it? Why put this much time into it? Why put this much time into it? Why put this much time into it? Why put this much time into it? Why put this much time into it? Why put this much time into context engineering? I know that we've talked about this throughout our conversation, but I definitely sense there are going to be some people that are still hesitant (t: 1400) to put this much time into sharpening their axe, so to speak. So what would you have to say to that? Like, why should we put the time into engineering this much context? (t: 1410) Yeah. So for me, it's obvious because I come from product management. So I used to do this anyways. I used to do this type of work anyways, (t: 1420) but not for an AI assistant, but for... for a team of developers. So it really has built on that. Like someone building a real project, someone is doing this work, (t: 1430) whether it's you or whether it's an AI or someone else. So really thinking deep about the important questions, like why are we building this in the first place? (t: 1440) Who are we building it for? How exactly should the user experience look like? Those are really important questions to answer. And then making the decisions (t: 1450) of how to build that and how to build it in the first place. And then making the decisions of how to build that and how to build it in the first place. Like when you build your first prototype or something or a version of something, you have to like make that decision and then go test it on users. (t: 1460) And when you work on a real existing code base, you will get a lot of that feedback from users already. So you can like build your PRP based on the user feedback that you're getting already. (t: 1470) So that's why it works so well on existing things too. Not only that you already have user feedback to get, but you also have an existing code base to follow. That's exactly what this example is showing as well. (t: 1480) That when we build a new version of PRP, when we have like this couldn't have been possible to one-shot this like large piece of work without having such a good foundation (t: 1490) that this MCP server template really has. So the AI will have like all of these patterns to follow that already exists and that will help us build this out reliably. (t: 1500) Yeah, that is really good. Thank you, Rasmus. That's everything that I had for questions here. And you just set the stage so well for why we should care about PRP and how it works too. (t: 1510) And so now what I wanna watch is, I wanna walk everyone through is a live example using this template to build an MCP server from scratch. And so I'll show you everything (t: 1520) that Rasmus walked us through, but we're gonna go through it step by step. So I'll create that initial .md, right? And I'll generate the PRP. We'll build the MCP server and validate everything. (t: 1530) And it's just amazing the results we get. So thank you Rasmus for walking us through everything. And now let's get into the demo. So here we are in the read me for the MCP server template (t: 1540) that I have in the context engineering intro. This is what I was showing earlier in GitHub. And so now I have the cloned locally. I'll walk you through the three steps here to create our MCP server from scratch (t: 1550) with all the contexts that we have as a launching point. And so we'll create the initial .md where we describe the MCP server that we want. We will generate and validate our PRP (t: 1560) and then finally execute it. And then you can iterate from there and deploy to Cloudflare workers. And definitely check out my last video on this MCP server that I have as a starting point for you (t: 1570) if you wanna go into the whole deploy to Cloudflare side of things. And speaking of that MCP server, that is a part of the context here. So we're essentially taking that MCP server, (t: 1580) turning it into a scaffold that we have in this template. So we have everything around PRP plus the MCP server as a scaffold. (t: 1590) And then all we need to do with our PRP when we generate and execute it is essentially tell it how to take the skeleton of an MCP server and add all of our custom tools. That's what we're gonna be doing here. (t: 1600) And so we need to start by editing our initial.md. So that's within the PRPs folder. And then within here, we can describe the MCP server that we want to create (t: 1610) based on this template. And so I have an example kind of fully written out here. This is to build our PRP Taskmaster MCP that I demoed earlier, (t: 1620) but you can adjust this to whatever you want to build. I just generally recommend keeping the feature section. Then we have the examples and documentation, which a lot of that we already have set up (t: 1630) in this MCP specific PRP. So we can just go ahead and do that. And then we can just go ahead and do that. And then we can just go ahead and do that. And then we can just go ahead and do that. And then we can just go ahead and do that. And then we can just go ahead and do that. And then we can just go ahead and do that. So this is a PRP base that we've been talking about. And then other considerations is also very important. This is a good place to reference any gotchas that AI Coding Assistant seem to mess up on a lot. (t: 1640) Like I talk about how to use environment variables, for example. And this is very much following the initial.md that I described setting up (t: 1650) in my last video on context engineering. So it's very much the case that we took the root of this repository and now we've just made a super-specific version to building MCP servers. (t: 1660) And now we've just made a super-specific version with my last MCP as added context, kind of like a scaffold, right? So that's the beauty of this. And so what I've done in my initial.md is I've said something pretty simple here. (t: 1670) Like we wanna build an MCP. We wanna create a simple version of the Taskmaster, but parsing PRPs instead of PRDs. And I probably could have been a lot more specific here, (t: 1680) but I just wanted to show this as a demo. And so your initial.md might be quite a bit longer. You might wanna be a lot more specific. This is just to get you started. It's just to give you a general idea (t: 1690) of what would go in each section. So these are all the features that we want in our MCP, like the different tools we're looking for. And like Rasmus said, you might wanna actually have a PRP per tool (t: 1700) if you really wanna make sure it nails it. In our case, this is actually working really well, which is just insane. And then for the example documentation, most of it is already within our PRP MCP base. (t: 1710) And then also I'm giving it the GitHub repository to Cloud Taskmaster. So as a part of our PRP, we are going to tell Cloud Code to actually go look at the implementation so it understands all the tools, (t: 1720) so it can replicate that for our PRP Taskmaster MCP. And then just a few considerations that I've got here at the bottom. So that is my initial.md. (t: 1730) And then going back to the readme here, let me open up the readme. Once you have that set up, now we just need to generate our PRP. So I'm gonna do Control J to open up a terminal here in my VS Code. (t: 1740) And then I'll type in Cloud. So we are within Cloud. And then I will do slash. And then all we have to do is slash PRP MCP create. I can press tab to autocomplete. (t: 1750) And then I just have to specify my initial.md, which you can just call out the file name itself, or even better, the relative path to the file in the repository like this. (t: 1760) Boom, there we go. So I'll go ahead and send this in. I'll do shift tab so that auto edit is on. And now it's going to do a bunch of research based on the MCP that I want to create (t: 1770) based on the PRP MCP base. And it's going to create a custom PRP for me based on this template. And it's gonna take a while. It does quite a bit of research (t: 1780) and architecting and planning. And so I'll pause and come back once we have our PRP and then we'll validate it together. And there we go. After about 10 to 15 minutes, (t: 1790) we have our custom PRP generated. So I'll do control J to exit out of my terminal, click into this PRP and let's take a look. And so remember, we need to validate everything here. (t: 1800) And these are pretty long generally. These PRPs can get quite lengthy, but it is worth taking a look at everything, at least glazing over the details here. (t: 1810) Making sure there's nothing crazy, making sure that it's actually going to implement the MCP server or the application that you want to implement. And so, yeah, let's take it from the top here. So we are building an MCP server. (t: 1820) The goal is to build a production ready MCP for a PRP parsing system, task management tools. We got GitHub OAuth, Cloudflare worker deployments. (t: 1830) We're using Postgres to manage everything in our database. This is looking really good. And it lists out the tools that we want for PRP parsing, task management, documentation, (t: 1840) basically everything that you saw in the demo earlier. Really, really good authentication and database. And one thing I want to show you that's really important is we always want to have a section in the PRP (t: 1850) that talks about all the documentation and references that we want to bring into the context for our AI coding assistant when we execute the PRP. And so this is something that you definitely want to reveal in detail (t: 1860) because everything that we have listed here, it's going to be reading through and using as its reference point for implementation. So it'll understand here how to (t: 1870) register new tools for MCP and hook that into our scaffolding. That's super important. It's going to analyze the existing code base to see how we set up our MCP server. We've got official documentation from MCP. (t: 1880) So generally, you want to have a good mix between external sources like our official MCP docs and then our existing code base and examples that we're giving. And all that together holistically creates (t: 1890) all of the context along with the PRP. There's just so much that goes into this. The other thing that's really cool is generally with PRP is what Rasmus and I have been doing (t: 1900) is having it list out the current structure for the code base and then the desired structure, what we want to add or modify for the final MCP server implementation. (t: 1910) And man, this just gives so much structure. And I've seen Cloud Code especially just stick with this so well. Like every time I use this kind of PRP, (t: 1920) whatever it outputs, like it actually matches this structure like file by file. It's so, so cool. And so there's not a lot that I've actually wanted to change at this point. So if we keep scrolling down, (t: 1930) one thing that I did notice off camera here is that it wants to update the .dev.vars, which that's kind of Cloudflare's equivalent of a .env. You don't typically want your AI coding assistants (t: 1940) to actually go and edit your secrets directly. So I would want to take this section out. And so that's just to give you an example of something that like you very much would want to change. (t: 1950) And that's why it's so important to validate your PRPs. I don't want to bore you with the details of like tweaking everything right here. And so my plan right now is to go through this entire PRP myself, like I'd recommend you doing. (t: 1960) I'll adjust things as I see fit. Then I'll come back and we will execute this PRP. All right, so I've made some adjustments to the PRP, (t: 1970) really making sure that it's implementing what I want it to do based on my initial .md. There's not a ton that I had to change overall. Like it did a really good job. And so now we can move on to the last part of the process (t: 1980) where we execute our PRP. And yeah, this is really simple overall, especially because of how we can use this template as such a good starting point for building MCPs. (t: 1990) And that's what I want to continue to build out for a lot of other use cases you would care about making as well. And so what I would generally recommend doing once you go back into Cloud here is I'd recommend exiting or clearing the conversation. (t: 2000) Typically, you want to clear the context. So you're starting with a blank slate when you move on to executing the PRP. And so now it's going to be PRP MCP execute. (t: 2010) Again, just tab to auto complete. And then what we want to do is just pass in the relative path to our new PRP. So it's PRP slash. And then PRP parser MCP dot MD. (t: 2020) There we go. All right. And I got auto accept edits on. Now it's going to first analyze the PRP. And then it's going to go through all of the documentation and example files (t: 2030) and everything, create a comprehensive to do list. And then it's going to start knocking out the tasks one by one. And it's even going to implement what Erasmus likes to call validation gates. (t: 2040) So it's going to have linting. And it'll create unit tests. And it'll run those unit tests and iterate on that until the MCP server is complete and working from at least what it can tell. (t: 2050) Right. And so it's a pretty extensive process. Like this is going to go on for a while. And so you can go and take your dog on a walk, go and work on another project to come back (t: 2060) once this is done. And you're going to have an insane starting point for an MCP. It doesn't always one shot, but sometimes it can as well. (t: 2070) And so, yeah, I will pause and come back once we have our implementation. And there we go. After about 25 minutes. Cloud Code knocked out all of the tasks for us. (t: 2080) And we now have our fully implemented MCP server. Take a look at all of the tools that are built here. And we have green checkmarks across the board. And so now we can use Wrangler, the CloudFlare CLI to run our MCP. (t: 2090) And I'm not going to dive into the details for how that all works with Wrangler and CloudFlare. You can definitely check out my last video where I really build out this MCP with you (t: 2100) to get those kind of details. But right here we can use the Wrangler dev command to run it, specifying the configuration that Cloud Code just made. So we can see that we have our new MCP server, our PRP Task Master. (t: 2110) So there we go. We'll run this. It'll simulate my production CloudFlare environment. So I basically have a local worker running and this is my URL to access the MCP server, (t: 2120) either with streamable HTTP or SSE for legacy purposes. And so going back into my IDE here within my cloud desktop configuration, I've added (t: 2130) this for my PRP Task Master. So just pointing to that URL. And then slash MCP for server. Or streamable HTTP. And then I can restart my cloud desktop. (t: 2140) So let me do that really quickly. Bring it over here. All right. And there we go. And then if you want to check and make sure that your MCP server is connected properly, you just click on search and tools, go down and there we go. (t: 2150) We have our PRP Task Master with the 18 tools that we have implemented. This is just so, so cool. All right. And so I will say that off camera, I had to iterate one time to get things working. (t: 2160) There's just a couple of bugs. And I think there's like one tool that I needed to. But otherwise it worked really well. (t: 2170) So it was a two shot for this MCP server. And it is basically the same as my last demo. So I'll just use the same prompt here so we can kick this off and just kind of see it in action here like we did earlier. (t: 2180) But yeah, we are good to go. And so now you can take this and deploy it to CloudFlare. You can follow the instructions in the README and also in the repo specifically for my Cloudflare (t: 2190) Worker Remote MCP, which I'll have that linked in the description as well. But yeah, the main thing that I wanted to show you here is done. We went through the three step process, creating an MCP server using context engineering specifically (t: 2200) with the PRP framework. It is just fantastic. And so, yeah, we can see this in action here. It's going to validate our PRP and ask us to approve this action, which I'll go ahead (t: 2210) and do. I won't show the full demo again, but yeah, things are looking really good. So we have built an MCP server from scratch. You can go through the same process no matter the MCP that you want to make. (t: 2220) So there you have it. That is everything that I wanted to show you. For context engineering and specifically doing it with the PRP framework. (t: 2230) So I hope you can use this as a starting point to very easily build any MCP server that you want and with a bit of imagination, you can see how you could adjust this to make anything that you want. (t: 2240) That's the beauty of context engineering is it's going to 10x your workflow no matter what you want to make. And like I said, me and the Dynamis community, we want to build out this repository of all these different templates to help you get started with anything. (t: 2250) And that is the future of AI coding. So if you appreciate this video. You're looking forward to more things AI coding and AI agents. I'd really appreciate a like and a subscribe. (t: 2260) And with that, I will see you in the next video.

