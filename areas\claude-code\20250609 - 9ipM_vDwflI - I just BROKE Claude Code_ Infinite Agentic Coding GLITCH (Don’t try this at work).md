---
title: "I just BROKE Claude Code: Infinite Agentic Coding GLITCH (Don’t try this at work)"
artist: IndyDevDan
date: 2025-06-09
url: https://www.youtube.com/watch?v=9ipM_vDwflI
---

(t: 0) Engineers, I think I just broke Clawed Code, the best agentic coding tool in the game. Check this out and you tell me. The amount of value you can create in a single prompt is incredible. (t: 10) The amount of value you can create in two prompts is insanely mind-blowing and not well understood. (t: 20) Let me show you exactly what I mean. Here I'm running a Clawed Code infinite agentic loop. Now what does that mean and what does that look like? Inside of this five directory code base, I'm generating infinite self-contained UIs that self-improve on each set. (t: 40) How is this possible? If I open up .clawed, go into commands, you can see I have this infinite.md prompt that's fueling this Clawed Code agent that's fired off five sub-agents. (t: 50) You can see them all working here live right now. This one just wrote 1,000 lines. We have another 1,000 lines here. One tool use. Three, one, two. (t: 60) And you can see here this is wave two with five agents in parallel and more are getting queued up right now. You can see it just finished wave two. How can just two prompts make Clawed Code run forever? (t: 70) You can see wave three is getting set up right now. Iteration 16 through 20. If we scroll down here, you can see a new set of iterations loaded up. (t: 80) Check out this task list, right? This is going to just keep running. Back to the question. How is this possible? This is enabled by an infinite agentic loop. (t: 94) This powerful pattern is fueled by just two prompts. (t: 100) It's fueled by the infinite prompt that we're going to get into in just a second. And of course, your spec, your plan, your PRD. (t: 110) So if we open this up a little bit, you can see here I have just three specs, but we're inventing some new UIs. I have three versions of them. Let's go ahead and kick off another. Infinite agent loop. Infinite agentic loop like this. (t: 120) And while it's dedicating work to multiple sub-agents for us, we can talk about how you can use this to generate a virtually unlimited set of solutions for a specific problem. (t: 130) I'll create a new terminal instance. Let's fire up Clawed Code here and let's update the model. I want to fire this off on Opus. Very clearly state-of-the-art model. (t: 140) And then we'll use the infinite custom slash command here. I'll type slash infinite. And you can see here we have the infinite agentic loop command. I'll hit tab here. (t: 150) And now we need to pass in a few variables into this. So the first parameter is the plan we want to fire off. I'm going to go ahead, copy this, get the path to this, paste it in here. (t: 160) You can see we're still running in the background, right? Agent 16 through 20 still running here. It takes a new directory. So you can see our first agent is operating in the source directory. (t: 170) Let's set this directory to source underscore infinite. And then lastly, it takes a count or the information dense keyword. Infinite. We're going to, of course, pass in infinite. (t: 180) So we're going to kick this off. And now we're going to have two agents running in parallel. And so we can see here our second infinite agentic loop is starting to fire off here. (t: 190) So if I close this and open up the second directory, you can see that got created here in our plan. You can see Clawed Code writing up this plan for infinite generation. (t: 200) We need to dive into the prompt. This is the most important thing. It's the pattern here. That's so valuable. Let's go ahead and dive in here and understand how this infinite. Agentic loop works with our two prompt system. (t: 210) And then let's talk about how this breaks down. If you've been using long running Clawed Code jobs, you already know exactly how this breaks. There's a natural limit here that we're starting to bump into over and over and over. (t: 220) And it completely breaks this infinite agentic loop workflow. Let's start with the infinite prompt to have our initial command. (t: 230) And then we have a really important part of this. The variables with Clawed Code custom slash commands. You can pass in arguments like this. And they'll be placed in position. (t: 240) Our first argument gets replaced with spec and then we get infinite source and then we get infinite. So this gets replaced and then we can use these variables throughout this prompt. (t: 250) And the Clawed 4 series is smart enough to know that it should replace the variables we placed in here with the actual variables passed in. Right. So you can see the spec file throughout this prompt and you can see the output directory as well. (t: 260) Then we have count, which is going to be one to n or, of course, infinite. You can see here in this first. Spec. Step of the infinite agentic loop prompt. (t: 270) We're reading the spec file. This is a really interesting pattern. We're treating prompts, right? Our specs as first class citizens that can be passed in to other prompts. (t: 280) Okay. This is a really powerful technique. There's a lot of value here. That's untapped. We explored this a little bit in our parallel agentic coding with get work trees video. (t: 290) We put out a couple of weeks ago. What we're doing here is a little different because we're running infinitely. And we're generating a single file. (t: 300) Although to be completely clear, you know, we could rewrite this prompt to generate any set of files. So we have argument parsing. Our agent is going to first read the spec file to understand what's going on. (t: 310) Then it's going to understand where it's going to output all these files. Then it's going to fire off parallel agents in groups of five. This is going to speed up the output of our agent. (t: 320) Our first round files have already been created for that infinite loop. And then this is really important. We're actually specifying what each. Subtract. Sub agent receives. (t: 330) Okay. So it's getting spec. It's getting the directory. It's getting its iteration number, right? You can see they all have their own iteration number and it's getting their uniqueness directive, right? We want these all to be unique. (t: 340) We want each example to grow on each other. This is really cool. So here we're actually passing in a prompt for our sub agents. So that's, what's getting written out here, right? (t: 350) This is a concise prompt for the sub agent. And then we have, you know, phase five. We're just kind of continuing down the line, infinite cycle. And then I have this. Line in here. I'm not 100% sure if this works. (t: 360) I don't know if Claude can see the end of its context window, but it seems to work. Okay. Evaluate context capacity remaining. If sufficient continued next wave, if approaching limits. (t: 370) Complete and finalize, right? So this is where this pattern completely breaks Claude code. You can't keep running this. It's going to hit the context window. Of course, we don't actually have infinite context windows. (t: 380) This will generate, you know, some 20, 30 files or sets, um, depending on your setup. All right. So then. We're going to. Just continue along the lines here. There are some details at the bottom here. (t: 390) Not all this matters. As you can see here, I am writing these prompts now with agents. We're entering this interesting zone where you want to be writing prompts that write the prompts for you. (t: 400) You can see here, you know, both of our lists here are continuing to expand. We now have 10 hybrid UIs inside of source infinite. Let's go ahead and actually look at what the heck is getting generated here. (t: 410) Right. You know, just to briefly describe the prompt that we're passing in. Right. So we have our spec file. That. We're passing in to our infinite agentic loop prompt. (t: 420) We're saying invent new UI V3. And what we're doing here is we're creating uniquely themed UI components that combines multiple existing UIs into one elegant solution. (t: 430) Okay. And that's, that's basically it. That's a key idea of what we're doing here. And I'm using UI as a example, just like with our parallel agentic coding video (t: 440) with get work trees, UI is just the simplest way to show off a powerful pattern like this. You know, we're specifying that naming scheme here with the iteration. And then we have a kind of rough HTML structure. (t: 450) That's all self-contained into a single file. So let's go ahead and open this up. Let's see what this looks like. Right? So if we open up a terminal here and we get the path to one of these files, we can say, uh, Chrome, and then open up one of these files. (t: 460) Check this out. Neural implant registry. Um, very interesting. This is a classified database access terminal. (t: 470) Very clearly. It's just a table, right? So this is kind of interesting. It's got a really cool, unique theme to it. Let's see what we can do here. So we can search. Nice echo. Cerebra max. (t: 480) Okay, great. So we can search across columns. We can sort. That looks great. Status filters, active risk level here. I'm constantly impressed with the caliber of code that the cloud four series is producing now. (t: 490) It's just kind of mind blowing that not only was it able to one chop this, it did five versions at the same time, right? (t: 500) You and I, we really have access to premium compute that we can now scale up infinitely. Uh, with this pattern, right? Very cool. (t: 510) You, why let's go onto a, another example, right? Adaptive flow, UI, liquid metal. So obviously some UI issues here, but this is just a simple UI. It looks like nothing special. Oh, interesting. (t: 520) That just adapted. Very interesting. I did not expect that. So it was actually creating a additional UI here based on what we type in, or like this kind of error state. (t: 530) Look at this. It's aired right here, right? This is not a true email address and we do get email auto-complete here. Very cool. And you can see, we also have a. Progress bar here at the bottom. In particular, I like this like active state. (t: 540) Let's go ahead and look at another UI that was generated for us. Again, this is all happening in parallel in the background. You know, this compute is solving this problem for us at scale, creating many, many versions, right? (t: 550) What do we have? Some 20, um, yeah, 50 versions now with two parallel infinite agenda coding agents. (t: 560) This is crazy, right? This is really cool. Very powerful. Obviously the real trick with this pattern is going to be to pointing it. At a problem where you need multiple potential solutions. (t: 570) Okay. This is the real trick with this pattern. You know, everything we do on the channel, you need to take it and you need to point it at a problem. There's a ton of value here that you can get out of this. (t: 580) Interesting, interesting to prompt infinite, a dentec loop pattern, right? We're starting to compose props. We already know that great planning is great prompting and you know, maybe that's a important thing to really highlight here, right? (t: 590) We're generating all these cool UIs. Um, you know, we can continue to just look at. Look, look at this. So interesting, right? We can look at UI after UI, right after UI and look at this one. (t: 600) So interesting, right? Look at all these just interesting creative UIs. There's, you know, a lot of likely garbage here, but there's a lot of value here as well, right? (t: 610) We're literally inventing new UIs as we go along and new UI patterns, right? We can just keep going. (t: 620) Check this one out. How cool is this? Okay. So, you know, this is the power of an infinite agentic loop. Multiplier. Multiple solutions. It's just going to keep going, keep firing. (t: 630) Uh, we're using a ton and ton and ton of compute here, right? You can see we're launching another wave of agents inside of this agent, right? One tool call 30 K 30 K 30 K two minutes each. (t: 640) These are shorter jobs. I've run jobs that are 30 minutes plus, and you can fire them all off in a subtask. (t: 650) It's so incredible. What we can do with cloud code and with the right pattern, right? The right prompting patterns. That lets us. Scale compute. Okay. So really interesting stuff there. What's important. (t: 660) What's the signal here, right? Couple of things to call out. Um, you can pass prompts into prompts. You can specify variables at the top of your files. (t: 670) You're likely going to want multiple variables that control what happens and what gets done. Okay. We have this infinite information, dense keyword. (t: 680) This triggers are a gender coding tool to run infinitely. Of course you need to phrase things. You need to be more specific. You need to be more specific with how that works. You can start with this prompt and modify it, build it, make it your own. (t: 690) Couple more key ideas. This is a classic one, right? Um, we have been using plans for over a year now on the channel and every principal AI coding member, you know, that great planning is great prompting. (t: 700) I sound like a broken record bringing this up for, you know, over half a year now, but there's a reason for it. (t: 710) Okay. We know that tools will change. We know that models will improve. You can't fixate on these things. Right? A cloud code is the very clear winner right now, but it won't always be that way. (t: 720) Okay. And we're going to get another model. All that stuff changes. What doesn't change is the principles of AI coding. Many of you know, this is why I built principal AI coding. (t: 730) Sorry for existing members and for engineers that have already taken this, but the repetition is probably important anyway. It's so, so important to realize that you want foundational skills that don't change with the next model, with the next tool, the plan, right? (t: 740) Great planning is great. Great prompting. This is principle four or five. This is so relevant. (t: 750) It's increasingly important. Okay. Why is that? It's because we can now scale or compute further. Right? But how we do that is always about communicating to our agents. (t: 760) Okay. Cloud code is the best top agent right now for engineering. Why is that? It's because it operates in the highest leverage environment for engineers, the terminal, anything you can do, clog code can do. (t: 770) And you know, part of me wants to say. It's better. You know, we'll debate that more on the channel as time goes on. It's definitely getting there. (t: 780) But you can see we're generating yet another batch of agents here. Okay. We have this ocean file Explorer. Very interesting. But anyways, refocusing here, right? The spec is super important because this details what we want done inside of this infinite agentic loop, right? (t: 790) So we have this really cool pattern where we're treating our prompts like you can treat functions and certain languages, right? (t: 800) You can, you can pass the function into a function. That's what we're doing here. Right? It's the same idea transferred to this domain of agentic coding and really prompt engineering. (t: 810) We're taking a prompt, passing it in to a prompt. You know, the magic is obviously in the pattern of this infinite agentic loop, but it's really in what you ask your spec to do, right? (t: 820) So what you ask your agent to do. There's a ton and ton of value in this pattern. I hope you can see how powerful this is. (t: 830) When do you want to use something like this? Look at all these UIs we have generating, right? We have two, two. Uh, agents going back to back here. Very, very cool. (t: 840) So when do you want to use something like this? You want to use a pattern like this. It's very similar again to our parallel agentic coding with get work trees there. We cloned our entire code base into the work tree directory so that multiple agents can work on their own directories. (t: 850) Again, link for that video is going to be in the description. I highly recommend you check that out. But what we're doing here is so fascinating. (t: 860) It's so powerful. We're scaling our compute. We're solving a specific problem with many variations of how we're doing it. We're scaling our code base with a lot of different versions of how it can be solved. So when do you want to use the infinite agentic loop? (t: 870) You want to use it when there are multiple potential solutions that you want to explore. You want to use it when you're working on a hard problem that you don't know the answer to, and you think that having many versions will help you get closer. (t: 880) And so this is all stuff you would encode in your lower level prompt that the infinite agentic loop prompt will execute on, right? (t: 890) And you want to use this when this is a really, really big idea. Uh, this is like a lead. Researchers are doing this. This is a really, really big idea. We're going to call up a self improving agentic workflow that is trying to achieve some verifiable outcome that increases over time. (t: 900) Okay. We've all heard about reinforcement learning. You can take that idea of reinforcement learning. (t: 910) You can take that idea of self verifiable domains and you can embed it in an infinite agentic loop prompt like this. This is a really, really big idea. (t: 920) More on this on the channel in the future. We don't have enough time to cover that here right now. Those are kind of the three big use cases for this that I can find right away. I'm sure if you dig into this, if you start using this, you'll find more use cases for this, right? (t: 930) So pretty incredible stuff, right? We have two agents running in cloud code. You can see I am hitting the limit. (t: 940) I'm breaking cloud code right now. Okay. We're running straight out of Opus credits. I am running in the cloud code max pro subscription, wherever the top tier is. I'm going to go ahead. I'm going to stop these agents. (t: 950) I need a few more credits for today to do some other engineering work. I'm going to stop. These here. You can see we're literally infinitely generating tons and tons of solutions to this problem, right? (t: 960) That's the trick here, right? That's the real value prop of the infinite agentic loop. You want multiple versions, multiple potential futures of an answer to a problem that you have. (t: 970) Okay. UI is obviously the simplest one. That's why I've showed it here a couple of times on the channel. Um, you know, we can just keep looking through these different user interfaces with different ideas and themes blended together. (t: 980) Check this one out. Very smooth. Very cool. Um, and this is all happening, you know, in the background with compute, we're scaling up doing this again. (t: 990) We're scaling up our compute even further beyond. That's what we do on the channel. Every single Monday, check out principal AI coding. As many of you know, I am actively working on the second phase course. (t: 1000) This is the foundation. I highly recommend you check this out. What comes next after AI coding is of course, agentic coding. I'll have more details on the next generation course. (t: 1010) As we move closer to the release date, looking at a Q3. Launch. So stay tuned for that. You know, this is a really powerful technique. Try this. Don't ignore this, please. (t: 1020) Uh, for your own good. Um, you know, it's completely free. A lot of the stuff I'm doing here obviously is all free for you guys. Link in the description to this code base. I'll save some of these generations. (t: 1030) So you can kind of really see and understand how this works, but it's really about the infinite prompt. Take all this stuff, make it your own, improve on it, solve your problem better than ever with compute. (t: 1040) Big theme on the channel to scale your impact. You scale your compute. Okay. Tune in, make sure you subscribe, like all that good stuff. Compute equals success. (t: 1050) Scalier compute. You win. You know where to find me every single Monday. Stay focused and keep building.

