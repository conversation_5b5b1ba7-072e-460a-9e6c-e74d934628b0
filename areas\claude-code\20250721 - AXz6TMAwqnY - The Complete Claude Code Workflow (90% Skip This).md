---
title: The Complete Claude Code Workflow (90% Skip This)
artist: "Yifan - Beyond the Hype"
date: 2025-07-21
url: https://www.youtube.com/watch?v=AXz6TMAwqnY
---

(t: 0) Cloud Code has become my go-to coding agent for the past few months. In this video, I'll show you all of the workflows and features that I've found incredibly effective over the past three months of intense usage. (t: 10) I'll also show you the custom tools I've built to make those workflows even more effective. Quick update before we dive in. (t: 20) I'm super excited to be launching Beyond the Hype newsletter, where you can receive all these pro tips and insights directly in your inbox, plus insider-only content and early access to my future projects. (t: 30) Subscribe at beyondthehype.dev. Now back to the video. The first part of the workflow, use tests wherever you can. (t: 40) Because when you write tests in your codebase and give <PERSON> the capability to run those tests, you give it a mechanism to automatically feed back to itself on its own runs. (t: 50) So when it implements new features, <PERSON> can just directly run those tests to verify everything is working before returning to you. Otherwise, the workflow most of us would go into in the prototyping phase is (t: 60) getting <PERSON> to return, you go to manually test, copy and paste the errors back into <PERSON>, (t: 70) <PERSON> goes into debugging mode, and runs repeat. That can waste quite a lot of time, and it's a lot of interrupt for the human working with code. But when you have tests, all of these workflows are automated. (t: 80) And this works especially well for larger, complex codebases, or even for the cloud-based codebases. But when you have tests, all of these workflows are automated. And this works especially well for larger, complex codebases, or even for the cloud-based codebases. But when you have tests, all of these workflows are automated. And this works especially well for larger, complex codebases, or even for the cloud-based codebases. For smaller codebases, but working on complex features. (t: 90) I know at this point, many of you might be thinking, well, I'm just writing my prototype. I don't want to spend so much time writing tests. Can I skip this? Yes, you can actually. (t: 100) Because for Cloud, just adding a phrase, whether in your Cloud or MD file, or just in your prompt to say, you know, testing this feature before returning, (t: 110) Cloud will try to at least either write test itself ad hoc on the run, or it will try to write temporary scripts to test the API or the UI as it implements itself. (t: 120) But just by having that one line in, will make Cloud try to iterate harder on the work it's outputting itself. (t: 130) One thing I found, if you don't give it any additional prompt, Cloud will only really try to test its own code about five out of 10 times. So just enforcing this behavior will give you really great results. (t: 140) If you have an RSS, look at how you can add test frameworks for your current setup. And trust me, you will not regret any minute you spend in it. (t: 150) Testing by itself probably already deserves its own video. Let me know in the comment below if you want me to dive into this in further detail. (t: 160) The second part of the workflow, being really mindful about Cloud Code's memory. So this is essentially the Cloud Code rules files that you can apply, (t: 170) that Cloud follows upon every single run. To make sure it has the right context about projects so that it knows your coding standards, it knows your workflows. (t: 180) Cloud actually gives us a super convenient way to initialize this Cloud.md file that sits at the root of the repository. So you can run the init command inside your directory, (t: 190) and you'll see it reading through all of the project context, the other agent rules files, where it knows how to learn from cursor and GitHub copilot. (t: 200) Read through the file directory and the readme, a lot of the key techniques, and then you can start to run it. So this is the Cloud Code rules files. Then analyzes all of that to generate this overarching Cloud.md file. (t: 210) Well, one thing that you'll see inside the Cloud.md file is the project overview about what the project does, key workflows and architecture, tech stack, folder structure, (t: 220) and a lot of the high level information about the project. Even some high level critical library information in there as well. (t: 230) As you iterate through the project, there will be points where you'll see that there are a lot of things that you can do. There are a lot of things that you can do. There are a lot of things that you can do. There will be points where you need to update this context. So you can just run slash init command again to get those things updated without having to do this manually. (t: 240) One thing to keep in mind for working with the Cloud.md file is you want to make sure that you don't overload it with every single fine detail in your project. (t: 250) Because Cloud Code is actually pretty decent at discovering information on the run, much like how it's doing the init command Cloud.md file generation in the first place. (t: 260) It knows how to explore the code base to find the information it needs. Cloud.md file is really the high level pointers that you give Cloud so that it knows where to find the right information. (t: 270) You can already see that inside my current init run, because I already had a Cloud.md file directly inside the repository, (t: 280) it's discovered some updates I've made to the project and also now suggesting updates. And I'll just click on shift tab to auto accept these edits for now. (t: 290) The other principle to keep in mind when working with Cloud is that it's not just about the code base. The other principle to keep in mind when working with Cloud.md file is to make sure that you only add things into this file when these are the rules that should really be followed with every single one of your Cloud prompt runs. (t: 300) Most of the times, because Cloud Code is already great actually writing decent code and following the best practices, (t: 310) if you see Cloud already doing the right thing most of the time, then there's no point enforcing it additionally with additional rules. (t: 320) Cloud also gives us a credit card. It's a relatively easy way to add new memory. So we can just do hash anywhere inside the input box and during the prompt. (t: 330) And we can say that, and let's just use the example, right? Always use descriptive var names. And we'll choose to save it into the project memory. (t: 340) So the ones that we have committed and it will add this in in the background. And now if we open up the Cloud.md file, you'll see that it's added this new rule directly into the memory file. (t: 350) And even added a new heading specifically for that. So that's a great thing about this hash command. It will know to take the new memory rule that you want to add in and insert it into the right place inside the memory. (t: 360) And at times, if this is actually relevant and applies to multiple rules within your Cloud.md file, (t: 370) it will insert that into multiple locations and it will take the semantic meaning correctly, making it super easy for you to add these things ad hoc. (t: 380) There will be times when you want to add these things ad hoc. You want to give very specific commands to Cloud where you have a lot of additional context for a specific subset of tasks. (t: 390) You can use the commands capability inside of Cloud Code. You can add this into the commands folder in the .cloud repository. Let's create the directory and add a code review command specifically here. (t: 400) So in this file, you also have two directives that you can use. You can use the $arguments (t: 410) to allow arguments to be passed in directly into this command when you execute it in Cloud Code. You can also use the exclamation mark and then quoting it with the bash commands (t: 420) that you want to run to include the output of those bash commands directly into this thing. So here, let's write the file instead of Cloud. (t: 430) Let's restart it. And you'll see that we have now the slash code review command directly here. And we can click on that and it will go into the review mode. (t: 440) To execute the current working set. This is incredibly convenient if you have a specific set of workflows that you want (t: 450) to hard code that requires additional context, but without having to pollute the root cloud.MD file. Next key part of the workflow, context management, (t: 460) even though all of the latest Cloud models have 200,000 context windows, one thing I'm sure you've noticed is that as your conversation length with the Cloud grows, (t: 470) its response just degrades. over time, you'll want to be really mindful of what's currently in the cloud code's context (t: 480) while you're executing. And if we go back to the workflow that we had already started, whenever you finish a task, the first thing you want to do is clear the existing context. Because (t: 490) most of the time, given how great cloud is at discovering information from the repository, and the fact that you've already got much of the overview directly in your cloud.md file, (t: 500) then you can easily start from fresh. I'd say slash clear would already be sufficient for 90% of the use cases. But maybe you've just had a long conversation during a debugging session, (t: 510) and you want to carry some of the information that you found and the decision you found directly into your next feature implementation. You also have the compact command, which cloud (t: 520) also runs automatically whenever its context window is fully filled up, or you can manually (t: 530) trigger it before you go into the next piece of work to give you a cleaner context. And the cool thing is, as you can see in the instruction, you can provide additional instructions for it to focus (t: 540) on during this summarization. So cloud will take out all of the key decision made and code changes (t: 550) and put that into a much more compact format without retaining the entire message history, and then start a new conversation based on just the summary. This is great when you're (t: 560) working on a new project, and you're working on a new project, and you're working on a new project, and you're working on a new workflow. But what if during your current flow, there's things that has gone wrong, and you need to revert back to a previous point of the conversation? Perhaps you've (t: 570) noticed you've misled cloud down a wrong path. You can press the double escape key at any point during your command execution, and then select a message to actually go back into. Let's say we go (t: 580) back to the place before it did the code review here, and we get here, right? So this is really convenient for you to just clean up the context, and then you can go back to the previous point (t: 590) of the conversation. So you can see that cloud actually gives us quite a few tools in our pocket to control context. I think the cloud devs really know that this is something that's really key. (t: 600) But there is one caveat though. Unlike windsurf and cursor, when you navigate back in the message (t: 610) history, it doesn't actually revert your code base back to the point at that message. So you have to do version control yourself manually. I really hope that the cloud team adds this in sometime soon. (t: 620) The next part of the workflow, permission management during tool executions. So by default, you'll see that when you start up a cloud code command, it will start in the default mode, (t: 630) which is pretty much read only. So this thing is actually great for doing code base explorations, and we just need to find out things within the code base. Because if not, the default workflow (t: 640) that you should really go to is the auto accept edits mode, which you can cycle through these modes by shift tab. It also has the planarization, which is the planarization of the code base. (t: 650) The planarization mode, which forces a plan then act workflow so that it basically starts cloud code in a read only mode, generates the entire plan for you to review and awaits for your approval for the (t: 660) plan before it goes into the auto edits execution mode. And if we exit the command here, there's also (t: 670) another very well hidden command called dangerously skip permissions. This can only be enabled when you start up cloud at the command line. (t: 680) So if you're using a cloud command, you can see that on the bottom right hand corner, it will say bypassing permissions. In this workflow, cloud will run all commands, whatever the bash commands (t: 690) there, and without any confirmation from the user. This workflow is great if you're working in CI, or if you have got a great dev container set up. But if you're using this on your main dev machine, (t: 700) I'd recommend not to in most of the cases, just in case something goes wrong. (t: 710) One thing I would also suggest is that if you're using a cloud command, you should not use a cloud command. The best you do is changing the default mode that cloud starts up on. Because the one I almost always use is the auto accept edits mode. So you can go to cloud and then go into the local (t: 720) settings here. Inside the permissions block, we can add in the default mode. And we can say accept (t: 730) edits here. Once we save, let's actually restart. You'll see that it will always have the auto accept edits on, which is super convenient. With the accept edits mode, it's not going to be a lot of (t: 740) work. It approves most of just the plain file edits. But when it comes to running any bash commands, even LSS directly inside your repository, it will ask for your approval, which can be quite annoying. (t: 750) I've had a lot of times where I've started cloud code on a task, which I thought it would be able (t: 760) to complete by itself, only to return 10 minutes later, realizing that it got stuck on one command approval at the very beginning of the execution run, which is fairly annoying. Because I want (t: 770) something between the two commands, and I want something between the two commands. So I'm going to actually create a new tool called Cloud Code Boost that uses LLMs and actually uses the cloud (t: 780) models to look at the command at every single tool call, decide whether it should auto approve it, (t: 790) given that it's standard development workflow, and then injecting that directly as a hook inside the repo does all of the auto approval, but without the worry that it would execute some (t: 800) dangerous command without asking for your additional approval. Next part of the workflow, version control. As I always say, when you're working with coding agents, it's great to use (t: 810) version control to always keep your current repository in a working state. As previously mentioned, though, cloud doesn't have a built in way to do checkpointing. So you have to do (t: 820) a lot of this work manually. And the workflow I generally go into is after every round of prompting with cloud code, I'm going to do a lot of this work manually. And the workflow I generally go into is after every round of prompting with cloud code, (t: 830) After I see it's got a working solution, I add the current file into the gate staging, which you can do with a git add dot. And if you want to be more selective, you can choose the individual files that you want to add (t: 840) specifically. Then once you get to a working state, cloud code actually gives us a credibly easy way for making the final (t: 850) commit. So here, we can literally just say commit, and it will look through all of the changes is currently implemented. And if you (t: 860) already had a conversation history directly with cloud, it will also use that as context to only commit the things it's worked on, and ignore the rest of the changes, it will also look at the get history to see how commit messages has been formatted in the past. And then look at (t: 870) the current changes, then create the commit directly. And because I've already got my tool to auto approve a lot of the git (t: 880) commands, which is why you didn't see it prompting me for the confirmation during the git commits. (t: 890) And here, it correctly summarized everything with all of the feature additions. And every single time, it does always inject in a generated with Cloud Code, (t: 900) which I'm fine with, given the great work is actually done. I would highly recommend adding a pre-commit or checks directly into your repo to give Cloud even more automated feedback. (t: 910) I use a tool called Husky to add in these kind of pre-commit checks. The three things I always have in there, the compile checks, the lints, (t: 920) and quick unit tests. So every time Cloud goes to commit the work, those pre-commit checks will run automatically, and Cloud will be able to directly act on the errors (t: 930) and then correct, fix all of the issues before it commits the code. So these kind of hard-coded workflows really enforces the high quality of code (t: 940) Cloud commits every single time. Something really simple to add in, but just make sure that you never go into a broken repo because every single one of your... (t: 950) pre-commit checks ensures that the repo is in a working state. The last part of the workflow, async work together with Cloud. Cloud Code provides a very convenient way to install the GitHub plugin directly here, (t: 960) which is what we'll use for this async workflow. And during this process, it will confirm with your repo, (t: 970) install the plugin and add in the auth tokens, and also add in the GitHub workflow files to automatically execute Cloud Code and also code reviews. (t: 980) As part of all changes, you can see that we've gone two new workflows here, and one which is a review that will trigger every single time a pull request is opened. (t: 990) The thing that I would really suggest is inside the prompt here, customize this based on your need for code review, because the default code review provided by Cloud Code can be quite verbose. (t: 1000) And generally, I only have identify potential bugs and also security concerns, then ignore all else. (t: 1010) And the next one is the Cloud.yaml, which allows you to add mentioned Cloud in any GitHub comments, whether in issues or inside reviews. Cloud Code will then take the comments as context, (t: 1020) then directly make the changes and then update the branch accordingly without having to set up any local environments. (t: 1030) This workflow is especially powerful if you've also got automated test setup, because then you can have Cloud iterating on new features, working on new things, (t: 1040) directly in the Cloud and only returning to you in the code review with updated and relevant code, so that you can directly just review the code and merge when everything actually checks out. (t: 1050) This is just an example of a Cloud Code reviewed pull request. You can see that the review is extremely comprehensive with a lot of actionable comments directly in there. (t: 1060) And obviously this is before I'd optimize it, just focus specifically on security issues and bugs. But you can see how powerful it is. (t: 1070) And this is just a quick overview of what the workflow can be. And here is another usage where inside my GitHub issues, I've asked it to make a specific change inside of my repo. (t: 1080) When I mentioned add Cloud here, it will go into the execution mode and show you all of the to-do updates, much like it does for you on the CLI, (t: 1090) and then shows you all of the work it does, the key changes made and the next steps for you to actually follow up on. Once the work is complete, it will give you a link to directly create the program, (t: 1100) pull requests, which is incredibly convenient. Hopefully here, I've given you another reason to write great tests inside of a repo to give you an even more powerful workflows like this. (t: 1110) Now that we've done with the workflows, here's a quick fire list of very useful utility features I've found over the past three months of usage. As previously mentioned, (t: 1120) you can use hash to directly add memories conveniently. You can use also the exclamation mark for running bash commands for Cloud to directly add in the output of the command. (t: 1130) And also during the Cloud thinking process, you can use keywords like think or think hard, (t: 1140) think harder, also ultra think to trigger different levels of thinking budgets for Cloud's models. (t: 1150) And I would say use this sparingly because it uses up your quota really quickly. And I've really had to use these things to solve problems directly in my code base. Anyway, (t: 1160) you've noticed Cloud falling down a trap and it's just not able to come up with the right solution. Try these keywords in turn inside your prompt to get it to think harder and harder to try to resolve the problem. (t: 1170) And if on the max plan, sure, you can always apply the ultra think every single time to get it to solve the hardest problem. (t: 1180) But the general recommendation is to go through these step by step. As we have also typed here, because it's a command line tool, you would think that it doesn't have a lot of the convenience features. (t: 1190) To undo, they recently just added the shortcut for control plus dash to allow you to undo types directly inside your terminal. (t: 1200) And this is incredibly useful. If after a long command that you realize you've accidentally just deleted the whole line, then you can easily bring your content back with control plus minus. (t: 1210) Many people say that Claude didn't have an easy way to deal with images. In fact, he did actually from day one. (t: 1220) Let's take a screenshot of Claude code itself, and then we can press control V to paste the image in. You can ask it, what is this? (t: 1230) And you'll see that Claude is able to use its vision capability to analyze the image. It works amazingly great if you're working with UI, (t: 1240) so you can just pasting errors without having to deal with the copy and pasting so that Claude can even reason about element positions and things like that during a session. It's debugging process. (t: 1250) Additionally, if you're working with larger repos where you have a multi repo set up, or maybe you're just working with open source repo where you need reference to, you can also use the command for add there to specify additional directories that your current Claude code instance should also have access to. (t: 1260) Once you do this, (t: 1270) it will be able to read through it and also make changes directly when you do this. And now if we just exit Claude a bit, there's a few cool things. Also on the outside, (t: 1280) it will create something new. There's the non interactive mode for Claude. You can use this to get Claude to execute on the command, (t: 1290) but not print out any of the thinking process and only give you the final output. And the simple one, you know, like Claude-p, what is the date? (t: 1300) It will run obviously a bit slower than the traditional command, then only output the final results. Command is also incredibly useful. If you just run it, you just want to run something quick, (t: 1310) but don't really care about the thinking process. You can say, what does this project do? We can wait for Claude to actually generate this response and you can see that it goes through the summary and just get the output here. (t: 1320) There will be times where you might want to return to a point inside your Claude conversations. (t: 1330) So here you can use Claude dash dash resume to go back to a specific conversation. That you've had. (t: 1340) I think I really hope that they create a better summary rather than just using the first message, but it's okay. And then now, Oh, actually it's added the branch. It was on while it was working on this feature. (t: 1350) So making it quite easy for you to go back in history to find out what's going on there. And if you are just continue with the last conversation, (t: 1360) you can use Claude dash dash continue to run those command very easily. And now we're going back to the one that we just run. Just run non-interactively and you can see the conversation. (t: 1370) It did. If you ever need to debug the non-interactive mode, well, this is how you do it. Well, there you have it. There's a ton more things that you should go back to try with your Claude coach workflow. (t: 1380) As mentioned earlier in the video, if you want these insights and pro tips directly in your inbox, be sure to subscribe to beyond the hype.dev newsletter. (t: 1390) And until then, happy shipping. And I'll see you in the next one. (t: 1395) Bye.

