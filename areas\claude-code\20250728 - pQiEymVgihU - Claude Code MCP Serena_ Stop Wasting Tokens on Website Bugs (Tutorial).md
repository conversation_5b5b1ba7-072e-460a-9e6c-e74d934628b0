---
title: "Claude Code MCP Serena: Stop Wasting Tokens on Website Bugs (Tutorial)"
artist: AI Business Lab
date: 2025-07-28
url: https://www.youtube.com/watch?v=pQiEymVgihU
---

(t: 0) Hey guys, what's up? It's your boy, it's <PERSON> with AI Business School and Zio Advertising. Thank you for coming and hanging out with me. Happy Monday. I love Mondays. If it's not Monday, you're probably close to a Monday. (t: 10) In which case, it's a great day to make some money. Now, if you're like me and you've been getting stuck on cloud code, (t: 20) I'm here to share with you a pretty cool shortcut I found. It saves a ton of tokens. No matter which version of cloud you're on, you're going to start saving some real money. (t: 30) And it's way better at context awareness. And it got me through a problem that had me locked down for about two or three weeks. And I'm so grateful that I found this. (t: 40) I found it from another channel, which is... I'll share the other channel I got it from a little later, AI Labs. (t: 50) Really cool guy. But yeah, I want to show you a little bit about what it is. And in the description, I'm going to add a link. Which has all of the prompts that you need to get this started (t: 60) the first time in a project and every time you start a project. That way, you can just jump on and have it sorted out. (t: 70) Now, what this does that's a little different is, the first time you launch this into a project, it goes through and it kind of creates a glossary of every single thing that happens in the project, (t: 80) like all the different bits and bobs and pieces and how they all sort together. And then once it has that, every time you open the green box, you're gonna see something. And I'm not going to jump off the page because I think it's really cool. But it's really cool and it's really fun. the project and you start this, instead of going through the project and track every single, (t: 90) the projects, every single line and trying to find that like one thing it needs to fix, this creates a glossary. So it goes kind of through the glossary and then it uses that (t: 100) glossary to find exactly what it's looking for. So what would happen as an example is I would try (t: 110) to change like a button or I'd try to build a website and the color was off. Like it would be white on white in the background and I needed it to be, you know, readable black letters on white (t: 120) background or blue letters on a red background or whatever yellow on white or yellow on black or whatever color. So it was like white on white. I couldn't read it. And no matter how many times I (t: 130) was like, Hey, this button's not working. Here's the page. Here's the link text. Here's the button. Can you go and fix it? It would fix that. And then three or four other things would just be wrong. (t: 140) And I was like, this is, this is not working for me. And it took me about three weeks. I found this it's called, (t: 150) it's an MCP called Serena. It's not really an MCP. It's kind of more of a tool that you load into the project. And I have it in most of my projects, but what I'm going to do is I'm going to show you a project that I don't have it linked into. And then from there, I'm going to link it (t: 160) into that project and I'm going to show you what it does. And then I'm going to show you how to do (t: 170) it. So I have a website right now. I'm going to show you how to do it. And then I'm going to show you how to do it. And then I'm going to show you how to do it. And then I'm going to show you how to do it. And then I'm going to show you how to do it. And then I'm going to show you how to do it. Now I'm doing all of my testing on for this stuff, the SEO, the website building, it's called H2 echelon and it's a hydrogen thing. So what I have is I have it stored on my local. (t: 190) I have it stored on my local desktop. And so what you do is you just come into here, you go CD and this takes it to my desktop. And then the file name, I'll go CD again, (t: 200) and I'll go H2 echelon desk, July 23rd. And now you can, oh, that didn't work. Hold on. (t: 210) dashed July 23rd. H2 dashelon. I must have spelled something wrong. Oh yeah. I missed the Y. So you (t: 220) have to be very, see I missed the Y in July and it said it didn't work. CD H2 echelon dash July 23rd. (t: 230) CD H2 echelon dash July 23rd. Okay, now we're in. Now we're in. So I'm going to go ID and I'm going to try that again. And. And I'll go H2 echelon desk, July 23rd. And now you can, oh, it didn't work. Hold in okay and now here's the prompts again these this will all be loaded in the bottom so the (t: 240) first time you open a project you have to add serena to the project so i'm just going to copy this here and you just paste this in it says claude mcp add serena uvx from github there's (t: 250) a link context ide assistant project and now it's going to go in and it's going to add this it already exists so it's not doing it because it's already done at once (t: 260) that's fine now i'm going to go claude dangerously skip permissions (t: 271) cool we're in now the first time you load serena you give it the instructions so i'm going to go (t: 280) mcp initial instructions and click enter (t: 286) and it's going to craft (t: 290) right and you can see here that it's going through and it is checking onboarding is (t: 300) performed if it's been performed on the project onboarding was already performed so i'm doing this now to show you kind of what it would be like the first time you onboard it but once it's (t: 310) onboarded it's there you don't have to do this again you're you're chilling um so now it's saying how can i help you today i'm just going to load in the project i'm just going to load this in again just for the sake of having it (t: 324) so you can see it's analyzing the root structure examine the json packages exploring source code (t: 330) identify comprehensive memory files and then identify configuration files and build setup now when it does this there's going to be another page that opens (t: 340) there's going to be another page that opens on your desktop i'm going to show you that now okay so this is the serina logs that it will create for you and this is kind of this is (t: 350) what it shows so you can shut down the server when you want to you don't have to i've left (t: 360) mine open but you'll see here the 12700.1 that just means that it's using it's using the tool (t: 370) locally on your computer so you don't it's not on the internet it's on your computer so it has a project on your computer now it's brought this tool in and once it (t: 380) understands a tool it's using that tool against what's already on your computer now now this is cooking so I'm gonna see if I can kind of put these side by side (t: 390) and then you can see what it's like to have these each of these kind of set up so now what you see here is you see Serena which has kind of the entire (t: 400) thing logged and then over here you have the identify configuration files build like it's so it's kind of chatted with us it's done that it's created memory (t: 410) files of the project overview codebase crusher so now it understands things at a much deeper level so if we want to mess around with this we can just open (t: 420) up the website we can have a look at what we're working on okay and we're just gonna say like look this section here is a little not working so I'm (t: 430) gonna grab this section I'm gonna click inspect and this is this is the section because I (t: 440) inspected this specific section so we'll just go down here where is it if we know it's this so inspect this is the section I'm gonna go copy (t: 450) element I'm gonna say this element on the home page is not working (t: 460) automatically for our image folder so this version of the home page is not gonna need it but once they're out of the way so we can kind of got to know (t: 470) things we can sort of go about moving this back so let's say that we wanna pass this on to the center page because we have the link button in the download menu and we don't know what the system here at declutter should do so (t: 480) let's tap ok and now it shows that we have a common page so what you'll want to do is just go ahead and open it up in terms of your Google formatreiben so you (t: 490) I'll show you something else while we're doing that because why not multitask? I'm kind of giving you the idea of how Serena works. (t: 500) Now I do want to do ht-nosh-on. So it hasn't been performing super well. I wanna see if there's a reason for this. (t: 510) So I'm gonna bring this to the side and we'll do a little bit more like SEO stuff with this as well. Let's have a look at pages, 41 not indexed, right? (t: 520) Seven reasons. Let's find out. So here's an error, validation failed. (t: 530) These are a bunch of pages with h2o errors. (t: 540) I'm gonna skip this for now. We're gonna do this on the next video just so that way we can have that be a thing that's done. So, okay, so now this is a new page. (t: 550) This is where we're starting. So now you can see it successfully replaced the broken image. (t: 560) So let's have a look. It won't be fixed right away, but it will be fixed. And you can see now this is hard to read. (t: 570) So what we're gonna do is I'm gonna tell it, okay. (t: 575) Okay. (t: 580) I found another issue that I also need to fix. I want to fix this on a different page here. So I'm gonna download it under Winit first and then (t: 590) I'm gonna go out and do it on the level tab, so I can have it indexed to a different page, so you can just copy in the same thing, as long as you don't invested in this page (t: 600) first after you've infected Google and then desde el modem de arri sécurité And from here, home page page, Hey going to Нет. So if you want to have the same going on again, (t: 610) I'm gonna use cup. You just have to click here. Be sure to didで esto. read here is the code for the section how can we make (t: 621) this easier so i'm saying here's a section um okay i found another issue that i want to fix before we (t: 630) push this to the git here's a section of the home page that has white writing on a light pink (t: 640) background as you can see here no bueno okay so load that in i'm going to paste this section i'll say and then i'm going to say here's the code how can we make this easier to read (t: 650) and again i'm going to come in here and right click inspect and i'm going to grab the actual section here that kind of has all of them figured out like you can see here when i (t: 660) hover it covers all three of those sections and then but these are also the sections that need to be fixed (t: 670) right so i'm going to grab these sections i'm going to copy element and i'm going to paste those in there as well and i'm going to paste that whole thing now we're going to let (t: 680) it do its thing and we're going to see what it comes up with when it's done (t: 690) okay so i do believe this is fixed to confirm i'm going to say can you launch this locally so i can check (t: 700) if the errors have (t: 702) being corrected (t: 706) now this is like just classic problem solving (t: 710) um i'm just asking it to launch it locally so i can see what the issues are and if it's working better now (t: 720) now in the comments i'll link the mcp and i will link a free document that anyone can sign on to and just you can get these (t: 730) so that way whenever you open up a folder in something new you'll have it readily available right there let's see if this works (t: 740) yeah it's here some of the things are kind of buggy i don't think that's fixed but it's not broken and this is better to see okay so it's kind of (t: 750) working it's kind of not working it's kind of working but it has kind of done what i wanted it too. Okay. So if you have made it this far, thank you so much for hanging out with me. (t: 760) The ending of the last of what I originally recorded this video is a little botched. Anyways, at the bottom, there will be a Google drive link where you can apply Serena for the (t: 770) first time. And then every subsequent time that you log into any project, which is saved locally, (t: 780) the instructions are there, they're for free, the links in the description. As you can see, we were able to come through and make these changes and it even added an image (t: 790) there. So yeah, that was a little bit of like how to install Serena, what it's good for and a little use case scenario. If you made it to the end, thank you so much for spending a little bit of (t: 800) time with me. Hopefully this was helpful. If it was, hit the like, hit a subscribe. It goes so (t: 810) far whenever you guys even click on it. So thank you so much for hanging out with me. Comment on any of my stuff. So I super appreciate it. It keeps the channel going. And yeah, (t: 820) thank you so much for spending this time with me. I'm super grateful that you're here. And I hope that this helps you make a little bit more money a little faster over a long period of time. Cheers. I'll see you later.

