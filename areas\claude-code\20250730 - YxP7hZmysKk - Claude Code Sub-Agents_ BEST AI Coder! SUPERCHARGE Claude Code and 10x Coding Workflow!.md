---
title: "Claude Code Sub-Agents: BEST AI Coder! SUPERCHARGE Claude Code and 10x Coding Workflow!"
artist: WorldofAI
date: 2025-07-30
url: https://www.youtube.com/watch?v=YxP7hZmysKk
---

(t: 0) Cloud Code just got a game-changing upgrade with the introduction of Subagents. Recently there have been pretty massive updates coming to Cloud Code that's flying under (t: 10) the radar and it has the potential to revolutionize your development workflow. I'm talking about new features called Subagent and it could practically 10x your coding efficiency (t: 20) and effectiveness. But what are Subagents? Well about a week ago the team at Anthropic rolled out with a powerful new feature inside Cloud Code. (t: 30) Subagents which is something that lets you spin up specialized multi-agent setups within your terminal. Agents that are tailored for specific tasks with their own system prompts, toolsets, and (t: 40) even dedicated context window. This is a big deal for reducing hallucination, improving context management, and enabling more intelligent task delegation. (t: 50) Think of it like deploying a team of expert agents, each one handling different parts of your development workflow. One could manage Git repositories, or another could focus on debugging, whereas the other (t: 60) one could specialize in documentation or testing. And for those who are new to Cloud Code, it is Anthropic's powerful CLI tool designed (t: 70) to work inside your terminal. It understands your codebase, helps you write and refract your code, automates routine tasks, (t: 80) explains complex logic, and even handles Git workflows, all through simple natural language prompts. And now with Subagents? It becomes a general practice. It's not just a simple task, it's a whole new level. (t: 90) Acting more like a project manager that assigns the right specialists to each part of your codebase. In short, it is something that has a specific purpose or expertise area, it uses its own (t: 100) context window separate from the main conversation, can be configured with specific tools it's allowed to use, and includes a custom system prompt that guides its behavior. (t: 110) This overall is going to preserve the context when you're dealing with multiple tasks, it's going to be specialized with different expertise, where it could be fine-tuned to work on a (t: 120) specific task rather than having the general AI agent focus on everything. You have the ability to reuse this, where after you create a subagent, it could be used (t: 130) across different projects as well as different tasks that's shared across your team. And you also have flexible permissions with all of these different agents. (t: 140) Now obviously to get started, you're going to need to make sure you have the prerequisites, which is to make sure that you have Node.js 18 and above. Once you have that installed, you can install Cloud Code for whatever operating system you (t: 150) have. Since I'm on Windows, I have it installed using WSL, which is the Windows subsystem for Linux. After you have installed Cloud Code, what you can do is simply open it up using the (t: 160) Cloud command to start up the terminal agent. And this is where you're going to need to set your workbench and where you want the (t: 170) files to be saved. And then you want to also log in so that you can then have your API connected to Cloud Code. Essentially, once you have connected your API and set your path, what you want to do (t: 180) is click on typing slash agents within the search tab over here. And this is where you're going to be able to manage the agent configurations for different (t: 190) subagents. You can create a new agent and you can choose whether if you want to create a project level or a user level subagent. You can then define the subagent, which is recommended that you generate with Cloud first, (t: 200) then customize it to make it yours. Describe the subagent in detail. Name, domain, and password. Then add the new agent and the subagent. And that's it. Next step is to create a new agent. Then you need to create a new agent. And you can create a new agent. should be used select the tools that you wanted to have like mcps as well as different plugins (t: 210) as well as the interface that shows all the available tools making selection easy now if you want there is a cloud code agent toolkit and essentially you can discover different sorts of (t: 220) agents all of these prompts that help you create these different sorts of sub-agents within cloud code so if you want a ux optimizer you can click on this copy the prompt and then you can provide (t: 230) it to the cloud code sub agent so after i click on create new agent i can then provide the actual (t: 240) location of where i want it to be operated if it's a project one i'll keep it within the project wide directory or if it's a personal one i'll keep it within personal now what you can do is you can (t: 250) either generate it with cloud or you can have a manual manual configuration this is where you can define the agent type so in this case it's a ux optimizer so i can paste this in so it actually (t: 260) needed to be created in the project and then i can add it to the project and then i can add it to the project and then i can add it to the project and then i can add it to the project and then i can add it to have a dash in between the lines so i added that and now i can click enter and essentially now i (t: 270) can add in the agent prompt that i've gotten from this marketplace and paste it in and then we're going to have our new description for the sub-agent which is going to be the ux optimizer (t: 280) another step is where you have the ability to select the tools that you can add to this agent so there's a lot of different tools that you can add based off of the mcps (t: 290) that you have connected to cloud code in this case i don't have a lot of tools connected but if i did i would be able to select the ones that i want to add by clicking enter and then you can also have it so that it has access to all the tools and then you can click on continue you can (t: 300) also provide the automatic color for the background so if that's something like yellow purple orange (t: 310) or something like pink you can then provide that and click enter and then now you have your sub agent created within cloud code before we get started i just want to mention that you should (t: 320) definitely go ahead and subscribe to the blog if you haven't already and i'll see you in the next video (t: 350) we have a signup workflow that i've actually just created with cloud code. And you can see that there's a lot of different fields that I have to enter. Obviously, if you're working on having someone onboarded for something, (t: 360) you'd want to have it focus on collecting as much information as possible. But it's not intuitive. (t: 370) There is a lot of additional information that we need to provide. And this is where I want to redesign this so that it reduces two to three steps while improving the clarity and minimizing (t: 380) cognitive load. And that's where I want to actually focus on using the subagent that we've created to help us fix all this. So now here's where the subagent can actually come into place. (t: 390) We had generated that UX for that signup page. But now what we can do is use a subagent from (t: 400) cloud code to work on having it so that it could design a modern intuitive user interface that is able to make it easier to work with. So that user flow is super easy. (t: 410) And it only takes approximately two to three easy steps. So now we can use the subagent to focus on this particular task, whereas we can deploy another subagent, which could maybe even work (t: 420) on UI itself, as well as another subagent that can work on something else. Right now, you can see that the pink tag has been shown, which is going to design the modern signup UX flow, which is going (t: 430) to be better than what we had saw previously. And while I have the UX optimizer agent working, (t: 440) I want to actually have another UI agent working alongside that other UX agent to work on creating a better unified, I would say, interface for the signup flow, whereas the UX optimizer can work on (t: 450) making the confusing flow a bit less confusing, reducing the steps. And we can see that with both (t: 460) of these two agents combined, we're going to have a better app deployed. And there we go. Just like that, cloud code with its two new subagents that I created. So now we can see that the subagent can (t: 470) create a new unified signup workflow, which looks a lot better and it's easier to work with. It's not so demanding like the last one that we saw, where you saw that there's so many different (t: 480) field names, which is just unnecessary. And overall, you can just see that this is a more simplified way for people to easily sign up with your workflow. But that is essentially just a (t: 490) example of what you can do with subagents, having two different agents working together (t: 500) on developing different signups. So you can see that the subagent can create a more unified workflow, which is just a little bit more complicated. And you can see that the subagent can create a more unified workflow, which is just simply just deploying different agents with different use cases. Just take a look at another (t: 510) subagent in action. This is where someone had created a delegator subagent, which researches tasks, writes clear descriptions, and dispatches background agents to handle them. This multi-agent (t: 520) setup is going to transform your workflow by breaking down complex projects into specialized tasks. It's also able to run in parallel, which is drastically boosting efficiency. So you can see (t: 530) that the subagent is able to run a lot of tasks in a single go. So you can see that the subagent is able to run a lot of tasks in a single go. So you can see that the subagent is able to run a lot of tasks in a single go. It's like having an AI project manager inside your terminal, making coding faster and smarter. (t: 540) If you liked this video and would love to support the channel, you can consider donating to my channel through the super thanks option below. Or you can consider joining our private Discord, where you can (t: 550) access multiple subscriptions to different AI tools for free on a monthly basis, plus daily AI news and exclusive content, plus a lot more. But that's basically it guys for today's video. If you want to see more videos like this, please subscribe to our channel, and you can also click on the bell to get notified when we upload a new video. (t: 560) Thanks for watching! I hope you enjoyed this video on the new subagents that Cloud Code has provided. I highly recommend that you take a look at this because it's definitely going to greatly improve your efficiency when dealing with Cloud Code. (t: 570) It is now becoming a really cool, new, and gen-sync AI terminal code-based tool. And it's something that's going to deeply elevate your coding workflow. (t: 580) So I highly recommend that you take a look at this, the links are in the description below. But with that thought guys, thank you guys so much for watching. Have an amazing day. Make sure you subscribe to the second channel, (t: 590) join our newsletter, as well as our private discord follow me on twitter and subscribe to the youtube channel so that you can stay up to date with the daily ai news but with that thought guys have an amazing day spread positivity and i'll see you guys fairly shortly peace out fellas

