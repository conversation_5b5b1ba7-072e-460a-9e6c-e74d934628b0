---
title: I Launched a SaaS in 60 Days With <PERSON>  (here’s how)
artist: Income stream surfers
date: 2025-08-29
url: https://www.youtube.com/watch?v=aXJN4k17NhM
---

(t: 0) Welcome to this video where I'm going to be teaching you guys absolutely everything I've learned from launching a SaaS in less than two months. (t: 10) This is everything, all of my secrets, all of my tips, all of my tricks that I've learned from building SEO Grove. This is already doing, you know, 5,000 a month, maybe even more depending on how many people roll over from the 80% discount to the, you know, 0% discount in two months time. (t: 30) I mean, it could be doing 15 if we don't lose too many people in the transfer. So let's talk about absolutely everything I have done. (t: 40) Okay, so this is a little document that I've created with all of my tips, all of my tricks. I may add to this as well. And I'll put this in the description of the video as well. (t: 50) So the first thing is use Claude code Opus 4.1 as much as possible. Use the $200 max plan. You will not regret this, guys. You can use Sonnet as well if you want. (t: 60) But honestly, Opus 4.1 is, it's incredible. And I personally wouldn't recommend using anything else. Now, the $200 max plan, the reason being is because you just get a lot of access. (t: 70) So I'm dropping max plan. I barely run into limits anymore. Like I wasn't. I was originally running into limits when I was originally coding the start of SEO grow. (t: 80) But, you know, these days I don't hit any limits at all. Now you do get <PERSON> code access if you use, for example, Pro. (t: 90) But I just, I really don't think you're going to get enough access for it to be worth it. So I would definitely recommend the max max plan, which is $200 a month. (t: 100) And it's highly worth it. This would be my first tip and probably the most important tip. Actually. I disagree with myself. (t: 110) The best tip I can give you is the step by step vibe coding is so much better than trying to one shot with Claude flow, context engineering, archon, et cetera, et cetera. (t: 120) If I was starting from scratch, I would do everything step by step. This is how I did SEO Grove in the first place. (t: 130) Actually, to be honest with you, by the way, this video is sponsored by SEO Grove. If you're interested in a Shopify automation platform, check out SEO Grove.ai. This is your last chance to join the wait list for $1, which rolls over to $99 a month. (t: 140) Um, and yeah, we're, we're literally about to get rid of this. There's about a week left of being able to join the wait list. We've got enough people now for us to actually test and make sure everything works and that we can scale, et cetera, et cetera. (t: 150) So if you want to join and become a founding member, we'll be giving founding members something the other members don't get, for example, 15 blogs a month instead of eight. (t: 160) And even if you're on WordPress, we will be. Yeah. Adding WordPress probably within the month. (t: 170) As soon as I get back from holiday, bang, I'm going to be adding WordPress. So if you want to lock in the three month deal of 99 a month for three months, even if you're on WordPress, it's still worth it because within a month we will be adding WordPress. (t: 180) So yeah, definitely step-by-step vibe coding. Don't try and one-shot things, guys. It just doesn't work as well as you might think. (t: 190) Number three is plan your entire stack before starting to build anything. Okay. And put that stack. In. Inside. Claude. M. (t: 200) D. This is so important. Now, I would actually recommend using Superbase. There's a very specific reason, which is Magic Link, which I'll talk about again, and I'll talk about more in a second. (t: 210) But not only is Superbase a fantastic production database that you can run locally and then put on their servers later on, it allows you to use Magic Link. (t: 220) It allows you to use Resend. It allows you to use a lot of very, very cool things. You could use MySQL or SQLite for the original build. (t: 230) Honestly, though, I would just have a locally running Superbase server, and that will save you so much time in the future when you move over to production. (t: 240) The framework, React, Next.js, there's a hundred things that you could write here. SEO Grove is built on HTML, CSS, and JavaScript. The only reason I'm not fully recommending that is that it does have some issues with security. (t: 250) And security, for some people, if you're working with financials. Or whatever it might be, security might be your number one priority. (t: 260) And I can guarantee SEO Grove will have some kind of problems with security. So I would say use HTML, CSS, and JavaScript. (t: 270) But if you're worried about security, definitely use something like React or Next.js just because they have much better security. And then Redis, you can use Uptash. Redis is just for cron jobs, you know, daily jobs, things like that. (t: 280) But like I said, plan your entire stack. You can use ChatGPT for this. You can say, like, I'm trying to build a Shopify automation platform. (t: 290) Help me build my entire stack. But be careful with that because it may say some things that you don't need. It may say overcomplicated things. Just be careful. (t: 300) In my opinion, just say, I want to create an HTML, CSS, JavaScript, Superbase, Uptash platform. You know, build it for me. So once you know your entire stack, you should put that inside Claude.md. (t: 310) And ask Claude code to constant. But otherwise, you're just going to be updating this, you know, all the content that you've done. (t: 320) So, I'm going to be constantly updating Claude.md with what it's done so far and what the overall picture is. That's also super important. Use Open Router or router instead of individual API systems. (t: 330) Have an Open Router service which can use any model and use Open Router search to give access to the Internet. Now, the reason I mention this is because I'm using three different models inside grove. I'm using, I think, Gemini 2.5 Flash. (t: 340) I'm using Claude Sonnet 4. Or 4.1. I can't remember, or four with the million context. (t: 350) I can't remember which one we're using. And also GPT-4.0 for translation, which we'll move over to GPT-5 shortly. That requires three different services. (t: 360) Anthropic service, Google or Gemini service, and OpenAI or ChatGPT service. If you just use OpenRouter, and it also has OpenRouter search for those that are unaware. (t: 370) If you go on Google, type in OpenRouter search or web search, you can see that just by adding a colon online after any model name, (t: 380) it's basically perplexity, right? It can actually use the internet just like perplexity does, which is amazing, amazing, amazing for things like, (t: 390) well, if you need to find links, or if you need to find this, or if you need to find external information. Honestly, this is an absolute lifesaver, and I cannot recommend OpenRouter enough. (t: 400) It also has much higher ratings. It's got great limits because you're using their API instead of your own. Now, number five, launch with a waitlist. (t: 410) Get as many people as you can interested by paying $1, right? When they've paid that $1, you can then charge them with a huge discount. So we charged $99 after a week or so of them being on the waitlist. (t: 420) And you have their billing details because they've already paid $1. The waitlist is really cool because you can add five people at a time, (t: 430) 10 people at a time. You can control it so that you know how to scale. If you just go ahead and launch and you get 100 customers and you're not set up for 100 customers, (t: 440) I made this mistake with Harbor, you will regret it because people who have a bad experience in the first one or two minutes of using your platform, (t: 450) they will never, ever come back, right? So be extremely careful. Launch with a waitlist. Stagger the amount of people that get access (t: 460) so that you know that you are ready to scale. Create as many landing pages as possible. Now, what the hell does that mean? If I just go to SEO Grove, this is SEO Grove's search console. (t: 470) You can see that we're getting 3.7K impressions a day right now for a brand new SaaS. Very, very interesting stuff happening, right? Now, you can see a lot of it. (t: 480) There are no clicks. But if we can get, for example, Shopify SEO checklist, if we can get this up to position five, for example, (t: 490) which is a long-term strategy of ours, I mean, we could be getting, I don't know, what's 28 days, let's have a look. We could be getting anything from like 100 to 300 clicks a month (t: 500) just for this one landing page. So all you do, and I'll show you exactly how I did this. Now, first of all, obviously, we have a theme, et cetera, et cetera. (t: 510) But I'll show you how I got the keyword for this, right? So if I go on Google and type in keyword tool, now, this is a free tool. Do not pay for it, okay? (t: 520) You can search for free. To see the numbers, you'd have to pay, but you don't need to see the numbers, right? Okay. So what I did was search for a term that will give me useful results no matter what, right? (t: 530) Which in this case is Shopify SEO, right? And if I do control F or command F, whatever, and search for checklist, you'll see Shopify SEO checklist is one of the top things right here. (t: 540) Another one is best Shopify SEO apps. I have made pages for basically all of these keywords one by one, right? And the reason is, is that I know (t: 550) that they will rank on Google because they're here. So what you can also do, is you can take this, right? Shopify SEO checklist, press enter, and then you can grab further keywords if it's relevant. (t: 560) It's not always relevant though. So don't, you don't have to do this, but some of these will be relevant, like Shopify SEO checklist template. That one's relevant. (t: 570) And you just grab all of the ones that were relevant, right? So this one, this one, I don't know. You could do a PDF download pretty easily. This, this, this, right? (t: 580) For example, click here, press copy, right? Okay. So you can see the keywords here. So you can see the keywords here. So you can see the keywords here. So you can see the keywords here. And then let's just hop on over to floor code. Let's just write Claude here. (t: 590) And then if I say, make me a, obviously this is not with the context of my project, but pretend this is with the context of my project. Maybe a landing page for these keywords with my theme, et cetera. (t: 600) Now this is obviously going to say, what the hell are you talking about because there is no theme. (t: 610) But if you do this within the context of your project, it will create a page that looks well, not. It looks like this, but you understand what I mean, right? It works like this. They can actually take as well, which is pretty cool. (t: 620) So this is our secondary strategy. Obviously the primary strategy is YouTube. I'm a YouTuber. That's just a fact, right? (t: 630) But not everyone is a YouTuber. So if you're not a YouTuber, then this is where I would go. You can do ads as well, obviously, but this is just such a good free strategy. (t: 640) There is no cost to this whatsoever. And I honestly believe in. This is where this is where you get causes one save plus create an ad seems (t: 650) like these are going to be going to do a search, not a simple Google search. I'm like, oh, yeah, man, and there's nothing like this. Like one at a time. I'm gonna make a post balls of stuff. (t: 660) And do things like (t: 670) think about that andonterra. High equals. Like I'm gonna say washed water. Okay. And I'm going to put. Like a, not water. this all the time we vibe coded from a similar sass that we very much liked the look of we took (t: 680) their css etc and we applied it here we changed some things we changed some fonts we made our own animation animations for example this here all my business partner said rowan all he said was (t: 690) make me an animation using our design theme our theme design of a brain animation animated into (t: 700) the ai automation center and it vibe coded or created this then for example he said now create me a pricing thing now create me an roi calculator now create me a cta now create me a six part with (t: 710) animations you know blah blah blah etc etc this is all we did right there's nothing really necessarily (t: 720) magic or crazy going on here it's literally just vibe coding landing pages now we actually didn't (t: 730) have a css design system so we didn't have a css design system so we didn't have a css design system so we actually went through the awful process of using an ad hoc design system honestly create your (t: 740) design system at the very beginning of your project make sure it's in claw.md and make sure that it sticks like glue to your design system and then this part here about the player (t: 750) imcp you don't necessarily have to use the player imcp but basically you just need to process the css of a competitor that you like take their css change it and then (t: 760) create a design system from that. Decide what MCPs you actually need and whether it's better to use an MCP or the CLI. For example, you can use the Stripe MCP, but would it be better to (t: 770) just give Claude access to the CLI instead? So what does this mean, right? If I run docker ps, (t: 780) right? Okay, so it's not running right now, but that's fine. You can see that that ran a docker command, right? Or you could technically have a docker MCP, but they're the same thing. (t: 790) I think there are, there probably is a docker MCP, right? Yeah, let's just say this one here. (t: 800) This is an unofficial, but so, you know, this is 375 stars. A lot of people use this, right? So what does this do? It does exactly the same thing as the CLI, right? There's no difference. (t: 810) So what you really need to know is, is it better to use an MCP or is it better to use a CLI? (t: 820) Another great example of this is Superbase. Superbase, you can run everything locally on your computer and it can run its own Superbase commands inside Claude code, right? Or you can (t: 830) use the MCP. So I would use the MCP if I wanted to change something in my production database, (t: 840) which is on superbase.com. But if I was just coding at the very beginning, I would use the CLI to create the database. To populate the database, to edit the local database, et cetera, et cetera. And then I would (t: 850) only use the MCP once it gets to the point where I want to use it for production purposes. And then my next thing here would to be, you should build the skeleton of what you want to (t: 860) build first and then start building functionality. Don't start building functionality until you have Next.js set up, React set up, HTML, CSS, whatever, a homepage, a dashboard. You know, (t: 870) you need to understand that. And then you need to understand that. And then you need to understand how people are going to interact with the website. You can't just go ahead and code a system. You need (t: 880) a framework or a skeleton around it first. And then two more points here. Number 10 is use magic (t: 890) link on Superbase for login. I can't stress this enough. Who the hell uses passwords in 2025? Passwords are extremely complicated. You have to have forgot password. It's just annoying, (t: 900) right? Whereas with SEO grow, all you do is you put your email here, right? And then I get an email. And this is all using Superbase and Resend. (t: 910) I won't show you. So look, we got this email here. I just press log into Grove and we're in the dashboard, right? So this is the separation between the front end and the back end. (t: 920) And you can see just how well that actually works. It's super convenient way to give people access. (t: 930) Just say no, by the way, in order to register an account, it's the same thing. You just let's go log in. And then you just have to do it. And then you have to do it again. So you have to do it again. in there's no register right now just because we've still got the waiting list so we create an (t: 940) account here you put your information put the email you get an email and then that creates the account right so it's super simple as well and then finally number 11 is have an onboarding flow (t: 950) with retention emails use resend for emails make sure you're sending thank you emails when someone signs up make sure you're sending emails when people take action and make sure you send update (t: 960) emails as well also make sure you're sending up like emails for things like non-payment or failed payment all of that good stuff because churn is the thing that will kill any sass if people are (t: 970) churning at 50 or whatever you're going to have a real problem growing i'll leave the video there guys these are just my 11 tips and basically everything i learned from launching my well (t: 980) second sass this is the only sass that i've made where i've done pretty much everything to it (t: 990) harbor was created by a company this was created by me and my business partner rowan thank you so much for watching guys if you're watching all the way to the end of the video you're an absolute legend i'll see you very very soon with some more content peace out

