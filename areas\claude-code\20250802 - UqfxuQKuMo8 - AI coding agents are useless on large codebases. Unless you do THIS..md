---
title: AI coding agents are useless on large codebases. Unless you do THIS.
artist: <PERSON>
date: 2025-08-02
url: https://www.youtube.com/watch?v=UqfxuQKuMo8
---

(t: 0) These AI coding agents, they don't work for my context. My codebase is legacy. It's too big. These tools choke on the amount of code. That's what I hear a lot nowadays. (t: 10) While sometimes that is the case, I think more often than not, it's just the case of using them correctly. I like to compare that to eating spaghetti with a spoon. (t: 20) It's technically possible, but it'll go a lot faster if you use a damn fork. So today I want to show you what kind of tools I use, and I will show you how to 10x, 20x, or even 30x performance of your AI coding assistants (t: 30) without even having to resort to sub-agents or other approaches (t: 40) that throw a lot more money at the problem. So yeah, let's dive in. So the first tool I would like to show you is called <PERSON>. And <PERSON> does semantic search and edit, so it's a bit smarter than text files and brute forcing. (t: 50) The official docs are here. It's on GitHub. It's open source. It's free. You can run it on your machine without paying anything. (t: 60) And the powerful features are semantic code retrieval and editing tools, so it's a smarter way to search for code or search through codebases and to edit code. (t: 70) So let's see how <PERSON> performs on the original task we did, (t: 80) the refactoring session a couple of videos ago. So this is the same codebase at the same start. I have my docs and my doc tests, and I want to refactor this. (t: 90) And I already have Cloud Code up and running, one of my AI coding agents. And I installed the Serena MCP server, as you can see. (t: 100) And I already initialized the system by asking it to run in Serena mode. It's all in the documentation, if you are curious. (t: 110) So there we go. We are up and running. Let me grab my prompt. There we go. (t: 120) I'm going to put the prompt here, and we can discuss it in a second. But it's basically asking to refactor it. (t: 130) I'm giving a bit more detail than I did in my original video, like what I want to happen. And yeah, that's it. So let me start the timer. (t: 140) In my original run, it took 12 minutes to do this myself. So now if you know what you're doing, and you have the tools necessary, let's see how far we get. (t: 153) So I'm asking it to both refactor and add the new behavior at the same time. (t: 160) And it's going to build a plan. That plan looks good. (t: 165) And as you can see, it's using Serena. Now it's doing search texts. (t: 170) But now it's doing semantic searches, this find symbol thing. Which is a faster way. Especially in large codebases. This is a really mid-small codebase, this eshop on web thing. (t: 180) So it's not really that impressive. But already in this example, we'll see what the impact is. (t: 190) And as always, you can follow along if you want to. But I have become a bit more hands-off. (t: 200) Now it's implementing the subclasses. Yeah, it already introduced the factory method. Did not push the methods down as much. We specified in the system prompt, or in the prompt. (t: 212) It now starts to figure out that it has references. And now it's using the semantic search, as you can see. Not just a grep-based approach. (t: 220) Again, small example won't matter. Huge legacy codebases matters a lot. Now it's going to TDD the new test drive, the new implementation. (t: 230) We're rerunning our tests. And we're running the full suite. So it's probably done. If there's no compile errors. (t: 240) Maybe there's a compiler or two. (t: 243) It's going to run the full solution build to make sure. We can take a look at the code already. (t: 251) This is the interesting part. Let's first see what it did on the test code. (t: 260) Let's see that we don't miss the done signal. So yeah, I see a factory method I like. (t: 270) I see new tests that are correct. And it tests for the basic behavior. (t: 280) So it did everything what I would expect. Let's take a look at the implementation. The only thing it did not do is push down the members. I obsessed over that in my previous video. (t: 290) Yeah, that's not happening here. But other than that. It looks good. It uses a lot less tokens. (t: 300) So your weekly anthropic token budget will be way more. You will be able to run a lot faster and a lot longer. (t: 310) Especially if you use these kinds of tools. So yeah, now we're just waiting on the build. But we are five minutes in. And I'm not obsessing over the code quality. (t: 320) We could try that. But I first want to have it like finish the job. Which would be good enough in most circumstances. And as you can see. (t: 330) It's faster than I would ever be able to do this. Yeah, so it's done in six minutes. That's twice as fast as I would be able to do this. (t: 340) Okay, so now I'm going to ask it to push down members. To see if it gets there in the same amount of time. I asked you to push down all members of the dog type. (t: 350) To the specific subclasses that use them. Please do that now. Verify the unit tests still pass. (t: 360) So if this all like hits within 12 minutes. That means that I can go do something else. (t: 370) Attend a meeting. Coach a colleague. Whatever. So yeah, that is actually pretty impressive. Cool, we're already in unit test run. (t: 380) Let's take a look at the resulting code. Yeah, I love it. This is exactly the way I would have done it. (t: 390) And we needed two prompts and an MCP server. And it's faster than I'm doing it. Than I would do it by hand. So that's pretty impressive. (t: 400) So that's the first MCP server I wanted to highlight. If you find one for your language. That does either embedding based searches. Or semantic search and edit like Serena. (t: 410) Those are the ones that you can use. Those really speed up things. It has to do less build, fail, compile, retry loops. And the token spend is impressively lower. (t: 420) So yeah, that's the first two. I did a lot of searching on GitHub for a big .NET codebase. Because that's the language I am most familiar with. (t: 430) And I stumbled on Umbraco. Which is a content management system. Open source. And it's pretty beefy as we'll see. (t: 440) So I downloaded that. We'll see. I cloned that one. And then I did a line count. Using the clock. Count lines of code tool. And as you can see there's 55,000 files. (t: 450) And there are 182,000 typescript files. And 360,000 lines of C sharp. (t: 460) So not huge. But large enough to prove our point here today. So that is the codebase we will be taking a look at. (t: 470) So first let's do a benchmark again. So we have something to compare it to. First I want to do it myself. Then I want to do it naively with cloud. (t: 480) And then I want to start using the power tools. So we can compare stuff. Super scientifically of course. So I am going to start the timer. And I am going to just do a simple refactoring. (t: 490) I am going to rename a heavily used type in a large codebase. So I'm going to grab it. It's the global settings type of this Umbraco. (t: 500) And let's rename it. Doesn't really matter. And now we are just using my IDE. (t: 510) So we are using Rider in this case. Which has pretty good refactoring support. (t: 520) That's why I am name calling it. And as you can see in a multi hundred thousand lines of code. Codebase this happens. But it's also not immediate. (t: 530) Okay. So we changed 119 files. No. 147 files. (t: 540) So that's not nothing. And what I would do myself after doing a refactoring. I would run unit tests. So I am also going to do that. And that's super important if you use AI coding agents. (t: 550) So I am going to include that as part of the experiment. This does both a compile of the solution. Or most of the projects in the solution. (t: 560) And run the unit test suite. And yes this takes a while. There we go. So give or take three minutes to do this ourselves. That is the benchmark we will be comparing against. (t: 570) So let's get this party started. And let's first do another benchmark. So we are not going to give a plot code any forks. (t: 580) We are just going to have it do this with a spoon. So no additional tools. Just the basic plot code approach. This is a huge codebase as we mentioned. (t: 590) So it's going to be wild. Let me grab a prompt. There we go. (t: 600) So the prompt is asking it to rename a heavily used file in this codebase. And make sure that the unit tests still pass. (t: 610) So nothing too out of the ordinary. I am going to start the timer. And we will move on. We are going to have to fast forward through this in the video. (t: 620) In order to not bore you to pieces. So yeah here we go. Okay this just took way too long to capture on video. So it got there in the end. (t: 630) It had to rebuild the code four times. And it took three whole hours. Alright now let's see how a smarter plot would handle this. (t: 640) Or a plot given some smarter tools at least. And the tool I will be showing you. Or I would like to showcase. Is Refactor MCP. It is an MCP server. (t: 650) It is for .NET only. But if you look around for the language you are using. I am sure you will find one that does similar things. But this one is Refactor MCP by Dave Hillier. (t: 660) And it's a simple standard IOMCP server. So it's a one liner to add this to any AI coding assistant. And it provides Roslyn based refactoring tools for C sharp. (t: 670) So just like Serena. It's a smarter way of. Navigating and editing. Especially editing code. In this case it's based on Roslyn. (t: 680) Analyzers. But that's not super important. What is important is that it provides a server. That exposes tools to do refactorings for you. (t: 690) So you can ask it to extract a method. Move method. Rename things. Save, delete. Extract types. All the things you would look for in a refactoring tool. (t: 700) They are here. So yeah. I went and installed that. Let me spin up Cloud. (t: 710) And let's look at the MCP server. So that one is running. Yeah. So it's running. And I disabled all the other ones. And then we need to do one more thing. (t: 720) Which is tell it to use this MCP server for refactoring. You could add this in the Cloud.md file. Or in the system prompt somewhere. (t: 730) Still has to find a solution. That's my bad. And then let's grab the refactoring prompt. Which is the same rename as we did. (t: 740) As you can see this takes a bit of time. (t: 744) And then let's start our timer. Because now we are starting the refactoring session. (t: 752) And timer is running. Prompt is fired. Codebase is clean. (t: 760) So yeah. We're good to go. Yeah. No need to tweak anything. Now it should be calling the MCP server to perform the rename. Yeah. (t: 770) So it's calling the rename symbol tool. (t: 772) Which is also pretty heavy process. (t: 780) It's a huge codebase. 150-ish files need to be changed. (t: 784) But it sure beats a doom looping. So the rename has happened. (t: 790) Now it should run. Now it should run the tests again. Yeah. Let's take a look at what happened. 143 files were changed. (t: 800) That sounds correct. (t: 803) It's building the test project. (t: 810) And this is where like most of the time goes. (t: 814) Once a coding agent starts doom looping. And needs to like have multiple passes of failed compiles. (t: 820) Changed some files. Failed compiles. So this is where your time goes if you don't give it intelligent tools. All right. That's the compile done. (t: 830) Now we are running our unit tests. That takes about 30 seconds. So it should get done in five-ish minutes. (t: 840) There we go. So four minutes, 30 seconds. And I didn't have to intervene. This was a one-shot prompt. (t: 850) So I just put prompt and it went on and did its thing. So imagine this in a multi-step refactor process. (t: 860) It's totally hands off. It's not as fast as doing it yourself. But it's like autonomous. Which is pretty interesting as a result. So yeah. (t: 870) This is using the refactor MCP server. Okay. So now to some conclusions. And to share some numbers to put. Things in perspective. These are the two runs I did with the large code base. (t: 880) Simple rename. And the one on the 31st. The first row is the brute force approach. The just do it cloud approach. (t: 890) And as you can see it takes around 28 million tokens. And like the cost estimate somewhere around 14 bucks. And if you look at what changed when I used the MCP server. (t: 900) I had a lot of problems. I had a lot of problems with the code base. The only thing that changed when I used the MCP server. (t: 910) You see 1 million tokens being sent back and forth. And only 60 cents. So that is a factor of roughly 28 times less token spend. (t: 920) And a factor of 20 times cheaper. And especially if you look at the time spent. Running this cloud code agent in the default way. (t: 930) It took three hours to finish. It did finish. It had to recompile I think. Four times. But that's nothing compared or that's a whole lot compared to the five minutes it took when using this MCP server. (t: 940) So that's also like a 30x improvement. So please before using subagents take a look at this stuff. (t: 950) So yeah as you could see one or two MCP servers in your tool belt will make these tools almost as fast as you yourself would be and will improve these coding agents productivity by sometimes even 20 times. (t: 960) Which is not something that should be ignored. So if you found relevant MCP servers or tips and tricks to help these things working in a large code base please shout out in the comments below. (t: 970) And I hope to see you again next time. (t: 980) Bye bye.

