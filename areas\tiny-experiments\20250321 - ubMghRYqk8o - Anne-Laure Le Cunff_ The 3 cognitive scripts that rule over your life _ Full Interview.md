---
title: <PERSON><PERSON><PERSON><PERSON>: The 3 cognitive scripts that rule over your life | Full Interview
artist: Big Think
date: 2025-03-21
url: https://www.youtube.com/watch?v=ubMghRYqk8o
---

- [00:00:00](https://www.youtube.com/watch?v=ubMghRYqk8o&t=0) - I'm <PERSON><PERSON><PERSON><PERSON>. I'm a neuroscientist and the author of Tiny Experiments, How to Live Freely in a Goal-Obsessed World. Taking control of your mindset with <PERSON><PERSON><PERSON><PERSON>. Part one, the experimental mindset. Are we all experiencing cognitive overload? A lot of us are currently experiencing cognitive overload, and there are many reasons for that. One of them is that the world is changing fast, and we're trying to hoard as much information as possible to understand what's going on around us.

- [00:00:30](https://www.youtube.com/watch?v=ubMghRYqk8o&t=30) - A lot of us are currently experiencing cognitive overload, and there are many reasons for that. One of them is that the world is changing fast, and we're trying to hoard as much information as possible to understand what's going on around us. Another one is that we're trying to be as productive as possible in order to keep up, again, with this world that keeps on changing. Because of that, we try to build systems, we try to stick to routines, and we try to go through very long lists of tasks, often ignoring our mental health in the process. In essence, there is a lot more to think about on a daily basis, but our brains haven't evolved. They're still the same that they were thousands of years ago. We're all staring at a giant leaderboard with social media,

- [00:01:00](https://www.youtube.com/watch?v=ubMghRYqk8o&t=60) - In essence, there is a lot more to think about on a daily basis, but our brains haven't evolved. They're still the same that they were thousands of years ago. We're all staring at a giant leaderboard with social media, where we can see how other people are progressing, their success, and where we keep on comparing ourselves to each other. This creates anxiety because we keep asking ourselves, how am I doing? Am I doing better? Am I being fast enough? Am I being productive enough? Am I being ambitious enough? What is the maximalist brain? What I call the maximalist brain is this belief that we have that whatever we do, it has to be the biggest, the most ambitious version of this goal.

- [00:01:30](https://www.youtube.com/watch?v=ubMghRYqk8o&t=90) - What is the maximalist brain? What I call the maximalist brain is this belief that we have that whatever we do, it has to be the biggest, the most ambitious version of this goal. When we want to exercise, we decide that we have to go to the gym every single day. If we want to start writing, we decide that we're going to start writing a book. If we want to explore a new project, we decide that this has to be a start-up. The problem with the maximalist brain is that it very often leads to overwhelm and burnout and sometimes just completely abandoning our projects because they're just too busy. So we have to start writing. So we have to start writing. Tiny experiments offer an alternative to this maximalist approach, where instead of going for the bigger thing, you go for the thing that is most likely to bring you discovery,

- [00:02:00](https://www.youtube.com/watch?v=ubMghRYqk8o&t=120) - and burnout and sometimes just completely abandoning our projects because they're just too busy. So we have to start writing. So we have to start writing. Tiny experiments offer an alternative to this maximalist approach, where instead of going for the bigger thing, you go for the thing that is most likely to bring you discovery, fun, enjoyment, and that is based on your curiosity rather than an external definition of success. When you're running a tiny experiment, you're not trying to do something big. What you're trying is just to learn something new. How did you discover the experimental mindset? I divide myself into two groups. I divide myself into two groups. I divided myself into two groups. I divided myself into two groups. In the first one, I had a very linear approach that was driven by

- [00:02:30](https://www.youtube.com/watch?v=ubMghRYqk8o&t=150) - I divide myself into two groups. I divide myself into two groups. I divided myself into two groups. I divided myself into two groups. In the first one, I had a very linear approach that was driven by traditional definitions of success. I did my best to do well in school and then I got a good job at Google. And then I tried to climb the corporate ladder, getting a promotion, working on the best projects possible. And from an external standpoint, I should have been happy, but I wasn't. Instead, I was feeling empty inside. I was both bored and burned out. I was both bored and burned out. my job at Google thinking that I should try to do something different and not realizing that I was following yet another script of success. I decided to start a startup and again I didn't find happiness

- [00:03:00](https://www.youtube.com/watch?v=ubMghRYqk8o&t=180) - I was both bored and burned out. I was both bored and burned out. my job at Google thinking that I should try to do something different and not realizing that I was following yet another script of success. I decided to start a startup and again I didn't find happiness there by following this idea of success that everyone around me was following. It's only when my startup failed and that for the very first time in my life I didn't have a clear idea of what I was supposed to do next in order to be successful that I finally asked myself what is it that I wanted to do? What would make me happy even if I forgot about the traditional definition of success? And so I went back to the drawing board and I started thinking about what I was curious about. Again not based on traditional definitions of success. What were topics I would be excited to

- [00:03:30](https://www.youtube.com/watch?v=ubMghRYqk8o&t=210) - wanted to do? What would make me happy even if I forgot about the traditional definition of success? And so I went back to the drawing board and I started thinking about what I was curious about. Again not based on traditional definitions of success. What were topics I would be excited to explore even if nobody was watching? And for me that was the brain. I had always been fascinated with why we think the way we think and why we feel the way we feel. So I decided to go back to university to study neuroscience. I completed my graduate studies and I got a PhD in neuroscience. Throughout this journey I decided to learn in public and this is how I started my newsletter. Every week I would pick a topic that I had discovered in my studies in university and I would take those neuroscience

- [00:04:00](https://www.youtube.com/watch?v=ubMghRYqk8o&t=240) - I completed my graduate studies and I got a PhD in neuroscience. Throughout this journey I decided to learn in public and this is how I started my newsletter. Every week I would pick a topic that I had discovered in my studies in university and I would take those neuroscience insights and turn them into practical tools that I would write about in the newsletter to help other people apply them in their life and work. This tiny experiment of starting to write online and sharing what I was learning in public was the beginning of my journey of learning about neuroscience. I was learning about neuroscience in public and I was learning about neuroscience in public and I was learning about neuroscience in public of my work, of trying to understand how we can live more experimental lives. Why is mindset so important? A mindset is a default way of seeing the world. And our mindsets influence so many things in our

- [00:04:30](https://www.youtube.com/watch?v=ubMghRYqk8o&t=270) - of my work, of trying to understand how we can live more experimental lives. Why is mindset so important? A mindset is a default way of seeing the world. And our mindsets influence so many things in our lives. They influence our decisions. They influence our relationships. They influence the way we think and even the way we feel. When we're not aware of our mindsets, they can impact the direction of our life, the path that we're taking without us even realizing it. Being aware of your mindsets is the difference between living a conscious life where you're making choices in accord with what you actually want and going where you actually want to go

- [00:05:00](https://www.youtube.com/watch?v=ubMghRYqk8o&t=300) - the direction of our life, the path that we're taking without us even realizing it. Being aware of your mindsets is the difference between living a conscious life where you're making choices in accord with what you actually want and going where you actually want to go versus being on autopilot and having those mindsets subconsciously drive all of your decisions. The great thing about mindsets is that they can actually change. But the first step is to make them conscious. What are the mindsets that hold us back? There are three subconscious mindsets that get in the way of us living happy, conscious lives. These three mindsets are called the cynical mindset, the escapist mindset, and the perfectionist

- [00:05:30](https://www.youtube.com/watch?v=ubMghRYqk8o&t=330) - There are three subconscious mindsets that get in the way of us living happy, conscious lives. These three mindsets are called the cynical mindset, the escapist mindset, and the perfectionist mindset. The cynical mindset is when we have lost all curiosity and ambition in life. And we're actually sometimes even making fun of earnest people who still have this high level of curiosity and ambition. When we're cynical, we feel like there's no point trying because we're in survival mode. We're in a state of panic. We're in a state of panic. We're in a state of panic. We're in a state of panic. We're in a state of panic. We're in a state of panic. We're in a state of panic. All the time. So things that we might be doing instead is doom scrolling, sitting on the sofa, going through negative news, being stuck in this cycle, and then maybe even spending a lot of time and energy discussing and debating those negative news with other people. In the escapist mindset,

- [00:06:00](https://www.youtube.com/watch?v=ubMghRYqk8o&t=360) - All the time. So things that we might be doing instead is doom scrolling, sitting on the sofa, going through negative news, being stuck in this cycle, and then maybe even spending a lot of time and energy discussing and debating those negative news with other people. In the escapist mindset, we're still curious, but we have decided to let go of our ambitions. We're trying to do everything we can to escape our responsibilities. That can take the form of retail therapy, binge watching, or dream planning, or next vacation. Instead of doing something right now to change our lives. In the perfectionist mindset, we try to escape uncertainty through work. So we have high ambition, but low curiosity. That might look like self-coercion, overworking ourselves, toxic productivity. Our goals are

- [00:06:30](https://www.youtube.com/watch?v=ubMghRYqk8o&t=390) - Instead of doing something right now to change our lives. In the perfectionist mindset, we try to escape uncertainty through work. So we have high ambition, but low curiosity. That might look like self-coercion, overworking ourselves, toxic productivity. Our goals are driving all of our decisions. We feel like if we manage to achieve that goal, if we manage to be successful, then we'll be happy. You can picture those three subconscious mindsets on the four by four matrix, where the two different factors are one, and the other is the other. So we're going to look at the four by four matrix, and we're going to look at the four by four matrix. So we're going to look at the four by four matrix, and we're going to look at the four by four matrix. In the case of the cynical mindset, it's low curiosity, low ambition. For the escapist mindset, high curiosity, but low ambition. And with the perfectionist mindset, you do have high ambition, but you have decided to let go of your curiosity. Those mindsets are actually very fluid,

- [00:07:00](https://www.youtube.com/watch?v=ubMghRYqk8o&t=420) - In the case of the cynical mindset, it's low curiosity, low ambition. For the escapist mindset, high curiosity, but low ambition. And with the perfectionist mindset, you do have high ambition, but you have decided to let go of your curiosity. Those mindsets are actually very fluid, and they might change depending on our situation and different triggers and different ambitions that we might have at the moment. This is actually really good news because that means that these mindsets are not fixed personality traits. We can change our lives. We can change our lives. We can change them by becoming aware of them and then making the decision to change our mindsets. This is something we can achieve. What mindset should we strive for? There is an alternative to those three mindsets, which is called the experimental mindset. This is

- [00:07:30](https://www.youtube.com/watch?v=ubMghRYqk8o&t=450) - change them by becoming aware of them and then making the decision to change our mindsets. This is something we can achieve. What mindset should we strive for? There is an alternative to those three mindsets, which is called the experimental mindset. This is a mindset where your curiosity and your ambition are both high. In an experimental mindset, you're open to uncertainty. You see it as an opportunity to explore, to grow, and to learn. Having an experimental mindset helps us completely reimagine a relationship to ambition and to goals. When you have an experimental mindset, instead of chasing those linear goals that give you the illusion of certainty, you're open to designing experiments.

- [00:08:00](https://www.youtube.com/watch?v=ubMghRYqk8o&t=480) - and to learn. Having an experimental mindset helps us completely reimagine a relationship to ambition and to goals. When you have an experimental mindset, instead of chasing those linear goals that give you the illusion of certainty, you're open to designing experiments. Instead of trying to get to a specific outcome, you start from a research question. Anytime you don't understand something, it doesn't create fear. It creates curiosity. Having an experimental mindset means seeing failures as data points. You can see failures as data points. You can see that you can learn from them. It means being open to making mistakes because you know you're going to learn from them. It means embracing the fact that you might not have a plan, that you don't know what's coming. And this is great. It means that you can design your life

- [00:08:30](https://www.youtube.com/watch?v=ubMghRYqk8o&t=510) - that you can learn from them. It means being open to making mistakes because you know you're going to learn from them. It means embracing the fact that you might not have a plan, that you don't know what's coming. And this is great. It means that you can design your life in a way that is conscious and connected. How do you cultivate an experimental mindset? The idea of cultivating an experimental mindset is based on the scientific method. And this is very simple. First, you start by observing your current situation, by looking at the world around you. Then you ask a research question and you design a tiny experiment to collect data, which you can then analyze. Based on those results, you can decide what your next step is. What's great about that is that even

- [00:09:00](https://www.youtube.com/watch?v=ubMghRYqk8o&t=540) - you start by observing your current situation, by looking at the world around you. Then you ask a research question and you design a tiny experiment to collect data, which you can then analyze. Based on those results, you can decide what your next step is. What's great about that is that even though you don't know where you're going, you can trust that you're going to grow through each cycle of experimentation. To design an experiment, you need to commit to curiosity. A great way to do this is to design what I call a pact. This is a commitment to curiosity. You need to commit to curiosity. And a bouquet of visitor figures is relied on to elevate your scientific thinker vidéo score. So thisòng lecture of questions is Rے had more than $1 on it. Today, I'm Una and my team consists of two Seanrio,

- [00:09:30](https://www.youtube.com/watch?v=ubMghRYqk8o&t=570) - curiosity. And a bouquet of visitor figures is relied on to elevate your scientific thinker vidéo score. So thisòng lecture of questions is Rے had more than $1 on it. Today, I'm Una and my team consists of two Seanrio, になり we design a semi- Jabia Aqcois in Indonesia, and for this semester. Now, these are recently published research documents with a questionnaire. Here, you can see R página, non- parte de experiment soil does not lay out and complex fact, but here we have an reflecting divided set of results from Ku enquanto açœ ktes complacibrês. And I'm going to tamp, I'm muy compachsenlal p cosmic experimentbuat con qualche experiment. Hey man qui a compara pé ahper te l'explora est un experiment et quire rempliche to determinнуться And once you have all of that data, you can analyze it and decide what the answer is. The second reason is that it allows you to notice when you're falling prey to the maximalist brain. You can make sure that you're keeping your experiment tiny enough that you're actually going to complete it

- [00:10:00](https://www.youtube.com/watch?v=ubMghRYqk8o&t=600) - And once you have all of that data, you can analyze it and decide what the answer is. The second reason is that it allows you to notice when you're falling prey to the maximalist brain. You can make sure that you're keeping your experiment tiny enough that you're actually going to complete it by choosing a duration that is reasonable, something you can actually achieve, so you can collect all of the necessary data. A pact is purposeful. It needs to be something you care about. And what's great is that when each pact you design has purpose imbued into it, you don't need to have a grand purpose in life. A pact is actionable. This is something that you need to be able to do right now. You don't need extra resources. You don't need help from other people. This is something you can try straight away. A pact is continuous. This is something that you need to do regularly.

- [00:10:30](https://www.youtube.com/watch?v=ubMghRYqk8o&t=630) - A pact is actionable. This is something that you need to be able to do right now. You don't need extra resources. You don't need help from other people. This is something you can try straight away. A pact is continuous. This is something that you need to do regularly. Again, you decide on the duration, the number of trials, and then you say, I'm going to do this action for two weeks. I'm going to do this action for two months. I'm going to do this action for one year. And finally, a pact is sustainable. A pact is trackable, not measurable. You don't need complicated metrics. You only need to be able to say whether you did it or not. Did you do the action? Did you perform it? Yes or no. That's the only tracking you need. A pact is not a New Year resolution.

- [00:11:00](https://www.youtube.com/watch?v=ubMghRYqk8o&t=660) - You only need to be able to say whether you did it or not. Did you do the action? Did you perform it? Yes or no. That's the only tracking you need. A pact is not a New Year resolution. This is not something that you decide on that is very ambitious at the beginning of the year and that you're going to abandon. This is something small and achievable that you can start doing at any point during the year. A pact is not a habit either. The difference between a habit and a pact is that it's not a habit. It's a habit. It's a habit. A habit and an experiment is that with a habit, you're very clear that this is something that's going to be good for you. And so you commit to it for an indefinite amount of time. For example, you say, starting today, I'm going to go to bed at the same time. Whereas with an experiment, you're not quite sure whether this is going to work for you or not.

- [00:11:30](https://www.youtube.com/watch?v=ubMghRYqk8o&t=690) - A habit and an experiment is that with a habit, you're very clear that this is something that's going to be good for you. And so you commit to it for an indefinite amount of time. For example, you say, starting today, I'm going to go to bed at the same time. Whereas with an experiment, you're not quite sure whether this is going to work for you or not. You're going to test it. And so you're going to say, I'm going to go to bed at the same time every night for two weeks. And then only I'm going to decide whether this is good for me or not and whether I want to turn it into a habit. And a pact is not a KPI, an OKR, a performance metric or however people call them in the corporate world. It's really just about learning something new. It's not about being successful or getting to a specific outcome.

- [00:12:00](https://www.youtube.com/watch?v=ubMghRYqk8o&t=720) - And a pact is not a KPI, an OKR, a performance metric or however people call them in the corporate world. It's really just about learning something new. It's not about being successful or getting to a specific outcome. How do you analyze the collected data? Once you've completed your pact and you've gone through the entire duration of that data collection phase, you can finally look at the data. I highly encourage you. While you're running your pact to take little notes. It can be very simple, a few bullet points on your phone, something in your journal, but just to keep track of whether you did it or not and how it felt. Based on that, you can make the decision to either persist with your pact as is because it works for you.

- [00:12:30](https://www.youtube.com/watch?v=ubMghRYqk8o&t=750) - It can be very simple, a few bullet points on your phone, something in your journal, but just to keep track of whether you did it or not and how it felt. Based on that, you can make the decision to either persist with your pact as is because it works for you. You can also pause it if you feel like that's not really something you want to keep going with, or you can pivot, which means making a little tweak and changing something before you start your next cycle of experimentation. A lot of us tend to only pay attention to the fact that we're doing something wrong. So we're not going to do anything wrong. We're going to do something right. We're not going to do anything wrong. We're not going to do anything wrong. We're not going to do anything wrong. External data or internal data when we're analyzing our experiments. For example, we might only look at the external metrics of success or only at the internal feelings that we might be experiencing. But both are very valuable in order to make the right decision when it comes to your next steps.

- [00:13:00](https://www.youtube.com/watch?v=ubMghRYqk8o&t=780) - For example, we might only look at the external metrics of success or only at the internal feelings that we might be experiencing. But both are very valuable in order to make the right decision when it comes to your next steps. The external signals might show you whether this is something that is worth pursuing in terms of financial success or career or any ambition that you might want to explore. But the internal signals are also very important. There's no point being successful externally if it feels horrible to work on this project. And equally, if it feels really good, but you have no way to sustain yourself with this project, it might be worth reconsidering the parameters to find a way where you can have an overlap

- [00:13:30](https://www.youtube.com/watch?v=ubMghRYqk8o&t=810) - There's no point being successful externally if it feels horrible to work on this project. And equally, if it feels really good, but you have no way to sustain yourself with this project, it might be worth reconsidering the parameters to find a way where you can have an overlap between external success and also internal positive feelings and emotions of working on this. How have you personally employed the experimental mindset? Let me give you an example. I designed an experiment where I wanted to explore whether I wanted to become a YouTuber. This was something I had noticed a lot of friends around me doing, and they seemed to have a lot of fun. That piqued my curiosity enough that I wanted to give it a try. So I designed a pact, and as said, I'm going to publish a video every week until the end of the year.

- [00:14:00](https://www.youtube.com/watch?v=ubMghRYqk8o&t=840) - This was something I had noticed a lot of friends around me doing, and they seemed to have a lot of fun. That piqued my curiosity enough that I wanted to give it a try. So I designed a pact, and as said, I'm going to publish a video every week until the end of the year. Very simple pact, which I completed. Every week, I published a new video. At the end of my pact, I looked at the data. External data, pretty good. But looking at the traditional metrics of success of a YouTube channel, I got to a good number of subscribers, a lot of positive comments, people seemed to like the videos, and I even had some people reaching out and asking if we could collaborate together. But internal data. I actually did not enjoy producing these videos.

- [00:14:30](https://www.youtube.com/watch?v=ubMghRYqk8o&t=870) - a lot of positive comments, people seemed to like the videos, and I even had some people reaching out and asking if we could collaborate together. But internal data. I actually did not enjoy producing these videos. Every week when I had to sit down in front of the camera, I was dreading it. I love having face-to-face conversations with people and seeing their reactions in real time, but just looking at a camera with no feedback whatsoever, was very uncomfortable for me. As a result, every week I was procrastinating for so long, every time I had to film a video. I felt deeply anxious, and I was not even able to work on anything else on those days where I was supposed to film. This is a typical example showing why it is so important to considering both the external and the internal signals before making a decision.

- [00:15:00](https://www.youtube.com/watch?v=ubMghRYqk8o&t=900) - As a result, every week I was procrastinating for so long, every time I had to film a video. I felt deeply anxious, and I was not even able to work on anything else on those days where I was supposed to film. This is a typical example showing why it is so important to considering both the external and the internal signals before making a decision. Based on this, even though the YouTube channel was fairly successful in such a short amount of time, I decided to stop. I realized that I was not going to be a YouTuber. I prefer to keep on writing my newsletter. What are some tiny experiments anyone can do? You can run tiny experiments in all areas of your life. In work, for example, you could say, I'm going to write an internal newsletter every week for the next six weeks,

- [00:15:30](https://www.youtube.com/watch?v=ubMghRYqk8o&t=930) - What are some tiny experiments anyone can do? You can run tiny experiments in all areas of your life. In work, for example, you could say, I'm going to write an internal newsletter every week for the next six weeks, where I share the most interesting links that I find. In relationships, you could say, I'm going every Sunday to sit down and send a note to a friend I haven't talked to in a while. And even when it comes to work, you could say, I'm going to go for a walk for 20 minutes for 20 days to see how I feel at the end of that experiment. With an experiment, you're not making any assumptions as to whether this is going to work for you or not. A habit that a friend has, for example, going for a run three times a week, might not be that good for you.

- [00:16:00](https://www.youtube.com/watch?v=ubMghRYqk8o&t=960) - With an experiment, you're not making any assumptions as to whether this is going to work for you or not. A habit that a friend has, for example, going for a run three times a week, might not be that good for you. Maybe when it comes to your health and body movement, you prefer dancing or something else. That's why it's interesting to run an experiment before committing to your habit. When it comes to running, for example, you could say, I'm going to try that. I am going to go and run three times a week for three weeks, not for the rest of my life. And at the end of the three weeks, I'm going to decide whether this is what I want to keep doing, or if I want to experiment with another way to move my body.

- [00:16:30](https://www.youtube.com/watch?v=ubMghRYqk8o&t=990) - And at the end of the three weeks, I'm going to decide whether this is what I want to keep doing, or if I want to experiment with another way to move my body. In essence, committing to curiosity is ensuring that you're going to live a life that is intentional. That you're going to live your life, not the life that other people are expecting you to live. Curiosity keeps you adaptable and nimble in an ever-changing world. It ensures that you stay open to new possibilities. And frankly, it just makes life more fun. This all might sound philosophical, but there's actually a lot of neuroscientific research showing that

- [00:17:00](https://www.youtube.com/watch?v=ubMghRYqk8o&t=1020) - It ensures that you stay open to new possibilities. And frankly, it just makes life more fun. This all might sound philosophical, but there's actually a lot of neuroscientific research showing that when we experience thirst for water, the same parts of the brain activate than when we experience thirst for information. So when we say, I'm thirsty for knowledge, I want to learn more, I want to know more, we couldn't be more right. And thinking about your own mindsets, developing this self-awareness, is really just a way to direct this curiosity towards the things that you actually want to do with your life.

- [00:17:30](https://www.youtube.com/watch?v=ubMghRYqk8o&t=1050) - is really just a way to direct this curiosity towards the things that you actually want to do with your life. The pace of technological and societal change has accelerated massively. We feel like we cannot rely on the world. We don't rely anymore on those traditions and institutions that used to give us a sense of stability. It's become increasingly difficult, or maybe even impossible, to know what skills we should invest in in order to build the careers of the future. And to make it worse, we have infinite access to information, which makes us feel like if we just keep on reading, on listening, on watching,

- [00:18:00](https://www.youtube.com/watch?v=ubMghRYqk8o&t=1080) - It's become increasingly difficult, or maybe even impossible, to know what skills we should invest in in order to build the careers of the future. And to make it worse, we have infinite access to information, which makes us feel like if we just keep on reading, on listening, on watching, we might get that key information that is going to give us finally the sense of elusive certainty. This modern environment, is anxiety-inducing. The problem today is that it's become really, really hard to know the difference between information and knowledge. Information is just a piece of data. It could be valid, it could be not valid, it could come from any kind of source, it could be based on different contexts and circumstances that we're not aware of. And by consuming all of this information,

- [00:18:30](https://www.youtube.com/watch?v=ubMghRYqk8o&t=1110) - Information is just a piece of data. It could be valid, it could be not valid, it could come from any kind of source, it could be based on different contexts and circumstances that we're not aware of. And by consuming all of this information, we might actually not take action to go in the real world and collect our own data, our own knowledge, that would be much more helpful in order to build our own knowledge. The less control we have, the more we seek it. And that might take a lot of forms. For example, trying to consume as much information as possible, just so we feel like we have the sense of clarity as to what is going on. That could also look like sticking to the safe path, the more obvious one, where we feel like we understand all of the parameters.

- [00:19:00](https://www.youtube.com/watch?v=ubMghRYqk8o&t=1140) - just so we feel like we have the sense of clarity as to what is going on. That could also look like sticking to the safe path, the more obvious one, where we feel like we understand all of the parameters. Or sometimes that could even look like doing nothing, because we're too afraid to do something dangerous. How are uncertainty and anxiety linked? Uncertainty fuels anxiety. Research shows that when we experience uncertainty, our neural activity intensifies. Our brains are basically on high alert, getting ready for any threat that we might face. And that happens even when there's no actual danger.

- [00:19:30](https://www.youtube.com/watch?v=ubMghRYqk8o&t=1170) - our neural activity intensifies. Our brains are basically on high alert, getting ready for any threat that we might face. And that happens even when there's no actual danger. In studies, uncertainty has been found to cause more stress than individual pain. The reason why is because when we know we're going to experience uncertainty, we can mentally prepare for it. Whereas when we're facing uncertainty, the doubt of not knowing what kind of pain, what level of pain, is actually more stressful than knowing exactly what's going to happen. This is also why we prefer getting bad news than waiting for an answer. This feels better. Now we know. Even if it's negative, we feel like we're in control.

- [00:20:00](https://www.youtube.com/watch?v=ubMghRYqk8o&t=1200) - is actually more stressful than knowing exactly what's going to happen. This is also why we prefer getting bad news than waiting for an answer. This feels better. Now we know. Even if it's negative, we feel like we're in control. Why did our brains evolve to fear uncertainty? Our brains are wired to fear uncertainty. And that makes sense from an evolutionary perspective. When you think about the conditions in which our species started, the more information you had, the more likely you were to survive. A weird noise in the bushes or a new food that you'd never tried before, all of these could be lethal. So anytime you find yourself in a situation where you feel like you don't understand

- [00:20:30](https://www.youtube.com/watch?v=ubMghRYqk8o&t=1230) - the more information you had, the more likely you were to survive. A weird noise in the bushes or a new food that you'd never tried before, all of these could be lethal. So anytime you find yourself in a situation where you feel like you don't understand everything that's going on, where you feel like you don't have all of the information, your brain is trying to figure out what's going on. Your brain is trying to get to a solution. And obviously this was very useful in the jungle, but not so much in our modern environment. We try to get to an answer as quickly as possible, which means we sometimes go for the most obvious one rather than the most interesting one. It might even reduce our ability to connect with other people, because we'd better stay with people that we understand and people we know rather than trying to connect with strangers, because that could mean danger.

- [00:21:00](https://www.youtube.com/watch?v=ubMghRYqk8o&t=1260) - rather than the most interesting one. It might even reduce our ability to connect with other people, because we'd better stay with people that we understand and people we know rather than trying to connect with strangers, because that could mean danger. That means uncertainty. And that means fear and anxiety. How should we approach uncertainty instead? Instead of fearing uncertainty, we should learn to collaborate with it. What that looks like is seeing uncertainty as an opportunity for learning and for growth. When there is no uncertainty, when we know exactly what we're doing, that means we're not growing anymore. So we should actually seek uncertainty, embrace it, and every time we face it, saying,

- [00:21:30](https://www.youtube.com/watch?v=ubMghRYqk8o&t=1290) - What that looks like is seeing uncertainty as an opportunity for learning and for growth. When there is no uncertainty, when we know exactly what we're doing, that means we're not growing anymore. So we should actually seek uncertainty, embrace it, and every time we face it, saying, hello, welcome back, let's work together. All scientists know that real growth requires both trial and error. If you keep on repeating the same trial and everything works as expected, it means that you're not growing, you're not learning anything. You're just repeating the same thing that you know is working already. It's only through errors that you can adjust your path, that you can try new approaches, and that you can discover that some of your assumptions were wrong. And this is why all scientific experiments are just cycles of trial and error.

- [00:22:00](https://www.youtube.com/watch?v=ubMghRYqk8o&t=1320) - It's only through errors that you can adjust your path, that you can try new approaches, and that you can discover that some of your assumptions were wrong. And this is why all scientific experiments are just cycles of trial and error. And this is modeled over names, and this is how nature, this is also how nature evolves. This is how we can evolve. By making sure that we try new things, we make mistakes and we learn from them, this is how we can grow in our personal and professional lives. A linear model of success is based on a fixed outcome that we try to get to. It implies that first you do A, then B, then C, and then you'll be successful.

- [00:22:30](https://www.youtube.com/watch?v=ubMghRYqk8o&t=1350) - A linear model of success is based on a fixed outcome that we try to get to. It implies that first you do A, then B, then C, and then you'll be successful. There are lots of problems with a linear model of success. One of them is that it assumes that you know where you're going, which might not always be the case. Another one is the assumption that wherever you want to go right now is where you will want to go in a few years from now. Things are changing very fast. Our world is evolving. And you should allow yourself to change the direction of your ambitions with the world, as the world changes. Another problem is that linear goals breed toxic productivity and unhealthy comparison.

- [00:23:00](https://www.youtube.com/watch?v=ubMghRYqk8o&t=1380) - And you should allow yourself to change the direction of your ambitions with the world, as the world changes. Another problem is that linear goals breed toxic productivity and unhealthy comparison. When we're all climbing similar ladders and pyros, we might compare ourselves to each other, looking to the right and to the left and asking, is this person being faster? Is this person working harder? Is this person being more successful? This creates toxic productivity when we overwork ourselves just trying to climb those ladders as quickly as possible. This can create a sense of ambivalence toward goals in the sense that it feels so exhausting to keep on climbing this ladder

- [00:23:30](https://www.youtube.com/watch?v=ubMghRYqk8o&t=1410) - just trying to climb those ladders as quickly as possible. This can create a sense of ambivalence toward goals in the sense that it feels so exhausting to keep on climbing this ladder with no certainty at all that we might get to the top or that we might get there fast enough. And having this exhaustion from constantly comparing ourselves to each other, that for some people, the new mantra has become, no goals, just vibes. How can we go from linear success to fluid experimentation? To break free from this goal-obsessed life, we need to go from rigid linearity to fluid experimentation. That means letting go of the focus on the outcome

- [00:24:00](https://www.youtube.com/watch?v=ubMghRYqk8o&t=1440) - How can we go from linear success to fluid experimentation? To break free from this goal-obsessed life, we need to go from rigid linearity to fluid experimentation. That means letting go of the focus on the outcome and instead embracing the joy that we can find in the process. Instead of focusing on climbing the ladder, that means instead trying to design cycles of experimentation. And instead of focusing on certainty, this is all about focusing on curiosity. Breaking free from this goal-obsessed life and developing an experimental mindset is all about going from outcomes to processes, from ladders to growth loops, and from certainty to curiosity.

- [00:24:30](https://www.youtube.com/watch?v=ubMghRYqk8o&t=1470) - Breaking free from this goal-obsessed life and developing an experimental mindset is all about going from outcomes to processes, from ladders to growth loops, and from certainty to curiosity. How can labeling emotions help manage uncertainty? Affective labeling means labeling your emotions. It really just means putting words to feelings. And it's incredibly helpful because it allows you to better connect and understand your emotions so you can manage them better. Affective labeling allows us to reduce activity in the amygdala, which is involved in unconscious emotional processing, and to increase activity in the prefrontal cortex,

- [00:25:00](https://www.youtube.com/watch?v=ubMghRYqk8o&t=1500) - and understand your emotions so you can manage them better. Affective labeling allows us to reduce activity in the amygdala, which is involved in unconscious emotional processing, and to increase activity in the prefrontal cortex, which is in the prefrontal cortex. And it's also involved in rational thinking. And that allows us to better manage our emotions. What's great about affective labeling is that it's very simple, doesn't cost anything, and you can do it straight away whenever you want to better regulate your emotions. It's really just about picking a word to describe your current emotion. And if you can't find the right word, there is research showing that describing a landscape is also a great way to practice affective labeling. So you could say, for example, that it's a stormy day

- [00:25:30](https://www.youtube.com/watch?v=ubMghRYqk8o&t=1530) - And if you can't find the right word, there is research showing that describing a landscape is also a great way to practice affective labeling. So you could say, for example, that it's a stormy day over a dark forest. Or you could say that it's a scary cliff over a beautiful sea. That's a great way for you to describe your emotions where you cannot find just one word that captures exactly what you're feeling. And that's really a way to describe your emotional landscape. Whenever we face a disruption in our life, it's very tempting to try and immediately solve the objective consequences, the actual problems that arise from that disruption. And what we do when we do that is that we completely ignore

- [00:26:00](https://www.youtube.com/watch?v=ubMghRYqk8o&t=1560) - it's very tempting to try and immediately solve the objective consequences, the actual problems that arise from that disruption. And what we do when we do that is that we completely ignore the emotional experience of that disruption. Affective labeling is really about making space for that emotional processing before you deal with the objective consequences. The reason why it's important is because, first, this is going to be better for your mental health in the long term. By processing these emotions as you go through them, rather than ignoring them, you're going to be more connected to yourself and you're going to have better mental health in the process. Second, this is also going to help you better solve the actual problem,

- [00:26:30](https://www.youtube.com/watch?v=ubMghRYqk8o&t=1590) - By processing these emotions as you go through them, rather than ignoring them, you're going to be more connected to yourself and you're going to have better mental health in the process. Second, this is also going to help you better solve the actual problem, when your mind is clear, when you have processed the emotions, you will be able to think about the actual consequences in a more efficient way. This practice is particularly useful for people who like to feel like they're in control. And where instead of connecting with our emotion, we hide it underneath something else. That might be hiding it underneath a more manageable emotion on the surface that we're more familiar with. So, for example, if we're sad, we're just going to say that we're a little bit anxious.

- [00:27:00](https://www.youtube.com/watch?v=ubMghRYqk8o&t=1620) - we hide it underneath something else. That might be hiding it underneath a more manageable emotion on the surface that we're more familiar with. So, for example, if we're sad, we're just going to say that we're a little bit anxious. We're a little bit angry about this, a little bit disappointed when really the emotion is sadness. Or sometimes that might look like hiding the emotion under thinking, logic, the rational brain, where we feel like we are looking for a solution, we're being proactive, we're dealing with the problem and everything is fine. We're not feeling anything too complicated or unmanageable here. Why do humans struggle with transitional periods? We struggle with liminal spaces and transitional periods

- [00:27:30](https://www.youtube.com/watch?v=ubMghRYqk8o&t=1650) - We're not feeling anything too complicated or unmanageable here. Why do humans struggle with transitional periods? We struggle with liminal spaces and transitional periods because our brain really likes to be able to categorize situations as safe or dangerous as quickly as possible. And when we can't do that, this feels very uncomfortable. We want to get out of this transitional space. We want to get back to safety. We want to get back to this sense of clarity. Imagine that you're on a plane 30,000 feet up in the sky with no Wi-Fi. There are two different responses that you can make. The first response, response one, is to feel anxious.

- [00:28:00](https://www.youtube.com/watch?v=ubMghRYqk8o&t=1680) - We want to get back to this sense of clarity. Imagine that you're on a plane 30,000 feet up in the sky with no Wi-Fi. There are two different responses that you can make. The first response, response one, is to feel anxious. And because of that, you might try to sleep the entire time, just ignore that you're on that plane. You might drink alcohol to dull some of the anxiety. And in general, you just want it to be over as quickly as possible. Then there's response two, where actually you feel like, this is great, no Wi-Fi, nobody to contact me. This is freedom. And you might decide to watch a movie that you've been meaning to watch for a long time. And you might decide to watch a movie that you've been meaning to watch for a long time. Maybe crack a book open that you wanted to read.

- [00:28:30](https://www.youtube.com/watch?v=ubMghRYqk8o&t=1710) - this is great, no Wi-Fi, nobody to contact me. This is freedom. And you might decide to watch a movie that you've been meaning to watch for a long time. And you might decide to watch a movie that you've been meaning to watch for a long time. Maybe crack a book open that you wanted to read. Maybe journal or just let your mind wander. And really enjoying that space of transition, that space that is outside of your daily routine, outside of the sense of certainty that you normally have as you go about your daily life. There's a famous quote that has been attributed to a lot of different psychologists, including Viktor Frankl. And that says that our freedom lies within the gap between stimulus and response. What that means is that whenever we find ourselves in a situation

- [00:29:00](https://www.youtube.com/watch?v=ubMghRYqk8o&t=1740) - psychologists, including Viktor Frankl. And that says that our freedom lies within the gap between stimulus and response. What that means is that whenever we find ourselves in a situation or whenever we face a trigger, there is a little gap between that trigger and our response. And in that gap lies the freedom to make a choice. Are we going to go for the automatic response, the one we have no control over, or are we going to pause and ask ourselves, how do I want to respond to this situation? Every time we use that freedom, every time we pause and we ask ourselves, what do I actually want to do? We prove to ourselves that we have agency, that we are able to make our

- [00:29:30](https://www.youtube.com/watch?v=ubMghRYqk8o&t=1770) - Every time we use that freedom, every time we pause and we ask ourselves, what do I actually want to do? We prove to ourselves that we have agency, that we are able to make our own choices. And that doesn't mean this is always the correct choice. In many cases, there is no such thing as making the right choice because we don't have all of the information and things are changing so fast. But at least we know that we don't have to go for automatic response. And ultimately, that means living a life not of control but of agency, experimentation, and exploration. This allows us to build the confidence in our ability to change the world as it changes around us.

- [00:30:00](https://www.youtube.com/watch?v=ubMghRYqk8o&t=1800) - And ultimately, that means living a life not of control but of agency, experimentation, and exploration. This allows us to build the confidence in our ability to change the world as it changes around us. When you ask happy people how they discovered their passion and if they give you an honest answer, they'll tell you they stumbled upon it. What we can learn from that is that finding your passion, finding your purpose in life is not really about seeking it, obsessing over it, or applying a plan where you're going to change. It's not about going to the gym and trying to figure out what it is. But instead, it's about following your curiosity, experimenting,

- [00:30:30](https://www.youtube.com/watch?v=ubMghRYqk8o&t=1830) - purpose in life is not really about seeking it, obsessing over it, or applying a plan where you're going to change. It's not about going to the gym and trying to figure out what it is. But instead, it's about following your curiosity, experimenting, exploring, trying new things, and trusting that you will figure out what is the thing that makes you excited to wake up in the morning. What is a cognitive script? A cognitive script is an internalized behavioral pattern that tells us how we're supposed or at least how we think we're supposed to act in certain situations. They can be very helpful for routine. They can be very helpful for routine. They can be very helpful for routine tasks and decisions in everyday life. Cognitive scripts were first discovered in a seminal 1979 study where

- [00:31:00](https://www.youtube.com/watch?v=ubMghRYqk8o&t=1860) - we're supposed or at least how we think we're supposed to act in certain situations. They can be very helpful for routine. They can be very helpful for routine. They can be very helpful for routine tasks and decisions in everyday life. Cognitive scripts were first discovered in a seminal 1979 study where researchers found that people are following very similar scripts in similar situations such as going to the doctor or going to the restaurant. And since then, they have found those cognitive scripts in all areas of your life. For routine everyday decisions, cognitive scripts are actually very practical and useful. For example, you know that when you're going to go to the doctor, you're supposed to wait in the waiting room and then someone is going to call your name and then you're going to go into the doctor's office and this is where

- [00:31:30](https://www.youtube.com/watch?v=ubMghRYqk8o&t=1890) - For routine everyday decisions, cognitive scripts are actually very practical and useful. For example, you know that when you're going to go to the doctor, you're supposed to wait in the waiting room and then someone is going to call your name and then you're going to go into the doctor's office and this is where you're going to start telling them about whatever the issue is, right? And you know in which order you're supposed to do all of those different actions. Great, useful, and it's a pretty good thing that you don't have to ever think it every time you go to the doctor. The problem with cognitive scripts is when we use them to make more important decisions in our lives, in our careers, in our relationships, instead of asking ourselves, is that really what I want to do? Is that my decision? We let our choices being driven by those stories that we have internalized,

- [00:32:00](https://www.youtube.com/watch?v=ubMghRYqk8o&t=1920) - decisions in our lives, in our careers, in our relationships, instead of asking ourselves, is that really what I want to do? Is that my decision? We let our choices being driven by those stories that we have internalized, by those scripts that tell us how we're supposed to behave in a certain situation. What is the sequel script? One of these scripts is the sequel script. That's the script where we feel like we've always behaved in a certain way, so we're going to keep on behaving in the same way. We feel like the narrative needs to make sense. This is the script that makes people choose careers that are aligned with whatever they studied at university.

- [00:32:30](https://www.youtube.com/watch?v=ubMghRYqk8o&t=1950) - so we're going to keep on behaving in the same way. We feel like the narrative needs to make sense. This is the script that makes people choose careers that are aligned with whatever they studied at university. That is the script that makes people keep on dating the same kind of people they've been dating before. And this is, in general, the script that makes us repeat the exact same behaviors and patterns that we've had in the past. In relationships in particular, what's very interesting is that we want the sequel to connect to whatever the previous experience was. So that might mean dating the exact same type of person or choosing the next person. We want to pick the next person in response to whoever we were dating before. And they might look like the complete opposite, but the truth is we still pick

- [00:33:00](https://www.youtube.com/watch?v=ubMghRYqk8o&t=1980) - So that might mean dating the exact same type of person or choosing the next person. We want to pick the next person in response to whoever we were dating before. And they might look like the complete opposite, but the truth is we still pick this person based on a sense of continuity with whatever the previous experience was before. With the sequel script, it's quite obvious why it limits the possibilities that we might explore in life. Because we feel like whatever decision we're making next needs to make sense in relation to the decisions we made in the past, we ignore a lot of more left-field, unexpected things. And we're not going to be able to make the same decisions again and again. So we're going to be able to make more of those unexpected, unexpected

- [00:33:30](https://www.youtube.com/watch?v=ubMghRYqk8o&t=2010) - relation to the decisions we made in the past, we ignore a lot of more left-field, unexpected things. And we're not going to be able to make the same decisions again and again. So we're going to be able to make more of those unexpected, unexpected decisions that might be opportunities for growth and exploration and self-discovery. What is the crowd pleaser script? Another cognitive script that rules our life is the crowd pleaser script. This is the script where we make decisions based on whatever is going to please people around us the most. Quite often, those are the people who are our parents. And we're going to be able to make decisions based on what we feel like we're safe and successful. But the audience for the crowd pleaser script can also be your friends, can be your

- [00:34:00](https://www.youtube.com/watch?v=ubMghRYqk8o&t=2040) - people around us the most. Quite often, those are the people who are our parents. And we're going to be able to make decisions based on what we feel like we're safe and successful. But the audience for the crowd pleaser script can also be your friends, can be your partner, can be your colleagues. And what you don't realize when you follow the crowd pleaser script is that you're not making decisions based on what you want and what would make you happy, but based on what will make others around you happy. What is the epic script? Finally, there's the epic script. And this one is very insidious because it's actually celebrated in our society. It's the script that says that whatever you do, it needs to be big.

- [00:34:30](https://www.youtube.com/watch?v=ubMghRYqk8o&t=2070) - What is the epic script? Finally, there's the epic script. And this one is very insidious because it's actually celebrated in our society. It's the script that says that whatever you do, it needs to be big. It needs to be very ambitious. It needs to be impactful. Anything less than that is failure. The epic script is an extreme version of the idea of following your dreams. And because of that, it has created a form of stigma around having a small, simple goal. A small, simple life. A life that is focused on just being happy in the moment, being present, being connected and exploring your curiosity. Because if you don't have those external signs of success, if you're not following

- [00:35:00](https://www.youtube.com/watch?v=ubMghRYqk8o&t=2100) - A small, simple life. A life that is focused on just being happy in the moment, being present, being connected and exploring your curiosity. Because if you don't have those external signs of success, if you're not following your grand passion, then are you really living a meaningful life? This is the anxiety inducing question that is created by the epic script. When you think about it, this is a very myopic definition of success where we try to put all of our eggs in the same basket. We choose this one thing and we say, if I succeed to this, then I'm successful in life. The problem with this is that if we fail at this particular project, this particular goal, we feel like we have failed at life entirely.

- [00:35:30](https://www.youtube.com/watch?v=ubMghRYqk8o&t=2130) - We choose this one thing and we say, if I succeed to this, then I'm successful in life. The problem with this is that if we fail at this particular project, this particular goal, we feel like we have failed at life entirely. And the other problem with putting all of our eggs in the same basket is that then sometimes the basket just becomes too heavy and we drop it altogether. Our modern hyper-connected online world has made the epic script unfortunately very, very popular with people. We have become overly obsessed with it. We have become overly obsessed with finding our purpose. Mentions in books of the phrase, find your purpose, have surged 700% in the past two decades only. We see all of those stories of success, of people who have found their passion and who

- [00:36:00](https://www.youtube.com/watch?v=ubMghRYqk8o&t=2160) - We have become overly obsessed with finding our purpose. Mentions in books of the phrase, find your purpose, have surged 700% in the past two decades only. We see all of those stories of success, of people who have found their passion and who are very happy as a result of this. We see the entrepreneur that followed their passion and was very successful. But we don't see all of the thousands of other ones that have tried the exact same thing and failed. This is called survivorship bias. And it's very unfortunate how nowadays we are basing all of our decisions and even our self-worth based on that incomplete information. What should we do when we notice we are following a cognitive script?

- [00:36:30](https://www.youtube.com/watch?v=ubMghRYqk8o&t=2190) - And it's very unfortunate how nowadays we are basing all of our decisions and even our self-worth based on that incomplete information. What should we do when we notice we are following a cognitive script? Once you have identified the cognitive scripts that rule your life, you can actually break free from them. And it really starts by seeing them as stories we tell ourselves rather than truth that we need to follow blindly. A really good way to do that is to look at the stories that we tell ourselves. A really good way to do this is to stop for a second every time you hear yourself saying, I should do this. This word should is actually a really good signal that there might be a cognitive script at play somewhere here. And once you've identified the places in your life and the situations in your life where you're using that word should,

- [00:37:00](https://www.youtube.com/watch?v=ubMghRYqk8o&t=2220) - A really good way to do this is to stop for a second every time you hear yourself saying, I should do this. This word should is actually a really good signal that there might be a cognitive script at play somewhere here. And once you've identified the places in your life and the situations in your life where you're using that word should, you can then decide to replace that word with another word, might. What might I want to do instead of what should I do? What might I want to explore? What might I want to experiment with? If you want to start writing your own scripts in life, there are three questions that you can ask yourself while designing your next experiment. The first one is, am I following my past or discovering my path? The second one is, am I following the crowd or am I discovering my tribe?

- [00:37:30](https://www.youtube.com/watch?v=ubMghRYqk8o&t=2250) - If you want to start writing your own scripts in life, there are three questions that you can ask yourself while designing your next experiment. The first one is, am I following my past or discovering my path? The second one is, am I following the crowd or am I discovering my tribe? And then finally, am I following my passion or am I discovering my curiosity? Those three questions together allow you to embrace the liminal space. We're all in. To make friends with uncertainty, to start experimenting more, and very importantly, to deal with those three powerful cognitive scripts that underlie a lot of our decisions on an unconscious level. Part four, in defense of procrastination.

- [00:38:00](https://www.youtube.com/watch?v=ubMghRYqk8o&t=2280) - To make friends with uncertainty, to start experimenting more, and very importantly, to deal with those three powerful cognitive scripts that underlie a lot of our decisions on an unconscious level. Part four, in defense of procrastination. Why does procrastination have a negative connotation? Procrastination has become a little bit of a dirty word. And that's because we're not doing it right. And that's because we have just been through hundreds of years of moralization around productivity and work. Being productive means that you are a good, useful, helpful contributor to society. In a way, we have all agreed to tie our self-worth to our productivity. And so not being productive means that you're being lazy, you're not contributing, you're not being useful and helpful to society.

- [00:38:30](https://www.youtube.com/watch?v=ubMghRYqk8o&t=2310) - Being productive means that you are a good, useful, helpful contributor to society. In a way, we have all agreed to tie our self-worth to our productivity. And so not being productive means that you're being lazy, you're not contributing, you're not being useful and helpful to society. Because of the efficiency worship that we have developed in our industrial age, we are now seeing procrastination as a character flaw rather than what it is, a signal that is worth listening to. This has propped up an entire industry of productivity, whether it's online courses telling you how to manage your life and your time and how to make the most of every single minute. Templates for you to track all of your tasks.

- [00:39:00](https://www.youtube.com/watch?v=ubMghRYqk8o&t=2340) - This has propped up an entire industry of productivity, whether it's online courses telling you how to manage your life and your time and how to make the most of every single minute. Templates for you to track all of your tasks. Wearables to tell you whether you've been using your time in the right time and making sure that you're not missing any notifications. Calendars that are ruling the way we spend our time. So even if at a personal level you don't feel like productivity is the most important thing you want to optimize for, it's very hard to resist the sirens of our society telling you, you must be more productive. As a result of this society that keeps on telling us that we need to be productive to be a good person, whenever we find ourselves procrastinating, we just try to ignore it.

- [00:39:30](https://www.youtube.com/watch?v=ubMghRYqk8o&t=2370) - it's very hard to resist the sirens of our society telling you, you must be more productive. As a result of this society that keeps on telling us that we need to be productive to be a good person, whenever we find ourselves procrastinating, we just try to ignore it. We push through using our willpower and we feel self-blame and self-judgment. Instead, there's a very simple tool that you can use whenever you're in a situation of need. A tool that is based on self-discovery, on thinking like a scientist and trying to understand what those signals are. This tool is called the triple check and it's about asking yourself, why am I procrastinating? Is it coming from the head, from the heart, or from the hand? If it's coming from the head, it means that at a rational level, you're not fully convinced that you should be working on that task in the first place.

- [00:40:00](https://www.youtube.com/watch?v=ubMghRYqk8o&t=2400) - A tool that is based on self-discovery, on thinking like a scientist and trying to understand what those signals are. This tool is called the triple check and it's about asking yourself, why am I procrastinating? Is it coming from the head, from the heart, or from the hand? If it's coming from the head, it means that at a rational level, you're not fully convinced that you should be working on that task in the first place. If it's coming from the heart, it means that at an emotional level, you don't think this is going to be quite fun or enjoyable to work on. And if it's coming from the hand, that means that even though at a rational level, you feel like, I should be working on this. At an emotional level, you feel like, that looks like fun. At a practical level, you feel like you're not equipped with the right tools or you don't have the right resources in order to get the job done.

- [00:40:30](https://www.youtube.com/watch?v=ubMghRYqk8o&t=2430) - And if it's coming from the hand, that means that even though at a rational level, you feel like, I should be working on this. At an emotional level, you feel like, that looks like fun. At a practical level, you feel like you're not equipped with the right tools or you don't have the right resources in order to get the job done. How can the triple check inform what we do next? What's great about the triple check is that it's not just about the work that we do. It's about the work that we do. What's great about the triple check tool is that it's not just a tool for a diagnosis. It also tells you what to do for each of those situations. So if the problem is rational, coming from the head, it means that you might want to redefine the strategy. Maybe reach out to your colleagues and tell them, hey, I'm not quite convinced this is the way we should go about this. Do you want to go and brainstorm together and see if there's a better approach that we could use?

- [00:41:00](https://www.youtube.com/watch?v=ubMghRYqk8o&t=2460) - So if the problem is rational, coming from the head, it means that you might want to redefine the strategy. Maybe reach out to your colleagues and tell them, hey, I'm not quite convinced this is the way we should go about this. Do you want to go and brainstorm together and see if there's a better approach that we could use? If the problem is emotional, coming from the heart, you might want to redesign the experience so it's more fun. That could look like going to your favorite coffee shop or grabbing a colleague and say, hey, let's do a little bit of co-working while I work on this task. And if the problem is practical, where you feel like you don't have the right tools or resources or skills, then raise that hand. Ask for help. Tell people around you, the people you're working with, that maybe you need a bit of mentoring or coaching or you need to take a course in order to be able to do that task.

- [00:41:30](https://www.youtube.com/watch?v=ubMghRYqk8o&t=2490) - And if the problem is practical, where you feel like you don't have the right tools or resources or skills, then raise that hand. Ask for help. Tell people around you, the people you're working with, that maybe you need a bit of mentoring or coaching or you need to take a course in order to be able to do that task. Sometimes you'll go through the triple check. Head, heart, hand. And everything fills in alignment and still you're procrastinating. In this case, it might be worth looking outside of yourself for systemic barriers. And that means having honest, sometimes difficult conversations with other stakeholders, redesigning your environment if it is not conducive to you being focused and productive, or sometimes completely removing yourself from that work environment and doing something else because you might not be able to change that situation every time.

- [00:42:00](https://www.youtube.com/watch?v=ubMghRYqk8o&t=2520) - And that means having honest, sometimes difficult conversations with other stakeholders, redesigning your environment if it is not conducive to you being focused and productive, or sometimes completely removing yourself from that work environment and doing something else because you might not be able to change that situation every time. What are metrics? What are your metrics? And what's the Magic Windows? Your Magic Windows are those moments of high productivity, creativity, and focus where you feel like there is zero effort involved. Time is flowing, and your attention is completely locked onto the task that you're working on. Whether you've noticed it or not, we have all gone through those kinds of Magic Windows in our lives.

- [00:42:30](https://www.youtube.com/watch?v=ubMghRYqk8o&t=2550) - Your Magic Windows are those moments of high productivity, creativity, and focus where you feel like there is zero effort involved. Time is flowing, and your attention is completely locked onto the task that you're working on. Whether you've noticed it or not, we have all gone through those kinds of Magic Windows in our lives. Those are the moments when you look at the time and you feel like where did it go? What happened? Maybe you were lost in a conversation with a friend. Maybe you were working on a creative task. Maybe you were taking a walk. Those are your magic windows. And what's amazing is that once you learn to identify them, you can start becoming a bit more intentional and opening those magic windows at will during your daily life. What is mindful productivity?

- [00:43:00](https://www.youtube.com/watch?v=ubMghRYqk8o&t=2580) - And what's amazing is that once you learn to identify them, you can start becoming a bit more intentional and opening those magic windows at will during your daily life. What is mindful productivity? You might think that mindfulness and productivity are kind of antithetic and don't really belong together. But when you go back to the very definition of mindfulness, it is really about paying attention to your experience in the present moment without self-blame or self-judgment. Mindful productivity is about cultivating this awareness in the way you work and direct your focus. Mindful productivity is really about answering three key questions. When is my magic window?

- [00:43:30](https://www.youtube.com/watch?v=ubMghRYqk8o&t=2610) - Mindful productivity is about cultivating this awareness in the way you work and direct your focus. Mindful productivity is really about answering three key questions. When is my magic window? What belongs in this window? And how can I keep that window open? What is mindful productivity's most valuable resource? The traditional definition of productivity only focuses on time as the most important resource. And the assumption here is that every minute is a little box that you need to fill with as much stuff as possible in order to be productive. With mindful productivity, you consider other very important things,

- [00:44:00](https://www.youtube.com/watch?v=ubMghRYqk8o&t=2640) - And the assumption here is that every minute is a little box that you need to fill with as much stuff as possible in order to be productive. With mindful productivity, you consider other very important things, like the way you think about yourself, and the way you think about yourself. And you can think about what you think about yourself. And you can think about what you think about yourself. You can think about your own resources, your physical resources, your emotional resources, and your cognitive resources. And that really means thinking about your emotions, your energy, and your executive function. To practice mindful productivity requires to change your perspective as to what the most important resource is, from time to energy. How does managing emotions influence productivity? We have a very negative relationship with procrastination.

- [00:44:30](https://www.youtube.com/watch?v=ubMghRYqk8o&t=2670) - from time to energy. How does managing emotions influence productivity? We have a very negative relationship with procrastination. If you go online trying to find solutions as to how to deal with procrastination, you're going to only find articles that tell you how to beat procrastination. Very violent. But instead, if we start seeing it as a useful signal, it allows us to better connect with the emotions that are around that procrastination, and to understand that this is really just our brain trying to send us a signal. By reconnecting with these emotions instead of trying to ignore them, not only are we going to understand ourselves better,

- [00:45:00](https://www.youtube.com/watch?v=ubMghRYqk8o&t=2700) - and to understand that this is really just our brain trying to send us a signal. By reconnecting with these emotions instead of trying to ignore them, not only are we going to understand ourselves better, but also because of them, we're going to be able to understand ourselves better, but also because of our emotions. better understand our relationship to work, and become more productive in the process. What does death by two arrows mean? In Buddhist philosophy, there is a concept called death by two arrows that tries to explain some of the sources of human suffering. So the way it works is that whenever we experience a difficult emotion, that's the first arrow. So in the case of procrastination, when we procrastinate, that's the first arrow. But then there's a second arrow,

- [00:45:30](https://www.youtube.com/watch?v=ubMghRYqk8o&t=2730) - So the way it works is that whenever we experience a difficult emotion, that's the first arrow. So in the case of procrastination, when we procrastinate, that's the first arrow. But then there's a second arrow, which is the shame and the self-blame that we experience because of the first experience. What's very important to know is that that second arrow is completely optional. We don't have to add a second layer of suffering to the difficulty and the challenges that we're facing in the first place. What's the hardest part of knowing what to do next? The hardest part of knowing what to do next is often to get started. And the reason why is because we focus on the outcome.

- [00:46:00](https://www.youtube.com/watch?v=ubMghRYqk8o&t=2760) - What's the hardest part of knowing what to do next? The hardest part of knowing what to do next is often to get started. And the reason why is because we focus on the outcome. We focus on the outcome rather than the process. By not focusing on the outcome and instead designing a tiny experiment, what you can do is that letting go of any definition of success, letting go of that binary result that you're looking for, and instead focusing on a research question, a hypothesis that you might have, something that makes you feel curious and that you want to explore. Whatever the outcome, whatever the result, if you learn something new, that's progress.

- [00:46:30](https://www.youtube.com/watch?v=ubMghRYqk8o&t=2790) - a hypothesis that you might have, something that makes you feel curious and that you want to explore. Whatever the outcome, whatever the result, if you learn something new, that's progress. How can we practice self-anthropology? A great way to start reimagining what your life could look like is to practice what I call self-anthropology. Becoming an anthropologist with your life as your topic of study. Just imagine like an anthropologist that goes somewhere and studies a new culture. They know nothing about this culture. Everything is new to them. So what do they do? They take their notebook out and they start taking field notes. They ask questions like, why are people doing things?

- [00:47:00](https://www.youtube.com/watch?v=ubMghRYqk8o&t=2820) - They know nothing about this culture. Everything is new to them. So what do they do? They take their notebook out and they start taking field notes. They ask questions like, why are people doing things? The way they're doing them? Why do they care about this? Why is this thing so important to them? You can do the same thing with your life. As a little exercise, you can say that for 24 hours, you're going to treat your life as if you were an anthropologist. Take a notebook or do it on your phone and start taking little notes. Observe what gives you energy and what drains your energy. The conversations that you enjoy having. The projects that you like working on. Just by doing this, you are going to start questioning a lot of your life. You are going to start questioning a lot of your life.

- [00:47:30](https://www.youtube.com/watch?v=ubMghRYqk8o&t=2850) - Observe what gives you energy and what drains your energy. The conversations that you enjoy having. The projects that you like working on. Just by doing this, you are going to start questioning a lot of your life. You are going to start questioning a lot of your life. You are going to start questioning a lot of your life. You are going to start questioning a lot of your scripts, a lot of your assumptions, a lot of your goals. And you are going to open a window for experimentation. There's no fixed rule when it comes to what you can include in your field notes. It should really be guided by your curiosity. But here are some ideas for inspiration. You could include some insights that you have during the day. After reading something or listening to something, you can capture your mood. You could capture your energy levels. Or you could even capture the result of some encounters and conversations with other people.

- [00:48:00](https://www.youtube.com/watch?v=ubMghRYqk8o&t=2880) - After reading something or listening to something, you can capture your mood. You could capture your energy levels. Or you could even capture the result of some encounters and conversations with other people. Just like a scientist, you need to first start with observation. And it can be really tempting when we're a doer and we like to get things done, to jump straight into designing the experiment. But it's important to have that phase of observation. Let's say that you observe that you get a lot of energy from giving presentations at work. But also a little bit of anxiety. Then in the second step, you need to formulate a hypothesis. Maybe you would benefit from taking public speaking classes.

- [00:48:30](https://www.youtube.com/watch?v=ubMghRYqk8o&t=2910) - Let's say that you observe that you get a lot of energy from giving presentations at work. But also a little bit of anxiety. Then in the second step, you need to formulate a hypothesis. Maybe you would benefit from taking public speaking classes. Maybe you would benefit from being coached. Or maybe you would benefit from just practicing more and giving more presentations at work. The great thing is that you can just pick one of those hypotheses and start testing it. The other ones will still be there if you want to try them later. So you pick one of them. And that's the last part where you design a tiny experiment. And you say, I'm going to commit to this action for this duration. In this case, for example, you could say, I'm going to commit to giving a presentation.

- [00:49:00](https://www.youtube.com/watch?v=ubMghRYqk8o&t=2940) - And that's the last part where you design a tiny experiment. And you say, I'm going to commit to this action for this duration. In this case, for example, you could say, I'm going to commit to giving a presentation. Every two weeks at work for one quarter. And at the end of the quarter, I will look at the data and I will see how I felt at an internal level. But also the signals at an external level. Did my colleagues enjoy it? Did that bring me new projects? Was that something that was valued inside of the company? Based on those external and internal signals, I can decide whether I want to keep going, stop, or if I want to tweak my experiment for the next cycle.

- [00:49:30](https://www.youtube.com/watch?v=ubMghRYqk8o&t=2970) - Based on those external and internal signals, I can decide whether I want to keep going, stop, or if I want to tweak my experiment for the next cycle. Want to support the channel? Join the Big Think members community where you get access to videos early, ad-free.

