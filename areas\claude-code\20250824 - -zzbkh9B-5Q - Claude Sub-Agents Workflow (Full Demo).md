---
title: <PERSON> Sub-Agents Workflow (Full Demo)
artist: <PERSON>
date: 2025-08-24
url: https://www.youtube.com/watch?v=-zzbkh9B-5Q
---

(t: 0) Instead of one overworked AI, imagine having a whole team of specialized agents working together. Dedicated code reviewers, designers, test writers, documentation agents, all vibing together. (t: 10) So check this out. Join me as we explore Anthropix's new sub-agent workflows that have just been announced. So this is actually perfect for those who are beginners or those who are (t: 20) experienced developers as well. I'm a solo engineer and I'm trying to use AI and I'm always not in the code all the time. I'm pretty much going to be following this guide to get us (t: 30) started. From what I've been able to see right now is that like these sub-agents can be either called if you tell them like specifically in the chat or Anthropix as it's going through things, (t: 40) it can figure out that saying, oh, I should use a sub-agent for this. Here's one that <PERSON> already has for design review. The other positive is that it actually has its own context window. So what (t: 50) that means is if you have like 200k tokens for a context window, once you reach about 50%, you'll start to get a lot of degradation. In your actual outputs. So you want to always either start new chats or start new conversations (t: 60) or try to like separate your concerns. Now we can separate our concerns over these different sub-agents, a code reviewer, someone who's going to do a design review. Someone's going to order (t: 70) me a pizza. And those types of things are going to have their own context window to basically think about like a big old room. You can be able to stretch out and you're going to be able to do (t: 80) more things. And that's basically what they're going to be able to do on our behalf. Getting into the Anthropix agents thing here, I'm going to say create a new agent. So we're just going to open up cloud code and I'm just (t: 90) going to create a new session here. If I type in slash agent, we should be able to see manage or configure agents. This is really cool. So it's going to allow us to now create a new agent, (t: 100) create a specialized sub-agents that cloud can delegate to each agent has its own context window, custom system prompt, specific details. So try creating a code reviewer, a code simplifier, (t: 110) a tech lead or UX reviewer. So we're going to do one that's basically like a UX reviewer based off my guidelines here. Okay. So I'm going to say create new agent. Okay. And so we have a couple of options. Looks like you can choose either to have it in the (t: 120) project or for personal. I'm going to go ahead and select project. I like having maybe project specific agents. The design one is something that I use in all of my projects, new agent creation (t: 130) methods. So generate with cloud recommended or manual configuration in the actual documentation. Cloud says that if you want to create your own agent, you would actually kind of follow this (t: 140) file format. So you can easily give this to cloud code and have it generate more sub-agents. Describe what the agent should be do when it should be used, be comprehensive for the best (t: 150) results. I want this to be an expert designer. That's going to go through and use some design principles. Some of the design principles will involve like color rules, typography systems, (t: 160) clean visual structure, and try to make sure that whatever code was generated follows our (t: 170) foundation of the tailwind V4 integration. I'm just curious to see how cloud is going to generate a type of description for us and see if it actually expands upon what I said here. Cause that'd be (t: 180) really interesting, right? Cause a lot of the times he's generating agent configuration. So I think it actually is using some type of model, probably Opus or probably, you know, sonnet to generate (t: 190) an agent based off of these instructions. So I'm a really, really big fan of having AI models, (t: 200) write instructions for themselves. And I think that's what it's doing here. And it's really, really cool to see. So the name is the design system enforcer. Okay. Okay. Use this agent when you need to review or (t: 210) refactor UI code to ensure it adheres to the established design principles, ensuring color harmony, typography, consistency, and the clean visual structure. Yo, I'm not going to lie. (t: 220) This is very hot for a couple of reasons. One, I sort of described in plain English what I was thinking about, but then it kind of took that and added it a little bit further. I'm wanting to (t: 230) learn here and see how these like sub agents are created. So that way you can learn what most pathways of some of these functions are designed to be bike and ski bike support functions, non aerobics and bot skills. Some of these, I can't really (t: 240) call them tools and jugarcs, only sometimes they're just part of like footprint of them and so I'm thinking like a bunch of the, all these good things that maybe even others have prove are like top end tools, build-ara advanced tools, like Being More . Like I've seen quite a number of models that caninity, fueronty platform, and then (t: 250) specifically be able to build on the web or insane social enlightenment tool right there. So I think it's going ahead and willing to build on these two, but it should be so for anyone of you guys. You got these low end quad file, a lot of them are super large and simple to copy and paste into lots of different parts of your정 offer. And you can get a (t: 260) with the actual doc and this is the documentation. (t: 263) Bruh, wait, wait, wait, wait, wait. Did it know? Hold up. (t: 270) It knows about like the typography system. So somehow it had got context in my code base about these rules. Cause literally these were in my design format (t: 280) and it put them in here. I don't know what type of drugs you're smoking, anthropic, but damn you're good. (t: 290) Like it went through my code base and somehow probably found that file about my design reviews and these key terms. And it slid them right into its own agent. Be like, yo, I got you fam. (t: 300) This is how I'm going to be a solo engineer with a team of agents. Oh my God, this is too good, man. Wow, wow, wow. Look at the way it describes even like the type system (t: 310) and like the visual structure. I didn't give it the eight point grid system. Okay, now it has two agents. It has a design system enforcer and then the built-in general agent. So it's got the agents which are always available. And then now we can go through and create a new agent. (t: 320) If we want to go ahead, we can go back or enter to escape. Man, this is crazy. So now that I have this enforcer, I'm just going to go ahead and check this into my code. (t: 330) I want to see if maybe we can even fix some bugs and see if the agent either, it should kick off. It should just basically figure out the design system. (t: 340) I mean, so these agents can also call MCPs and they can also call specific tools as we can see there. Explicit invocation. So to request a specific call, you can call a specific sub-agent by mentioning it in your command. (t: 350) So use test runner sub-agent to fix the failing tasks. Oh, so I can say, hey, use our, God, this name's horrible. Design system enforcer. (t: 360) All right, so let me show you real quick. On my app, I'm not really proud of this moment, but it is what it is. I think I was valuing shipping over anything. And so in here, I want to fix like some of this layout. (t: 370) So there are these types of things. I'm just going to give it a screenshot. Basically, this is a screenshot of our app. (t: 380) And then I'm going to see if I can use the design system enforcer. Hello, Mr. Cloud. I'm going to use you. And see if I can have it do some design system enforcing. (t: 390) Please use the design system enforcer to help me fix the UI here when I hit all transcriptions. This also needs to work for mobile as well as desktop view. (t: 400) So that we will have the appropriate design system for viewing all of my tasks. So that's it. I'm going to go ahead and start my transcriptions. (t: 410) It's currently reading the image, but I was hoping it was going to kick off a sub-agent for it. Yes. Okay. So now what we're seeing is it's kicking off my design system enforcer, which is all of this prompting stuff, (t: 420) like 73 lines, which is cool because that's my, my, my regular design system prompt is like several hundred lines. It's going to have its own context window, has its own dedicated agent with these specific instructions. (t: 430) And so I'm curious to see what code changes start rolling through so that I can go ahead and review them and see if it works. So we're just trying to fix this like janky UI that I just tried (t: 440) to ship as fast as possible. And we're also trying to see if it can actually work on mobile a lot better because you can see a mobile yet to do the sideways scroll thing. So this is the file that it generated and it's asking me to (t: 450) do the edit. I'm just going to do a quick cursory glance at this. And it is doing the whole divisible by the eight point grid system because previously you could see PY4, PY3, (t: 460) and now it's doing four and six, and it's actually adjusting for the actual mobile views and stuff as well. Okay. Okay. Okay. (t: 470) So now we have one of the code changes landing and let's just see if I can actually see this live right now. Okay. A lot better. A lot better. Okay. Yeah. (t: 480) But you see all of these icons are now like the right size where previously like one would be smaller. And then if the file name was too long, it would basically work like not do the right word wrapping. (t: 490) Oh, we're seeing this change in real time, which is pretty fun. So it's getting better for the mobile view because you can see it actually takes out some of the stuff and that's probably the second iteration that I'm going to do, (t: 500) which is nice. You know, it just gives you the information you need to kind of wrap some of the things here. And that's probably has not accounted for that, but for the desktop view, it's kind of nice. (t: 510) It's very reactive. And I like this a lot. You can kind of see it kind of adjusting itself. What's happening right now is Claude is actually going through and doing a secondary pass on the output and reviewing, (t: 520) am I done yet? Is everything solved from what the user was originally asking? And then it's thinking by itself. And so, like, oh, if this has changed now, this needs to change. (t: 530) Let me go ahead and see if I could find it in the code. And that's kind of the advantage of using some of these sub-agents because now it has more token windows because it only used 50K tokens to continue through and actually kind of (t: 540) making a better pass at it. And that to me saves me prompting time as a solo engineer. This is super duper helpful and super duper resourceful. (t: 550) And I'm glad it's kind of doing this on its own without me really telling it to do what it needs to do. So now, it's actually checking to see if it can run the server and it did a second pass. (t: 560) And now it's basically kind of fixed that. So it's like it fixed the table layout view, it fixed the file name display and remove the truncation in favor of break words to show the full file names. Nice. (t: 570) In that couple of iterations, like Claude code by itself, figure that out. That's my favorite thing about Claude code right now compared to a lot of other agents is that fact that it's using that output. (t: 580) It's thinking it's coming back out. Oh, my gosh. Iran linting on everything. Oh, no. OK. That's a different problem for a different day. This is working really great so far. (t: 590) And I want you to do the iteration on the mobile view right now, because in the mobile view, we need that same type of treatment for the titles, because as you can see, they're very long as well. (t: 600) I noticed Iran linting and it ran linting on files that we didn't touch in the code base. I want you to go ahead and review and discard the changes for the files (t: 610) that don't require linting right now. I don't want to check in all that code. I just want to check in code that we have. Specific. Modified. And well, look at that on mobile. It's already fixed. (t: 620) Wow. This is this is clean. I like this. Yo, not going to lie. This is noise. So if I go the sheesh, let's go, man. (t: 630) This is now looking like a professional app one day at a time, right? This is all this is all I have time to do. It's like one day at a time. I'll work on one feature. But setting these types of sub agents are so helpful because now we're going to start to leverage the AI for future tasks. (t: 640) Claude can run. Oh, my God. So now that's a big one. And when you're up and running, you're finally rolling the app. (t: 650) And then, you know, your (t: 660) MVP (t: 670) is doing good right here, right? When you get. you're going to automate that away so that later on you're now sitting in GitHub issues, (t: 680) you're sitting in a Slack command, you're sitting in somewhere else that will go write the issue in and then Claude will kick off and start solving those problems. And this is the workflow that I'm (t: 690) trying to get into that the Claude Code team currently does and all the folks at Anthropic. I think it's like over 80% of their code is written by AI and more specifically by Claude Code. So by leveraging that and their workflows, we get to bring that into our thing. I'm a solar (t: 700) developer. So can you actually be solo and be a designer, be an engineer, be a project manager? (t: 710) Yes, you can. Right? Because we start to define them slowly, right? And this is how you can leverage those workflows to do that type of thing. Okay. So it did the linting thing. It did this (t: 720) thing. I'm going to check in this code. This is fire. Almost in one prompt, right? Like I really just said, do this. Here's the screenshot. Okay. Make sure we do the same treatment for mobile. (t: 730) Cooked. Absolutely cooked. Shipping my app is probably better than doing it on my phone. I'm not going to do that. I'm going to do it on my computer. It's better than me waiting for it to be this perfect. Is a user going to care that much? No, they just want the transcriptions. They came in here for a job. They're going to get their (t: 740) stuff and get out real fast. In here, I can take like a live stream. I can start to generate the transcripts. And what's nice about it is now kicks off its own process. So I can review the other (t: 750) transcripts. I can come back to it and it still has its progress. I can go into here, view the other transcripts and do other types of work. So it starts to actually feel like a solid web app (t: 760) versus like, you know, just the structure. Just a straight up static page. One of the things I'm really excited about is that you can just tell it in plain English what you want. I didn't have to go and generate these crazy long documents. (t: 770) I didn't have to go and like figure out these other things. Just in plain English, I can actually describe what I want this agent to do. And then it writes its own instructions. (t: 780) And it's really, really, really good at following them, which I'm really impressed by. So don't be afraid. All you have to do is hit slash agents, start the conversation, (t: 790) and then you can put it into your project. And that'll be a good way so that in the future, you can kick them off with a GitHub workflow. Probably get into a little bit later.

