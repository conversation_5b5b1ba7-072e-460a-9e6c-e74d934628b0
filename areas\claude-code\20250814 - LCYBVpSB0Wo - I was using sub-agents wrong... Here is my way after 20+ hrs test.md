---
title: I was using sub-agents wrong... Here is my way after 20+ hrs test
artist: AI Jason
date: 2025-08-14
url: https://www.youtube.com/watch?v=LCYBVpSB0Wo
---

(t: 0) So Cloud Code introduced this sub-agent feature a few weeks ago. It was super exciting concept. However, for people who tried it, they often get quite negative experience where sub-agent feels (t: 10) slow, consume much more token, and most importantly, it didn't feel like it's contributing to the better result. And I was among one of those people, but only recently I started learning the (t: 20) best practice of using sub-agents and that has totally changed the game and make my Cloud Code perform much better consistently. That's why today I want to share how do I think about and (t: 30) design sub-agent system. So firstly, we're going to understand why did Cloud Code introduce sub-agent concept initially. And if you don't know how exactly Cloud Code agent works behind the scenes, (t: 40) it's basically a tool called agent that has equipped with a list of different tools for read file, listing all the files, edit files, stuff like that. And some of the tools can consume a lot more token like read tool because you're going to include the whole content of the file into the (t: 50) conversation history. And before Cloud Code has the sub-agent, it's going to have a lot of agent feature. Everything will be done by the Cloud Code agent itself, which means before it (t: 60) starts implementing, it might already use 80% of the context window because those files will contain large amount of context, which will likely trigger this compact conversation command that (t: 70) will summarize the whole conversation before it can proceed. And as we know, every time when you compact conversation, the performance just drops dramatically because it starts losing context about what it has done before. That's why later they introduced this task tool for the Cloud Code (t: 80) agent. It allowed Cloud Code to assign a task to another agent, and this agent will have the exact same set of tools, including a read file, search file. So it can trigger this agent to (t: 90) actually scan the whole code base, understand what are all the relevant files to change, and then based on that information to do the actual implementation. And the way this saving (t: 100) token is because from the parent agent perspective, all steps that sub-agent take in the middle won't be part of the conversation history for the parent agent. It can only see that it assigned tasks to (t: 110) the sub-agent, and then sub-agent come back with a summary of the research report, which can be used as a solution for the next action. By doing that, you've effectively turned those massive token (t: 120) consumption from the read file, search file actions to something like just a few hundred token summary, but still contain the most important information to guide the next action. (t: 130) So the whole purpose of the sub-agent has been around context engineering and context optimization. But where things fail is when people start trying to get the subagent, not only doing the research work, but also directly doing the implementation. For example, the first thought I (t: 140) had was, what if we can have front end dev agent to get the agent to perform this work? And you know, that's the first thing that I always ask the person to do. You know, for example, the first thought I had was, what if we can have front-end dev agent to design a project that is really also, I guess, to do just a front-end implementation with special rules and workflows, (t: 150) as well as back-end dev agent who is specialized at back-end implementation. Then for the parent agent, it really just orchestrate the whole conversation and delegate tasks to others. (t: 160) This sounds really good at beginning, but the moment if whatever sub-agent implemented is not 100% correct and you want agent to fix it, that's where the problem begin. Because for each agent, (t: 170) it only has very limited information about what is going on. For the front-end dev agent, it only knows the action it take as well as the final message it generate in that specific task. (t: 180) Same for the back-end dev agent. And if you prompt cloud code agent that there's a front-end bug, even though it assigned to front-end dev again, this will just trigger a new conversation (t: 190) because the front-end dev agent wouldn't know what happened before in the last front-end session. And also won't have any context about what back-end dev has done before. So each task is very contained session. (t: 200) Meanwhile, for the parent cloud code agent, it also has very limited information because again, it won't see all the actions that has been taken for the sub-agent, which means it won't know (t: 210) what specific files have been created and what did they actually put in those files. All the parent agents see is that I assigned tasks to front-end dev and front-end dev come back saying I complete task and same thing for the back-end. (t: 220) So if you want to get cloud code agent to fix the bug itself, it will have very limited information about what is causing the issue. This probably will be resolved later in future (t: 230) where we figure out a good way to share context across those different agents so that each one of them always on the same page about what has been done. But for now, the best practice would be (t: 240) consider each sub-agent almost as a researcher and think about what kind of planning and research steps can actually dramatically improve your current AI coding workflow. I also receive a similar feedback from Adam Wolf, (t: 250) who is one of the key engineer on the cloud code team, where he says sub-agent works best when they just looking for information and provide a small amount of summary back to main conversation thread. (t: 260) So with this one, I got this idea. What if each service provider like Versailles AISDK, Superbase, Tailwind, they can just have one agent that is equipped with all the latest knowledge (t: 270) about their documentations, best practice and design. And then this agent can start looking through my existing code base to figure out an implementation plan. (t: 280) And this is exactly what I tried. I start creating different experts of agents for each service from Chessian, where it has access to special MCP tool that can retrieve relevant components (t: 290) and same design to do a really good front-end job. Or Versailles AISDK expert that is loaded with the latest Versailles AISDK v5 doc, because they just released this new version (t: 300) a couple of weeks ago. Or a Stripe expert that is loaded with latest Stripe doc, as well as tools like Context 7. So you can do complex setup like usage-based pricing (t: 310) very easily out of the box. Meanwhile, I also did some optimization about the context sharing across different agents. This is something I learned from Manus's team's blog on Context Engineer, where they talk about all the tricks and tips (t: 320) of how they make Manus to execute long running tasks. There are a lot of good stuff in there, but the part that inspires me most is how they use file system as the ultimate context management system. (t: 330) So instead of storing all the tool results in the conversation history directly, they receive a result to a local file, which can be retrieved later. In their case, when the agent run a web scraping tool, (t: 340) instead of including the whole context script inside the conversation history directly, which might take more than 10,000 token, they will just save the script content into a local MD file, (t: 350) which can be retrieved later in any point of conversation. And this is exactly what I designed here. Inside doc cloud folder, there will be a task folder that contains the context of each feature (t: 360) that you want the team to implement. Meanwhile, each subagent can start creating those MD file about the specific research report and implementation plan, so that the process will be the parent agent (t: 370) will always create a context file that include all the information about the specific project we've tried to execute. And for every subagent, before they start doing the work, they will read this context file first (t: 380) to understand the overall project plan and where things are at now. And after they finish, they will start to look at the application process, which is called the application process. And after they finish, they will also update the context file to indicate what are the core steps they did and save the research report into a .md file in the doc. (t: 390) So the parent agent or all the other agents can just read this doc later for getting more context. And this setup has dramatically improved the success rate and result for my cloud code. (t: 400) And this is what I want to quickly show you today. So hopefully you get an idea about what type of subagent you can create that is going to be actually useful. But before we dive into that, I know many of you are first-time farmers. (t: 410) Building product is just one part of the puzzle. You also need to learn how to acquire users, how to price it, and how to prove value to customers. (t: 420) That's why I want to introduce you to this free material called Money-Making AI Agents. It is done by Dimitri Shapiro, founder and CEO of MindStudio, which is one of the fattest growing AI stars. (t: 430) He shared his whole journey and experience from spotting the real problem and worth solving all the way to pricing and closing deals, covering all the practical and essential workflows, tools, and processes. (t: 440) It even includes specific scripts about how he did demo. And he also shared his own experience with customers. And he also shared his own experience with customers. And he also shared his own experience with customers. As well as many real-world case studies of how people are building and launching AI products that cost 6 to 7 figures annual recurring revenue. (t: 450) And my favorite part is how to think about pricing of your AI product and service, different framework of work with enterprise versus SMB, and how to estimate the value from your client's point of view. (t: 460) It has more than one hour practical guide plus guides that you can start using for free. You can click on the link in the description below to get this resource. (t: 470) And thanks HubSpot. For bringing us this awesome material. And now call some agents. So to build those sub-agents, the general rules I have is that I would include a lot of important docs directly inside the system prompt. (t: 480) So I can have confidence that it will follow the latest practice. And meanwhile, also gives them random tools to retrieve important contacts. In this chassis and expert example, as I mentioned before, there are MCP to that specific design for those information retrieval form that specific package. (t: 490) Why is this chassis and component MCP? (t: 500) They allow you to retrieve components. The example code for each component. So it will have the full context as well as another MCP tool to retrieve and design a scene for Chassian. This MCP is from TweakScene, which is a website that is (t: 510) specialized in those same design. And this MCP will just retrieve some well-designed scenes so that it can be used as reference. Normally I will open the terminal to code.cloud.json. (t: 520) This will open your global settings. And here's one key called MCP server. We are passing this Chassian component and Chassian scene tool. (t: 530) So sub-agents will have access to it. And we can choose which two agents should have access to the model, the color. And then you will see this new agent created. You can add an agent if you want, but what I normally do is that I will open (t: 540) terminal and then do code.cloud. So this will open your personal settings for cloud code, which will be applied across all the projects. So we're creating new agent and we can either create a project specific agent (t: 550) or personal level agent that will be used across all the projects. For our example, I will use a personal level and we can generate a cloud. It will try to generate title, description, and the system name. (t: 560) Okay. So here's the description I give is that it's a Chassian frontend expert who can help design world-class frontend UI, using relevant Chassian MCPs. As I mentioned before, there are special MCP tool that I use here. (t: 570) So here you can see an example we created here. Normally what I do at this point is either pasting docs into their system prompt or attaching special MCP tools and rules about how to use this MCP tool. (t: 580) In the final version I designed before, it has all those rules about the process it should follow, and it also has the rules about how to use it. (t: 590) So the overall plan here is that it will listing all the components, choosing the right component and get example code to know how exactly to use that. And also get some reference about how to composite different UI patterns together (t: 600) using the block, gather relevant themes, as well as some rules about where to put which component files. And normally what I will also do is I will firstly add a goal here for each (t: 610) sub-agent where I will mention that the goal is to design, propose a detailed implementation plan and never do the actual implementation. (t: 620) And once they finished the process. I should save the design file to doccloud slash doc file. And in the end, I will also include this output format to instruct that the final message output should look something like this. (t: 630) I've created a plan as this file. Please read that first before you proceed. And this message will be sent back to the cloud code parent agent so that it will know that I need to look up this file as well as list of different rules to keep (t: 640) instructing that don't actually do the implementation. And before you do any work, it should look at the context file first to get full context and after finish your work, it should update this context file. (t: 650) And this also one kind of weird behavior. I observe sometimes sub-agent will try to run this cloud MCP client to call itself my suspicious because sub-agent actually inherit cloud.md file we have. (t: 660) So I just add this rule here to making sure it is not getting confused. And this output format and the rules as well as the goals are almost identical (t: 670) across all the sub-agent I created. For example, for this Vercel AI SDK expert. I also have just the same structure for the output format and the rules. The only difference here is that I will include more. (t: 680) Detailed documentation about the latest Vercel AI SDK doc, which is something I directly grabbed from their website. I just copy over some kind of fundamental important pages about Vercel AI SDK v5 (t: 690) into the system prompt as well as a migration guide to clearly spit out the difference between 4.0 and 5.0. (t: 700) That is also something I get from their own doc. So this is an example of how we can set up those specialized sub-agent for each service that you're going to use. So now let's give it a try. Our first is set up in Next.js. (t: 710) I'm going to create a project with Chessian and then I will do cd myapp and cloud. So this will set up the initial project. I'll do init to start initializing the code base and create a base cloud code (t: 720) rule and I will create this cloud.md file. But to make it work better in the cloud.md, I also want to add some special rules about the sub-agents. So firstly, I want this parent agent always keep the project plan in the (t: 730) .cloud slash task slash contact sessions so that we can use this file as source or choose to maintain in the context. (t: 740) And after I finish the work. It must update this .md file. And meanwhile, I also want to give some rules about the sub-agents. So that note, it has this two sub-agents that it should delegate. And also when we pass tasks to sub-agent, making sure we do pass this .md file name. (t: 750) And after each sub-agent finished work, it need to read the documentation they created before it execute tasks. So with this set up, let's give it a try. (t: 760) So I'll give a prompt. Help me build a replica of ChessGPT using ChessCNS frontend and Versailles AIS DKAS AI service. Let's firstly build the UI, making sure we consult the sub-agent. (t: 770) And firstly, it will try. To create this contact session one .md file to document the context about this project that we're executing. Then it triggers ChessCNS agent. If I do control R to open the detail, you can see that it gives a very specific (t: 780) task to this ChessCNS UI expert agent. Including the context file to read as well as specific tasks. Then the first thing the sub-agent do is that it try to read this context file. (t: 790) Then it start running the MCP tool called ChessCNS components. So this special MCP tool that we connected. So if I go back to the agent, it will. (t: 800) Continuously using those relevant tools to retrieve information that can help it design the UI with right components. And for each component, it can also get some example code of how to use it. (t: 810) So in the end, this ChessCNS agent will finish the work and create a doc file about this UI design, overall layout, and the planned components to (t: 820) use with very detailed structure. And based on that, the parent agent will read this plan and start breaking down the actual implementation. And after a while, it finished the whole thing in just. (t: 830) One go and also update this context session file to indicate what kind of things have been done and what are the overall architecture and I can run this application by the way, cloud code just introduces background session that it (t: 840) can keep running and monitoring the result, which is really useful. If I open this UI, you can see it is extremely high fidelity with all those (t: 850) detailed interaction considered, which looks almost identical to the first version of ChaiGPT and there's some arrows, which we can paste in. Then we'll start fixing those arrows. (t: 860) And this is why the new subagent structure is so good because all the execution would be done by this parent agent. So it will have full context. What's the best way to fix the issue? (t: 870) Great. So now there's no arrows. And if I type in a message, it will also have nice interaction and animation has been handled. So next we can ask it to connect to the Vercel AI SDK. (t: 880) So I'll ask it to let's do the Vercel AI SDK integration and make sure it consults the subagent. So this will trigger the Vercel AI SDK implementation planner. (t: 890) Inside this agent, again, it will firstly try to read this context file. Then it will look through the whole code base to see what are the best way to implement this. (t: 900) And after finish, it will create this talk about the implementation plan for the Vercel AI SDK and also update this context session MD file to document what has been done. And then this parent agent reads the whole implementation plan and come up (t: 910) with the specific implementation steps. And cool. Again, after finish, your marketing is completed and continuously update this (t: 920) context file. So now we get this application running. If I just type in, hi, I'm Jason. It have this agent actually connect to the large-length model and return me the results in just one shot, which is amazing part. (t: 930) So here's a quick example of what I learned as best practice for using subagents. If you want to learn more in AI Build Club, I paste in all the prompt and process of creating those subagents that I just show you. (t: 940) Meanwhile, we have this cloud code template that we are curating, which include a list of hooks, commands, and agents that we actually tested and really useful in production. (t: 950) So if you're interested, you can click on the link below to join AI Build Club. We also have weekly session to talk through the best practice we learn every week. I hope you enjoy this video. Thank you. And I see you next time.

