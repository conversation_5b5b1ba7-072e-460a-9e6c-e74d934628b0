---
title: <PERSON> Went <PERSON> on Claude Code—These Are My Top 13 Tricks
artist: <PERSON>
date: 2025-07-07
url: https://www.youtube.com/watch?v=T_IYHx-9VGU
---

(t: 0) Claude Code is already super powerful and I use it every day. But with just a few of these tricks, I've turned it into something that I actually truly enjoy using. (t: 10) Two fish are in a tank. One turns to the other and says, Do you know how to drive this thing? Gosh, I just wish that I could get this more easily done inside of Cursor. Oh, good lord. (t: 20) I mean, I asked for crazy. Wow. Yes. Yes. I didn't tell you about it. Today I'm going to cover 11 of my favorite tips and tricks. Some of them are very obvious and I'll fly right through them very quickly. (t: 30) But others are kind of brand new or really interesting to see. And one of them really makes it just really delightful to work in Claude Code. (t: 40) And I hope you think so too. But admittedly, I like to be a little bit silly sometimes. But we really have to push forward. So this will be a little bit quick. If you get lost anywhere, I have a previous video about Claude Code in general. (t: 50) If you don't know about <PERSON>, you can go check that previous video out. And maybe you'll find it in the description. Maybe that'll give you a little bit more information or just ask <PERSON><PERSON> about some of these things. (t: 60) All right, let's dive in real quick. Oh, one note before we get started. A lot of this is going to get compressed and it's going to feel pretty tight and fast. (t: 70) It's not intended to be that fast. Let me know. Send me a comment or something like that. If you need me to make a whole video about any one of these or share something that I didn't share. (t: 80) But at the same time, it also got a little bit long. I have so much to share that really makes a difference with the way that I use Claude Code. And it really makes it. Very valuable to me that I really wanted to share them all. (t: 90) That's why I got a little bit longer. So I have to apologize upfront, taking even more time to just do that, which is insane. But I apologize for the length. I think toward the end, if you see the first ones and you feel like, oh, I've seen all this, you won't have seen all the ones toward the end. (t: 100) So I would say just skip forward or wait until those come up because these really compound quite nicely. (t: 110) All right, let's jump into the real content. All right. This is not number one, but installing Claude Code, you'll have to go to Claude Code. Just search. For it and take this command to install, take it to a terminal and install it in your terminal right there and you'll be on your way. (t: 120) So that's how you get Claude Code started installed. Once you have it installed, you can just type in Claude and you'll open Claude Code in the folder that you're in. (t: 130) I wanted to show you this. I opened this in my root folder and Claude Code now comes up. It didn't do this in the past, but it comes up and say, no, are you sure you want to open this in this folder? (t: 140) What you really want to do is open Claude Code in a project folder where you're ready. So you can just type in Claude Code in your project folder and it'll open up. (t: 150) And then you can just type in Claude Code in your project folder and it'll open up. And then you can just type in Claude Code in your project folder and it'll open up. So just create a brand new folder if you need to and do the same thing. Just type in Claude. Nice and easy. Okay. The first tip open Claude Code in cursor or Visual Studio Code. Both of those are free applications to just install and use. (t: 160) You don't have to be a subscriber. VSC if you're not a cursor user at all. What we're really trying to do is have some of the controls like being able to see files and be able to see the contents very easily. (t: 170) So we're going to be inside of cursor here. I will pull open a terminal just like we were in a terminal window before. Once you're inside of cursor and you've run Claude. (t: 180) So here we are looking at Claude. I'm going to run a slash command and there is a slash command called IDE. And you can see here that it says manage IDE integrations and show your status. (t: 190) So if I hit that it knows that I'm in cursor. And what that really does is if I happen to open a file then what will happen is when I come down here it will gain reference to the file that I have opened because of that integration. (t: 200) So that's one of the things that's one of our first tricks is once you do this you'll be able to see the file. And you can see that the IDE working within the IDE and files that you have and things that you select will show up down below or inside of the terminal version of Claude when you're using it. (t: 210) And it allows both of them to work together. We got to move on. (t: 220) All right. For the next one very easy one it's slash init. And it's really the way that you get started inside of Claude code in a new project. So what it's about to do when I hit init is it'll look through the entire project and tell me something about the project and record it all in this special file called Claude MD. (t: 230) It's. All the directories and all the opinions of the system. (t: 240) This is what they call their memory file and this is kind of a thing that's pumped into the beginning of every chat that you start. It basically pre seeds all of the chats that you start just once so that every chat you have has this information inside of it and contextually the models underneath will always kind of have an idea of what project it's working inside of. (t: 260) So basically new project start get started and then run Claude you can do init at any time. Okay. So the next one. In fact to really fast I'm going to show you because you know these already you've probably heard about them as you move in think chat GPT you start a new chat you have a conversation it gets longer and longer all of that is within the context is the idea so all of this gets sent back and forth sort of to the model every single time the model has all of that information to continue to work on and it helps kind of get better about the conversation as it goes forward. (t: 290) Same thing happens here except there's a lot more data sometimes in a project right. These projects get really really fast. So it's really really big in the context window is pretty big these days so everything inside of this this context is piled on top of everything what you'll start seeing as a message down here saying 25% until compaction or something like that. (t: 310) What it's trying to say is I can't have an endless amount of information going in so I have to summarize it to some degree and keep the nuances so that the next question in this chat that you have I'll still remember all the other things that we were talking about so that one is compact and you can run that. (t: 320) Yourself and you'll see it's clears conversation history but keeps a summary so that was important you can run compact anytime you're kind of working through a problem continually and you want smaller and smaller essentially. (t: 330) Context for the model to kind of work on instead of having this enormous history it will auto compact it takes a while to compact so do it when you feel like you have time to do it if you're going to compact but the one that I would advise is actually clear so it's a sister to the compact and it's really the first one that you need to learn it's basically hitting the new button or the new button that you're going to use. (t: 350) The new button or the new button that you're going to use. The new button or the new button that you're going to use. The new button or the new button that you're going to use. The new button or the new button that you're going to use. (t: 360) This one just clears everything out starts a new conversation history by the way shoves that cloud M.D. file in there so that Claude kind of understands those those things again and then you're off to the races with a new context. (t: 370) Okay some later ones get better I know you've probably heard quite a few of these but I thought they were important to put in here just in case some people hadn't the next one I'm going to show is you can change the model that you're on if you have a subscription with an anthropic that allows you to have both opus and sonnet this is the one that you want to use. (t: 380) The way that you can choose between opus and sonnet or you can choose their default which does opus for as the first model all the time up to 50% of your usage and then it starts using sonnet for when it thinks oh you might be getting close let me pull back a little bit. (t: 400) The other one here just going to rush through this if you hit shift tab you'll see that this is flashing at the bottom of the screen here as I'm continuing to hit shift tab so it starts on this mode no mode at all let's call it default mode and then if you shift tab one time it goes into auto accept. (t: 410) And that really is every now and then when you ask it to do something if it's going to write a file if it's going to list your files read your files change your directory those kinds of things it will come back and say is it okay that I do this yes no yes every time within this session don't ask me again those are the kinds of things that you'll see here this mode kind of auto accepts many of those not all of them you will still get that question plan mode though is just for conversational it won't write any files the idea is you ask it to plan something out it comes back with a huge well thought out plan. (t: 440) And it asks you if you're okay with it I would advise reading through that plan and saying no and having more conversation with it it will end up with a very good plan if you work back and forth with it a bit and then you can say yes and it will go off to the races and start doing its thing shift tab into auto accept and you'll have a much better time but that's not the only way to do this okay here's another I'm going to try to be brief about this one but this one is critically useful to me so I want you to hear you can put files into this now admittedly we're inside of the cursor environment at this point. (t: 470) So it gets a little confusing but I can drag this file over here and if I drop that file there you'll see that it puts in the path essentially to that file that's one way so that you can reference files directly with a path name and that works but you can also use the at command to get to that so if I wanted to get to the calculator file that's how I would reference it with an app so tab completed that it just selected what I was on so that's an easy way to kind of reference files that are within your project the other thing that's actually quite useful. (t: 500) Is if you take a screenshot of something so that takes a screenshot I have a little utility down here that allows me to drag my screenshot around and if I put it there you'll notice all it's really doing is putting the path in to the screenshot which is kind of nice so that's just referencing the file that we created underneath this and there's another way and I'll have to show you an interesting utility that I have called paste which is this is my clipboard this is the clipboard that I things that I've clipped recently and what happened when I took that screenshot they automatically go into my clipboard as well. (t: 530) So I'm going to go ahead and do that. I can drag it from here or I can just paste it here and this is not obvious so this is the one that's worth talking about control the now in Mac all Mac users will kind of hear that ago yeah yeah command V they meant command V I really meant control V so weirdly in in this terminal application of cloud code if you use control V it'll just paste right in so this is essentially the same image that's the trick I highly highly advise sending images in it is a fantastic way to work these models can figure out a whole bunch I could just take a whole picture of this this in (t: 560) entire. system and say I up go figure out what's wrong and it does a very good job of figuring on that all right when a okay quick side tricks so I haven't talked about this yet let me exit this terminal version of Claude will close that window down there happens to be a command escape mechanism that I can open up a Claude tabs so it works more like a tab that it does that parked terminal when interesting but it's just shell running in a tab. (t: 590) I'm going to run this way just wanted to show you that and I really like command escape to be able to pull open clawed at any time. and then I can just treat it like any other tab. Okay, so with that, (t: 600) we're going to need to talk about custom commands. Now, these commands are these slash commands. We've seen them before, and you can see that I have a couple extra commands at the top, including the normal ones that Claude will give you. (t: 610) So let's talk about these extra ones at the top. I have one called joke me, tell me a dad joke, and if I hit enter, it executes that command right away, goes to the model, (t: 620) the model runs the execution, and then whatever comes back is what it presents to us. Why don't scientists trust atoms? I think you know the answer to that. Okay, so very quickly, (t: 630) where did this come from? What is that all about? All right, so if you look into the files over here, it's one of the reasons that it's neat to be inside of cursor or some other system that you can kind of just directly edit files. (t: 640) I have created a .claud folder. I just created all this by hand, so just feel free to create. Then there's inside of that .claud folder, another folder called commands. This is just the file structure (t: 650) that Claude code uses to hold these things. And then inside of that is a markdown. File any markdown file that it finds inside of the commands folder, (t: 660) it will use as a custom command and you can put them in sub folders, a whole bunch of other things. Go look it up. But if we look inside of joke me, this file that I've created, tell me a dad joke is what shows up when I hit the slash. (t: 670) So the first line is what shows up. This ends up being the prompt. The entire thing, in fact, goes into the model as the entire prompt. And you'll see that I've even said there's an optional element down here. (t: 680) It says if the user provides a topic, make a joke about that topic. Otherwise, tell me a dad joke. OK, dollar arguments is where whatever you type in will go in. (t: 690) Let's take a look at that. Two fish are in a tank. One turns to the other and says, do you know how to drive this thing? But don't bomb. OK, let's briefly talk about those custom commands. That one was within this file, within this project. (t: 700) So these are project settings that happen. We'll look at settings a little bit more in just a second. So just realize we're inside of this project and there's a .claud folder. These are things that will get checked in (t: 710) depending upon your gitignore file so that other members of your team can get to it and all those kinds of cool things. So everybody gets the same kind of experience. However, this folder also exists in your root folder (t: 720) in your home folder. So I have a home slash .claud and inside of that have some of these things as well. And I'm going to confuse you real quickly, (t: 730) but I'm going to pull up another version of cursor here. This instance of cursor is actually pointing at my home directory and that .claud folder. So it's just everything inside of my .claud folder (t: 740) in my home directory opened up here. And what you can see is there is a commands folder just like we were looking at. And I have created some commands in here, one for a git save kind of mechanism. (t: 750) We'll do a commit message, a special commit message that it does with different icons and things like that. And another one is a set of project settings (t: 760) and design iteration that we do. So we need to really look at that one because that's one of the cool. So you might notice if I was you and I was looking at this a couple of weeks ago, I would have said, gosh, I just wish that I could get this more easily done (t: 770) inside of cursor. I have a way of coloring, all of my cursors. Now this is just Visual Studio Code. You can color a project. It's been there for a long time. (t: 780) I'm not inventing the wheel here by the way, but I did use .claud code to solve this problem for me because it's a little bit trickier these days in Visual Studio Code than it used to be to be able to do this. (t: 790) So, okay, if you remember, we were looking at project level settings here. There are also the commands that are done at a higher level or my global level, essentially, (t: 800) my system level. If I hit slash, you will see that the gsave is there, project settings is there. So these other files that I've created here are actually also available here in every project (t: 810) that I run. And that's one of the values of course, is putting them in your home directory so that every project that you run gets them. One of them though is very, very cool. (t: 820) So we have to look at this one. First, we're gonna look at the project settings, which we just just solved for. And if I want this version of cursor to be a different color, (t: 830) then I can just run project settings. And that project settings function, I'm just gonna let him run. If we come back and look at it, is not terribly complex. It says we want to colorize the current workspace for VSC. (t: 840) Here's the intent, really what we're up to. Tells it, hey, what we need is a .VSCode slash settings JSON file. (t: 850) Go create those. Here's an example file as a default, if it doesn't exist. And that's all it's gonna do. And you might notice in the background here, it did create this file. (t: 860) And once I accept that file, then it changes. All of the color schemes. And that's awesome, right? So now I have a theme environment just using this VSCode folder, (t: 870) very similar to the cloud one we just created by the way. And if I do something like choose a different color theme here, then this project will look like that going forward. Right? Very cool. Okay, there's one more interesting one down there (t: 880) that we're gonna take on. So there was another one down here called design iterate. And what I've done in this design iterate is just said, I want you to launch in number, (t: 890) default of three concurrent sub tasks. So there's sub agents going on. One of the thing Cloud Code can do, and we'll see this in a second, is it can run itself holistically. (t: 900) So Cloud Code is a CLI interface that can also run essentially headless, which means it doesn't need to show you anything. It'll just go and execute what you ask it to execute (t: 910) and then return the response value from that. We'll see that in a second. I have some real cool things from that as well. What you can do is ask it to create sub tasks. And what we're saying is for each one of the sub tasks, (t: 920) I want you to create sub tasks, and then you can run them. And then you can run them. So we're going to create, if the user gives you an image, a version of that UI, because we kind of want to iterate on a UI idea to see if we can come up with some creative things from that. (t: 930) So what we're going to do is we're going to create a parallel set of calls out that are going to create as many kind of iterations as we want. (t: 940) Let's take a look at that. We're going to come back to this guy and we're going to say, we want to do, oh, sorry, sorry, spinning around, design iterate. So we want to design iterate. And I want to, if you recall, (t: 950) use this clipboard object here. So that's the PNG. That's our image that we're going to design iterate. And I want five designs, some simply crazy. (t: 960) All right, I can see this as calculator interface with a dark theme. I'll create five different design iterations. And what we're going to see it do, now that it knows that it needs to do this, (t: 970) it's created the UI iterations folder, and it starts creating these tasks. Create minimalist calculator, create neon cyberpunk calculator, create retro eighties calculator. These have not completed yet. (t: 980) They haven't even started yet. Create 3D isometric calculator and the emoji chaos calculator, which I just can't wait. So you'll see they're all running at this point. (t: 990) Sorry, sorry. There are some issues with the UI here sometimes. There we go. So they're all running right now or kind of initializing and getting ready to run, if you will. (t: 1000) And then you'll see that they keep, they start. And as they work through their work, they'll update inside of this little task list. So each one of them will have their own little checklist and those kinds of things. (t: 1010) It's worth saying I have asked over here for construct a standalone HTML page featuring one interface screen that addresses the user requirement or brief. So basically we're gonna get, (t: 1020) this is single shot HTML programming just so that we can get designs back. Once we get them, then we can kind of steal some of the components if we like them. Let's let this thing cook. Okay, and it's done. (t: 1030) Let's take a look at what it came up with. We can kind of see them right here, which is kind of neat. Let's load the first one. Oh, good Lord. Okay. I mean, I asked for crazy. What's next? (t: 1040) Wow. Yes. Yes. I, yeah, that's pretty cool. Well, maybe some design issues here. Can't quite click them. Well, all right. Eight. I got an eight. My eight minus. (t: 1050) Okay, moving on. Okay. Excellent work. Okay. We have three more and they're really fun. The last one I love. I actually love it. You might've already experienced it here, (t: 1060) but we'll see in a second. So the first one really super simple. Let's drop out here and we'll get back to shell and say, okay, Claude dangerously skip permission. (t: 1070) Now what I'm doing here, I want to tell you not to do. It's the right thing to do. Don't start Claude this way. This will bypass permissions. I use it this way quite a bit, to be honest. (t: 1080) So I find this tremendously useful that I can just generally walk away from the system, allow it to do what it needs to do and cook. I have used it a lot and have not had any issues with it, (t: 1090) but that doesn't mean that it absolutely couldn't start eating things it's not supposed to. So be really, really cautious about this at your own risk, put your own protections down. (t: 1100) Okay. So if you're using it, you can just put it down the way you like, but this one has been a real game changer for me. I didn't tell you about it. Okay. This next one is going to feel pretty esoteric. So live with me for just a second. (t: 1110) And I have to describe a couple of weird things to you. I'm going to bring up a utility here that I use to launch applications, similar to spotlight. If you're a Mac user, you use spotlight. I use something called Raycast. (t: 1120) Raycast has the ability to allow you to run scripts. You write your own scripts and run them. In fact, of course, I write all of my scripts with Claude code. (t: 1130) When I have a script, you'll see one of them is clipboard to downloads. This is using Claude itself. So what this utility that I wrote or really asked Claude to write for me is doing (t: 1140) is down in here somewhere, you'll see that it's using the Claude command, just like we would by hand and running a command for us. Let's take a look. If I have something on my clipboard, (t: 1150) I'm going to copy that. I'm going to show you my clipboard utility that this is copied onto my clipboard. And then I'm going to, I'm going to run this command, which is clipboard to downloads. (t: 1160) And what it's doing is it's sending the whole text to Claude with another prompt that says, evaluate what's on this. I need a file name. I need an extension for this. (t: 1170) I need to understand what's in the file. And it gives that back to me. Then the script goes forward, saves this into my downloads folder. So I have an actual file (t: 1180) and then also pushes that file onto my clipboard so that I can paste that file in certain places. So for this case, what I'm going to do, so that you can understand what I'm doing here (t: 1190) is I will paste the file right here, just because it's on my clipboard as if I had dragged it in from my downloads. And I can say, is this the poem? And it says, yep, that's the poem. If I said, okay, go grab this. (t: 1200) You'll see I've grabbed a bunch of markdown. And if I run it again with a wholly different thing, basically a blog post, this time Claude's going to come back and say, (t: 1210) oh, well that looks like a markdown file. I'm going to save this as a .md. And then you have a .md file. So this just allows me to save this as a .md file. It allows me to select things on the web and create files out of them very, very quickly. (t: 1220) I do this very often as I need to move files around inside of LLMs more frequently than just a one-time text prompt that I'm dropping in. Okay, I thought I'd show you that. (t: 1230) I know that one's crazy because you have to know Raycast and scripts and all of this other stuff, but this is just us using Claude as a utility at the command line or within another script, essentially, (t: 1240) to give us data back and use intelligence inside of what otherwise would have just been a plain script. All right, moving on to the best one. The best one is next, my favorite. So hang in there. (t: 1250) It's pretty short. So let's get through it. Okay, here's my favorite last one. It takes a little bit of explaining, but not nearly as complicated as that RayScript one. (t: 1260) Okay, the Raycast one. So here we go. What I am doing is I'm showing you first, I'm inside of that Claude project or the Claude folder, the .Claude folder inside of my home directory. (t: 1270) So this is just the base area for settings for Claude on my system. And I just needed to show you that there's this settings file inside of this folder. (t: 1280) And in many cases, you'll find this inside of your project. So many projects will have these Claude settings as well in the .Claude folder, like we talked about before. (t: 1290) But what I've done at the top level is I've put in something called a hook. So if we come back to Claude code itself and said, I want hooks, slash hooks is a slash command (t: 1300) coming from Claude itself. And if I go into this and you'll see, they have, five different types of hook, pre-tool use, post-tool use, (t: 1310) notification stop and sub-agent stop. So pre-tool and post-tool is right before a tool starts to execute or right before the tool execution is done. (t: 1320) You can kind of interject things. You can then be in the pipeline, which is great. Kind of a plugin interface here. Notification is sort of when Claude needs your attention. (t: 1330) It's not just when it's finished and it gives you the prompt back, but when it might be asking you a question, or prompting you for a selection to make or approval, something like that. Stop is when it finishes the work (t: 1340) that it's working on at this moment. And that's the one that we'll be showing. Stop sub-agent is when you have a sub-agent or one of these sub-tasks that's kicking off doing work. (t: 1350) Every time each one of those stops, you can do something inside of that agent. All right, that's a whole bunch of conversation about something. What it does, if I had gone in and set that up, (t: 1360) I just wanted to show you what it writes. I have written a command here, basically it's just running a shell command called AFPlay. AFPlay is the audio framework for Mac. (t: 1370) So you can always drop down to do AFPlay, point to some path that is a wave file, MP3 file, something like this. And this one just points at a wave file (t: 1380) that happens to be on my computer somewhere, right? So this is just a standard wave file. And what I'll show you here is that will play once this is done. (t: 1390) Okay, so that awesome chime, every time Clog Code is completed in any project (t: 1400) is going to kick off. That's what you're gonna hear, I'm going to hear, because I put it at my root level. And I wanted to show you that because I'm compounding these things. So watch this. (t: 1410) In this project, which is a service that deals with numbers, I have something that goes and reads a whole bunch of YouTube numbers. It was in my previous video about Clog Code. (t: 1420) You can take a look at it there. And I felt like, oh, you know, who deals with numbers? Stats up, bro. Other than, you know, maybe a crypto bro. (t: 1430) So that one's great. But you heard both audio files playing here. If I look in here, this one has a Clog file in settings as well. And inside of its setting, it has a stop hook. (t: 1440) So both of these stop hooks are going off at the same time. And that's the neat thing that's happening is I have two stop hooks, one at the global level and another one down here at this project level. (t: 1450) And if I do it at a third project. Oh, Stats UI. Then my Stats UI project also tells me, by the way, I made these sound files with 11 labs, (t: 1460) with personality and all of this other stuff. So a lot of cool stuff going on here, but this one is great. So these things run off and take 10, 20 minutes sometimes to do their work. And I have four or five of them open a lot, very, very often. (t: 1470) And so I'm chasing them and checking them over and over. I don't know which one, even if I just had that general sound, I don't know which one made the sound. (t: 1480) So now I have them, some of them at least, telling me what happened, who finished. So I thought I would share that. To me, this was the most enjoyable, the silliest one, (t: 1490) but really makes me feel good when it goes off. All right, I hope you enjoyed that. I had a lot of fun with these, just trying to pull together the top things that I'm doing inside of Cloud Code that augment Cloud Code itself, that make it kind of useful. (t: 1500) There are quite a few more that I do that I think are really useful and valuable. Cloud Code itself is a great environment, (t: 1510) but with just a few of these little tips that you start to understand what it's trying to do, for you or how to make it more useful, or to kind of automate some of these things, (t: 1520) like the subtasks, those sorts of things, are really, really wonderful and allow you to start really broadly thinking how you can use this tool, rather than it's just simply a place you ask a question (t: 1530) and it creates some kind of calculator for you. All right, I hope any of these were interesting to you. I have a feeling you hadn't seen one or two of them. With any luck, I hope. (t: 1540) I had a lot of fun sharing them. If you like this kind of content, thanks for coming along this long, and thanks for being here, but subscribe. (t: 1550) Please subscribe. I love putting this kind of stuff out, and that signal really matters. It improves the channel. It certainly gets it a little bit more lift, but it also lets me know what people are interested in. (t: 1560) Give me comments, subscribe. It really does help. I appreciate it. Thanks for coming along for this one, and I'll see you in the next one.

