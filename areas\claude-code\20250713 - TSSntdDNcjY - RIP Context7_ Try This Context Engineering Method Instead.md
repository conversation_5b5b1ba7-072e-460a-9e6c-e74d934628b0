---
title: "RIP Context7: Try This Context Engineering Method Instead"
artist: Income stream surfers
date: 2025-07-13
url: https://www.youtube.com/watch?v=TSSntdDNcjY
---

(t: 0) Okay guys, I'm going to be continuing to experiment with context engineering today. Today we're going to be doing something slightly different where we use Docker to run the service, (t: 10) get it to test itself, self-improve, and then the final product that we get should hopefully be of much higher quality. Okay, so as usual I have the context engineering intro set up. I have my (t: 20) Claude<PERSON>. I've just added this stuff about the Docker so <PERSON> knows that it's actually got (t: 30) access to Docker. Nothing much has changed from yesterday. This is literally all I'm trying to do. I'm just trying to make a node backend, HTML5, CSS, ES for the front end. There should be a (t: 40) marketing front end, then behind a login there should be a (t: 55) forward backend. I'm just going to say use MongoDB. I have it installed. Use the CLI to set it up, (t: 60) etc. You don't have to use Mongo. You can use MySQL. It's just last time I did this it used (t: 70) Mongo. So I'll just use Mongo just because that's what I set up. Now, interestingly, it just deleted (t: 80) my entire project. So I'm actually having to run this again. But I'm going to show you guys how I do this. So the first thing I do, is I go on WSL and I do Claude, dangerously skip permissions. This is such a huge advantage for (t: 90) trying to do what we are trying to do here. Then what I normally do is I go to my school community. (t: 100) Yes, that is a genuine step that I take. It's not just me plugging this. I genuinely do go to the school community. And I go to the Claude code section here and the context engineering template (t: 110) right here. This is also available in various places online for free. I'm just going to add one thing. You must use Docker during the building process to test everything. So it works and the so they go to (t: 120) runs and I can test etc. Okay, so let's send the first prompt here. What this does is it just reads (t: 130) everything. So that I know that it's actually reading everything properly. And we'll actually (t: 140) follow everything properly as well. You can see here it's using multiple tools at the same time. Now it should just give me a summary of all of my instructions (t: 150) for creating a PRP for this exact process that I'm trying to perfect. I will say right now, I had (t: 160) some pretty good success with this. It wasn't perfect. But I did manage to make a CRM Pretty easily and pretty quickly. And obviously creating a CRM is not easy. But with node, I'm using node (t: 170) just so you guys know, I'm using a node backend this time just because it was recommended to me and it does seem to (t: 180) have its advantages for sure. If you are interested in the school community, if you just want to get things delivered in a more kind of user-friendly way, or if you just need a bit more help with (t: 190) certain things like Docker, GitHub, MCPs, all that good stuff, then definitely check out the school community. It'll be the first link in the description. Now, another way you could do this (t: 200) instead of using Gina, if you are worried about proxies or anything like that, if you don't want to get banned from Google, definitely check out Bright Data. They have a really, really good MCP (t: 210) that you could use for this process as well. Or you could just set up their API basically on your computer. But I would probably recommend their MCP server to be honest with you, Bright Data MCP. (t: 220) So the reason you would use this over something like Gina is just because you've got the advantage (t: 230) of proxies. You don't have to worry about getting banned. You don't have to worry about getting banned. And it does basically the same thing. Kind of the only difference between the two is Gina's just a bit easier to set up. Gina's just like one line of code, whereas the MCP can be (t: 240) quite difficult to set up. However, the good thing about Bright Data is you won't get as many (t: 250) like broken pages because it will retry or it's just better at scraping and all that stuff. So that will be a link in the description as well. Get yourselves $15 free credit with Bright Data. (t: 260) And thanks to Bright Data for sponsoring the channel. Okay, so that's now done that. And it says here use docket, etc, etc. So I'm trying totally new things in this video. Okay. Just so (t: 270) you know, well, not totally new things. I'm just I'm still working on this process. I'm very close to having this perfect. So I'm just going to say please spend multiple agents of agents to speed up (t: 280) research and get going. Okay, guys, so this is how it works. I've shown this a little bit before. (t: 290) But basically, I'm going to say, oh, Jesus, wait, I don't have enough balance. Hang on. Okay, so just so you know, a little secret with Gina is you can use it completely for free. So if I just go on an (t: 300) incognito tab and copy this API key, oops, and then say use this API key, that API is out of (t: 310) credits, then it'll change to this API key and we won't have this problem. (t: 320) So we can do that. And now we have to change the page. And then we can go back to the page that (t: 330) says insufficient balance. So we should see this start again, let's just change. Let's just move this page. Okay, these are all the documentations I mentioned, which is perfect. So now it's just (t: 340) going to do a load of research. Okay, so there we go. Now we have open, open, open ruse of research, (t: 350) API keys. All that good stuff. Node.js, MongoDB, Express and routing, data modeling, all that good stuff. So (t: 360) it's just going to start filling these in so that has these as references from now on. Okay, so you can see here that we're at this point, it's just created the PRP, right? So normally you run slash (t: 370) clear, right? Or slash compact, I think I am currently testing not doing either of those things, just so you know, so execute PRP. And then paste this. (t: 380) So I'm just going to remove the stuff about react, because I do not want it to use react. So let's get rid of this. (t: 390) So I'm just going to get rid of frontend UI. I deleted from and UI UX research as it was wrong, and I don't want to react. (t: 400) So I don't know if you can actually put things here. But it is what it is. I'm just gonna press escape as well. And so remember to I'm just gonna press escape as well. And so remember to I'm just gonna press escape as well. And so remember to (t: 410) use Docker. So and self test. So everything just starts and works. (t: 420) Okay, so this is what has been created. I didn't realize it was on 8080 for some reason. Okay. Let's go sign in. Let's see if anything works. So the sign in button doesn't work. Classic. (t: 430) Okay, so we now have a register page. Everything's working pretty damn well, to be honest with you. (t: 440) Let's see. Okay, so we now have a register page. Everything's working pretty damn well, to be honest with you. (t: 450) Let's create an account. See if it works. See if there's any console stuff. (t: 460) Doesn't seem to be working. It's almost there. I'm pretty sure. (t: 470) Just doesn't seem to be working. It's almost there. I'm pretty sure. Just doesn't seem to be working. It's almost there. I'm pretty sure. Just doesn't seem to be working. It's almost there. I'm pretty sure. Just doesn't seem to be working. It's almost there. I'm pretty sure. Doesn't seem to be working. It's almost there. I'm pretty sure. Doesn't seem to be working. It's almost there. I'm pretty sure. Okay, there we go. And we actually have an account as well. (t: 480) Beautiful. Let's see if we can create a task. Nope doesn't look like it. Okay, so overall, this entire process was much simpler, okay, to get to this point here where you have a working like system, right where you can even sign in. (t: 490) Let's see if it actually lets me sign in. There we go. (t: 500) Redirecting to dashboard. Now I just need to actually create the implementation and everything. This was a much, much better build than any other system that I've used, (t: 510) except step-by-step prompting, but that just takes too long. Overall, it was about half an hour. I have a dashboard. I have a database. (t: 520) I have basically everything that I need. I just need to make the dashboard actually working. So one thing that I have to say is it didn't do an amazing job (t: 530) of actually creating the functionality, but it did a much better job of creating something that was actually ready to go. So the good thing about having it on Docker is every time there's an update, (t: 540) the update is instantly added to here. And also, it did a lot of the bug fixing itself. (t: 550) Normally, this shit takes ages getting it to this point and also having an actual sign-in process as well. Okay, so like I said, the amazing thing about this is (t: 560) with Docker... everything just gets updated in real time. So every time it updates the code, it instantly shows the code update here. Okay, so to be honest with you, in two hours, we managed to get to this point. (t: 570) We have a basic functioning CRM MVP. Now, from this point onwards, we can just very easily add new features. (t: 580) This is built on Node, HTML, CSS, and JavaScript. Now, there's a few things missing, the home page and stuff, the SEO, stuff like that. (t: 590) The home page is actually fine. But, you know, we have the login, we have everything, the sign-up works, redirects into dashboard. This is a pretty good state to be in, to be honest with you. (t: 600) Now, we're very close to having this as an AI-powered thing. All we're doing right now is just bug fixing. I think it could have done this itself. It's just I wanted to get this done for the video. (t: 610) What I'd recommend is making sure that it's testing all endpoints. And then basically, you do have to do some bug fixing just because of the way that, like, you know, something can work (t: 620) and it's a curl request. But when you actually click a button like this, maybe it doesn't work. And it's very hard to get it to also do that as well. (t: 630) Now, you could use an MCP and that might be the next part to this. Now, it looks like it might have actually generated there. So, AI subtasks generated correctly. (t: 640) Oh, wow. There we go. Okay. So, we now have an AI-powered CRM. That's actually pretty crazy. So, conduct the audit, keyword research, on-page. (t: 650) SEO, improve page speed, mobile optimization, set up tracking analytics, review and finalize changes. It's actually done. Wow, that's pretty impressive. (t: 660) I'm not going to lie. This is the fastest I've ever gone from nothing to having some kind of prototype. So, this works. The next thing I'm going to do is I'm going to add Puppeteer and I'm going to make it so it uses Puppeteer to do this part automatically (t: 670) because sending... I don't know if it needs Puppeteer, but basically just needs to be able to send... (t: 680) the browser console logs somehow to the AI. And then pretty much like you should be able to build a SAS MVP in 30 minutes with this system. (t: 690) And yeah, very few errors. Very, very nice process. It worked, guys. I'm very, very happy with this. (t: 700) I hope you enjoyed this video. Check out the school community if you want to support me. If you're watching all the way to the end of the video, as usual, you're an absolute legend. And I'll see you very, very soon with some more content. (t: 710) Peace out. Oh, one more thing. Sorry, just to quickly mention. This is like... It's better than if I just vibe coded this because it's built with things like WebSockets and security. (t: 720) It's using Node. It's using Node packages. All that good stuff, right? So, it's kind of like a framework just because Node is a framework. (t: 730) But yeah, I don't know. This seems like a very interesting process thing. Peace out.

