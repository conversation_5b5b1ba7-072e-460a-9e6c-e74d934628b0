---
title: Building and prototyping with <PERSON>
artist: Anthropic
date: 2025-08-21
url: https://www.youtube.com/watch?v=DAQJvGjlgVM
---

(t: 0) These developers tend to like to run multiple cloud sessions at once. And they've started calling this multi-clouding. So you might see sessions where people have like six clouds open on their computer at the same time. (t: 12) Hey, I'm <PERSON>. I lead cloud relations here at Anthropic. Today we're going to be talking about cloud code (t: 20) and I'm joined by my colleague <PERSON>. Hey, I'm <PERSON>. I'm the product manager for cloud code. Kat, I want to kick this off just talking about the insane rate of shipping in cloud code. (t: 30) It feels like literally every time I open it up in my terminal, there's a new product or a new feature, something for me to use. Can you walk me through what the process looks like of the team going from an idea to actually shipping (t: 40) something to end users? Yeah, so the cloud code team is full of very product-minded engineers. And a lot of these features are just built bottoms up. (t: 50) It's like you're a developer and you really wish you had this thing and then you build it for yourself. And the way that our process works is instead of writing a doc, it's so fast to use cloud code to prototype a feature (t: 60) that most of the time people just prototype the feature and then they ship it internally to ants. And if the reception is really positive, (t: 70) then that's a very strong signal that the external world will like it too. And that's actually our bar for shipping it externally. And then of course there's always features that aren't (t: 80) exactly right that need some tweaking. And if we feel like, okay, ants aren't really using it that much, then we just go back to the drawing board and we say, like, okay, we're going to use this. Okay, what else can we change about this? (t: 90) And when we say ants here, we mean anthropic employees. Yes, yes. Yeah. That's really fascinating. I've never seen a product have as strong of like a dogfooding (t: 100) loop as cloud code. Do you think that's something we purposely did or that just kind of naturally arise from the product itself? It is quite intentional. (t: 110) And it's also a really important reason why cloud code works so well. (t: 114) Because it's so easy to prototype features, all of the features that we use, all of the features that we use on cloud code, (t: 120) we do push people to prototype as much as possible. But it's hard to reason about like exactly how a developer will use a tool because developers are so heterogeneous (t: 130) in their workflows. So oftentimes, even if you theoretically know you want to do something, like even if you theoretically know that you want to build an IDE integration, (t: 140) there's still a range of like potential ways you could go about it. And often prototyping is the only way that you can really feel that your product will actually be in your workflow. (t: 150) So yeah, it's through the process of dogfooding that we decide what version of a feature we decide to ship. I see. And there's something about the almost like the flexibility, but also the constraints of the terminal too that allows for (t: 160) easy addition of like new features, which I've kind of noticed where it's like because we have the primitives built out of like slash commands and things, (t: 170) it's easy to add another one on top of that. Yeah, it's totally designed to be customizable. Yeah. And because we have the primitives built out of like the code, it's easy to add a new one. Yeah. (t: 180) And because so many developers are familiar with the terminal, it makes like new feature onboarding super straightforward. Because for example, for a feature like hooks, (t: 190) which lets you add a bit of determinism around quadcode events, because everyone, every developer knows how to write a script. (t: 200) And really at the end of the day, all hook is is a script. And so you don't need to learn a new technology to customize quadcode. You write this script that you already know how to do, (t: 210) and then you add it to one of the quadcode events, and now you have some determinism. We're really trying to meet customers or developers where they are, I guess, with this tool. (t: 220) Yeah, definitely. Yeah. Switching gears slightly, alongside this insane rate of shipping is also the insane growth rate of quadcode with developers everywhere. (t: 230) Can you walk me through what that's been like to kind of be on this rocket ship? And how are we seeing various developers, whether it's at startups or individuals or even large (t: 240) enterprises, use quad? So one of the magical things about quadcode is that the onboarding is so smooth. After you do the NPM install, quadcode kind of just like (t: 250) works out of the box without any configuration. And this is true whether you're an indie developer through to if you're an engineer at a Fortune 500. (t: 260) I think this is the magic behind quadcode because you can run quad in a very simple way. It has access to all of the local tools and files that you have. (t: 270) You have this like very clear mental model for what quadcode is capable of. We do see different use case patterns, though, between smaller companies and larger ones. (t: 280) We find that engineers at smaller companies tend to run quad more autonomously using things like auto accept mode, which lets quad make edits by itself without approval of each (t: 290) one. We also find that these developers tend to like to run quad sessions at once. And they've started calling this multi-quadding. So you might see sessions where people have like six quads open (t: 300) on their computer at the same time. Maybe each of them are in a different Git workspace or in a different copy of the Git repo. (t: 310) And they're just like managing each of them. Whenever anyone stops and asks for feedback, they'll jump in there and then send it off and let it continue running. (t: 320) And on the other end of the spectrum for larger companies, we find that engineers are more likely to run quad sessions than quad mode. So we find that engineers really like to use plan mode. So plan mode is a way for developers to tell quad code to (t: 330) take a second, explore the code base, understand the architecture, and create an engineering plan before actually jumping into the (t: 340) code itself. And so we find that this is really useful for harder tasks and more complex changes. So going back to multi-quadding, just because I think that's a (t: 350) fascinating concept. I'm sure we kind of imagined folks wanting to do things like that, but maybe it was like somewhat surprising. Is there other things in that domain of like, oh, wow, this is a (t: 360) usage pattern that we really did not expect that have kind of popped up organically and we've shifted our roadmap around a little bit? (t: 370) Yeah, I think multi-quadding is the biggest one because this is something that we thought was just a power user feature that like a few people would want to do. But in fact, this is actually a really common way. (t: 380) And so for example, they might have one quad instance where they only ask questions and this one doesn't edit code. That way they can have another quad instance in the same repo that does (t: 390) edit code and these two don't interfere with each other. Other things that we've seen are people really like to customize quad code to handle specialized tasks. (t: 400) So we've seen people build like SRE agents on quad code, security agents, and so forth. And so we've seen people build like SRE agents on quad code, security (t: 410) agents. And so we've seen people build like SRE agents on quad code, security agents and incident response agents. And what they made us realize is that integrations are so important (t: 420) for making sure quad code works well. And so we've been encouraging people to spend more time to tell quad code about hey, these are the CLI tools we commonly use. (t: 430) Or to set up remote MCP servers to get access to logs and ticket management software. When these engineers are customizing quad code, does that mean they're creating sub-agents or are they creating markdown files like quad MDFiles? (t: 440) like Cloud MD files. How exactly are they creating these different types of agents? Yeah, I think the most common ways that we've seen people customize is by investing a lot into the Cloud MD file. (t: 450) So the Cloud MD file is our concept of memory. And so it's the best place for you to tell Cloud Code about what your team's goals are, (t: 460) how the code is architected, any gotchas in the code base, any best practices, and investing in Cloud MD, (t: 470) we've heard, dramatically improves the quality of the output. The other way that people customize Cloud Code is by adding custom slash commands. (t: 480) So if there's a prompt that you're always typing, you can add that into the custom slash command, and you can also check these in so that you share them with the rest of your team. And then you can also add custom hooks. (t: 490) So if, for example, you want Cloud Code to run lints before it makes a command, you can add custom hooks to that. (t: 500) So if you want Cloud Code to commit, this is something that's great for a hook. If you want Cloud Code to send you a Slack notification every time it's done working, this is actually the original inspiration for making hooks. (t: 510) And so these are all customizations that people are building today. Tell me more about what is the Cloud Code SDK? The Cloud Code SDK is a great way to build general agents. (t: 520) Cloud Code SDK gives you access to all of the core building blocks of an agent, including, you can bring your own system prompts, and you can build custom tools. (t: 530) And what you get from the SDK is a core agentic loop where we handle the user turns, and we handle executing the tool calls for you. (t: 540) You get to use our existing permission system so that you don't need to build one from scratch. And we also handle interacting with the underlying API. So we make sure that we have back off (t: 550) if there's any API errors. We very aggressively prompt cache to make sure that your requests are not (t: 570) If you're prototyping building an agent from scratch, if you use the Cloud Code SDK, (t: 580) you can get up and running with something pretty powerful within like 30 minutes or so. We've been seeing people build really cool things with it. We've seen people prototype a domain. Outside of coding, we've seen people prototype legal agents, (t: 590) compliance agents. This is very much intended to be a general SDK for all your agent needs. The SDK is pretty amazing to me. I feel like we've lived in the single request API world (t: 600) for so long. And now we're moving to like a next level abstraction almost where we're going to handle all the nitty gritty (t: 610) of the things you mentioned. Where is the SDK headed? What's next there? We're really excited about the SDK as the next way (t: 620) to unlock another generation of agents. We're investing very heavily in making sure SDK is best in class for building agents. (t: 630) So all of the nice features that you have in Cloud Code will be available out of the box in the SDK. And you can pick and choose which ones you want to keep. So for example, if you want your agent (t: 640) to be able to have a to-do list, great. You have the to-do list tool out of the box. If you don't want that, you can just go ahead and do it. If you don't want that, it's really easy to just delete that tool. (t: 650) If your agent needs to edit files, for example, to update its memory, you get that out of the box. And if you decide, OK, mine won't edit files (t: 660) or it'll edit files in a different way, you can just bring your own implementation. OK, so it's extremely customizable. Basically, general purpose in the sense (t: 670) that you could swap out the system prompt or the tools for your own implementations and they just nicely slot in to whatever thing you're building for, whether it's a system prompt or a tool. whether it's a system prompt or a tool. Whether it's an entirely different domain than code. Yeah, totally. (t: 680) I'm really excited to see what people hack on top of this. I think especially for people who are just trying to prototype an agent, this is, I think, by far the fastest way to get started. (t: 690) We really spent almost a year perfecting this harness. And this is the same harness that Cloud Code runs on. And so if you want to just jump right into (t: 700) the specific integrations that your agent needs, and you want to jump right into just working on the system prompt (t: 710) to share context about the problem space with the agent, and you don't want to deal with the agent loop, this is the best way to circumvent all the general purpose harness (t: 720) and just add your special sauce to it. All right. Well, you heard it here. You got to go build on the SDK. Before we wrap up here, I'm really curious to hear your own tips (t: 730) for how you use Cloud Code and what are some best practices we can use to build on the SDK. Before we wrap up here, I'm really curious to hear your own tips for how you use Cloud Code and what are some best practices we can do to get started. Before we wrap up here, I'm really curious to hear your own tips for how you use Cloud Code and what are some best practices we can do to get started. When you work with Cloud Code or any agentic tool, (t: 740) I think the most important thing is to clearly communicate what your goals are to the tool. I think a lot of people think that prompting is this like magical thing, (t: 750) but it really isn't. It's very much about, okay, did I clearly articulate what my purpose is, (t: 760) like what my purpose with this task is, how I'm going to use it, I'm evaluating the output of the task, any constraints in the design system. (t: 770) And I think usually when you can clearly communicate these things, Cloud Code will either be able to do them or just tell you that, like, okay, this thing I'm not able to do because A, B, C, (t: 780) and you want to try D, E, F instead. So it's all about the communication, just as if you're working with another engineer. (t: 790) Yeah, totally. And another thing is if you notice that Cloud Code did something weird, you can actually just ask it why it wanted to do that. (t: 800) And it might tell you something like, okay, there was something in the Cloud MD that said this, or I read something in this file that gave me this impression. And then that way, you can actually use talking to Cloud as a way to debug. (t: 810) It doesn't always work, but I think it's definitely worth trying, and it's a common technique that we use. Use Cloud Code to debug Cloud Code. (t: 820) Yeah, yeah. Like the same way that you, when working with a client, you're like, oh, I'm going to do this. And then when working with a human, if they say something that you didn't expect, you might feel like, oh, interesting. Like, what gave you that impression? (t: 830) Or why did you think this? And I think you can do the same with agents too. That's fascinating. Well, Kat, this has been great. Really appreciate the time. Thanks for having me.

