#!/usr/bin/env python3
"""
YouTube Channel Videos URL Extractor from Clipboard HTML
========================================================

This script extracts YouTube video links and titles from HTML content copied to the clipboard
from a YouTube channel's videos page and adds them to user-selected .md or .txt files. 
It supports section-based insertion for markdown files and removes duplicates.

IMPORTANT: This script is designed for YouTube CHANNEL VIDEOS pages, not homepage/recommendations.
    Make sure to copy HTML from a specific channel's videos page (e.g., /@channelname/videos)
    NOT from the YouTube homepage or "What to Watch" page.

Usage:
1. Navigate to a YouTube channel's videos page (e.g., https://www.youtube.com/@channelname/videos)
2. Select all content (Ctrl+A) and copy to clipboard (Ctrl+C)
3. Run this script

Features:
- Reads HTML content directly from clipboard
- Extracts YouTube video URLs and titles from channel videos HTML/JSON
- Filters out homepage recommendations and suggestion sections
- Supports both youtube.com/watch and youtu.be formats  
- Allows browsing for target .md or .txt files
- Detects markdown headers and allows section selection
- Inserts URLs into specific sections or at end of file
- Removes duplicate URLs from the entire file
- Preserves existing URLs and file structure

Requirements:
- pyperclip: pip install pyperclip
- beautifulsoup4: pip install beautifulsoup4
- tkinter (usually comes with Python)

Author: Generated for YouTube Transcript Manager
"""

import os
import re
import sys
import json
from pathlib import Path
from urllib.parse import parse_qs, urlparse
import tkinter as tk
from tkinter import filedialog, messagebox
from datetime import datetime, timedelta

try:
    from bs4 import BeautifulSoup
    import pyperclip
except ImportError as e:
    print(f"Missing required package: {e}")
    print("Please install required packages:")
    print("pip install pyperclip beautifulsoup4")
    sys.exit(1)


# No hardcoded configuration needed - reads from clipboard


def extract_video_id_from_url(url):
    """Extract YouTube video ID from various URL formats."""
    # Handle different YouTube URL formats
    patterns = [
        r'(?:youtube\.com/watch\?v=|youtu\.be/)([a-zA-Z0-9_-]{11})',
        r'youtube\.com/embed/([a-zA-Z0-9_-]{11})',
        r'youtube\.com/v/([a-zA-Z0-9_-]{11})'
    ]
    
    for pattern in patterns:
        match = re.search(pattern, url)
        if match:
            return match.group(1)
    return None


def normalize_youtube_url(url):
    """Convert YouTube URL to standard format."""
    video_id = extract_video_id_from_url(url)
    if video_id:
        return f"https://www.youtube.com/watch?v={video_id}"
    return url


def is_recommendation_section(json_data, context=""):
    """Check if the current JSON section is likely a recommendation/suggestion section."""
    # Only apply filtering if we detect this is homepage content
    if isinstance(json_data, dict) and 'FEwhat_to_watch' in str(json_data):
        return True
    
    # For channel pages (no FEwhat_to_watch), allow all content
    return False


def extract_youtube_data_from_json(json_data, video_data_list, context=""):
    """Recursively extract video data from JSON structure."""
    if isinstance(json_data, dict):
        # Skip sections that are clearly recommendations/suggestions from homepage  
        if is_recommendation_section(json_data, context):
            return
            
        # Check for video renderer patterns
        if 'videoRenderer' in json_data:
            renderer = json_data['videoRenderer']
            video_id = renderer.get('videoId')
            if video_id:
                # Extract title
                title = "Untitled"
                if 'title' in renderer:
                    if 'runs' in renderer['title']:
                        title = renderer['title']['runs'][0].get('text', 'Untitled')
                    elif 'simpleText' in renderer['title']:
                        title = renderer['title']['simpleText']
                
                # Extract date (approximate from published time text if available)
                date_str = datetime.now().strftime('%Y%m%d')
                if 'publishedTimeText' in renderer:
                    pub_text = renderer['publishedTimeText'].get('simpleText', '')
                    parsed_date = parse_date_from_text(pub_text)
                    if parsed_date:
                        date_str = parsed_date
                
                url = f"https://www.youtube.com/watch?v={video_id}"
                video_data_list.append({
                    'url': url,
                    'title': title,
                    'video_id': video_id,
                    'date': date_str
                })
        
        # Check for grid video renderer (channel pages)
        elif 'gridVideoRenderer' in json_data:
            renderer = json_data['gridVideoRenderer']
            video_id = renderer.get('videoId')
            if video_id:
                # Extract title
                title = "Untitled"
                if 'title' in renderer:
                    if 'runs' in renderer['title']:
                        title = renderer['title']['runs'][0].get('text', 'Untitled')
                    elif 'simpleText' in renderer['title']:
                        title = renderer['title']['simpleText']
                
                # Extract date
                date_str = datetime.now().strftime('%Y%m%d')
                if 'publishedTimeText' in renderer:
                    pub_text = renderer['publishedTimeText'].get('simpleText', '')
                    parsed_date = parse_date_from_text(pub_text)
                    if parsed_date:
                        date_str = parsed_date
                
                url = f"https://www.youtube.com/watch?v={video_id}"
                video_data_list.append({
                    'url': url,
                    'title': title,
                    'video_id': video_id,
                    'date': date_str
                })
        
        # Check for compact video renderer
        elif 'compactVideoRenderer' in json_data:
            renderer = json_data['compactVideoRenderer']
            video_id = renderer.get('videoId')
            if video_id:
                # Extract title
                title = "Untitled"
                if 'title' in renderer:
                    if 'runs' in renderer['title']:
                        title = renderer['title']['runs'][0].get('text', 'Untitled')
                    elif 'simpleText' in renderer['title']:
                        title = renderer['title']['simpleText']
                
                # Extract date
                date_str = datetime.now().strftime('%Y%m%d')
                if 'publishedTimeText' in renderer:
                    pub_text = renderer['publishedTimeText'].get('simpleText', '')
                    parsed_date = parse_date_from_text(pub_text)
                    if parsed_date:
                        date_str = parsed_date
                
                url = f"https://www.youtube.com/watch?v={video_id}"
                video_data_list.append({
                    'url': url,
                    'title': title,
                    'video_id': video_id,
                    'date': date_str
                })
        
        # Check for rich item renderer with video
        elif 'richItemRenderer' in json_data:
            rich_item = json_data['richItemRenderer']
            if 'content' in rich_item:
                # Check for videoRenderer
                if 'videoRenderer' in rich_item['content']:
                    renderer = rich_item['content']['videoRenderer']
                    video_id = renderer.get('videoId')
                    if video_id:
                        # Extract title
                        title = "Untitled"
                        if 'title' in renderer:
                            if 'runs' in renderer['title']:
                                title = renderer['title']['runs'][0].get('text', 'Untitled')
                            elif 'simpleText' in renderer['title']:
                                title = renderer['title']['simpleText']
                        
                        # Extract date
                        date_str = datetime.now().strftime('%Y%m%d')
                        if 'publishedTimeText' in renderer:
                            pub_text = renderer['publishedTimeText'].get('simpleText', '')
                            parsed_date = parse_date_from_text(pub_text)
                            if parsed_date:
                                date_str = parsed_date
                        
                        url = f"https://www.youtube.com/watch?v={video_id}"
                        video_data_list.append({
                            'url': url,
                            'title': title,
                            'video_id': video_id,
                            'date': date_str
                        })
                
                # Check for gridVideoRenderer within richItemRenderer
                elif 'gridVideoRenderer' in rich_item['content']:
                    renderer = rich_item['content']['gridVideoRenderer']
                    video_id = renderer.get('videoId')
                    if video_id:
                        # Extract title
                        title = "Untitled"
                        if 'title' in renderer:
                            if 'runs' in renderer['title']:
                                title = renderer['title']['runs'][0].get('text', 'Untitled')
                            elif 'simpleText' in renderer['title']:
                                title = renderer['title']['simpleText']
                        
                        # Extract date
                        date_str = datetime.now().strftime('%Y%m%d')
                        if 'publishedTimeText' in renderer:
                            pub_text = renderer['publishedTimeText'].get('simpleText', '')
                            parsed_date = parse_date_from_text(pub_text)
                            if parsed_date:
                                date_str = parsed_date
                        
                        url = f"https://www.youtube.com/watch?v={video_id}"
                        video_data_list.append({
                            'url': url,
                            'title': title,
                            'video_id': video_id,
                            'date': date_str
                        })
        
        # Check for shorts lockup view model (Shorts)
        elif 'shortsLockupViewModel' in json_data:
            shorts = json_data['shortsLockupViewModel']
            if 'onTap' in shorts and 'innertubeCommand' in shorts['onTap']:
                command = shorts['onTap']['innertubeCommand']
                if 'reelWatchEndpoint' in command:
                    video_id = command['reelWatchEndpoint'].get('videoId')
                    if video_id:
                        # Extract title from overlayMetadata if available
                        title = "Untitled"
                        if 'overlayMetadata' in shorts and 'primaryText' in shorts['overlayMetadata']:
                            title = shorts['overlayMetadata']['primaryText'].get('content', 'Untitled')
                        
                        date_str = datetime.now().strftime('%Y%m%d')
                        url = f"https://www.youtube.com/watch?v={video_id}"
                        video_data_list.append({
                            'url': url,
                            'title': title,
                            'video_id': video_id,
                            'date': date_str
                        })
        
        # Look for any direct video ID patterns in URLs or endpoints
        elif isinstance(json_data, dict):
            # Check for watchEndpoint with videoId
            if 'watchEndpoint' in json_data and 'videoId' in json_data['watchEndpoint']:
                video_id = json_data['watchEndpoint']['videoId']
                if video_id:
                    # Try to find title in parent context
                    title = "Untitled"
                    url = f"https://www.youtube.com/watch?v={video_id}"
                    date_str = datetime.now().strftime('%Y%m%d')
                    video_data_list.append({
                        'url': url,
                        'title': title,
                        'video_id': video_id,
                        'date': date_str
                    })
        
        # Recursively search through all dictionary values
        for key, value in json_data.items():
            new_context = f"{context}.{key}" if context else key
            extract_youtube_data_from_json(value, video_data_list, new_context)
    
    elif isinstance(json_data, list):
        # Recursively search through all list items
        for i, item in enumerate(json_data):
            new_context = f"{context}[{i}]" if context else f"[{i}]"
            extract_youtube_data_from_json(item, video_data_list, new_context)


def parse_date_from_text(text):
    """Parse date from text using various patterns."""
    if not text:
        return None
        
    text = text.lower().strip()
    
    # Common date patterns
    patterns = [
        # "2 days ago", "1 week ago", "3 months ago", "1 year ago"
        (r'(\d+)\s+days?\s+ago', lambda m: days_ago_to_date(int(m.group(1)))),
        (r'(\d+)\s+weeks?\s+ago', lambda m: days_ago_to_date(int(m.group(1)) * 7)),
        (r'(\d+)\s+months?\s+ago', lambda m: months_ago_to_date(int(m.group(1)))),
        (r'(\d+)\s+years?\s+ago', lambda m: years_ago_to_date(int(m.group(1)))),
        # "Streamed 2 days ago", "Published 1 week ago"
        (r'(?:streamed|published)\s+(\d+)\s+days?\s+ago', lambda m: days_ago_to_date(int(m.group(1)))),
        (r'(?:streamed|published)\s+(\d+)\s+weeks?\s+ago', lambda m: days_ago_to_date(int(m.group(1)) * 7)),
        (r'(?:streamed|published)\s+(\d+)\s+months?\s+ago', lambda m: months_ago_to_date(int(m.group(1)))),
        (r'(?:streamed|published)\s+(\d+)\s+years?\s+ago', lambda m: years_ago_to_date(int(m.group(1)))),
        # Exact dates like "Jan 15, 2023", "2023-01-15"
        (r'(\w{3})\s+(\d{1,2}),\s+(\d{4})', lambda m: parse_month_day_year(m.group(1), m.group(2), m.group(3))),
        (r'(\d{4})-(\d{2})-(\d{2})', lambda m: f"{m.group(1)}{m.group(2)}{m.group(3)}")
    ]
    
    for pattern, converter in patterns:
        match = re.search(pattern, text)
        if match:
            try:
                return converter(match)
            except:
                continue
    
    return None


def days_ago_to_date(days):
    """Convert days ago to YYYYMMDD format."""
    date = datetime.now() - timedelta(days=days)
    return date.strftime('%Y%m%d')


def months_ago_to_date(months):
    """Convert months ago to YYYYMMDD format."""
    # Approximate months as 30 days
    date = datetime.now() - timedelta(days=months * 30)
    return date.strftime('%Y%m%d')


def years_ago_to_date(years):
    """Convert years ago to YYYYMMDD format."""
    # Approximate years as 365 days
    date = datetime.now() - timedelta(days=years * 365)
    return date.strftime('%Y%m%d')


def parse_month_day_year(month_str, day_str, year_str):
    """Parse month name, day, year to YYYYMMDD format."""
    month_map = {
        'jan': '01', 'feb': '02', 'mar': '03', 'apr': '04',
        'may': '05', 'jun': '06', 'jul': '07', 'aug': '08',
        'sep': '09', 'oct': '10', 'nov': '11', 'dec': '12'
    }
    
    month = month_map.get(month_str.lower()[:3])
    if month:
        day = day_str.zfill(2)
        return f"{year_str}{month}{day}"
    return None


def extract_youtube_links_from_html(html_content):
    """Extract YouTube video links, titles, and dates from HTML content."""
    soup = BeautifulSoup(html_content, 'html.parser')
    video_data = []

    # Look for JavaScript variables containing JSON data
    scripts = soup.find_all('script')
    
    for script in scripts:
        script_content = script.get_text()
        
        # Look for multiple possible JSON data variables
        json_variables = [
            ('var ytInitialData', 'var ytInitialData = '),
            ('window["ytInitialData"]', 'window["ytInitialData"] = '),
            ('ytcfg.set', 'ytcfg.set('),
            ('var ytInitialPlayerResponse', 'var ytInitialPlayerResponse = '),
        ]
        
        for var_name, var_prefix in json_variables:
            if var_name in script_content:
                # Extract JSON data
                start_idx = script_content.find(var_prefix) + len(var_prefix)
                if var_name == 'ytcfg.set':
                    # Special handling for ytcfg.set format
                    end_idx = script_content.find(');', start_idx)
                    if end_idx > start_idx:
                        json_str = script_content[start_idx:end_idx]
                        # Remove the first parameter if it's a string
                        if json_str.startswith('"') or json_str.startswith("'"):
                            comma_idx = json_str.find(',')
                            if comma_idx > 0:
                                json_str = json_str[comma_idx + 1:].strip()
                else:
                    # Standard variable assignment
                    end_idx = script_content.find('};', start_idx) + 1
                    if end_idx > start_idx:
                        json_str = script_content[start_idx:end_idx]
                
                if end_idx > start_idx:
                    try:
                        json_data = json.loads(json_str)
                        extract_youtube_data_from_json(json_data, video_data, var_name)
                        print(f"Successfully parsed data from {var_name}")
                    except json.JSONDecodeError as e:
                        print(f"Error parsing JSON from {var_name}: {e}")
                        continue
    
    # Method 2: If no videos found in JSON, parse HTML directly for rendered video links
    if not video_data:
        print("No videos found in JSON data, attempting HTML parsing...")
        # Look for video links specifically in video grid containers
        video_containers = soup.find_all(['ytd-rich-grid-media', 'ytd-grid-video-renderer', 'ytd-video-renderer'])
        video_links = []
        
        # If we don't find specific containers, fall back to all video links
        if video_containers:
            print(f"Found {len(video_containers)} video containers")
            for container in video_containers:
                links = container.find_all('a', href=re.compile(r'/watch\?v='))
                video_links.extend(links)
        else:
            print("No video containers found, searching all links")
            video_links = soup.find_all('a', href=re.compile(r'/watch\?v='))
        
        print(f"Found {len(video_links)} potential video links")
        
        for link in video_links:
            href = link.get('href')
            if href and '/watch?v=' in href:
                # Extract video ID from URL
                video_id_match = re.search(r'[?&]v=([a-zA-Z0-9_-]{11})', href)
                if video_id_match:
                    video_id = video_id_match.group(1)
                    
                    # Try to extract title more carefully
                    title = "Untitled"
                    
                    # Method 1: Look for aria-label which usually contains full title
                    if link.get('aria-label'):
                        aria_text = link.get('aria-label').strip()
                        # Remove duration from the end (e.g., "Title 54 minutes" -> "Title")
                        if ' minutes' in aria_text or ' seconds' in aria_text:
                            # Find the last occurrence of time pattern
                            import re as regex_module
                            time_pattern = r'\s+\d+\s+(minutes?|seconds?)\s*$'
                            cleaned = regex_module.sub(time_pattern, '', aria_text, flags=regex_module.IGNORECASE)
                            if cleaned.strip():
                                title = cleaned.strip()
                            else:
                                title = aria_text
                        else:
                            title = aria_text
                    
                    # Method 2: Look for title attribute
                    elif link.get('title'):
                        title = link.get('title').strip()
                    
                    # Method 3: Look for yt-formatted-string child
                    elif link.find('yt-formatted-string'):
                        title_elem = link.find('yt-formatted-string')
                        if title_elem and title_elem.get_text():
                            title = title_elem.get_text().strip()
                    
                    # Skip if title looks like a duration (e.g., "54:40", "1:26:57")
                    if re.match(r'^\d{1,2}:\d{2}(:\d{2})?$', title):
                        continue
                    
                    # Skip if title is too short (likely not a real video title)
                    if len(title.strip()) < 10:
                        continue
                    
                    if title and title != "Untitled":
                        url = f"https://www.youtube.com/watch?v={video_id}"
                        video_data.append({
                            'url': url,
                            'title': title,
                            'video_id': video_id,
                            'date': datetime.now().strftime('%Y%m%d')  # Use current date as fallback
                        })
                        # Clean title for console output to avoid encoding issues
                        clean_title = title.encode('ascii', errors='ignore').decode('ascii')
                        print(f"Found video from HTML: {video_id} - {clean_title[:50]}...")
    
    # Remove duplicates by video_id
    seen_ids = set()
    unique_video_data = []
    for video in video_data:
        if video['video_id'] not in seen_ids:
            seen_ids.add(video['video_id'])
            unique_video_data.append(video)
    
    # Sort video data by date (latest first)
    unique_video_data.sort(key=lambda x: x['date'], reverse=True)
    return unique_video_data


def get_target_file():
    """Get the target file path by browsing for .md or .txt files."""
    root = tk.Tk()
    root.withdraw()  # Hide the main window

    file_path = filedialog.askopenfilename(
        title="Select .md or .txt file to add URLs to",
        filetypes=[
            ("Markdown files", "*.md"),
            ("Text files", "*.txt"),
            ("All files", "*.*")
        ],
        initialdir=os.getcwd()
    )

    if not file_path:
        messagebox.showwarning("No File Selected", "No file was selected. Exiting.")
        return None

    return file_path


def detect_markdown_headers(file_path):
    """Detect markdown headers in the file and return a list of sections."""
    headers = []
    if os.path.exists(file_path):
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                lines = f.readlines()
                for i, line in enumerate(lines):
                    line = line.strip()
                    if line.startswith('#'):
                        # Count the number of '#' characters to determine header level
                        level = 0
                        for char in line:
                            if char == '#':
                                level += 1
                            else:
                                break
                        header_text = line[level:].strip()
                        headers.append({
                            'level': level,
                            'text': header_text,
                            'line_number': i + 1
                        })
        except Exception as e:
            print(f"Warning: Could not read file {file_path}: {e}")
    
    return headers


def read_existing_urls(file_path):
    """Read existing URLs from the target file."""
    existing_urls = set()
    if os.path.exists(file_path):
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                for line in f:
                    line = line.strip()
                    if line and not line.startswith('#'):  # Skip comments and empty lines
                        # Extract URL if line contains external markdown link format (with or without date prefix)
                        if line.startswith('- [') and '](' in line and line.endswith(')'):
                            url = line.split('](')[1][:-1]
                        elif line.startswith('- ') and ' - [' in line and '](' in line and line.endswith(')'):
                            # Handle format: "- YYYYMMDD - [Title](URL)"
                            url = line.split('](')[1][:-1]
                        elif line.startswith('[') and '](' in line and line.endswith(')'):
                            url = line.split('](')[1][:-1]
                        else:
                            url = line

                        # Normalize the URL
                        normalized = normalize_youtube_url(url)
                        existing_urls.add(normalized)
        except Exception as e:
            print(f"Warning: Could not read existing file {file_path}: {e}")

    return existing_urls


def select_section(headers):
    """Let user select which section to add URLs to."""
    if not headers:
        return None
    
    print("\nFound markdown headers in the file:")
    print("0. Add to end of file (no specific section)")
    
    for i, header in enumerate(headers):
        indent = "  " * (header['level'] - 1)
        print(f"{i + 1}. {indent}{header['text']}")
    
    while True:
        try:
            choice = input(f"\nSelect section (0-{len(headers)}): ").strip()
            choice_num = int(choice)
            if choice_num == 0:
                return None  # Add to end of file
            elif 1 <= choice_num <= len(headers):
                return headers[choice_num - 1]
            else:
                print(f"Please enter a number between 0 and {len(headers)}")
        except ValueError:
            print("Please enter a valid number")


def find_section_end(file_path, selected_header):
    """Find the line number where the selected section ends."""
    if not selected_header:
        return None  # Will append to end of file
    
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            lines = f.readlines()
        
        # Find the next header at the same or higher level
        for i in range(selected_header['line_number'], len(lines)):
            line = lines[i].strip()
            if line.startswith('#'):
                # Count header level
                level = 0
                for char in line:
                    if char == '#':
                        level += 1
                    else:
                        break
                
                # If we find a header at the same or higher level (lower number), this is where the section ends
                if level <= selected_header['level']:
                    return i
        
        # If no next header found, section goes to end of file
        return len(lines)
    
    except Exception as e:
        print(f"Error reading file: {e}")
        return None


def write_urls_to_file(file_path, video_data, existing_urls, selected_header=None):
    """Write URLs to the target file, optionally inserting into a specific section."""
    new_urls = []
    duplicate_count = 0

    # Filter out duplicates and skip "Untitled" videos (likely Shorts)
    for video in video_data:
        if video['url'] not in existing_urls:
            # Skip videos with generic "Untitled" titles (likely Shorts or hidden videos)
            if video['title'].strip().lower() in ['untitled', '']:
                print(f"Skipping untitled video: {video['url']}")
                continue
            new_urls.append(video)
        else:
            duplicate_count += 1

    if not new_urls:
        print(f"No new URLs to add. Found {duplicate_count} duplicates.")
        return 0

    try:
        # If no specific section selected, append to end of file
        if not selected_header:
            with open(file_path, 'a', encoding='utf-8') as f:
                if os.path.getsize(file_path) > 0:
                    f.write('\n')  # Add newline if file is not empty

                for video in new_urls:
                    # Write in template format: - YYYYMMDD - Video ID - [Video Title](Video URL)
                    f.write(f"- {video['date']} - {video['video_id']} - [{video['title']}]({video['url']})\n")
        else:
            # Insert URLs into the selected section
            with open(file_path, 'r', encoding='utf-8') as f:
                lines = f.readlines()
            
            # Find where to insert the URLs
            section_end = find_section_end(file_path, selected_header)
            if section_end is None:
                section_end = len(lines)
            
            # Prepare the new URLs to insert
            new_lines = []
            for video in new_urls:
                new_lines.append(f"- {video['date']} - {video['video_id']} - [{video['title']}]({video['url']})\n")
            
            # Add a blank line before the URLs if the previous line is not empty
            if section_end > 0 and lines[section_end - 1].strip():
                new_lines.insert(0, '\n')
            
            # Add a blank line after the URLs if the next line is not empty
            if section_end < len(lines) and lines[section_end].strip():
                new_lines.append('\n')
            
            # Insert the new lines
            lines[section_end:section_end] = new_lines
            
            # Write the modified content back to the file
            with open(file_path, 'w', encoding='utf-8') as f:
                f.writelines(lines)

        print(f"Successfully added {len(new_urls)} new URLs to {file_path}")
        if selected_header:
            print(f"URLs added to section: {selected_header['text']}")
        if duplicate_count > 0:
            print(f"Skipped {duplicate_count} duplicate URLs")
        return len(new_urls)

    except Exception as e:
        print(f"Error writing to file {file_path}: {e}")
        return 0


def remove_duplicates_from_file(file_path):
    """Remove duplicate URLs from the file and return the count of duplicates removed."""
    if not os.path.exists(file_path):
        return 0
    
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            lines = f.readlines()
        
        seen_urls = set()
        new_lines = []
        duplicates_removed = 0
        
        for line in lines:
            original_line = line
            line_stripped = line.strip()
            
            # Skip empty lines and headers
            if not line_stripped or line_stripped.startswith('#'):
                new_lines.append(original_line)
                continue
            
            # Extract URL from line
            url = None
            if line_stripped.startswith('- [') and '](' in line_stripped and line_stripped.endswith(')'):
                url = line_stripped.split('](')[1][:-1]
            elif line_stripped.startswith('- ') and ' - [' in line_stripped and '](' in line_stripped and line_stripped.endswith(')'):
                # Handle format: "- YYYYMMDD - [Title](URL)"
                url = line_stripped.split('](')[1][:-1]
            elif line_stripped.startswith('[') and '](' in line_stripped and line_stripped.endswith(')'):
                url = line_stripped.split('](')[1][:-1]
            elif line_stripped.startswith('http'):
                url = line_stripped
            
            if url:
                normalized_url = normalize_youtube_url(url)
                if normalized_url not in seen_urls:
                    seen_urls.add(normalized_url)
                    new_lines.append(original_line)
                else:
                    duplicates_removed += 1
            else:
                # Keep non-URL lines as is
                new_lines.append(original_line)
        
        # Write back to file if duplicates were found
        if duplicates_removed > 0:
            with open(file_path, 'w', encoding='utf-8') as f:
                f.writelines(new_lines)
        
        return duplicates_removed
        
    except Exception as e:
        print(f"Error removing duplicates from {file_path}: {e}")
        return 0


def main():
    """Main function to extract YouTube URLs from clipboard HTML and save to markdown."""
    print("YouTube Channel Videos URL Extractor from Clipboard")
    print("=" * 55)
    
    # Read HTML content from clipboard
    try:
        html_content = pyperclip.paste()
        
        if not html_content:
            print("Error: Clipboard is empty")
            print("Please copy HTML content from a YouTube channel's videos page first.")
            return
        
        if not html_content.strip().startswith('<'):
            print("Error: Clipboard doesn't contain HTML content")
            print("Please copy the full page HTML from a YouTube channel's videos page.")
            return
            
    except Exception as e:
        print(f"Error reading from clipboard: {e}")
        return
    
    # Check if this appears to be a homepage/recommendations page
    if 'FEwhat_to_watch' in html_content and 'channel.videos' not in html_content:
        print("WARNING: This appears to be YouTube homepage/recommendations HTML.")
        print("   For best results, use HTML from a specific channel's videos page.")
        print("   (e.g., https://www.youtube.com/@channelname/videos)")
        print()
    elif 'channel.videos' in html_content:
        print("Detected channel videos page - good!")
        print()
    
    # Extract YouTube links
    print("Extracting YouTube links from HTML...")
    video_data = extract_youtube_links_from_html(html_content)
    
    if not video_data:
        print("No YouTube video links found in the HTML content.")
        print("This might be because:")
        print("- The HTML is from the wrong page type (homepage instead of channel)")
        print("- The filtering is too aggressive")
        print("- The page structure has changed")
        return
    
    print(f"Found {len(video_data)} YouTube video(s):")
    for i, video in enumerate(video_data, 1):
        # Clean title for console output to avoid encoding issues
        clean_title = video['title'].encode('ascii', errors='ignore').decode('ascii')
        print(f"  {i}. {clean_title[:60]}{'...' if len(clean_title) > 60 else ''}")
        print(f"     {video['url']}")
    
    # Get target file
    file_path = get_target_file()
    if not file_path:
        return
    
    # Check for markdown headers and let user select section
    headers = detect_markdown_headers(file_path)
    selected_header = None
    if headers:
        selected_header = select_section(headers)
    
    # Read existing URLs
    existing_urls = read_existing_urls(file_path)
    print(f"\nFound {len(existing_urls)} existing URLs in {os.path.basename(file_path)}")
    
    # Write new URLs to file
    added_count = write_urls_to_file(file_path, video_data, existing_urls, selected_header)
    
    # Remove duplicates from the entire file
    duplicates_removed = remove_duplicates_from_file(file_path)
    
    if added_count > 0:
        print(f"\nSuccessfully processed! Added {added_count} new URLs to:")
        print(f"   {file_path}")
    else:
        print(f"\nNo new URLs were added to {file_path}")
    
    if duplicates_removed > 0:
        print(f"Removed {duplicates_removed} duplicate URLs from the file")
    
    input("\nPress Enter to exit...")


if __name__ == "__main__":
    main()