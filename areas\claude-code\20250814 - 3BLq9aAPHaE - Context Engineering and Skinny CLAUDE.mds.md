---
title: Context Engineering and Skinny CLAUDE.mds
artist: Beachy
date: 2025-08-14
url: https://www.youtube.com/watch?v=3BLq9aAPHaE
---

(t: 0) I want to talk a little bit about context engineering. It's been a topic that's been popular in the broad community of AI and software development for the last little bit here after (t: 10) <PERSON><PERSON><PERSON> responded to that tweet talking about it and trying not to quote another term. I think that it's still front of mind for everybody, (t: 20) but it's still getting a little bit lost in the weeds in terms of the terminology. I want to throw my two cents in. I think I feel that I have some value to add for what it's worth. (t: 30) I'm a CTO here in Australia working with a FinTech company and I've been in the software industry for well over 10 years and have been working around machine learning or looking at it or engaged with the space for a little while. (t: 40) Then I haven't necessarily always been actually developing actively with it, but I am now and have been paying attention to it recently. (t: 50) I have this sentiment that context engineering is the term that's getting butchered and used in ways that it doesn't necessarily. I don't think it was originally intended for, (t: 60) but I do think that it's something that we should all be paying attention to. I think the long and short of it is that when we're working with LLMs, we should try to give them the information that they need to make the decision that they need to make. (t: 70) No more than that. <PERSON>'t aim to have the right amount of information rather than far too little or far too much. (t: 80) I'm going to touch really quickly on in a very high level, how, how LLMs work because it helps put into perspective that the reason that context engineering sort of is important. (t: 90) But if you're broadly familiar with transformer layers and how that architecture works, and this is part of the video that you could skip, (t: 100) but effectively, I put this together in v0, by the way, if you haven't had a play, it's pretty cool. But effectively, when we have words, (t: 110) we take the tokens, I say words here, but it's tokens more specifically, they get translated to, vectors in high dimensional space, with a semantic understanding, (t: 120) a positional understanding, and then like they use attention to change each of these, which suffice to say that we kind of take the language that we interpret, (t: 130) and use the same kind of underlying processes that are happening in our minds to turn them into vector or mathematical representations of the words. And so in this little diagram, (t: 140) each of these little lights represents a value between, well, a, a non, a real number, and vector math is applied to each of them. (t: 150) I think I believe in the case of semantic and positional, it's just additive. And that's actually true for attentional as well. I've got the multiplication symbol here, but ultimately you take what would have been the language looked up in a graph against a vector ID or a vector that goes into a feed forward layer. (t: 170) And there's probably a bit more complexity to it. And like I said, I'm not necessarily the, a master of this space, but I know enough, you know, to infer some things about context engineering. (t: 180) So, but the reason that I use this little indicator is because, you know, in this little example, we've got 20 nodes and the word, (t: 190) the lazy dog or the words, the lazy dogs, you know, light up a fistful of nodes here. And that's sort of supposed to loosely be indicative of, or sort of representing vector space being kind of lit up and then being applied into a limited number of, (t: 200) you know, of, of, of, of, of points that we can sort of reference. And, and these then go into this feed forward matrix, (t: 210) which is, again, it's just matrix math. That's kind of applied. And the weights that are in between these sections. And if you want to learn more about transformer feed forward matrices or for neural nets or for how like wordpressing, (t: 220) word processing works three brown, one blue, do a great video on it. (t: 230) I'll link it as well. But the point is that you kind of have this limited space that you can put information in. And if you put more words into that limited space, each of these dots would get, (t: 240) you know, like some more lights would light up as you get more words and you will dull others to help show the differentiation of like the content that's been put in. (t: 250) But at some point, any given word is getting kind of dulled out by the other words that are in the sentence. And so like I tried to, I tried to indicate what this might look like by having loads of words. (t: 260) And for some reason, VZero wants to flip these words. Sometimes I hasn't done it for me now, which is nice. But you know, if all of the lights are lit up and, (t: 270) and you know, the reality is it would just be like different, different lights lit up different amounts. But you know, if there's some level of noise happening in the system such that when you get an output, (t: 280) it looks less deterministic or like less sort of pointing specifically to one place. Then even though we only have three points here that we really care about, (t: 290) they kind of get lost in the noise. And so like a very quick example of this talking, just talking to an LLM might be, you want to say, (t: 300) uh, I, I went over to Japan's every Australian. Apparently now I went over to Japan and I ate lots of food and I walked a lot. Um, and so I'd really like to try to mimic the diet that I had over there because obviously it helped me lose weight. (t: 310) Um, and then, but you know, you have this long conversation about how much you enjoy being over there and how you went skiing and rah, rah, rah. And this kind of represents filling the context with that, (t: 320) with the chat, but you talking about your entire history. And then at the end you kind of say like, Hey, can you make me, um, a meal plan that matches what it was like to be living in Japan? Um, (t: 330) and the three dots really should be Japanese food, lose weight and, um, make a meal plan please. And then maybe you say like a period of the meal plan that you want, but it's got all this other noise in here. (t: 340) And so, you know, even if that, um, by the nature of the way that sort of semantic transforming happened, this section up here, you know, attention is the concept. (t: 350) Um, attention is all you need is that the paper that was handled by Google, I think in 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, something like that, which kind of was the first real pointing towards, um, (t: 360) what now became the, the math and behind neural processing that we use as LLMs, IE, chat GPT. Um, that's a lot of letters in that sentence, (t: 370) but, um, the, you know, basically we end up this math that gets done should kind of infer that that is ultimately what that entire sentence was meant to be, (t: 380) but there is still a lot of other noise. And so even if the important bits are prevalent, uh, present, um, and they might be more prevalent than the other stuff, there is still more stuff. (t: 390) There's more just noise in, in the system. And so, um, ideally you can get to the place where you feed it exactly the right information. And so now when we take this to the idea of programming, (t: 400) which I think, you know, it's the space that I am sort of spending the most time with LLMs. And it's the space that I think that we have, um, in the immediate future there, it's the sort of most kind of push the needle, (t: 410) you know, push the lever kind of really make the different space. Um, there, and, and so I've been paying a lot of attention to it and I've spent (t: 420) enough time working with the code programs or coding agents rather like code code specifically, um, to kind of get a sense of the things that are really, um, you know, moving, moving the needle there. (t: 430) So, um, the biggest thing I think is that every time you write a prompt into code code into these agenting models broadly, you know, we, we're all putting Claude MD files in the, in the top of our folder to give it some instruction about, (t: 440) um, how commands that you can run the kind of broad folder structure that you can run. Um, you have, um, you should be putting in there like why this project exists. Um, (t: 450) if you don't have that already, go, go pop that in there. Um, I found also that I'm spending some time putting in a bit of, um, like, you know, semantic logical business logic that I have so that when I type a (t: 460) prompt that I don't have to kind of do the translation into the specific files every single time, it's, it's often worth doing anyway, because, um, I'm sure lots of people have experienced this, (t: 470) but the smaller you make the task, the kind of better it is at, um, going and executing it. If you really point it, you say like, Hey, go to the email center. Dot pi and alter the function, blah, to this other change. (t: 480) It'll, it'll go and nail that. And if you kind of just say there's a problem with the emails, it does have more, um, difficulty kind of narrowing in there. But, um, I, you know, what I wanted to indicate is that based on this context engineering (t: 490) being a problem, don't make your Claude MD really, really long so that you've got, you know, don't have instructions on how to do TDD in there as well as here's the (t: 500) massive folder structure. Here's what the home.jsx, here's what the login.jsx does. Um, this is the problem that we're often having over here. Please don't forget to make sure that when you commit, don't put Claude in the commit message. (t: 510) Um, all of this sort of stuff. If you put all of that in the file, it will forget it because it's going to get drowned out by, um, the rest of the information that you put in. (t: 520) And I think more importantly, you have a, you know, 2000 token long Claude MD file and you write a prompt that just says, (t: 530) Hey, can you go into the login page and update the button to be blue? The only thing you care about in that moment is getting into that file. It understanding what that, you know, what blue might infer if, (t: 540) if that color is specific and then being able to just make that change. And now it's sort of overburdened with, you know, let's say 500 tokens about, you know, how the form handler works. (t: 550) Um, and so the, the easiest win and you know, I'm not doing anything special here. Anthropic literally have this in that documents. I just think that it's kind of underutilized. (t: 560) Go and make your Claude MD files thinner, go and make them as thin as you, as you functionally can. Uh, I have been putting, um, a brief description of what the project is very broadly, (t: 570) how, how to navigate it. And I, I mean only in the sense that they can kind of go, if you need to deal with the interface stuff, it'll be in the pages section. And if you need to deal with a user service, (t: 580) go to the user service section. And then the Claude MD is set up in those folders. As it navigates into the folders, it'll actually pick that up. That's, that's part of the way that the tool works. (t: 590) And so you can have a bit more information here. You can get us, lower level as talking about the way that the functions in here work, if you'd like. Um, and ideally you, you know, you can talk to Claude at, (t: 600) at the top level, ask it a very broad question and it'll sort of navigate to the right place. Um, and, and it'll pull in the context as it needs it rather than kind of having it all up front. And then your little question about the blue button is fed 500 tokens saying, (t: 610) Hey, make sure you do TDD and then kind of go and do it at the pages section. Um, I would even go as far as, I think if this was my project, (t: 620) this is just a dummy that you spits out from, uh, with the V zero, um, mapping, but I would even go as far as to say, if this was my project, I would probably take the pains to put each of these files in their own sub (t: 630) folder, almost exclusively just to be able to stick a claw dot MD file in there and then describe roughly what they're, what they are and how the components go there. And now you won't always hit them because sometimes it might want to go (t: 640) straight to a component, but you know, if it's set up properly and you ask and you kind of prompt it correctly, it will loop that way. Um, and it'll pick up the context. (t: 650) That's pretty much it. Um, put, thin, Claude MDs or out throughout your files. Be conscious of the way that context works. Um, I think that's it for now. Have a good one. Thanks.

