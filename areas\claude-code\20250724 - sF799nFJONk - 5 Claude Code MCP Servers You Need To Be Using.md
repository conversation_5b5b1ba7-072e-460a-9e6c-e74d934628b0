---
title: 5 Claude Code MCP Servers You Need To Be Using
artist: <PERSON>
date: 2025-07-24
url: https://www.youtube.com/watch?v=sF799nFJONk
---

(t: 0) All right, the comments on my last MCP video were pretty wild, and shout out to the guy who said he doesn't understand how my channel isn't bigger. You the real MVP. But times have changed. More (t: 10) MCP servers, more capabilities, more fun stuff for us to get our vibe-cody hands on. So today (t: 20) I'm delivering exactly that. My top five clawed code MCP servers that will not only help you (t: 30) develop faster and better, but also avoid some critical vulnerabilities that can tank a SaaS company overnight. Now I've personally used every single one of these in production, and these are (t: 40) not your basic GitHub MCP server type of tools. So make sure you stick around for all of them, (t: 50) because they are all pretty cool. That being said, let's dive in. So quick 15 second refresher for anyone that is new here. MCP stands for Model Context Protocol. And the way to think about this is to (t: 60) think about the way the internet used to be. Because the internet we know today is not always (t: 70) what the internet was. It used to be, for example, as an analogy, if you were on a Mac, you could only talk to people on Macs. If you were on a window machine, you could only talk to people on Windows. And that all stopped when they created a (t: 80) unit called the Microsoft Internet. And that's what we're talking about. So let's dive in. So that's what we're talking about. So let's dig into what we know today with AI tools. And interesting about this is that AI tools is impacts on how people use AI tools and how people use AI tools to communicate with each other. And you've heard about the fact that this tool was used to be a simplified way for everyone to be able to talk to each other. And then boom, funny cat videos everywhere. Well, that's what coding with AI tools used to be. And now MCPs (t: 90) have stepped in and unified the way that those tools and models can communicate with each other, which is driving the explosion that we're seeing today. You can kind of think of it like an app (t: 100) store to an iPhone. Pretty big game changer. So first up on the list, we have a tool called (t: 110) Ref, which is an MCP server that can communicate with people on Mac. And that's what we're talking that allows you to pull in up-to-date documentation on any framework or library. Meaning, gone are the days of a language model making up functions that don't exist (t: 120) or making up patterns that don't exist and just breaking your app, leaving you with a bunch of bloated code that you don't really understand. (t: 130) But I need to address something right off the bat. Context 7, which I talked about in my last video, is still a pretty solid choice and it gets the job done. (t: 140) But here's the annoying reality. Context 7 loads the entire source documentation into your context window, which is very inefficient and can be costly. (t: 150) Think about using a tool like Cloud Opus, for example, or Cloud Code, which can be quite expensive. You do not want to load up a bunch of context that you don't necessarily need. (t: 160) Now, Ref completely flips this approach. So instead of going out and downloading the entire documentation, it can intelligently go out and only find the functions that are relevant for what it is that you are trying to do. (t: 170) Let's look at an example. So let's say in this project, I'm working on building a chat component that is powered by OpenAI and I need to understand the latest parameters (t: 180) for making the chat completions and streaming responses back so I can have that streaming effect inside of the chat interface. If I was using Context 7 to do this, I would be loading a ton of context into this window. (t: 190) Now, when I make this request for Ref, we can see that it is going through and it is intelligently searching and pulling specific documents. (t: 200) So I can see that I need to have the documentation based on what I need. So it's pulling the chat completion APIs. It's pulling the response streaming APIs. (t: 210) It's pulling some APIs involved with, again, the completions API. And so it's intelligently querying the specific sections that I actually need from the documentation (t: 220) instead of just loading in massive context. And so once this finishes up, we can see we have the entire relevant chunks of the documentation (t: 230) that we need. We need to actually implement this. And so the result here, pretty astounding. You go from burning through API quotas in a few hours because of these massive context windows (t: 240) to intelligently using only the things that you actually need. Now, according to their own metrics, Ref reduces documentation related token usage by up to 85%. (t: 250) So it keeps you in the zone by giving you exactly what you need. Nothing more, nothing less, and for a lot cheaper. (t: 260) But even with the, the best documentation, you may inadvertently introduce kryptonite into your project. And that's why my favorite new tool of the year is tool number two. (t: 270) And so tool number two is called Semgrip. Now, let me hit you in the face for a moment with the harsh reality. (t: 280) Most vibe coders know absolutely nothing about security. We're focused on building all this cool stuff super fast, but the idea of it being, (t: 290) a secure application is a serious afterthought. If it's even thought of at all, I've seen my own projects slapped together security vulnerabilities (t: 300) that would make Sam Altman weep. But again, here is the reality. According to recent data, up to 74% of applications have at least one security vulnerability. (t: 310) And that's with people that are trained software developers. So when you're moving fast, you are most likely creating security vulnerabilities (t: 320) that you are not. Are not aware of. And Semgrip changes that. So Semgrip has a database of over 2000 security rules and can scan millions of lines of code in a few seconds. (t: 330) But more importantly, it understands the context of your application, not just the syntax. So let me show you what I mean. So last week I was building a user authentication system and wrote what I (t: 340) thought was pretty standard code, just a simple clerk integration for this react native app that I'm building. But what I did was I came through. (t: 350) And I asked Semgrip to scan my project for vulnerabilities. Now, after it ran it scan, it found a few problems. (t: 360) No rate limiting in place. We have wild cards that are allowing requests to be made from any host. And there's some ambiguity around whether or not my database password is being stored properly. (t: 370) So three potential security disasters just lying in wait, waiting to happen. But here's what I love about it. It doesn't just tell you, yeah, what you did right and what is wrong. (t: 380) But what specifically you need to fix. So it's not just saying, hey, you don't have rate limiting in place. It's saying, hey, your code is structured well, but you need to integrate Redis. It's not just saying, (t: 390) hey, you're allowing requests from too many hosts. It's saying you need to restrict the allowed host field, right? So it's giving me actual practical implementation guidelines that I can just give the language model and let it implement those things for me. (t: 400) And all accessible via a basic one line MCP server command, which is awesome. (t: 410) So when you are shipping fast, having things like this built into your workflows should be a non negotiable. So tool number three is called pieces, (t: 420) and it solves one of those problems that drives me absolutely crazy in vibe coding workflows, which is that we're working in so many tools, (t: 430) experimenting with different technologies, trying to do all these different things that we often forget how we solved specific problems in the first place. Now, (t: 440) I will say this is the one tool on this list that I do not run inside of cloud code because it has its own desktop app, which I will show you in a second. Now, if you vibe code often, (t: 450) odds are you find yourself forgetting specific solutions to specific problems, which is why you find yourself debugging the same problem over and over and over again. (t: 460) Now, pieces creates what you could call a developer memory graph, meaning it not only remembers the problem that you ran into, (t: 470) and the, and the solution to that problem, but all of the steps and errors that you encountered along the way. So here's a perfect example for whatever reason, cursor utterly fails at doing basic tailwind CSS configurations inside of vanilla, (t: 480) create react apps where everything should be pretty straightforward, but then you just hit this same post CSS configuration nightmare every single time you try to kick off a project. (t: 490) So the thing that is really cool, (t: 500) right? The thing that is really cool about pieces is that it keeps track of everything you do inside of your ID, inside of your terminal, or even inside of anywhere else you can figure it to watch on your computer. (t: 510) Now, the thing that is really cool is that in this timeline, we can go back to specific problems that we had, and we can actually chat with pieces about that problem and how we solved it. (t: 520) So now we can again, start this chat and say, what was the exact error we ran into? And then what was the solution? So, (t: 530) we can actually go back to the solution and it documents everything that happened, right? So the exact error, why the error happened and what the different solutions were that actually led us to that solution. (t: 540) And so we can just copy this solution now, next time we run into it and give the context of that solution to Claude. (t: 550) Now, if your brain is running on this one, like mine did, this is helpful beyond just the scope of solving problems. This could be used for how you, (t: 560) how you connect solutions, how you think about different features, how you do certain product design, UX UI, the sky's the limit in terms of how you could actually use this thing. So where it gets really powerful is that pieces uses AI to connect related concepts across your development history. (t: 570) So if I, for example, were to come in and type in tailwind, I'm going to see a timeline of every single time I've run into a situation where I was dealing with tailwind problems or post CSS configuration problems, (t: 580) and I'm going to see how that might be. (t: 590) So it's like having a conversation with every version of yourself that has ever run into a situation and solves your way through that situation. So the time savings are massive, but more importantly, it helps you build on your past discoveries instead of constantly reinventing the wheel. (t: 600) But even then, sometimes we need to solve a problem that ultimately lies outside of the context of what Claude code or cursor were actually trained on, (t: 610) which is why our next tool is Exasearch. So Exasearch is basically a, competitor to perplexity, (t: 620) except in my opinion, it does a better job at answering developer oriented questions and understanding the real context of what it is that you want. (t: 630) Now I use this most for staying up to date with development, best practices and patterns. For example, if I was going to try and build an agentic feature into this app using crew AI, (t: 640) I would want to make sure that I have up to date documentation on crew AI, and that I understand modern, best practices for building an agent. (t: 650) Traditional search tools are optimized for general queries, which means there may be a result that is very relevant to what you're asking. (t: 660) That is just buried somewhere. Google is engineered to show you the most popular result, not necessarily the one that is going to answer your question the best, (t: 670) because those two worlds are not always the same thing. So Exa is specifically optimized for developer queries. It actually ranks the results, based on technical relevance and recency of the page. (t: 680) Here is a perfect example from last month. So I'm building an agentic lookup functionality for a project I'm working on, and I need to understand the current best practices for agentic rag implementation. (t: 690) Now, this idea of agentic rag is like kind of new, kind of bleeding edge, and it's changing a lot. (t: 700) Now, if I were to Google agentic rag best practices, I get a bunch of generic AI articles, ads, basically, marketing, content. Now, this is not helpful when I need architectural patterns and decision-making guidance. (t: 710) But now when I use Exa search to research and understand modern best practices for agentic rag, (t: 720) focusing on architecture patterns and implementation using crew AI, it immediately surfaces more technical details. Technical blog posts from engineers who have actually built this type of stuff. (t: 730) Recent GitHub discussions, detailed implementation guides, even academic papers. (t: 740) So now the real power comes when we can take all of these search results and pass the context directly into Cloud Code before it starts building out that agentic functionality. (t: 750) So instead of Cloud Code working on potentially outdated training data, it's working on the most current technical discussions and proven implementation patterns. (t: 760) And so this creates this great workflow where Exa can find the most up-to-date documentation, and then Cloud Code can work on implementation patterns. And so this creates this great workflow where Exa can find the most up-to-date documentation, and then Cloud Code can work on implementation patterns. and then Cloud Code can work on implementation patterns. (t: 770) And so this creates this great workflow where Exa can find the most up-to-date documentation, and then Cloud Code can work on implementation patterns. But once I've put these things together, then you'll see that quickly I'm performing a whole lot of intend buildquery議 because the idea or having anureau called (t: 790) products there. I want to be blue. So let me tell you this. MCP server called Playwright, which is critical to a process that I call AI-graded self-improving (t: 800) UIs. So here's the problem. When you're vibe coding and you are developing things fast, (t: 810) there's not a really good systematic way to ensure that what you are building meets those fundamental guidelines you had for your UX and your UI. You might think that your new layout (t: 820) should be looking better, but is it really? And even if you do have feedback for the system, simply describing in text what is wrong and what should be fixed doesn't actually work that well. (t: 830) And so Playwright helps us change this fundamentally. Here is how it works. So Playwright can actually take screenshots of your UI at different points in time, (t: 840) and then feed those screenshots into the language model and have the model grade itself based on (t: 850) how well it conformed to standard UX and UI guidelines. And it's a bonus, if you provide those guidelines yourself. So in this video, we can see this as a basic process (t: 860) where we have this dashboard interface. And after each major change that we make, Playwright can open up the browser, automatically take a screenshot, and then grade the results of (t: 870) that screenshot against our UX guidelines, our UI guidelines, and our style guide. (t: 880) And this is where it's really cool. The LLM is not just critiquing, it is first grading itself, giving an evaluation of its work, and then moving on to actually (t: 890) optimize the outputs. So it might actually grade it and say something like, the navigation hierarchy is unclear because the primary buttons don't have enough visual weight, (t: 900) or the form layout is creating too much cognitive load because of how you grouped your fields. And now even better with Cloud Code, we can set up Playwright to do this automatically using (t: 910) Cloud Code commands. Now I go through that. In detail in the video that I will link somewhere around here. So the result is that you have this (t: 920) continuous feedback loop where your UI is being constantly evaluated and improved by a senior (t: 930) product designer. I've been using this approach for a while now, and the results are pretty dramatic. My UIs come out more polished, more accessible, and they overall just feel a lot (t: 940) better. So the time savings are huge, but more importantly, it helps us create apps that are not just fun. So if you're a product designer, you're going to want to make sure that you're not just making sure that you're not just making sure that you're not just making sure that you're not making sure that you're not just making sure that you're not just making sure that you're not actually functional, but actually look nice and feel good to use. So when you are vibe coding, (t: 950) this type of intelligent feedback cycle stops you from making UIs that are functional, but kind of suck to use. So there you have it, five MCP servers that will help redefine your approach to producing (t: 960) production-ready code. So to break this down in terms of impact, Ref is going to help you cut (t: 970) down your token usage when you're dealing with documentation by 85%. Semgrep is going to help you a lot more. So if you're a product designer, you're going to want to make sure that you're basically (t: 982) and tank your project. Pieces is going to help you turn those hours of debugging into minutes of context retrieval. Exa is going to help make sure you have the most up-to-date relevant context as (t: 990) possible. And Playwright is going to help you build an autonomous UI improvement system. So the big theme here is building systems that help you maintain the vibe coding flow (t: 1000) while building more secure程s for your content. And that's going to help you build a more more polished apps. So drop a comment and let me know which of those tools you're going to (t: 1010) actually integrate into your workflow and that you are excited to use. That is it for this video. I will see you in the next one.

