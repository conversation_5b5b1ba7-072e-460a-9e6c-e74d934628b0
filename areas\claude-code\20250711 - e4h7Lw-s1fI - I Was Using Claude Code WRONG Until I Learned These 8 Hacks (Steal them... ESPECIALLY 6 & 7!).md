---
title: I Was Using Claude Code WRONG Until I Learned These 8 Hacks (Steal them... ESPECIALLY 6 & 7!)
artist: AI Oriented Dev
date: 2025-07-11
url: https://www.youtube.com/watch?v=e4h7Lw-s1fI
---

(t: 0) Hey everyone, welcome to another tutorial on how to use Cloud Code. Now in this video, I'll be showing you 8 workflow hacks that every Cloud Code user has to know about. (t: 10) Now I've been using Cloud Code for a while now. I wish that somebody had actually told me about some of these tips before I got started because it would have saved me a lot of time and headache and helped me to resolve issues a lot faster. (t: 20) Now instead of just telling you what these hacks are, I'm going to show you how to use all 8 of them with an actual working project. (t: 30) So that you can see how to incorporate these into your workflows. This is the app that we're working on today. It's just a very simple to-do list app. However, you'll see there are a couple of problems with it. (t: 40) For example, none of these buttons actually work. And when I try to add a new task to my list, like buy groceries, and I click this, you see it doesn't work either. All it does is just log it to the console. (t: 50) And what hope would we have that the theme toggles would actually be working? Just imagine somebody has handed you this code base and nothing works and you need to fix this up because you've got the data. (t: 60) Well, in today's tutorial, I'll be showcasing how you can get from this broken app state into this working version. So as you can see, if I do buy groceries now, it actually works. The task list updates correctly. (t: 70) The toggle theme button not only works, but looks fantastic as well. So we'll be getting from that broken state to this fully working state using ClockCode and these 8 hacks. (t: 80) And you definitely want to stay till the end of this video because I'm pretty sure that the last hack will be something that you wouldn't have known about. Now, if you want to get hold of this project, including the full source code, and the lesson plan, you might want to check out my AI-oriented Insiders Club. (t: 90) It's completely free to join and you can sign up by going to this link over here or in the description below. Within it, you'll be able to get behind the scenes material and resources (t: 100) related to this ClockCode series, as well as all my other videos, including the Agent Development Kit series. As you can see, I publish previews of the entire lesson plan before it goes live. (t: 110) So Insiders like you will be able to see what's up and coming before the video goes fully public. You will be able to get access to my private repositories, such as the one with the Google Cloud. And of course, the one related to today's episode. (t: 120) It includes the entire full source code of the project, including all the prompts, as well as the custom commands that you will see me using in this tutorial. So if you're interested in getting hold of all of this, (t: 130) why not join the Insiders Club by heading over to the link in the description below. It's completely free and I'll see you on the inside. All right. So without further ado, let's get started with today's tutorial. (t: 140) Right. So the very first thing we're going to do is take a look at the project that we are working with today. So I'm just run NPN run dev. And just imagine that you have received this project from somebody. (t: 150) And we're going to take a look at how it functions. And it looks pretty good at first glance. It's a to do app allows you to add a new to do. (t: 160) It's got a list of existing to do's and that's even a dark mode. However, there are a couple of problems with this project. The checkboxes don't actually work. (t: 170) If we try to add a new to do such as buy groceries, it doesn't work. And neither does the theme settings work. All right. So this is where we're starting off from. But no worries. (t: 180) I'll be showing you how to fix this app up today in cloud code. And while I'm doing so, I will demonstrate to you the crucial hacks that you need to know whenever you're working on a project with cloud code. The very first thing you should always do when you're starting a new project is to start cloud code itself. (t: 190) When you type cloud, it's going to initialize. And in a lot of cases, it will say that it needs permissions to actually run within the folder. (t: 200) I've already done it before. So that's why I've managed to skip it. The first thing you always need to do is to run the initialize command or init command. And what that does is it will say, And what this step actually does is that cloud code is going to analyze the existing code base. (t: 210) It's then going to create something called a cloud.markdown file that will then summarize all of its findings and provide a lot of context that this project consists of as it stands. (t: 220) This is really important because with this now, it's going to be in a state where anybody that subsequently uses cloud code on this project will be able to look at the cloud.md file (t: 230) and immediately get an understanding of what it is. The technology choices that has been taken, where all the files are, where all the photos are. (t: 240) And this is always your number one starting point whenever you're working with cloud code. So it's done now. I'm going to allow cloud code to always make changes to cloud.md. And once it's done, it's probably worthwhile taking a look. It's actually done a project overview. It's looked at all the commands. It understands the architecture that has been taken and all the key components. It even knows what this app was meant to do, which is to demonstrate the cloud code workflows. And it's done. So now, I'm going to go back to my cloud.md. (t: 250) And I'm going to go back to my cloud.md. And I'm going to go back to my cloud.md. And I'm going to go back to my cloud.md. And once it's done, it's probably worthwhile taking a look. It's actually done a project overview. It's looked at all the commands. It understands the architecture that has been taken and all the key components. It even knows what this app was meant to do, which is to demonstrate the cloud code workflows. And there are a bunch of development notes as well. And what I like to do whenever working with markdown files is to actually preview it (t: 260) a little bit nicer. So you can do control shift P and then open the command palette and do markdown and then open preview. This will give you a rendered version. And then you can see that the app is now working. So you can see that the app is working. So you can see that the app is working. So you can see that the app is working. So you can see that the app is working. So the app is working really well. (t: 270) And I'd like to show you we've also created a full Hired IO liquid pen plugin necesitations and just about anything that's needed to be written. And then that comes together to create a visual viewer that pops back in could be something called a hairOUTPUT. And again, I don't mind if you interrupt it via好. 姫 Can Can back to describe by the way there is this little (t: 280) Vinshaera to theelles is going to coming in and this is the element that you can back (t: 290) in. So that we can see that what is a high after. Do we even have a notice on the timeline as well? I think I've done. Oh, my working with is checking myPi Potter application and I think with the data setup なんて we've got, That's quite Cursor rule. So whoever was working on this was using cursor and has already laid out (t: 300) all of these TypeScript best practices to be using within the project. So it's worthwhile to let Cloud Code know that this exists if it doesn't already. The best way you can do it is to do something called (t: 310) memory rules. And the way you give Cloud memory by using the hash commands and then it will say add to memory. In this case, you must follow the guidelines (t: 320) in cursor rules TypeScript best practices for all TypeScript code. And what is this going to do? It's going to (t: 330) prompt you where you want to store it. I like to start within the project memory because this allows me to pass this project on to somebody else and they'll immediately have the same memory (t: 340) that I had regarding the project and they'll also know to use the TypeScript best practices. You can do it locally and in this case, it's actually just for you in case you actually want to remember something that you don't want to (t: 350) need to share with somebody else. And then in user memory, you usually do this when you want something across all projects. In this case, for this project, we're going to do project memory and what we'll see is that (t: 360) within Cloud.md, it actually appends it at the bottom as one of the TypeScript guidelines. So this is best practice. You should always keep your Cloud.md file updated (t: 370) and one way you can do so is by using the memory function within Cloud Code itself. Remember the way you do it is that you press pound sign and then you just write it and you append it automatically to Cloud.md (t: 380) Great. So that's hack two. Now for hack number three, this is something that I see a lot whenever there are new people to Cloud Code. They're like, wait a minute, everything is the command line. How (t: 390) am I supposed to take a screenshot of something and tell the AI to implement it? I'm going to show you this right now. If you look at how our theme settings button looks, it looks pretty out there. All right. So I (t: 400) actually found this really nice looking theme switcher. And what I'm going to do right now is take pictures of this so I can insert it into the Cloud Code command line. I've already taken pictures of how (t: 410) this theme switcher looks. And so what you can do is you can actually just drag it straight into the command line and I can now say fix the theme toggle button (t: 420) to look more like the universe such that it plays more beautiful. Let's give this a shot. And you will see a lot of people sometimes complain, (t: 430) well, everything's in the command line. I miss having cursor because I can actually just add images into cursor directly. Well, you can do the same thing with Cloud Code because it's very useful to take a screenshot of the (t: 440) something and get Cloud Code to fix it. So you can see right now, it's going to fix the theme toggle styling to match that. Let's switch back into our project. And it's pretty good, right? (t: 450) Check this out. It already has updated our theme toggle to look a lot more like the screenshot. So this is actually a very powerful hack. You can actually just take screenshots of stuff. Cloud is going to be able to read those images (t: 460) and you will see that it's actually going to be pretty good at implementing it. Now, previously, when I told it to update the theme toggle, it was able to identify which file to manage. The problem with doing it that way, (t: 470) sometimes with a larger code base is that it will need to actually run through the entire base and try to find out where it resides. One of the things that you should be focusing on is to try to be as specific (t: 480) and provide as much context as possible. You should know that you can actually be very specific about which file you want the AI to be modifying. So in this case, you can see the to do form (t: 490) really isn't working, right? Because whenever I add something in, it actually, like I said, it actually just does lock something to the console. It doesn't actually work. There's actually a add to do (t: 500) component right over here and it shows that it's an intentional bug. We need to get this to work. So hack number four is showing you that you can actually provide scope context (t: 510) to Cloud Code, which will help to reduce the number of tokens you send because it doesn't have to read the entire folder structure. It also makes it a lot more targeted in what it does. What I'm going to do is here, I'm going to say (t: 520) the add to do form isn't working. Fix it by targeting and then you can actually do it here. Call to do form. (t: 530) Do list. And then you press add and you will see it provides you with a set of files you can select. But if you continue typing, it will actually be able (t: 540) to update that and you can see it's picked the right file to be editing. And then we just press tab and then press enter. You will be able to select that and then press enter again and (t: 550) you will submit this as the specific context to be looking at. It will actually be a lot more directed and this will help to optimize your cost as well. Also helps to reduce miscellaneous (t: 560) mistakes because it won't end up modifying something else in the code base which you maybe did not want the AI to mess around with. So it's identified where it needed to make the fix. If you look through it, (t: 570) it says I'll fix the add to do component and then it will need to update the code with this new function. Continue implementing the entire to do. And then (t: 580) it will continue and implement the rest of the functionality so that it works. The great thing about Cloud Code is that it actually goes into a lot of detail about what it did to make it work. The best thing to do is to actually test it. So let's do it now. (t: 590) Buy groceries today. And if I add a to do now, you can see that it goes into the buy groceries list. If I actually mark it, now it does cancel and if I unmark it, it also (t: 600) fixes it. So with just one line of code and one specific file, it was able to fix all the functionality related to to do list. And so that's the great thing about Cloud Code and thumbs up hat number four. (t: 610) So far, we've been making very simple fixes. We were just changing the image of the theme toggle. We were just fixing the to do list. But one of the things that you should know about (t: 620) is that it's often best not to let Cloud Code make changes based on a single prompt. And the reason for that is that oftentimes when you're working with something a little bit more complex, you (t: 630) want to make sure that Cloud is thinking carefully about what it's doing and planning its actions before you actually say, yes, go ahead and do it. So this is something called (t: 640) plan mode. And the funny thing is that it's not very obvious how to toggle it. And I'll show you exactly how to do it here. Let's get this theme toggle to actually work. (t: 650) And for it to work, it actually is a little bit more complicated because there are multiple profiles to modify. You have to do some styling with CSS as well. So the best thing to do is to ask Cloud Code to plan it (t: 660) before executing. But the way that you do plan mode is you just go into Cloud Code over here and you do shift tab and you will see it has now said plan mode on. And you (t: 670) can cycle between plan mode and regular agent mode by pressing shift tab again. Let's switch back into plan mode now. All right. So I want you to plan how to make a (t: 680) theme toggle functional and add it to the path. In this way, you will see that within plan mode, what it does is that it's actually not (t: 690) going to make any edits. It's going to look through your entire code base. It's going to research how the current implementation works and then plan the proper architecture of how to get this (t: 700) working correctly. It's going to analyze existing code bonus to know how to integrate it. And then it's going to design any elements that are missing. So I'm just going to make this a little bit bigger. But now you can see it's finally (t: 710) done with its planning mode and it says it's ready to go. It gives you an entire theme system implementation plan. It actually tells you what it needs to do with the tailwind config. It tells you (t: 720) an implementation strategy, which looks pretty good. It's going to create a theme context and provider, integrate the provider and update the toggle component before making all of the other components. (t: 730) It's even going to tell you which files is going to modify and what is expected to go. You can actually tell it to keep planning if you're not happy with something or you want it to consider something else. Otherwise, you can say proceed with the current plan. (t: 740) So let's just say yes here. And as this goes along, you can see it's starting to add the theme toggle. All right. Starting to change the different themes that are possible. And then it's going to update the theme toggle (t: 750) to use the context that's been provided. All right. It refresh now. And you can see there's an error here. You can press escape to interrupt it. And it's probably worthwhile. Copy and (t: 760) pasting the issue in here and say fix this first. Then I can paste the text I copied into clock code. Kick it off. You can see it has taken the error that was (t: 770) showing up over here and then popping it into the command line. All right. It's identified the issue because there's a mismatch between the component types and it seems to have made the fix. I'm going to (t: 780) refresh this. All right. And you can see here that cloud is struggling a little bit to get this to work correctly. And this is a great time to show you hack number six, which is called extended thinking. (t: 790) The best way to explain this is in this article by Anthropic themselves. If you scroll down, the main thing that you want to take out here is this section where it says that if you use the word think or think (t: 800) hard or think harder or ultra think, it actually prompts clock to actually work harder about trying to figure out how to fix a problem. All right. So we're going to try it out right here. So I'm going (t: 810) to copy this error again. I'm going to paste it in. I'm going to say think harder about why this error is still occurring despite your previous changes (t: 820) which have not fixed it. OK, so there we go. We are going to let it go ahead and think about why it's still throwing up this error because it's made a fix. (t: 830) A few times now, but it's not being able to resolve it. So this is one hack that you want to really pay attention to because you can actually make clockwork work and think a little bit harder about why (t: 840) something is not working. All right. Now you can see that we're able to switch between night mode and dark mode with no issue. The error doesn't appear. And because of the additional thinking which you can see over (t: 850) here, it's been able to reason about why the issue was continuing to happen despite numerous fixes. This is one hack that you really want to pay attention to, because if you try to just (t: 860) randomly vibe code your way into fixing issues, you can just go in a cycle and you just never break out of it. Whereas using extended thinking all the way up to ultra think oftentimes will help (t: 870) you resolve the issue much faster. All right. So now that this is fixed, I intentionally interrupted the execution of the previous plan because I wanted to show you hack number seven. (t: 880) This is something that not a lot of people realize how to do, but it's extremely useful as well. One of the great things about clockwork is that it actually maintains a to-do list of things that it needs to get done. At least (t: 890) each time it finishes something, it will then strike through it. So if you actually look at this original to-do list, you actually finish all of these things and it was about to work on the dark theme for the various components. The problem (t: 900) with it living within the terminal is that there's no way for you to reference this if you close the terminal. Wouldn't it be nice if this was actually written out to a file such that you can see updating the (t: 910) to-do list live as it goes along. There are other tools that actually do something similar, such as Taskmaster, and you can replicate what Taskmaster does within Clockwork using this simple (t: 920) hack. Now the trick here is to actually figure out what tools are available to Claude. So if you actually do something like this, what tools do you have available to use? (t: 930) And this is actually a really useful meta hack to figure out about whether there's any new functionality or capabilities that are present in Clockwork that you may not be aware about. (t: 940) Claude has the following list of tools. He can read and write files, he can do edit files, he can do multi edits and all the other stuff. But the most important thing is that it has something called to-do, which is create and (t: 950) manage task list. This is how it manages and updates this to-do list that you see over here. Alright, so I'm going to combine two hacks into one. I'm going to create a custom command that (t: 960) tells Claude to sync any to-do list onto the local file system at the same time. Alright, and the way that you do so is that within your .Claude folder, you have (t: 970) a subfolder called commands and you can see I've actually created something called syncTodoes.md here. This syncTodoes file tells Claude to keep track of all to-do calls (t: 980) and maintain a persistent record across sessions onto a file. By having this syncTodoes here, I can press slash, you can see that there's this new command that corresponds to this custom command (t: 990) file that I've defined here. What happens when you execute this custom slash command now is it will sync the current to-do list with an external to-do list file. So it's going to check the to-do list state first (t: 1000) and it will say that there's no file that exists, so it's going to have to write that file onto the file system. And now if you open it up, you can see it's synced the entire state (t: 1010) of the to-do list that was planned previously and then it can show you what's in progress and what's pending. And this matches up exactly because you can see everything that we've completed in this session. (t: 1020) You can see what is focusing doing next and what is left remaining to do. This is a very powerful way of exposing the to-do list function within Claude up into a file because this actually (t: 1030) gives you a much easier way to see how things are done. So this syncTodoes custom command is extremely useful and I've made this available for free. All you have to do is click on the link in the description (t: 1040) below and join the AI-oriented Insiders Cloud. It's completely free, so click on the link and subscribe. You'll get full access to this entire repository as well as this custom command. (t: 1050) Alright, let's just finish up this application. Based on the to-do, finish the implementation. And so what it's going to do right now is finish the rest of the implementation for the app. (t: 1060) Hopefully with that, we'll have a working to-do app. We can see it's starting to add the DartMode to the components here, to the list itself, and then switching over to (t: 1070) making sure that the app to-do section also has it. And then now it's to the rest of the page. And now it's done. It's fully functional and you should be able to switch between (t: 1080) DartMode and LightMode. And it's got this really nice button over here. And then I can say buy groceries. And this time the button works. And marking (t: 1090) the to-do list now actually works as expected. Alright, so there you have it, folks. I've shown you a working example of how you can use Cloud Code with this eight really useful hacks. (t: 1100) Now, are any of them new to you or do you already know about all of them? Let me know in the comments. And if there's anything else you know about that I didn't cover that you feel would be considered a pro hack, let me know in the comments (t: 1110) below as well. I really hope you enjoyed this tutorial. If you haven't already checked out my previous tutorial on how to do context engineering with Cloud Code correctly, you will definitely want to check (t: 1120) out this video over here. If you enjoyed this tutorial, don't forget to give it a like, subscribe, and remember to turn on the notification bell so that you'll be the first to know whenever to see a new video. (t: 1132) And I'll see you in the next one.

