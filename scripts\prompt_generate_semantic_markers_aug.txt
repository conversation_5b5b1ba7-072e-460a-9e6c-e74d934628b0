# Direct Instruction: Semantically Chunk All Transcripts

**DO NOT write scripts or code. DIRECTLY EDIT the transcript files yourself.**

## Your Task:

Go through every transcript file in the `claude-code` folder and ADD semantic dividers to chunk the content meaningfully.

## What to do:

1. **Open each transcript file** in the `claude-code` folder
2. **Read and understand** the content
3. **Add dividers** at natural topic boundaries using this format:
   ```
   (topic: Title of This Section)
   ```
4. **Save the edited file** with the dividers added

## Chunking Rules:

- Identify where topics shift or new subjects begin
- Insert the divider on its own line with blank lines before and after
- Create descriptive titles (3-8 words) that summarize what that chunk is about
- Don't modify any original text - ONLY add the dividers
- Aim for chunks of 200-500 words but prioritize natural breaks

## Example of what you should do:

If a transcript says:
```
Let me explain how to use the API. First you need to get credentials...
[more API content]
Now let's talk about error handling. When errors occur...
```

You should edit it to:
```
(topic: API Setup and Authentication)

Let me explain how to use the API. First you need to get credentials...
[more API content]

(topic: Error Handling Strategies)

Now let's talk about error handling. When errors occur...
```

## Action Required:

Start processing the files now. Edit each transcript file directly by adding these semantic dividers. Do not create scripts, do not ask for clarification - just perform the chunking task on all transcript files in the claude-code folder.