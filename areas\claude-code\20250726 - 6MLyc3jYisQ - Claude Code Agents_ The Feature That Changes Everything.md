---
title: "Claude Code Agents: The Feature That Changes Everything"
artist: <PERSON>
date: 2025-07-26
url: https://www.youtube.com/watch?v=6MLyc3jYisQ
---

(t: 0) Yesterday, Cloud Code changed the game again. They released agents that live in your project and seem to be able to read your mind and do exactly what you need, even if you don't exactly ask for them. (t: 10) Today, I'll show you how to build agents in Cloud Code that really have a chance to change your use of the tool. But there are a few things to be aware of. (t: 20) Okay, what exactly are agents? Think of agents as little prompts that live in a special folder waiting for you to call them up (t: 30) or mention something that they do. This is gonna feel a little bit like MCP and a little bit like agents and a little bit like slash commands. There's a lot of moving parts here (t: 40) under something that seems so simple. So I really wanna show you how to use them today. First, let's look at what Anthropic announced. Very simply, they have a page for sub-agents (t: 50) inside of their documentation. What I will point out here, the only part that's really meaningful to us at this point is what are the key benefits of writing these agents. (t: 60) Now, they call them sub-agents, but when you use the command, there's a slash command to find them and that slash command is slash agents. But the two things that are really meaningful here (t: 70) is that they have their own context, which means everything inside of that agent area, that's why they're sub-agents, is they have their own sub-context area (t: 80) that they might do hundreds of steps and all of the context that they build up to do all of their moving parts, stays within that agent. So when it's complete, what it's done with, it passes back out (t: 90) and everything else is consumed and goes away within that sub-agent. Now, what's important about that is it doesn't pollute your main context. If you're having a chat and you want something to occur, (t: 100) please write a file or go research the best account that I need to use for this sample. That might need to go read a database (t: 110) or look at the web or whatever it might need to do. All of the extra moving that it's doing in there, you don't need in your main chat context. You're trying to work through a problem, whatever that problem may be. (t: 120) And so all of these little subtasks that go off and do their own thing need to kind of manage their own memory space, if you will. Now, you're sending in some information that it can consume and work with (t: 130) and it's sending its response back out. And we'll take a look at how I determined that in a moment. But that's the one really major thing that these things are actually doing for us is kind of encapsulating the space of the context (t: 140) that they're part of. The other thing that really is truly important here is they have flexible permissions. So this is a way to say, what can this agent do? (t: 150) We will look very clearly at this so you understand it, but it's basically saying, can this agent write files? Can it read files? Can it use this MCP or that MCP? (t: 160) All of those kinds of things can be described inside of the definition of the agent itself. And that way you can greatly limit or even enhance what any given subtask (t: 170) or subagent is doing for you and the tools that they have to do it. All right, so with that, let's dive in and take a look at how you first interrogate the agents that exist and then build our own. (t: 180) Okay, let's build a dice rolling agent. It's simple enough to understand and maybe complex enough to really show off how to build your own agent. For anybody that doesn't know, (t: 190) I am running Claude code inside of cursor. Really that just basically means you are seeing terminal environment here and cursor on the outside. (t: 200) And that really gives us the ability to look at files. I always really advise running Claude code in something that you can interrogate the project that it's working with. You can collapse the side and just get back (t: 210) to Claude code itself if you want. But enough of that, just so that you understand the environment that we're looking at here. If you recall, it told us we could use the agents command, (t: 220) the slash command of agents that you can see here. And there are no agents that it found currently in this project. So we can create our own agent. I will show you that where this is gonna go (t: 230) is in our Claude folder or dot Claude folder. There's an agents folder and inside of that agents folder, which is currently empty. That's really what we're about to build. We're gonna let the system itself build our agent. (t: 240) I think this is the best way to get started. And they really advise that you always get started with this because the file that it creates has a little bit of a format. We'll look at that. (t: 250) Here we go. So you can change where you want your agent. Do you want your agent in this project or do you want it? It says personal. I might think of this more as global because it's putting it in my top level dot Claude folder, (t: 260) which is in my home. So if I put it there, if I chose personal, it will be available to every instance of Claude (t: 270) that I pull up from this home folder, from this account. And that's really useful. I'm very excited about that. I don't have any installed now, as you could see, but that's really where I think I'm gonna put (t: 280) several of the things that happen across many of my projects. So that I think is a real power here. At the same time, what you might put inside of a project is also really powerful (t: 290) because projects really have their own concerns very often. And this might really be a way to let things occur a certain way without every user having to know how to ask for it. But look, I'm getting ahead of myself. (t: 300) Let's build an agent. All right, we are going to generate with Claude and tell it that I want an agent that rolls dice. Okay, so once it's gone through, and of course this is doing a full LLM thing, (t: 310) we'll see this in a second when we see the completed file. So it really is using probably Sonnet, I don't really know, to describe the best version of an agent that it can, (t: 320) but we're at the point of needing to tell it what tools it has the ability to use. So if we turn off, we're gonna see, oh, it's done. If we turn off all tools, it has no ability. That's not entirely true because it's an agent. (t: 330) It still has access to an LLM. And really, frankly, that underlying LLM can also do things. So it's not really all tools. Don't think of it as this isn't gonna do anything, (t: 340) but it's not using any of your actionable tools that are extraneous to the typical Claude environment, if you will, or the typical LLM interactions that you might have. You can allow it just the read-only tools (t: 350) or read-only and edit tools. You can do anything you want here. But if I show the individual tool, and say, let's take a look at what the read-only tools turned on, you can see that it has some of the different (t: 360) read mechanisms that you might have at shell level. And also it's got its web search and notebook read mechanism. Here, let's try this. (t: 370) This one, we can just, it's too big. Let me hide that so that we can see. I'm gonna turn off all tools for this guy, because it really doesn't seem to make sense that he needs any tools. So let's give him a shot. (t: 380) He's just rolling some dice. And now it says up here to continue. And now you also get to continue to give these things a color. I think this is great. I'm glad they've moved into this. I'm gonna make our dice roller blue. (t: 390) And that just means when you see it running, you'll see it very clearly called out in the stack here in the conversation. Okay, and so this is the prompt that it ended up creating. (t: 400) You can't see it all here. So we'll look at it as a file. So it's saying press S or Enter to save. Now we've saved it and we can see it here. Okay, so let's exit out of this (t: 410) and then come up and take a look at the file that it created. The prompt is just down here so that you can just come in and write anything you want in the prompt. Up at the top is where some of the magic is happening. They describe this in the documentation (t: 420) if you need to know more, but really what's going on is there's a special way for it to call out its name and also description. Now the description one is probably the most important. Tools are important as well, (t: 430) but really description is the one that's kind of the most critical. And you can see that this has a very special format to it with a little bit of extra markup inside of it. That's why they're advising or recommending (t: 440) that you use their tool to get it started and then come back and make changes. But this description here, use this agent when you need to simulate dice rolls for games, probability calculations, random number. (t: 450) So this is a very important description. Anybody that's used MCPs, built MCPs, and even in some cases, agent tools, things like that, depending upon what platform you're using, (t: 460) what framework you're using, the description is something that this agent system, that Claude code is using to interrogate and really go discover these tools. And now I can't say how important that is. (t: 470) It's very difficult to get that across in a very simple statement like that. If you know what I just said, you probably just went, oh, what, really? These are not things that the system necessarily knows about. (t: 480) It's like all of the other MCP tools that we've seen. It has a self discovery. So it, when it launches, looks at all the agents that it's aware of (t: 490) and takes an inventory of all of their descriptions. Those descriptions become part of the tool set that it gives to an LLM when it says, hey, the user asked me to do X, here's some tools I know about, maybe they're useful. (t: 500) Let me know if you want me to call one of those. So it's using this as a tool that the LLM might go, oh yeah, you know what you should do? You should roll dice using that dice rolling agent (t: 510) that was given to you because it looks like in the description, which is the most critical aspect of this, they indicate that they can roll dice. So let's give this thing a shot (t: 520) now that we understand what's going on here. And if, of course, if we do agents, we should see our dice roller here, roll a D6. Now, of course, not fast. This is definitely not the most efficient way (t: 530) to roll a D6, I wouldn't think, because it's going to an LLM, the LLM is saying, okay, why don't you actually call this tool that you told me about? Then it's saying, oh, I'll call this tool. (t: 540) Let me call the tool. The tool's going to an LLM saying, hey, I'm supposed to do all this. It's kind of nuts to think that this is how you would roll dice, but that's the idea of agents. This is kind of a natural way for agents to work. (t: 550) And you can see, it says you rolled a four on the D6. Excellent. So this is the output that's actually coming out of this action here. And if I go in and do a control R, which is a way that you can see the actions that occurred, (t: 560) all the conversation that was kind of collapsed, if you will, you can actually see some of what was happening. So it started to call the dice roller the first time. (t: 570) It doesn't know about a slash roll command that it tried to do. Here's the commands that you're allowed to do. And it says, huh, maybe that's not what I need to do. Let me try with simpler prompt, calls the dice roller, (t: 580) and the dice roller goes to the agent or to the LLM, does the rolling, comes back with this kind of output. And then what it finally summarizes it to is this. So that's all of what's happening inside of this agent. (t: 590) That's how agents work. You can see, it didn't have to say use the dice roller agent. All we said was roll a D6. That description is doing that work. (t: 600) Remember this discovery thing is super, super important. And so if you are writing these agents, what you want to do is make sure the description is very unique. You don't want something like writes files or reads files (t: 610) because you're going to be reading files a lot and it could get confused. And these agents are always kind of in memory or in access for the LLM. (t: 620) So if it just sees something generic saying, oh, I read a file. Well, at any time, it's going to be like, oh, I read a file. It's going to be like, oh, I read a file. It's going to be like, oh, I read a file. But at any given moment, the LLM might send back a tool call saying you need to read a file and it could choose the wrong one. So just be aware that you want these to be very discreet (t: 630) and do work that's meaningful for you and meaningful that you might describe when you're asking for it. I need to query my local database for X. If you have a tool or an agent that is locally queries (t: 640) the database using these PSQL commands, blah, blah, blah. That's going to be a very valuable kind of interaction. If it's just can read databases. (t: 650) That might be challenged. You'd probably get there. They're in most cases but I think you might get some false positives in that case as well. All right. So that's enough of, we built our first agent. (t: 660) Let's take a look at how I can tell that they are passing information when you change. Okay. So I've dropped a few new agents in here. And so one of them is we have our dice roller (t: 670) as we saw before. We have another one that summarizes and knows how to write markdown kind of summary from information that it's given. And another one that knows how to write files (t: 680) that don't have a name. So if you have a big string and you're just trying to say, save a file hopefully this one will kick up and be used. And it'll call the LLM with the content to try to figure out (t: 690) what the value, the name of the file should be and what file type it is. So we're going to try to chain these together. So we saw calling dice roller and it'll come back with its value. (t: 700) But how about if we said, okay, use dice roller and then save the information directly as a file. So let's give that a shot. Now, this is one of those examples. I'm saying save the output to a file. (t: 710) Hopefully my description is strong enough to say if it doesn't have a name and you have a string this is a good agent to use to write a file, but it might not. (t: 720) So it does seem that it's picked it up here nameless writer and dice roller. So it went and called the dice roller. It's now calling the nameless writer. (t: 730) So this all looks successful. It also picked text. Maybe it didn't turn it into markdown, which is great. And that's great. So this is probably output that we saw that would have come out of the dice roller, right? (t: 740) So this is kind of proof that what we're seeing is the dice rollers return value. The string essentially that's being in the response is then going in since I'm chaining those two together. (t: 750) This is kind of called chaining when you're mentioning two things in a row roll some dice and save the output. I'm chaining two actions together. (t: 760) Theoretically in a normal agent system that's a way that you would pass context from one to the other. You're seeing that happen here. So let's try the same thing. And I'm gonna use the up arrow here (t: 770) to give us roll some dice, save the output. And summarize, and then finally save the output to a file. So let's see what it comes up with on this one because we have a summarizer agent also. (t: 780) To do write tool to keep track of these tasks. Okay, let's see if it works. Dice roller, excellent. Summarize, excellent. Using the summarizer. And you can see the colors coming into play here (t: 790) so that you can really tell what's going on that you're using kind of a smart system or potentially a very smart system that's housed away in one of these agents. And then hopefully we see the file writer happening here (t: 800) at the end, nameless writer. I like it, excellent. This is working. Okay, so hopefully this will give us a new dice roll result. Hopefully it doesn't pick the exact same title. (t: 810) Dice roll summary this time, perfect. And it's a markdown because if you remember the summarizer turns everything into a markdown file. Excellent, so let's take a look at the result. All right. (t: 820) The executive summary of a series of dice rolls. Total dice rolled 11. Wow, we did just, I think we just told it to roll some dice. (t: 830) Rolled five sets of dice. Created a comprehensive summary. It does, okay, and it's got five sets, great. Okay, so we're gonna call it good. So this is something that I wanna talk about (t: 840) and you might be hearing me as I'm saying, I hope it calls this, I hope it does this other thing. It is probabilistic. I will get to that at the end. It's really a very important thing to understand (t: 850) that you're introducing here. And it is one of the warnings that I would say comes along with this kind of action. All right, first, let's look at a much, much more advanced (t: 860) of something like this that has nothing to do with programming. And by the way, if you haven't noticed, we haven't written any programs yet. It's a way that I've been using Claude Code (t: 870) and I'm kind of gonna advocate you to think about using Claude Code this way as well. The next example also. Okay, I'm gonna give you a little peek behind the curtain on how this has been happening. (t: 880) I've done this traditionally in ChatGPT. Even I have multiple custom GPTs. And by the way, maybe you're starting to see that these agents here in Claude Code are pretty similar (t: 890) to what you can do with a custom GPT. And I would say that's very, very true. Now, of course, these you can use while you're coding or doing other things. (t: 900) They're in service of something else. And a custom GPT is kind of the end result. So it's a little bit of a different pattern just in regards to what action it's playing (t: 910) in your pipeline. But here I will say I have notes for the different videos that I create. Okay, so today, this is today's video, Claude Code Agents. (t: 920) And I did a walk with myself. I do this very frequently. That is kind of all of the things that I wanna talk about. I just do a little meeting and I record it and transcribe it. (t: 930) And so I've put it here. And so what I'm gonna tell this system is I wanna create an outline for this video. And I have a very explicit kind of definition of how outlines should be created, what I need from them, (t: 940) the format of them, a whole bunch of stuff that's really opinionated and important to me so that I can kind of shoot one of these videos. For example, let me show you, pull this on. (t: 950) Let me pull this on screen real quick. These are the notes that I'm currently working from to film this video just to keep me on track. Okay, with that in mind, (t: 960) let's see if we can have that happen. I will show you in Claude up here in the agents, I have two different agents. One is an outliner agent and another one is the info agent. I'll show you that one second. (t: 970) The outliner agent is the one that we're gonna run. Get rid of all of that so you can kind of see what's going on here. And I'm gonna reduce the size of this for a second just so that you can see what's going on in here. (t: 980) This is that front matter, that YAML front matter again, that describes when it should be used. You can see that I have a hard thing here that's used for all video outlining (t: 990) so that I don't have to be explicit on how I ask for it. If I ever ask for, hey, I kind of want a video outline, that should be enough clue into this. I don't have to name video outliner agent, but it also, as you can tell, is a very big prompt. (t: 1000) And this is of single shot, which means I'm giving it a full example to service from. I'm saying, okay, here's some things to think about. (t: 1010) When you create this outline for me, I want you to be able to tell a story, help me how to craft things in the right order, figure out how they flow together, give me some samples of things I might say, (t: 1020) but more than that, I kind of need the organizational help. And so that's what it's kind of doing. And I've written in a workflow or kind of a report of an example that I like. (t: 1030) And it's not a full example, it's not an actual example, it's telling it little notes in each one of these, but giving it the full layout of what I just pulled on screen. (t: 1040) So that's kind of a single shot example. If we come back here and say, I'll pull this open so you know what I'm asking about. I'm going to ask about Claude Code agents. And so this is kind of a sophisticated use. (t: 1050) I want to kind of describe, this is a new way of using Claude Code. Anthropic has come out and said that they have use across their company, (t: 1060) across all areas, not just engineering, using Claude Code. Other people have started sharing how they're using Claude Code for things that have nothing to do with code. This is my note system, you might imagine, (t: 1070) Obsidian or something like that. This is essentially what's going on there, except I can keep things that are not files, not kind of markdown files. I can do a lot more here that programmatically (t: 1080) I'm using an LLM to create the next notes for me. And so it knows I've said Claude Code agents, it's going to go and figure out where Claude Code agents is (t: 1090) because of what I have in my Claude file. My Claude file is kind of descriptive saying, this is what's going on in this whole repository. This whole area is very specific. (t: 1100) Here's where you'll find things in the raw notes form. That's where you want to read from. The reports is where you write to. You should never write to raw notes or update things there. (t: 1110) All of those kinds of rules are in this Claude MD file. And so that's how it knows how to pull this information out. And I'm just showing you all of this because I think this is a critical concept of saying, (t: 1120) let me allow Claude Code to be something that I just ask questions for. I highly advise, go get a CSV from somewhere. The next time you end up with a little bit of data or something else, start a brand new folder, and then you can start a new folder. And then you can start a brand new folder. (t: 1130) And then you can start a brand new folder. And then you can start a brand new folder. Start Claude Code, save your file there, and just say, hey, Claude, tell me about this file. Can you tell me, give me some ideas, give me some reports here, tell me kind of the information that's in that file. (t: 1140) Give me some insights that I can't tell from it. The things you might ask ChatGPT, because right after that, you can then say, oh, why don't you write a file for me? Oh, could you write a program that could show it to me? (t: 1150) How about a Streamlit application that's a little dashboard around it? Oh, could you publish this? Could you also send it to Instagram? Is there a way that you can push this over to my note? All of this stuff becomes really possible. (t: 1160) And that's what they're really outlining. And I think that's what Agents is really for. It's gonna be really useful in the engineering practice as well, of course, (t: 1170) but it's really valuable when you start saying, hey, I have an agent that knows how to send something crafted in the right way to Notion. Not just a connector that knows how to connect to Notion, (t: 1180) all the rules around the way that I like to store it, where it goes within my Notion system, all of those other things. That's something that I'll be putting in an agent and it knows how to use it. Use the right MCP. (t: 1190) So it no longer are we here trying to actually do everything ourselves. We get to encapsulate a lot of that into what we call Agents at this point. You can't tell, I'm kind of excited (t: 1200) about where this is going. All right, so it is finished and we have two files. Interestingly, you'll see I have V01 and V02 here. I have my own versioning mechanism (t: 1210) that I outlined inside of my agent that said, this is how you should save files. Here are the considerations for how you save and name files. I want to make sure that you're always using the YMD (t: 1220) that you find on the folder, as well as the video name, which can be found on the folder. And then the number is a zero padded two digit thing (t: 1230) with an RMD after it, that kind of thing. All of that is done so that I can just have conversation after conversation and every edit that it makes, every time we make an update to this outline, (t: 1240) it will just save another version right behind this one of the same name. To me, really useful that I can roll back to previous versions very, very easily and see, the progression of the changes that we've made. (t: 1250) All right, enough of that. Let's take a look. Did we get good enough notes? All right, hook some outtakes, promise. What are cloud code agents? Building your first agent. (t: 1260) Yeah, this all looks really, really good. This looks exactly like what I need to work from. At first, the non-determinism freaked me out. Then I realized it might be the whole point. Hey, guess what? (t: 1270) At first, the non-determinism freaked me out. And then I realized it might be the whole point. Okay. That's pretty funny because it's kind of from the notes, that talk that I told you that I recorded (t: 1280) and used from my notes. So that's kind of neat that it actually is something from an LLM that's from me that I have to read to give to you. It's very meta here, sorry. (t: 1290) But really, one of the things you need to be cautious about is very much like MCPs, these things are not something you have to name every time to call. (t: 1300) Okay, that's really cool. Like you saw that my nameless file writer was writing files when there was no name to be found and it had to determine the name. So it went, the content off got the name. (t: 1310) Cool. That's kind of a really neat idea that I would have this thing sitting around. However, boy, I could get into trouble if while the system is normally doing coding for me, (t: 1320) it sees that agent hanging out inside of my code base and says, oh, wait a second, I don't really have a name for this, do I? Let me use the nameless file writer. And all of a sudden it's starting to rename my files (t: 1330) accidentally or duplicate them into new names and things really go haywire from there. I absolutely can see something like this occurring. There's no great guardrails for something like that (t: 1340) because you're in an agentic calling process. So at any given moment, at least my assumption, I have not seen this happen in the wild, but I haven't used them that much yet. (t: 1350) I could absolutely imagine that halfway through a process it's determining what tool should I use? Because it's just giving all of these tools as we talked about to the LLM every time saying, (t: 1360) here's a list of things that you can choose from to solve this problem. It absolutely could accidentally choose, choose one of the tools that you're describing when you don't really want it to. (t: 1370) So we need to be cautious about that. I think writing your descriptions in a way to say, absolutely only use this when, never use this for, (t: 1380) those kinds of things might help and help the discovering LLM who's got this as kind of the definition of the tool to say, (t: 1390) oh, yeah, maybe I shouldn't use that right now. It might make it harder to use them to make them happen accidentally. But one of the values here, I think, that I think would be very interesting (t: 1400) is you saw me save these scripts or these agents inside of the project itself, right? That would be in kind of your version controlling system, (t: 1410) which basically means the next person that checks out the project will also get this information. And there's some real value in that. They're also just released. (t: 1420) I am talking about just 10 minutes ago or so, released a way to be able to load your settings from JSON files. So you'll be able to denote where to go get your settings and load them in from. (t: 1430) So they're really starting to lean into this idea of when you're in a shared environment, how do you load in the context that's useful, that everyone on the team can get the same kind of context, (t: 1440) plus you can get your own personal tools. And I think that's what we're starting to see. It would be great if I wandered into somebody else's system, (t: 1450) started doing a little bit of coding, and the tools that would help clean up the files for the way the team wants them are actually... already in there. And the agent knows to say, oh, if you're about to do a commit, (t: 1460) go make sure all the files are XYZ. That could be an agent that knows all of the considerations that it needs to know to be able to do that. And it's just aware that commits are the things that it's looking for. (t: 1470) So it is very interesting to think that we're going to be able to share these things gracefully. But I will say that non-determinism, (t: 1480) that really is a bit of a concern that we are not sure when it's going to call some of these features. MCPs are in the exact same boat that you might have a tool that is callable (t: 1490) and you don't go through the front door of saying, call this MCP and use a tool. As that gets a little bit more traction inside of these systems, we might be making calls that we didn't intend (t: 1500) or calling using tools that we necessarily didn't intend that could have artifacts that, you know, that we're not thinking of. (t: 1510) I don't know how destructive they'll be, but, you know, of course, they absolutely could be destructive. Okay. After spending a day with, agents in Cloud Code and kind of this new paradigm that they're trying to introduce, (t: 1520) I'm kind of convinced that we're getting a glimpse into a new way of coding or a new way of interoperating with these tools in general. (t: 1530) One where we describe what we want much more than we describe how to do it. Now we've been moving that direction with kind of plan-driven development (t: 1540) and a couple other things like that. I will have another video shortly because that's what I was in the middle of recording, when this hit. I will have a video that kind of talks about (t: 1550) plan-driven development or hints at some of the ways you might be able to use that. Subscribe to get that. I think that's kind of an interesting set of some of the findings that I've had over using Cloud (t: 1560) over a course of a month and the things that I think are really useful. But I think this idea of, instead of saying, oh, please go do this, update this file, I need you to use this database, (t: 1570) use this schema, those kinds of things, we only start saying those when we really have to, or we put them in something like agents that guard us from ever making a different decision. (t: 1580) I really think this is just the beginning, but this is really obviously something that we will start doing much more often when we're coding and, in fact, (t: 1590) working with our notes and many, many other things, I believe. All right. This one was kind of a quick one, even though it probably wasn't terribly short. (t: 1600) I had to turn it around very quickly, so I hope it made a lot of sense. I hope it helps a little bit, untie what's going on with these agents in Clogged Code. Where else are we going to see them? (t: 1610) Because I know they're going to start popping up in a lot of places. Thanks for coming along for the ride on this one, and I'll see you in the next one.

