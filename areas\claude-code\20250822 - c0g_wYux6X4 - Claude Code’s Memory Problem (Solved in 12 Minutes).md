---
title: <PERSON>’s Memory Problem (Solved in 12 Minutes)
artist: <PERSON><PERSON><PERSON>
date: 2025-08-22
url: https://www.youtube.com/watch?v=c0g_wYux6X4
---

(t: 0) You're in the middle of your coding session with Cloud Code, but suddenly the outputs are getting worse. It forgets what you already talked about, misses important details, and suggests changes that would break your project. Here's the problem. We're cluttering up the context window way faster as we should, (t: 10) which confuses the AI, wastes your tokens, and kills your results. In this video, we're going to solve this by learning everything you need to know about how Cloud Code's context window works (t: 20) and successfully managing it through context engineering. We uncover the best and simple methods you can apply today to keep Cloud Code focused, never lose momentum, and 10x your outputs. (t: 30) You will have more productive coding sessions, ship features faster, and have a better development experience. (t: 40) So to better understand how we can manage and improve the context of Cloud Code, we first need to understand how context works inside of Cloud Code. So every time you start a new session, you start at zero tokens. (t: 50) You're starting completely fresh. Then you type in your first user message of whatever change, you want to do, or whatever you ask Cloud Code. (t: 60) It generates an output, and this also has a certain size, right? You have a certain size of your input in tokens and a certain size of your outputs. Now, if you keep sending messages, it will always insert your old message and the old response back into your new message that you type and generate a new response. (t: 70) Now, this goes on until you fill the tokens window of Cloud Code, which is right now at 200,000 tokens. (t: 80) And once it has reached this context window, you probably already saw Cloud Code summarizing everything that was going on before. (t: 90) And then with that summary, you type your new message and you keep going on with that conversation. (t: 100) So not only your prompt and response counts into the context window, all the file reads, log reads, document fetching, if you paste in images into your chat, it will also go ahead and analyze these, puts it into a structure that an LLM can understand. (t: 110) So all of those things and even more will always be in the context window. So all of those things and even more will always be in the context window. So all of those things and even more will always be in the context window. So all of those things and even more will always be in the context window of whatever is going on. (t: 120) Now, very soon, Cloud Code will expand this context window to up to 1 million tokens. So it's getting five times bigger. It's already in beta for some users and will be rolled out very soon, I'm convinced. (t: 130) However, this will not solve the problem that the outputs are getting better or worse the more you clutter up the context window. Because if you're unaware, you just feed up this context window with even more irrelevant information. (t: 140) So to improve the outputs we are getting, we need to develop this mindset of always being aware. What inputs we're feeding into the system because the quality of the inputs determine the quality of the outputs. (t: 150) And we call this mindset context engineering. Context engineering is basically being aware and engineering modifying what we feed into an LLM to give us a certain output. (t: 160) Because if you improve this part over here, we will improve this part over here, the output we're getting. Now that we understand the context window and understand the importance of this relationship between the input and output, (t: 170) Let's get into context. Let's get into context. Let's get into context engineering. So how do we improve the input that we're giving into Cloud Coach? (t: 180) Now, the first important point is creating documents that will feed the long-term memory of Cloud Coach. And the most important one of these is the CloudMD file. (t: 190) This whole text file will also be pasted on top of every new session you start. So whenever you start a new session with zero tokens, when you type in your prompt, also your CloudMD file will be passed over to Cloud. (t: 200) So that's why it's important that we put in all the long-term memory. So that it should never forget across different chats inside of this document. (t: 210) So this involves like technical requirements, how it should work with your app, what typical packages you're using, what type of development experience you want to have. (t: 220) But this could also be very project specific, like feeding in certain core features of your app or how you want things to be structured inside of your app. (t: 230) Now, if you have a serious project, you could go ahead and not only have this CloudMD file, you could also create a PRD.MD, so a product requirement file where you list out all the features, all the main and core functionality you want to give. (t: 240) So when we work in a new chat, work on a small feature, we always reference this bigger picture of, hey, this is how in the end I want my app in general be structured. (t: 250) And then we already have a high quality input where it knows, okay, this tiny thing I'm working on in the bigger picture, it should fit in like that. (t: 260) Now that we have a great long-term memory in place, let's get actually into the work. So let's say we're working with Cloud Code. There are two absolutely important commands that you should always use for being aware of the context. (t: 270) Now, if you already worked a lot with Cloud Code, you always have this percentage that will show up when you're getting close to this 200,000 K context window and shows you, hey, 13% left. (t: 280) And once it is going to 0%, it automatically compacts the conversation. However, we can also proactively make use of this compact function. (t: 290) So what this does is it. Proactively goes through all the recent chats. So everything you have discussed up here and just generates a summary of that. (t: 300) So I use this command whenever I'm still working on a feature, but I know, okay, what we've discussed in the beginning might not be relevant. And you can just use the compact command and it will auto summarize kind of everything. (t: 310) But you also see if you type in a space after, you can also give it some instructions of what of this part it should keep. For example, in this chat, I was working on the whole login signup components. (t: 320) In the beginning, I was just talking about this. I was talking about the styling and stuff. But in the end, I was more discussing about the auth logic. (t: 330) And if I next also wanted to further adapt to this auth logic, I can just say something like slash compact keep auth logic. (t: 340) And then this will generate a summary, but it will focus on what instructions you inserted there. Right. So we see down here it compacted the conversation. (t: 350) And the next time I insert something, it won't have all of these detailed back and forth. I had with code code. It will just have a summary of the main points in my case of the auth logic. (t: 360) And then we start with a fresh input over here. Just a summary above. Now, the second most important command over here is the slash clear command. What this will do is it will completely clean up the previous context and you completely start from a fresh token limit. (t: 370) You start with zero tokens again and just your next message you type in. (t: 380) And I'm using this command whenever I'm working on something new. Like a new feature or anything that was not related to the past chat. Because if you would just keep on going chatting with Claude without resetting the context window, it will would have all this irrelevant information that we already discussed and cannot really focus on the new task. (t: 390) But if you clean up the conversation history, we basically tell Claude, hey, just focus on what I'm telling you next, which will drastically improve the output. (t: 400) Now, I'm using the clear command even if I know, OK, I want to later jump back to this auth logic. (t: 410) But I have another command. I have another feature that I want to develop now or another thought that I want to ask it. And whenever I want to jump back to a previous chat, we can use this resume command, which will basically show us a list of all the recent chats. (t: 420) So even within one chat, I can jump to a specific, like, for example, if I'm in this chat, I can jump back to a specific state over here, like the first text respond, for example. (t: 430) And then just pop it up over here. And then from there, we basically cleaned up everything down here. (t: 440) And we just give Claude all the context up here. But from there, can develop something new. Now, another strategy of keeping the context window of ClaudeCode empty is planning out certain stuff or questions outside of ClaudeCode. (t: 450) I see myself using grok a lot for technical things. I just find it is the best LLM for, like, all technical details. (t: 460) It has the most knowledge over there because also axe is very technical. It has access basically to all posts over there. So whenever I have a question about, like, how should I sort of do this? (t: 470) Like, how should I structure a super base database? Or how should I structure a new feature? I see myself going inside of grok, come up with a plan over here, and then go on with the chat over here until I'm fully happy with the plan and generate it. (t: 480) Then copy this plan, paste it into ClaudeCode, and basically saying, hey, here's the plan, how I want my new database tables being structured. (t: 490) Execute that. And then it doesn't have, like, all this irrelevant context of me going back and forth with the LLM of figuring out how to do this. (t: 500) I'm just going to go ahead and start off figuring out, okay, what is the best plan over here? A bit more advanced strategy of keeping the context window of Claude always clean and minimal is utilizing subagents. (t: 510) Now, what subagents do is basically they're separate instances that have their own instructions, their own context window. And if you use subagents, like the main ClaudeCode chat calls a subagent, this has its own context window, does its job, and then only puts out the summary back to the main chat instead of doing all of that. (t: 520) What I'm going to do is I'm going to go ahead and start with the main chat. (t: 530) So, what clutters up the context window a lot is reading through files, for example. So, I've set up this API planner subagent over here in my global user ClaudeCode folder, which does exactly that. (t: 540) So, whenever I work with a new API, I am utilizing the Contacts 7 MCP. (t: 550) If you don't know, Contacts 7 is a very popular MCP where you basically have access to all the latest documents of every API. And every modern app that's out there. (t: 560) But reading through those documentations really consumes a lot of tokens. We're going to do that right now and you see how many tokens it actually consumes. (t: 570) So, that's why I set up this subagent over here, which is my API planner. So, this agent will always be proactively called whenever a user mentions working with API, API integration, API documentation. (t: 580) What it will then do is it will pass over just the main objective to this subagent. And then this will operate on its own, right? It will analyze it. It will analyze the object, fetch all of the latest documentation through the Contacts 7 MCP, then create a strategy and deliver the actionable results. (t: 590) And then only the results are being placed back to the main agent. (t: 600) And instead of having all those documentations that are irrelevant, it just knows, hey, this is how I should structure this API call. So, if you want to make use of this subagent, I'm pasting a link down in the description. (t: 610) But for this one to work, you would also need to install the Contacts 7 MCP. This one is super easy. I want to install it. (t: 620) Just paste this command down here, cloudmcp add scope user. We want to have it on user level. And then the Contacts 7 MCP. And then I can come to my main Cloud Code agent in the project and say something like, I want to integrate Stripe into our application and only want to allow users to access our app that have an active Stripe subscription. (t: 640) Call the API Fetcher subagent and come up with a plan of how to integrate Stripe. So, this will then go ahead and analyze our initial plan. (t: 650) And then calling the API planner, it will pass over this initial plan. You see it's already want to access the Contacts 7 MCP over here. (t: 660) But all of that is happening inside of the API planner, right? And we see now that the API planner is finished. It called eight tools by itself. (t: 670) It consumed over 54,000 tokens. And a lot of that is reading every single character in this Stripe documentation. Right? And then we see that we have a new application that could be relevant. A lot of that was not relevant. (t: 680) And now our main agent only comes up with the plan over here to integrate Stripe. So, subagent for sure is one of the main improvements if you want to keep your main Cloud Code agent focused. (t: 690) You can set up, for example, one for just focusing on security, just focusing on performance. And then those subagents will have all their contacts, all their resources free for just improving that. (t: 700) But whenever we work with the main Cloud Code agent, it just has the information of like the higher level picture and what's important next without cluttering up its own contacts window. (t: 710) And just by that, you will massively improve the outputs you're getting. If you want to learn more in depth about Cloud Code and want to have a comprehensive guide from zero, (t: 720) like from your app idea to final launch, you can check out my Builders Club course and community where we will basically go over everything from just setting up Cloud to a finished app. (t: 730) So, I hope to see you there. But if you want to keep on browsing YouTube videos, you can check out my latest video over here. (t: 740) So, thanks for watching this one and I will see you in the next one.

