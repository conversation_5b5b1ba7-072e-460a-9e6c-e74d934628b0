---
title: Agile Coding Is HERE… 90% AI Coding Is Already Done With This
artist: AI LABS
date: 2025-07-15
url: https://www.youtube.com/watch?v=fD8NLPU0WYU
---

(t: 0) Ever since AI came around, it has enabled many people to easily create websites and even apps. The problem is, most people skip the proper software development process and just go to (t: 10) cursor asking it to build an app or clone a site. While this quick approach might work for demos, it doesn't create production-ready software. That's why the industry has evolved from simple (t: 20) vibe coding to sophisticated context engineering. Today I'm going to show you how to take this even further. You can now have an entire software development team working right inside your IDE, (t: 30) whether it's Cursor AI or Cloud Code. This framework can build whatever you want, no matter how big it is. Before I tell you about this framework, let me give you some background so you understand how I found this and why it's so important. In one of my previous (t: 40) videos, I introduced a framework that enabled Cloud Code to work through command files, assigning different roles to simulate a complete software development team. It essentially (t: 50) supported development across the entire software development team. But the problem was, there was no documentation. And even when there was some documentation, (t: 60) there still wasn't a clear proper workflow implemented in that framework. Someone actually commented under that video, pointing out that it was based on a workflow I'm about to show you. (t: 70) That led me to discover this new method, which is called the BMAD method. It's by a contributor named BMAD Code, who also has a YouTube channel. And honestly, this workflow is really, really (t: 80) amazing. The BMAD method stands for Breakthrough Method. It's a method that allows you to create a workflow that is based on a workflow that is based on a specific method. And it's a method that allows you to create a workflow that is based on a specific method for agile AI-driven development. And I'll link its GitHub repository in the description below. Agile development is the standard approach software engineers use when (t: 90) building real software products. This method involves building software in small chunks, testing those chunks, and shipping them incrementally. The typical workflow starts (t: 100) with a PRD, a product requirement document, which lists the features that need to be built without any technical details. It's just the main list of features that are needed. The (t: 110) development team then breaks this into smaller tasks, and then they're able to do the work. The development team then breaks this into smaller tasks, and then they're able to do the work. The development team then breaks this into smaller tasks, and then they're able to do the work. Works on those chunks one by one, works on those chunks one by one, tests them, and gradually ships the final product through (t: 120) iteration. This BMAD workflow enables exactly that same process. What makes it a game changer is how it brings together everything I've shown you on this channel. Tools that make Cursor go step-by-step (t: 130) and context engineering concepts, combining them into one unified system. The method works across all IDEs. So whether you're using Cursor, Windsurf, or Cloud Code inside your terminal, this workflow (t: 140) slots right into your system. So whether you're using Cursor, Windsurf, or Cloud Code inside your setup. I'll walk you through exactly how this method works, and by the end, I'll show you how to install it and get it running inside your own project. Since the Cloud Code method I previously (t: 150) showed lacked proper documentation, you might be wondering how you're actually going to learn this new workflow. Well, that's exactly why I'm making this video. The BMAD method doesn't have that (t: 160) issue. It comes with solid user documentation built right in. It's complete, clear, and everything you need is there. But here's the thing. I'm going to show you the entire workflow step-by-step (t: 170) in this video, so you'll only need to dive into the documentation if you want access to extra commands and additional information. The method also includes a dedicated agent called Orchestrator. (t: 180) When it's loaded, as you can see here, it gets activated and guides you through the entire process. It helps you understand how the system works and keeps everything moving smoothly. (t: 190) Here's the best part. This workflow isn't just for software developers. It's for anyone who wants to build and ship software, and the entire process can be handled by AI agents. (t: 200) I'll also show you a bonus method, using AI to dive deeper into this project after you've learned the workflow, because there's so much potential here that's seriously worth exploring further. (t: 210) To start the workflow, I needed two files, the PRD and the architecture file which the agents need to (t: 220) make the full app. For that, I went back to the GitHub repository and navigated to the dist folder, then into Teams and opened the team-fullstack.txt file. You can download this file. It contains (t: 230) everything the AI agent needs. I use this for both the project and the workflow. I also have a lot of resources that I can use to build and ship software. I'm going to show you how to do this. I'm going to show you how to build and ship software. I'm going to show you how to build and ship software. I'm going to show you how to build and ship software. I'm going to show you how to build and ship software. I'm going to show you how to build and ship software. I'm going to show you how to build and ship software. I'm going to show you how to build and ship software. I'm going to show you how to build and ship software. They recommend Gemini since it's most cost effective, but ChatGPT works perfectly fine. (t: 240) I uploaded the file with their recommended instruction, where I told the AI that the operating system instructions were attached, and it must not break character. I used the (t: 250) brainstorm command. In this framework, the asterisk symbol triggers different built-in commands for both commands and agents. The brainstorm command started asking me questions to understand what I (t: 260) wanted to build. I described the commands in a simple way, and I added a few of them to the framework. I added a few of the commands to the framework. I added a few of the commands to the I described an iOS productivity app with feature ideas. It set up a brainstorming session and gave (t: 270) me different paths to proceed. I explained what I wanted and didn't want, and it suggested new features like iOS widgets. For UI design, it provided Sample interactions, color coded (t: 280) categories, Smart Filters, and tapped to expand Task Views. After brainstorming the entire app, it suggested moving forward with a Feature Matrix. It generated a road map with Version 1 as the MVP (t: 290) and v2 the PHP code as the equals. After I set up all the tools I had, Iars 돌아wa awer with this documentation. I decided to expand my things to a Ticheline wallet where I wouldn't need a Toggle. café In Cartology method, I just did a SQL brain Hamilton macro which wasn't interestingly with additional features. I made it clear I didn't want voice capture for this to-do list style productivity app. Then it generated my brainstorm markdown file. I moved to the next command, (t: 300) PM, which switched the role to product manager. I used the create doc command, which started building the PRD through a step-by-step workflow with five stages. Each stage provided menus with (t: 310) decisions real software teams make when drafting PRDs. The workflow proved solid. When I accidentally (t: 320) pressed zero, it immediately told me that wasn't a valid option and provided clear feedback throughout. I completed the entire PRD process and downloaded the final PRD. Then I switched to (t: 330) architect, which builds the app's architecture, including tech stack and component connections. The documentation step built out a full architecture plan in interactive mode. With both key files (t: 340) ready, the PRD and app architecture, I was ready to move to the IDE and start building. You can open any IDE you want, whether it's cloud code or (t: 350) cursive code, or you can open any IDE you want, whether it's cloud code or cursive code, or you can open any IDE you want, whether it's cloud code or cursive code, or you can open any IDE you want, and you'll follow these steps. Once you've completed the installation, which I'll show at the end of the video, you'll see these folders have been created, a cloud folder, a cursor folder, and (t: 360) inside these, predefined roles representing different roles in a typical software team. After getting your PRD and architecture files, you'll use these roles in a step-by-step process (t: 370) to build out your product. This workflow loops through each role methodically. There's one manual step. You'll need to create a folder named docs and place the architecture.md and prd. (t: 380) md files inside it. The other folders are generated automatically by the agents. The first agent is the PO agent, which stands for product owner. This is the agent you'll initialize first. (t: 390) In cursor, agents are initialized using a special command format. You type PO, it suggests the agent, you select it, and start it. In cloud code, you use a slash command instead. Even though I'm using (t: 400) cursor in this walkthrough, you can follow the same process in whatever ID you're using. I'll show you the full list of supported IDs at the end. (t: 410) Once the PO agent is active, you'll use the shard doc command and give it the path to your documentation files, both the PRD and the architecture file. The term shard refers to (t: 420) how it breaks your PRD and architecture documents into smaller index chunks so they can be followed step-by-step, organizing everything into manageable tasks. This is where the agile-driven approach (t: 430) kicks in. Instead of building the whole app at once, you focus on one piece at a time, designing it, building it, testing it, then moving to the next. (t: 440) Once sharding is done, it confirms completion and you're ready to move to the next agent. Moving to the next agent, the scrum master. I initialized it using the cursor rule file and (t: 450) ran its method called draft. It checked my PRD and saw that everything was well-defined, except that I hadn't generated any epics. Epics are supposed to be created using a separate method called create epic, which should have been run earlier in chat GPT. (t: 460) This would have broken down the PRD into multiple epics, four epics total in this case. The workflow is tight, so the agent, (t: 470) detected this and immediately offered to run the epic generation for me. I selected the option by typing one and it automatically created epics based on my PRD. Epics are big chunks of the PRD divided into major steps. Inside each epic are stories. (t: 480) For example, epic 1 has stories like 1.1, 1.2 and so on. Epic 4 has stories going up to 4.4. (t: 490) I ended up with 16 stories total, four per epic. Once the epics were done, the scrum master agent generated all the stories that are in the epic. I then added a new one to the list, which was called, (t: 500) all of the stories. Story 1.1, for example, is about basic task creation and storage. When these stories are first created, their status is set to draft. This needs to be manually changed (t: 510) to approved by typing that into the file and saving it. That approval signals to the next agent, usually the developer, that the story is ready to be worked on. (t: 520) Once I change the status to approved, I move to the next agent, which is the dev agent. The dev agent is straightforward. It starts up and loads, then asks which story to implement. (t: 530) In real-world software, stories aren't always implemented sequentially. Some can be done independently. I told it to begin with story 1.1, and it followed the story exactly. The story doesn't just contain (t: 540) tasks. It breaks those tasks down into subtasks. It's incredibly modular, tightly controlled, and nothing is left vague for the AI. I started each agent in a new chat, which is important. (t: 550) These files are large and take up context space. Splitting agents into separate chats helps avoid confusion and keeps things simple. I've added a new agent to the list, which is the dev agent. (t: 560) Once the story is complete, its status changes to ready for review, not done. This is intentional. These stories need to go through testing before they're considered fully complete. (t: 570) Oh, and if you're enjoying the content, please hit that subscribe button. We've also launched channel memberships. 93 people have already joined our first tier. Members get priority replies to (t: 580) comments, so if you need feedback or have questions, that's the best way to reach us. We're considering adding more tiers based on the incredible support so far. Testing has its own way of doing things. It's a very simple way to do things. It's a very simple way to do things. (t: 590) I initialized it with its rule file, called the review method, and it checked that everything in the story was implemented correctly. It scans the code base for the specific items the story (t: 600) required. If it doesn't spot any critical issues, or after it autofixes repetitive code with active refactoring, it marks the story as approved. Once that happens, the agent flips the story's (t: 610) status from review to done, completing the full lifecycle of a story. From there, the process moves to the next story in the epic. This mirrors how the story is going to be reviewed. (t: 620) The agent then locates all epics, checks each story's status, and only works on those that are ready. I could have it spin up five new stories at once. It will still verify whether earlier stories are (t: 630) complete before moving forward. That's what makes the workflow so robust. Starting each agent in a new chat avoids context-length issues. In my case, the agent saw that the next item was (t: 640) task list display and basic organization. It pulled key insights from the previous story, plus architecture, and then added a new one. The agent then added a new one. This is how the story is going to be reviewed. (t: 650) I can now see the text in the text box. I can see the text in the text box. I can see the text in the text box. Bundled that into a single story packet and handed it off. At that point, I opened a fresh chat, started the dev agent again, and it immediately began work on the next story. Easy, controlled, (t: 660) and perfectly in line with real agile development. That was the entire workflow. Before showing the installation, I'll explain how I understood this system. I opened the GitHub link for the bmad (t: 670) method and changed the URL by replacing GitHub with git ingest. This tool converts any GitHub repo into a format-readable, user-friendly, and user-friendly. I can now see the text in the text box. I opened the file, pasted the git ingest link, uploaded the file, and asked questions about the (t: 680) workflow. Claude explained everything step by step, which is how I understood the full workflow before (t: 690) running it myself. Now for the installation process. The installation is actually pretty easy. You don't need to copy-paste all the files. Start by heading to the GitHub repository and (t: 700) copying the npx command provided there. Open the terminal in Claude code or cursor and paste that command. This set up is very easy. You can also use the command to copy and paste the files. This setup is project specific, so it needs to be run separately inside each project where the bmad (t: 710) workflow will be used. This keeps everything self-contained and clean. Once the command is pasted, the installer starts up. First, it asks for the full path to the project directory. After (t: 720) providing the path, it asks what to install. Select the first option, install bmad-agile-core-system. (t: 730) This sets up the .bmad core inside the project. Next, it asks if the PRD should be split into multiple files. I selected yes. I'll paste the .bmad-agile-core-system into the project. Next, I'll paste the .bmad-agile-core-system into the project. Next, I'll paste the .bmad-agile-system into the project. Next, it asks if the architecture documentation should also be sharded. I selected (t: 740) yes again. The installer then shows IDE selection options. Multiple IDEs can be selected at once. (t: 750) I selected cursor, Claude code, and windsurf. After confirming, it asks about including prebuilt web bundles. Since I followed the method shown earlier, I selected no. Once installation is (t: 760) complete, all the rule files appear inside the project. The IDE might need to be restarted for them to show up, but once that's done, they're ready to go. For example, if I want to install a new project, I can just click on the install button and then click on the install button. For example, if I type pm, the pm.mdc file shows up and can be run to follow the full bmad workflow. (t: 770) That brings us to the end of this video. If you'd like to support the channel and help (t: 780) us keep making videos like this, you can do so by using the super thanks button below. As always, thank you for watching and I'll see you in the next one.

