---
title: New Claude Code Output Styles Are HUGE
artist: Income stream surfers
date: 2025-08-15
url: https://www.youtube.com/watch?v=IokDmpuXTrQ
---

(t: 0) Hey guys, welcome to this video where I'm going to be going through the latest Claude Code updates. This latest update is very very interesting because it does something or it allows something that you've never ever been able to do before on Claude Code. (t: 10) Now we can finally do this thing and I'm going to tell you what that is in just a second. So there's been a few updates you can see here, but we want to be talking about 1.081 right here. (t: 20) So I'll just show you how to get that on your system. So if I do Claude dash dash version, if this says anything other than 1.081, then you can do Claude update in order to update your Claude Code. (t: 30) Now let's do <PERSON> here so that we're inside Claude Code and then we'll click on the output styles. (t: 40) This is the new feature which I will show you the amazing thing that we actually haven't been able to do before. (t: 50) So it says output styles versus agents here. Output styles directly affect the main agent loop and only affect the system prompt. As far as I understand it. This is the first time that we've actually been able to change the system prompt. (t: 60) So for me, that is extremely interesting. So let's talk a little bit about this. Okay, so let's talk about output style. (t: 70) So adapt <PERSON> code for users beyond software engineering. Output styles allow you to use Claude code as any type of agent while keeping its core capabilities such as running local scripts, reading, writing files and tracking to do's. (t: 80) Built in output styles. Claude code's default output style is the existing system prompt designed to help you complete. Software engineering tasks efficiently. (t: 90) There are two additional built in output styles focused on teaching you the code base and how Claude code operates. Explanatory provides educational insights in between helping you complete software engineering tasks, helps you understand implementation choices and code base patterns. (t: 100) And then learning collaborative learn by do learn by doing mode where Claude and this exists. (t: 110) And this is pretty interesting where Claude will not only share insights while coding, but also ask you to contribute small strategic pieces of code yourself. Claude will add. Claude will add. To do human markers in your code for you to implement. (t: 120) That is super interesting. So Claude code might actually be the best way to learn how to actually code as well. Our output styles work. (t: 130) Output styles directly modify Claude code system prompt. Non. So this is the interesting thing. Non default output styles exclude instructions, specific to code generation and efficient output normally built into Claude code, such as responding concisely and verifying code with tests. (t: 140) Instead, these output styles have their own custom instructions added to the system prompt. So you can either so change your output style. (t: 150) You can either run slash output style to access the main menu. So let's do that now. So slash. So slash output style. (t: 160) And then this menu comes up and let's say we wanted to go to the learning one. So Claude pauses and asks you to write small pieces of code for hands on practice or, for example, explanatory. (t: 170) Claude explains its implementation choices and code based patterns. Interesting. So now let's do slash output style new. So it looks like it's now going to help us create one. (t: 180) So to set up an output style with Claude's run help run output style new. I want to blabber flat. Okay. So let's just exit out here. (t: 190) You Claude. Yes. And then slash output style. And then you write what you want here. (t: 200) So you write the style that you want here. So you can see here, it says by default output styles created through slash output style. New. I saved as Markdown files. Add. And then I'm going to type in the name of the user level in Claude output styles and can be used across projects. (t: 210) They have the following structure. So you can actually just give this structure to Claude code or it probably has it already or chat GPT five, for example, and you can work on something specific that you want. (t: 220) Now, one use of this for me would be to just slightly alter the system prompt of the software engineering part of Claude code, which is, you know, totally fine. (t: 230) But I just I would say just be careful with this feature. Because you don't want to use it to completely change how poor code works because Claude code was designed by anthropic. (t: 240) So let's just finish this on here. Comparison to related features, output styles versus Claude. End versus append system prompt. (t: 250) I didn't even know this existed. So, but it looks like it didn't actually change the system prompt output styles completely turn off the parts of Claude code default system prompt specific software engineering. (t: 260) Neither Claude. MD nor append system prompt edit Claude codes. Default system prompt. Claude. MD adds the content as a user. Message following Claude codes. (t: 270) Default system prompt. Append system prompt appends the content to the system prompt. Okay, interesting. So this is the first time you've actually been able to edit the system prompt itself. It looks like output styles versus output styles versus agents. (t: 280) Output styles directly affect the main agent loop and only affect the system prompt agents are invoked to handle specific tasks and can include additional settings like a model to use the tools they have available and some context about when to use the agent. (t: 290) Output styles versus custom slash commands. (t: 300) You can think of output styles as store system prompts and custom slash commands as store prompts. Now I need to talk about this for a second because this actually thinking about it is an absolute game changer. (t: 310) I don't have an example necessarily right now, but if you watch some of my videos, I talk a lot about like behavior and specifics with models. (t: 320) Right? So behavior. Sorry if I spell that wrong. Okay. Behavior and specifics. (t: 330) So behavior is like what you want Claude code to do. So for example, one thing, one issue that Claude code has is that when it tries to create a next GS project, because it has to say yes or no in the shell to questions, it can't actually do this right now. (t: 340) Now I know there are MCPs and things, but what you could actually do with this is you could set it up so that the behavior of, of what. (t: 350) Claude code actually does. So you're completely changing the behavior. (t: 360) So you can say, instead of using MPX create, and then, you know, getting stuck here, you can actually say, use when, when you want to create an XGS project, use the specific command, which one shots it. (t: 370) And then the specifics are the user prompt, right? (t: 380) So the way to change the behavior of something like Claude code is to change the system prompt. The way to change. What? It's specifically is working on is the user prompt, right? (t: 390) Which is what you write to Claude code. So the fact that we can now change the system prompt and make it into something else basically means that they've just given us a Ferrari and yeah, we can just do whatever the fuck we want with that Ferrari. (t: 410) Now, I don't have the time right now to kind of look into this. I will have a lot more videos on this. But basically. What I'm telling you is that Claude code is no longer just an AI dev. (t: 420) It's an AI anything because you can change what it does because you can change the system prompt, right? (t: 430) So for example, if you wanted a very specific version of Claude code that is extremely specific to UI UX, it uses the playwright MCP. (t: 440) It knows that has to go through every single page of the website and do full testing and everything. You can now do that because you can change the system. Prompt and it's got the intelligence of Claude code generally and your but it's removing the software engineer prompt and you can actually add whatever prompt you want so you can make it do whatever you want. (t: 460) Now the way that we used to do this by the way is we would edit the Claude MD file, but this was not the best system. I can tell you from experience that there is a big difference between it reading a file and it having something in side the system. (t: 470) Prompt now, if you think that you can prompt better than anthropic as well, you can now make it work in a different way for you to code specifically. (t: 480) The possibilities are endless guys. This plus custom agents. (t: 490) I'm going to have a lot more content on this soon. This stuff is absolutely crazy. I will leave the video here guys. I just wanted to give a very quick rundown of this update. Why will be using it for and what maybe you guys can start using it for let me know in the comments if you've used it for anything and what you used it for. (t: 500) And yeah, thank you so much for watching. If you're watching all the way to the end of video. I'm still legend. I'll see you very soon with some more content. (t: 510) Peace out.

