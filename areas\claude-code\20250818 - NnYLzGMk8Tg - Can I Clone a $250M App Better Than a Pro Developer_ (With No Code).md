---
title: Can <PERSON> Clone a $250M App Better Than a Pro Developer? (With No Code)
artist: <PERSON>
date: 2025-08-18
url: https://www.youtube.com/watch?v=NnYLzGMk8Tg
---

(t: 0) Can a vibe coder make a better app than a senior iOS engineer? Well, that's exactly what we're going to try and find out today. In this video, <PERSON>, <PERSON>, a vibe coder, am taking on <PERSON><PERSON><PERSON>, (t: 10) a senior iOS developer who's been building iOS apps for the past 10 years. We're going head-to-head to see who can build a better clone of Granola, (t: 20) a $250 million app with multiple AI features. We each only have five prompts to build this app, and we can only use AI. (t: 30) And in this video, we're going to see whose app is better. And so <PERSON><PERSON><PERSON> is going to be using CloudCode, and I'm going to be using a tool made specifically for vibe coding mobile apps. (t: 40) And you're going to be the judge of whose app is better in the comments, and we're going to be giving $1,000 of credits to three people who vote in the comments below. (t: 50) And if you like videos like these, make sure to hit that like button, and subscribe button, because if this video does well, we're just going to keep doing them. (t: 60) Let's not waste any more time. Let's dive into the video. So here are the rules. Obviously it is a senior iOS developer versus a vibe coder. I'm going to get a slight advantage since you're going first, (t: 70) I will kind of get to see what your first prompt is, right? So we're each going to have five prompts to create a Granola clone. Granola, for those of you who don't know, is a, at this point, (t: 80) their last valuation was $250 million company. And it is a AI note taker app. And so that's what we're trying to create today. The challenge after five prompts, who can get the closest to this app? (t: 90) And the specific features that we want today are, is an app that records voice and audio. (t: 100) It transcribes that audio into just a transcript, and then it syncs with calendar so that when you record a meeting, it shows up in your calendar at that specific time. There is an AI summary. (t: 110) So after transcription, it gives an AI summary, and then there are folders. That the user can add and it can, they can add the meetings to different folders. (t: 120) Does that make sense? Yeah, makes sense. So the way that we're going to do this is we're going to do this snake style. So 10 total prompts, it's going to go Vishal, Riley, Riley, (t: 130) Vishal, Vishal, Riley, Riley, Vishal, Vishal, Riley, 10 total prompts. So what is, what is your stack? Yeah. So I'm thinking, you know, building a Swift app or building an iOS app. (t: 140) So we've got to use Swift. That means we're going to use Xcode for sure. As our iOS app. And in terms of where I'm going to be prompting, I'm going to be rolling with Claude code. (t: 150) Legend. The most talked about AI tool right now, I would say is Claude code for sure. Yeah. And I think in practice too, I've seen Claude code probably out edges GPT-5 in terms of Swift, but I mean, (t: 160) we're putting that to the test right now. Let's do it. So what you should do right now is go ahead and talk us through your setup. (t: 170) You're going to get set up with your stack and then go ahead and you can do your first prompt. Yeah, let's do it. I think the first thing that we're going to do is probably open up Xcode and create a directory for this. (t: 180) So let's get started and make a new project. We're going to be making an app here and let's pick a name. Hmm. I guess we're, we're cloning granola. (t: 190) So, you know, I think it's only right. We call it serial. That's great. So let's, so you're basically just creating the folder where this project will exist. (t: 200) Exactly. It kind of creates some like base template. I guess we'll use simulator so folks can see as well. I remember my first time seeing simulator. (t: 210) I tried to do every single thing I could do on my phone on the simulator to see like, you know, maybe you don't need a phone. Right. But it's not that good. Can't make calls yet. As you can see, there's like nothing here. (t: 220) This like main app file, which kind of houses the root directory of this app. And then this one content view where the only thing inside you can see is like an image and texts that are vertically. (t: 230) Aligned. Got you. Got you. And so what are you going to be using to generate the code? Cause Xcode doesn't generate or can kind of, but it's not that good. (t: 240) It has some like base kind of auto complete, but it's not really good at all. And it definitely can't do like multi-file edits that I've seen. (t: 250) Yeah. So for that, we're going to be using Claude code in terminal. So what I'll do is like, I'll go ahead and open up my terminal. So right now I'll find, so LS I'll list kind of the directories here. (t: 260) CDL I'll go into. Yeah. Yeah. I'll go into the five code folder, check it again, and we have serial. (t: 270) So CD serial, what does that mean? I'm basically just moving into the serial director. So what we're going to do here is we'll get started with Claude code and you know, they've made this really easy. (t: 280) You know, all you got to do is type Claude. So we'll go ahead and proceed here and here we are. So we can just start prompting immediately. Not going to waste any prompts yet, but yeah, this is initialized in this directory and now Claude code because I'm in serial. (t: 290) Has access. To like view and edit anything in this serial directory. So you're ready to prompt. (t: 300) Yeah. All right. We're ready to go. So let's get the first prompt. Let's do it. Let's build the serial. So looking at the kind of instructions here again, we're, we want to record voice and audio transcribe audio sync with calendar. (t: 310) When there's a meeting, it should show up and I should be able to click on that and presumably start recording. And after the recording is done, I should have some kind of transcription, but also an AI summary. (t: 320) So there's going to be some. Kind of like speech to text element as well. And then lastly, users can create folders and there's like an organizational component too. (t: 330) So I think a good place for us to get started. I think we'll leave folders out of this prompt for now, just because I think folders is something that Claude code is probably pretty good at just reorganizing at the end. (t: 340) Right. We're going to start off with, let's see if with this one prompt, we can get to a state where we can transcribe audio and see it list out for conversations. (t: 350) So let's start with our prompt. You are a senior iOS engineer helping me build a granola app granola style app for context. (t: 360) Granola is an app that lets users record meetings and then it uses AI to automatically transcribe the meeting as well as come up with AI generated meeting summaries. (t: 380) We want to create a similar. A similar. Style app where users can record meetings that are on their calendar and then view trends, transcriptions and AI summaries after the recording has finished. (t: 400) Your job is to help me build this in accordance with best practices for a swift native development with a iOS target app and whichever. (t: 410) AI frameworks are the best available for the functionality we are trying to reproduce. (t: 420) Got you. Wow. Yeah. So I think we got the gist of it. So you seem, you got all of them, but the, but the folders, but the folders that's probably, yeah, that is probably a good idea. (t: 430) Yeah. And I think like broadly, it's good to break down the problems where like, if you have an organizational or folders is really just like a UI thing. (t: 440) It's not really a functionality. Yeah. Yeah. Yeah. Yeah. And, and I'll Pikachu, but for. Post in development, you do that wealth. For cloudazi over here. Remember if you couldn't actually run up to a specific detail perhaps, on our a, some other occasions, you might just pop in quickly. (t: 450) And, you know, that, what we saw over here and we did the kind of API proof shifters. And if you guys. I'll do серge this. Some initial blood test for Windows too, but really before that, if you wanted to see. You might want to. A Roadreens. The build out of handy. This just built from Azure. Then start, you want to get down. (t: 460) That's really important. To make it move full. One. Now if you want to import a whole structure from an easy release. And anthropology standard. And my parents may forgot about souvenirs. (t: 470) services resources and then you're just like data models and then it's i guess going to set those up (t: 480) start off with the calendar integration and then do the audio recording using both using event kit and av audio recorder which are actually ios native frameworks so both of those are maintained (t: 490) and used by apple it's going to integrate a speech framework for transcription but it actually hasn't said which one yet so i'm curious here's what it's going to say right when we get there um (t: 500) there's so many steps in this one it's actually wild yeah wait one two three four five six seven eight nine ten steps for this yeah damn and then set up a oh wow we're already going so i guess (t: 510) this is the model for the the meeting itself start date end date attendees interesting so this is (t: 520) probably based on the event kit framework i never mentioned this but uh you guys are all going to be (t: 530) voting for whose granola app is the best and we're giving a three-part video on how to use granola app and we're giving a three-part video on how to use how to use granola app and we're giving a one-on-one conversation where it's super simple it'll be stormbed ultimately legitimized and it'll give it like a three-part review off the top eight tadpole app you're like oh it doesn't really matter what it would be like scientific if it was like the phone app or the gev, you could do (t: 540) nothing there it's a you Hey, it's not the aik been doing that well, it's like, there's a fiction, that's a thing that (t: 560) called cloud-based innovation, which is it's kind of like you know like a Korean egg. transcription service using the apple speech framework so let's just oh it's using apple speech framework yeah so it chose interestingly to stick with apple looks like it's almost done (t: 570) now oh your app is ready we're done okay this is fun so now remember before you entered this prompt (t: 580) it was just hello world yeah and we're gonna see it now so you hit the play button no editing no no editing nothing just the play button oh no build failed okay it looks like it's only one (t: 590) error so um what you're gonna have to do is if maybe you use one of your prompts on fixing an (t: 600) error that's part of this game yeah unfortunately you can't test it because it failed so it looks like next turn that you use is gonna have to be fixing an error if i run into the same thing (t: 610) same thing like that's part of the risk of tackling too many things at the beginning yeah um i'm not allowed to edit the code none no you only get prompts into the ai that's it that's it (t: 620) okay i i like it let's let's see what these errors are okay okay so there's two errors here one is in our calendar service you can see the guard body must not fall (t: 630) through consider using a return or a throw to exit the scope so one thing that we're gonna do (t: 640) is we're just gonna copy this one we're gonna go to cloud code and say there are some errors i'm going to paste all of them and you will help me yeah cool you will help me fix these errors so (t: 650) that i can test the changes that we have made right so i'll paste them but i won't send it yet (t: 660) and what's cool is is like even if you fail like even if like you ran into this fail but if you can fix it in one prompt you're still only on prompt three and you only have one feature left to add (t: 670) exactly so this other one is so it's a uh uh uh not use an optional chaining on non-optional value URL. This one is just a classic optional (t: 680) versus non-optional Swift error. What was the first error about? Was it about the calendar? So the first one was, it's not about the calendar per se, but it's basically saying that if the (t: 690) guard here, so a guard is away, this authorization status, this guard is just checking that it needs (t: 700) to be full access. If it's not, it's going to request. And this is essentially saying that we need to exit the scope if this guard fails. As in when you enter in this like else block, it needs (t: 710) to be able to get out of this fetch event. So it's telling us either return nothing or throw an error. (t: 720) But you got to do something. Gotcha. Yeah. But I think, you know, these errors aren't too bad. So I'll just keep it for here now. Should I click enter? I guess if you click enter, that will be (t: 730) your turn. That'll be my turn two. And then when we come back, this will be done. And so then we'll be ready to go for your turn three. All right. Perfect. Cool. Okay. So let's switch it to me. (t: 740) So now I guess I'll introduce my stack. We can mark these off just so everyone kind of understands where we are in this. If you follow along on the snake part of this. So you just did (t: 750) this turn and then you entered this turn. Yeah. And so you're basically two prompts deep. You're about to test and enter your third. I still need to do my first and second. So for my stack, (t: 760) I'm going to be, you know, I'm a vibe coder, right? I was born and raised a vibe coder. So we're going to be using vibe code and we can use either the web app or the mobile app. So this (t: 770) is just my phone view. So the shawl can see what's going on too. I'll set this here. Being a vibe coder. There's a lot that you just did, right? Like you opened a terminal, which is scary X code, (t: 780) which is scary. Um, vibe code, I think is the best way to build mobile apps to kind of, um, not go down that path of complexity and it just makes it more simple. So we're going to be using (t: 790) this tool. And let's start thinking about the first prompt here. So before you do your first prompt, what's your model of choice? What's my model of choice. Okay. Before we do that, (t: 800) why don't you look in cloud code and see what model you are using? I don't know if you can do that if it's done, but if it is done, you should check because I think I should match you (t: 810) whatever you did. Okay. So if you are using cloud code and you just type slash model, you can see what model you're running. You're on default. I'm on default. It said it, (t: 820) it said each set isиной. So I'm going to do different models and just add multiple models to see if I'm getting pretty good. Okay? Okay. So let me do my first one here. So this one is (t: 830) set, make you apply. So this next mengen start set him a very quick and easy Пос Tom you need to try to add three fonts. So open Bl� deme. What are you? Okay. Leave me a copy. Okay. And (t: 840) let me. Okay. So in this case, I'm just gonna addということで or spunk. (t: 850) Okay. Spunk. So I saw what you did. I think, I have a hunch that the hardest feature for me to add will be the calendar. So I'm gonna do four of these, (t: 860) but I'm gonna wait and do the calendar one. I'm gonna add that at the end. Why do you think calendar is the hardest? I just, I don't know why. I know, I mean, I vibe code a lot. (t: 870) You know me, I vibe code every day. I've built apps very similar to this and I don't think I'll have a problem with any of them. I just have, I've never done calendar integration. So for that reason, (t: 880) I'm gonna stick to what I know and then do the hard part at the end. That's just what my instincts are telling me and I'm gonna rely on my instincts. I have good vibe instincts. Yeah. Okay, so let's chip this. (t: 890) So I'm going to start off kind of like, I need the greatest AI meeting note taker app of all time. It's probably not the best way to start off the prompt, (t: 900) but I don't care. It's gonna be great. We are creating an app that is very similar to Granola, except I want you to focus on these features first. I'm just gonna copy. (t: 910) So these are the features that I want. Records voice. It transcribes that voice into audio. (t: 920) I'm going to skip this one for later, right? We're gonna add this one in in just a second. After transcription is complete, it automatically generates an AI summary. (t: 930) I can create folders and add meetings to that folder. So on the home screen of the app, (t: 940) I should see a list of all my meetings. If I click on one of the meetings, it should take me to a meetings details page. The meeting details page should have the transcript (t: 950) and the AI summary, and then all of the other information you might need, including date and time, and then anything else you would absolutely need. (t: 960) Don't veer off and try and add anything beyond the scope. But if there's something that I clearly missed, go ahead and add that because in the future, not now, but in the future, (t: 970) we're going to be adding a calendar integration. Not yet. I just want you to prepare for that. So build it with that in mind. This is going to integrate with the calendar. And that is basically the full scope of the app. (t: 980) But I want you to focus on these four features that I listed above first. Act like the greatest engineer of all time and make this happen. Let's go. What are your thoughts? (t: 990) I think that's a great prompt. I think probably good intuition on not including the calendar. Let's just run it. I just want to run it. We're going to ship this in. All right. It's going. It is. (t: 1000) I'm letting Jesus take the wheel here. My first prompt is done. Anyway, what were you saying? Sorry. So for calendar, for access to your calendar, it's going to be using EventKit. That is like the native Apple calendar access framework. (t: 1010) And that's why I think it's, you know, probably good intuition to not have included it in the vibe code prompt first to focus first on like the core functionality (t: 1020) and then include like Apple specific frameworks after. Oh, I didn't specify the open. AI API. (t: 1030) That's one regret I have, but that's okay. I, oh, integrate transcription with open AI 4.0. That's crazy. Go or GPT 4.0. So Vishal went first and he got an error and then he entered in his second prompt. (t: 1040) And we're about to just take a, take a look. We're not going to enter in any prompts, but we're going to take a look and see how that did. I just entered my first prompt. (t: 1050) So I'm done with my first prompt. So after we check on Vishal's, we'll come back to mine. Okay. Let's take a look. Uh, so looking at Claude. Code here, both the build errors have been fixed. (t: 1060) Okay, cool. So remove some unnecessary chaining, changed it to an if so avoiding the fall through. Okay. (t: 1070) Let's see if it builds. Build failed again. Okay. (t: 1074) Oh, this, this, this issue seems like so derive data is probably the (t: 1080) bane of every iOS developers existence. Uh, there's just. Some like cached build information that is preventing this build from working. (t: 1090) Is there an actual error? We don't know, but what I'm going to go ahead and do is clear the derived data. (t: 1100) We were having a derived data info P list issue and we fixed that by just removing it. Seems like it was just copying it twice in the building phase. So we were able to fix that and let's give it a run while Riley's is still chugging away. (t: 1110) Let's see it. Okay. Okay. Serial. Okay. So it works. So now it's. The first thing that it did is, is asking for access to your calendar, which is what you want. (t: 1120) Yeah. So we're going to allow full access, access the mic. This is good. It's like the main two things we need and speech recognition. (t: 1130) So one is like, can I listen to audio? And the other is, can I transcribe the speech? That makes sense. And I think mine is going to use Apple's speech recognition. (t: 1140) So probably what they use for dictation mode. Got you. Yeah. Let's ship this dude. Oh, wow. I. Okay. Okay. Meeting. Should we give it a try? (t: 1150) I don't see a record button. Yeah. So I, I might have to click on a meeting. Let's see. Okay. Oh, okay. I click on a meeting to record. (t: 1160) How do you add a meeting? Oh, it's oh, the way yours is set up. It goes to the calendar. Oh, you, oh, you schedule a meeting on your calendar. You click on it, then you pl okay. (t: 1170) This makes sense. So let's, let's just see if it works. Let's let's add a calendar event. Podcast with Riley. Podcast. With Riley. Location. Vibe code. (t: 1180) HQ. It is vibe code HQ. Okay. So we're gonna add that in. Let's go back to our app here. Click this refresh. Perfect. Click on it. Wow. Wow. (t: 1190) Okay. This is good. It's looking good. Wow. Shall we record? Yeah, do it. Do it. Okay. Oh no. That looks good. (t: 1200) It's a sick animation. That looks really good. I would change the coloring a little bit. I would change the color. I would change the color. I would change the color. I would change the color. I would change the color. Okay. So, I think, Riley and Vashal are in a huge argument about Yu-Gi-Oh! (t: 1210) And whether or not it's actually anime or not. (t: 1220) This is just for recording purposes. Yeah. I think it is not real anime but Riley does. Okay. And that is Vashal speaking. And I am Riley speaking. (t: 1230) And I do. Yeah. Okay. Stop. Okay. Oh, no. Yes! OpenAI API key is missing. Oh, okay, okay. You can add the API. Please add it to the environment variables. (t: 1240) You can add the API key. That's okay. That's okay. Just to switch it back to me while you're going to that. Like, okay, so just a reminder, if we take a look and see what Vishal has done. Vishal did his first prompt. (t: 1250) His second prompt was fixing the error. And so you're about to enter your third prompt. But you're two prompts in. Yes. (t: 1260) Okay. So you're about to go into that. And right now you're getting your API key. So we've added the open AI API key to the environment for this Xcode project. (t: 1270) One unique thing about Xcode is like the way you add it is you kind of click on your target up here and you click edit scheme. (t: 1280) So unlike, you know, React Native apps, you might just be adding it to the dot ENV. Here you do edit scheme and you add it as an environment variable here. (t: 1290) Let's see if it works. So click play. Build succeeded. Let's hope it works. Let's hope it fails. This will be big. This will be big if it fails because then you'd be on prompt four. (t: 1300) I will be on prompt three. You'll be on prompt three, but you'll have to fix it. Yeah. Prompt three will be fixing it. So I'm and I don't have folders yet. You don't have folders. (t: 1310) Let's see. Okay. Ready to record. Okay. We're here filming a episode with Riley in the studio. Riley, do you want to say anything? (t: 1320) Yes. Please fail. Please don't fail. Okay. Okay. Let's see if it worked. Recording? Yes! No. Wait. (t: 1330) Did it get the transcript? Let's see. The error, though, is failed to generate summary from API. So let's see if it got the transcript, though. (t: 1340) Failed to generate summary from API. Interesting. Looks like the transcript worked. Okay. So you did get the transcript. Okay. So your transcript did work. So it's using the Apple Speech Library. (t: 1350) It's able to get the transcript and transcribe what I'm saying. the OpenAI API call didn't work. Okay. Right. And yeah. And so like, so what we're doing here (t: 1360) is we're basically recording our voice. Then we're using a speech to text model, which is Whisper to convert it into a transcript and then immediately converting that transcript (t: 1370) into a summary, right? And so imagine if you talked for an hour, it would immediately just grab the whole transcript, which is useful. You can command F it, but you also want that quick summary. (t: 1380) So you have like action items, et cetera. Yeah. So it failed on that last part. Yeah. Except this one's not using Whisper. Yours might be. Oh yeah. You're using a different one. I'm using, (t: 1390) cause Cloud Code chose to go with the Apple native speech to text, which honestly is probably worse. It probably is worse. I mean, that's the one that if you press on the bottom right of your phone while you're recording, (t: 1400) that's the same one, right? Yes. Wow. Yeah. That one is worse for sure. It's significantly worse. But maybe, maybe fingers crossed if I have three prompts. (t: 1410) So one bug fix, one folders, yeah, yeah, yeah. I mean, I mean, you're in a decent position. You're in a decent position. I still have four prompts left. Keep that in mind. Let's go back. Let's go back to our app. (t: 1420) I think we'll come back to prompt three in a second. So, okay. So we'll go back to my screen here. Here's where we are. All we've done is one prompt, right? (t: 1430) You can see that all we had was our first prompt and we're still cooking along here. I am going to hit this refresh button. (t: 1440) Let's see what it looks like. No meetings yet. You see this? I know you can't see it, but no meetings yet. Okay. It looks pretty good. Yeah. It looks great. I'm nervous. I'm nervous. Where are you going to get the calendar meetings though? (t: 1450) Yeah. The calendar part is tough, but I think we can, I think we can, it's modifiable. I don't think it's like, I saw a calendar button on the UI. So here. Yeah. (t: 1460) Oh, sorry. That's the folder button. That's a folder bit. Okay. So I'm going to test it. So we have folders. I'm not going to look at folders until we do record something. So, okay. (t: 1470) This looks pretty good. It's, quite simple. Meeting title at the top. So I'm just going to call this podcast with Vishal and then return. (t: 1480) And now, okay. It is ugly. All right. I'm recording with Vishal. We are testing the recording feature. (t: 1490) Wow. Is the styling bad? But nothing has technically failed yet. We'll see after this. No build failures. (t: 1500) No build failures. Nothing. Let's see. Transcribing. Okay. I mean, this is a good sign. Usually if it fails, it would have already failed. So it's doing something, but now it's taking a little long. (t: 1510) It is a little long. Whisper should not be taking this long. If we get caught in this infinite loop, (t: 1520) trying to figure out why. Hmm. Cause we don't know whether or not it's failing on which step it's failing. We don't know. Yeah. Um, so it looks like I'm going to be doing some error prompts, unfortunately. (t: 1530) Oh, okay. So we do have this render error. Ooh, I hope it's a render error. Yeah. Um, and since we are doing this snake style, remember I am, I'm going next, (t: 1540) even though Michelle went next, um, I am going to go head and enter my second (t: 1550) prompt here. Let's see. So copy and dismiss. Okay. So I got an error on the, uh, when I started recording my voice, it was, uh, I (t: 1560) recorded my voice. I recorded myself for a minute speaking and I pressed submit and then it was stuck permanently loading. And I don't know what the issue is beyond this. (t: 1570) Uh, when I swipe down to dismiss the meeting in the permanent loading state, it showed a render error and that error is listed below. (t: 1580) So I just type this in, or I use the voice on this. And then here, what I'm going to do is I am going to paste in that render error. (t: 1590) Okay. And I actually. I'm going to finish my second prompt and we're actually even you're about to start your third prompt. So we're neck and neck. I feel like you might be a little bit ahead in features if yours works. (t: 1600) Um, but let's go ahead and give this a good old refresh. So no meetings yet. Um, I'm going to go ahead and start my recording. (t: 1610) I'm gonna put, uh, Riley and Vishal and let's see if this works. I'm testing this out. This is a recording between Riley and Vishal. Riley is better than Vishal. (t: 1620) But. Every way. Vishal's app will win in every single way. Except for the ways that are important, which are all of them. (t: 1630) Oh, let's go. Let's go. No way. Look at that. Look at that. And summary transcript. We have this clean toggle. (t: 1640) How does the transcript look? Transcript. I'm testing this out. This is an, I mean, this is using whisper, so this is going to be better than yours. At least so far. I mean, you're using Apple. (t: 1650) You're using the native Apple one. Look at that though. Come on. That's clean. Okay. I'll, I'll give you that, but. Okay. Wow. And it's 714 and it has the time. (t: 1660) So I don't think it's going to be too difficult to integrate with, um, calendar calendar. Okay. I mean, let's see what's harder though. We each have, we each have three. I'll switch the camera back to you. You have the floor. (t: 1670) Let's see. Okay. So this is definitely concerning, um, that Riley's app with vibe code is like pretty much almost there and we still have to add folders. (t: 1680) But he still needs to add calendar. Oh yeah. I forgot. I have folders. You don't yet. Yeah. I didn't even look at that yet. And we've each used two prompts. So let's, uh, so the error that we were getting earlier, the, um, I believe it was failed to generate summary from API. (t: 1690) I did a quick Google search and it turns out I had $0 left on my open API. (t: 1700) So I had to go buy some tokens, bought $50 a token. So hopefully we don't run out. (t: 1710) Okay. So now, um, and then we'll give it another try. Amazing. So podcast with Riley. Oh man. It seems like this one. See, like there's some extra functionality here that we didn't even predict, which is, uh, once you record one, you can't record again for, for one of the meetings. (t: 1720) Cause it, it won't let me. (t: 1730) That's another thing you need to fix is you need to be able to, uh, to just add an event in your app too. It should work both ways. If you add a calendar event, it syncs to the app. Yeah. (t: 1740) If you add a calendar event to your record, it generates a calendar event for that. Mm. Mm-hmm that would be cool. Yes. Here. You can see, we added it, came back refreshed and let's, let's give it a try. (t: 1750) I do like this a lot though. Test, test. I have the better animation, right? I would say you do actually. Let's pause it. (t: 1760) Pausing is actually low key fire. Yeah. Pausing is great. Yep. Now we've resumed it and now we've ended it. I wonder if that could, that could cause some errors. Instant transcription. Right. (t: 1770) And the summary. Animation, one participant claiming to have the superior. Yep. Yep. Yep. It paused and resumed. So it knew we were pausing and resuming. Okay. So yours is working. (t: 1780) Do you have a double bottom sheet as well? You do. Yeah. Yeah. So we both, we both have this double, like a scroll, push that down, like both double (t: 1790) bottom sheets. Kind of weird. So you kind of got to do both. That's a bad design, right? Like it is. So it is your turn. Turn. It is my turn. (t: 1800) It's your turn for prompt number three. So we have three more prompts. Yeah. So I think there's, you know, one thing we definitely need is folders. So let's see if we can get that in this prompt (t: 1810) just to get the base criteria out of the way. And maybe we can nail in the functionality. Maybe we'll do folders and UI. We'll have this prompt focused on reorganization (t: 1820) and UI enhancements. So one thing I wanna do is, I don't like how you have to click transcription and summary back and forth. (t: 1830) I don't like that. So I think both of them should be on this page. Like mine. Yeah, like Riley's. Yeah. And I want a plus button to start another recording. (t: 1840) And I want all the, you know, one thing we'll steal from Riley's is when you do a recording, (t: 1850) I want it to be in this like main folder, and then you should be able to add folders and then select which ones go in the folder. So let's see. Let's see if Cloud Code can nail this. (t: 1860) So say, okay, great. The build is working and I am able to run the app. We have made some key changes to get the app (t: 1870) to successfully run. So please read through the code base again, just to re-familiarize yourself. (t: 1880) Just wanna make sure Cloud Code doesn't edit that like info P list error we were getting earlier. And now let's get into the final step. The functionality. Let's focus on making some UI enhancements now (t: 1890) and expanding functionality through padding folders. I want to be able to create folders (t: 1900) for the different recordings, I guess on a recording level or on an event level. What's better? Maybe event? (t: 1910) I think it's an event. Yeah. Event. Because one thing I wanna do in mine is I want multiple recordings within an event. In an event? that makes sense and you organize the events in the folder right in real life you might have (t: 1920) multiple yeah yeah you put the you can think of it as meetings so meetings might have multiple recordings yeah the meetings are in folders yeah yes the meetings aren't that makes sense yeah (t: 1930) work meeting but you can have as many recordings as you want in a given meeting exactly yeah so i want to be able to create folders for the different events i also want to be able to (t: 1940) create more than one recording summary within a given event for the recording let's just show the (t: 1950) transcription and the summary on the detail view without having to open up a new sheet each time (t: 1960) please focus on not breaking existing functionality as you make these changes but rather expanding (t: 1970) to the current format and then you can add a new one to your list so i want to be able to create more than one recording summary within a given event for the recording let's just show the transcription include these additional features if you need to refactor components to do so please do it yeah so (t: 1980) i think i'm pretty happy with this i'll give it one more instruction which is if you're making ui (t: 1990) related changes focus on good design principles and simple and intuitive ui so that's my prompt there we go okay so you're entered your prompt (t: 2000) so you're entered your prompt i'm trying to do a lot here but you are trying to do a lot but it also did a lot the first time it did a lot the first time calendar it you know if this were (t: 2010) to fail where do you think it would fail it's a good question i think it might get confused when we're talking about having right now we just have events and we have one recording it's right one (t: 2020) event one recording now we're doing event folder multiple events in a folder and now it's not event recording it's multiple recordings in an event right right and i think it might get confused (t: 2030) as it's doing that i hope not this might take a while why don't i just go for my next prompt while this is loading so yours is loading so you just did prompt number three i am about to do three so (t: 2040) we both kind of like just to kind of like do like a summary so my first prompt i went through these four you did the other four um you also had to fix an error on your third and now you're trying to (t: 2050) get you're trying to get folders while on this one i'm trying to get calendar oh god yeah (t: 2060) uh calendar access okay so let's go ahead and give this prompt here okay i want you to allow (t: 2070) this app to integrate with my calendar this means that i should see all of my events in the calendar in my app so all of them and then i should also be able to create if i just record like i am now (t: 2080) it should add it to the calendar at the current time and by default make it 30 minutes for (t: 2090) simplicity reasons time whatever time that it gets recorded at for simplicity's sake i also want to be able to click on events on my in the app like i should see the calendar events in my app that are coming (t: 2100) up soon and i want to be able to click on those events and then immediately create a transcription (t: 2110) or i want to be able to record the meeting notes for that event that is what i want to do in this way let's just go ahead and link the javascript folder to click on it and we'll log in to it (t: 2120) I want to do in order to do this please look up the expo calendar documentation to implement this and I please do this in a simple manner and make sure that you get everything that I just said (t: 2130) and nail that core functionality so you sent the prompt I recorded my voice I'm sending it in (t: 2140) sticking with opus 4.1 sticking with opus 4.1 you think I'll run into trouble with that prompt um maybe I think maybe I think I think the transcription part maybe was like a little (t: 2150) confusing maybe maybe it's opus it's opus but I think it was nice that you gave oh you've been recording for two and a half minutes I have a little surprise test let's see how I like it I (t: 2160) you know a real life use case so yeah a longer one this is good let's see if it works and then you can it'll summarize my prompt yeah that's the goal at least oh I should have told it to switch (t: 2170) to open AI whisper so now I've used my third I have two more you have two more prompts okay yeah you need to (t: 2180) paragraph this bro yeah that is ugly action items consult expo calendar under operational section (t: 2190) 4.1 wow it really has no clue what we're doing it doesn't have the context yeah you know um yeah yeah it's very interesting and if we look at the to-dos it is it's going it's updated the data (t: 2200) models it refactored the meeting to support multiple requirements recordings it is it created the folder management system and right now it is updating the (t: 2210) detail view to show the transcriptions and summaries so I think we're on the UI part of it now so you got folders oh it's still working it's still working oh what the heck yeah it's a long (t: 2220) one forever okay oh wow I did not realize it's still going opis doesn't play clearly it is (t: 2230) going off hanging out doing damn okay so we'll wait for both of ours to finish we both entered our third yeah okay we're back so it finished cloud code you know updated all (t: 2240) these to-dos new features folder organization check multiple recordings per meeting check (t: 2250) enhanced meeting detail view check and improved ui ux this was a lot of changes so yeah that was insane how long that worked for yeah that was 10 minutes that was a long time i i guess opus just (t: 2260) takes a while takes forever go ahead and run it again it succeeded succeeded all right let's see so i'm gonna be depressed if you get this data could it be read because it is missing that is (t: 2270) not good so it's asking for all the permissions again and then it added these as like placeholders with the proper ones there's work personal important plus folder oh nice no this oh my god (t: 2280) this is great so uh damn say unimportant don't you dare put me in that folder you know what's (t: 2290) going on this is pretty good so let's recording or at least changes oh my oh wow it has all those (t: 2300) recordings up top oh wow i like this let's on to something it's on it's on it's cooking something yeah let's see okay do do another do another you got this man see i i think it should dismiss (t: 2310) immediately i don't like how it stays there for a sec because i feel like i could just click (t: 2320) record again right oh this is cool though we're getting somewhere oh nice yeah this is cool very cool settings is still dead but that's fine let's see if the (t: 2330) folder worked oh no did it add to the folder add to a folder unimportant save oh no so the folder (t: 2340) functionality isn't working this is not good all right so that's prompt three i need to check on my app so let's go ahead and switch it back i can let you ponder the issue that's going on here so (t: 2350) this is my email this is just one that i copied from your list it as well let's use here okay (t: 2360) you just completed wow someone wrote i dawned i printed this a few minutes later i believe this one i finished so it was done in three being i real regretted it because i really (t: 2370) thought i got this sounds funny just because getting this and ending it here sometimes people are just like well you know whatever whatever even though i mean three days which (t: 2380) is a little bit like a full sweet celebration but anyways that's the escape option please inventory Let's see. So let's add something to my calendar. I don't use the native Apple calendar. Oh wait, I don't even (t: 2390) calendar so I can click on today and If I add an event here called test Save it in theory it would show up here, right? So this must be the area you're getting (t: 2400) Okay, so oh Yes, it's up top. You see that upcoming events. I like the upcoming events. I like it, too (t: 2410) I like it too so I can click on this. Oh and it automatically shows up as test and I can hit record (t: 2420) so one thing that I want to do very close in the future is change the Recording animation. This is absolutely (t: 2430) horrendous Absolutely horrendous, dude. I'm not gonna lie We're integrated with calendar. Oh man Look at that. I just opened it up and recorded we have the transcript (t: 2440) But can you create multiple recording? No, I will add that next but I'm liking where this app is at (t: 2450) I think there's probably just some render error. I think that's the error. I don't think it's a big it's definitely not a big error Yes is working well But I'm feeling good where I'm at after (t: 2460) three prompts Okay, so fourth prompt now, we're gonna focus on So the the folder functionality wasn't working. That's not good (t: 2470) like I can Add to folder You can click Add to folder But yeah, it's not showing any indication that it's in that folder and it's not even saying it's not even saving it (t: 2480) So it's you know, something's up something's up Everything's still in all so that's not good. The other thing is it's not a big fan of (t: 2490) This this transcription. I like how it tells me the words, but I want to use whisper so let's try and nail those two and then maybe like quick UI fixes on this like (t: 2500) I'll say I'm able to add multiple recordings per meeting slash event (t: 2510) But the folders functionality is not working. I can add folders and (t: 2520) Let's see. Can I remove folders? I can remove folders as well add folders and remove folders but I'm unable to add an (t: 2530) event slash meeting to a folder when I hit save it doesn't save that meeting to the folder and (t: 2540) When I applied the filter the meeting that is supposed to be in the folder is not there another Say I want you to investigate and fix this issue. So the folders (t: 2550) functionality works another change I want you to make is (t: 2560) to use open AI whisper for the speech to text the same API key for open AI is there so you can reference (t: 2570) That please make both of these changes Small UI fix the title gets the large title gets cut off (t: 2580) when clicking on a meeting and There is a delay after (t: 2590) the recording ends before the recording sheet Dismisses we should probably have instant dismiss and loading so this is my fourth prompt (t: 2600) focusing on two minor UI fixes whisper and fixing folders mmm only for to do's (t: 2610) For to do so this one's a much simpler But I think let's just go ahead and while yours is loading while yours is loading. I think this will succeed by the way (t: 2620) This seems like a pretty compact relatively Straightforward prompt which will allow you to go for kind of like the final punch on your last prompt exactly only have one more only one You have only one more. I have two more prompts. Okay, so I can add things to my calendar currently so I can call this (t: 2630) celebrating victory against (t: 2640) Vishal an event which won't actually happen. Okay, so now we come back here if we were to refresh it, I think it would show up (t: 2650) Okay, so we are getting this weird render error So that's thing number one there we go, right something's weird happening, right? Test isn't showing up properly in the upcoming events. You see that? Yeah, so that is a problem, but I can click on this (t: 2660) I'm going to celebrate with myself. Okay, so everything's working Except as you can see here the two things that I want to fix right now (t: 2670) Number one is the render error below when I add a new (t: 2680) calendar Event so that's number one. That's the first thing that I'm going to fix The next thing that I'm going to fix is the animation while (t: 2690) Recording is a really ugly bottom sheet instead make the recording animation much simpler and better and cleaner think deep about (t: 2700) This and make it good third, please add a (t: 2710) follow-up recording that I can have that has a summary and transcript that are additional (t: 2720) recordings beyond the original that live Inside the meetings so meetings slash events can have (t: 2730) multiple Recordings. All right, this is a huge risk. Yeah, but we're doing it ship it this might be like a This might be like a (t: 2740) This might be like a this might be like, you know, this could be the win. This could be the win or the loss or the loss. Yeah, please Think about these changes that I want you to make and make them correctly. I'm counting on you. I need you to be successful with this (t: 2750) the emotional plea here they are (t: 2760) okay, that was a good prompt all right, so that's number four you let's uh, we will go back to you for (t: 2770) number five. All issues fixed. I hate you. Opus, I love you. And let's see if it builds. Please build. (t: 2780) This is not good. Oh, no. He's got a lot of errors. A redeclaration of transcription error. Very interesting. (t: 2790) So it seems like there are two transcription errors which are getting thrown. Yeah. We have two. The whisper service has one and a regular one has one. (t: 2800) So you're saying is you didn't vibe hard enough. I didn't vibe hard enough. And one of these needs to be deleted. Right. (t: 2810) Actually. So, but we're not allowed to touch the code. You're not allowed to touch the code unless it's deleting one singular bracket. Yeah. So, okay. So now who, you were just, you were just saying like, oh, you're going to be here, spend your last call. (t: 2820) I know, I know. And now you have to do it fixing these errors. You can't even style it. This is an easy one though. This is an easy one. Yeah. I believe in you Opus. (t: 2830) Opus, you're my guy. Whether or not it's easy, it's your last one. Yeah. So are you confident in the styling? Because this isn't, that's part of the voting. People are going to be voting. You're going to be voting guys out there. Styling should play a factor. (t: 2840) And so. Okay. If I get mine, then I get to focus my last prompt just on styling. I think, man, this, this is tough, right? This is a fun game. Because I can't, if I lose on styling, I might be done. (t: 2850) But if I can't get the app to build, I'm definitely done. Yeah. So, okay. (t: 2860) Let's, let's craft this prompt. Final prompt. Last dance. Yep. Let's go. Last prompt. Okay. There is a build error. (t: 2870) Invalid redeclaration of transcription error in the file. Transcription. Transcription service. Seems as though there is a transcription error for both the old Apple speech (t: 2880) to text to text service. And the new one you made just now for open AI whisper. (t: 2890) Can we fix this issue and make all caps, make sure that this new functionality (t: 2900) will work and build successfully. Also, I'm going to go for it. I'm going for the big one. (t: 2910) You have to ship it. Go for ship. Also, I want to make UI improvements across the app in. A way that does not impede on any functionality and just focuses on design. (t: 2920) I want the app to feel more fun and colorful while maintaining the intuitiveness that we already. (t: 2940) This is a dangerous prompt. It's dangerous. I'm going for it. Not only does it need to succeed, but it needs to add the colorful elements in a way that. Doesn't make it look incredibly ugly. (t: 2950) Yeah. Yeah. Let me give you my thought process. Green background. Okay. Don't make it ugly. (t: 2960) If you feel as though your design changes might not look good or clean, please do not make them. (t: 2970) And your end goal is to ship an app that. Foremost works, but also looks and feels good to the user. (t: 2980) If the app doesn't work, you are a failure. (t: 2990) Ship it. Go. You sir are done for the day. Yeah. Back. Oh, mine's still loading. So we'll wait for both of ours to finish. (t: 3000) I still have a prompt left. Yours is going to take like 20 minutes. I said that's going to cost $10. Okay. We'll wait. Okay. It is done. And. What we're going to do is we're going to look at the app here. (t: 3010) If there's any errors, so be it. I'm done editing this. And then we'll compare it to yours really quickly. And then we'll let everyone decide who wins. Yeah. Cool. Let's do this. (t: 3020) So I'm going to just refresh it. It's just force a habit. I know I don't even have to. Okay. It built. It did build. And it didn't get that little render error at the beginning. But that also might be because I deleted the calendar events for testing here. (t: 3030) Yeah. Let's go ahead and add eating food. Let's see if a render error pops up. All right. So here we come back to the app. (t: 3040) Right. All we do is we refresh. We'll see eating food here at the top. No render error. Right. We can go to eating food. (t: 3050) Let's see. This is a moment of truth. I swear. Honestly, if it has the stupid animation, bro, I'm going to be pissed. I mean, it's just so simple. (t: 3060) You know, sometimes simplicity is good, though. I know. I know. It doesn't have the gradient. There's no gradient. It doesn't have any. Decibel. It has no like a reactivity to the audio, but it does look good. (t: 3070) Like this looks I would say it looks more professional than yours. I would say I'm team Vishal on that one. Oh, but I think that's a little bias. (t: 3080) OK, so here we have the meeting details. Transcript works as normal, but we can do record follow up meeting. Last time we were dealing with this error right here where it's trying to create a calendar event when I'm doing a record of follow up meeting. (t: 3090) Here we go. Moment of truth. OK, it did a little flicker thing, but I think we're in the clear testing, testing, testing banana chicken turtle. (t: 3100) And this is where the error happened last time. This is. Oh, we are good. Oh, yeah, it's money. And it shows the time for each one. (t: 3110) So 20 seconds, seven seconds. And it makes that distinction. You see how it puts the parentheses follow up? Yeah, I do see that. I'm not going to lie. (t: 3120) I'm feeling relatively confident. Oh, yeah. One other thing I want to add is a couple of nested menus there. Second. Right. We have second. So. Now that should be in the second menu. (t: 3130) So the folders work like basically perfectly low key. Oh, man. But that is five prompts with Claude Opus four point one. (t: 3140) And that's oatmeal. My app oatmeal. Let's go over to the show when we have his app called meetings. Apparently it's no it's called cereal. (t: 3150) Oh, really? Hmm. Well, I think, you know, it's because there's more functionality. Yeah. It is. Yeah. Fair enough. You know, and like really who put who puts granola in their oatmeal? (t: 3160) No, that's true. That's true. Actually, I put granola in my oatmeal. I also do. And peanut butter. Yeah. And a lot of other things. (t: 3170) Strawberries, strawberries, granola and like peanut butter. All of it. All of it. But stop diverting the attention from the fact that my app is better. So, you know, I'm going to make the case for my app. (t: 3180) I think one, I like the today view. I like how it's not just upcoming meeting at the top. It's all your meetings. For the day. Yeah. Because, you know, the business world is really fluid. (t: 3190) Things can move around. Fair enough. And, you know, it shows everything. And not only that, you also get a glimpse of the next week. You know what's coming up, et cetera, et cetera. (t: 3200) And you can kind of see what was earlier in the week, what's later in the week. Sure. You know, so that's one thing I like about mine. I think another is, you know, I think the summary and this is something, of course, (t: 3210) that like we didn't prompt the summary like we told AI to come up. We came up with a prompt for another AI to make a summary for what we are saying, which (t: 3220) is transcribed by an AI. Right, right. And we could definitely optimize the prompt. (t: 3230) There's prompt optimization across the board. Right. But again, we didn't, we can't even go into the code to edit the prompt. So yeah. Exactly. This is a cool little animation, I guess. (t: 3240) It is cool. One thing, the folder functionality doesn't work perfectly, which is annoying, but I do have the location. I put vibe code HQ on the calendar invite and it does show up here. (t: 3250) And yeah, I think, you know, you got a couple little colors there and you also have here a sick, sick recording animation, you know, not just this button, but. (t: 3260) It is more fun. It feels like this would be maybe for like solopreneurs or something. Mine seems more catered towards, you know, like a, you know, a McKinsey consultant or (t: 3270) something. Exactly. Like this gradient is maybe too distracting for like, you know. Yeah. I mean, I think it's, it's a little bit more of a, you know, a presentation or something, (t: 3280) but I do, I mean, Loki, I think mine might have an edge. So, okay. So that's it. That is, that's both of our apps. I'm very curious for those of you watching whose app is better. (t: 3290) And we decided if you vote down below on whose app is better as long as you're subscribed to the channel and you also like this video you can get, you can potentially win $1,000 (t: 3300) worth of credits for the vibe code app. If you vote below, I want to hear your thoughts. Vishal and I are going to be responding to as many of these comments as we can. (t: 3310) I'm very curious to see who, who you think won. This was a fun, really fun match. We're going to be doing these a lot, this format, and we might even get a third person (t: 3320) in here and do like a three-way battle. I think it could be even more immersive and maybe we'll just do speed round. We won't do limited by prompt. (t: 3330) Maybe you'll just have 45 minutes to cook and maybe I'll do a live announcement between like you and someone else at the company. I think that'd be really fun. Yeah, no, I think that'd be great. (t: 3340) I really liked it. This was a good match. This was a good match. Good match. We'll see you guys here for the next video. Let's go team Vishal. .

