---
title: The Power of Tiny Experiments
artist: theblurb
date: 2025-06-25
url: https://www.youtube.com/watch?v=3IQLyOQNo0I
---

- [00:00:00](https://www.youtube.com/watch?v=3IQLyOQNo0I&t=0) - If you've consumed enough of the self-development Kool-Aid, you've probably come across the advice of setting goals, creating a vision, and building long-term habits. In fact, I've made a few videos on it myself. But what if there was a better way to approach your goals? This is where tiny experiments come in. Rather than making a year-long commitment where the chances of failure are pretty high, why not approach your goals like a scientist? Come up with a hypothesis, run a small experiment, collect data, and then come to a conclusion. The author of this book did something similar. She decided to run an experiment and publish one blog post every weekday for 100 days. At the end of the 100 days, she gained 6,000 email subscribers, leading her to eventually

- [00:00:30](https://www.youtube.com/watch?v=3IQLyOQNo0I&t=30) - collect data, and then come to a conclusion. The author of this book did something similar. She decided to run an experiment and publish one blog post every weekday for 100 days. At the end of the 100 days, she gained 6,000 email subscribers, leading her to eventually becoming an author and building a successful online business. That single experiment changed the trajectory of her life. So how can we do the same and run our own tiny experiments? Simply follow this formula. I will do X action for Y duration. For example, I will write for 30 minutes for the next 30 days. I will walk 20 minutes for the next 20 days. Or I will meditate for 30 minutes for the next 20 days. Or I will meditate for 30 minutes for the next 20 days. Or I will meditate for 30 minutes for the next 50 days. Your experiment can be as long or as short as you wish, but use intentional constraints to make the experiment more specific and trackable.

- [00:01:00](https://www.youtube.com/watch?v=3IQLyOQNo0I&t=60) - 30 minutes for the next 30 days. I will walk 20 minutes for the next 20 days. Or I will meditate for 30 minutes for the next 20 days. Or I will meditate for 30 minutes for the next 20 days. Or I will meditate for 30 minutes for the next 50 days. Your experiment can be as long or as short as you wish, but use intentional constraints to make the experiment more specific and trackable. This can be input-based or time-bound. For example, writing 300 words per day or writing 30 minutes a day. Above all, follow your natural curiosity. Don't pick something because you feel you have to. The idea is to simply collect data and analyze the results. Not to judge whether this will lead to success or failure, like all other conventional goal-setting frameworks. And by the end of it, you may be able to do the same thing. If you don't, you may not be able to do the same thing. You may just discover an experiment that changes the direction of your life. And speaking of tiny experiments, the sponsor of today's video, Brilliant, is worth talking about.

- [00:01:30](https://www.youtube.com/watch?v=3IQLyOQNo0I&t=90) - to success or failure, like all other conventional goal-setting frameworks. And by the end of it, you may be able to do the same thing. If you don't, you may not be able to do the same thing. You may just discover an experiment that changes the direction of your life. And speaking of tiny experiments, the sponsor of today's video, Brilliant, is worth talking about. Brilliant is an interactive learning platform that helps you build your understanding from the ground up by making you solve problems, experiment, and practice as you go along. Methods which are proven to be six times more effective than casually consuming content. With thousands of lessons across programming, data analysis, AI, and math, Brilliant is designed to help you learn faster and smarter. What I really love about Brilliant is that it's a place where you can learn from your experience. And I think that's why Brilliant is so important to me. Brilliant is a place where you can learn from your experience. And I think that's why Brilliant is so important to me. Through their sleek mobile app, I can learn on the go whenever I have some spare time, helping me stick to my experiment without needing to find additional time in the day.

- [00:02:00](https://www.youtube.com/watch?v=3IQLyOQNo0I&t=120) - help you learn faster and smarter. What I really love about Brilliant is that it's a place where you can learn from your experience. And I think that's why Brilliant is so important to me. Brilliant is a place where you can learn from your experience. And I think that's why Brilliant is so important to me. Through their sleek mobile app, I can learn on the go whenever I have some spare time, helping me stick to my experiment without needing to find additional time in the day. So if you want to bring your daily experiment to life, head over to brilliant.org slash the blurb, or scan the QR code on screen to get 30 days of Brilliant for free, plus 20% off their annual subscription. Thanks once again to Brilliant for sponsoring this video. After completing your tiny experiment, there are three paths you can take. Path 1, persist. If you're enjoying the experiment or you feel like it's adding value to your life, then extend the experiment and maintain the momentum.

- [00:02:30](https://www.youtube.com/watch?v=3IQLyOQNo0I&t=150) - After completing your tiny experiment, there are three paths you can take. Path 1, persist. If you're enjoying the experiment or you feel like it's adding value to your life, then extend the experiment and maintain the momentum. Path 2, pause. If the data reveals that the experiment didn't quite fit with your daily schedule or you simply didn't enjoy it, then pause and reassess. Path 3, pivot. If you think you need to course correct, then consider increasing or decreasing the scope of your experiment. For example, change 20 minutes to a 10-minute walk instead. If you're unsure which path to take, look for internal and external signals. Does this experiment easily integrate with the rest of my daily commitments? Did this experiment increase or drain my energy? And there's no right or wrong path, because when you view things as an experiment, there

- [00:03:00](https://www.youtube.com/watch?v=3IQLyOQNo0I&t=180) - If you're unsure which path to take, look for internal and external signals. Does this experiment easily integrate with the rest of my daily commitments? Did this experiment increase or drain my energy? And there's no right or wrong path, because when you view things as an experiment, there is no failure, as the ultimate goal was to never arrive at the binary destination of success and failure. It was simply to test the hypothesis and gather data. This is why so many of us quit too soon on our goals, because when we don't see immediate results in the first few uploads, we move on and chase the next big thing, rather than using our internal and external signals to guide our next steps. Goals will be the same. Risk and gain gain gain gain, and so on. But there's no such thing as a good hope or a good work. Goals will lead to a fixed mindset of success and failure, whereas experiments will create an open mindset of learning, adapting, and growing. So before you pick your next big goal, pause and ask yourself, can I turn this into a tiny experiment?

- [00:03:30](https://www.youtube.com/watch?v=3IQLyOQNo0I&t=210) - using our internal and external signals to guide our next steps. Goals will be the same. Risk and gain gain gain gain, and so on. But there's no such thing as a good hope or a good work. Goals will lead to a fixed mindset of success and failure, whereas experiments will create an open mindset of learning, adapting, and growing. So before you pick your next big goal, pause and ask yourself, can I turn this into a tiny experiment? Because the benefits could be far more long-lasting. And to help you with your tiny experiments, I've created a free habit tracker so you can track your progress and analyze the data.

