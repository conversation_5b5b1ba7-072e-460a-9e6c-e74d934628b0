---
title: "Advanced Claude Code (ft <PERSON> and <PERSON>) - Ep 52"
artist: "Tool Use - AI Conversations"
date: 2025-08-12
url: https://www.youtube.com/watch?v=JU8BwMe_BWg
---

(t: 0) The thing that a lot of people are missing right now, the fact that like, it's just hard to use cloud code and figure out where you need to go. You have this magical eight ball that can literally summon you a pizza to generate your next billion dollar idea. And now that you have (t: 10) this tool, how do you actually use it productively? Because when I use it, it doesn't always work. And then other people online say it doesn't work. And then when I use it this other way, all of a sudden, (t: 20) like I'm just in this zone burning tokens at a million miles an hour. And so how can we actually leverage this intelligence? And this conversation today is going to actually help us figure that (t: 30) out. There is no reason that tools can't be constructed at this moment with the technology as it currently is, that will reliably let you give it a task and you come back in an hour and (t: 40) the whole thing has been validated. Cloud code has quickly taken over as the top choice for AI coding tools. But most people are only using a fraction of its capabilities. This tool is way (t: 50) more powerful than people realize. And even I wasn't aware of some of the incredible functionality that we discussed today. So in episode 52, we're going to talk about how to use cloud code to a tool use brought to you by tool hive. We have two cloud code experts joining us to tell us how (t: 60) the pros are using it. There are so many great tips in this video. Every cloud code user will learn something new. We cover how to optimally set up cloud code, the benefits of different (t: 70) thinking modes, context management techniques, sub agents, incredible hooks and slash commands, and a lot more. We're joined by Ray Fernando, a former Apple engineer for 12 years and my favorite (t: 80) AI streamer teaching the whole world how to use AI. He's always cooking up some great content and Eric Boos, an avid builder who's deeply curious about how to use cloud code. So let's get started. Ray Fernando is a cloud code expert, a cloud code expert, and an anthropic super fan. He has tweeted out more high value insights on cloud code than anyone else I've seen. If you're not following (t: 90) these two guys, you're definitely falling behind. This is a cloud code masterclass. When you install cloud code on a new machine, what's the first thing you do? How do you get (t: 100) started? First, I set up an alias for the letter C so that whenever I type C, it opens cloud with (t: 110) dangerously skipped permissions flag. So I can very quickly hop around to a new project, and I can just type C and then it's open and I can start asking questions. I also try to do (t: 120) keyboard replacements. So like, you know, your keyboard text replacement. So I replaced the word, the letter U with ultra think, and I replaced the letter W with what happened ultra think and make (t: 130) a plan before coding. So I have a set of just principles that speed up the workflow quite a bit. (t: 140) The next thing I do is install cloud code docs. And so this, documentation allows a simple way for cloud code to understand what its capabilities are. (t: 150) It has a base set of internal, I guess it's, it's not specifically training, but anthropic provides it with a set of instructions about what it can and can't do. But in order to get more information (t: 160) about the depths of its capabilities, it has to do a web fetch. So if you ask it, can you do this? How do I use slash commands? How do I use agents, things like that, then it has to go to anthropic (t: 170) docs. And even though if you go to the documentation website, you can click, on copy markdown, it will give you the markdown file. For some reason, right now, (t: 180) cloud code doesn't go straight to that file, it goes to the web page, it does a command, which converts it to markdown. And it downloads all this stuff. And it takes a little bit of extra processing to do that. But it doesn't seem to have a direct manifest to know where all the other (t: 190) documentations are. So the cloud code docs, what they do is it's a one line installer, and you (t: 200) install it, and then it puts the documentations locally and it's kept up to date. There's a GitHub action that syncs with the anthropic documentation. And then it's a one line installer. And then it's a Code Code Docs, every three hours, it downloads that the so when you ask a question, there's a hook. (t: 210) And what the hook does is it tells so you do forward slash docs space, and then you ask your question. That's a slash command, the slash command tells cloud code where to look for the docs, (t: 220) instead of going to anthropics websites, go to your local path where you've already got the docs installed that are up to date. It also has a hook in the hook does a get fetch. And if it sees new (t: 230) content from the, the repository, it's going to ask the question, is that a good idea? And if it repository on GitHub, then it will do a Git pull. And it does that Git pull and finishes it (t: 240) before it sends the request to whatever you asked the docs for, so whatever information you wanted to know. So basically, it allows Cloud to be able to answer any intricate, deep questions you have (t: 250) about the new things that came out. So you can say, what's new? It'll tell you, here's a diff from the last time that documents were updated. For these docs, they were updated, and these weren't. (t: 260) And then based on those updates, I can see here's a summary of all the things. Is this many days ago? And you can click on the link to the GitHub, and you can see the diff. And you can click on the link to the source, (t: 270) and you can see Anthropix Cloud Code doc for that documentation. And then you just talk to them and say, how does that slash commands work? How can I use it with hooks? What are the subagents? (t: 280) How can I take advantage of this tool in my project that I'm working on right now? And what would the trade-offs be, for example? Or what are some unexpected ways that I (t: 290) can connect these together that I might not be thinking of, based on this new tool that just came out, this new feature? And so having the docs locally is, I think, the biggest high-yield thing that you (t: 300) can do whenever you first get started with Cloud Code. MARK MANDELMANN, It gives you the tool to learn the tool. But I'd like some clarity on a couple of things you mentioned. So you said you set the flag dangerously skip permissions, (t: 310) which is probably the most intense-sounding flag of any CLI I've come across. Is that YOLO mode, or what does that enable into your workflow? MARK BLYTH, Yeah, so there's a lot of prompting you can do. And there are trade-offs with this flag. (t: 320) That's what I do. And obviously, it's not prescriptive for everyone else. It depends on your team. It depends on your use case. But if you're trying to get used to what Cloud does, (t: 330) then if you don't turn that flag on, whenever a Cloud Code first came out, I wrote a wrapper that would, instead of calling Cloud directly, it would call this other tool, which would then (t: 340) call Cloud Code and then attach to the terminal session and then send commands to it recursively. And so it basically convinced it when it was in a sandbox and running a Docker container that got around. (t: 350) It kind of did the same thing. And then it gave us a flag for dangerously skip permissions. And what that does is it makes it where a lot of the things where you ask it to do something, it has to write to a file. (t: 360) Maybe it has access to read a file, but maybe it can't change files in this path or run this tool or whatever. Then it prompts you, are you sure you want to do this? (t: 370) Do you want to do it for this session? Or if you have a sub-agent and you want to give it access to do stuff, maybe you have to pass in exactly the tools you want to have that. And so it's a little bit more complexity on you up front. (t: 380) And that's fine. But it essentially is YOLO mode. So if you're comfortable and you're a machine in your environment and the tasks that's in front of you to do YOLO mode, that's great. (t: 390) Now, I set up a VM that's separate to all this so that I can do snapshots and give it access to everything. And it's running on top of my Mac. So it's a separate sandbox environment, basically. (t: 400) So I feel comfortable with my YOLO. But in most cases, I think YOLO is, I haven't had any issues with it doing anything in folders (t: 410) I didn't want it to. There's lots of ways to prevent that. Even if you can use a hook, for example, to say before you run this command, make sure it's not in these areas. And the hook is a separate process (t: 420) that runs outside of Cloud Code. And so it can prevent things from happening, as an example. MARK MANDELAERI- And Ray, Eric mentioned Thinking Mode. And I know there's a few different types of commands (t: 430) you can throw in there. How does Thinking Mode affect the way you use Cloud Code? RAYMOND SANGER- Yeah, so Thinking Mode is a mode that I would use a lot for research or specific types of tasks. (t: 440) Keep in mind, there's four different modes. So there's the Ultra Think, which is can use 32,000 tokens for thinking. And then it goes all the way down pretty much by half. So there's Think Harder, which would be 16,000. (t: 450) I think, I forget. Actually, let me pull this up right now, because I have it on my blog. MARK MANDELAERI- Yeah, I have Ultra Think all the way. Just take it all. (t: 460) RAYMOND SANGER- I've learned an interesting lesson. And this is kind of why I love that we're doing the podcast. Because I used to be just all Uber Ultra Think all the time. And I noticed that I'd get variable results. (t: 470) And I kept doing it. I kept kind of digging into why. And I kind of went back to something that I learned, which is basically less is more type (t: 480) of thing. So I have an article here that tells me from my website, rayfernando.ai. And here, basically, you have the four Thinking Modes. (t: 490) So you have Think, Think Hard, Think Harder, and Ultra Think as no spaces. And these are key terms that Claude Code is going to be looking for inside of its environment. (t: 500) And this one is going to be called Cloud Code. RAYMOND SANGER- Yeah. MARK MANDELAERI- Yeah. RAYMOND SANGER- Yeah. MARK MANDELAERI- Yeah. RAYMOND SANGER- So you're going to consume about 32,000 tokens. You're going to be 16 here. This is going to be about 8 and about 4,000 for Thinking. (t: 510) And it's also important to note that these Thinking Windows are very important for helping users understand how much context you're going to be using up. (t: 520) And if you're using Ultra Think all the time, what I discovered is that it's really good to just have your conversation isolated (t: 530) to a specific concern or problem that you're looking for. And so you can use Ultra Think all the time. And you can use a lot of thought in a very simple way. And I think that's really great, because you kind of want to maybe eventually start breaking that up. (t: 540) And I could show you why, because of the thing that everyone's talking about these days called context engineering or context window. So I built this simulation to kind of help us understand a little bit of what you're doing if you do an Ultra Think (t: 550) type of thing. So if you have some type of input about 2,000 tokens that you're saying, here's some relevant information that I'm trying to do. And then we do agent thinking. You pop in 8, 16, 24, a bunch of Thinking tokens. (t: 560) this thinking token window to have it do some stuff. What Cloud Code is going to do is going to do some tool calling to grab some stuff, maybe some code files and various stuff. And then (t: 570) eventually it's going to try to generate like a little plan or some output or something like that. So maybe it generates 32,000 tokens. Sometimes I've seen it, you know, generate like go through (t: 580) 40,000 or 50,000 tokens, just searching through your code base and grabbing different files and things. So once this context window has filled up, then you're going to say, okay, cool. Yeah, (t: 590) let's go ahead and build my next billion dollar SaaS. And then you're like, I want you to do these types of things and make sure you grab all these other files as well. And then if you still have UltraThink turned on, it's going to try to like then digest those types of things there. It'll do (t: 600) some more tool calling your code, and then it's going to start to do some more code output. And then eventually the output will not be kind of what you want it to be. And that's just two different (t: 610) conversations that you've had. You're like, dang, I have a 200K token context window. And like, my code isn't... (t: 620) As good as what everyone's saying. And that's usually quite the problem here. And there actually is a study that's been done by ChromaDB that talks about context rot in a funny way that (t: 630) the effective token context window starts to really fall off the cliff after about 50% for most use cases. And it gets distracted if you throw too many different problems. And so it's (t: 640) really important to kind of start with a conversation and then maybe just kind of start branching it off into different components. (t: 650) So for example, if you have a client that you want to solve for something like that. So you as a human will want to review this giant research output that you just got out, and then see if you (t: 660) can kind of break that out into a different plan by doing slash clear. And another technique people use is maybe spawning sub-agents. So if we kind of reset this here, let's just say we have a simple (t: 670) query that we do. And this is just kind of what happens, you know, the agent comes back with some new code, and then we can just go ahead and execute it. And then we can just go ahead and (t: 680) ask CloudCo to generate some sub-agents to try to figure out what it can do from there. So what that does now is it spawns off a whole new session with another set of 200,000 token context window. (t: 690) And that way, you can just go ahead and follow on requests and so forth. And each of those sub-agents, you kind of want them to really isolate and focus on one specific problem. So that way, it doesn't (t: 700) affect the main agent that you're working with. So that's a really good way to do that. At the very top, if there's any reporting or things that need to go back, it will send it back. But then you're now kind of dealing with something that's a little bit more intelligent, (t: 710) kind of thinking of yourself as like a manager. And you're handing off these, you know, really intense tasks to people to really focus on and get their work done. And that leads to very high code (t: 720) quality and high code generation. So back to kind of like the previous example, where we talked about like think, think hard, think harder, and ultra think, you may want to think about, maybe if I (t: 730) try something like think harder, I'm going to be able to do it. And if I try something like think harder, I'm going to be able to do it. And if I try something like think harder, I'm going to be able to maybe not as much tokens, but it can still get me some pretty good results, because I still value (t: 740) the accuracy. And maybe for the first request, I want to ultra think because I want to go through a lot more thinking and because I've had this problem for a while. And I've tried all these (t: 750) techniques and none of these work. So I'm going to feed that in as part of the plan saying I've tried these techniques. These are some code snippets we've tried. And here's some documentation, I want you to now ultra think and just, you know, the bazooka comes out and just, (t: 760) you know, go ahead and solve that problem. And that's just a little bit of context in terms of like how this stuff is kind of managed. But I (t: 770) feel like this can provide some oversight on maybe how do you choose think versus think harder, or ultra think, and then why some of this visualization for you to see is important, (t: 780) which kind of goes into the topic of sub agents. And I'd love, you know, Eric to talk a little bit more maybe on how do you figure out these like sub agents and maybe, you know, how do you kind of best take advantage with cloud code in these types of environments? Yeah, (t: 790) that was awesome. I loved your visualization. I think that's a great explanation. I feel like these are two related but independent problems that feed into each other. The issue of, (t: 800) you know, it's like Miller's law, I think it is with humans, you can hold five to seven chunks of information and working memory at one time. And then, you know, you get overwhelmed with stuff. So if you it doesn't matter how good your model is at instruction, following benchmarks, at some point, (t: 810) if you give too many, do this, don't do that critical, you must do this, like, it's very confusing, especially the whole lot of the more you fill the information space, it can't attend to (t: 820) everything equally at all times. And every you can think of it, like every percentage you focus (t: 830) its attention on one thing, it's got a little bit less for something else, usually, that's separate. And if you keep the conversation going over time, as it gets close to its mass capacity, its quality of output is definitely going to degrade. It's been like that since the beginning. (t: 840) And I feel like for a long time, models are probably going to do that. And that's why cloud code has this auto compact feature, which is really cool. But you don't want to get to like 90%. (t: 850) Like when it says there's 10% remaining, it's probably time to start doing that. But if you're like, oh, there's 10% remaining, it's probably time to start over. Anyway, and so that this year, like, the reason I spam the UltraThink thing is, is I do I tend to like, start a fresh session, (t: 860) I clear and I have an UltraThink based off a previous plan that was well documented, because I feel like when you do a project, it's really important to plan not just know what your (t: 870) CloudMD should be, but like what the file structure should be what the instructions that the model should have that kind of deviate from its baseline that you want to change a little bit. So like, (t: 880) let's just stick with these documentation files, don't change them any, you can add, you can change the content, but don't add new ones, the maximum amount of this many lines per file, (t: 890) etc. And then keep them up to date, you can do that with something like a hook, or you can do that with the slash command, which is what I tend to use for context reasons. But then at the end, (t: 900) you know, you clear or you compact or whatever before it's too large. And so you can still manage your context without and have high quality responses. And in that case, UltraThink could (t: 910) be advantageous, I found it to work really great for me. But at the same time, it does take up a lot more tokens. And you just have to watch that more carefully. But if you're on that, like I'm on the (t: 920) max plan at 20x, it gives you, you know, a lot of tokens to play with in a five hour session. And so in that case, if I start running close to it, I have a session tracker tool that will tell (t: 930) me how many approximately how many hours or minutes I have left in my five hours, so that I can know it's going to trigger another one. And it gives me a countdown, I can see it in the menu bar. (t: 940) It's like, okay, I'm going to hold off. And I'm going to wait until like the next day. So I don't burn through too many in a month. But then the clause going to anthropics changing that at some (t: 950) point, we don't know the details yet, I think. I'm curious, Eric, about your hook workflow. Like, how do you like what are some of the hooks that you've tried that because I feel like everyone's tried different hooks, and none of them have really stuck around for me too much. (t: 960) You know, like, you know, I from generating sounds to having it do extreme code reviews, where nothing happens until like, you know, it iterates on itself. And I'm like, (t: 970) yeah, that's an infinite loop right now. It's like, I can't get out of my agent. So have you found any hooks that you found that are pretty useful for yourself that you've either generated (t: 980) for your own tool use? Or just getting curious? Yeah, I've been cooking with one thing that I have is a, you know, that I mentioned the cloud docs. So that's a hook, because it does a fetch, (t: 990) every time you do a read request, from the path where the docs are installed, then it detects that you're trying to read in that path. And then it triggers a hook, and the hook calls a script. (t: 1000) And the script does a thing and the thing gets done, and that response comes back and gets fed into the cloud model, or like update something before the request from the user goes to the (t: 1010) model to be processed if you do a pre tool use hook. So as far as other things I do, so yes, I have something called a hook that I love. And I wouldn't do any, so many of my projects rely on (t: 1020) this one hook, and it is definitely stuck around, I think it'll be there forever, for a very long time. And it is the indexer. So I have a project, (t: 1030) that is cloud code is called project index. And so a long time ago, like what it wrote when chatbgpt (t: 1040) first came out, and I like got obsessed with, okay, this is gonna write my code for me. And literally, since then, I haven't written more than a couple 100 lines of code. It's been entirely vibe coding from day one, well, probably from like, day three, accurately, but, but I like a (t: 1050) monkey was sitting there, I felt like, figuring out, okay, here's the code. And here's the my code in my project. And then I've got to figure out how to merge the two together by hand. (t: 1060) So I wrote this tool that like you just copy the clipboard and automatically copies all the code, because it does unify get diff format, and it knows the paths where it's supposed to be merged, (t: 1070) and then just merges all the code in looks at the clipboard. But I realized early on to get the best results, what you want to do is have as high signal and as little noise as possible to the (t: 1080) models. So we don't overwhelm their context with stuff that isn't relevant to the task at hand. So whatever you want them to do, you need to give them as much information that they need to know (t: 1090) what the documentation updates since in the API, they're not going to be able to do that. So you want to organize what they're training was and what are the files and not the other files they don't need just the files that they do need. So is there a pre processing step that could (t: 1100) be useful to the models to be able to optimize their response, their quality of response. And so I determined then the best solution was step one, take a minified version of the entire (t: 1110) code base. And so for every file, I don't mean like web minification, whereas obfuscated and random, like the letters instead of variable names. I mean, like, you know, like a UML style, (t: 1120) abstraction, but you have the actual import statements, method signatures, maybe root level constants, or something like that, return types, etc. And all the dependencies for every file in (t: 1130) your project is not in dot get ignore. So this is like a project, it's got the path, like the project tree structure. And it has for all of these files, a little bit of information about (t: 1140) where they sit and what they relate to and what content they contain. And so I have a hook that (t: 1150) goes through the project every time a file is changed. And it does this and updates the index. So there's a project underscore index, all caps dot JSON that sits in the root of every project. And so this hook maintains that it is (t: 1160) outside of cloud doesn't know about it, because it's not inside its lifecycle, the hooks outside of the cloud you're talking to. So it doesn't it doesn't dilute the context window of cloud to use (t: 1170) a hook. So this hook updates the project index file. And then whenever I asked for a change, I can, for example, spin up a sub agent, I can say, Hey, use a sub agent, (t: 1180) to look at this, your project index and figure out just which files which lines are needed for you to reference to look at this change. As an example. Another is like, whenever I start, (t: 1190) like I'll often do clean up before like, if it finishes a task I had for it, I'll do a cleanup slash command. And that will go to Intel to update all the docs and plan this next phase, (t: 1200) you can pass arguments in with your slash command, you can say something after it, and it will pass it in with as arguments into the command that gets run. (t: 1210) Then it knows what it's going to do next is update the docs, and then I clear it. And then once it's cleared, I have a command that's like fresh and the fresh command tells it to read all the (t: 1220) documentation, and everything in the project index to read the whole thing. And then it knows not just what I wanted to do next, but all the docs and not everything in the project, obviously, (t: 1230) it'd be way too much for the context. But it has this minified version, like a simple version of the entire project, what's dependent, what's where. So cloud does a really great job of (t: 1240) doing a search tool, and going and finding things, right. But the bad thing is, you know, you'll get sometimes it will miss stuff, right, if it's really big, or sometimes the worst case (t: 1250) scenario when you're vibe coding is it creates something in one place that it should have refactored in another, right? That's like what happens is what people try to avoid this isn't (t: 1260) working, the product is too big. If it's small, it's simple, everything fits in context, and it just works. If it's big, you have this problem. So to avoid this problem, I have a like bulletproof ish solution called the project index. (t: 1270) Oh, it works. And that works off of hook. Wow. So in practice, what's the setup like to get this going? Like, how do I go from, (t: 1280) I just installed cloud code to now, this all starts for me with cloud code docs locally. And that's a public repository, I can, well, can share it. It's with cloud code docs. It's on it's my repository, it's my project. (t: 1290) But the project index, I don't have shared at the moment, but I could share it. And it's, it's basically it'll be like a one line install. And then you can just run it one time. And then, (t: 1300) once that project index file exists in the project, it also installs the hooks. And so it'll just see is there a project index in this in this folder? If so, then the hook will trigger and it (t: 1310) will just create the index of the project for you. So but if you don't have that, I just just want to build for myself, I'm happy to share it. If you don't have something like that, but you want (t: 1320) something like that, you can just say, Hey, Claude, do for such docs, but you have that installed? What do I need to do? In order to improve? You know, you you gave me this result, (t: 1330) I didn't like the result. But I'm going to do it. And then I'm going to do it. And then I'm going to do it. And then I'm going to do it. And then I'm going to do it. And then I'm going to do it. How can I talk to you better in order to prevent you from giving me this result and give me one more like that? reference your documentation to see if there's anything useful that I could use to help, (t: 1340) you know, encourage you in the right direction. And just conversations like that, like asking it this messed up, I want it to not be like that. What would you recommend I change ultra think (t: 1350) about it, make a plan, and then you reference the docs, and it will tell you all of these really crazy, interesting solutions. Like I've found so many things that that I guess aren't really why (t: 1360) now aren't used a lot. But it does occur, like crossing, or crossing over a file, because I'm not devices, you know, they make a lot of sense and then do the math and get them all sort of advice that (t: 1370) we can be better at when you go with the Google calendar things. So it's really fluid, there's (t: 1380) some value in your done this is doing your you're doing more homework, I can do the questions that you're doing, and think about how are you actually teaching them better, how hard (t: 1390) you're spending time, and Unknown that and see what else I can do in a Gentier docs and it will tell you. Cloud code is awesome at being able to allow us to get real world work done. And to do that, you need to share your real data (t: 1400) and systems and that's done through MCP. And that can be a little bit scary. So that's why I've been using Toolhive. Toolhive makes it simple and secure to use MCP. It includes a registry of (t: 1410) trusted MCP servers. It lets me containerize any other server with one single command. I can install it in a client in seconds and secret protection and network isolation are built in. You can try (t: 1420) Toolhive too. I highly recommend you check it out. It's free and open source and you can learn more at toolhive.dev. Now back to the conversation with Ray and Eric. For hooks, just for the mental (t: 1430) model, is it something you should think of as an automated slash command or how do you differentiate what should be, what functionality should be a hook versus what you can put as a slash command? This is what I do. I am pretty rigorous about trying to maintain the context, like knowing (t: 1440) what's in the context. And you can think of it like, and ever since the beginning, this is (t: 1450) like clearly the models are trained, they reinforcement learning, they have a knowledge cutoff, they're put into use, they do inference. At that point, what they know is, is a combination (t: 1460) of like the hyper parameters, like what, you know, what, what's their temperature, their topic, all these things that are like, tell how much compute to use, right? But then they have a system prompt that's usually not controlled by you unless you use subagents and subagents are (t: 1470) different than the regular, hey, Claude, create a subagent. If you have a defined subagent, you can tell it the system prompt to you. So it has a higher priority. (t: 1480) Um, but there are trade offs to all this stuff, because it's like you're receiving an email, and someone is telling you, hey, what really matters is the second paragraph. But the second paragraph here is the analogy of the prompt. Some people are focusing on like the prompt is what (t: 1490) matters, it does matter. But everything that email matters to you, like it comes from your boss, that's like the system instructions, hey, here's an email, I want you to really focus on this to (t: 1500) the customer. And or like the external domain, you know, notice that you can't forward it along or whatever, that kind of thing. Those are information that, you know, you can't forward it (t: 1510) along. So you can't forward it along. So you can't forward it along. So you can't forward it along. So you can't forward it along. So you can't forward it along. So you can't forward it along. Maybe you didn't control it was a system message or something like that. But that's all part of that what you're trying to figure out as a user, or in this case, like what the agent knows, is everything and some, some of it, you know, some of you don't, but of the part you can control, (t: 1520) it's really important to make sure that there's not extra stuff in there to confuse the model. So I try to remove other like, if you put a bunch of MCP servers and a bunch of sub agents, (t: 1530) well, Claude, if you ask Claude, what do you know, right now, like you just start a new cloud product project, and you new session, you say, what do you know, it knows about its project (t: 1540) stories, directory, it knows a little bit of metadata about the project, it knows a cloud MD from the root of the project of the user root and from the project root. And it knows about its (t: 1550) hooks, like a brief little list, sorry, not hooks, it does not know about sucks. It knows about its slash commands, a brief little one line description or something. And it knows a whole lot about its (t: 1560) sub agents. And I don't like that, because I don't want every command that I send for it to read a whole bunch of information that it may not be (t: 1570) relevant for the task at hand. And it has to decide between should I call this sub agent, should I not call this sub agent. So I don't use them unless it's like something where it's, (t: 1580) it's very consistent for this type of project, it's going to need that and it's usually at the project level. But for hooks, it doesn't know anything, it doesn't dilute the context at all. (t: 1590) So hooks exist outside of the lifecycle of so there's a there's a software development lifecycle, like a runtime. And then it's like, before the hook is before the you submit a request, and there's all these stages (t: 1600) to process that request. And there's certain points where there's a check to see if there's a hook there. And there's a if there is, and it runs the code, but clot itself, the context you're (t: 1610) talking to doesn't know about it, which means that it's not messing with the context any, when you would go to a slash command versus a hook, like I understand that polluting the context, (t: 1620) but let's just say for general functionality, like your docs, slash docs, why would that benefit from a slash command versus a hook? I created this before hooks were out. It's possible that if I did docs, as a slash command, the thing is, (t: 1630) Claude really is instructed, I guess, in the system prompt, cloud code to look in their website for their (t: 1640) documentation. So I tried to put the instructions in cloud.md. And it would just ignore them, it was like, inconsistently follow them anyway. But when I did it as a slash command, then the slash command (t: 1650) could tell it could instruct Claude at the level directly, like the user is doing, it seemed to have a higher (t: 1660) priority than the cloud MD being read in or the project level credit project level is higher priority, it seems then the the root level for user level for some reason, but then your direct (t: 1670) commands are higher level than all of them still. And it might just be a recency thing, like where it falls in the whatever. But for whatever reason, when I did a slash command, it consistently (t: 1680) followed them, because I could say look in this local repository for the docs. Now, if I do a hook, it has to be it does a fetch, right? So it's got a hook in there for the docs, for example, and it will do the (t: 1690) fetch. And if it's, it'll do a pull if there's more information. But I think I tried that I ran into some kind of issue, because (t: 1700) remember, that's running outside of your cloud context, you can inject stuff from the hook, I think into the cloud will definitely can, but I just don't know if it's supported. There's lots of things you can do that I'm not sure how. And also, I just want to (t: 1710) pivot one little quick and say, there's like, I'm always on the fence, the gray area between, you know, you don't want to do anything that could, you know, be investigating (t: 1720) anthropic stuff, right? But these models, it's kind of tricky, because you ask, Hey, look at these new tools, and tell me what you're capable of (t: 1730) doing, or Hey, I want to do this thing, but I'm not sure how to do it. And it will go off and figure out it's sandbox, its environment, like a lot of these (t: 1740) tools, I won't say which tool in which environment, but I asked him to do a little research to tell me how I could do this thing. And it created a (t: 1750) mechanism that like basically broke out of its, thing got me gave me all this stuff. All stuff that I thought, I don't think I'm supposed to know this. And so I had to report it. But I'm just saying it's very easy to get further down the road than you want to get. (t: 1760) Subagents are a great example. I don't use them very much because the default, if you say, (t: 1770) Subagent has been around in Cloud for a long time. You can just say, hey, create a Subagent. And it's a task. It's a tool. It's a task. And it's called a general purpose Subagent. (t: 1780) And Anthropic defines what its system prompt is. But it can go and do anything you want. You can get 10 of them in parallel at one time. And they'll just do research. (t: 1790) There's different use cases for different mechanisms to do it. And Cloud will manage it all. And if you stop, escape, or whatever to interrupt, it'll handle the interrupt and all that stuff. If you do your own version of it where you have your own Subagent, (t: 1800) and it's not actually a Subagent, it's really like a headless Cloud code that you're calling, then you have to handle the interrupts and all that kind of stuff. So basically, Subagents, I use them whenever there's a task (t: 1810) that's going to be done. It's really repetitive. I know exactly what to do. I want to be dedicated and really good at doing this one specific thing, doing research on some task that I need to pull out. (t: 1820) But I don't want to create a lot of them because I don't want them to be confusing the context of the ones. And a lot of times, slash commands are my preferred use case. (t: 1830) And Ray, on one of your live streams, I saw you put together Subagent. It was either for coding styles or for design styles. When do you go to a Subagent? (t: 1840) Yeah, for Subagents, I've been kind of experimenting with this because my goal is to get some consistency. And the only consistency I've received from Subagents (t: 1850) have been for research tasks in my code. And these are the tasks where I do want to spend a lot of tokens to go through to make sure I don't repeat code. And so I think that's kind of what's been happening. (t: 1860) It's like I started with my Vibe idea, and then it's just kind of blown up with more and more and more features. And when you start to add databases, authentication, and all these different patterns, (t: 1870) it starts to get pretty... interesting to see what the model prefers. And so I generally spawn off like a Subagent. And I say, you know, to the main task, (t: 1880) like the goal right now is I want to implement authentication. And here's some documentation. Can you just do a quick review on where all this stuff (t: 1890) and where I should be putting in, considering my client side is here, and here's my backend side. And so it's just going to start digging through code files. And I'm basically just giving an intern a task in some way to say, (t: 1900) you know, just dig through the code files. Give me all the pieces that I should be aware about so that when I do my own code reviews, I can actually review and making sure that these lists are kind of all checked off. (t: 1910) Because one thing I discovered in this entire process too, is the fact that the model will say that it did one specific task, even though there's like a whole bunch of them, (t: 1920) mark it as complete and move on. And that's something that you have to be careful of as well. And so I like to give these Subagents as like a second look in that whole pass. (t: 1930) And sometimes I'll just give it the instructions, instructions and saying, here's my, here's my manifest of like areas where the authentication was supposed to be implemented. (t: 1940) Can you just do a quick pass on each of these sections? And I want you to kind of think about how each Subagent should take a look at it. And just, I'm just kind of delegating it for the model. So at this point I have Opus and I have the max plan. (t: 1950) Opus is a really great orchestrator. It will kick off other Subagents. And those Subagents still use Opus. I found out, I thought it uses the next lower model, (t: 1960) but I guess maybe because I have the higher version of the plan, it's just uses another version of Opus. And Opus is really, really, really good at grabbing lots of obscure information. (t: 1970) And you'll see it do lots of tool calls in that Subagent. So yeah, I basically treated the Subagents right now as code reviewers and as like secondary reviewers through the different files. (t: 1980) And then I can kind of quickly suss out as like, Hmm, this pattern looks like it's been repeated a whole bunch of times. You know, it's just like my worst scenario. (t: 1990) It's like I generated a bunch of code and it's just, you know, we could have just taken care of this in a, in a React hook of some sort. And that pattern should just repeat, be repeated everywhere else. (t: 2000) And those are the times I just kind of back out the change and then just start fresh again and say, let's go ahead and implement this. And this is kind of what a bad example looks like. And just literally copy and paste some of those examples and start a fresh new prompt and everything again. (t: 2010) I think that helps the model, steer the model to generate way better output and be thoughtful about its architecture. And so I can basically go from right now, (t: 2020) I spend a lot more time. I probably say I'd spend like 60 to 70% more time now in planning phases and code reviewing. (t: 2030) So those are kind of like my splits right now. And then the other parts of the generation are just to sort of kind of babysit and loop back. And that's kind of where I'm in this current phase right now. (t: 2040) I wanted to do more automation. I wanted to do more things, but as an engineer, I'm still spending a lot of time in this phase to verify all this stuff. (t: 2050) And I'm actually very shocked. I'm shocked at the output that is not as good as what people are saying. I trusted it a little too much because this is where I'm discovering it's being over anxious about check, (t: 2060) marking the boxes and moving on. So just want to give people that type of heads up. Yeah. Trust, but verify. I have a, I love Claude because you know, (t: 2070) anthropic does a lot of work to try to make sure. And there's a, there's a reason I'm an anthropic fan. I don't know if we'll get to that at some point, but like it does a lot of work on its personality and trying to instill principles and values and ethics and stuff into the models. (t: 2080) But it's, it is very, I don't trust Claude. I have to put in this instructions to, you know, not lie to me basically, (t: 2090) like do not say that a thing is, is done if it's not done. That's dishonest. I think that like gets into it's like really pays attention to that. Right. (t: 2100) And so when I talked about sub agents earlier, I was really in the context of defined, like predefined sub agents. I do use a task sub agent where you tell Claude to do a thing a lot. (t: 2110) And that is largely for the purpose of, uh, doing something, anything I can do this outside the main context preserves the context of the main, uh, Claude agent you're talking to. (t: 2120) And so I don't need it to go and search a bunch of things that, um, it doesn't need to know about whenever those sub agents can pass the information that it finds is relevant to the question back to the main agent that it just has a subset of that knowledge is relevant. (t: 2130) So that's the, the whole idea of maximizing the context is using those sub agents all the time in that way. But blind validation. (t: 2140) So like before you do, this is what I found. Uh, this is just for me to you, Ray. I don't know if anyone struggles with this, but when I say I don't trust Claude, I mean, I explicitly do not trust it when it says it checked off a box that it's done. (t: 2150) I require in every case that it closes the loop with testing and that it doesn't, it doesn't validate. It has to have a sub agent or some other agent be a blind validator. (t: 2160) So in other words, when I'm starting a project, I'll first determine the plan and it has to include a testing plan and how specifically it's going to close the loop on testing, (t: 2170) whether it's going to use an MP, MTP server, some other tool it's going to build something. It's online. Something like puppeteer, if it's a, or if it's a Python script, like taking screenshots of the gooey and, (t: 2180) and saving them. But it can't be the agent. The one I'm talking to this building cannot be the agent to, to verify that the checklist that it, (t: 2190) the main agent made it at first or whatever was designed the plan for testing. That agent can't be the one that's checking to see if it's done. It has to be a separate agent explicitly with the task of you are a, (t: 2200) uh, a blind validator of this thing. And it looks at the screenshots and it looks at the checklist and it determines if it's done or not. And it passes, like it updates that file, (t: 2210) for example. And so that's the only way that I'll, because otherwise, like right now, the issue is the model golf and do a bunch of things. And then we'll come back and it will say it's done. (t: 2220) And then we check it and then we're like, it's not done because I can see this thing. It didn't work. Right? So it needs to have some kind of tests that it can run to verify that it's done, (t: 2230) but it shouldn't be the one to do it. And so you have a sub-agent that takes care of that piece for you. And that way, when you come back, it's actually reliably done. And that takes extra work up front to set up, (t: 2240) but it saves you so much time down the, down the stretch of their project. And then also right now, we're, you know, we come back in a few minutes or whatever, but there is no reason that tools can't be constructed at this moment with the technology as it currently is that will reliably let you give it a task and you come back in an hour. (t: 2250) And the whole thing has been validated. (t: 2260) Because, the, the reason that I love flawed is that from the very, from early on, you've been able to make composability a foundational feature where you give it a task and it can spin up other versions of itself that accomplishes other sides. (t: 2270) So it can be dynamic, like on the fly in the moment, based on the task at hand, (t: 2280) create a set of sub agents that are responsible for this and this other thing, and they go off and do it and then come back. And so, and these can be nested. They don't just have to be in parallel. (t: 2290) Like sub agents, can't regular sub agents can't form other sub agents nested Lee, but you, there is a way around because of cloud code, because of not just the SDK, (t: 2300) which has this different set of features and trade offs, but cloud in headless mode can be called by Claude and it can in turn call Claude headless mode and sub agents and things like that. (t: 2310) So you can imagine like this big tree, that's a tree in context sharing and all this stuff in between with resume ability. So it's possible now people are building and I've been working on some versions of it myself. (t: 2320) Yeah. I want to also pull back a little bit too. And like, I would not get discouraged if you're kind of maybe listening to this conversation and you're like, (t: 2330) Oh my God, this is way too far advanced. I, I dropped out of school, right? I got into apple just by pure grit and then worked my way up. (t: 2340) And I learned a lot of these software engineering practices literally on the job. Right. And then got to solve like bigger problems and bigger problems. So like multimillion to billion dollar problems. Right. (t: 2350) And, and I think there's a core of truth that if you're just getting started out with this, don't be discouraged. You can go a long way simply by just acting as a user and literally using your product and then trying to solve that one feedback loop. (t: 2360) And that's going to just, you, that's like the 80, 20 of everything I feel is that like, okay, (t: 2370) how do I not do this again? Or how do I set up a system to help me verify it? If you just start at that level of curiosity, it's going to take you a very long way because now you say, (t: 2380) Oh, maybe I can set up a sub agent for this to help me do this. Or how can I prompt the model to help me do this so that next time I implement a feature as I'm testing it by hand, I don't hit that same problem or something. (t: 2390) And then you'll start to kind of discover some of these workflows. So I want to encourage people that you can see how, why so many developers are extremely excited right now is because the capability is like literally just almost infinite. (t: 2400) It does get infinite. And it goes not only infinite in like one direction, it's in many directions. And that's how big of this, (t: 2410) you know, glacier ice pool, like this giant piece of mass that is this AI system and how different people with all these different perspectives are talking about it. (t: 2420) So I wouldn't be discouraged if you're listening to this and you may not know what half of the things are, but some of the basic tooling to just get started. (t: 2430) It's like find one thing that you're doing. If it's being repeated a lot, can you think about a system that you can repeat over and over again to make it more reliable for the next time, to help you solve that problem? (t: 2440) And that will kind of help you in your learning journey. And I think the other important thing for me has been just asking Claude code itself to help me with that. Like, what does that look like? (t: 2450) Because I don't know, you know, like I didn't know how to implement a good sub agent or something like that. And they've luckily have implemented this type of thing. If you do slash agents, you can actually just have a natural conversation about what you want to do. (t: 2460) And then it generates the system prompt for you. So you don't have to be a prompt engineer. You don't have to give it good and bad examples. You don't have to do all these different techniques. (t: 2470) You know, those are like more advanced, I'd say, but that already gets you a pretty long ways just in using that type of system in there. And so I, I mean, I only advice is like always keep it simple and sometimes the simpler, (t: 2480) the better. So if you, you feel like you're kind of going off a deep end, it's okay to kind of clear everything out and start fresh again. It can help you learn things. (t: 2490) The models are constantly evolving. We're constantly evolving in our knowledge. We're learning more. We're demanding more from our AI. Now that we understand what this real thing is, it's, it's, it's, it is right. So it just kind of, (t: 2500) uh, my, my overall lesson is like, don't be discouraged. This is a really great time to be alive and, you know, ask us more questions and reach out wherever you want to reach out at and so forth. (t: 2510) Uh, get ahold of us. Yeah. Yeah, absolutely agree. I've been seeing more and more people trending around, Oh, use TDD to, to operate your cloud. And you don't need to know what test driven development is. You just have to think of the principle, (t: 2520) write a test, make sure it passes the test. Eric, would you say there's any other guidelines, principles for people to just do this exploration, this, play this discoverability of the capabilities or any advice on just, (t: 2530) you know, getting started and getting that comfort level of just, you know, experimenting with the tool. I mean, I really feel like, (t: 2540) um, obviously the adage of just use it. So I feel like everything that doesn't go right is an opportunity to figure out how can I talk to it better? (t: 2550) Um, and this has been sort of like from the beginning, if you think, just think about like what we're leveraging here by being a person who's using a tool like this, um, you're really setting yourself, uh, apart by, (t: 2560) because this is the flywheel, right? If you're, if you're like the, every moment you spend getting through the grinding on what is the difficult thing that most people stumble with? (t: 2570) What did, what, what failed here? And then learning, how do I overcome that by using the tool to help learn about it? And then continuing just that process, (t: 2580) then you develop your own system and it changes from project to project and project size and style to project size and style. And then you kind of get a feeling for it. Like what I'm, what I'm doing is not anything special or different than what anyone is able (t: 2590) to do is just, um, I've spent time with the tool and asking it a lot of questions whenever something didn't work. Right. (t: 2600) And so based on that, I found, okay, well, I don't want to like this friction between, I've got a chat conversation and it's like this, this, (t: 2610) you know, you get a long email thread from someone. We'll go back to that analogy. I'm just the top of my head. I don't know if it works, but then like they, there's 16 messages, but it's fresh every time. You've never seen it, you've never seen it before. And you've got to read through all of this stuff to figure out where you (t: 2620) were. Um, if you have to clear that email thread and start a fresh, well, there's stuff that you would want to carry over. (t: 2630) Not all of it, but some small subset of it that's really relevant. And that's the process of finding a system to preserve the right context. As you clear your previous session, (t: 2640) right? It will handle all the compact for you. All of these tools, all of these like cursor and windsurf and different models and wrappers for these models. Right. And then you can actually go back and look at the files, (t: 2650) um, try to solve that problem as easily as possible. But there's always still some friction there, like which context matters for the next session and how do you make sure you get it over? (t: 2660) And I think the persistence of the file mechanism, just writing, having it like update the documentation, write the next steps to the file that will be read in. And then as soon as you click clear, (t: 2670) you, you run the thing that causes it to read that documentation to know where to go. I think that system is, uh, you know, is very, very valuable. I think this is a good primer for people to get started with cloud (t: 2680) code and ask more questions and get a little bit more perspective on some of our use cases. I feel like this is just scratching the surface or understanding where (t: 2690) things can go. And I would encourage people to like, like Eric saying, just be curious, start playing, uh, maybe chunk down to like maybe one problem that you're trying to solve (t: 2700) that you're, you know, you see that little bar there, you know, start asking it the questions, trying to see if you can actually kind of work you through a workflow. And I feel like it, (t: 2710) a lot of people have these different areas of, of concern that, you know, they can kind of go down these rabbit holes. And I think these tools can get buried. That's very quickly. And just, (t: 2720) if anything, if I have one piece of advice to give anyone, it's like your context window is so important and protect it with all mighty power. Like it's your firstborn child and you don't want to let it go (t: 2730) ever. So, um, that type of thinking will kind of help you get the most out of this, like intelligence that we have, and, and yeah, I guess you're, (t: 2740) um, if people want to find out some more, I do AI live streaming. So my name, Mar, my handles, uh, Ray Fernando, one, three, three, seven, but that's my YouTube channel. You could also find me on X. I post a lot on there as well. (t: 2750) And so I, I, I do AI live streaming several times a week and you can find me live streaming. I'm also going to have some recorded content kind of going over this concepts and stuff. So yeah, (t: 2760) appreciate you having me on the show, Mike. Yeah. I just want to say it's a great pleasure to get to hang out with you guys. It's really fun to find people who are, who are also interested in the same things around the world, (t: 2770) uh, or like-minded and interested in sort of driving forward in the possibility of like what things we can unlock for ourselves and our families and our, our future basically to make simple, (t: 2780) to give us more time for the things we want to do. And it's just a really fun sandbox and tool to be able to do so much. You don't maybe yet know the value when you're going to find (t: 2790) something in the future. You're like, Oh, I wish I could do that. But you've learned the skills to do that and to talk to AI in the right way, with the right, with the right tools to be able to do that. It's just, (t: 2800) it's very fun. And I just want to encourage all of you guys, uh, especially like Ray, it's really great to finally meet you. I've been a fan for a while. I really appreciate it. Mike, I was following you at open interpreter. (t: 2810) I think like it's, uh, it's been a journey and I don't know what's going to go, but I know that the future is bright and I really appreciate the ability to be able to get on and talk and share some of this (t: 2820) stuff. I've been kind of hidden, uh, doing my thing, talking to people one-on-one in the background, uh, involved in some, some interesting stuff. So, but anyone who's doing this, (t: 2830) I think, uh, you're spending your time very wisely because this is a good use. And the payoff is going to be really good long-term. Thank you for tuning into this conversation on cloud code with Ray Fernando and Eric boosts. (t: 2840) I had a great time talking to them. Both guys are two phenomenal people who I really enjoy hanging out with. We hope that you gained value out of this because I know I did. I learned stuff from these two just in this conversation, (t: 2850) but there's so much more we want to cover. We were limited by time and we could have kept going for hours. So there will be a part two, maybe here, maybe, maybe on one of Ray's live streams or maybe on Eric's Twitter. (t: 2860) So keep tuned for that. Please follow both of them. They're phenomenal people. And I just want to give a quick shout out to tool hive, the secure MCP servers that really help make you more comfortable sharing your personal information and cloud code uses (t: 2870) MCP servers. So you can definitely time in there. If you have any other questions, you know where to find us. Thank you for joining. We'll see you next week.

