---
title: Vibe Coding Just Got A HUGE Upgrade
artist: Income stream surfers
date: 2025-08-21
url: https://www.youtube.com/watch?v=l01zhgTYFvU
---

- [00:00:00](https://www.youtube.com/watch?v=l01zhgTYFvU&t=0) - Hey guys in today's video I'm going to be going through my entire vibe coding setup. I am convinced that vibe coding is actually the way to do things and that one-shot prompting and things like context engineering or Claude flow are actually over complicating things. Now I'm going to be talking about this in great detail today hopefully this is going to be beginner friendly so beginner friendly people can start to build their own applications as well. If there's anything you don't understand just look it up on chat.gbt and everything will be in the description of this video in this document which you can find either in a pinned comment or in the description. So let's get into it. This video will break down the process I use to vibe code applications. I personally believe

- [00:00:30](https://www.youtube.com/watch?v=l01zhgTYFvU&t=30) - anything you don't understand just look it up on chat.gbt and everything will be in the description of this video in this document which you can find either in a pinned comment or in the description. So let's get into it. This video will break down the process I use to vibe code applications. I personally believe that one-shot prompting is kind of pointless and doesn't really work but also vibe coding if you just jump onto Claude code and expect it to work it also doesn't work. So this is how I do it. So the first stage is obviously to download and install Claude code. You can use Claude desktop, you can use cursor, you can use whatever you want. I just use Claude code personally and I also think it's the best setup. You should use the pro or max plan for maximum efficiency of cost. You will never pay more than a hundred or two hundred dollars and this

- [00:01:00](https://www.youtube.com/watch?v=l01zhgTYFvU&t=60) - you can use whatever you want. I just use Claude code personally and I also think it's the best setup. You should use the pro or max plan for maximum efficiency of cost. You will never pay more than a hundred or two hundred dollars and this is massively worth it for people. Now you can either set up on Windows or Mac or Linux you can set up on anything. You can set it up inside Docker if you're having problems just try and get it set up in Docker instead. So let's talk a little bit about Docker. So Docker basically what it allows you to do is it allows you to containerize your applications so that you can code in the same environment that your code will actually sit on when you launch it onto

- [00:01:30](https://www.youtube.com/watch?v=l01zhgTYFvU&t=90) - it allows you to containerize your applications so that you can code in the same environment that your code will actually sit on when you launch it onto a server. I think like 99% of servers I don't know what the actual figure is are Linux based servers so if you're developing on a Mac or if you're developing on Windows like the majority of people who are vibe coding probably are you're gonna have problems unless you use Docker. So just install those two things Claude code is fairly easy to install and also Docker desktop is fairly easy to install. Once you've installed them both then you want to open up a terminal. Now a terminal is basically it's just you know it's it's the back

- [00:02:00](https://www.youtube.com/watch?v=l01zhgTYFvU&t=120) - are you're gonna have problems unless you use Docker. So just install those two things Claude code is fairly easy to install and also Docker desktop is fairly easy to install. Once you've installed them both then you want to open up a terminal. Now a terminal is basically it's just you know it's it's the back end ignore that file name it's the back end of your computer right if you think of it like that so this is how you talk directly to your computer like if you want if you want to run a script if you want to make a new file whatever then instead of you know going through the user interface you go directly to the back end. So the way Claude code works is that it's folder based so you want to make a new folder with mkdir which means which means make a new directory and

- [00:02:30](https://www.youtube.com/watch?v=l01zhgTYFvU&t=150) - want if you want to run a script if you want to make a new file whatever then instead of you know going through the user interface you go directly to the back end. So the way Claude code works is that it's folder based so you want to make a new folder with mkdir which means which means make a new directory and we'll call this I don't know anything that comes to mind coffee right and then we'll cd into coffee. CD is change directory so you see after I wrote cd coffee it changed directory to the coffee folder so now we're coding inside the coffee folder instead of my main Mac folder. Now to ensure that Claude code is installed you want to do Claude dash dash version

- [00:03:00](https://www.youtube.com/watch?v=l01zhgTYFvU&t=180) - see after I wrote cd coffee it changed directory to the coffee folder so now we're coding inside the coffee folder instead of my main Mac folder. Now to ensure that Claude code is installed you want to do Claude dash dash version right and then it should say 1.06 or whatever the current version is when we're doing this and then we want to use this command here Claude dash dash dangerously skip permissions. Now you don't have to do this it's just I do this because I don't want to sit there pressing enter all the time it's just kind of annoying etc. So once you're here what you want to do is you want to say set up Docker CLI and get CLI so you can run your own Docker and GitHub CLI

- [00:03:30](https://www.youtube.com/watch?v=l01zhgTYFvU&t=210) - kind of annoying etc. So once you're here what you want to do is you want to say set up Docker CLI and get CLI so you can run your own Docker and GitHub CLI commands also please make it pop up pop up with an OAuth login for GitHub. Now you can just copy this prompt it'll be in the document as well I'll just add it right now so that you can just take it from the document if you want so we'll just put this here like that and I'll solve that in a second. What you want to do now is just press enter I don't have to do this because I already have GitHub set up but

- [00:04:00](https://www.youtube.com/watch?v=l01zhgTYFvU&t=240) - right now so that you can just take it from the document if you want so we'll just put this here like that and I'll solve that in a second. What you want to do now is just press enter I don't have to do this because I already have GitHub set up but yeah you do need to do this to part the process and then I don't think I have this one set up so I'm gonna do the same thing again I'm gonna I'm gonna set up my database I'm gonna say please set up the super base CLI and set up a local bb for testing purposes. So what this basically means is instead of using the MCP I think a lot of people are obsessed with MCPs with good reason they are very very cool but 99% of the time with cloud code you actually don't need an MCP you can just literally run the CLI which is the command line interface

- [00:04:30](https://www.youtube.com/watch?v=l01zhgTYFvU&t=270) - bb for testing purposes. So what this basically means is instead of using the MCP I think a lot of people are obsessed with MCPs with good reason they are very very cool but 99% of the time with cloud code you actually don't need an MCP you can just literally run the CLI which is the command line interface which is basically it runs terminal commands instead of using an MCP to talk to an MCP server so it's just over complicating things you don't need the super base MCP you can just use a local super base server so you'll see in a second it's just installed the super base CLI then it will initialize a super base project it'll start a local super base database and then we know that it can basically use that local configuration as the local database for

- [00:05:00](https://www.youtube.com/watch?v=l01zhgTYFvU&t=300) - second it's just installed the super base CLI then it will initialize a super base project it'll start a local super base database and then we know that it can basically use that local configuration as the local database for whatever project we're building so you don't actually need MCPs you don't actually need to go into super base you don't need to start this that and the other you can see right now it's just doing it all itself right so I'm just starting a super base project in this project which is a really really important part of this process but something that people massively over complicate by going on super base comm or getting the super base MCP and getting all their API keys and blah blah that's not really needed until much further on

- [00:05:30](https://www.youtube.com/watch?v=l01zhgTYFvU&t=330) - important part of this process but something that people massively over complicate by going on super base comm or getting the super base MCP and getting all their API keys and blah blah that's not really needed until much further on in the process when you're basically using a production kind of dev mindset and you need to bug fix that's when you need the super base MCP but at this point in the process you do not need the super base MCP you can just set up a local super base server and you can run it there okay so we can actually see if we go here now then I believe anyway I've never actually done this before I probably should have done this before I made a video on it but pretty sure this should just be super base yeah exactly so this is a locally running version of

- [00:06:00](https://www.youtube.com/watch?v=l01zhgTYFvU&t=360) - local super base server and you can run it there okay so we can actually see if we go here now then I believe anyway I've never actually done this before I probably should have done this before I made a video on it but pretty sure this should just be super base yeah exactly so this is a locally running version of super base and I've seen people do this before that's why I did it so what we can now do is we can use this as a base for our actual project right and then I'm assuming you can also self host super base on for example digital ocean although I would have to look into that etc etc but for now let's just continue with this but you can see how we can very easily just set up a set up super base locally and we don't need the MCP right now there is one MCP that I do

- [00:06:30](https://www.youtube.com/watch?v=l01zhgTYFvU&t=390) - although I would have to look into that etc etc but for now let's just continue with this but you can see how we can very easily just set up a set up super base locally and we don't need the MCP right now there is one MCP that I do recommend which is the digital ocean MCP so obviously I can't show you guys this exactly because I have my own API key but I just ran this code here with this with my actual API key this is just a this isn't a real API key just so you know and then so you what you want to do is you want to exit out of Claude code run the command right and then you do Claude dash dash dangerously skip permissions and then you do dash C at the end of it right and then then you're

- [00:07:00](https://www.youtube.com/watch?v=l01zhgTYFvU&t=420) - know and then so you what you want to do is you want to exit out of Claude code run the command right and then you do Claude dash dash dangerously skip permissions and then you do dash C at the end of it right and then then you're continuing with the conversation that you're having before but now if I run slash MCP you'll see the digital ocean MCP is now connected so you basically now at this point you have everything you need for a CI CD pipeline what you should now do is when you actually have some code is you should push to a github right which is called dev and then have another one called main right

- [00:07:30](https://www.youtube.com/watch?v=l01zhgTYFvU&t=450) - should now do is when you actually have some code is you should push to a github right which is called dev and then have another one called main right and then do the same with the database right so you have a dev database and a main database you split everything up and then you do all your dev here and when you're ready you push it to digital ocean and then when you're actually ready to launch you do dev becomes main then you launch it here and then you make a change locally you push it to dev you do all the database migrations you write codes to do the database migrations and then you push everything

- [00:08:00](https://www.youtube.com/watch?v=l01zhgTYFvU&t=480) - ready to launch you do dev becomes main then you launch it here and then you make a change locally you push it to dev you do all the database migrations you write codes to do the database migrations and then you push everything to main and it should automatically update the database in main as well so you can also add any other MCPs I'm not going to be adding any other MCPs that's all we need so what I'm going to do is I'm just going to send this right using Docker and so use random ports as I have a lot of Docker projects running at the moment and then I'll just enter what this is going to do is it's going to create an HTML CSS JavaScript front-end including a dashboard and then it's

- [00:08:30](https://www.youtube.com/watch?v=l01zhgTYFvU&t=510) - Docker and so use random ports as I have a lot of Docker projects running at the moment and then I'll just enter what this is going to do is it's going to create an HTML CSS JavaScript front-end including a dashboard and then it's going to create a fast API backend which is a full stack application that you can see here front-end backend both containerized with Docker using random ports what that means is we're starting to actually build our environment that we're going to code now you might be tempted to try and make it already start building something I would argue it's better to get a skeleton of everything you need first and then start to build something so kind of the last phase of the process is to find the documentation that you need now I think I only need an

- [00:09:00](https://www.youtube.com/watch?v=l01zhgTYFvU&t=540) - building something I would argue it's better to get a skeleton of everything you need first and then start to build something so kind of the last phase of the process is to find the documentation that you need now I think I only need an open router API key here and just like two pages so the quick start is good and then we need web search too so literally these two pages all I do is just press copy page here that's all I need to feed this to board code and put for it to build whatever I want so I'm going to be showing you guys this entire process I am going to build something shortly whether I'll finish the entire project or not is another matter because it does take a bit of time doing it this way but

- [00:09:30](https://www.youtube.com/watch?v=l01zhgTYFvU&t=570) - copy page here that's all I need to feed this to board code and put for it to build whatever I want so I'm going to be showing you guys this entire process I am going to build something shortly whether I'll finish the entire project or not is another matter because it does take a bit of time doing it this way but I'm just going to show you that by building the building blocks like this you can build something completely functional in an hour for example so I'm just going to say can you serve agents to hurry this part up it's not that complicated and then sorry there is another MCP that I've just thought of while I've been working on this I'm actually going to add this as well so add the playwright MTP for testing this is essential now you want to add the playwright MTP because it again actually though to be fair you can I'm just going

- [00:10:00](https://www.youtube.com/watch?v=l01zhgTYFvU&t=600) - while I've been working on this I'm actually going to add this as well so add the playwright MTP for testing this is essential now you want to add the playwright MTP because it again actually though to be fair you can I'm just going to put this in brackets could use the playwright could use playwright scripts right playwright scripts I would argue in this case the MCP is better right the reason being is that it can use AI like clawed code to identify where it has to click and things whereas with the script it has to kind of already know the layout of the page which of course it does because it's just coded it but I still think the MCP is better in in this case being see here I just quickly said can

- [00:10:30](https://www.youtube.com/watch?v=l01zhgTYFvU&t=630) - reason being is that it can use AI like clawed code to identify where it has to click and things whereas with the script it has to kind of already know the layout of the page which of course it does because it's just coded it but I still think the MCP is better in in this case being see here I just quickly said can you serve agents to hurry this part up it's not that complicated I'm just going to quickly do is I'm just going to press escape exit out do the clawed MTP command and then we'll do clawed dangerously skip permission C and then we'll do slash MTP this should work yeah I'll say I just added the playwright MTP so you can test properly please continue please hurry up okay so like I

- [00:11:00](https://www.youtube.com/watch?v=l01zhgTYFvU&t=660) - we'll do slash MTP this should work yeah I'll say I just added the playwright MTP so you can test properly please continue please hurry up okay so like I said because this is quite a simple task and I only recommend doing this with incredibly simple tasks I just asked can you please spin up multiple serve agents because it was taking ages I don't want this to take ages I want to show that you can do this in kind of under an hour so yeah now you can see it's actually doing this properly so you can see we're starting to get like rapidly increasing things like we've got a Dockerfile now got a Dockerfile for the back end super base looks really really cool this is kind of you can see the the setup is

- [00:11:30](https://www.youtube.com/watch?v=l01zhgTYFvU&t=690) - doing this properly so you can see we're starting to get like rapidly increasing things like we've got a Dockerfile now got a Dockerfile for the back end super base looks really really cool this is kind of you can see the the setup is very very simple you have front-end back-end database right very very well organized this is what you want to be going for guys because trust me shit gets complicated very very quickly another thing I highly recommend is having a lot of m variables you really really need to use and variables you do not want to mess this up because the way it works is right you have when you have something like super base and non-key instead of putting never hard code

- [00:12:00](https://www.youtube.com/watch?v=l01zhgTYFvU&t=720) - having a lot of m variables you really really need to use and variables you do not want to mess this up because the way it works is right you have when you have something like super base and non-key instead of putting never hard code something like this okay always put an m variable like I it's so important because when you put it on digital ocean and you have dev and prod trust me it gets very confusing very quickly unless you code like this and also just remember you can have an m variable for anything like one thing that we did was stripe discount codes or an m variable for us and then what you want to see is when it says come Docker compose up dash dash build and then you can see it's

- [00:12:30](https://www.youtube.com/watch?v=l01zhgTYFvU&t=750) - remember you can have an m variable for anything like one thing that we did was stripe discount codes or an m variable for us and then what you want to see is when it says come Docker compose up dash dash build and then you can see it's even opening up playwright and navigate into that page so this is what it's done so far right this is everything I've asked it to do so far it's the dashboard it hasn't separated the dashboard and home page and I'm just going to mention that quickly to it but you can just see how quick that actually was I'm just going to say please separate the dashboard and the home page the home page should be the base URL not the dashboard also please make the

- [00:13:00](https://www.youtube.com/watch?v=l01zhgTYFvU&t=780) - was I'm just going to say please separate the dashboard and the home page the home page should be the base URL not the dashboard also please make the match I'll do that later okay so this is one last thing I do you probably do this in prompting in the original thing guys if you're not following this step by step if you're following this step by step then do this now because you don't want the dashboard to be on the home page obviously and also this is kind of messed up as well you don't want to go into things with a messed up CSS pattern to be honest with you but yeah you can you can just reprompt this for example it's quite funny because it's come up with coffee shop management system I am

- [00:13:30](https://www.youtube.com/watch?v=l01zhgTYFvU&t=810) - want the dashboard to be on the home page obviously and also this is kind of messed up as well you don't want to go into things with a messed up CSS pattern to be honest with you but yeah you can you can just reprompt this for example it's quite funny because it's come up with coffee shop management system I am thinking of doing little sassas and things like that to handle little problems that I see like a little coffee shop CMS or a little coffee shop management system and that'd be pretty cool actually to be fair but I guess they kind of want apps not web apps but yeah you could just literally make thousands and thousands of these right and have them stand on their own two feet I'm going to talk a little bit about SEO towards the end of this video guys so if you're curious about that definitely keep watching as I'm going to talk about how I envision a model where you grow your application without any effort just

- [00:14:00](https://www.youtube.com/watch?v=l01zhgTYFvU&t=840) - thousands and thousands of these right and have them stand on their own two feet I'm going to talk a little bit about SEO towards the end of this video guys so if you're curious about that definitely keep watching as I'm going to talk about how I envision a model where you grow your application without any effort just simply using SEO okay so now this is the home page right and then we press dashboard and go to the dashboard perfect so this is this is what we wanted so we'll just stop this this is perfect you could run slash compact right now I don't think I even need to to be honest with you I'm just going to show you a very very simple thing that I do to actually now build the application right so like I said first of all we grab the open router stuff so just paste this and we'll just paste this and we'll just say I need an open

- [00:14:30](https://www.youtube.com/watch?v=l01zhgTYFvU&t=870) - show you a very very simple thing that I do to actually now build the application right so like I said first of all we grab the open router stuff so just paste this and we'll just paste this and we'll just say I need an open route key as well actually oh actually I'm just gonna grab their models page as well because it annoys me that it just assumes that the models that I'm talking about don't fucking exist super super frustrating so copy this page to good so GPT-5 okay so we'll just say use GPT-5 it's a model

- [00:15:00](https://www.youtube.com/watch?v=l01zhgTYFvU&t=900) - about don't fucking exist super super frustrating so copy this page to good so GPT-5 okay so we'll just say use GPT-5 it's a model I promise I want you to create a business summary generator in the dashboard which creates a business summary for our keyword tool it takes business URL as an input searches it on Google or whatever using web search from

- [00:15:30](https://www.youtube.com/watch?v=l01zhgTYFvU&t=930) - dashboard which creates a business summary for our keyword tool it takes business URL as an input searches it on Google or whatever using web search from open router and then generates the business summary this is all I want you to do for now make sure that dashboard this place properly for this so just one thing that you guys kind of need to know is unless you restart Docker oftentimes the changes that it says have been done will not show up here so just be very very wary of that you could for example prompt here and say make sure

- [00:16:00](https://www.youtube.com/watch?v=l01zhgTYFvU&t=960) - one thing that you guys kind of need to know is unless you restart Docker oftentimes the changes that it says have been done will not show up here so just be very very wary of that you could for example prompt here and say make sure that every time you make a change Docker restarts automatically but if you just always remember and it should also remember this but if you always remember to tell it to restart Docker then that's also a good way to do it now one more thing I want to mention is when you've built your first feature add it add everything that it's done so far to memory or another way to do this is when you see compact here or context length left until compact once it gets low just

- [00:16:30](https://www.youtube.com/watch?v=l01zhgTYFvU&t=990) - thing I want to mention is when you've built your first feature add it add everything that it's done so far to memory or another way to do this is when you see compact here or context length left until compact once it gets low just have it really fill in its memory of everything it's done everything correct that it's done so far right because you do want to have kind of like a story or like a memory of everything that has been done now that's super super important please do not forget that okay so we see here the business summary generator has been added but you can see as well that it's actually finding out mistakes as it goes along this is the key thing about this process instead of

- [00:17:00](https://www.youtube.com/watch?v=l01zhgTYFvU&t=1020) - important please do not forget that okay so we see here the business summary generator has been added but you can see as well that it's actually finding out mistakes as it goes along this is the key thing about this process instead of me testing it and not necessarily checking all of the logs in this case okay obviously there's something wrong with the CSS here which needs to be fixed so now I'm just working with it right so I'm just telling it what to do I'm saying just set the open route to API key in the dot-env and also fix the weird CSS issue on the dashboard page okay so yeah another thing with play right guys is if you're having a little bit more of an issue with stuff you can actually get it to take its own screenshots which is what I just did you can see here can you take a screenshot of it it's getting kind of stuck this

- [00:17:30](https://www.youtube.com/watch?v=l01zhgTYFvU&t=1050) - weird CSS issue on the dashboard page okay so yeah another thing with play right guys is if you're having a little bit more of an issue with stuff you can actually get it to take its own screenshots which is what I just did you can see here can you take a screenshot of it it's getting kind of stuck this happens a lot with CSS you'll notice this but it's not just images right you actually need to process the CSS as well and then yeah the really important part of this guys is to actually process the CSS so it finds the mistake with the screenshot it fixes it and then you process it and it sees that it's actually not updating so it needs to do a complete hard refresh of the website then it's going to make sure that the right things are actually being processed

- [00:18:00](https://www.youtube.com/watch?v=l01zhgTYFvU&t=1080) - actually not updating so it needs to do a complete hard refresh of the website then it's going to make sure that the right things are actually being processed this time okay you can see now it's actually fixed the problem so let's see if this is now working up what okay yeah it looks like it's actually working that see human generate summary okay so this is kind of the secret behind things guys you have both the playwright but you also have the docker logs so we can see here what's going on in the back end as well and in the front end and then we can see what's going on

- [00:18:30](https://www.youtube.com/watch?v=l01zhgTYFvU&t=1110) - okay yeah it looks like it's actually working that see human generate summary okay so this is kind of the secret behind things guys you have both the playwright but you also have the docker logs so we can see here what's going on in the back end as well and in the front end and then we can see what's going on in the browser using playwright this is the key to everything that we are doing and we can see that it is working right so it's analyzing the business website at the moment so this is a much much faster way to do everything right it just feels like it takes longer because you have to take the time to actually do it but the issue with context engineering is just to get or you know Archon or Claude flow is they can get you to this point more quickly but first of

- [00:19:00](https://www.youtube.com/watch?v=l01zhgTYFvU&t=1140) - just feels like it takes longer because you have to take the time to actually do it but the issue with context engineering is just to get or you know Archon or Claude flow is they can get you to this point more quickly but first of all you might not know how it's built second of all it might have placeholder code all over the place third of all it's just not you know you're never going to be sure of the actual code that's inside your application which is just a terrible way to code be honest with you so we see here raw summary copy summary I guess download as JSON I'm not actually sure where the summary is but okay so this is another thing I really really recommend you do as well guys is that when you're coding this stuff you ask it to add error logging so

- [00:19:30](https://www.youtube.com/watch?v=l01zhgTYFvU&t=1170) - just a terrible way to code be honest with you so we see here raw summary copy summary I guess download as JSON I'm not actually sure where the summary is but okay so this is another thing I really really recommend you do as well guys is that when you're coding this stuff you ask it to add error logging so do you know all of the outputs from all of the API's because what can often happen is something comes back empty like it did before I just got this which makes it look like it's empty right but the response is not empty it's just it didn't code it properly so just be really really careful with things like that make sure that you're reading all of the outputs and the code more importantly is reading all of the outputs from everything okay so I think

- [00:20:00](https://www.youtube.com/watch?v=l01zhgTYFvU&t=1200) - makes it look like it's empty right but the response is not empty it's just it didn't code it properly so just be really really careful with things like that make sure that you're reading all of the outputs and the code more importantly is reading all of the outputs from everything okay so I think I have to use GPT-4-0 I think GPT-5 it requires a little bit more setup with open router and I'm not sure that this particular API key is set up I'm not exactly sure how that works but I'm just gonna hop on over to GPT-4-0 mini instead see if that actually works okay there we go so now this is generating a business summary you can see apple.com this is the business summary that was created obviously you can change this prompt but now what we want to do is

- [00:20:30](https://www.youtube.com/watch?v=l01zhgTYFvU&t=1230) - instead see if that actually works okay there we go so now this is generating a business summary you can see apple.com this is the business summary that was created obviously you can change this prompt but now what we want to do is kind of build the rest of this keyword tool which is actually what I'm building I'm gonna say okay now add two more prompts after this one as a workflow the first one takes the business summary and generates all main pillar page topics that you would expect a business like that to have then the second one takes each each pillar page and generates ten sub pillar pages this

- [00:21:00](https://www.youtube.com/watch?v=l01zhgTYFvU&t=1260) - pillar page topics that you would expect a business like that to have then the second one takes each each pillar page and generates ten sub pillar pages this should only be two prompts and the sub pillar pages should be displayed as potential blog post topics themselves not further subneaching down after that right and then press enter here now this isn't actually that important part of the process to show to be honest with you I'm just showing you because I don't

- [00:21:30](https://www.youtube.com/watch?v=l01zhgTYFvU&t=1290) - potential blog post topics themselves not further subneaching down after that right and then press enter here now this isn't actually that important part of the process to show to be honest with you I'm just showing you because I don't want to just show you some random shit that all it does is generate a business summary but I could already kind of move on from this part here and a final part of this that I do want to talk about is how to generate the front end right so I just showed you how I do the back end you just keep building on that keep adding new tools keep adding this keep adding that keep adding the other make sure you're doing end-to-end testing make sure everything is working right once you've done that then you create the front end with the context of the back end now the reason I actually have word stream open earlier is because I

- [00:22:00](https://www.youtube.com/watch?v=l01zhgTYFvU&t=1320) - adding new tools keep adding this keep adding that keep adding the other make sure you're doing end-to-end testing make sure everything is working right once you've done that then you create the front end with the context of the back end now the reason I actually have word stream open earlier is because I was looking for a keyword tool that I liked the design of what you can do is you can send this to Claude code with the playwright MCP you can say I really like the style of this website make my front end the same as this but use the context of my back end to generate it not the same but like say like change the colors a little bit etc etc and then you can say like generate five SEO pages or generate ten SEO pages and what you can do is you can go on here and you can

- [00:22:30](https://www.youtube.com/watch?v=l01zhgTYFvU&t=1350) - context of my back end to generate it not the same but like say like change the colors a little bit etc etc and then you can say like generate five SEO pages or generate ten SEO pages and what you can do is you can go on here and you can type in like keyword or like free keyword tool right which is funny because this is a free keyword tool and you can grab these keywords here right click here or click here copy these this is how I did the SEO for my application and then send it to Claude code and say you know generate ten pages that are linked to or that use this key this keyword right so let's just do keyword tools is not actually a free keyword tool so yeah like Google keyword tool

- [00:23:00](https://www.youtube.com/watch?v=l01zhgTYFvU&t=1380) - and then send it to Claude code and say you know generate ten pages that are linked to or that use this key this keyword right so let's just do keyword tools is not actually a free keyword tool so yeah like Google keyword tool right I don't know look for ones where they're not branded etc you can click on questions here for example if you want to find some questions that you want to answer for blogs right blogs is a bad word just pages is actually a better word okay so look for now I'm just I'm just gonna skip this part here where I actually make a working tool like I'm I can do this in another five minutes it's just a waste of time because I've done it a million times before and it's not really what this video is about so I'm gonna say now I want you to create the

- [00:23:30](https://www.youtube.com/watch?v=l01zhgTYFvU&t=1410) - actually make a working tool like I'm I can do this in another five minutes it's just a waste of time because I've done it a million times before and it's not really what this video is about so I'm gonna say now I want you to create the front end create me five SEO pages this is the website I want to use the the theme of but I want you to use a different font and colors and make it for my website my app is called best keyword tool AI calm completely made up guys obviously and then I'll give it word stream I want you to use word

- [00:24:00](https://www.youtube.com/watch?v=l01zhgTYFvU&t=1440) - for my website my app is called best keyword tool AI calm completely made up guys obviously and then I'll give it word stream I want you to use word streams layout but not colors or fun also choose from these topics for SEO page now this is kind of best part of this this is the part that I think is slightly different to what a lot of people are talking about right so I'm just gonna choose some of these I'm gonna do what are I don't know what are best keyword research tools are keywords important for SEO which tool can be used for keyword research I'll just copy these three for now and then we'll grab

- [00:24:30](https://www.youtube.com/watch?v=l01zhgTYFvU&t=1470) - slightly different to what a lot of people are talking about right so I'm just gonna choose some of these I'm gonna do what are I don't know what are best keyword research tools are keywords important for SEO which tool can be used for keyword research I'll just copy these three for now and then we'll grab some from the keyword suggestions so yeah no didn't copy great so I'll just grab some of these words okay so I'll just hit enter here should use playwright on word stream get all of its CSS and then start to build something that actually looks looks like I want it to write okay this is a point that I cannot

- [00:25:00](https://www.youtube.com/watch?v=l01zhgTYFvU&t=1500) - grab some of these words okay so I'll just hit enter here should use playwright on word stream get all of its CSS and then start to build something that actually looks looks like I want it to write okay this is a point that I cannot make enough you are gonna have to cancel it sometime so it just tried to create a new directory for the marketing website this is not what I wanted what I actually wanted it to do was put it directly in the front end so that what we've got already is already website right so it should be able to get word streams layout here I would have preferred it to process the CSS rather than take a screenshot but I'm not gonna pause it here I'll just let it do its thing and then I'm just gonna say because this is a simple task we spin up multiple sub agents to hurry it up with the front end I'm not that bothered I

- [00:25:30](https://www.youtube.com/watch?v=l01zhgTYFvU&t=1530) - streams layout here I would have preferred it to process the CSS rather than take a screenshot but I'm not gonna pause it here I'll just let it do its thing and then I'm just gonna say because this is a simple task we spin up multiple sub agents to hurry it up with the front end I'm not that bothered I think it's pretty good to spin up multiple sub agents I'm gonna go take a five ten minute break I'm gonna come back and I'm gonna show you how to actually launch this website and we've done all of this in basically under an hour and the only thing missing was the actual full implementation the backend but trust me I could code that myself with just using ChadGVT5 front end in less than two minutes so it's really not an important part of the process okay so I'm just gonna keep refreshing this here guy out there it is bang so this is kind

- [00:26:00](https://www.youtube.com/watch?v=l01zhgTYFvU&t=1560) - but trust me I could code that myself with just using ChadGVT5 front end in less than two minutes so it's really not an important part of the process okay so I'm just gonna keep refreshing this here guy out there it is bang so this is kind of what I've been trying to explain to people guys this is the really really amazing thing about this yeah about this setup is yeah we can make all of these amazing pages now it doesn't seem to have made I can't I can only see the home page right now but then yeah playwrights going through this so we can just follow playwright like look at this this is all just SEO SEO SEO SEO right trying to rank on Google for the various keywords etc and then obviously I ever

- [00:26:30](https://www.youtube.com/watch?v=l01zhgTYFvU&t=1590) - amazing pages now it doesn't seem to have made I can't I can only see the home page right now but then yeah playwrights going through this so we can just follow playwright like look at this this is all just SEO SEO SEO SEO right trying to rank on Google for the various keywords etc and then obviously I ever got SEO importance here as well bang bang bang yeah and this is kind of the entire process guys and then you just keep keep making the back end better and better keep adding more and more front end pages to rank on Google and that is my entire process guys once you get to this point all you need to do is say now push this to a private github and put it on digital ocean for me so it's just

- [00:27:00](https://www.youtube.com/watch?v=l01zhgTYFvU&t=1620) - better keep adding more and more front end pages to rank on Google and that is my entire process guys once you get to this point all you need to do is say now push this to a private github and put it on digital ocean for me so it's just running through the github process now it's going to create the github repo for me and then it's going to use the digital ocean MCP to actually push to digital ocean right so again I'm not doing anything guys this is just doing all of this itself I'm not really correcting anything anymore either this is my entire process and it's so simple and so easy we now have a database we have a github setup in about 10 seconds we're gonna have digital ocean setup and we can literally just yeah continue development and it's that easy guys so

- [00:27:30](https://www.youtube.com/watch?v=l01zhgTYFvU&t=1650) - is my entire process and it's so simple and so easy we now have a database we have a github setup in about 10 seconds we're gonna have digital ocean setup and we can literally just yeah continue development and it's that easy guys so we can see here best keyword tool AI there we go it's now building it should already be connected to github as far as I know I don't actually I'm not sure how that works but I'm assuming it's already connected to github and very very soon we'll have you know something that we could send to people you know whatever we could start to even get customers in or people on the wait list or whatever and then like I said at the beginning because we're using Docker and also

- [00:28:00](https://www.youtube.com/watch?v=l01zhgTYFvU&t=1680) - that works but I'm assuming it's already connected to github and very very soon we'll have you know something that we could send to people you know whatever we could start to even get customers in or people on the wait list or whatever and then like I said at the beginning because we're using Docker and also digital ocean as far as I understand it uses Docker or something similar to Docker there's not going to be any issues it should just build now it might not there might be a problem you know whatever we would just then fix the problem but as far as I know there should be no reason why this should not just build okay so there might be a problem clicking here it does take a little bit of time for the DNS to work I'm not going to be going through that in this video just because it does take so long but everything is set up right if we scroll up here you should see that you know selecting branch main

- [00:28:30](https://www.youtube.com/watch?v=l01zhgTYFvU&t=1710) - just build okay so there might be a problem clicking here it does take a little bit of time for the DNS to work I'm not going to be going through that in this video just because it does take so long but everything is set up right if we scroll up here you should see that you know selecting branch main check out checking out commit so it is connected to github and yeah that's pretty much the entire process like I said I'm not going to go through this now because it takes a while for the DNS for digital oceans actually show the website but I guarantee you that everything will be set up thank you so much for watching guys if you're watching all the way to the end of video you're an absolute legend I'll see you very very soon with some more content peace out

