---
title: This MCP Makes Claude Code Design 100x Better (FREE)
artist: Income stream surfers
date: 2025-07-28
url: https://www.youtube.com/watch?v=XtFSJXgmOEo
---

(t: 0) Okay guys, so I'm going to talk about the Playwright MCP in this video. It's actually much better than most people think. It's not just screenshots, I'm going to go into that in (t: 10) a lot of detail in just a second. It actually allows you to process the CSS, code, JavaScript in the browser, allow AI to actually understand what is happening. So towards the end of this (t: 20) video I'm going to show you an example of this but let's just follow me for two seconds. Have (t: 30) you ever been in the situation where you have a front-end design, right? And for some reason every time you try and edit this section here nothing actually changes in the code, right? (t: 40) I'm sure a lot of you have actually experienced this. I've experienced it many many times. (t: 50) There's something wrong with the actual code here. So when it makes a change instead of actually reflecting on the front end, it makes a change but the change doesn't (t: 60) actually affect anything because something is actually broken. That's where <PERSON><PERSON> comes in. Now if you don't know, <PERSON><PERSON> has an MCP. You don't need an API key. (t: 70) There are some complications to <PERSON><PERSON>. The main one is if you're on Mac or Linux. If you're using cloud code on Windows it should work just fine. But if you're using it on WSL for example, (t: 80) you will have a problem. So if you're using it on Windows, it should work just fine. But if you're not using it on WSL, you are going to have to install some kind of browser for WSL to use. (t: 90) WSL out of the box will not have Chrome or Chrome tools or anything like that installed. (t: 100) So look at the error and understand what the error is saying. Feed that error to Cloud Code. Get it to give you the commands and then eventually you will be able to run the Playwright MCP. (t: 110) Now what the Playwright MCP actually does is it will process the code of this page, right? And it will notice during that process that either (t: 120) something about the JavaScript or something about the CSS is off with this section. And that's why every time it makes a change, it's not actually reflecting in the front end because it's it's broken. (t: 130) Something about this is broken, right? And the AI, what it does is normally what the AI does, which is kind of annoying, is it just takes a screenshot (t: 140) or you take a screenshot, you send it to Claude Code. Right. And it edit it edits this section based off what it thinks would be correct. (t: 150) But it's not working off like what the code is actually or what code is actually being executed by the browser. (t: 160) So let's just see this in action. So I'm going to open up Claude Code. I'm going to use WSL just because I've used WSL since the beginning of Claude Code. (t: 170) I've just yeah, I just use it all the time. So so , I always like to make a new project. So MKDib video playwright (t: 180) example. Now I'm going to try and get some code from before that had this exact problem that no matter what we did, it would not fix. (t: 190) OK, so this is the example right here. We I'll show you how we normally do things and I'll show you it with playwrights. OK, so there was the section here we were having a real problem (t: 200) with every single time we tried to make it look like the screenshot. It just wasn't working. We use playwright the normal way with vision. I'm going to show you a very special way to do things. (t: 210) So I'm just going to start by taking a screenshot as you normally would. Right. And I'm just going to say, please put these in columns. (t: 220) Such a properly then not. Right, the formatted right now, if this works now, obviously, as I'm making a video that would be quite annoying, (t: 230) but I did do this originally and. No matter what we tried, Rowan tried for hours. He sent it to me. I tried for five minutes and realized, you know what? (t: 240) Let's try something else. So we'll just see what it does here. Right. I'll just show you this in action. This is like the normal way. Right. I can see the formatting issues displayed in a single column (t: 250) in proper spacing. Let me find and fix the link building template. Let's see if it can fix on its own. If it does, that's fine. But I'll still show you the kind of other fix anyway. (t: 260) It's just this originally you'll have to say my word for it. If. This does fix it first time, which I wouldn't be surprised just by the nature of what it's like to make videos. (t: 270) But I promise you, the first time we did this, it was not playing ball whatsoever. And I'm sure you guys have experienced the same thing as well. OK, so it says it's fixed it. I'll refresh this. (t: 280) Doesn't look like anything's fixed. I'll be very, very fair. Restart Docker just to make sure it's not a cache issue. I'll just restart Docker, although I believe CSS does not require (t: 290) Docker to be restarted, but I don't want people to comment saying, oh, you didn't restart Docker. So let's restart Docker quickly. OK, so you probably all experienced this a lot. (t: 300) Web server is running. The application should now reflect the layout fixes. Let's refresh. You can see nothing has been fixed. Now, I'm sure a lot of you have had the same experience as me. (t: 310) I've heard people talk about this before. OK, so what is the alternative? The alternative is to quit out of this, run one very simple command, which is for WSL. (t: 320) You can also get a Windows version, which I'll leave both. In the description of the video and literally just press enter here. This should work. (t: 330) MCPs are a little bit temperamental, as you may know. OK, that's how it ran sometimes. Like I said, it's temperamental. So let's do Claude. (t: 340) Oh, my God, I hate Windows. Claude-C. OK, let's do dash MCP. Should see that it's running. (t: 350) There we go. Connected. So now I'm going to say now actually fix the problem by using the Playwright MCP to process the CSS and Java script (t: 360) to understand why the formats aren't working properly. Right. (t: 370) So let's see this in action. This is the really cool thing about Playwright. And the thing that I have found to be easily the most reliable on actually fixing front end issues. (t: 380) Right. OK, so you can see you can easily put the the access key here. I just I didn't put it on dangerously allowed browsing, but that's fine. (t: 390) Let's see if it can make its way down to the link building stuff, which I guess it will be able to. There we go. Beautiful. (t: 400) So that should just take a second to load. OK, there we go. That loaded. So it's taking a screenshot. I didn't actually want it to do the screenshot stuff. (t: 410) That's fine. We'll just we'll let it let it play with its food here. OK, scroll down. Let's see. What it does now. Now, let me inspect the CSS being applied to the pricing section. (t: 420) This is the the key thing here. This is the difference, right? It's not taking a screenshot. It's looking specifically at the styles (t: 430) that are actually being applied to the live browser. Right. I believe again or not again. I'm not deaf, right? (t: 440) As everyone knows, I'm actually not deaf. I'm getting there. Right. And I probably will study development at some point because I just find it so fascinating. I think it's stupid to say that there's no point studying it anymore. (t: 450) But from my from what I'm seeing right now, it's evaluating the JavaScript. Right. So it's actually executing the JavaScript and seeing what the result of that is. (t: 460) So you can see, look, so it can actually see the issue here. OK, let me fix this by making the CSS more specific and ensuring it works with Bootstrap. (t: 470) So let's see if it actually works now and actually works. It actually fixes the problem. So I don't want to pause too much or skip too much because I just want you guys to see the process. And also, like, I want you to actually see that I'm not doing anything else. (t: 480) Right. Whether it. Whether it works or not. Right. I'll show you the process and then whether it works or not. (t: 490) OK, let's see live together whether it has worked or not. So it will navigate to link building here. There we go. And scroll down. (t: 500) Why is my heart beating so much? Like, I bet this doesn't work just because I'm obviously making a live video. So let's see. It didn't work. Obviously, I'll be really upset now if it also says that it's now fixed (t: 510) because it's not it's not fixed. This is the real problem with floor code, guys. I'm still going to leave this video up. (t: 520) I'm still going to release this video because it does solve a problem. But as you can see, it's still not 100%. OK, and there we go. So it did work it out in the end. Right. (t: 530) And it did fix it. I just said. It's still broken, by the way. And then this time it did the same thing. And then eventually I realized that the styles aren't being applied, which is weird. (t: 540) And then it works out that the problem was this just tiny piece of code right here where block head was correct instead of extra CSS. (t: 550) So actually it did work. It just needed one more push. But normally you'd be doing this for 15 hours in a row, going in a circle forever. Right. (t: 560) So hopefully this will help a lot of people because UI UX is definitely one of Claude's weak points in terms of like fixing things. Like, I don't know if you guys have noticed, but if you say like, (t: 570) I'll change this text to blue, like it will do a different piece of text or like it will do something else or just it just won't do things perfectly. Right. (t: 580) So what playwright does is it allows it to actually execute that code. So if you actually look after I said, please check again, it's literally still pure shit, buddy. The first thing it did was it tried to process the CSS again, and it still didn't work. (t: 590) Right. You can see I see the issue now. The CSS grid is still showing as display block, even when important. The styles aren't being applied. Let me check if there's a CSS file that needs to be compiled or if the styles are cached. (t: 600) Right. And then did a cache reset when that still happened. It like it's like the Sherlock Holmes expression, right? (t: 610) Yeah, it's this one here. Once you eliminate the impossible, whatever remains no matter no matter how improbable must be the truth. Obviously, this part's irrelevant, but it just through process of elimination, it realized (t: 620) like, oh, I'm still processing. It's still processing the old code. Why? There must be a cache. OK, let's do a cache reset. (t: 630) Now let's see if it's being applied. You know, and then it sees that there's another problem. It's like, well, how can there possibly be another problem? Right. So instead of it just saying, oh, I'm done, I'm finished, it realizes (t: 640) because it's actually processing the CSS and it still is using it's still not applying the changes that it made before. (t: 650) So there's a. Fundament. Problem with the code. That is the only explanation to this problem. It changed it. It did a hard refresh. (t: 660) And it still wasn't showing the right code. So realize there must be a serious or not serious, but an actual fundamental (t: 670) problem with the code and then found that and fixed it. This took 15 minutes. Rowan tried to do it for six hours or five hours the other day. He couldn't he couldn't do it basically. (t: 680) And luckily, at the time, it was a good day. And luckily, at the time, it was a good day. And luckily, at the time, it was a good day. And luckily, at the time, it was a good day. I just started to use the playwright MCP and I actually don't recommend using vision mode. I recommend not using vision mode and just letting it process the CSS (t: 690) because if it's processing it and reading the results, it's not guessing. It's not saying, oh, well, there's code there. Why isn't it working? (t: 700) You tell me. You tell me, buddy. But with the MCP, it's like, oh, I can actually see or it can actually see what CSS is being processed and therefore by pure process of elimination, there must be. (t: 710) And therefore by pure process of elimination, there must be. And therefore by pure process of elimination, there must be. A fundamental problem with the code. It finds the problem. It fixes the problem. And you can see here that it's the only way that we could actually manage to get this page correct. (t: 720) So, yeah, thanks for watching, guys. That was a really good demonstration. Actually, I enjoyed that. Thank you so much for watching. If you're watching all the way to the end of the video, you're an absolute legend. (t: 730) And I'll see you very, very soon with some more content. Peace out.

