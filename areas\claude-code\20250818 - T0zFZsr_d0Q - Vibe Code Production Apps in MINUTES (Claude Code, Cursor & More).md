---
title: Vibe Code Production Apps in MINUTES (Claude Code, Cursor & More)
artist: <PERSON>
date: 2025-08-18
url: https://www.youtube.com/watch?v=T0zFZsr_d0Q
---

(t: 0) I created a template that will drastically speed up your Cloud Code projects and get you better results. And it's completely free. Although I'll be using Cloud Code today, this will also work with Cursor, Windsurf and any other coding agent. (t: 10) This is perfect for both vibe coders and experienced developers who simply want to eat the ground running. (t: 20) I have taught a lot of people how to use agents to build their projects and I keep seeing the same patterns. They would use platforms like Lovable or Bolt to get a beautiful UI working, but then everything falls apart. (t: 30) They can't get back-end systems to work like user authentication, they're unable to persist data in a database or they're unable to store files. (t: 40) And that's exactly why I created this template, to solve all of these problems that's been tripping people up. You can set this boilerplate up within minutes and out of the box it supports authentication, (t: 50) a connection to a database, beautiful UI components and it's also AI ready. (t: 60) So you can add AI related functionality in your applications as well. So in the next few minutes, I'll show you how to download this boilerplate, set it up on your machine. (t: 70) We'll also build a small application and I'll show you how to deploy it to production so users can start using your application today. I'm planning a series where we'll build real world applications. (t: 80) And instead of wasting a lot of time, I'll show you how to build a new application. So let's get started. So let's start by setting up our project. We'll use this template to jump right into the project. First, I'll give you a quick overview on how this boilerplate works. (t: 90) And then we'll go through the entire process step by step. Sounds good? Let's jump into it. First, you can download the boilerplate by going to this GitHub repo page. (t: 100) Then under code, you can either clone the code or you can download the zip file. Then let's simply rename that folder and open it in our favorite code editor. (t: 110) I'll be using cursor in this video. Then let's go to the code editor. Let's open up the terminal and install the project's dependencies by running npm install. (t: 120) After installing the dependencies, we can start the dev server by running npm run dev. We can then open the project by clicking on this link. (t: 130) And now we can view our boilerplate project. When we scroll down, you will see the setup checklist. Now, technically, this is all optional. If you don't care about any of these, you can simply start building your project by (t: 140) clicking on start building and then entering this prompt. Well, let's say we wanted access to all of these features. What we would have to do is rename this dotenv file to dotenv. (t: 150) And in this file, we simply have to provide a few environment variables. Again, we will go through this entire process step by step during this video. So I've gone ahead and entered all of those details and now we can see the checklist is (t: 160) complete and we can move on to the next step, which simply involves migrating our data to this data center. Now, in the next step, we'll go ahead and enter our data center, which is the one that (t: 170) we'll be using for this project. which simply involves migrating our database schema to our actual database. Now, don't worry if you don't know what this means. Just know we need to run these commands in order for our database to work. (t: 180) I'll simply open up a new terminal and let's run the first command. And then let's run the second one. And after we've executed these commands, we can try out these features by trying to access a dashboard, (t: 190) which should be protected if you're not logged in. Or we can try chatting to an AI agent just to test out our AI functionality. (t: 200) Let's try the dashboard. This is correctly telling us that we do not have access to the dashboard and we have to sign in. (t: 210) So let's try that. And after selecting my Google account, I am indeed signed in. I'm able to access the dashboard and I can also see my avatar image was pulled in correctly. (t: 220) I'm able to sign out and I can see my name. And my email address. Now we can also test out our AI functionality by going to AI chat. (t: 230) And let's click on go to chat. This will give us a very basic interface where we can say, hey, and if everything was set up correctly, (t: 240) we can see the response from the large language model streaming in. Cool. And to start building, we can simply go to the start building section and click on get AI starter prompt. (t: 250) Now this will actually generate the prompt for you that you can pass to your agent when you're done with the process. When you first start to build your project. So we could provide something like, I would like to build a business listing platform that allows businesses to list their business and to assist users in searching for those businesses. (t: 260) When we copy this prompt, we can go back to our code editor. (t: 270) And if you're using cursor or windsurf, you can simply paste in that prompt. Or if you're using cloud code also, if you start up a new cloud code instance, and all we have to do is paste in that prompt and press enter. (t: 280) Now, I do. Want to show you what this prompt actually looks like. So what I recommend you do is go to the docs folder and under business, simply paste in that starter prompt. (t: 290) If we have a look at what that looks like, it's a super detailed prompt that tells the agent what the structure of this project is. (t: 300) And a lot of stuff like you need to override the boilerplate content. And of course, if you scroll down, we can see the section, what I want to build, and this includes your actual instruction. (t: 310) So if you scroll down, we can see the section, what I want to build, and this includes your actual instruction. And once the agent is done, we can refresh this project. And you can now see we actually have a working application. (t: 320) This includes user authentication. And all the data in this app is actually persisted in a database. I also want to mention that you do have access to light and dark modes. (t: 330) And we can also test out the authentication system. So let's try to sign in. This seems to work. So let's select my user. (t: 340) And I can complete this form to add my business. And from the dashboard. I can view the business that I just added. And of course, because we are connected to a database. (t: 350) When we refresh this app, our data is still there. And I'm actually going to try something. What happens if we enter a search phrase? We can see the search engine is actually working and pulling up relevant listings. (t: 360) And these are showing up because during build, Claude decided to see the database with sample data. Either way, I just wanted to show you that this is the power of this boilerplate. (t: 370) So whether you're a vibe coder or not, you can see that this is the power of this boilerplate. So whether you're a vibe coder or not, you can see that this is the power of this boilerplate. So whether you're a vibe coder or not, you can see that this is the power of this boilerplate. Either way, I just wanted to show you that this is the power of this boilerplate. So whether you're a vibe coder or not, you can see it here. Either way, I just wanted to show you that this is the power of this boilerplate. So this is the power of this boilerplate. (t: 380) So let's go back to building. Let's see if we start with the井 And when we create a build that opens up this book, it holds a token. (t: 390) Now, if we open up the tile sait, we can see that this is completely possible to manage. Now we have to build here, of this user that actually share this Mail. (t: 400) Verändern that we're working with. the UI of your application. So let's now go through this process step by step. And again, (t: 410) I want this template to appeal to everyone, whether you're an experienced developer or a non-coder who simply wants to vibe code their own solutions. So if you're familiar with (t: 420) using Git and GitHub, then feel free to skip ahead to the next section. You will find the link to this repo in the description of the video. And again, this is completely free. So from this page, (t: 430) you can definitely support my template by clicking on the star icon. Then when you scroll down, you will see all the features offered by this template, along with all the prerequisites. (t: 440) You'll need Node.js, we'll set up Git, and we'll need a Postgres database if you are planning on (t: 450) using a database. Then I've also listed all the setup instructions on this document. But don't worry, we'll go through everything together. Let's start with the prerequisites. (t: 460) First, we'll need to set up the template. So if you're a developer and you want to set up a Postgres database, you'll need to set up a Postgres database. First, we need to install Node.js. So go to Node.js.org, then click on Git Node.js, select your operating system, and install Node.js. (t: 470) Then we also have to install Git. So let's click on download here, then download and install Git for your operating system. Then if you are planning to persist your data in your app, (t: 480) which I assume everyone wants to do, we will set up a Postgres database. There are many different providers you could use to set up your Postgres database. (t: 490) But for this video, we will be hosting our database with Vercel, and there's a good reason for it. We're also going to use Vercel for our production deployments in this video. (t: 500) So for me personally, it's just really easy to keep everything in the same place. So go over to Vercel.com and sign into your account. The free tier is extremely generous. (t: 510) So from this dashboard, go to storage, then click on create database. From this list, (t: 520) click on Neon, and click on continue. Then select your region. I'm going to disable auth, and click on continue. Let's give this a name like YouTube tutorial, and click on create. (t: 530) And let's click on done. All right, that's all we have to do for now. You can leave this tab open (t: 540) as we will come back to these connection details in a few minutes. Now let's copy this repository over to our own machines. What you can do is... (t: 550) On the top, click on code. Then if you have Git installed, simply copy this URL, or alternatively, you can simply download the zip file and extract it on your machine. But I'm simply going to use (t: 560) Git. And I do want to mention there's another reason why we installed Git. We'll use it to do version control later on in this tutorial. This is how the checkpointing system will work. (t: 570) After the agent completed a feature, we would want to create a checkpoint of sorts, so that if we make a mistake, we can... (t: 580) Always roll back to that previous commit or checkpoint. All right, let's copy this URL. Then let's create a new folder anywhere on our machines, and let's give it a name, (t: 590) something like to-do app. Now this should all be in lowercase characters and no spaces. Instead of a space, use a dash instead. Then let's open this folder in our code editor. Again, (t: 600) I'll be using cursor, but for you, it might be windsurf or VS code. It really doesn't matter. Then in the terminal, (t: 610) and by the way, to access the terminal, you can press control or command and tilde, or at the top menu, simply go to terminal and new terminal. Then from here, enter Git clone, (t: 620) then paste in that URL, which you copied from GitHub, followed by space and dot. If you don't (t: 630) specify dot, it will create a subfolder within the current directory, which is not what we want. So I'll simply delete this. And instead, (t: 640) let's run Git clone with that URL space period. Let's run this, and this will now download that repo into the current working directory. Now that we've downloaded the boilerplate project, (t: 650) all we have to do is install the dependencies. So let's run npm install. So this will now automatically download and install all the dependencies for this project. And this only (t: 660) took a few seconds to complete. And what we can do now is run the dev server. We can do that in the (t: 670) browser. We can do that by typing npm run dev. This command will simply allow us to access our application. So it gives us this URL, which we can open in the browser. And there you go. (t: 680) If you see this page, then you successfully set up the boilerplate project. And if you don't care about having a database or authentication or any of these features, you can actually already start (t: 690) to build your application. Simply go down to start building, click on this button, and tell the agent (t: 700) like to build. But the point of this boilerplate is to allow you to build advanced applications that include authentication and the database. So let's set all of this up, starting with the (t: 710) database. Now I've built a boilerplate, so it actually shows you what is outstanding in your application. And we can also see that the first step is to set up these environment variables. (t: 720) So what we need to do is back in our project, rename this env.example file to .env. I'm just (t: 730) going to hide the terminal. And then in this file, we can provide the database connection. Where do we get this Postgres URL value from? Well, if we go back to our database in Vercel, I'm (t: 740) just going to click on show secret. And what we're looking for is this Postgres URL string. So I'll (t: 750) just copy this entire thing and add it to our project. Cool. So after saving this file, we can go back to our checklist. We can see this step number two over here. (t: 760) Let's show you how to do this. It shows you how to set up the database. All we have to do is run these two commands. Let's actually do that now. So back in our project, I'll just start another terminal. Then let's paste in (t: 770) that first command. And once this is done, let's grab the second command and run that as well. And now if we click on recheck, our database is now connected along with the schema. So if we (t: 780) wanted to, we can already start to build an app that's able to persist data in the database. And let's keep going. (t: 790) We also want to add user authentication to this project. By default, this project uses Google as the auth provider. And yes, it is free to use as well. It's not critical that you know this, (t: 800) but I just wanted to mention this as an aside. This boilerplate uses an authentication system called BetterAuth. If you wanted to add additional providers, so maybe you don't just want to use (t: 810) Google, you can simply go to the documentation of BetterAuth. Then under authentication, (t: 820) you can view all of the different providers like Apple, Discord, Facebook, of course, Google, which we will use and much, much more. So if you wanted to get additional details on how to add (t: 830) something like GitHub, as an example, you can simply go to the GitHub page, copy this markdown, (t: 840) and then ask your agent to add GitHub as an authentication provider in your app. Now, as I mentioned, we will be using Google as our provider. So what we need to do is complete (t: 850) the auth section in this .env file. In other words, we have to provide this client ID and the client secret. So in order to set up those keys, go to cloud.google.com and then sign into your (t: 860) account or click on console. Then let's create a new project. So from this dropdown, click on new project. Let's give it a name like to-do list vibe tutorial and click on create. Then click on (t: 870) select project or select it from this dropdown list. (t: 880) Then from the burger menu, click on APIs and services. Now this does involve a few steps, but it's really not that bad. And trust me, once you're used to it, you can speed run this entire (t: 890) process in under 10 seconds. From here, go to OAuth consent screen. Effectively, what we're doing here is we're setting up that pop-up that shows up when you click on sign in with Google. You know (t: 900) the one where you have to select your email address. So from here, click on get started. Then let's give our app a name like to-do list. (t: 910) Then select your email address. Click on next. Click on external. Then click on next and enter your email address. Click on next and select finish and continue. And finally, click on create. (t: 920) Now that that's done, click on audience and publish app and simply confirm this pop-up. (t: 930) Then finally, click on clients. Click on create client. For the application type, select web application. (t: 940) Let's give it a name like to-do list vibe tutorial. Then under authorized redirect URIs, click on add URI. And now we have to paste in a very specific value. And you can get that value (t: 950) from the GitHub repo by simply copying this URL when you are in development. And we will update (t: 960) this value once we deploy this to production. So simply copy this value and paste it in here. Then let's click on create. (t: 970) And now finally, we have access to our client ID and secret. So let's copy the ID and add it to our environment variable file. Let's also copy our secret and add it here as well. Right. So after (t: 980) we've added these values, let's go back to our app. Let's click on recheck. And now our auth system has been configured as well. This means users can now sign up or sign into our application (t: 990) using their Google accounts. Finally, we can also add AI integration. Now again, this is optional, but I'll do it for the sake of the tutorial. And by default, (t: 1000) this boilerplate uses OpenAI as the LLM provider. So what we need to do is get our OpenAI API key. (t: 1010) And you can get that by going to platform.openai.com slash API keys. Then let's create a new secret key. (t: 1020) I'll just call this to-do vibe tutorial. Let's create the key. Let's copy this. And let's add that to this API. So let's go back to our app and click on recheck. Our AI integration is now fully set up. We can (t: 1040) actually test that everything is working by going through this try the feature section. And let's click on view dashboard. This should allow us to sign in with Google. So let's click on this. (t: 1050) And now we do see this modal. And then I'll select my email address. And awesome, we now have a fully functional authentication system in our app. Then let's go back to the boilerplate. Let's click (t: 1060) on try AI chat. And let's say, why is the sky blue? And we get the answer streaming in, meaning our AI (t: 1070) integration is working as well. Cool. And of course, we are able to sign out and everything (t: 1080) is now ready to go. Next, I'm going to show you how to build your project. And then I'll show you how to deploy it to production. But before we move on, I do want to show you one more environment project. So let's go ahead and start with the project. So let's go ahead and start with the project. So let's go ahead and start with the project. So let's go ahead and start with the variable. And at the bottom of the file, you'll see this file storage section that expects a blob (t: 1090) read write token. Now this is useful if you want to allow your users to upload files or images. (t: 1100) Setting up file storage is really easy as well. In Vercel, go to storage, then create database and select blob storage. Then click on continue. Then give it any name and select your region (t: 1110) and click on create. Then from this page, click on .env.local and copy the snippet and add it to your .env file. Your agent will now be able (t: 1120) to add file uploads to your application. That was really the hard part. And trust me, once you're (t: 1130) familiar with this process, you can spin up this boilerplate within minutes. Now for the fun part, let's build our application. On this page, click on start building. And let's describe the app (t: 1140) that we're trying to build. I actually recommend writing a very detailed prompt here. So to do that, tell the agent exactly what type of features you want and what the user journey should be. (t: 1150) The more specific, the better. But for this demo, we'll simply build a to-do list app. I know the (t: 1160) app itself is not complex, but the focus of this video is to get you going with a boilerplate that includes authentication and database integration. We will use the same boilerplate to build super (t: 1170) advanced applications going forward. So let's say I would like to build a simple to-do app that allows users to sign in, add, remove, update, and complete to-dos. For fun, let's add some AI (t: 1180) capabilities as well. Let's say, please use AI to categorize the to-dos. Then let's copy the starter (t: 1190) prompt. And back in our project, let's go to docs, business, and let's replace the starter prompt that (t: 1200) we just copied. Cool. Now, of course, if you're using cursor, you're welcome to use the agent in the sidebar. But I've really been using the agent in the sidebar. So let's go ahead and do that. And let's go ahead and do that. And let's go ahead and do that. And let's go ahead and do that. I've really been enjoying Cloud Code lately. So I'll just use Cloud Code. I'm just going to (t: 1210) rename this tab to Cloud Code. And Cloud Code is simply asking our permission to access this (t: 1220) folder. But it's also telling us that it found an MCP server configuration file in the project. So what I've done is I've pre-configured this .mcp.json file to give our agents access to (t: 1230) Context 7. Now, Context 7 is simply a repository with up-to-date information, on all of the technical frameworks used within this application. So if the agents get stuck, (t: 1240) they can reach out to Context 7 to see the proper way of implementing these features. (t: 1250) So let's proceed with the MCP servers. So my workflow is as follows. I like to start the dev server by myself in the terminal. So always have the terminal open. And let me just stop it (t: 1260) for now. And in the terminal run, npm run dev. Now, if you want the agent to start the server for you, that's perfectly fine as well. There are actually pros and cons to both approaches. (t: 1270) If the agent starts a dev server, it will have a view of any error messages in the terminal, (t: 1280) and it will try to auto-correct those issues itself. Or if you just want to run the dev server yourself, like how I'm doing it here, if you run into any issues, you can just manually (t: 1290) copy the errors and paste it into the chat. Either way works. So all we have to do is click on the starter prompt. And then we can start the dev server. And if you want to start the dev server, then copy all of this and paste it into the chat. Or simply drag and drop this file into the chat (t: 1300) window. Either way is fine. Let's press enter. So Claude is saying, I can see you've shared the (t: 1310) starter prompt documentation for your project. The document emphasizes that this is a template meant to be completely replaced when building the actual application. And all we actually have to (t: 1320) say is, please go ahead and build this app. So Claude is now doing his thing. He's going to go ahead and create this to-do list. And it's asking my permission to make these changes. (t: 1330) I'm actually just going to press shift and tab. And this will go into accept edits mode, (t: 1340) which means it won't ask our permission every time it wants to change the file. Now we can sit back and wait for Claude to complete. And I do want to mention that sometimes you might see Claude asking your permission to generate the database schema (t: 1350) changes. This is perfectly fine as well. As Claude is building this application, it might realize that it's not working. So Claude can just go ahead and build this app. It needs more tables in the database to store new information relevant to the app. The first time (t: 1360) we manually executed these commands, it was to migrate over all the user tables so that our (t: 1370) authentication system would work. But since we're not creating a to-do list app, Claude will create new tables where all these to-do items would be stored. So you can just go ahead and accept those (t: 1380) changes to the database. All right, cool. So the agent is done. And I do want to share with you some of the cool features that Claude has created. So let's go ahead and build this app. And I do want to share with you some of the cool features that Claude has created. So let's go ahead and build this is included in this boilerplate. You will notice that whenever the agent completes its changes, (t: 1390) it will automatically run the database generate and migrate scripts as well as the next.js (t: 1400) build command to check for any error messages. So let's have a look at our app. The dev server is still running. And if we refresh this page, we get this internal server error. Now, (t: 1410) unfortunately, that will happen every time the agent runs the build command. That's just how Next.js works. So when that happens, simply stop the dev server by pressing control and C or (t: 1420) command and C and simply start the dev server again. If you want the agent to run the dev server (t: 1430) for you, you could simply provide a memory to say, always start the dev server yourself. After (t: 1440) completing changes, use npx kill to free up port 3000. So this is a very useful tip if you wanted (t: 1450) the agent to run the dev server itself. Right, let's refresh our app. All right, so let's have a look at what we got. We have an app that gives us access to a homepage and a dashboard. We have (t: 1460) our sign in with Google button at the top, and we're able to change the theme from light mode to dark mode. Then we have this folder. (t: 1470) We can add to do's, but we're not yet signed into this app. So this doesn't make a lot of sense yet. Let's go back to our agent and say, users need to be signed in in order to manage their to do's. (t: 1480) Then I also don't like that we're able to add to do's from the homepage. So instead, let's say the homepage should contain some marketing copy to promote our to do app. Also give the app a (t: 1490) catchy name other than to do app. Use a suitable icon with the logo. (t: 1500) Then we have the app. We have the app. We have the app. We have the app. We have the app. Then move the to do app features to a dashboard page instead. Let's run this. Cool. So I can see Claude has already started making changes to the app, and we will fix up the styling of this app (t: 1510) in a second. Let's just wait for Claude to complete. And apparently it is done. So of course, when I refresh this, it's going to fail and we just have to restart the dev server. (t: 1520) And then we can see our app. So it's got all of this cool marketing copy. And let's try to sign (t: 1530) in. I'll select my email. And now we can manage our to do's from the dashboard. Let's try this. Let's say buy bread. Let's add this. And cool. There we go. It's also say call mom. Let's add this. Nice. (t: 1540) We can see our two items showing up. Let's try to set this one to complete, which it did. Also, (t: 1550) if I refresh this page, all of our data should still be there, which it is cool. So we now have an app with a working authentication. So let's try to set this one to complete. And now we can (t: 1560) see our to do's from the dashboard. So let's try to set this one to complete. And now we can see our to do's from the dashboard. So let's try to set this one to complete. And now we can system and our data is persisted in a database. Now let's have a look at fixing the UI. By default, it's using all the colors that I used in the boilerplate template. But you are able to set up (t: 1570) your very own design system yourself. So I'm just going to clear this chat. And what you can do is run a custom slash command, which is included in this boilerplate. And that command is called (t: 1580) create design system. When you run this, this agent will ask you a bunch of questions. And (t: 1590) you can have a back and forth conversation with this agent to build out the perfect design system (t: 1600) for you. So let's say we want a warm and friendly color scheme and a playful and casual visual style. (t: 1610) You can design your own design system. So let's say we want a warm and friendly color scheme. And a (t: 1620) side on the rest, which would make sense for a to do list app. Let's run this. So what this agent will do is actually take into account all of your preferences. And now create a custom design system (t: 1630) in the docs folder, we can see it's created this new folder called UI. And within this UI folder, (t: 1640) it's going to create four different files. And I do want to mention that you can use the same slash command to iterate on this design at any point. So if you're really (t: 1650) don't like something about the design, run the same command again. But this time, the agent will see that you already have a design system in place, and then ask you what you would like to (t: 1660) change about that system. Cool. So what exactly can we use this for? We can simply drag and drop this UI folder into the chat, and then tell the agent to please fix the UI or a specific page. (t: 1670) But what we can do instead, if you are using cloud code is use a specialized sub agent that (t: 1680) can do the UI components for us. So we can tag that agent with the add symbol. And then in this list, let's look for the agent UI developer. Now this sub agent, you can actually see it in this (t: 1690) agents folder contains a lot of custom instructions to force it to implement this very specific design (t: 1700) system. So here we tell it to always follow the guides located in this docs folder. So let's (t: 1710) run this. Of course, you could just give this UI folder to your main agent, and it should do a very (t: 1720) good job. But the sub agent was carefully prompted to give even better results. All right, so the agent is still busy. But we can already see that the new design looks very different to the boiler (t: 1730) plate. The design is way more casual and warm. And we have this fun little animation as well. What I did notice is that this text is very different from the previous one. So I can see (t: 1740) that the text is quite hard to read. And if I go to the dashboard, this text is hard to read as well. So when this happens, in fact, as I was talking, the agent automatically fixed this. So maybe it's (t: 1750) not an issue. But if you run into any design issues, for instance, text that's hard to read, what you can do is take a screenshot of the UI, and then paste it back into the chat. It's very (t: 1760) hard for these agents to kind of predict what this UI would look like, as it's effectively coding blind. So if you run into any design issues, you can run into any design issues, and you can run (t: 1770) So if you can give it a screenshot of what you see, it will greatly improve the results as well. And really scrolling down with this page, I hope you can see this design is very different to what (t: 1780) we had before. All right, so it's completed all of these UI changes. And if I refresh this, the agent fixed up all of his little UI issues. And now we have a fully functional to do list app (t: 1790) that looks very different to the boilerplate template. Now let's talk about checkpointing. I recommend creating checkpointing in the boilerplate template. So if you're using a (t: 1800) checkpoint, you can create a checkpoint as often as possible. Since we have to find the mantles of the app up and running, it would be a good idea to create a checkpoint or a commit. This means that if something goes wrong with the app, we can always roll back to this current (t: 1810) checkpoint. So what we can do is tell Claude to create a git commit. Or what you can do is use a custom slash command, which is included in this boilerplate as well. So enter slash, then select (t: 1820) checkpoint, and Claude will now create a checkpoint for you. So let's go ahead and create a checkpoint for Claude. So let's go ahead and create a checkpoint for Claude. The first time you execute this, it will ask you to run git init. Just say yes. Then I'll just say yes again. (t: 1830) And cool. So we can see all of our checkpoints by going to this source control option in cursor or VS code. (t: 1840) And in here, we can see this initial commit that was created by Claude. And it gives us a nice little summary of everything that was done up to this point. (t: 1850) So if we somehow mess up the application, we can always ask Claude to roll back to that commit. As an example, let's say, please change the branding of the app to Taskflow 2.0. Let's send this. (t: 1870) All right, so Claude changed the name of the app to Taskflow 2.0. And we can see those changes all throughout the app. Now, what if we realized that was actually a bad idea? Well, all we have to do is say, please roll back to the previous commit. (t: 1880) Let's send this. This is going to run git reset. And then we can see that this is a good thing. (t: 1890) So we can see we've changed the name of the app to Taskflow 2.0. And if we go back to our app, we can see that we've changed the name of our app to Taskflow 2.0. So if we go back to our app, we can see that we've changed the name of our app to Taskflow 2.0. Cool. So that's how you can roll back to any previous commit or checkpoint. So let's say we want to add a new feature to our app. (t: 1900) And we can see that we've changed the name of our app to Taskflow 2.0. Now, one thing that's missing in our app is our AI functionality. When I add a to do, I want an LLM to categorize that to do item. So let's say I'm just going to clear this chat. (t: 1910) And I do recommend starting a new conversation whenever you start with a new feature. A clean context will always produce better results. Please add some AI logic that will categorize to do items as they're being created. (t: 1920) All right. So let's try adding a new to do like go to gym. Let's add this. And this is actually use GPT to create this item and categorize it under health. (t: 1930) If you wanted to add advanced functionality to this application, you can do that by clicking on the button. (t: 1940) Perhaps like the ability to search through to do's. Or maybe you wanted to integrate a chat interface with an AI model. You can do that by going to docs. (t: 1950) And under technical, I've added some documentation that the agent can reference to implement some advanced functionality. And I will be adding additional documentation to the repo. (t: 1960) For instance, let's say we wanted a chat interface with the AI model. You can simply grab this streaming document, add it to the chat, (t: 1970) and say something like, please add a chat interface with an AI model so that I can ask questions about my to do's, etc. (t: 1980) But I'm actually happy with this application as it currently stands. So let's have a look at deploying this app to production. Now we will be using the terminal for this. (t: 1990) So I'm actually going to stop this development server. Now the first thing we need to do is to ensure that all of our latest changes have been committed. So either you can add a new to do, (t: 2000) or you can just use the source control window. Or of course you can just ask Claude to create a checkpoint for you. Now that we've committed all of our changes, we can open up our terminal. (t: 2010) Hey Leon from the future here. While editing the video, I realized that I totally forgot to mention one more dependency. In order for the Vercel deployment to work, we have to install one dependency using the terminal. (t: 2020) Simply run npm i-g vercel. Simply press enter. (t: 2030) And you can then use Vercel to deploy your app to production. Back to the video. And then what we need to enter is vercel dash dash prod. (t: 2040) Then let's run this. Then it's asking us if we want to set up and deploy. And then let's say yes. Link to existing project. (t: 2050) Let's say no, as we don't have this project in Vercel yet. And for the project's name, I'm just going to use the default name. And I'll just press enter. (t: 2060) And enter again. And that is actually it. The project is now being deployed and built in production. And we will be able to access it through a public URL in a minute. Alright, so we're getting this message saying that the build has completed. (t: 2070) And it's done. So what we can do is go back to Vercel. And then search for our project. Which we called to do app. And on this page, you'll see this public domain that you can use to access your app from anywhere in the world. (t: 2080) Now this won't work yet. What we have to do is go to the Vercel application. And then we'll go to the Vercel application. And then we'll go to the Vercel application. And then we'll go to the Vercel application. (t: 2090) And this won't work yet. What we have to do is go to settings. Then go to environment variables. And in here, we have to copy across all the environment variables from our project. So all I'll do is select everything and copy. (t: 2100) Then simply press control and V to paste. Then simply press control and V to paste. And now we can see our environment variables. Let's Save. (t: 2110) This will ask us to redeploy the project. So let's actually do that. And let's click Redeploy. And I'll simply go back to overview. and then go to the deployment tab and here we can see the project is currently building. (t: 2120) We'll just wait for this to complete. Cool, our project was deployed. So let's go back to the overview page. Let's open up our app and let's see if everything is working. (t: 2130) When we click on sign in with Google, I do want to warn you that this is not going to work and I'll explain why. What we have to do is copy this URL and then add it to our list of redirect URIs. (t: 2140) So back in our Google Cloud platform, let's click on our client, (t: 2150) then under authorized redirect URIs, paste in that URL and let's also include API and auth. So all of this stuff at the end. (t: 2160) Let's save this and these changes could take a minute or two to take effect. And actually come to think of it, let's go to settings, then under environment variables, look for the variable called next public app URL (t: 2170) and click on edit. And change this from localhost to your app's URL. Then let's save these changes. (t: 2180) Let's redeploy and let's go to view deployments. And let's wait for this build to complete. All right, we're done deploying. So let's refresh our app. (t: 2190) Then let's click on sign in with Google. And afterwards, we can now see our to do items. And let's actually try to add one more. So let's say buy dog food. (t: 2200) Let's add this. And indeed, the item was added. If we refresh this, the data is persisted in the database. (t: 2210) Now, of course, for a real application, you don't want to use this generated URL. The nice thing about Vercel is you can assign a custom domain as well. Simply go to settings, (t: 2220) then go to domains. And by default, we can access the app using this generated URL. But you can also buy a domain from Vercel directly. (t: 2230) Or if you're using another domain provider, simply click on the domain, enter the name, and then you'll be able to access the domain. And Vercel will give you the exact instructions for connecting that domain to your application. (t: 2240) If you assign a custom domain, just remember to add that domain to your list of authorized redirect URIs, and go to environment variables and change this next public app URL variable to your custom domain. (t: 2250) And everything should work perfectly. (t: 2260) This boilerplate and this video was a lot of effort to create. So if it helped you in any way, then please consider hitting the like button and subscribing to my channel. Also give this repo a star. (t: 2270) And thank you so much for watching this video until the end. I look forward to building some exciting projects using this boilerplate going forward. If you want to watch more of my videos, (t: 2280) then click on the card on the screen right now. Otherwise, I'll see you in the next one. Bye bye.

