---
title: 31 <PERSON> tips you NEED to know (learn shortcuts,  rules, memories, prompts, commands & hooks!)
artist: "Morning Maker Show with <PERSON> and <PERSON> "
date: 2025-08-11
url: https://www.youtube.com/watch?v=Z8b6h8cNx-Y
---

(t: 0) If you're not ready for a video that is fully packed with gems that'll 10x your productivity, you might as well save this for later, guys. This video is going to take you from a cloud code noob (t: 10) to Sam <PERSON>man levels of expertise. Without the tips that I'm going to show you, you'll likely be more productive in Cursor, Windsurf, and dare I say, even in GitHub Copilot. (t: 20) After shipping many apps to production with both Cursor and cloud code, I found out that cloud code is quite low level and there are many secrets you need to unlock to turn cloud code (t: 30) from pretty good to mind-blowing. Let's do this. I'm going to start you off easy with some keyboard shortcuts. So cloud code has a number of input modes. You can shift tab to cycle through them. (t: 40) If you shift tab once, you're going to go into auto-accept edit mode, which will just write files without asking for permission. If you shift tab twice, you're going to go into plan mode, (t: 50) which won't make any code changes but will research the web and come up with an action plan. And if you shift tab a third time, you will get back to the default edit mode, (t: 60) which will require your approval to do file changes. You can install cloud code as an extension to your IDE. In Cursor, Windsurf, or any other VS Code-based editor, you can just open up the integrated terminal and run cloud to set it up. (t: 70) And in JetBrains IDEs, you can install the cloud code extension. Run the slash terminal setup command so you configure the shift plus enter key binding so (t: 80) you can write, beautiful multi-line prompts like these. Run the slash IDE command to connect to Cursor, VS Code, or your JetBrains IDE. (t: 90) With the IDE connected, any lines you select will be automatically added to the cloud context. I also recommend that to go to slash config and then set auto connect to IDE to true. (t: 100) By the way, you'll probably find yourself opening and closing cloud a lot to do this configuration. You can just hit command escape to open it. Cloud code also supports images, (t: 110) so you can either, drag and drop or copy and paste an image directly into the prompt. To get detailed reports on your usage, you can use the npx cc usage command which will (t: 120) show you a detailed breakdown of your input and output tokens. In under one week, I have spent 149 bucks so I definitely recommend that you get the (t: 130) Claude Max subscription. By the way, if you think these slash commands are cool, it's very easy to add your own. You just need to add the file in .claud slash commands. (t: 140) As you can see here, I added aw.md and it appears in my commands now. Alright, basic commands done, let's just get into some prompting tips now. (t: 150) To get Claude Code to allocate some thinking budget, you can just prompt it to think. If you want it to allocate more thinking budget, you can tell it to think harder. (t: 160) And if you want to use the maximum thinking budget, you can tell it to ultra think. Another thing you can prompt it is to use sub-agents. Claude Code will then reason about the prompt and spin up the (t: 170) appropriate number of commands. So you can tell it to run the task in a loop. Thinking will appear in the terminal as italic gray text. And sub-agents will appear in the terminal as a task. (t: 180) You can also tell Claude Code to run things in a loop and perform certain actions at the end of it. For example, here I tell it to run the build in a loop and fix all of the errors as they get reported. (t: 190) After such a long task, I recommend that you run the slash clear command. So we start with a fresh context. If you accidentally close your browser or you have a power outage or anything like that, (t: 200) don't worry. You can just type the slash resume command that will show you all of your past sessions. You can just select any of them, press enter, and then you'll be back where you left off. (t: 210) If you want to write a large prompt with lots of formatting and code samples, you are better off hitting control plus N, typing everything in there, (t: 220) selecting it, copying it, and pasting it back into the prompt window. It'll collapse into one line that it's a lot more manageable to work with. All right, that was a lot, but now we're getting into the real advanced stuff. (t: 230) But first, coffee. (t: 234) Let's do this. You gotta use the Taskmaster AI MCP. (t: 240) This is the absolute best way to reduce the errors in your project. Taskmaster AI will break the work into manageable chunks, analyze complexity and dependencies between tasks, (t: 250) and help you split the work into subtasks so Cloud Code has a better chance at implementing it. If you want to see how to set that up, I have an entire video on it. (t: 260) The next one is also a must-have. The PlayWrite MCP gives Cloud Code the capability to open up a browser and click around or check for console errors. (t: 270) Just imagine the kind of workflows this unlocks. Another one that I definitely recommend is the Context 7 MCP. This MCP is an absolute lifesaver if you just upgraded to a new version of a framework or (t: 280) library and the AI keeps insisting and writing things the old way. Here's a pro tip that I heard from the Cloud team itself. (t: 290) Use another MCP to set up a new workflow. Use another MCP to set up a new workflow. Use another MCP to set up a new workflow. Use another MCP to set up a new workflow. Use another MCP to set up a new workflow. Use another MCP to set up a new workflow. Use another MCP to set up a new workflow. Use another MCP to set up a new workflow. Use another MCP to set up a new workflow. Use another MCP to set up a new workflow. Use another MCP to set up a new workflow. For example, I'm using the Code Rabbit extension to review the changes here. It's pointing out that the Context 7 documentation that Cloud Code generated (t: 300) is actually not that good. But speaking of documentation, we haven't created any rules for this project yet. (t: 310) If you're using Cloud in an existing project, the best way to get started is with the slash init command. This command will generate an entire Cloud.md file with the conventions (t: 320) found in your codebase. If you want to add a new rule or memory as they call it, you can just type hashtag and then explain the rule you want to add. By the way, you can nest Cloud.md files anywhere in your project to keep rules contextual to a (t: 330) directory. Another pro tip is that you can add files in your Cloud.md file to reference them. (t: 340) This is a really cool way to, for example, avoid duplicating rules between Cursor and Cloud Code. But speaking of rules, here are a few that every project should have. (t: 350) Add and commit all the files that you want to add to your project. Add and commit all the files that you want to add to your project. Add and commit all the files that you want to add to your project. Add and commit all the files that you want to add to your project. Add and commit all the files that you want to add to your project. Add and commit all the files that you want to add to your project. Add and commit all the files that you want to add to your project. Use descriptive commit messages and capture the full scope of changes. Remember, Cloud Code doesn't have checkpoints, (t: 360) so we need to use Git to be able to roll back when a change breaks something. Another rule that will probably save you hundreds of hours of work is to tell (t: 370) Cloud Code to run the id diagnostics after it completes a task. This will run both typechecks and your linter and whatever else you have set up in your project (t: 380) in one go. It uses fewer timeframes because it needs to run a number of times. tokens and it's super fast. Another rule that you saw in action just moments ago is to use the context 7 mcp server whenever the user requests some documentation. Another super important rule (t: 390) is to document your tech stack and dependencies. I've caught Cloud Code trying to install a library that I already have a number of times now. This is going to save you a lot of time and tokens. (t: 400) Another great one to have is the project structure rule. This will help Cloud Code understand your project better, find things faster, and also avoid duplicating modules that you already have. (t: 410) Okay, one more and then I'll show you some really really cool stuff. The self-improve rule. (t: 420) Essentially, this tells Cloud Code to create its own rules whenever it detects repeating patterns or bugs that could be prevented by a rule. Okay, you've stayed with me for a while and you shall (t: 430) be rewarded. Here's the thing that I absolutely missed the most from Cursor. That is the freaking bell sound whenever it's done doing something. But don't worry, I got you. Here's how you set up the same in Cloud Code. (t: 440) Cloud Code has this concept called hooks which is a way to run deterministic commands at certain (t: 450) stages. So to set up a sound whenever Cloud Code completes a task, we can use the af play command in the stop hook. You can also go completely nuts and play a sound after each task finishes. (t: 460) You do that by setting up the post-tool use hook. But I have an even better command for that hook. (t: 470) And that is to run a code formatter whenever Cloud Code writes a file. And let me tell you, there's nothing more annoying than to get a 200 line diff for (t: 480) one line change in a file that Cloud Code didn't format correctly. This hook fixes that. You've made it. This is the last tip for today. And this is a clever workaround for the current (t: 490) generation of LLMs. This is the always works hook and it runs before each tool use. This tells Cloud Code to always test changes to the code before each change. So you can see that the code is always working. And that's why it's called the always works hook. And it runs before each tool use. This tells you to always test changes instead of assuming that they work and always verify that outputs (t: 500) match expectations. Adding this as a pre-tool use hook ensures that it doesn't get lost somewhere in the context and Cloud Code actually respects it always. All in all, Cloud Code hooks are (t: 510) super powerful. They allow you to run any script, but they also allow you to call webhooks. So, (t: 520) for example, you could send a message to yourself on Discord when something is complete. I hope you learned something today, but for now, this is all I have. So remember, like, comment, subscribe, and I'll see you in the next video.

