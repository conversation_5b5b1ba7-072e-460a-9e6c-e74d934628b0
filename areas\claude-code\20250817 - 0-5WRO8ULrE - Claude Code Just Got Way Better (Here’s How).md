---
title: <PERSON> Code Just Got Way Better (Here’s How)
artist: Better Stack
date: 2025-08-17
url: https://www.youtube.com/watch?v=0-5WRO8ULrE
---

(t: 0) Cloud Code is quickly becoming the go-to AI coding assistant for many developers and also non-developers, but it's had so many updates recently like custom status lines, background (t: 10) tasks and output styles that if you're not following <PERSON> or <PERSON> on social media, then there is a chance you might have missed it. So let's go through them. And before we do, make sure you hit that subscribe button. First up is creating a custom status line (t: 20) to add contextual information below the input prompt. Running just the status line command will try to recreate your terminal's prompt using the status line setup sub-agent. You then have to (t: 30) close and reopen Cloud Code to see the updated status line. And it also updates your settings.json (t: 40) file. This is a good start, but what's much better is to add a prompt after the status line command, making sure the status line config is in a separate file. Note the first line of the standard (t: 50) out becomes the status line text and the status line code receives this structured JSON data whenever the conversation message updates. And this is the result, if you've already added the status line to your command. You can also add a padding value, (t: 60) which will move the status line away from the edges. Next is background command, which lets you run any bash command in the background by either pressing control B, (t: 70) which then shows over here, or if you're lucky, Cloud Code will do it for you automatically. You can press the down arrow and enter to view the running background command, or you can run the bashes command. From here, we can view the standard out, (t: 80) and because Cloud can read it as well, this is useful for fixing issues or just asking questions. So if you use Bunn or have another way of sending browser logs, you can also add a background task to the terminal. This will save you a lot of time copying and pasting. (t: 90) And remember to press K to kill a background task, because if you quit the Cloud Code without doing that, the application will still run. And then we have output styles, (t: 100) which changes the way Cloud communicates with you. This can be changed with the output style command, where you can change from the default to explanatory, (t: 110) which adds an educational insights section, or learning, which also adds insights, but then adds these to do human comments in the code, which is a very useful way to keep your coding skills sharp. (t: 120) These basically edit Cloud's default system prompts, which means you could make Cloud worse at coding by replacing the default system prompt specific to coding, (t: 130) but you could make it better at doing other things like creative or non-fiction writing. And the documentation tells you why this is different from using the append system prompt flag, (t: 140) agents or using custom slash commands. But you can also add a custom output style with the output style new command, which uses the output style setup subagents, (t: 150) or you can just write a prompt asking Cloud to create a new output style, which adds a custom output style to the output style directory, meaning I can select it in Cloud code and now Cloud uses more emojis. (t: 160) And you can also just change the output style as a command argument. The next two updates are pretty small, so I'll go through them quite quickly, (t: 170) like using the permissions command to allow or deny specific tools, which you could always do in the settings. But there's also this new ask option for Cloud's commands, to always ask permission before running a tool, (t: 180) and workspaces, which allows you to manage your added directories. And the new Opus plan mode, which uses Opus for planning mode and Sonnet for everything else. (t: 190) So you can benefit from the superior reasoning of Opus 4.1 and the 1 million token context window of Cloud Sonnet 4. For a bonus tip, you can use the release notes command (t: 200) to have a look at all the new changes in your version of Cloud code. The team at Anthropic have added so many helpful features to Cloud code that I would have never thought to add myself. And I didn't even go through security reviews, (t: 210) PDF supports, and support for multiple MCPConfig files. Shout out to Boris, Cad, Sid, TheReek and everyone else for working so hard. (t: 220) I'm super excited to see what comes out next. Again, don't forget to subscribe for more AI news and until next time, happy coding.

