---
title: How To Use Claude <PERSON> (Beginner Tutorial)
artist: <PERSON>
date: 2025-08-04
url: https://www.youtube.com/watch?v=1JDVrQr2pPc
---

(t: 0) Now that you have Cloud Code set up, you might be tempted to start building your very first project. But hold on, there are a few important tips and tricks you need to know to get the best out of your Cloud Code experience. So in this video, we'll build a simple project, (t: 10) but you'll learn a workflow that works every single time, and you'll learn a lot of very cool stuff along the way. First, in order to start Cloud Code, you have to run the command (t: 20) cloud. But did you know you can start cloud in other ways as well? For instance, you can run it in YOLO mode by passing the flag dangerously skip permissions. By default, <PERSON> will ask (t: 30) your permission to run certain commands. But with this flag, it won't ask your permission at all. (t: 40) Now, I don't recommend doing this. And if you run this command, <PERSON> will actually give you this warning saying that Cloud is running in bypass permissions mode. And it's saying Cloud Code will not ask for your approval before running potentially dangerous commands. Now, (t: 50) if you know what you're doing, this might be fine. But I prefer to see what Cloud Code is doing and then stop it when it's going down the wrong path. (t: 60) So what I'll do is run Cloud normally. So here's another tip for you. At the moment, we're running Cloud Code in the terminal. But to give ourselves some more room to work with, (t: 70) we can grab this terminal and add it to the stop section. And now we have a lot more real estate. And of course, we can still pull up the terminal when we need it. I'm also going to rename this (t: 80) tab to Cloud Code. Cool. Let's say you wanted to enter something like, please set up the following. Now, (t: 90) you might be tempted to press enter or shift enter or control enter to add a new line. But irrespective of what you press, this command will be sent to Cloud, which is not ideal. We can (t: 100) add support for new lines by pressing front slash. This will bring up all the commands available to (t: 110) Cloud Code. In here, let's search for terminal setup. And this will install the shift and enter key binding. Now, if we hold shift and press enter, we can add as many new lines as we want. (t: 120) Now, let's have a look at setting up our basic project structure. You might be tempted at this stage to tell Cloud to go and build your application for you, and it will decide on (t: 130) the tech stack for you. I'm personally not a fan of that approach. I believe in giving it the tech stack that it needs to use. And for this tutorial, we will be using Next.js, which is an incredibly (t: 140) popular framework for building full stack applications. So let's go ahead and start building the next stack. Now, you don't have to know what Next.js is. Just know it's the underlying framework that Cloud Code (t: 150) will use to build our application. Now, if you're using Python or something else, you might want to use Django or another framework instead. So I'm going to ask Cloud to set up this Next.js project (t: 160) for us. So let's enter, please set up the following. And I'll press shift and enter to add a new line. (t: 170) And let's say set up the latest version. Next.js will set up a new CMD. This one is going to be (t: 180) or .xml bound for iotv. I'm going to hashtag that. Or I'm going to change it to center policy. (t: 190) And I'll put my png variable and the first line of that is going to be center log野. Of course also saying that you can use level or whatever you like. But if you are using a full stack, I thought you could press issues, (t: 200) but most are going to be different. There are also some nice ones out there. code to use the defaults for all those questions. Now you don't have to specify this. Cloud Code (t: 210) will figure it out itself. But I found that this does save on some token usage. And let's add a database. So let's say set up a SQLite database. Now we're adding a database because typically in (t: 220) an application, you would want to add and retrieve data. And you don't want to lose all of that data (t: 230) every time you restart your server. So we can persist that data by using a database. Now in order for Next.js to communicate with the database, we need to add an ORM. So let's say set up Drizzle (t: 240) ORM for connecting Next.js to our database. And that should be it. Let's press enter to send this (t: 250) command. So this is another core feature of Cloud Code. For complex tasks, it will create this to-do (t: 260) list. And you can see exactly what it does. So let's say I want to add a database to my database. And I can see exactly which step it's currently on. And because we're not running Cloud Code in YOLO mode, it will stop to ask our permission to run certain commands. So now we can either say yes (t: 270) to approve this command, or we can say yes and approve all similar commands going forward. Or we can stop the process and tell Cloud Code what to do different. I'm simply going to say yes. And I'll (t: 280) just keep selecting yes for all of these commands. And our cursor is asking our permission to edit (t: 290) this file. So I'll just say yes. In fact, I'll just select the second option. And by selecting that second option, the mode was changed to auto accept. But we will have a look at the modes in a second. (t: 300) And cool, it seems like everything was indeed installed. Now in order to see if everything was set up correctly, I would like to run the dev server. Now of course, we could open up the (t: 310) terminal and run it ourselves. For instance, we could call npm run dev. And that will give us this URL. And if we open that in the browser, we can see this box. And we can see that we have a (t: 320) boilerplate Next.js project. Now I don't want to run the dev server myself. I actually want Cloud Code to do it every time it makes a major change. So how can I get Cloud Code to remember (t: 330) this rule for all changes, even if we started a new conversation? Now that takes me to memory in Cloud Code. We can use memory to give Cloud Code instructions that it needs to follow for (t: 340) all future requests. In order to add memory, we can create a new file in the root of our (t: 350) project called cloud.nd. And now in this file, we can add any rules we want. So for instance, let's use a silly example like always respond like a pirate. If I save this, we can go back to Cloud (t: 360) Code. And in order to make those changes take effect, we can start a new conversation by entering (t: 370) slash clear. And now if I send something like hello, Cloud Code will respond like a pirate. (t: 380) So that's not what you want to use this file for. Instead, we want to add very specific rules related to our project. So I could add a new section. And let's just call this something like (t: 390) post change instructions. And then let's add a new line and say always start a dev server after (t: 400) completing your changes. Now we could manually add more rules if we want, but we can also add rules in the chat itself. So we can enter like a pirate, or we can say, oh, I'm going to add a (t: 410) Enter a hashtag and now we can add memory. So let's say something like always start the dev server on port 3000. (t: 420) Use npx kill if the port is already in use. And let's press enter. (t: 430) NAT will ask us if we want to add this instruction to project level or to user level. At project level, this rule will only affect this current project. At user level, that rule will be applied to all of your projects. (t: 440) I'll simply select project memory. And if we go back to our file, we can indeed see that rule was added. (t: 450) Now I'm going to add a few more rules to this file. In fact, before running the dev server, I want Cloud Code to run the build command to check for any syntax errors. (t: 460) So I'm going to say always run the next js build command after making any changes. Actually, I'm going to take this. Step further and say always run the DB generate migrate and next js build commands after making any changes. (t: 480) So this will just cater for the instance where database changes were made as well. All right, that should be good enough. So I'm just going to start a new conversation to let those changes take effect. (t: 490) Hey, Leon from the future here. During editing, I realized that I completely forgot another way in which you can create the code. So I'm going to go ahead and create a new code. (t: 500) Of course, you can create the file manually and then add memories using the hashtag. But you can also get Cloud Code to generate the file for you. All you have to do is enter front slash, then init. (t: 510) And Cloud Code will automatically scan your project and then create the MD file for you. Now, if you already have your own rule set up, what Cloud will do is analyze your code base, (t: 520) generate a Cloud MD file, and then merge your rules into its rules. So you shouldn't lose any. Of these changes. (t: 530) All right, now the code is done. We can switch back to the Cloud MD file. And now you can see that Cloud created a very detailed file containing all of the scripts, how to migrate database changes, the architecture of the project, and a lot, lot more. (t: 540) But at the bottom of the file, we can see our own custom rules as well. (t: 550) So back to the video. And in fact, let's actually start building our budget tracking application. Let's say please build a. Super simple budget tracking application and ensure to use real data from the database. (t: 560) Let's send this. (t: 570) All right. So it's asking to run the database generation. Let's run this. And I've wants to run the migration step. So let's run that as well. And I'm actually going to press escape to stop this process. (t: 580) And escape is your best friend when it comes to Cloud Code. If you don't understand what it's doing, then rather. Hit escape. And you can always continue the process if you feel comfortable with the changes. (t: 590) So the reason I press escape is I noticed this comment. It says it's going to see data to the database and then create a simple script to populate it with sample categories and transactions. (t: 600) Now, I don't want Cloud Code injecting some rubbish data into my database. (t: 610) So I've pressed escape to stop the process. And now I can course correct it by saying, please do not see the database. And now I can see the data. (t: 620) So it's going to see the data. And I'm going to run this. And now Cloud Code will course correct. So it says, okay, understood. I'll skip the database seeding and focus on building the budget tracking app. (t: 630) That's exactly what I wanted. And now it's just asking my permission to remove the seed data file. And I'll just select option two. And that's really why I'm not a fan of YOLO mode. (t: 640) You really want to keep an eye on what Cloud Code is doing so that you can stop it and make changes. And here we can see Cloud Code is now running the budget tracking app. And I'm going to press escape. And that is because it picked up that we gave that instruction in the Cloud.nd file. (t: 650) And luckily we asked it to do that because it actually picked up a syntax error and is now making that fix. (t: 660) Now it's running the bold command again. And this time everything is working. And now it's trying to start the dev server on port 3000. (t: 670) But it realized this port is already in use. So it's going to kill our existing session. And it should start the dev server, which it did. Now you might be wondering. Why I'm starting the dev server every single time. (t: 680) Yes, when you make changes, those changes will automatically be detected by Next.js. And it should show up on the front end. (t: 690) But if you've ever worked with Next.js, you do know that sometimes some major change will simply kill the dev server. And you have to manually restart it anyway. So with that said, let's refresh our app. (t: 700) And there we go. We now have our budget tracking app. Let's try to add a transaction. And it's very hard to read this text. So we will fix that in a second. (t: 710) But I'll just enter some title like salary. And for the amount, I'll just enter something. And for the type, let's select income. (t: 720) And that should be it. Let's add this transaction. And we can see that transaction in this list. Let's try to add an expense. So let's just say food. Let's enter some amount. (t: 730) For the type, we'll leave it on expense. And let's add this transaction. And cool. So we can see our income. And expense in this list. Let's see if the database works. (t: 740) So let's refresh this app. And these values are still there. Cool. Now let's fix the issues with this app. On this input form, it's very hard to read this text. (t: 750) So back in Cloud Code, let's say, please assist in fixing the following issues. The form text is very hard to read. (t: 760) Because the text is too light on the light background. So let's... Let's send this. All right. It's made a few changes. (t: 770) And if we go back to the app, that looks way better. Also, I noticed we get this error at the bottom. And if we open this, Next.js is giving us this warning or error. (t: 780) To fix this, all we have to do is copy this log and then paste it into Cloud Code. And you can paste it just by right-clicking on this field. (t: 790) Let's send this. All right. It's made some changes. And if we refresh the app, we now get this error message. And that is why I prefer to refresh the dev server every now and again. (t: 800) And it seems that for the small change, Cloud Code decided not to restart the dev server. So let's tell it, please restart the dev server. All right. It's going to kill the existing one. (t: 810) And now it's started the dev server. So let's refresh this. And there we go. And we don't have any error messages anymore. Now we are going to improve this app quite a bit. (t: 820) But since the basic functionality is now working, this would be a very good time to create a commit or a checkpoint. A checkpoint simply means we can store the current state of this application. (t: 830) So if we make mistakes going forward, we can always roll back to this current checkpoint. Thankfully, we can get Cloud Code to do that for us. (t: 840) All we have to do is say, please create a commit for these changes. And let's send this. (t: 850) And I'll just keep approving these changes. And cool. That should be it. We can see all of those checkpoints. By going to this version control option. (t: 860) And at the bottom, we can see the commit that Cloud Code just made. Along with a lot of detailed information about the work that was done. Cool. So in fact, let me show you how to roll back if we manage to break something. (t: 870) Let's say change the theme to red. Just as a very simple example. So it says it's going to change the theme from blue to red. (t: 880) In fact, the color change might be too subtle. Let's just say, please. Change the brand to my tracking app. (t: 890) Okay, cool. So it's made this change. The app is now called my tracking app. And it's changed some of the elements to red. Now, what do we do if we actually want to roll back these changes? (t: 900) Well, since we've created the checkpoint, all we have to do is say something like, please revert back to the previous commit. (t: 910) And I was asking our permission to reset to this previous commit. So let's just approve this. And of course, we can see that we've made this change. Of course, it forgot to start the dev server. Please restart the dev server. (t: 920) We could always adjust the prompt in the cloud file to make it really strict to always start the dev server. But this will happen from time to time. (t: 930) All right, let's refresh this. And there we go. Everything is back to the way it was. All right. So this conversation is starting to get very long. And I highly recommend using small context windows. (t: 940) The longer a conversation gets, the more muddier it gets as well. And the quality of the responses will start to go down. (t: 950) In order to start a new conversation, we can use the command front slash clear. This will clear the conversation history and free up the context. (t: 960) Now, I know sometimes that's not what you want. You might be in the middle of a very complex problem and you can't lose this context. So what you can do then is run compact. (t: 970) This will first create a summary of the conversation history and then start a new conversation. Freeing up some of that context. If the conversation history is quite complex and you really only want certain information to be recalled, (t: 980) you could optionally add additional instructions like only summarize the stuff about changing the brand. (t: 990) And if you run this, Cloud Code will now summarize the conversation history and start a new conversation. (t: 1000) So if you wanted to see the summary that it created for you, you can press control and R. Now you can see the complete summary. Or you can press control and R to hide that view again. (t: 1010) Now let's have a look at a few more useful commands. We can exit out of Cloud Code by pressing front slash exit. (t: 1020) Or if we want to resume a conversation, we can simply type in front slash resume. And if we press this, we can see all our previous conversations. (t: 1030) So let's select the last one we created, which was the conversation that contained this summary. But again, this is just a summary. So again, I recommend after completing a feature, rather start a new conversation with clean context. (t: 1040) And while we're on the topic of discussing commands, I do want to introduce you to the concept of custom slash commands. (t: 1050) So in Cloud Code, when you press front slash, you can access all of these default commands, but you can also create your own. For example, at the start of the project, (t: 1060) we asked Cloud Code to set up things like Next.js and our database. Now, maybe we don't want to manually, type all of that stuff out every time we start a new project. (t: 1070) It would be ideal if we had some command that will instantly start a new project for us. So to create those custom slash commands, what you have to do is within the Cloud folder, (t: 1080) create a new sub folder called commands. And then within this folder, we can create a file for each new command that we want to add. (t: 1090) Like let's add a file called new Next.js project dot md. Then on the first line, let's enter hash and the name of this command, (t: 1100) like start Next.js project. And below that, we can provide a short description of this command, like start a new Next.js project with a SQLite database and drizzle ORM. (t: 1110) And now we can add something like commands to run in sequence. (t: 1120) And honestly, you can add anything to this file. It's simple. It's just basically a prompt that will run when you select that command. (t: 1130) So we could do something like set up Next.js in the current folder and set up a SQLite database (t: 1140) and set up a drizzle ORM connection between Next.js and the database. Something like that. (t: 1150) So back in Cloud Code, I'm just going to clear this. And actually, I think you have to exit out of Cloud Code. And then let's run Cloud. And now when we press front slash, we can see our custom command in this list. (t: 1160) So if we run this, it will inject that prompt into Cloud Code. But I'm going to stop it as we've already set up our project. (t: 1170) But just know if there's a prompt that you keep reusing, you can store that template in one of these command files and make it available through the slash commands. (t: 1180) All right, so let's continue with our project. In Cloud Code, we can change the current mode, by pressing shift and tab. By default, this mode will ask your permission to run any commands. (t: 1190) If we want to auto enable changes, we can press shift and tab, and this will go into auto accept mode. (t: 1200) This means Cloud Code will make changes to files without asking your permission first. But what you can also do is press shift and tab again, (t: 1210) and this will take you to planning mode. In planning mode, Cloud Code won't make any changes, to your project. This is ideal for planning the changes in advance, (t: 1220) before implementing those changes into your project. For instance, let's say we wanted to create a product requirements document, before making further changes. (t: 1230) Now, just a little pro tip. Cloud Code does have very strict usage limits at the moment. So you really don't want to waste your tokens on something like this. (t: 1240) Instead, use something like ChatGPT or Cloud to come up with these plans. So we could say, please create a PRD for a super simple budget tracking app. (t: 1250) It will only be used by one user. So no need for auth at this stage. (t: 1260) And let's send this. Creating a product requirements document can greatly improve the output of Cloud Code. Because if we simply ask Cloud Code to go and build an app for us, (t: 1270) it will kind of use its own initiative to figure out what features should be available, in the app. But sometimes just having this little bit of planning can make a massive difference. (t: 1280) So of course, we can have a look at what Cloud Code produced, and have a back and forth to refine this application. So it's giving us things like the target market, (t: 1290) success factors, core features, and user stories, etc. Okay, then I'm going to copy this document, and back in our project, let's create a new folder, (t: 1300) and let's call it Docs. And within the Docs folder, we'll create PRD.md and paste in our product requirement document. (t: 1310) Cool. Because the PRD doesn't contain any technical information, it was the perfect candidate for something that we could use Cloud or ChatGPT for. (t: 1320) But now that we have the PRD in our project, we can come up with a technical implementation plan that looks at the PRD and our current tech stack, and then comes up with a technical implementation plan. (t: 1330) That is what the Cloud Code planning agent is perfect for. So in planning mode, let's say, please assist with a technical implementation plan based on the PRD in the Docs folder. (t: 1340) And here's a really cool tip for using Cloud Code. (t: 1350) When it comes to planning, you can actually improve the reasoning capabilities of the agent to really carefully think about this plan. To force it into thinking mode, you can add keywords like think deeply or think (t: 1360) hard or harder. So let's do something like think deeply on this solution. (t: 1370) Let's send this. You will know that you've triggered the reasoning mode if you see this thinking step over here. Now, thinking will take slightly longer and consume more tokens, (t: 1380) but it does provide better responses. OK, cool. So Cloud Code came up with this implementation plan and now it's asking (t: 1390) if it can go ahead and make the changes or we can continue planning. Now, I don't want it to make changes yet. So I'm going to select option three and then I'm going to swap back to auto accept (t: 1400) mode using shift and tab and I'm going to say store this plan in the Docs folder. So this way other team members can also see the implementation plan. (t: 1410) And of course, I can always refer Cloud Code back to that implementation plan during any part of this build. And cool. (t: 1420) We now have this technical implementation plan file. Right. Now, as I mentioned earlier, it's always a good idea to start a new conversation to free up the context window. (t: 1430) And now let's say, please improve our app by implementing the changes in the technical implementation plan document. (t: 1440) Now, what you can also do is directly reference a file by typing the at sign, then let's go to Docs and within the Docs (t: 1450) folder, let's select our technical implementation plan. Now, Cloud Code should be able to find that file by itself. But by giving it direct access to the file, we do save on a lot of token usage. (t: 1460) All right. So I'm just going to press escape for a second and have a look at this to do list. So we can see it wants to install a few dependencies and then it wants to update (t: 1470) the database schema with all of our new tables. And again, it wants to see the database with predefined categories. (t: 1480) And I think for the categories, that's OK. I don't want it injecting transactions, though. So I'll just tell it to continue. All right, cool. So the build completed. (t: 1490) And I do want to mention a couple of things. The build command was an absolute game changer for this because Cloud Code would make changes, it will then run the build command to check for any syntax (t: 1500) errors and then proactively pick up issues and then fix them. Either way, we can now go back to our app and this is way more feature complete than what we had before. (t: 1510) We now have these graphs. And of course, we can still do this. We can see our recent transactions. And now we have other features like budgets and analytics. (t: 1520) So if we click on budgets, I assume we can add a budget. And under analytics, we can view these graphs. So that's all thanks to the PRD that we created and the implementation plan that Cloud Code came up with. (t: 1530) These changes took a very long time to complete. So ideally, I want some audio cue to tell me these changes have been completed. (t: 1540) For example, if you ever used Cursor, in Cursor settings, there's this option to play a sound whenever the changes are completed, we can do something similar in Cloud Code by using hooks. (t: 1550) In fact, before we look at hooks, let me just create a commit of these changes. Please create a commit for these changes. (t: 1560) OK, cool. So we now have a new checkpoint that we can roll back to if we break something. Now let's talk about hooks. (t: 1570) What we can do is add custom hooks to Cloud Code. We can manage hooks by entering front slash and we can go to hooks. Now, hooks are basically events that happen within Cloud Code. (t: 1580) For example, after Cloud Code made its changes, we can hook some custom process into that completion event. (t: 1590) Now, hooks are extremely powerful tools that allow you to do some complicated things, but all I want to do is once the changes have been completed, I want to play some sound. (t: 1600) So in this list of hook events, we have events like before tool execution, after tool execution, when notifications are sent. (t: 1610) And what I want is this stop event. This gets triggered right before Cloud Code concludes its response. In other words, after it's made its changes. (t: 1620) Let's select this event and now we can add a new hook. And now we have to provide a shell command that needs to be executed whenever this event triggers. So what we can do is download a sound effect and I'll link to this page in the (t: 1630) description of this video. And let's say whenever Cloud Code completes, I want this sound to play. (t: 1640) All right, I'm going to download this file and then add it to some folder on my machine. I'm just going to rename this file to something like Bell and that's it. (t: 1650) And now all we have to do is create that shell command to play this sound. So to be honest, I simply use Cloud to generate the script for me. So we can just say I need a shell command to play (t: 1660) a sound file on my machine without opening a media player. (t: 1670) The file can be found in ccloudcodebell.wav. I'm using a Windows OS. (t: 1680) Now, this command will be different depending on your operating system. So you can use a similar prompt to this. Simply change the location of that file and the operating system that you're using. (t: 1690) So if I run this. Let's copy this command and back in Cloud Code. Let's paste that in and let's press enter. (t: 1700) I'll set this at project level and that's it. If we want, we can add more hooks, but I think that's all we need for now. So I'll just press escape to go back and let's test it out. (t: 1710) I'll just say hello and let's see if the sound plays. I'm not sure if you heard that on the recording, but I did indeed hear that sound. (t: 1720) So whenever Cloud Code completes its changes, I now have some audio cue to listen out for. Finally, let's also have a look at processing images in Cloud Code. I'm just going to start a new conversation. (t: 1730) Let's say we wanted our app to have the same look and feel of this website over here. So what I'll do is grab a snapshot of this page and then save it somewhere on my machine. (t: 1740) Then back in Cloud Code, I'm going to drag and drop this image onto this chat window. But I'm going to hold shift and then release the mouse button. (t: 1750) This will add that image to the chat window. And then I'm going to drag and drop this image to Cloud Code. Now, let's say something like I would like our app to use the same color scheme (t: 1760) as the theme in this snapshot. Please, could you make these changes and let's send this. (t: 1770) So I'll just approve access to that image file. And I believe on Mac and Linux, you can also paste in the file using command V or control V. But that doesn't seem to work on Windows, unfortunately. (t: 1780) All right. So it seems like Cloud made those changes. And look at that. Our website now matches the theme of that template. (t: 1790) So did you learn something? If you did, consider hitting the like button and subscribing to my channel for more Cloud Code content. Also, let me know in the comments if I missed your favorite tip or secret. (t: 1800) Check out my other content by clicking on the card on the screen right now. Otherwise, I'll see you in the next one. Bye bye.

