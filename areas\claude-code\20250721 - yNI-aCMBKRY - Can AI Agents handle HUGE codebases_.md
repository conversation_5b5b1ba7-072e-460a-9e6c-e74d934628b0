---
title: Can AI Agents handle HUGE codebases?
artist: Augment Code
date: 2025-07-21
url: https://www.youtube.com/watch?v=yNI-aCMBKRY
---

(t: 0) It seems like every week one of the big tech companies is releasing their own AI coding agent and they're all promising to revolutionize how we build software. But how much of that is actually (t: 10) true and how much of that is hype? So introducing this series, the AI coding agent showdown. The first scenario we're testing for is understanding in large code bases. This really tests the AI (t: 20) agent's ability to gather context effectively. I'm trying to keep this as transparent as possible, so I've chosen an open source code base. This is Forum. This code base is huge. It powers Dev2. (t: 30) It's over 48 million tokens, over 1 million lines of code. To keep it fair, we're trying these agents (t: 40) in their default setting out of the box without any guidelines, MCPs, or anything else configured. We're giving the exact same prompt to every single tool and we're not giving any more additional (t: 50) context. All the context gathering must be done by the agent itself. I've recorded these on other days. So you might see me wearing a different shirt, holding a different mic, but everything will be (t: 60) as little edited as possible. And if you have any other suggestions on how to run these challenges, please let me know. Let's try to keep this as transparent and reproducible as possible. (t: 70) First up, of course, we have Augment Code and then Cursor and then WinServe and then Cloud Code. These are some of the most popular tools amongst the communities I've been engaged with. (t: 80) All right, so I'm in Augment here, just in VS Code. Now Augment, just to keep it fair, I've killed it. I've kept it all on the default settings. There's lots of cool integrations and MCPs we could have added, (t: 90) but I've turned them all off. Also, I've removed all my user guidelines and also there's no memories. (t: 100) So it's a fair comparison as if we just installed these and went straight to the prompt. I'm going to copy this exact prompt. Pretty much, I've said that there's already like this hover preview for the author. (t: 110) When we put our mouse over the author, it shows us the author. It shows like a little preview about the author. We want to have a very similar thing for each of the articles. (t: 120) If we hover on the title of an article, it should show a similar preview. So let's just kick that off. And yeah, this is the crucial stage of Augment doing its context gathering with its context engine. (t: 130) For each part, it will tell you what it's trying to find. Okay, we can see after about three context calls, it went to create the plan. (t: 140) So yeah, Augment has this really nice user interface. Where you can see each task and its progress on each one. (t: 150) You can also add new tasks, delete tasks and kind of manage the plan on the fly with this task list user interface. One cool thing about Augment is by default, it always tries to test all the functionalities added. (t: 160) Which makes it more effective and reliable in being able to kind of one-shot your prompts. (t: 170) Now, if you don't like this, you could always include it in your guidelines to not test the code. But I think it's really useful and effective. Okay. So it's done all the functionality and now it's just going to test. (t: 180) And yeah, it's pretty nice not having to constantly prompt it telling it, okay, now fix. Okay, I approve the plan. It's actually pretty effective at getting a lot of work done in just one message and only charging you one credit for that. (t: 190) Whereas other tools, they might charge you per tool call. You can also see Augment has used the browser tool to kind of look how things are running in the browser, which is really effective. (t: 200) This is one of its inbuilt tools that come with it default. And it's also actually able to find some. Issues here and it's able to debug that on the fly. (t: 210) So it kind of gives you a more correct solution before giving up and telling you it's finished. Great. And upon completion, it gives you a nice summary here telling you what features were implemented. (t: 220) And anything else, technical details, it seems. And testing results, which are really important, tells you what it tested. (t: 230) So, yeah, I mean, that always, it's always the case that what it says is true. So let's give it a test ourselves. Okay. So let's go ahead and test it. And see if this functionality was actually implemented here. (t: 240) Here, when we hover the author name, that's working. This was previous functionality. Now, if we hover over the title. Wow, look at that. (t: 250) Perfect. Exactly what we asked for. We get a little preview of the article here. Pop-up shortage. Yeah, that's perfect. (t: 260) Even like gives us the hashtags here. Read more. Even the comment count. This is really great. Oops, I accidentally clicked that stuff. Let's see if it works for the other ones. (t: 270) Yep. All of these like really quickly load on hover. That was amazing. Augment was able to one-shot this problem through a massive code base. 48 million tokens, over 1 million lines of code. (t: 280) Let's see how Cursor compares. Okay. So we're in Cursor now. I've pushed Augment's changes to a separate branch. (t: 290) And we're going to go back and check out main branch, where we were working off before. So it's just the same start that Augment had. (t: 300) We've opened up Cursor's agent here. Everything is on default settings. I haven't changed any of the settings. We're going to give the exact same prompt that I gave to Augment. (t: 310) And just kick this off with complete default settings and see how it compares. So we can see similarly, Cursor tries to do some context retrieval as well. (t: 320) Let's see how it compares. And it compares in this large code base. And okay, so it's finished the context. (t: 330) That was pretty fast. But it's given a plan and we have to look over the plan. It didn't kick off the plan itself. So it looks good. (t: 340) I'm just going to say continue. Looks good. So I just said continue from the plan. And it's going to continue and implement the plan here. (t: 350) It's looking like it keeps wanting my... attention. I'm not sure if there's specific modes I have to enable to make it more autonomous. (t: 360) But I'm just going to try prompt this and say, prompting it to say continue until the functionality is working to try get a similar result. Okay, cool. So we actually have to do's here, which is very similar to the task list we saw in Augment. (t: 370) Yeah, it looks pretty much the same, although I don't see an opportunity to edit these tasks. (t: 380) I don't see an opportunity to delete any of the tasks. I don't see any of the tasks. Or add new tasks. It's kind of just like a to do list that the agent has generated, which is nice. (t: 390) We do get to have that observability on what it's currently working on and what's next, but no ability for us to interact with that to do list. (t: 400) Okay, you can see it's actually finished. It doesn't even say it's finished one of the tasks, but it has asked me to if I wanted to continue. (t: 410) So it looks like I have to hold it. I have to hold cursor's hand here quite a long way. So now try a different prompt. Continue until all to do's are complete. (t: 420) Hopefully this time it'll just keep going without asking me for help. (t: 424) And then suddenly it's jumped to four out of four done. (t: 430) Okay, so maybe the progress of the to do's wasn't working properly. I'm not sure. But I guess it says it's finished. So let's test this out. (t: 440) Here I have running the version that cursor changed. We can see the hover on the author works just as previously. But now we want to see if the hover on title works. (t: 450) And nope, we can see that nothing happens when I hover. We should have seen a preview like I prompted, but didn't work. (t: 460) Yeah, I mean, this is one of the reasons why I chose augment. It's way more effective and it kind of gets it done in one shot. As you saw, cursor required a lot of should I continue? (t: 470) Are you happy with these plans? And the testing it did. It seemed really lackluster compared to augment. And that's why a lot of people are making the switch. (t: 480) Okay, windsurf's turn with cascade. Let's see if it can handle the forum repository. The same exact prompt on default settings. Interestingly, they have Gemini 2.5 pro as their default. (t: 490) It's probably after the open AI drama. Let's see how it goes. Okay, interesting. I think these might be the thinking tokens just given to us raw. (t: 500) There's no way anyone's reading this. This has been going on for like, you know, I think it's just stuck on a loop. (t: 510) It's actually been saying the same thing. Well, before the open AI stuff, windsurf probably worked with an open AI model. So maybe it'll work better if we try GPT 4.1. (t: 520) Let's see. Exact same prompt. Okay, this is looking a lot better. Anyway, it looks like it's actually working now, which is good. Okay, it's made a plan, kicked it off. (t: 530) Is there like a to do list? There's a changes overview. All right. This is much better than the. The gibberish. Oh, and it makes a sound. Okay. The code is now ready to test. That's where it gives up. (t: 540) It doesn't do any of its own tests. But, you know, if it works, then that's amazing. All right, let's test it. Let's see. Author. (t: 550) Okay. What about title? Here we go. Moment of truth. Will windsurf on title hover? No, nothing. Author still works. (t: 560) Title does not work, how we asked. Same exact prompt. It just wasn't able to handle it. All right. We have Claude code here. Fresh out of the box. Fresh install default settings. (t: 570) We're going to give it the exact same prompt and see how it compares. Okay. We got two dues. Great. Just like the task list and two dues in cursor. (t: 580) One really cool thing here is we see two separate tasks running simultaneously, gathering context, probably speeding up the context gathering. We can see it's doing little search patterns with keyword and regex style. (t: 590) Uh, grep call. As well as reading individual files. That's very cool. I can definitely see why Claude code is one of the most popular favorites in my community. (t: 600) Um, I definitely do like a terminal interface. I was using AIDR for the longest time before switching and I have under good authority that (t: 610) augment will be releasing a CLI pretty soon as well as probably a mobile and web app. (t: 620) Very exciting. I do have to say. I do have to shout out to Claude code for kind of, I think they pioneered this type of regex search pattern style. (t: 630) This type of regex search pattern style you see here with like wild cards and, and stuff. Um, this is how a lot of, um, human developers actually search as well. (t: 640) So, okay. It's done the context gathering stage and now we're onto the implementation stage. (t: 650) Yeah. One downside of this TUI. It seems like, you know, editing the task list and stuff that we had in, we had in the GUI of augment was a lot more straightforward. (t: 660) I'm here because we're on default settings. Um, it does ask us to approve a lot of changes and stuff. (t: 670) Um, but we're gonna, we're gonna just keep it going. Um, similar to how we continued with cursor. Luckily they do have a don't ask again in this session as well. (t: 680) So, um, I think that's a good thing. Okay. It's done implementing and now we're going to test the changes. That's really good to see that Claude code also kind of tests out of the box. (t: 690) Um, you don't have to tell it to test its changes. Um, personally, I think that is something that should be default in their settings or system prompt or whatever. (t: 700) All right. Testing has finally finished. Now I expect it to say it's done. (t: 710) There we go. And we're ready to do the manual verification testing. Let's see how Claude code did. (t: 720) Okay. This is Claude codes changes. Um, I see that the titles are in the middle here. Um, the hover on author works correctly. A hover on title just kind of highlights it. (t: 730) If we, oh, if we click on it, the hover preview does come. That technically isn't what we asked for. We asked for on hover. So I mean, overall it was very close. (t: 740) I'm sure one more prompt would have kind of made it work on hover instead of on click. And okay. Thank you to everyone who made it this far to the end of the video. (t: 750) Those were the four most popular agents, but maybe your favorite agent wasn't in there. So let me know which one was your favorite agent and why wasn't it augment. (t: 760) I know I picked this challenge because I know augments context engine is what really sets it apart and what made me switch over to augment. But yeah, let's take this further. (t: 770) I'm, I'm really interested to hear your feedback. And if augment is your favorite agent, please leave a comment as well. I'm trying to gather some super users and we can make some content and collaborate together. (t: 780) Oh, and by the way, I'll leave a link to the repository in the bottom where you'll see a separate branch for, actually, let me just show you guys. By the way, I'll leave a link to the repository in the description. (t: 790) If you want to check every agent's attempts, you just check the different branches here. Augment, cloud, augment, cloud, cursor and windsurf. (t: 800) Yeah. And all the other links. If I've missed anything, you know where to leave a comment.

