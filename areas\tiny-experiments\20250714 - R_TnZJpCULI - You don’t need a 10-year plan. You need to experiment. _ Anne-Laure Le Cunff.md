---
title: You don’t need a 10-year plan. You need to experiment. | <PERSON><PERSON><PERSON><PERSON>
artist: Big Think
date: 2025-07-14
url: https://www.youtube.com/watch?v=R_TnZJpCULI
---

- [00:00:00](https://www.youtube.com/watch?v=R_TnZJpCULI&t=0) - We're all staring at a giant leaderboard with social media where we can see how other people are progressing their success. Where we keep on comparing ourselves to each other, asking, is this person being faster? Is this person working harder? Is this person being more successful? This creates toxic productivity when we overwork ourselves just trying to climb those ladders as quickly as possible. We feel like if we manage to be successful, then, then we'll be happy. Because of that, we try to build systems, we try to stick to routines, and we try to go through very long lists of tasks,

- [00:00:30](https://www.youtube.com/watch?v=R_TnZJpCULI&t=30) - We feel like if we manage to be successful, then, then we'll be happy. Because of that, we try to build systems, we try to stick to routines, and we try to go through very long lists of tasks, often ignoring our mental health in the process. But when you ask happy people how they discovered their passion, and if they give you an honest answer, they'll tell you they stumbled upon it. What we can learn from that is that finding your purpose in life is not really about seeking it, obsessing over it, or applying a plan, but instead, it's about following your curiosity, experimenting, exploring, trying new things, and trusting that you will figure out what is the thing that makes you excited to wake up in the morning.

- [00:01:00](https://www.youtube.com/watch?v=R_TnZJpCULI&t=60) - obsessing over it, or applying a plan, but instead, it's about following your curiosity, experimenting, exploring, trying new things, and trusting that you will figure out what is the thing that makes you excited to wake up in the morning. I'm Anne-Laure Lecunf. I'm a neuroscientist and the author of Tiny Experiments, How to Live Freely in a Goal-Obsessed World. A lot of us are currently experiencing cognitive overload, and there are many reasons for that. The world is changing fast. We're all changing. We're all changing. We're all changing. And we're trying to hoard as much information as possible to understand what's going on around us. We're trying to be as productive as possible

- [00:01:30](https://www.youtube.com/watch?v=R_TnZJpCULI&t=90) - and there are many reasons for that. The world is changing fast. We're all changing. We're all changing. We're all changing. And we're trying to hoard as much information as possible to understand what's going on around us. We're trying to be as productive as possible in order to keep up, again, with this world that keeps on changing. In essence, there is a lot more to think about on a daily basis, but our brains haven't evolved. They're still the same that they were thousands of years ago. This creates anxiety because we keep asking ourselves, how am I doing? Am I doing better? Am I being fast enough? Am I being productive enough? Am I being ambitious enough? Tiny Experiments offer an alternative to this maximetist approach, where instead of going for the bigger thing, you go for the thing that is most likely to bring you discovery,

- [00:02:00](https://www.youtube.com/watch?v=R_TnZJpCULI&t=120) - Am I being productive enough? Am I being ambitious enough? Tiny Experiments offer an alternative to this maximetist approach, where instead of going for the bigger thing, you go for the thing that is most likely to bring you discovery, fun, enjoyment, and that is based on your curiosity rather than an external definition of success. A linear model of success is based on a fixed outcome that we try to get to. It implies that first you do A, then B, then C, and then you'll be successful. There are lots of problems. There are lots of problems with a linear model of success. One of them is that it assumes that you know where you're going, which might not always be the case. Another one is the assumption that wherever you want to go right now

- [00:02:30](https://www.youtube.com/watch?v=R_TnZJpCULI&t=150) - It implies that first you do A, then B, then C, and then you'll be successful. There are lots of problems. There are lots of problems with a linear model of success. One of them is that it assumes that you know where you're going, which might not always be the case. Another one is the assumption that wherever you want to go right now is where you will want to go in a few years from now. Things are changing very fast. Our world is evolving, and you should allow yourself to change the direction of your ambitions with the world, as the world changes. There's a famous quote that has been attributed to a lot of different scientists, including Viktor Frankl, and that says that our freedom lies within the gap between stimulus and response. What that means is that whenever we find ourselves in a situation

- [00:03:00](https://www.youtube.com/watch?v=R_TnZJpCULI&t=180) - There's a famous quote that has been attributed to a lot of different scientists, including Viktor Frankl, and that says that our freedom lies within the gap between stimulus and response. What that means is that whenever we find ourselves in a situation or whenever we face a trigger, there is a little gap between that trigger and our response. And in that gap lies the freedom to make a choice. Are we going to go for the automatic response, the one we have no control over? Or are we going to pause and ask ourselves, how do I want to respond to this situation? Whenever we face a disruption in our life, it's very tempting to try and immediately solve the actual problems that arise from that disruption.

- [00:03:30](https://www.youtube.com/watch?v=R_TnZJpCULI&t=210) - how do I want to respond to this situation? Whenever we face a disruption in our life, it's very tempting to try and immediately solve the actual problems that arise from that disruption. We completely ignore the emotional experience. Affective labeling means labeling your emotions. It really just means putting words to feelings. And it's incredibly helpful because it allows you to better connect and understand your emotions so you can manage them better. So, let's start with the first one. What is emotional labeling? This allows us to reduce activity in the amygdala, which is involved in unconscious emotional processing, and to increase activity in the prefrontal cortex,

- [00:04:00](https://www.youtube.com/watch?v=R_TnZJpCULI&t=240) - What is emotional labeling? This allows us to reduce activity in the amygdala, which is involved in unconscious emotional processing, and to increase activity in the prefrontal cortex, which is involved in rational thinking. It's really just about picking a word to describe your current emotion. And if you can't find the right word, there is research showing that describing a landscape is also a great way to practice affective labeling. So, you could say, for example, that it's a stormy day, over a dark forest. Or it's a scary cliff over a beautiful sea. This practice is particularly useful for people who like to feel like they're in control.

- [00:04:30](https://www.youtube.com/watch?v=R_TnZJpCULI&t=270) - So, you could say, for example, that it's a stormy day, over a dark forest. Or it's a scary cliff over a beautiful sea. This practice is particularly useful for people who like to feel like they're in control. When your mind is clear, when you have processed the emotions, you will be able to think about the actual consequences in a more efficient way. A mindset is a default way of seeing the world. And our mindsets influence so many things in our lives. They influence our decisions. They influence the way we think. They influence our relationships. And even the way we feel. Being aware of your mindsets is the difference between

- [00:05:00](https://www.youtube.com/watch?v=R_TnZJpCULI&t=300) - They influence our decisions. They influence the way we think. They influence our relationships. And even the way we feel. Being aware of your mindsets is the difference between living a conscious life, where you're making choices in accord with what you actually want, versus being on autopilot. There are three subconscious mindsets that get in the way of us living happy, conscious lives. These three mindsets are called the cynical mindset, the escapist mindset, and the perfect mindset. The cynical mindset is when we have lost all curiosity and ambition in life. And we're actually sometimes even making fun of earnest people who still have this high level of curiosity and ambition. So things that we might be doing instead is doom scrolling,

- [00:05:30](https://www.youtube.com/watch?v=R_TnZJpCULI&t=330) - The cynical mindset is when we have lost all curiosity and ambition in life. And we're actually sometimes even making fun of earnest people who still have this high level of curiosity and ambition. So things that we might be doing instead is doom scrolling, sitting on the sofa, going through negative news, and then maybe even spending a lot of time and energy on the fact that we're not really doing anything. So we're not really doing anything. We're just sitting on the sofa, going through negative news, and then maybe even spending a lot of time and energy discussing and debating those negative news with other people. In the escapist mindset, we're still curious, but we have decided to let go of our ambitions. We're trying to do everything we can to escape our responsibilities.

- [00:06:00](https://www.youtube.com/watch?v=R_TnZJpCULI&t=360) - discussing and debating those negative news with other people. In the escapist mindset, we're still curious, but we have decided to let go of our ambitions. We're trying to do everything we can to escape our responsibilities. That can take the form of retail therapy, binge watching, or dream planning, or next vacation, instead of doing something right now to change our lives. In the perfectionist mindset, we have high ambition but low curiosity. We try to escape uncertainty through work. We feel like if we manage to achieve that goal, if we manage to be successful, then, then we'll be happy. But you have decided to let go of your curiosity. Those mindsets are actually very fluid,

- [00:06:30](https://www.youtube.com/watch?v=R_TnZJpCULI&t=390) - through work. We feel like if we manage to achieve that goal, if we manage to be successful, then, then we'll be happy. But you have decided to let go of your curiosity. Those mindsets are actually very fluid, and they might change depending on our situation and different triggers and different ambitions that we might have at the moment. These mindsets are not fixed personality traits. We can change them. There is an alternative, which is called the experimental mindset, where your curiosity and your ambition are both high. Instead of chasing those linear goals that give you the illusion of certainty, you're open to designing experiments.

- [00:07:00](https://www.youtube.com/watch?v=R_TnZJpCULI&t=420) - where your curiosity and your ambition are both high. Instead of chasing those linear goals that give you the illusion of certainty, you're open to designing experiments. Having an experimental mindset means seeing failures as data points that you can learn from. If you keep on repeating the same trial and everything works as expected, it means that you're not growing, you're not learning anything. All scientists know that real growth requires both trials and error. It means embracing the fact that you might not have a plan, that you don't know what's coming. And this is great. It means that you can design your life in a way that is conscious and connected.

- [00:07:30](https://www.youtube.com/watch?v=R_TnZJpCULI&t=450) - It means embracing the fact that you might not have a plan, that you don't know what's coming. And this is great. It means that you can design your life in a way that is conscious and connected. The idea of cultivating an experimental mindset is based on the scientific method. And this is very simple. First, you start by observing your current situation, by looking at the world around you. Then you ask a research question, and you design a task. You can then design a tiny experiment to collect data, which you can then analyze. But to design an experiment, you need to commit to curiosity. A great way to do this is to design what I call a pact. This is a commitment device

- [00:08:00](https://www.youtube.com/watch?v=R_TnZJpCULI&t=480) - You can then design a tiny experiment to collect data, which you can then analyze. But to design an experiment, you need to commit to curiosity. A great way to do this is to design what I call a pact. This is a commitment device where you say, I am going to run this experiment and perform this action for this specific duration. A pact is actionable. This is something that you need to be able to do right now. You don't need extra resources. You don't need help from other people. This is something you can try straight away. And what's great is that when each pact you design has purpose imbued into it, you don't need to have a grand purpose in life. I highly encourage you, while you're running your pact, to take little notes.

- [00:08:30](https://www.youtube.com/watch?v=R_TnZJpCULI&t=510) - And what's great is that when each pact you design has purpose imbued into it, you don't need to have a grand purpose in life. I highly encourage you, while you're running your pact, to take little notes. Based on that, you can make the decision to either persist with your pact, as is, because it works for you. You can also pause it if you feel like that's not really something you want to keep going with. Or you can pivot, which means making a little tweak and changing something before you start your next cycle of experience. And that's what makes a good experiment. Curiosity keeps you adaptable and nimble in an ever-changing world. It ensures that you stay open to new possibilities. And frankly, it just makes life more fun. This all might sound philosophical,

- [00:09:00](https://www.youtube.com/watch?v=R_TnZJpCULI&t=540) - Curiosity keeps you adaptable and nimble in an ever-changing world. It ensures that you stay open to new possibilities. And frankly, it just makes life more fun. This all might sound philosophical, but there's actually a lot of neuroscientific research showing that when we experience thirst for water, the same parts of the brain activate than when we experience thirst for information. So when we say, I'm thirsty for knowledge, I want to learn more, I want to know more, we couldn't be more right. The problem today is that it's become really, really hard to know the difference between information and knowledge. Information is just a piece of data. It could be valid, it could be not valid, it could come from any kind of source.

- [00:09:30](https://www.youtube.com/watch?v=R_TnZJpCULI&t=570) - The problem today is that it's become really, really hard to know the difference between information and knowledge. Information is just a piece of data. It could be valid, it could be not valid, it could come from any kind of source. And by consuming all of this information, we might actually not take action to go in the real world and collect our own data, our own knowledge, that would be much more helpful in order to make our own decisions. Like sticking to the safe path, the more obvious one, where we feel like we understand all of the parameters. Or sometimes that could even look like doing nothing because we're too afraid to do something dangerous. Uncertainty fuels anxiety.

- [00:10:00](https://www.youtube.com/watch?v=R_TnZJpCULI&t=600) - where we feel like we understand all of the parameters. Or sometimes that could even look like doing nothing because we're too afraid to do something dangerous. Uncertainty fuels anxiety. Research shows that when we experience uncertainty, our neural activity intensifies. Our brains are wired to fear uncertainty, and that makes sense from an evolutionary perspective. When you think about the conditions, in which our species started, the more information you had, the more likely you were to survive. A weird noise in the bushes, or a new food that you'd never tried before, all of these could be lethal. And obviously this was very useful in the jungle, but not so much in our modern environment.

- [00:10:30](https://www.youtube.com/watch?v=R_TnZJpCULI&t=630) - the more likely you were to survive. A weird noise in the bushes, or a new food that you'd never tried before, all of these could be lethal. And obviously this was very useful in the jungle, but not so much in our modern environment. We try to get to an answer as quickly as possible, which means we sometimes go for the most obvious one, rather than the most interesting one. This is also why we prefer getting bad news than waiting for an answer. In studies, uncertainty has been found to cause more stress than individual pain. When we know we're going to experience pain, we can mentally prepare for it. Whereas when we're facing uncertainty, the doubt of not knowing what kind of pain,

- [00:11:00](https://www.youtube.com/watch?v=R_TnZJpCULI&t=660) - to cause more stress than individual pain. When we know we're going to experience pain, we can mentally prepare for it. Whereas when we're facing uncertainty, the doubt of not knowing what kind of pain, what level of pain, is actually more stressful than knowing exactly what's going to happen. When there is no uncertainty, when we know exactly what we're doing, that means we're not growing anymore. So we should actually seek uncertainty. This is how we can evolve. It's only through errors that you can adjust your path, that you can try new approaches, and that you can discover that some of your assumptions were wrong. A cognitive script is an internalized behavioral pattern that tells us how we're supposed,

- [00:11:30](https://www.youtube.com/watch?v=R_TnZJpCULI&t=690) - that you can try new approaches, and that you can discover that some of your assumptions were wrong. A cognitive script is an internalized behavioral pattern that tells us how we're supposed, or at least how we think we're supposed to act in certain situations. For routine, everyday decisions, cognitive scripts are actually very practical and useful. The problem with cognitive scripts is, when we use them to make more important decisions in our lives, in our careers, in our relationships, instead of asking ourselves, is that really what I want to do? We let our choices being driven by those stories that we have internalized. We feel like the narrative needs to make sense.

- [00:12:00](https://www.youtube.com/watch?v=R_TnZJpCULI&t=720) - instead of asking ourselves, is that really what I want to do? We let our choices being driven by those stories that we have internalized. We feel like the narrative needs to make sense. One of these scripts is the SQL script. That's the script where we feel like we've always behaved in a certain way, so we're going to keep on behaving in the same way. It's quite obvious why we're going to keep on behaving in the same way. It's quite obvious why we're going to keep on behaving in the same way. It's quite obvious why we're going to keep on behaving in the same way. Why? It limits the possibilities that we might explore in life. So that might mean dating the exact same type of person, or choosing the next person in response to whoever we were dating before. And they might look like the complete opposite, but the truth is we still pick this person based on a sense of continuity

- [00:12:30](https://www.youtube.com/watch?v=R_TnZJpCULI&t=750) - or choosing the next person in response to whoever we were dating before. And they might look like the complete opposite, but the truth is we still pick this person based on a sense of continuity with whatever the previous experience was before. Another cognitive script that rules our lives is the crowd-pleaser script, where we make decisions based on whatever is going to please people around us the most. And what you don't realize is that you're not making decisions based on what you want and what would make you happy, but based on what will make others around you happy. Finally, there's the epic script. And this one is very insidious because it's actually celebrated in our society.

- [00:13:00](https://www.youtube.com/watch?v=R_TnZJpCULI&t=780) - and what would make you happy, but based on what will make others around you happy. Finally, there's the epic script. And this one is very insidious because it's actually celebrated in our society. It's the script that says that whatever you do, it needs to be big. It needs to be very ambitious. It needs to be impactful. Anything less than that is failure. It has created a form of stigma around having a small, simple life. Because if you don't have those external signs of success, if you're not following your grand passion, then are you really living a meaningful life?

- [00:13:30](https://www.youtube.com/watch?v=R_TnZJpCULI&t=810) - Because if you don't have those external signs of success, if you're not following your grand passion, then are you really living a meaningful life? Our modern hyper-connected online world has made the epic script, unfortunately, very, very popular with people. We have become, over the years, overly obsessed with finding our purpose. Mentions in books of the phrase find your purpose have surged 700% in the past two decades only. The problem with this is that if we fail at this particular project, this particular goal, we feel like we have failed at life entirely. And the other problem with putting all of our eggs in the same basket is that then sometimes the basket just becomes too heavy

- [00:14:00](https://www.youtube.com/watch?v=R_TnZJpCULI&t=840) - The problem with this is that if we fail at this particular project, this particular goal, we feel like we have failed at life entirely. And the other problem with putting all of our eggs in the same basket is that then sometimes the basket just becomes too heavy and we drop it altogether. In a way, we have all agreed to tie our self-worth to our purpose. We have all agreed to put all of our efforts into our productivity. When we want to exercise, we decide that we have to go to the gym every single day. If we want to start writing, we decide that we're going to start writing a book. This very often leads to overwhelm and burnout and sometimes just completely abandoning our projects because they're just too big. Whenever we find ourselves procrastinating,

- [00:14:30](https://www.youtube.com/watch?v=R_TnZJpCULI&t=870) - This very often leads to overwhelm and burnout and sometimes just completely abandoning our projects because they're just too big. Whenever we find ourselves procrastinating, we just try to ignore it. We push through using our willpower and we feel self-blame and self-judgment. Because of the efficiency worship that we have developed in our industrial age, we are now seeing procrastination as a character flaw rather than what it is, a signal that is worth listening to. There's a very simple tool that you can use whenever you experience procrastination. It's called the triple check. And it's about asking yourself, why am I procrastinating? Is it coming from the head, from the heart,

- [00:15:00](https://www.youtube.com/watch?v=R_TnZJpCULI&t=900) - that you can use whenever you experience procrastination. It's called the triple check. And it's about asking yourself, why am I procrastinating? Is it coming from the head, from the heart, or from the hand? If it's coming from the head, it means that at a rational level, you're not fully convinced that you should be working on that task in the first place. If it's coming from the heart, it means that at an emotional level, you don't think this is going to be quite fun or enjoyable to work on. And if it's coming from the hand, that means that even though at a rational level, you feel like I should be working on this, at an emotional level, you feel like that looks like fun. At a practical level, you feel like you're not equipped with the right tools

- [00:15:30](https://www.youtube.com/watch?v=R_TnZJpCULI&t=930) - that means that even though at a rational level, you feel like I should be working on this, at an emotional level, you feel like that looks like fun. At a practical level, you feel like you're not equipped with the right tools or you don't have the right resources in order to get the job done. Sometimes you'll go through the triple check, head, heart, hand, and everything fills in alignment and still you're procrastinating. In this case, it might be worth looking outside of yourself for systemic barriers. And that means having honest, sometimes difficult conversations with other stakeholders, redesigning your environment if it is not conducive to you being focused and productive, or sometimes completely removing yourself

- [00:16:00](https://www.youtube.com/watch?v=R_TnZJpCULI&t=960) - sometimes difficult conversations with other stakeholders, redesigning your environment if it is not conducive to you being focused and productive, or sometimes completely removing yourself from that work environment and doing something else because you might not be able to change that situation every time. Let me give you an example. I designed an experiment where I wanted to explore whether I wanted to become a YouTuber. This was something I had noticed a lot of friends around me doing, and they seemed to have a lot of fun. That piqued my curiosity enough that I wanted to give it a try. So I designed a pact, and as said, I'm going to publish a video every week until the end of the year. Very simple pact, which I completed.

- [00:16:30](https://www.youtube.com/watch?v=R_TnZJpCULI&t=990) - that I wanted to give it a try. So I designed a pact, and as said, I'm going to publish a video every week until the end of the year. Very simple pact, which I completed. At the end of my pact, I looked at the data. External data, pretty good. Looking at the traditional metrics of success of a YouTube channel, I got to a good number of subscribers, a lot of positive comments. People seemed to like the videos, but internal data. I actually did not enjoy producing these videos. Every week when I had to sit down in front of the camera, I was dreading it. I love having face-to-face conversations with people and seeing their reactions in real time, but just looking at a camera with no feedback whatsoever

- [00:17:00](https://www.youtube.com/watch?v=R_TnZJpCULI&t=1020) - Every week when I had to sit down in front of the camera, I was dreading it. I love having face-to-face conversations with people and seeing their reactions in real time, but just looking at a camera with no feedback whatsoever was very uncomfortable for me. As a result, every week I was procrastinating for so long. Every time I had to film a video, I was procrastinating. I felt deeply anxious, and I was not even able to work on anything else on those days where I was supposed to film. Based on this, even though the YouTube channel was fairly successful in such a short amount of time, I decided to stop. I realized that I was not going to be a YouTuber, and I prefer to keep on writing my newsletter. By not focusing on the outcome and instead designing a tiny experiment,

- [00:17:30](https://www.youtube.com/watch?v=R_TnZJpCULI&t=1050) - in such a short amount of time, I decided to stop. I realized that I was not going to be a YouTuber, and I prefer to keep on writing my newsletter. By not focusing on the outcome and instead designing a tiny experiment, what you can do is letting go of any definition of success, letting go of that binary, and then focusing on the results that you're looking for, and instead focusing on a research question, a hypothesis that you might have, something that makes you feel curious and that you want to explore. A great way to start reimagining what your life could look like is to practice what I call self-anthropology, becoming an anthropologist with your life as your topic of study. Ask questions like,

- [00:18:00](https://www.youtube.com/watch?v=R_TnZJpCULI&t=1080) - A great way to start reimagining what your life could look like is to practice what I call self-anthropology, becoming an anthropologist with your life as your topic of study. Ask questions like, why are people doing things the way they're doing them? Why do they care about this? Why is this thing so important to them? Why do they want to do something with your life? Observe what gives you energy and what drains your energy, the conversations that you enjoy having, the projects that you like working on. And that really means thinking about your emotions, your energy, and your executive function. By reconnecting with these emotions instead of trying to ignore them, not only are we going to understand ourselves better,

- [00:18:30](https://www.youtube.com/watch?v=R_TnZJpCULI&t=1110) - And that really means thinking about your emotions, your energy, and your executive function. By reconnecting with these emotions instead of trying to ignore them, not only are we going to understand ourselves better, but also better understand our relationship to work and become more productive in the process. Want to support the channel? Join the Big Think members community where you get access to videos early, ad-free.

