---
title: Master AI Powered Project Management with <PERSON> (Full Guide)
artist: Blazing Zebra
date: 2025-07-13
url: https://www.youtube.com/watch?v=4jD69eHZK8Y
---

(t: 0) Hey, have you heard about this new concept called context engineering? This is a powerful new technique that AI developers are using to dramatically improve their workflows, but I strongly believe that anybody who is managing complex projects can really benefit from this. (t: 10) Here's the thing, these AI models have grown to become incredibly powerful, and they can handle massive amounts of information. (t: 20) The real challenge these days is how we manage the information or context that we give it. Context engineering is nothing more than a highly structured approach to how we handle (t: 30) all of the information that we give to the AI about our various projects. Think about it, information is a valuable resource just like money, so it just makes sense that we'd want some tools and methods for managing it (t: 40) the same way we use tools to manage our bank account and investment portfolios. Today I'm going to show you exactly how to implement context engineering into your project management workflow using the all-powerful Claude Code. (t: 50) While I was running my marketing agency, I watched talented teams lose millions due to bad communication. The same stakeholder questions every week, decisions made with incomplete information, (t: 60) project knowledge trapped inside people's heads, aka bad context engineering. (t: 70) And in many ways, AI is accelerating this problem. Today's framework covers three phases, context discovery, context architecture, and context preservation. Each builds upon the last to create systems that get smarter over time (t: 80) rather than degrading with each chat request. Most project managers are trapped in task coordination when they should be building proactive context systems. (t: 90) This isn't just about project management, this actually goes way deeper. This is about implementing anti-fragile and rapidly adaptable systems (t: 100) that are ready to meet the demands of this really crazy and quickly changing world. So jumping right into the cheat sheet, I create a cheat sheet like this for every single video that I create. (t: 110) They are all instantly available to anybody who joins my Patreon. There's a link in the description, so check that out if you are interested. We're going to jump right into this section for understanding context engineering as it (t: 120) pertains to project management. While most project management approaches treat information as scattered resources, with context (t: 130) engineering we're going to treat this information in a carefully architected way. This allows our project management system to actually grow smarter over time, and we can actually reuse different pieces of information across different projects very easily. (t: 140) I've already touched on some of the problems that this solves, but here's one that jumps out at me that project knowledge is often lost between the different phases of a project (t: 150) and as the team changes. I don't know if you've ever done any work with anybody in Europe, but there is nearly 100% of the time one of the team members is on holiday and nothing can happen. (t: 160) So those projects got basically infinitely stalled right off the bat. This process would have definitely helped with that. Here's another huge problem that this is going to solve when our AI assistants give inconsistent (t: 170) advice. It's due to mixing context. I'm sure you can relate to this when you think about all the times you've had to re-explain what you're working on to an LLM. (t: 180) With context engineering, we're going to be going through a systematic approach for how we collect this information, how we preserve this information, and we're going to notice that our results are going to compound not only through the life of that one project, (t: 190) but all future projects as we're going to be able to copy and paste the most pertinent knowledge into future projects very easily. (t: 200) This is pretty much impossible to do. It's not a lot of work to do inside of a normal LLM, as there's no great way to manage your context or your information. (t: 210) Sure, there's different memories that it stores, and you can point it at your Google Drive and so forth, but I've found all of those to be fairly unreliable. This gets much better when you use tools like Cloud Projects, ChatGPT Projects, and Notebook (t: 220) LLM, as these different tools allow you to upload and manage information in various ways. (t: 230) Most of what we do today, you can do inside of these different tools. So, let's get started. Let's get started. Let's get started. Let's get started. Let's get started. Let's get started. Let's get started. Let's get started. Let's get started. For these, I'm especially fond of Cloud Projects. If you're unfamiliar with Cloud projects, all you got to do is go into Anthropix Cloud, (t: 240) click this Projects tab, create a new project. And from here, you can add instructions about your project here to the LLM, as well as adding (t: 250) files in here. And I've got a couple videos that go in depth into this, and there's really some handy tips and tricks in those videos. I'll link to those in the description. But today, we're going to take it to the next level and use this very powerful tool. (t: 260) called Claude Code. I'm sure you've heard some buzz about this, and I've been incredibly impressed with what I've been able to do with this tool. And this tool was created to be used inside of (t: 270) your terminal, but that was always intimidating to me because I couldn't really see what it was doing. I just had to trust that it was doing the right thing. This all changed when I started to (t: 280) use it inside of Windsurf, because inside of Windsurf or really any IDE, maybe just VS Code or Cursor, this would work just as well. When you use Claude Code inside of these tools, you can (t: 290) really keep tabs on what it's doing, most importantly, watching how it is collecting and organizing your context. When you install Windsurf and crank it up, you're going to see this window, (t: 300) and all you got to do is just open a folder. So just create some sort of example folder on your desktop. I've opened up an empty folder, and on the left is where we're going to see all of our (t: 310) files, and we're going to be able to really understand and see our context in depth right there. We're going to see all of our files, and we're going to be able to really understand and see our context in depth right there. We're going to be able to edit and look at our files here in the top (t: 320) middle pane. Down here is where we're going to interact with Claude Code, and over here on the right is where you would normally interact with the large language model inside of Windsurf. Don't (t: 330) get me wrong, this is super powerful, but I'm going to just turn this off for now so we can focus on Claude Code. You actually might not see this down here, this terminal window. Yours might (t: 340) look just like this. So all you got to do is go up to the terminal menu, new terminal, and you'll see your terminal here. There are a lot of online resources that can walk you through exactly how to install Claude Code. I've got this all in the (t: 350) cheat sheet if you're interested. You've just got to make sure if you're on Mac that you have Homebrew installed, that you have Node.js installed, and then this is the code for installing Claude, (t: 360) and you should be good to go. I've also got some extra resources in there, including how to install it for Windows as well. One argument that people like to make against Claude Code is that it can (t: 370) be expensive, and it certainly is more expensive than your regular $20 a month options. However, I really am learning that I can't live without it, (t: 380) and for just a few dollars a day, I can get a ton of work done. Of course, if you're using it more than that, there are the $100 and $200 a month tiers that Anthropic offers in order to really, (t: 390) you know, save some money if you're hitting it very hard. At this point, I want to note that (t: 400) I'm not sponsored by Claude or Windsurf or really anybody. None of my videos are sponsored. These are just the workflows that I've found to work well for me. So now let's dive in. All we got to do is type in Claude right here, (t: 410) and we're off to the races. Claude has spun up, and we can just type in here the same way that we would type into a chat GPT, a notebook LM, or a Claude project. The difference, however, (t: 420) is that, A, Claude Code is way more powerful than a lot of these other tools. It's going to be able (t: 430) to really see all of our context that's going to appear here in a much deeper and more concise way. than any of the other tools. Additionally, it can search the web and it can find tools that it can (t: 440) install an upgrade itself as we move along. All right, so now that we've got everything set up it's time for this step 1. The core project definition and scope discovery. So, in the cheat sheet here I'm (t: 450) going to grab this prompt and copy this right into Claude Code. This prompt basically just says (t: 460) you're an expert project manager conducting comprehensive project discovery. Need you to help me establish the core project definition dances you to the chart簡� Straßeěn COVID robots basically does is you're an expert project manager conducting comprehensive project discovery, you to help me establish need you to help me establish the core project definitions andng labs and I usually set this down before I'll agree to further! the core project definitions andng everything to resolve this code just project definition through a series of focused questions. Ask me questions one at a time and (t: 470) after each response, provide a brief summary of what you've learned before moving on to the next question. So this probably sounds familiar from other workflows, but just wait because this is (t: 480) going to get crazy here in a second. The example for today is I want to work on a product launch for an info product that I'm working on. So I've now gone through and answered all of its questions (t: 490) as it relates to this core discovery prompt, as well as all of these other areas, including stakeholder and organizational context mapping, including technical and resource context (t: 500) assessments, including historical context and lessons learned, as well as risk assessment and (t: 510) success criteria. So the cheat sheet includes extensive prompts for all of that. But if you're just new to this, you may want to just experiment with the key points that I've mentioned. So I'm (t: 520) going to start with each of those sections, just, you know, telling the AI about your project, covering all of those. And if that sounds like a lot of work, it is. But basically, that is the one (t: 530) and last time you're going to have to do that work. Once you get all of that information in a detailed way uploaded, let me show you what we're going to do next. Of course, Cloud Code gives you some ideas (t: 540) of what you may want to do next, but we want to nudge it and make sure it saves all of that as a file here in its (t: 550) file. So we're going to go ahead and create a file folder and that'll be saved right on our desktop so that we can copy and paste it, share it with other documents. And what's more important is we're going to be able to update that document as the project evolves. So I'm grabbing this simple prompt right out of the cheat sheet and dropping it in here. It's created this file. So all we need to do is press enter here and we see that file show up to the left over here. Now, if this layout is intimidating to you because you don't code very much, just think of it like Notebook LM, which many people watching this video will be familiar with. So you can see that it's a little bit more complicated than the other ones. But if you're interested in learning more about this, you can go ahead and click on the link in the description. And then you can see that it's a little bit more complicated than the other ones. But if you're interested in learning more about this, you can go ahead and click on the link (t: 580) and this will have some experience with. Notebook LM looks almost exactly like this with all of your sources here on the left, all of your context. You're working with the AI here and, you know, the responses are here. So similar and just, you know, kind of ignore that it's all coding and get over that because you're going to have so much more control. Because inside WinSurf, we're going to actually be able to update these files and update, you know, add things here directly using the AI, which is not possible with these other tools. At least (t: 610) not in the way we're going to be doing it pretty soon here. So you can open that file up, take a look at it and, you know, make sure that it is all good there. And of course, that file now lives on your computer, which you can now have complete control over rather than having all these different pieces of content spread across different tools and different notebooks, etc. From there, the cheat sheet goes into a bunch of different prompts for continuing the planning phase and getting into the execution phase, all while updating (t: 640) multiple files, including status updates and Gantt charts, etc. here on the left. But as I was building this out, I realized there's a much more elegant way to handle this. So that led me to create this set of files. I call this my context engineering project template. This is all downloadable directly from Patreon. You just download the zip file, unzip it and open that file and you get something that looks just like this. And while I'd love it if you join my Patreon and grab this and start toying with this and customize it, I'm not going to be doing that. (t: 670) I'm going to be utilizing it for your own projects. What's even more important is you think about how you might be able to create a similar set of files for your specific projects. So this is the readme file that includes instructions not only for the user, but also for the AI assistant. This walks the AI assistant through exactly how to use these other files. These other files include all of the prompts for gathering the information, just like we went through, then getting into this planning workflow. And the planning highlights this context engineering project. (t: 700) This is a very important part of our process. It also includes reminders to Claude Code about all the tools that it has access to, including the fact that it can go out and research and install new tools that are pertinent for whatever project you're working on. Try doing that inside a notebook LM or a Claude project. So I'll show you just a little bit about how this works. I'm going to just fire up Claude Code again by typing in Claude. There's our buddy, Claude Code. So I'm just kicking all this off by saying, please take a look at the readme file and let's begin a new project. (t: 730) This is a new project related to an info product launch. This will be an AI masterclass. And that readme file is letting it know about all the other files there and the process that we're going to go through for project managing with context engineering as our focus. All right, so now it's gone through and created this massive to-do list for everything that we need to deal with based on all of the instructions I've given it. And it's asking us all these same questions again. So instead of answering them again, I'm going to just fire up the readme file and let's begin a new project. (t: 760) And again, remember, we have that file that we already created earlier. I'm just grabbing that file and dragging it right here into our file folder. And now I'm just saying I've uploaded the details about this project in a file called the AI Edge Coaching Program, and it can grab all the information right there. Perfect. I can see the file. And now this just goes ahead and works our way through the project. And we can then create new files over here. We can delete these files as we probably won't need these files. (t: 790) We can delete these discovery instructions down the road, and we can just continue to evolve this project, staying in lockstep with all of the context that we need. The final result of the planning phase is this microsite that keeps everybody in sync with what's going on with the project. This is a simple HTML file. Instead of creating documents that live in folders and so forth, I feel like creating a simple web portal is probably better than creating a bunch of these different documents and so forth that are (t: 820) floated around. All you got to do is copy and paste all of this HTML into this really cool tool I found called Static Run. It's completely free. Add a new project. Upload a folder inside of our context engineering. We have this folder. We upload that. Upload files. Boom. Go to our project settings. And just like that, we have this awesome web portal for our project that is completely dynamic once we update things inside of our Windsurf project. And then just read (t: 850) the HTML. We'll keep this up to date as the project progresses. It has all of the summary, key milestones, a Gantt chart, deliverables, a team page, and the risk analysis. And you can change this however you want just by communicating with Claude. Of course, you can get fancy and host it on Vercel and have it automatically update, et cetera. But if you're just getting started, just copy and pasting that HTML into this Static Run. (t: 880) Static.run is a great way to just keep everybody in sync with the project and all of the context about the project that you are continuously working on as this project evolves and as things change. To that note, one thing you might want to start experimenting with is then pushing this up, saving your work inside of GitHub the same way that developers do. That will make sure that you have version control and you can roll back to any version of this that you want. If something kind of goes haywire, you can just go ahead and do that. (t: 910) If something kind of goes haywire, you can just say, okay, let's just go back to where we were before this certain decision was made and kind of reboot from there. And you can then think about your projects in a lot of ways the same way a developer would. Because let's face it, those coding projects are very, very complicated. And if they can do it, you can implement a lot of that into your workflows for whatever project you're working on. (t: 930) So again, this really feels like the tip of the iceberg when it comes to controlling your project. (t: 940) context related to your specific projects, continuing to define and refine how you want to manage your projects by updating all these different (t: 950) instructions. And a lot of this then just starts to spill naturally into little pieces of code like that website I showed you. You can begin to get fancier with more advanced, you know, task tracking or various things that are (t: 960) directly related to whatever project you're working on. So again this cheat sheet with tons of extra resources is available to anybody who joins my (t: 970) patreon. Not only that but there's over a hundred and forty others that are all instantly accessible to anybody who joins. This goes into a lot of different (t: 980) rabbit holes, dynamic context management during execution. We didn't even get to but this is probably the most important part as the project is changing and (t: 990) shifting beneath you how to control all of that context. It also gets into advanced implementation for tech. Teams, you know, once you upload this to GitHub other team members can access it (t: 1000) and update it accordingly without overlapping each other. That's what GitHub is all about making sure that multiple people can work on one code (t: 1010) base or in this case one context engine. This gets into context overload, you know, cleaning that stuff out, making sure that everything is up to date. So definitely (t: 1020) check out the patreon for this cheat sheet as well as that zip file with all of the files that automatically walk you through all of this stuff including everything that you need to know about your project. So I'm going to go ahead and show you how to do that in just a minute. This is a really, really simple tutorial. You can do it all by using GitHub integration and a lot more. There's another video I've created all about (t: 1030) using Claude for project management here. That includes a bunch of other tips and tricks that you might want to grab and infuse into this new context engine (t: 1040) workflow so I'll see you over there.

