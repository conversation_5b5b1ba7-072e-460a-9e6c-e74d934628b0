---
title: Tiny Experiments & Ness Labs with <PERSON><PERSON><PERSON><PERSON>
artist: UT Gradschool
date: 2025-07-01
url: https://www.youtube.com/watch?v=OjdGeNAm930
---

- [00:00:00](https://www.youtube.com/watch?v=OjdGeNAm930&t=0) - A lot of the grad students are sort of figuring out what to do next in their career. They're in PhD programs, they're in other grad programs. And so the idea is, how can we help them ask the right questions and figure that stuff out? So to just set the stage, I wanted to ask you about your journey through curiosity. So were you always very curious? I was always quite curious. I think I was curious as a child. I loved learning. I loved reading. I loved asking questions. But as I grew up, and I think this is something a lot of people can relate with,

- [00:00:30](https://www.youtube.com/watch?v=OjdGeNAm930&t=30) - So were you always very curious? I was always quite curious. I think I was curious as a child. I loved learning. I loved reading. I loved asking questions. But as I grew up, and I think this is something a lot of people can relate with, there was a lot of pressure to look like I knew what I was doing, looking like I had the answers. And our society is designed in a way where people are rewarded for the answers they can provide, not necessarily for the questions they ask. So as I went through my studies, and I got my first job, I was giving less and less space to my curiosity. I was more and more focused on being productive, being efficient, on climbing the ladder.

- [00:01:00](https://www.youtube.com/watch?v=OjdGeNAm930&t=60) - not necessarily for the questions they ask. So as I went through my studies, and I got my first job, I was giving less and less space to my curiosity. I was more and more focused on being productive, being efficient, on climbing the ladder. And it's only a bit later in life, closer to the age I am now, that I actually reconnected with this curious side that I always had as a child. Why do you feel that we are rewarded more for the answers than questions? This is just the incentive structure in our society. People just in general pay you to provide answers. This is what a job is supposed to be all about,

- [00:01:30](https://www.youtube.com/watch?v=OjdGeNAm930&t=90) - This is just the incentive structure in our society. People just in general pay you to provide answers. This is what a job is supposed to be all about, and the traditional model of success that we have as a society. The problem with this approach is that you can be very successful when you take this approach of just doing the things that you know how to do, and really focusing on projecting this image of expertise. But you're going to be successful in a very narrow sense of the term. You're only going to be successful in, things that you're able to imagine today. And when you're curious, you're optimizing for a different kind of success.

- [00:02:00](https://www.youtube.com/watch?v=OjdGeNAm930&t=120) - But you're going to be successful in a very narrow sense of the term. You're only going to be successful in, things that you're able to imagine today. And when you're curious, you're optimizing for a different kind of success. The kind of success where you can look back in a few years, and you think, there was no way I could have imagined that that's where I would be now, right? So those are two completely different approaches to success. One of them is very linear, focused on things that you know how to do, focused on, again, productivity, efficiency. Whereas, the other approach that's more experimental, and that's the approach I talk about in my book, Tiny Experiments, is really about optimizing for exploration, for discovery,

- [00:02:30](https://www.youtube.com/watch?v=OjdGeNAm930&t=150) - One of them is very linear, focused on things that you know how to do, focused on, again, productivity, efficiency. Whereas, the other approach that's more experimental, and that's the approach I talk about in my book, Tiny Experiments, is really about optimizing for exploration, for discovery, for asking questions, for innovation. And it can be a little bit messier, but it also means that you're carving your own path, instead of copy-pasting whatever success is supposed to look like. How can people reframe their relationship with uncertainty? The first step is probably to, acknowledge and understand the fact that, the instinctive response that we have in front of uncertainty,

- [00:03:00](https://www.youtube.com/watch?v=OjdGeNAm930&t=180) - How can people reframe their relationship with uncertainty? The first step is probably to, acknowledge and understand the fact that, the instinctive response that we have in front of uncertainty, when we're faced with uncertainty, which is a response of fear and anxiety, is completely natural. From an evolutionary perspective, our brains are designed for survival. And so that means that our brain is going to try to reduce uncertainty as much as possible. In our evolutionary past, for our ancestors, the more certainty, the more information you had in terms of where are the resources, what are the different threats in the environment,

- [00:03:30](https://www.youtube.com/watch?v=OjdGeNAm930&t=210) - In our evolutionary past, for our ancestors, the more certainty, the more information you had in terms of where are the resources, what are the different threats in the environment, the more likely you were to survive. But in today's world, as modern human beings, we want more than just surviving. We want to thrive. And so there is a little bit of work to do to unlearn those natural responses that were very useful when we were living in the past. We want to learn how to live in the jungle, but not so useful in our modern society, and make space for that uncertainty.

- [00:04:00](https://www.youtube.com/watch?v=OjdGeNAm930&t=240) - And so there is a little bit of work to do to unlearn those natural responses that were very useful when we were living in the past. We want to learn how to live in the jungle, but not so useful in our modern society, and make space for that uncertainty. Embrace that uncertainty. And instead of seeing it as something to fear and to avoid, accept that uncertainty is a natural part of growth. And a little bit like a scientist, whenever you're faced with something you don't understand, something challenging, something new, instead of freezing or running away, seeing it as something you could learn from, asking yourself, huh, that's interesting. What does that mean? What does that mean for me? What can I learn from this?

- [00:04:30](https://www.youtube.com/watch?v=OjdGeNAm930&t=270) - instead of freezing or running away, seeing it as something you could learn from, asking yourself, huh, that's interesting. What does that mean? What does that mean for me? What can I learn from this? Is there anything I could experiment around to turn that uncertainty into an opportunity for self-discovery? Can we talk a little bit more about the linear life versus the experimental life? Could you just go a bit deeper into why the linear life is the way, why is that model existed right now? And how can people understand why experiments, mending makes sense? I think it's helpful to think about them in terms of almost like visual mental models

- [00:05:00](https://www.youtube.com/watch?v=OjdGeNAm930&t=300) - why is that model existed right now? And how can people understand why experiments, mending makes sense? I think it's helpful to think about them in terms of almost like visual mental models for anyone who's more of a visualizer listening to this. So in the linear model of success, the mental model is the one over a ladder, a ladder that you're supposed to climb. And that comes with certain assumptions. That comes with the assumption that you're supposed to do things in a certain order. So you climb the rungs of the ladder, one by one, and only when either you feel ready to go on to the next one, or someone else tells you now you're ready,

- [00:05:30](https://www.youtube.com/watch?v=OjdGeNAm930&t=330) - That comes with the assumption that you're supposed to do things in a certain order. So you climb the rungs of the ladder, one by one, and only when either you feel ready to go on to the next one, or someone else tells you now you're ready, now you're allowed to go on to the next one. Only then you go on to the next level. So a little bit like a platform video game where it's just level by level. You have to collect all of the points and the artifacts at a level before you're allowed to go on to the next one. Another problem with this linear approach is that because everybody is climbing a similar ladder, you can compare your success to the success of others. And that creates this anxiety that comes from social comparison

- [00:06:00](https://www.youtube.com/watch?v=OjdGeNAm930&t=360) - Another problem with this linear approach is that because everybody is climbing a similar ladder, you can compare your success to the success of others. And that creates this anxiety that comes from social comparison where you look at this other person, this other student, and you ask yourself, how come we technically started at the same time and this person is up there and I'm still here? What you don't realize is that they're probably, you know, at a different level in other areas of their lives, in other areas of their studies, but you don't see that when you're focusing on just the main ladder, is that everybody is climbing at the same time. The mental model of the experimental approach

- [00:06:30](https://www.youtube.com/watch?v=OjdGeNAm930&t=390) - in other areas of their studies, but you don't see that when you're focusing on just the main ladder, is that everybody is climbing at the same time. The mental model of the experimental approach compared to the linear approach. So in an experimental approach, it's not a ladder, it's a loop. You go through growth loops because you go through cycles of experimentation. That means that you open a loop by asking a question. You're a bit curious about something. You're not quite sure it's going to work, but you're going to run your experiments. You're going to try it. You complete the loop by, trying the thing, collecting your data, and then reflecting on what happened. Did that work?

- [00:07:00](https://www.youtube.com/watch?v=OjdGeNAm930&t=420) - but you're going to run your experiments. You're going to try it. You complete the loop by, trying the thing, collecting your data, and then reflecting on what happened. Did that work? Did I like it? Do I want to keep going? Or do I want to stop? Or do I want to tweak it? And based on that, you can design your next cycle of experimentation. And so you grow, but you grow through those cycles instead of climbing a linear ladder. What that does is that everybody is building their own sandbox, their own mini laboratory for experiments, experimentation, and you can't compare what you're experimenting with with what another person is experimenting with

- [00:07:30](https://www.youtube.com/watch?v=OjdGeNAm930&t=450) - everybody is building their own sandbox, their own mini laboratory for experiments, experimentation, and you can't compare what you're experimenting with with what another person is experimenting with because you have your own life, your own ambitions, your own parameters. So whatever experiment I'm running, it's very unlikely that it's the same you're running. And because of that, there's no social comparison. You're literally creating your own definition of success. Let me play the devil's avocado here. What is the benefit of, social comparison? Again, that's what I was saying earlier. If you want to be successful in the traditional sense,

- [00:08:00](https://www.youtube.com/watch?v=OjdGeNAm930&t=480) - Let me play the devil's avocado here. What is the benefit of, social comparison? Again, that's what I was saying earlier. If you want to be successful in the traditional sense, then this is great. This is great. It tells you where you are. It tells you what else you need to do, how many more rungs of the ladder you need to climb in order to get to that specific destination. So in some careers, if you want to have this more linear approach to your career, which is completely fine, there's nothing wrong with that. What I'm offering is an alternative. If for you, the linear career is not too appealing, I'm offering an approach for a more non-linear career. So social comparison can be really good

- [00:08:30](https://www.youtube.com/watch?v=OjdGeNAm930&t=510) - What I'm offering is an alternative. If for you, the linear career is not too appealing, I'm offering an approach for a more non-linear career. So social comparison can be really good when we all have the same definition of success and the same metrics of success. And we can then compare our success to the one of others. And we know where we are in the game. And we know what we're supposed to do in order to get to the next level. Very useful, very efficient. If you let go of the social comparison, it does come, with a lot of uncertainty. You don't really know where you're going. You don't know the rules. There's no playbook that you can copy and paste

- [00:09:00](https://www.youtube.com/watch?v=OjdGeNAm930&t=540) - If you let go of the social comparison, it does come, with a lot of uncertainty. You don't really know where you're going. You don't know the rules. There's no playbook that you can copy and paste in order to be successful. And that can be something that some people don't want in their lives. And so I think it's very important. And so thank you for asking this question to acknowledge the fact that it's not that one is better than the other, but it is going to bring you certain constraints for both of them. Linear constraints in terms of what your success is going to look like and not the one that you're going to have to face. Non-linear experimental is going to have also constraints in terms of if you're seeking a sense of safety and stability,

- [00:09:30](https://www.youtube.com/watch?v=OjdGeNAm930&t=570) - Linear constraints in terms of what your success is going to look like and not the one that you're going to have to face. Non-linear experimental is going to have also constraints in terms of if you're seeking a sense of safety and stability, at the beginning, it might be a little bit more challenging. I feel a lot of the conversations that I have with students, they've been around this topic of fear. And it's mostly stability is the solution to fear. And that's why they're moving towards these careers. Right. Right. Right. Right. Right. Right. Right. Right. Right. Right. Right. Right. Right. Right. Right. Right. Right. Right. Right. Right. Right. Right. Right. Right. Right. Right. Right. Right. Right. Right. Right.

- [00:10:00](https://www.youtube.com/watch?v=OjdGeNAm930&t=600) - And that's why they're moving towards these careers. Right. Right. Right. Right. Right. Right. Right. Right. Right. Right. Right. Right. Right. Right. Right. Right. Right. Right. Right. Right. Right. Right. Right. Right. Right. Right. Right. Right. Right. Right. Right. Right. Right. Right. Right. Right. Right. Right. Right. Right. Right. Right. Right. Right. Right. Right. Right. Right. Right. Right. Right. Right. Right. Right. Right. Right. Right. Right. Right. Right. Right. Right. Right. Right. Right. Right. Right. Right. Some people might make the incorrect assumption that when I tell people to live a more experimental life, it means to experiment with everything.

- [00:10:30](https://www.youtube.com/watch?v=OjdGeNAm930&t=630) - Right. Right. Right. Right. Some people might make the incorrect assumption that when I tell people to live a more experimental life, it means to experiment with everything. Quit your job, drop off college and just live your experimental life. And that's not what I mean. When I talk about living an experimental life, what I talk about is making sure that at any point in your life, you have at least one tiny experiment running. And this is what I would recommend to anyone who is currently in that transitional phase, trying to figure out what they want to do next. Or maybe you actually have a pretty good idea, but you want to make sure that you explore all of your options is to create those tiny experiments that you can do on the side.

- [00:11:00](https://www.youtube.com/watch?v=OjdGeNAm930&t=660) - And this is what I would recommend to anyone who is currently in that transitional phase, trying to figure out what they want to do next. Or maybe you actually have a pretty good idea, but you want to make sure that you explore all of your options is to create those tiny experiments that you can do on the side. So that's also something I see a lot of people doing. You might decide to be pretty linear in a part of your life because that's what you need right now. You're going to get your degree. Or maybe. You're doing a first internship and this is fairly linear and that's okay if that's intentional. What you can do, especially if you have the sense of stability and linearity in the part of your life, is to be a little bit more experimental in other areas of your life.

- [00:11:30](https://www.youtube.com/watch?v=OjdGeNAm930&t=690) - You're doing a first internship and this is fairly linear and that's okay if that's intentional. What you can do, especially if you have the sense of stability and linearity in the part of your life, is to be a little bit more experimental in other areas of your life. And so you could say, I'm going to complete this degree. This is what I'm going to study. But I've always been curious about that other topic, that other area of study. And that might not even be part of my curriculum. How can I design an experiment around that? And. Example I see a lot at the moment is around AI, for example. There are very few universities that have caught up in a way that they can really teach you the latest in AI. So how can you design tiny experiments where you actually teach yourself the latest in AI?

- [00:12:00](https://www.youtube.com/watch?v=OjdGeNAm930&t=720) - Example I see a lot at the moment is around AI, for example. There are very few universities that have caught up in a way that they can really teach you the latest in AI. So how can you design tiny experiments where you actually teach yourself the latest in AI? How can you implement that? And how can you figure out ways that are interesting to you that you might want to explore and that might align with your career ambitions? So this is the kind of experimental thinking that you want to have. And so instead of blindly. Just following the linear way in all areas of your life. Saying I'm going to be intentional. These are the areas of my life where I'm okay to be linear right now. And these are the areas of my life where I want to be a bit more experimental.

- [00:12:30](https://www.youtube.com/watch?v=OjdGeNAm930&t=750) - Just following the linear way in all areas of your life. Saying I'm going to be intentional. These are the areas of my life where I'm okay to be linear right now. And these are the areas of my life where I want to be a bit more experimental. So on this topic, the thing that came to mind was when I think about social media, I see a lot of the algorithms are based on interests. Is there a distinction you make between interests and curiosities? Yeah. So. An interest you kind of have established already that this is something that you're interested in with something you're curious about. You actually don't know yet. And this is why it's worth exploring. This is why it's worth experimenting with.

- [00:13:00](https://www.youtube.com/watch?v=OjdGeNAm930&t=780) - An interest you kind of have established already that this is something that you're interested in with something you're curious about. You actually don't know yet. And this is why it's worth exploring. This is why it's worth experimenting with. So the format of an experiment that I share in the book is to decide on an action based on something you're curious about and then decide on the duration. This is the duration of the experiment. So you might say, for example. I'm going to learn one new AI tool every week for six weeks. I'm going to write a weekly newsletter for two months. I'm going to meditate every morning for 15 days.

- [00:13:30](https://www.youtube.com/watch?v=OjdGeNAm930&t=810) - I'm going to learn one new AI tool every week for six weeks. I'm going to write a weekly newsletter for two months. I'm going to meditate every morning for 15 days. I'm going to. I will action for duration. If you have an interest, if you're interested in medieval history or whatever that is, you already know you're interested in it. And so you tend to read books about it and listen to podcasts about it. You don't necessarily need to have an experiment around this because you already know this is something you're interested in. What is amazing with tiny experiments is that the conclusion, the result might tell you, actually, I'm not interested in this.

- [00:14:00](https://www.youtube.com/watch?v=OjdGeNAm930&t=840) - You don't necessarily need to have an experiment around this because you already know this is something you're interested in. What is amazing with tiny experiments is that the conclusion, the result might tell you, actually, I'm not interested in this. I like the idea of it. When my friends were talking about it, I thought I would like it, but actually I don't. And I had an experiment like that where about a year and a half ago, I don't know why, but everybody around me decided to become a YouTuber. Everybody. Everybody around me decided to become a YouTuber. And those were friends who I deeply admired. I loved their creative thinking. I love their work. And I thought maybe I should start a YouTube channel too. Everybody is doing that.

- [00:14:30](https://www.youtube.com/watch?v=OjdGeNAm930&t=870) - And those were friends who I deeply admired. I loved their creative thinking. I love their work. And I thought maybe I should start a YouTube channel too. Everybody is doing that. And I'm creative too. I like writing. YouTube is just another creative medium. So I decided to design a tiny experiment. And I said, I will publish a weekly YouTube video until the end of the year. It was around September. So that was roughly three months. And I conducted the experiment. I published my weekly videos. And at the end of the experiment, when I looked at the external metrics, it looked pretty good. I had quite a few subscribers, positive comments, lots of likes.

- [00:15:00](https://www.youtube.com/watch?v=OjdGeNAm930&t=900) - And I conducted the experiment. I published my weekly videos. And at the end of the experiment, when I looked at the external metrics, it looked pretty good. I had quite a few subscribers, positive comments, lots of likes. But the internal signals of success, I actually was dreading recording those videos every week. I did not enjoy it at all. And so I decided to stop. After that experiment, I knew now. That this thing I was curious about actually would not turn into a long-term interest. So what are some questions people can ask themselves to understand what they're curious about? So the example I gave you is actually a really good example.

- [00:15:30](https://www.youtube.com/watch?v=OjdGeNAm930&t=930) - That this thing I was curious about actually would not turn into a long-term interest. So what are some questions people can ask themselves to understand what they're curious about? So the example I gave you is actually a really good example. Just things that your friends are talking about. Things that you've heard on podcasts or you've read about in books and you feel curious about. That can be a really good starting point. If you see people who you admire or you like their work or you like the way they think. And they mention that they like something. That might be worth investigating. Maybe you will also enjoy this. So that can be an area of curiosity that you might want to turn into a tiny experiment.

- [00:16:00](https://www.youtube.com/watch?v=OjdGeNAm930&t=960) - And they mention that they like something. That might be worth investigating. Maybe you will also enjoy this. So that can be an area of curiosity that you might want to turn into a tiny experiment. Another one is going back to your childhood. Thinking about what are things you were curious about as a child. And that maybe you put aside because you were busy with your studies and life in general. So that can be something. And one of my favorites. My favorite ones is noticing when you have a fixed mindset. So in my case, for example, last year I heard myself saying to someone. I'm so bad at meditation.

- [00:16:30](https://www.youtube.com/watch?v=OjdGeNAm930&t=990) - My favorite ones is noticing when you have a fixed mindset. So in my case, for example, last year I heard myself saying to someone. I'm so bad at meditation. I'm terrible at meditation. This is not for me. And I caught myself saying this. And I noticed this is such a good example of fixed mindset. So I decided to design a tiny experiment around it. And that's the example I shared earlier. I said I'm going to meditate for 15 minutes every day for the next 15. That was the tiny experiment. So noticing things where there's a bit of resistance. Where you hear yourself saying I'm just not good at this thing. This is not for me. That might be worth conducting a tiny experiment around it.

- [00:17:00](https://www.youtube.com/watch?v=OjdGeNAm930&t=1020) - So noticing things where there's a bit of resistance. Where you hear yourself saying I'm just not good at this thing. This is not for me. That might be worth conducting a tiny experiment around it. Even though your hypothesis is that it's not going to work. That can actually be a good hypothesis. I think I'm not good at it. But it's worth trying it for maybe 10 days or 15 days. And you know what? Your experiment might confirm this. And you will be able to say in the future. I actually tried it. Not for me. Or you might get the other. Disproving your hypothesis. And realize that maybe this is something you enjoy. Yeah. You know another aspect of this is obviously your cultural upbringing.

- [00:17:30](https://www.youtube.com/watch?v=OjdGeNAm930&t=1050) - Or you might get the other. Disproving your hypothesis. And realize that maybe this is something you enjoy. Yeah. You know another aspect of this is obviously your cultural upbringing. And how there are certain rules around that. Or the way traditionally people think about it. And you know there's a lot of people that come from all over the world. To the U.S. to study. And they have. A certain image of the career path. Because of how. I mean I'm from India. They say you're either a doctor, lawyer or a failure. So those are common things that are said very casually.

- [00:18:00](https://www.youtube.com/watch?v=OjdGeNAm930&t=1080) - A certain image of the career path. Because of how. I mean I'm from India. They say you're either a doctor, lawyer or a failure. So those are common things that are said very casually. But there's some weight to them. And a lot of times that students when they're thinking about. Yeah I want to do something that looks nothing like what I'm doing right now. And when it comes to my work and the career I want to design. It's that conversation going in their mind too. So what do I say? What do I say to my parents? My uncle, aunt? Like how can people kind of bring this mindset within their family? Or how to navigate those conversations? Because I think that's such a core part of making these decisions too.

- [00:18:30](https://www.youtube.com/watch?v=OjdGeNAm930&t=1110) - What do I say to my parents? My uncle, aunt? Like how can people kind of bring this mindset within their family? Or how to navigate those conversations? Because I think that's such a core part of making these decisions too. I think this is why it can be helpful to come to the table for those conversations. With a little bit of data that you collected. I think about. I don't know how many people know about him in the U.S. But here in Europe he's pretty well known. But. Ali Abdel. He is. Was a doctor. And ended up being a YouTuber. And he basically showed to his parents. That he was publishing these YouTube videos. And that people were watching them. And so it started in a way as an experiment.

- [00:19:00](https://www.youtube.com/watch?v=OjdGeNAm930&t=1140) - Was a doctor. And ended up being a YouTuber. And he basically showed to his parents. That he was publishing these YouTube videos. And that people were watching them. And so it started in a way as an experiment. And then it was a lot easier to show to his parents. That there was another way to be successful. It's the same for me. It's only when I started making money with writing online. And the work that I was doing. That my parents. Who also come from a fairly traditional background. My mom is Algerian. And when I left Google. She was panicking. Like she really thought I was heading straight to the homeless shelter. When I told her I was quitting my job.

- [00:19:30](https://www.youtube.com/watch?v=OjdGeNAm930&t=1170) - Who also come from a fairly traditional background. My mom is Algerian. And when I left Google. She was panicking. Like she really thought I was heading straight to the homeless shelter. When I told her I was quitting my job. It's only when I demonstrated. That I was actually able to support myself. In doing this. That they were okay. So there's also a way. There's I think. Personally that a lot of the advice that just says quit your job. Stop your studies. And just follow your dream. Is actually very dangerous. Advice. First because financially speaking. It often doesn't turn out to work out for people. But we never talk about these stories. We only talk about the success stories. Second. You're actually not that it should be the main reason.

- [00:20:00](https://www.youtube.com/watch?v=OjdGeNAm930&t=1200) - It often doesn't turn out to work out for people. But we never talk about these stories. We only talk about the success stories. Second. You're actually not that it should be the main reason. Why you make any decisions. But you're probably going to worry a lot of people around you. When it's not actually necessary to do that. So a way to do this. Is for now. To stay in whatever. Linear path. You're following. While. You explore something else. The real risk. The real risk. Is when you stay on that linear path. And you don't have any space for explore. Experimentation. And then you look back when you're 40.

- [00:20:30](https://www.youtube.com/watch?v=OjdGeNAm930&t=1230) - The real risk. Is when you stay on that linear path. And you don't have any space for explore. Experimentation. And then you look back when you're 40. And you realize that you've been living this life. And going through this career. That was not what you wanted to do. But if you're still in your early twenties. And you're still figuring out. There's nothing wrong with getting started for a couple of years. In whatever career. Is safe. And stable. While you figure out what is the other thing that you want to do. And again. Quite important actually. To actually test that career. Because right now. You might think that you might want to be an online creator. Or a writer.

- [00:21:00](https://www.youtube.com/watch?v=OjdGeNAm930&t=1260) - While you figure out what is the other thing that you want to do. And again. Quite important actually. To actually test that career. Because right now. You might think that you might want to be an online creator. Or a writer. Or a full-time athlete. Or whatever it is that you're interested in. But if you haven't given yourself the space. To explore this. And experiment with it. You don't have actual data. To back this. You don't know. You think you might like it. So that's what I would tell people in that situation. And I've been in this situation. In a similar situation. It's to try and experiment. With whatever other career. You're curious about. Which will help you both. Figure out if that's really something you want to do.

- [00:21:30](https://www.youtube.com/watch?v=OjdGeNAm930&t=1290) - It's to try and experiment. With whatever other career. You're curious about. Which will help you both. Figure out if that's really something you want to do. And second. If that's really something you want to do. Having those conversations with the people who love you. And reassuring them. Showing that this is not just a blind decision. I've been trying this thing. And I know I can at least make enough money. To support myself. So you don't have to worry about me. And. Yeah. You know. Another part of this. And I know that the whole mindset behind experimenting. Is to only come up with. You're trying to learn. You're trying to connect. Collect more data.

- [00:22:00](https://www.youtube.com/watch?v=OjdGeNAm930&t=1320) - Another part of this. And I know that the whole mindset behind experimenting. Is to only come up with. You're trying to learn. You're trying to connect. Collect more data. To learn about. If this makes sense. Externally. Internally. Kind of seeing the alignment there. I mean. If they're both aligned. Then you know that this is something. That you can continue doing. Where we are in a place. Also where it's so easy to. Judge. Our. Experiments. While they're kind of happening. And try to. Project out into. Oh wait. This is not going the way that I. Thought it would go. How can we. Be more detached. In the way we look at these. Experiments that we're running.

- [00:22:30](https://www.youtube.com/watch?v=OjdGeNAm930&t=1350) - Project out into. Oh wait. This is not going the way that I. Thought it would go. How can we. Be more detached. In the way we look at these. Experiments that we're running. Is there something that we can do there? This is actually an. In the inherent core. Part of running experiments. That's why you decide on the duration in advance. So you say. I'm going to do this thing. And I'm going to do it for this duration. And for that duration. I'm going to withhold judgment. Just like a scientist. A scientist is not going to start poking at the data. In the middle of the experiment. And if they don't like what they're seeing. They're stopping the experiment. Right. You have a protocol. That you write.

- [00:23:00](https://www.youtube.com/watch?v=OjdGeNAm930&t=1380) - A scientist is not going to start poking at the data. In the middle of the experiment. And if they don't like what they're seeing. They're stopping the experiment. Right. You have a protocol. That you write. At the beginning. And then you follow the protocol. You go through the experiment. And it's only at the end. That you analyze the data. And you see whether your hypothesis was correct or not. So it's the same. When you run experiments in your life. And your career. You decide on the duration. In advance. And you withhold judgment. And you might still experience some of the discomfort. The doubts. The anxieties. You might be feeling like. Oh. This is not really going in the direction I expected. But. This is my experiment.

- [00:23:30](https://www.youtube.com/watch?v=OjdGeNAm930&t=1410) - And you might still experience some of the discomfort. The doubts. The anxieties. You might be feeling like. Oh. This is not really going in the direction I expected. But. This is my experiment. And I committed to it for a month. So I'm going to finish it. And only when I'm done. I'm going to review the data. The experience. And see what I learned. And whether I want to keep going. Tweak it. Or pause it. And this is really how you can make sure. You make the right decision. And make the most of the experiment. And you learn from it. Because obviously. Especially if it's something quite new. There is going to be a little bit of friction at the beginning. It might be a little bit challenging. And there is going to be a temptation to stop. So pick your duration in advance.

- [00:24:00](https://www.youtube.com/watch?v=OjdGeNAm930&t=1440) - Especially if it's something quite new. There is going to be a little bit of friction at the beginning. It might be a little bit challenging. And there is going to be a temptation to stop. So pick your duration in advance. And stick to that duration. What's a lot of this. I think is also about how we learn. And in what settings we learn best. Is there anything from your research. That you have found about. How we can understand. The way that we are. Actually learning. What is optimal for one person. Versus the other. Is there any research on the way. To maximize our learning. Through those experiments. Because you could have a hypothesis. And you could run different experiments.

- [00:24:30](https://www.youtube.com/watch?v=OjdGeNAm930&t=1470) - Versus the other. Is there any research on the way. To maximize our learning. Through those experiments. Because you could have a hypothesis. And you could run different experiments. For the same. There could be different protocols. For example. To run certain hypotheses. Well in terms of protocols. Actually the format doesn't change. It's always. I will perform an action for a duration. That's the format. You don't need to change anything. You don't need to over complicate it. I think sometimes there is a. Again a temptation. To want to over optimize everything. And we end up creating. Those very complicated systems. And that is really not necessary. The basis of a tiny experiment. Is just to try something. An action for a specific duration.

- [00:25:00](https://www.youtube.com/watch?v=OjdGeNAm930&t=1500) - To want to over optimize everything. And we end up creating. Those very complicated systems. And that is really not necessary. The basis of a tiny experiment. Is just to try something. An action for a specific duration. And this is very aligned really. With how the brain learns. So the way we learn. And we explore the world in general. Is based on something. A thing neuroscientists call. The perception action cycle. And the way it works. Is that. Our brain perceives information. In the environment. Then based on that. Makes predictions. Then you take an action. And you see whether. The prediction you made. Was correct or not. And sometimes. It's correct. And that's great.

- [00:25:30](https://www.youtube.com/watch?v=OjdGeNAm930&t=1530) - Makes predictions. Then you take an action. And you see whether. The prediction you made. Was correct or not. And sometimes. It's correct. And that's great. And sometimes the prediction is wrong. But that's okay too. Your brain is going to take that new data. That new information. And is going to adjust. Its next prediction. And this is how you go through. The cycles. Those are really just cycles. Of experimentation also. That the brain is going through. And tiny experiments. Are that you can conduct. In your life. And work. And studies. Are really just more of a. Simple. Structured way. For you to go through. This perception action cycle. In a bit of a. More international way. And in terms of.

- [00:26:00](https://www.youtube.com/watch?v=OjdGeNAm930&t=1560) - And studies. Are really just more of a. Simple. Structured way. For you to go through. This perception action cycle. In a bit of a. More international way. And in terms of. I know that you journal. Pretty regularly. What has that changed. For you? Yeah so. Journaling is. A metacognitive practice. So metacognition. Is a. Means. Thinking about thinking. Scientists think that. Only humans. Are able to do this. So thinking. A lot of animals can do it. If you. Have a pet. A dog. A cat. Or any kind of pet. You know. That animals are able to think.

- [00:26:30](https://www.youtube.com/watch?v=OjdGeNAm930&t=1590) - Are able to do this. So thinking. A lot of animals can do it. If you. Have a pet. A dog. A cat. Or any kind of pet. You know. That animals are able to think. But thinking about thinking. Observing your own thoughts. Observing your own emotions. Asking. Why did I think. That thought. Why did I act in this way. Why did I feel the way. I feel. This is metacognitive. This is metacognition. And so far. Only humans. Have shown that they can do this. And journaling. Is actually a really great tool. To practice metacognition. It's a great way to take your thoughts. Out of your head. Put them in front of you.

- [00:27:00](https://www.youtube.com/watch?v=OjdGeNAm930&t=1620) - And journaling. Is actually a really great tool. To practice metacognition. It's a great way to take your thoughts. Out of your head. Put them in front of you. And observe them. You're creating that little. Observer. This distance. Where you can actually look at them. Work with them. Question them. And you asked earlier. About how we learn. It has been shown. There's a lot of research. Around this. Showing that. That metacognition. Actually helps you learn better. Because it helps you. Clarify your thinking. It helps you connect ideas. In a better way. And it helps you actually. Dig into. The why. The underlying assumptions. Behind your behaviors. So journaling is great.

- [00:27:30](https://www.youtube.com/watch?v=OjdGeNAm930&t=1650) - It helps you connect ideas. In a better way. And it helps you actually. Dig into. The why. The underlying assumptions. Behind your behaviors. So journaling is great. For people who don't like. Writing so much. There's something that's really great. About metacognition. Is that you can practice it. In lots of different ways. Having. An intentional. Coffee catch up. With a friend. Where you see. Say. Let me reflect. Like. Think out loud. About this problem. I'm facing at the moment. And I just want you to listen. And maybe ask a couple of questions. But that's it. Just listen. And maybe. Ask questions like. What do you mean by that? And why did it feel this way?

- [00:28:00](https://www.youtube.com/watch?v=OjdGeNAm930&t=1680) - About this problem. I'm facing at the moment. And I just want you to listen. And maybe ask a couple of questions. But that's it. Just listen. And maybe. Ask questions like. What do you mean by that? And why did it feel this way? That's a form of shared metacognition. That you can do with a friend. So journaling. Talking with a friend. Any kind of self-reflection. Where you really observe your own thoughts. Is going to help you. Make better decisions. But also learn better. On this topic. On metacognition. I was very curious about understanding. So when. When there's big transition. Periods in your life. Like a lot of these people. Students that are listening to this.

- [00:28:30](https://www.youtube.com/watch?v=OjdGeNAm930&t=1710) - I was very curious about understanding. So when. When there's big transition. Periods in your life. Like a lot of these people. Students that are listening to this. Are moving into some careers. That they think are interesting. And they're sort of interviewing. And talking to lots of people. Across different fields. How can we be more intentional. With our decision making process. For the kinds of. Because right now. It's a hypothesis of. Hey I think this is going to be interesting for me. Is there any way to be more intentional. About that decision. Without having actually been in it. Can we remove some of the learning. By thinking through it. Or do we have to be doing it.

- [00:29:00](https://www.youtube.com/watch?v=OjdGeNAm930&t=1740) - Is there any way to be more intentional. About that decision. Without having actually been in it. Can we remove some of the learning. By thinking through it. Or do we have to be doing it. To actually get to know exactly. I would say for. That for really big decisions. Like your career. Ideally you want to learn. Through actual experience. You can read a lot of information. Online. But that's never going to. Replace actual knowledge. Something you know. Because you've been through it. You've actually experienced. All of the nuances of the experience. So sometimes for. Not so important decisions. Relying on pure second hand.

- [00:29:30](https://www.youtube.com/watch?v=OjdGeNAm930&t=1770) - Because you've been through it. You've actually experienced. All of the nuances of the experience. So sometimes for. Not so important decisions. Relying on pure second hand. Type of information is fine. But you want the primary type of knowledge. If it's for a big decision like your career. The great thing though. Is that nowadays with the internet. You can reach out. To almost anyone. In any kind of job. And you can ask them. Can I shadow you for a week. Can I intern with you for a month. Can I. And I guarantee you. If you reach out to a few people. There will be at least one person. Who will say yes.

- [00:30:00](https://www.youtube.com/watch?v=OjdGeNAm930&t=1800) - Can I shadow you for a week. Can I intern with you for a month. Can I. And I guarantee you. If you reach out to a few people. There will be at least one person. Who will say yes. I'm not saying everybody will say yes. But it is worth. Reaching out to ask this. I have that every week in my inbox. I have people emailing me and asking. Can I intern at Nest Labs for a month. Can I write articles for Nest Labs. And I obviously cannot always say yes. Because it is work for me as well. To manage these people. And help them. But a lot of people will say yes. If they have the bandwidth. So I would say that. Yeah a lot of decisions can be based on pure. Information. But if it's your career you're thinking about.

- [00:30:30](https://www.youtube.com/watch?v=OjdGeNAm930&t=1830) - And help them. But a lot of people will say yes. If they have the bandwidth. So I would say that. Yeah a lot of decisions can be based on pure. Information. But if it's your career you're thinking about. It is worth it. To actually give it a try. Actually experiment with it. And collect your own data. Okay. And on this topic. This might be more tactical. But how can someone reach out. With an ask that's small enough. That lets them do these things. Like they were reaching out at Nest Labs. How can they navigate that conversation. To make it easier for the person. That they're reaching out to. To me it's really about. Making it very clear.

- [00:31:00](https://www.youtube.com/watch?v=OjdGeNAm930&t=1860) - Like they were reaching out at Nest Labs. How can they navigate that conversation. To make it easier for the person. That they're reaching out to. To me it's really about. Making it very clear. How this will also help me. And so I've had people say. I'll design three. Instagram posts for you. For example. Around these topics. And then. If you want to tell me about other things. I can help with. I will also be very happy to help with this. So that's one way. Showing that. Just providing value. And being helpful. And then in this way. When I read the email. I can already start thinking about. A lot of other ways. This person might be helpful. Another way that is essential.

- [00:31:30](https://www.youtube.com/watch?v=OjdGeNAm930&t=1890) - Showing that. Just providing value. And being helpful. And then in this way. When I read the email. I can already start thinking about. A lot of other ways. This person might be helpful. Another way that is essential. But you wouldn't believe the number of people. Who don't do it. Is just spending a little bit of time. Engaging with the work of the person. You're reaching out to. To me sometimes I receive emails. Where it's very obvious. That they. You copy pasted a template. Across a lot of people. But the road doesn't apply to me. It doesn't apply to Nest Labs. Doesn't apply to. All of the research. And the work that I'm doing. It's a kind of. You want to create a feeling of mutual respect. If I felt like someone took the time.

- [00:32:00](https://www.youtube.com/watch?v=OjdGeNAm930&t=1920) - Doesn't apply to. All of the research. And the work that I'm doing. It's a kind of. You want to create a feeling of mutual respect. If I felt like someone took the time. To write an email to me. Even if the answer is no. I will always take the time to reply. Because there's another human being. That spent a bit of time. Reading about my work. And crafted this email. For me another human being. And so I will take the time to reply. And it might mean no right now. But maybe in the future. You never know. But if I feel like the person. Just copy pasted something. And sent it to 50 people. Why would I waste my time. Replying to them. When they didn't take any time. To write to me.

- [00:32:30](https://www.youtube.com/watch?v=OjdGeNAm930&t=1950) - But if I feel like the person. Just copy pasted something. And sent it to 50 people. Why would I waste my time. Replying to them. When they didn't take any time. To write to me. So I would say those are the two main ingredients. Trying to provide value straight away. Or at least paint a clear picture. Of how you would provide value. Because emails that just say. I'd love to intern with you. I love your work. And it's more work for me. To really figure out how you can help me. Probably won't have time for this. And emails where I can tell you copy pasted it. This is also not going to. At least for me. I'm not saying this is universal advice. But I think for most people that would apply.

- [00:33:00](https://www.youtube.com/watch?v=OjdGeNAm930&t=1980) - Probably won't have time for this. And emails where I can tell you copy pasted it. This is also not going to. At least for me. I'm not saying this is universal advice. But I think for most people that would apply. Right. I want to touch a little bit more. On how you see the education landscape right now. Because you obviously work with a lot of companies. And help them from that angle. But what do you see are certain things. That we need to change about. How we've set up the learning outcomes. Or the way that colleges are set up right now. How can we improve. And build more of an experimental mindset. Within the college ecosystem. Do you have any thoughts on that? A lot of the problems I'm seeing today.

- [00:33:30](https://www.youtube.com/watch?v=OjdGeNAm930&t=2010) - Or the way that colleges are set up right now. How can we improve. And build more of an experimental mindset. Within the college ecosystem. Do you have any thoughts on that? A lot of the problems I'm seeing today. Are actually not new at all. Even when I was in university. Like 15 years ago now. It was already the case. That a lot of the things were being taught. As part of the official curriculum. Was already very outdated. Compared to what we needed. In the actual job market. And I think that probably has become even worse now. But that's not a new problem. And I think the solutions at the time. Still apply today. But probably in a way that is even more urgent. And important. To me when I went to university.

- [00:34:00](https://www.youtube.com/watch?v=OjdGeNAm930&t=2040) - And I think that probably has become even worse now. But that's not a new problem. And I think the solutions at the time. Still apply today. But probably in a way that is even more urgent. And important. To me when I went to university. The most value I got. Was from connecting with other people. Learning from other people. Learning together. Working on group projects. Being part of students organizations. Doing this kind of work. Those and learning in this way. Was a lot more valuable. When I started my career. Than anything that I studied. Through the official curriculum. So I would say. Still try and do well in your studies.

- [00:34:30](https://www.youtube.com/watch?v=OjdGeNAm930&t=2070) - When I started my career. Than anything that I studied. Through the official curriculum. So I would say. Still try and do well in your studies. Because a lot of employers. Are going to pay attention. To your grades. And so. And you're there. And especially in the US. Paying quite a bit of money. To get this education. So I would say absolutely. Try to get good grades. But also. Don't forget that. A lot of the long-term value. You're going to get from that experience. Is in the everything else. That happens outside of the classroom. The friends you're making. Which you might reconnect with. In three to four years.

- [00:35:00](https://www.youtube.com/watch?v=OjdGeNAm930&t=2100) - You're going to get from that experience. Is in the everything else. That happens outside of the classroom. The friends you're making. Which you might reconnect with. In three to four years. Because they're working on a really cool project. And you're going to get to collaborate together. The different skills you're going to pick up. Because you're working on a campaign. For a student's project. And you're going to get to collaborate. And you're going to get to collaborate. And you're going to get to collaborate. And you're going to learn how. To manage big projects. And collaborate with a team. And navigate uncertainty together. All of these things. These are the things you want to invest in. And you really don't want to ignore these. And leave uni with just your head. Full of facts that you learned. Through your curriculum.

- [00:35:30](https://www.youtube.com/watch?v=OjdGeNAm930&t=2130) - These are the things you want to invest in. And you really don't want to ignore these. And leave uni with just your head. Full of facts that you learned. Through your curriculum. And then none of the know-how. The learning how to learn. Which is what you need. In a world where everything is changing so fast. What's the. What's the most important skill. Or one to two skills you think are. Relevant for someone coming out of college now. That will help them for the future. Learning how to learn. That's all you need. I'm just seeing the pace of change in technology. Different industries being created overnight.

- [00:36:00](https://www.youtube.com/watch?v=OjdGeNAm930&t=2160) - That will help them for the future. Learning how to learn. That's all you need. I'm just seeing the pace of change in technology. Different industries being created overnight. Other industries disappearing overnight. If you want to be successful. You have to be able to teach yourself anything. Pretty quickly. And if possible. Developing. Enjoyment for it. Really actually enjoying learning new things. You can. Both learn how to learn. And also. Learn to fall in love with problems. With challenges. With questions and uncertainty. You're going to thrive.

- [00:36:30](https://www.youtube.com/watch?v=OjdGeNAm930&t=2190) - Both learn how to learn. And also. Learn to fall in love with problems. With challenges. With questions and uncertainty. You're going to thrive. You'll have nothing to worry about. When you say learning. How can we fall in love with the process of learning? By doing it. It's a practice. It's a really reinforcing. This feeling of. Facing something new. Difficult. Uncomfortable. The friction of exploring a new topic. A new idea. We know that. Just look back on things that you're able to do. With your eyes closed today. And just think back on the first time you did it. How difficult that was.

- [00:37:00](https://www.youtube.com/watch?v=OjdGeNAm930&t=2220) - The friction of exploring a new topic. A new idea. We know that. Just look back on things that you're able to do. With your eyes closed today. And just think back on the first time you did it. How difficult that was. And developing this love for this process. This struggle. And then the amazing payoff you get. When you get to a point. Where you look back. And you can see the progress that you've made. And how now this is something that you can do. Now you can write. Now you can code. Now you can edit videos. Now you can do all of these things. Now you can use this tool. When there was a point in your life. Where you could not do that. So it's really just through doing it. And it becomes a lot more easier to do it.

- [00:37:30](https://www.youtube.com/watch?v=OjdGeNAm930&t=2250) - Now you can edit videos. Now you can do all of these things. Now you can use this tool. When there was a point in your life. Where you could not do that. So it's really just through doing it. And it becomes a lot more easier to do it. And to get those reps in. If you pick topics. You're actually curious about. So instead of forcing yourself. To go through this struggle. Around things you don't care about. Where it's like the double arrow of suffering. Do it with things you're actually curious about. Then you're going to develop this. Taste for learning. And really enjoying. The effort of figuring it out. Last question for you on this is. When.

- [00:38:00](https://www.youtube.com/watch?v=OjdGeNAm930&t=2280) - Then you're going to develop this. Taste for learning. And really enjoying. The effort of figuring it out. Last question for you on this is. When. You talk about AI. What is. What do you see the relationship we will have. With AI. Going forward. I mean. You've talked about it being a. Like a person that you can. Sort of just brainstorm ideas with. Someone that's like a thought partner. Are there any more thoughts around that idea? Like how can we use AI. To benefit our learning? Yeah. I do think AI is an amazing thinking companion. AI never.

- [00:38:30](https://www.youtube.com/watch?v=OjdGeNAm930&t=2310) - Like how can we use AI. To benefit our learning? Yeah. I do think AI is an amazing thinking companion. AI never. Gets tired. Right. And so you can go as deep as you want. You can ask as many questions as you want. AI will never judge you either. Not that a teacher would judge you. But you might think they are. And you might be a bit shy. Asking certain questions. Especially if you have to do it in front of the entire classroom. You don't have that with AI. So you can obviously ask as many questions as you want. For as long as you want. You can also ask questions where. Generally. Your teacher would probably not be able to answer that question.

- [00:39:00](https://www.youtube.com/watch?v=OjdGeNAm930&t=2340) - So you can obviously ask as many questions as you want. For as long as you want. You can also ask questions where. Generally. Your teacher would probably not be able to answer that question. You can ask questions that are so weird. So niche. Asking about the intersection of two different subjects. Where there's probably no. Expert. Who would be able to answer that question. Or you would have a really hard time finding them. You can ask that question to AI. And so. In a way. I really think about it in the same way that. Wikipedia. Really allowed us to be more curious. About a lot more different topics. Because you could just pull out your phone. And have a quick look. When before.

- [00:39:30](https://www.youtube.com/watch?v=OjdGeNAm930&t=2370) - Really allowed us to be more curious. About a lot more different topics. Because you could just pull out your phone. And have a quick look. When before. You would have just forgotten about it. Right. You would have maybe thought. Oh yeah. I'll go check in the library. At some point. That would never happen. Decades ago. That's what would happen. Right. With Wikipedia. A lot of us started. Just checking it. Very quickly. Checking. What is that? What is that fact? What does that mean? And so. This is that. But on steroids now. That we have access to. Is there a part. Is there a discerning. There's some level of discernment. At the same time. Right. That that's needed. In the way that we. Take in this information.

- [00:40:00](https://www.youtube.com/watch?v=OjdGeNAm930&t=2400) - But on steroids now. That we have access to. Is there a part. Is there a discerning. There's some level of discernment. At the same time. Right. That that's needed. In the way that we. Take in this information. Because I mean. I see this already. All of us. UT Austin's pretty forward. In the way that. We have the perplexity. Chat GPT. Subscription for. For school. And so. People are using AI. But now. It's sort of this. Blind. Usage of it. Where. As soon as you get an assignment. It's copied and pasted. Into. Chat GPT. And. The answers. To those questions. That are in front of you. How can we get better. Discerning. What is. True. It's the same. With Wikipedia. Also. I feel like.

- [00:40:30](https://www.youtube.com/watch?v=OjdGeNAm930&t=2430) - The answers. To those questions. That are in front of you. How can we get better. Discerning. What is. True. It's the same. With Wikipedia. Also. I feel like. We're always. We're. We're. Quite philosophical. About it. When we don't have to. It's such a tool. It's. It's just a tool. It is also based on. On information. That was created. By humans. On the internet. So. Not all of it is true. Not all of it is correct. It's a starting point. It was the same. Again. When. I was. In university. 15 years ago. And that's when everybody. Started using Wikipedia. A lot of students. Thought they could be really smart. By copy pasting. Whatever. From there. And at first. It worked. And the.

- [00:41:00](https://www.youtube.com/watch?v=OjdGeNAm930&t=2460) - In university. 15 years ago. And that's when everybody. Started using Wikipedia. A lot of students. Thought they could be really smart. By copy pasting. Whatever. From there. And at first. It worked. And the. Because there was no system. For teachers. To check. Where the content came from. And then there were tools. To do this. And so. I think we're just going. Through the same cycle. And. In any case. You are always going. To have students. Who think. That the point. Of university. Is to just. Get through it. As quickly as possible. And they're going. To use AI. In a way. Where they can actually. Copy paste. Whatever. Save time. And not have to think. Very deeply. About whatever. They're studying. And they might. Actually be able.

- [00:41:30](https://www.youtube.com/watch?v=OjdGeNAm930&t=2490) - And they're going. To use AI. In a way. Where they can actually. Copy paste. Whatever. Save time. And not have to think. Very deeply. About whatever. They're studying. And they might. Actually be able. To cheat the system. In a way. That students have been doing. For thousands of years. As. Since school has existed. There were ways. To cheat the system. And students would use it. And then there are students. Who feel like. They're actually. Interested in the topic. And they actually. Want to learn. And that's why. They're here. They're actually. Dedicated. Dedicating many years. Of their lives. To learning. In university. And they will use AI. In a way. That is a lot more. Generative. As a companion. To go deeper. To think. In a more efficient way.

- [00:42:00](https://www.youtube.com/watch?v=OjdGeNAm930&t=2520) - Dedicating many years. Of their lives. To learning. In university. And they will use AI. In a way. That is a lot more. Generative. As a companion. To go deeper. To think. In a more efficient way. But not as a way. To bypass. The thinking. Completely. Awesome. Well. Thank you so much. Thank you. And Laura. Thank you for having me.

