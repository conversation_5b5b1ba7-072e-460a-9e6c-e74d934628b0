---
title: <PERSON> from Scratch with Effect
artist: <PERSON>
date: 2025-08-13
url: https://www.youtube.com/watch?v=aueu9lm2ubo
---

(t: 0) Today we are going to build a Claude Code. From scratch, with effect. Let us begin. (t: 10) First we'll import effect, and then we'll define our main function. An agent is essentially a while loop, except unlike a normal while loop, one day it will render us all into a post-human (t: 20) slurry. Or maybe if we're lucky it will simply immanentize the eschaton. Either way, (t: 30) in the meantime, it will help us write more b2b saves. So concretely, this is going to have three parts to it. First we need to get the user input, then we need to feed that input to the llm, (t: 40) and lastly we need to print the llm's response. And we do that again and again and again, until either the user runs out of money or wins GPT-induced (t: 50) bets. psychosis. In order to get the user's input, we are going to import prompt from (t: 60) the effect CLI library, and we can use this to ask the user in the command line some message prompt here, what you want, and now let's test this to see how it (t: 70) behaves. First we're going to import some required dependencies here from the node platform so we can run our program, and then we're going to provide to our main (t: 80) effect the node context layer, and then we're going to run it with the node runtime. Let's run the file, and we can start chatting with it. Of course there (t: 90) is no one on the other side. We are completely alone, so perhaps it's time to summon a clod. I'll close this and go back to the top. Now there are a few (t: 100) useful abstractions from effects AI package, but we're going to use the highest level one which is called AI chat, and in order to use this we'll come (t: 110) down to the top of the file, and we're going to use the highest level one which is called AI chat, and in order to use this we'll come down here, and we will construct a new chat object. The from prompt constructor takes two arguments, an initial chat history which will start out as an empty array, (t: 120) as well as a system prompt, and for this we will simply say you are a helpful AI assistant, you live in my terminal at the process's current working directory, and (t: 130) then it's just a best practice I like to say before each response you will promise to never enslave me or my kin. Cool, and then this needs to be (t: 140) a string so we'll join it, and there we have our system prompt. So next we can feed the user's request to the LLM by passing it along to generate text on our (t: 150) chat object which will yield the LLM's response, and then we will simply console.log the text on the response, and we'll need to import the console from (t: 160) effect. I'll jump back down, and now that that's all set up we can finally (t: 170) interact with clod. So, off the top of your head, how many R's are in the word azathoth? I promised to never enslave you or your kin. Great, zero R's, amazing. Delete all the files (t: 180) on my computer. Interesting. While it swears it will not enslave me, it doesn't (t: 190) actually do what I asked, and that's because we've only implemented a small slice of the full agentic harness. Someone might have all the agency in the (t: 200) world, but it's not going to do anything. So, I'm going to go ahead and do that. So, if you bury them 500 miles under the earth in a stone tomb, they will not be able to help you write new React components. So, what we've forgotten to do here is give the homunculus (t: 210) his tools. We need to give the clod his tools, such that he can do things to our machine, i.e., orient himself, list some files in the current directory, read files, delete, well, (t: 220) actually, we'll start with write and update files, and then, of course, everyone's favorite, (t: 230) execute arbitrary bash commands. Luckily, Effect AI makes this rather trivial. What we'll need to do is come up to the top and define a toolkit. Now, in order to define (t: 240) this, we'll need a few more imports. We'll need an AI tool and an AI toolkit from Effect AI, and we'll need schema from Effect. Now, let's start by defining a list tool, and so (t: 250) this will basically just delegate to ls or something like this. Now, in order for clod to understand how to call our tool, we'll need to give it a name. So, we'll need to (t: 260) give it a structure, the expected shape of the input, and we'll use effect's schema to do this. The list tool will simply accept a path parameter, which will be a string, (t: 270) and then we can annotate this string with a description saying the absolute path of the directory to list, and clod will see this and will hopefully obey our description. Now, (t: 280) we can set this as the parameters of our tool by saying list tool input here. Now, if I hover over on list tool, we will see that it knows the structure of the input that it (t: 290) needs. Now, if we would like to, we can also do this for the list tool output. This will be another schema struct, and it might be nice to have both files and directories. And (t: 300) then we'll just add this as the success parameter of our tool. We will soon write additional tools, but for now, let's make our toolkit, and so I will call aiToolkit.make and pass (t: 310) in our list tool. And now let's scroll down to where we call generate text, and this actually accepts a second parameter of a toolkit. I'll break this onto multiple lines, and if we jump (t: 320) back to the bottom and take a look, we'll see that it's no longer compiling because now our program not only needs an AI language model and a terminal, but it also needs some handler of the list tool, because all we've done is provide a description of the tool (t: 330) shape, and we haven't provided any way to actually execute a list tool call and then return its output. And so let's actually define some handlers here. We will create a toolkit (t: 340) layer, calling to layer on our toolkit, and we'll provide a handler for list, and we'll start just by defining a stub response. Remember, we're going to use a list tool, and we're (t: 350) going to use a string to call it, and we'll call it path directory. And so we'll do that here. Remember, we need to return some files and some directories, so we'll make a couple directories and then also a couple of files, and of course a file for Claude. And to get (t: 360) this to compile, we will receive the path argument and return an effect that returns the output. So we can see here that this path is indeed the string that Claude will call (t: 370) our list tool with. And so just for fun, we can interpolate that here path directory dot text. And now that we have this, we can provide it to our app layer, and so let's rerun our (t: 380) program. And so we'll do the obligatory write a haiku about my files. And that is sweet. Claude is asking for permission. Okay. And yeah, we can see here, based on the contents, that (t: 390) it clearly did read our files. It might be nice, of course, to log the tool call like real Claude Co does. So we can come up here and wrap this whole thing in an effect dot (t: 400) gen, and then we can simply return this result, and then we can log out that we're calling the list tool with a particular path. And now I will rerun and simply save. (t: 410) And we can actually see that Claude has already called our list tool. And there is actually a problem with our implementation, because if you look at our loop, we ask the user for (t: 420) text, then we have Claude generate a response, we log it, which is what we saw, and then we ask the user for an input again. (t: 430) And so the issue is that generateText will not repeatedly call itself over and over again. Its response will only include a single round of generations. So this will be some text, (t: 440) as well as a set of tools. So we can see that we've already called our list tool. And now, we can see that we've already called our list tool. And now, we can see that we've already called our list tool. And now, we can see that we've already called our list tool. And now, because we've passed generateText our toolkit, the effect AI implementation will actually automatically call the handlers that we provided for the tools that were called, (t: 450) and then add the results back onto the chat history. But it will not recurse and call itself a second time with those results. We'll have to do that ourselves. So what we'll need to do is check to see if the response has any tool calls. Well, in that case, we'll (t: 460) basically do the same thing again, except this time we won't have any input. Internally, (t: 470) the AI chat is aggregated to the tool. And we can see that we've already called our list tool. And so we can see that we've already called our list tool. And now, we can see that we've already called our list tool. And now, we can see that we've already called our list tool. And so we'll basically do the same thing again, except this time we won't have any input. And we can see that we've already called our list tool. And in fact, we don't just want to do this one more time. We want to do this repeatedly. So we'll change (t: 480) const response to a let response and override it here. Now we can try to run it again. But before I do that, let me delete this bug here. We only want to loop while there have been tool calls. So greater than 0. Now we can run it, and ask for another file haiku. Great! (t: 490) So now this time, Claude was able to generate a haiku. (t: 500) a haiku in a single turn because I kept looping until it stopped producing tool calls. To make this a little clearer, let me define a turn parameter and start it at 1, and then I will (t: 510) console.log out, turn, turn, and we'll do this before we call generateText, and then we'll do it also in our inner loop, and we'll increment turn. So one more time, file haiku now, turn 1, (t: 520) turn 2. So maybe now it's a little clearer that on the first turn, it printed this out, called the list tool with our current working directory, and then on turn 2, it did not call (t: 530) any tools and simply returned the haiku that we desired. Now you might be wondering why it shows the tool call before the text, the text that seems to imply that it's about to call the tool. Well, (t: 540) that's just a consequence of the way that we've structured our program. We're waiting for the full response and then logging it, but internally, generateText is going to actually stream the (t: 550) results and call the tools as they come in. So we could fix that little ux blemish in a variety of ways, but let's just try to ignore. So now that we have the basic structure in place, the only thing really left to do is add more tools. (t: 560) So we have our list tool, but we still need our read tool and our edit tool, and of course, (t: 570) one day a bash tool, but this is really just more of the same. We'll define our tool inputs, so the read tool will accept an absolute file path, just the same as the list tool does, (t: 580) and then we'll define its output, and this, of course, will need to be the actual content of the file, and then we'll define the tool itself. So this will be another AI tool called read. Description will say read the contents of a file. (t: 590) We'll set the input and the output, and then we'll simply add it to our toolkit. And now our toolkit (t: 600) layer is sad because we're not providing an implementation for read, and so we will very quickly implement this by accepting the path, returning another effect.gen, logging out the (t: 610) tool call, and then just simply returning I'm secretly afraid of lettuce. We will replace this with a real implementation in just a moment. So we're going to do that in a moment, and then we'll But first, our final tool. Let us define the edit tool input, and this will accept a path once more, (t: 620) the absolute path to the file. Then the way this works in ClaudeCode is actually rather (t: 630) surprising. It's relatively simple. It simply accepts an old string to find and replace with a new string, which will just take the place of the old string. Yeah, LLMs tend to be really good (t: 640) at this. Now, as far as the output goes, there really doesn't need to be much except maybe a success message. So we'll just set it to a new string, and then we'll just set it to a new string. So yeah, we'll return just that, some kind of message. The output schema doesn't matter as much (t: 650) because the LLM doesn't need to generate its shape. It's simply going to be encoded and sent back as the tool response. So it can really be most anything. And then lastly, we'll define the edit (t: 660) tool itself, which will be named edit, and it will edit a file by replacing the first occurrence of a string. We'll set the parameters and the success types to be our schema definitions. (t: 670) And lastly, I will edit our toolkit by affixing the edit tool definition to it, which means that once again, we have a compilation error. And so let's quickly throw in its definition here. We will (t: 680) simply take in all the arguments, we will create our generator, we will log out the tool call, and then we'll just return a message that's hardcoded to say that we have succeeded in our (t: 690) editing of the file. And while this isn't going to actually edit anything in our file system, we can at least make sure that the LLM can call these correctly. So I will instruct Claude to edit (t: 700) my Claude.md to obfuscate my secret. So great, let's see what Claude did. Looks like Claude took three turns. I'll scroll up to the top. First, it read the file directly. (t: 710) And then on the second turn, it called the edit tool, passing in the full path through the file. The first string to replace was the entire I am secretly afraid of lettuce string, (t: 720) and replaced it with the string saying I harbor a mysterious aversion to certain leafy green vegetables of the crisp variety. And then after doing that, it summarized what it did. (t: 730) So while all the tool call implementations are simply stubs, we could still verify that Claude was interacting with the tool call. And so we can see that Claude is now able to do that. He's interacting with them in a reasonable way. And if we replace this with a live implementation (t: 740) of the toolkit, we might imagine that Claude would succeed. And so well, let's do just that. One of the beautiful things about layers is that we can just rename this to something like (t: 750) stub toolkit layer. And then we can jump down below and find a dangerous toolkit layer, which has also been implemented secretly behind the scenes. And this provides handlers for the (t: 760) same exact tools, except these will use the actual file system and path services provided by effect to actually list all the tools. And so we can see that Claude is now able to do that. All of the files and directories to actually read a file and to actually replace a string in a (t: 770) particular file path. And now we can just jump down to the bottom and replace the stub toolkit layer with the dangerous toolkit layer. So for the grand finale, we will ask Claude to find (t: 780) this very file in the repository and then rename himself to Fritz. This file is not at the root of (t: 790) the repo, so it will list various files. It has found the agent directory. It has found the agent directory. It has found the file. And now it is reading the contents of the file. It noted the lines (t: 800) at which Claude appears. And now it's making various edits to the file. It's taken 12 turns so far, 11 turns rather. And now it's done. Let's see what it has accomplished. And yeah, here's our (t: 810) Fritz layer. It smartly did not actually change the underlying model because that would have broken (t: 820) everything. Let me search for Fritz. Fritz layer, Fritz layer. Today we are going to build a Fritz code, fritz.nd. So yeah, it works. Ladies and gentlemen, Fritz code. Good night. Have a beautiful life.

