#!/usr/bin/env python3
"""
YouTube URL Transcriber from Clipboard
=====================================

This script processes a single YouTube URL from the clipboard by:
1. Reading the URL from clipboard
2. Cleaning the URL (removing share parameters like ?si=...)
3. Downloading the audio file using yt-dlp
4. Creating a timestamped transcript using Whisper AI

Combines functionality from:
- 2_create_audio_files.py (audio downloading)
- 3_create_transcripts.py (transcription)

Requirements:
- yt-dlp: pip install yt-dlp
- mutagen: pip install mutagen
- openai-whisper: pip install openai-whisper
- pyperclip: pip install pyperclip

Author: Generated for YouTube Transcript Manager
"""

import os
import re
import sys
import time
import json
import subprocess
import logging
from pathlib import Path
from datetime import datetime
from typing import Optional, Dict, Any

try:
    import yt_dlp
    from mutagen.mp4 import MP4, MP4FreeForm
    import whisper
    import pyperclip
except ImportError as e:
    print(f"Missing required package: {e}")
    print("Please install required packages:")
    print("pip install yt-dlp mutagen openai-whisper pyperclip")
    sys.exit(1)

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[logging.StreamHandler()]
)

# Configuration from both scripts
AUDIO_FOLDER_NAME = r"D:\Scrapbook\202506150844 Audio-Only"
DELAY_BETWEEN_DOWNLOADS = 2
CHUNK_SECONDS = 30
WHISPER_MODEL = "medium"
PADDING_SECONDS = 5.0


def clean_youtube_url(url: str) -> str:
    """Remove share parameters and other unwanted parameters from YouTube URL."""
    # Remove share parameter (?si=...) and other tracking parameters
    cleaned_url = re.sub(r'[?&]si=[^&]*', '', url)
    cleaned_url = re.sub(r'[?&]utm_[^&]*', '', cleaned_url)
    cleaned_url = re.sub(r'[?&]fbclid=[^&]*', '', cleaned_url)
    
    # Clean up any trailing ? or &
    cleaned_url = re.sub(r'[?&]$', '', cleaned_url)
    
    return cleaned_url


def get_clipboard_url() -> Optional[str]:
    """Get YouTube URL from clipboard."""
    try:
        clipboard_content = pyperclip.paste().strip()
        
        # Check if it looks like a YouTube URL
        youtube_patterns = [
            r'https?://(?:www\.)?youtube\.com/watch\?v=',
            r'https?://youtu\.be/',
            r'https?://(?:www\.)?youtube\.com/embed/'
        ]
        
        for pattern in youtube_patterns:
            if re.search(pattern, clipboard_content):
                return clean_youtube_url(clipboard_content)
        
        print(f"❌ Clipboard content doesn't appear to be a YouTube URL: {clipboard_content[:100]}...")
        return None
        
    except Exception as e:
        print(f"❌ Error reading clipboard: {e}")
        return None


def extract_youtube_id(url: str) -> Optional[str]:
    """Extract YouTube video ID from various URL formats."""
    patterns = [
        r'(?:youtube\.com/watch\?v=|youtu\.be/|youtube\.com/embed/)([a-zA-Z0-9_-]{11})',
        r'youtube\.com/watch\?.*v=([a-zA-Z0-9_-]{11})',
    ]
    
    for pattern in patterns:
        match = re.search(pattern, url)
        if match:
            return match.group(1)
    return None


def sanitize_filename(filename: str) -> str:
    """Sanitize filename for safe file system usage."""
    invalid_chars = '<>:"/\\|?*'
    for char in invalid_chars:
        filename = filename.replace(char, '_')
    
    filename = re.sub(r'\s+', ' ', filename).strip()
    if len(filename) > 200:
        filename = filename[:200].strip()
    
    return filename


def create_filename_with_date_and_id(title: str, youtube_id: str, upload_date: Optional[str] = None) -> str:
    """Create filename in format: YYYYMMDD - YouTube ID - Video Title"""
    if upload_date and len(upload_date) == 8:
        date_str = upload_date
    else:
        date_str = datetime.now().strftime('%Y%m%d')
    
    sanitized_title = sanitize_filename(title)
    filename = f"{date_str} - {youtube_id} - {sanitized_title}"
    
    if len(filename) > 200:
        fixed_part_len = len(f"{date_str} - {youtube_id} - ")
        available_for_title = 200 - fixed_part_len
        if available_for_title > 0:
            truncated_title = sanitized_title[:available_for_title].strip()
            filename = f"{date_str} - {youtube_id} - {truncated_title}"
        else:
            filename = f"{date_str} - {youtube_id}"
    
    return filename


def create_audio_folder() -> Optional[Path]:
    """Create audio folder if it doesn't exist."""
    audio_folder = Path(AUDIO_FOLDER_NAME)
    if not audio_folder.exists():
        try:
            audio_folder.mkdir(parents=True)
            print(f"Created audio folder: {audio_folder}")
        except OSError as e:
            print(f"Error creating audio folder {audio_folder}: {e}")
            return None
    return audio_folder


def get_video_metadata(url: str) -> Optional[Dict[str, Any]]:
    """Get video metadata using yt-dlp without downloading."""
    ydl_opts = {
        'quiet': True,
        'no_warnings': True,
        'extract_flat': False,
    }
    
    try:
        with yt_dlp.YoutubeDL(ydl_opts) as ydl:
            info = ydl.extract_info(url, download=False)
            return {
                'title': info.get('title', 'Unknown Title'),
                'uploader': info.get('uploader', 'Unknown Channel'),
                'upload_date': info.get('upload_date', ''),
                'url': url
            }
    except Exception as e:
        print(f"Warning: Could not extract metadata for {url}: {e}")
        return None


def add_metadata_to_file(file_path: Path, metadata: Dict[str, Any]) -> bool:
    """Add metadata to audio file using FFmpeg and mutagen."""
    try:
        upload_date = metadata.get('upload_date', '')
        if upload_date and len(upload_date) == 8:
            formatted_date = f"{upload_date[:4]}-{upload_date[4:6]}-{upload_date[6:8]}"
        else:
            formatted_date = upload_date

        base_name = file_path.stem
        temp_file = file_path.parent / f"{base_name}_temp.m4a"

        cmd = [
            'ffmpeg',
            '-i', str(file_path),
            '-c', 'copy',
            '-metadata', f'title={metadata["title"]}',
            '-metadata', f'artist={metadata["uploader"]}',
            '-metadata', f'date={formatted_date}',
            '-metadata', f'comment=Source: {metadata["url"]}',
            '-f', 'mp4',
            '-y',
            str(temp_file)
        ]

        result = subprocess.run(cmd, capture_output=True, text=True)

        if result.returncode == 0:
            temp_file.replace(file_path)
            print(f"   ✅ Added standard metadata with FFmpeg")
        else:
            print(f"   ⚠️  FFmpeg error: {result.stderr}")
            if temp_file.exists():
                temp_file.unlink()

        # Add URL metadata using mutagen
        audio_file = MP4(str(file_path))
        audio_file['----:com.apple.iTunes:URL'] = [MP4FreeForm(metadata["url"].encode('utf-8'))]
        
        if '©cmt' not in audio_file:
            audio_file['©cmt'] = [f"Source: {metadata['url']}"]
        
        if '©nam' not in audio_file and metadata["title"]:
            audio_file['©nam'] = [metadata["title"]]
        
        audio_file.save()
        print(f"   ✅ Added URL metadata using mutagen")
        return True

    except Exception as e:
        print(f"   ⚠️  Error adding metadata: {e}")
        return False


def download_youtube_audio(url: str, audio_folder: Path) -> Optional[Path]:
    """Download audio from YouTube URL using yt-dlp and add metadata."""
    youtube_id = extract_youtube_id(url)
    if not youtube_id:
        print(f"❌ Could not extract YouTube ID from URL: {url}")
        return None

    print(f"🔍 Getting metadata for video...")
    metadata = get_video_metadata(url)
    if not metadata:
        print(f"⚠️  Could not get metadata, using defaults...")
        metadata = {
            'title': f'YouTube Video {youtube_id}',
            'uploader': 'Unknown Channel',
            'upload_date': '',
            'url': url
        }

    actual_title = metadata['title']
    new_filename = create_filename_with_date_and_id(
        actual_title, 
        youtube_id, 
        metadata.get('upload_date')
    )

    # Check if file already exists
    expected_path = audio_folder / f"{new_filename}.m4a"
    if expected_path.exists():
        print(f"⏭️  Audio file already exists: {expected_path.name}")
        return expected_path

    ydl_opts = {
        'format': 'm4a/bestaudio/best',
        'postprocessors': [{
            'key': 'FFmpegExtractAudio',
            'preferredcodec': 'm4a',
        }],
        'outtmpl': str(audio_folder / f'{new_filename}.%(ext)s'),
        'noplaylist': True,
        'quiet': False,
        'no_warnings': False,
        'retries': 3,
        'fragment_retries': 3,
    }

    print(f"🎵 Downloading: {actual_title}")
    print(f"   Channel: {metadata['uploader']}")
    print(f"   YouTube ID: {youtube_id}")
    print(f"   Filename: {new_filename}.m4a")

    try:
        with yt_dlp.YoutubeDL(ydl_opts) as ydl:
            ydl.download([url])
        
        print(f"✅ Successfully downloaded: {actual_title}")
        
        # Wait for file to be fully written
        for i in range(10):
            if expected_path.exists():
                time.sleep(1)
                break
            time.sleep(1)
        
        # Add metadata to the file
        if expected_path.exists():
            print(f"📝 Adding metadata...")
            add_metadata_to_file(expected_path, metadata)
            return expected_path
        else:
            print(f"⚠️  Could not find downloaded file: {expected_path}")
            return None
            
    except Exception as e:
        print(f"❌ Download error: {e}")
        return None


def format_time(seconds: float) -> str:
    """Convert seconds to HH:MM:SS format."""
    try:
        if seconds < 0:
            seconds = 0
        integer_seconds = int(seconds)
        hours, remainder = divmod(integer_seconds, 3600)
        minutes, secs = divmod(remainder, 60)
        return f'{hours:02}:{minutes:02}:{secs:02}'
    except:
        return '00:00:00'


def create_timestamp_url(base_url: str, seconds: float) -> str:
    """Create a timestamped YouTube URL."""
    try:
        timestamp = int(seconds)
        if base_url and 'youtu' in base_url:
            if '&t=' in base_url or '?t=' in base_url:
                base_url = re.sub(r'[&?]t=\d+', '', base_url)
            separator = '&' if '?' in base_url else '?'
            return f"{base_url}{separator}t={timestamp}"
        else:
            return f"#t={timestamp}"
    except:
        return "#"


def get_audio_metadata(file_path: Path) -> Dict[str, Any]:
    """Extract metadata from audio file using FFprobe."""
    try:
        cmd = [
            'ffprobe',
            '-v', 'quiet',
            '-print_format', 'json',
            '-show_format',
            '-show_streams',
            str(file_path)
        ]

        result = subprocess.run(cmd, capture_output=True, text=True)

        if result.returncode == 0:
            metadata_info = json.loads(result.stdout)
            format_info = metadata_info.get('format', {})
            tags = format_info.get('tags', {})

            normalized_tags = {}
            for key, value in tags.items():
                normalized_tags[key.lower()] = value

            metadata = {
                'title': normalized_tags.get('title', 'Unknown Title'),
                'artist': normalized_tags.get('artist', 'Unknown Artist'),
                'date': normalized_tags.get('date', 'Unknown Date'),
                'url': normalized_tags.get('url', ''),
            }

            # Try to get URL from mutagen if available
            if str(file_path).lower().endswith('.m4a'):
                try:
                    audio_file = MP4(str(file_path))
                    url_field = audio_file.get('----:com.apple.iTunes:URL')
                    if url_field:
                        custom_url = url_field[0].decode('utf-8') if url_field[0] else ''
                        if custom_url:
                            metadata['url'] = custom_url
                except Exception:
                    pass

            return {
                'success': True,
                'metadata': metadata
            }
        else:
            return {
                'success': False,
                'error': f"FFprobe error: {result.stderr}",
                'metadata': {
                    'title': 'Unknown Title',
                    'artist': 'Unknown Artist',
                    'date': 'Unknown Date',
                    'url': ''
                }
            }

    except Exception as e:
        return {
            'success': False,
            'error': f"Unexpected error: {e}",
            'metadata': {
                'title': 'Unknown Title',
                'artist': 'Unknown Artist',
                'date': 'Unknown Date',
                'url': ''
            }
        }


def transcribe_audio_file(audio_path: Path, url: str, video_id: str, model) -> Optional[Path]:
    """Transcribe audio file using Whisper."""
    print(f"🎵 Transcribing: {audio_path.name}")

    try:
        if not audio_path.exists():
            print(f"❌ Audio file does not exist: {audio_path}")
            return None

        file_size = audio_path.stat().st_size
        if file_size == 0:
            print(f"❌ Audio file is empty: {audio_path}")
            return None

        print(f"   File size: {file_size:,} bytes")

        # Get metadata from audio file
        metadata_result = get_audio_metadata(audio_path)
        if metadata_result['success']:
            metadata = metadata_result['metadata']
            print(f"   📋 Using metadata from audio file: {metadata['title']}")
        else:
            metadata = {
                'title': f"YouTube Video {video_id}",
                'artist': 'YouTube',
                'date': 'Unknown Date',
                'url': url
            }
            print(f"   ⚠️  Could not read metadata, using fallback")

        # Transcribe using Whisper
        print("   🔄 Starting Whisper transcription...")
        result = model.transcribe(str(audio_path))

        segments = result.get('segments', [])
        text = result.get('text', '').strip()

        print(f"   ✅ Transcription completed - {len(segments)} segments, {len(text)} characters")

        # Create transcript filename
        date_str = metadata.get('date', 'Unknown Date')
        if date_str and len(date_str) >= 8 and date_str.replace('-', '').isdigit():
            clean_date = date_str.replace('-', '')
            if len(clean_date) >= 8:
                formatted_date = clean_date[:8]
            else:
                formatted_date = "00000000"
        else:
            formatted_date = "00000000"
        
        safe_title = sanitize_filename(metadata['title'])
        filename = f"{formatted_date} - {video_id} - {safe_title}.md"
        
        # Create transcript in the specified areas/none directory
        transcript_dir = Path(r"C:\Users\<USER>\Documents\HEW Notes\Transcript Book\areas\none")
        transcript_dir.mkdir(parents=True, exist_ok=True)
        transcript_path = transcript_dir / filename

        print(f"   💾 Writing transcript to: {transcript_path.name}")

        with open(transcript_path, 'w', encoding='utf-8') as f:
            # Write YAML front matter
            f.write("---\n")
            f.write(f"title: {metadata['title']}\n")
            f.write(f"artist: {metadata['artist']}\n")
            f.write(f"date: {metadata['date']}\n")
            f.write(f"url: {metadata['url']}\n")
            f.write("---\n\n")

            if not segments:
                print("   ⚠️  No speech segments detected, creating placeholder entry")
                placeholder_url = create_timestamp_url(metadata['url'], 0)
                f.write(f"- [00:00:00]({placeholder_url}) - No speech detected\n\n")
            else:
                print(f"   📝 Processing {len(segments)} speech segments into {CHUNK_SECONDS}-second chunks")

                # Determine total duration for chunking
                max_segment_end_time = 0
                if segments:
                    valid_end_times = [s.get('end') for s in segments if s.get('end') is not None]
                    if valid_end_times:
                        max_segment_end_time = max(valid_end_times)
                    else:
                        valid_start_times = [s.get('start') for s in segments if s.get('start') is not None]
                        if valid_start_times:
                            max_segment_end_time = max(valid_start_times)

                if max_segment_end_time == 0:
                    max_segment_end_time = CHUNK_SECONDS

                num_total_chunks = (int(max_segment_end_time) // CHUNK_SECONDS) + 1
                print(f"   📊 Creating {num_total_chunks} chunks of {CHUNK_SECONDS} seconds each")

                for i in range(num_total_chunks):
                    chunk_start_seconds = float(i * CHUNK_SECONDS)
                    chunk_end_seconds = float((i + 1) * CHUNK_SECONDS)

                    extended_start = max(0, chunk_start_seconds - PADDING_SECONDS)
                    extended_end = chunk_end_seconds + PADDING_SECONDS

                    current_chunk_texts = []

                    for segment in segments:
                        seg_start = segment.get('start')
                        seg_end = segment.get('end')
                        text_content = segment.get('text', '').strip()

                        if seg_start is not None and seg_end is not None and text_content:
                            if ((extended_start <= seg_start < extended_end) or
                                (extended_start <= seg_end < extended_end) or
                                (seg_start <= extended_start and seg_end >= extended_end)):
                                current_chunk_texts.append(text_content)

                    full_text_for_chunk = " ".join(current_chunk_texts).strip()

                    if full_text_for_chunk:
                        timestamp = format_time(chunk_start_seconds)
                        timestamp_url = create_timestamp_url(metadata['url'], chunk_start_seconds)
                        f.write(f"- [{timestamp}]({timestamp_url}) - {full_text_for_chunk}\n\n")

        print(f"   ✅ Transcript created successfully: {transcript_path.name}")
        return transcript_path

    except Exception as e:
        print(f"❌ Transcription error for {audio_path.name}: {e}")
        logging.error(f"Transcription error for {audio_path}: {e}")
        return None


def main():
    """Main function to process YouTube URL from clipboard."""
    print("YouTube URL Transcriber from Clipboard")
    print("=" * 50)
    
    # Step 1: Get YouTube URL from clipboard
    print("📋 Reading YouTube URL from clipboard...")
    url = get_clipboard_url()
    if not url:
        print("❌ No valid YouTube URL found in clipboard")
        input("\nPress Enter to exit...")
        return
    
    print(f"✅ Found YouTube URL: {url}")
    
    # Extract video ID
    video_id = extract_youtube_id(url)
    if not video_id:
        print(f"❌ Could not extract video ID from URL")
        input("\nPress Enter to exit...")
        return
    
    print(f"🎯 Video ID: {video_id}")
    
    # Step 2: Create audio folder
    audio_folder = create_audio_folder()
    if not audio_folder:
        print("❌ Could not create audio folder")
        input("\nPress Enter to exit...")
        return
    
    print(f"📁 Audio folder: {audio_folder}")
    
    # Step 3: Download audio
    print(f"\n🎵 Downloading audio...")
    print("-" * 30)
    audio_path = download_youtube_audio(url, audio_folder)
    if not audio_path:
        print("❌ Audio download failed")
        input("\nPress Enter to exit...")
        return
    
    print(f"✅ Audio downloaded: {audio_path.name}")
    
    # Step 4: Load Whisper model
    print(f"\n🔄 Loading Whisper model: {WHISPER_MODEL}")
    print("   ⏳ This may take some time for larger models...")
    try:
        model = whisper.load_model(WHISPER_MODEL)
        print("✅ Whisper model loaded successfully")
    except Exception as e:
        print(f"❌ Error loading Whisper model: {e}")
        print("   💡 Try using a smaller model like 'base' if you encounter memory issues")
        input("\nPress Enter to exit...")
        return
    
    # Step 5: Transcribe audio
    print(f"\n📝 Creating transcript...")
    print("-" * 30)
    transcript_path = transcribe_audio_file(audio_path, url, video_id, model)
    
    # Summary
    print("\n" + "=" * 50)
    print("PROCESSING SUMMARY")
    print("=" * 50)
    print(f"🔗 Original URL: {url}")
    print(f"🎯 Video ID: {video_id}")
    print(f"🎵 Audio file: {audio_path.name}")
    
    if transcript_path:
        print(f"📝 Transcript: {transcript_path.name}")
        print(f"📁 Transcript location: {transcript_path.parent}")
        print(f"\n🎉 Successfully processed YouTube video!")
        print(f"📝 Transcript uses {CHUNK_SECONDS}-second chunks with {PADDING_SECONDS}-second padding")
        
        # Open the transcript in notepad
        try:
            subprocess.run(['notepad.exe', str(transcript_path)], check=False)
            print(f"📄 Opened transcript in Notepad")
        except Exception as e:
            print(f"⚠️  Could not open in Notepad: {e}")
    else:
        print(f"❌ Transcript creation failed")
    
    input("\nPress Enter to exit...")


if __name__ == "__main__":
    main()