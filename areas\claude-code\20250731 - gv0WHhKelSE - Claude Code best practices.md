---
title: <PERSON> best practices
artist: Anthropic
date: 2025-07-31
url: https://www.youtube.com/watch?v=gv0WHhKelSE
---

(t: 0) . Let's get started. Welcome everyone to Cloud Code best practices. (t: 10) In this talk, I'm going to talk about what Cloud Code is at a high level. Then we'll peer under the hood a little bit to understand how Cloud Code works. (t: 20) Then knowing that, because it's useful to know how your tools work, we're going to talk about good use cases for Cloud Code, and also best practices we've figured out both (t: 30) internally and from our users for getting the most out of this tool. But before I get started, I'd like to introduce myself real quick (t: 40) and talk about how I ended up on the stage. My name's <PERSON>, and I joined Anthropic about a year and a half ago to help start up a team we call Applied AI. (t: 50) It's the Applied AI's mission, our team's mission is to help our customers and partners build great products and features on top of Cloud. So what that really means is I spend a lot of my day (t: 60) prompting Cloud to get the absolute best outputs out of these models. That said, I also love to code, (t: 70) and I'm definitely one of those coders that starts a lot of projects, has some crazy idea, and then just never finishes them. So I have this graveyard of just like code that I started, never really finished, but I'm always spinning new things up. (t: 80) Late last year, I was in Slack and I was hearing about this new tool that a few people are using. They were saying it was really cool. And so on a Friday night, I downloaded the tool that (t: 90) would become Cloud Code, and I threw it at this kind of new note-taking app that I wanted to build. And like that whole weekend just kind of totally (t: 100) changed the way that I code and think about software engineering. I was carrying around my laptop with me all weekend. I was super addicted to just watching Cloud Code work. And I would press Enter, and I'd switch over to my browser (t: 110) and refresh, and I watched this huge, powerful application come together in front of my eyes. And I got way farther into this thing than I ever (t: 120) would have on my own, and it just blew my mind. And while I was doing this, I was a little worried. I was like, you know, I kind of know how these things work. So I'm like, man, I'm using a lot of tokens. (t: 130) I hope I don't get in trouble or anyone notices I'm not really contributing to anthropic code. But what I didn't know is that the Cloud Code team had built this internal leaderboard tracking how much all the (t: 140) anthropic employees were using this. And over the weekend, I had shot to the top. And so through that, I got to meet Boris and Kat (t: 150) and some of the early Cloud Code team. And I was able to start talking to them and say, hey, I love this tool. I also know a lot about prompting. Can I help you all out? (t: 160) And so through that, I got involved. And now I'm one of the core contributors on the team. And I do a lot of, I work a lot on the prompting, the system prompts, how the tools work, the tool descriptions and tool (t: 170) results, as well as I work on how we evaluate this tool. So when we think about changing the prompts, how do we make? How do we know we made things better or the same (t: 180) and we didn't totally ruin Cloud Code? So with that said, let's kind of dive in. So here's my current mental model of Cloud Code and how I describe it to people when people ask me. (t: 190) Cloud Code is like that coworker that does everything on the terminal. It's the sort of person that just never touches the GUI. (t: 200) They're a whiz. I think of when I was a junior engineer, I had this mentor. And I would walk over to his desk and I would say, hey, Tony, can you help me with this bug? And he would whip it over his terminal. (t: 210) And he'd be doing all these crazy bash commands and changing things around in Vim. And I'd always walk away thinking, wow, that was crazy. I should learn how to do that. I never did. (t: 220) But having Cloud Code on your computer is kind of like having Tony next to you all the time. (t: 226) So how does Cloud Code kind of work under the hood? (t: 230) At Anthropic, we try to always just, like, do what we call the simple thing that works. (t: 240) And what that means for Cloud Code is it's what we would consider a very pure agent. And Anthropic, when we talk about agents, what we really mean is some instructions, (t: 250) some powerful tools, and you let the model just run in a loop until it decides it's done. And that's really what Cloud Code is. (t: 260) So it's tools, powerful tools, and the tools that someone that was really good at a terminal would be able to use. Tools to create and edit files, to use the terminal, (t: 270) and then you can also do things like pull in other things with MCP. Now, on top of that, there's how Cloud understands the code base. (t: 280) And if you're going to build a coding agent or a coding tool a year ago, you'd probably have ideas like, well, OK, I'm going to get this user message about something (t: 290) about this code base. And I'll need to figure out which files are relevant. So maybe I'll, like, index the whole code base and embed it and do this fancy, like, kind of rag retrieval thing. (t: 300) That is not how Cloud Code works. We don't do any sort of indexing. Instead, Cloud kind of explores and understands the code base how you, if you were new to a team and new to a code base, (t: 310) would explore a code base. And that is through agentic search. It's the same sort of search tools you or I would use, things like glob and grep and find. (t: 320) And it can work its way through a code base and understand what's going on. And when we talk about agentic search, that really means the model can go do some searches. And then it can look at the results and say, hmm, (t: 330) maybe I need to figure out a few more things. I'm going to go do some more searching and then come back. And then on top of these primitives, on top of this agent, we have a few things. (t: 340) We have a very nice, light UI layer where you get to watch Cloud Code work. You see all the text fly by. And we have this nice permission system (t: 350) that allows the agent to work and kind of forces the human to butt in when the agent is doing something dangerous. And then on top of that, we also care a lot (t: 360) about security in this tool. And so because Cloud Code is just such a lightweight kind of layer on top of the model, and the fact that our model is available not just behind Anthropic APIs, (t: 370) but also with our cloud providers, AWS and GCP, it's very easy and native to point Cloud Code at one of these other services if you feel more comfortable (t: 380) consuming Cloud that way. (t: 383) A lot of people ask me, hey, Kel, what can I use Cloud Code (t: 390) for? What is it good at? Where is it interesting? And the reality is it's kind of great at everything. So let's start with discovery. (t: 400) Oftentimes in your career, you will be dropped into a new code base, whether that means you're switching teams, you're switching companies. I don't know. You're starting to work on some sort of open source project. (t: 410) And probably when you're first getting started and getting familiar, you're not very productive, because you're just trying to figure out, where things are in the code base, what patterns the team is (t: 420) using, things like that. And Cloud Code can kind of help supercharge that onboarding process. You can ask Cloud, hey, where is this feature implemented? Or since it's great at the terminal, you can say, hey, (t: 430) look at this file, and look at the git history, and just kind of tell me a story about how this code has changed over the past couple of weeks. (t: 440) One thing you can use Cloud Code for, and I think this is underrated, is instead of just diving in and starting to work. You can use Cloud Code as a thought partner. So oftentimes, when I'm working with Cloud, (t: 450) and I want to implement a feature, or we're going to change something up, I'll open up Cloud, and I'll say, hey, Cloud, I'm thinking about implementing this feature. Can you just kind of search around and kind of figure out (t: 460) how we would do it, and maybe report back with two or three different options? Don't start working. Don't start writing any files yet. And Cloud will go off and use those agentic search (t: 470) capabilities and come back with a few ideas. And then I can work with Cloud. I can work with Cloud to kind of validate things. And then we can jump into the project. (t: 480) Of course, Cloud Code is great at building and writing code. And I would say this on two different fronts. One, it can do the zero to one sort of stuff. You drop it in an empty directory, and you say, hey, (t: 490) build me an app. Build me a game. That demos very well. It's very fun to do. It's very gratifying. Of course, in reality, what really matters is, is Cloud Code good working in existing code bases? (t: 500) And this is primarily what we focus on. And the Cloud Code team, we have in our code base (t: 510) abnormally high, I would say, unit test coverage. And that's because Cloud Code makes it so easy and just straightforward to add unit tests. (t: 520) So we have great code coverage. And then the other thing we have in Cloud Code, in our own code base, is we have great commits and PR messages. Because when we finish working, we'll just say, hey, Cloud, (t: 530) write the commit for me. Write the PR message for me. We also see great opportunities. We have great opportunities to use Cloud Code in the deployments (t: 540) and in other parts of the lifecycle. And a few other people have talked about this, but this is using the Cloud Code SDK. So using it headlessly, using it programmatically, (t: 550) being able to sprinkle in a coding agent anywhere. And so that's things like sprinkling it into CI, CD to use it in GitHub, for instance, (t: 560) to help people programmatically. And then finally. It's great with support and scale. It can help you debug errors faster. (t: 570) One thing that we saw when we started giving Cloud Code to customers and talking to them about it, we didn't totally predict this. Or potential customers said, hey, we've (t: 580) been putting off this large code base migration. People that are on old versions of Java trying to get into a new one. Or a team that's on PHP and they're (t: 590) trying to get to React or Angular. We've talked to multiple teams like this. And having a tool like Cloud Code, makes projects like that a little more digestible when you go to your team and you say, hey, (t: 600) we're going to spend a month refactoring or rewriting large parts of the code base. And then on top of that, and this kind of matters across all these, is once again, remember, (t: 610) Cloud is great at the terminal. And that means it's going to be great at all those different CLI tools, things like Git, Docker, BigQuery, things like that. (t: 620) I never have to worry about, oh, I'm going to get myself, how do I get myself out of this sticky rebate? I'll just fire up Cloud Code and tell it the situation and be like, hey, can you fix this for me? (t: 630) It's incredible. Now, let's talk about best practices. And the first one is not going to be a surprise. But the first one is use Cloud.md files. (t: 640) So remember that Cloud Code, like I said, is an agent. And it has some tools. It has some lightweight instructions in the prompt. (t: 650) But it doesn't really have memory. And so the main way we share states, across kind of sessions, or across our team, when we fire up Cloud Code in the same code base (t: 660) over and over again, is this Cloud.md file. So when we start Cloud, what happens is if there's this Cloud.md file in the working directory, (t: 670) it's just plopped into context. It's plopped into the prompt. And basically what it says is, hey, Cloud, by the way, these are important instructions the developer left for you. Be sure to pay close attention to this. (t: 680) And there's various places you can put the Cloud.md file. You can put it in a project. And check it in so all your teammates share it. You could put one in your home directory (t: 690) if there's things you just want Cloud to always know about, regardless of what you're working on. And the things you put in here are things like, hey, by the way, maybe this is how you run the unit tests. (t: 700) Or just so you know, to make kind of your searching and life easier, here's just like an overview of kind of how this project is laid out, where the tests live, what different modules are, things like that. (t: 710) Or here's our style guide. All sorts of things like that to just make Cloud's life a bit easier. And you can build these things up over time. (t: 720) The other thing you can do which is important is permission management. When you're running Cloud Code, there's all sorts of different kind of permission things flying by. Kind of out of the box what happens when you start our tool (t: 730) is for read actions. If Cloud is searching or reading, we just let it go. But once it starts writing or running bash commands (t: 740) or doing things that could change stuff on your machine, potentially, that's when we kick in this UI and it says something like, yes, yes, always allow this, or no, (t: 750) I want to do something else. And using that permission management and being smart about it can help you work faster. (t: 760) So there's something called auto accept mode, where if you're working with Cloud Code and you press Shift Tab, Cloud will just start working. There's things you can do, like you can configure Cloud (t: 770) in the settings where specific commands, like on bash, like if you just are, like, tired of saying, yes, run NPM run test, you can just always approve that. (t: 780) So fiddling with your permission management is a great way to kind of speed up your workflow. Integration setup. So one thing that is going to help you get the most out (t: 790) of Cloud Code is remember that it's great at the terminal. And if there's applications that you use which have kind of a way to access them through CLI, and GitHub is a great example of that. (t: 800) They have a powerful tool called GH, you can basically give more work to Cloud Code. And you can do that either by just installing more CLI tools, (t: 810) or you can attach more MCP servers. I would say, just through experience, that if you're using something like a CLI tool that's (t: 820) well-known and well-documented, and you're trying to choose between the CLI tool and just installing it on your machine and grabbing an MCP server, I would recommend using the CLI tool. (t: 830) And then also, if you internally have your own tools at Anthropic, we have something called Koo that does a whole bunch of stuff for us. (t: 840) You can also tell Cloud about that. And that's the sort of thing you'd put in Cloud.MD. And then context management. So remember that Cloud is an agent. (t: 850) And what it does, it calls these tools. And the context builds up and up over time. And at least for Anthropic, our models have a context window of 200,000 tokens. (t: 860) And you can max this thing out. So you kind of have two options. When you're in a long session with Cloud, and you're working, and you're going back and forth, you'll see in the bottom right, you'll start (t: 870) to get this little warning that'll say, hey, you're starting to fill out the context window. And kind of depending on what's going on, you have two options. You can run slash clear and just start over. (t: 880) And that clears everything out, except for, for instance, Cloud.MD. Or you can run slash compact. And what'll happen is basically it's like a user message is inserted. (t: 890) And it just says something like, hey, I need to go summarize everything. And what we've been up to, I'm going to give this to another developer. And they're going to pick up where I left off. And then that summary is what kind of seeds the next session. (t: 900) And you can go from there. We spend a lot of time tuning this kind of compact functionality so that as you max out the context window and then run compact, you can start back over (t: 910) and keep going. Efficient workflows. What can you do with Cloud Code? And how do you get the most out of it? So using planning and to-dos. (t: 920) I talked a little bit about this before. But one of the best things you can do is when you open up Cloud Code, instead of saying, hey, I need you to fix this bug, you can say, hey, (t: 930) I have this bug. Can you search around, figure out what's causing it, and just tell me a plan how we're going to fix it. And this can save you a lot of time. Because you can verify, you can read Cloud's plan, (t: 940) and you can verify what it's going to do. And then the other thing that we have is we have this to-do list feature. So often when Cloud's working on a big task, it'll create a to-do list. (t: 950) And if you're kind of paying attention, you can kind of watch this. And you can change the to-do list. And if you see anything kind of weirder in there, or something that doesn't make sense, that's when you can press Escape and say, hey, (t: 960) Cloud, let's change the to-do list. I think you're on the wrong path. Smart vibe coding. So it's very tempting, and it's very powerful to just let Cloud work, and press Enter, (t: 970) and see what happens at the end. I think there's a few things that can help make this better. And there's, I think, a talk later today about just this for 30 minutes. But doing things like having test-driven development, (t: 980) having Cloud make small changes, run the tests, make sure they pass, always having Cloud do things like check the TypeScript and the linting, (t: 990) and then commit regularly so that if it's kind of going off the rails, you can always fall back and try again. You can use screenshots to guide and debug. So Cloud is built on top of our models, which are multimodal. (t: 1000) You can always just grab a screenshot, paste it in. Or if you have a file somewhere that's an image, you can just say, hey, Cloud, look at this mock.png, (t: 1010) and then build the website for me, or whatever. And then advanced techniques. So as you're getting used to using Cloud, what are some things you can think about to kind of push (t: 1020) things to the next level? And one of the things we see, both internally and with customers, is when you've started to use this tool for a while, (t: 1030) it's going to be very tempting to use multiple Clouds at once. And so I know people at Anthropic and a few customers that run four Clouds at the same time. There's various ways to do this. (t: 1040) You can have it in T-Mocs or just different tabs. All sorts of crazy things. So I would challenge you to try getting multiple Clouds running at once and kind of be orchestrating all these things. (t: 1050) It's quite fun. I can only do two, but I know people that do four. Use Escape. So Escape is your best friend. (t: 1060) While Cloud is working, you can kind of keep an eye on what it's up to, and you can press Escape to stop it and interject and say, hey, I think you're going on the wrong path, or I want you to do something else. (t: 1070) Knowing when the right time to press Escape is, versus just letting Cloud figure it out, is key to getting the most out of the tool. And there's a hidden feature. Not too many people know about it. (t: 1080) But if you press Escape twice, you can actually jump back in your conversation. You can go back and you can kind of reset. Tool expansion in MCP. (t: 1090) So this is taking it to the next level. If you feel like with Bash and with the tools that Cloud has that it still can't do something, this is when you should start looking at MCP servers. (t: 1100) And then headless automation. I think this is a thing we're most excited about. But also, we are still trying to wrap our heads around internally, which is how can we use Cloud programmatically? (t: 1110) We have that in GitHub Actions. We want to figure out other creative places we can start using it. I would challenge you all to do the same. (t: 1120) So with that said, I'm going to jump over to my computer, because there's one other best practice, which (t: 1130) is it's always good to stay on top of everything. Everything that's new. So we're shipping super fast. I'm going to throw out, I'm just going to go over a few things that are new as of today. (t: 1140) One thing is when you're in Cloud now and you fire it up, you can do slash model. You can see what model you're running on. I'm on default, which happens to be Sonnet. (t: 1150) We can jump over to Opus. You can do the same thing in slash config. Switch it here. (t: 1155) So that's new. Make sure you're running the model that works for you. (t: 1160) There's another thing that's new. There's a new feature that's new about these models, which is you can say something like, can you (t: 1170) figure out what's in this project? And for a while, we've had this think hard, or extended thinking. Now, this is great, but with our past models, (t: 1180) we wouldn't let our model think between tool calls. And that's probably when the thinking matters most. So starting with Cloud 4, our models now think between tool calls. (t: 1190) And we can watch this happen. So we have Cloud in this project. And I'm just going to tell it to think hard and figure out what's in this project. And we can watch Cloud start to work. And so the way you know you triggered thinking (t: 1200) is you'll see kind of this lighter gray text. And then it'll call some file. It'll call some tools. It'll read some stuff. And then we see some more thinking. (t: 1210) And this is awesome. So I encourage you, when you're working on tasks and solving bugs, throw a think hard in there. And then the other thing, and you know what? (t: 1220) We'll just throw it up real quick, is I have this in VS Code. But of course, this is in JetBrains as well. But we have these new great integrations (t: 1230) with VS Code and JetBrains. We can do things like, Cloud's going to know what file I'm in, what file am I in. (t: 1240) That is not what I meant to say, but Cloud's going to figure it out. And you can do things like this. (t: 1250) So these are the sort of things I would encourage (t: 1260) you to stay on top of. We have a public GitHub project called Cloud Code under Anthropic. You can post issues there. But we also post our changelog there. (t: 1270) And so I check this once a week and make sure that I'm on top of all the new stuff we're shipping, because even I can't keep up with it. So with that said, we have four minutes left. (t: 1280) I'm happy to answer questions about anything Cloud Code related. We have it here. I can live demo some stuff if you're interested. Let's do a few. Let's do him first, and then you. (t: 1290) Thanks, real quick. This might be obvious, but multiple Cloud.md files in a project, I presume that's possible, (t: 1300) and it just figures it out, or no? So there's a few options. Of course, in the same directory, you couldn't. (t: 1310) But you could have one here. And one in a subdirectory. And I think we changed this so that all the subdirectory ones (t: 1320) aren't read in, because like Anthropic, we have a monorepo. And people would open it at the top and blow up their context with all the Cloud MDs. So we encourage Cloud when it's searching around (t: 1330) and it discovers Cloud.md files in child directories that are relevant to be sure to read them. But by default, it just reads the Cloud.md file (t: 1340) in the current working directory when you fire it in. So you can fire it up. And then also, you can set one in your home directory. There are things you can do, though. We have this new thing. (t: 1350) In your Cloud.md, you can start referencing other files. So you could, for instance, do something like this with an at sign. (t: 1360) If you have other Cloud.md files that you just know you always want to read in, to do something like that. Hi. OK. I have not had luck getting Cloud to read. (t: 1370) I'm getting Cloud to respect my Cloud.md. There's one thing particular where I'll ask it to refactor something. And then it will leave inline comments explaining (t: 1380) the what of it is. And it's something that's extremely obvious. And so I'll tell it, go and remove any inline comments that describe the what of what's happening. (t: 1390) And then it will remove it and then immediately do it again in the same pass. So do you have any strategies for dealing with that? So there's kind of two things that fix that. So that was actually kind of a model problem. (t: 1400) There's nothing in the prompt. We have actually a lot in the prompt for 3.7 that said, whoa, do not leave comments. And despite that, the model just loves to leave comments. (t: 1410) So it doesn't surprise me that your Cloud.md didn't help much either. We already did a lot. I did a lot of work to try to tamp it down from what happens out of the box. So we mostly fixed that in Cloud 4. (t: 1420) Now there might be some new weird behavior quirks. But the other thing we made better in Cloud 4 is it's just better at following instructions. And we've gotten a lot of feedback from early testers. (t: 1430) And we've gotten a lot of feedback from users that all of a sudden, whoa, my Cloud.md is being followed way more closely. And it might be a good chance to go look in your Cloud.md (t: 1440) and decide, do I still need this stuff? Maybe I can take some of it out. Maybe I need to add a few new things. So moving over to the new models might be a good time to take another look at what's in there (t: 1450) and see what you need and what maybe can go. For the record, I'm trying to think of something that you might not have thought of. We're doing multi-agent execution and parallelization. (t: 1460) Yes. Can you make it so that for four agents, say, agents 2 and 3 use the context from agent 1. Maybe agent 4 uses the context from agent 2 at a certain point. (t: 1470) Yeah. Yeah, et cetera. That's interesting. We're trying to, so kind of like I said at the beginning, we're trying to do the simple thing that works, which (t: 1480) is just one agent that's great at coding and does everything. I think we want to figure that out. Probably what's going to happen is if you wanted to do that, you would ask all your agents to probably, (t: 1490) like, write to a shared markdown file or something like that so they can all kind of check in and communicate. Sometimes I'll be working with Cloud and I'll just say, (t: 1500) like, hey, I need you to write some stuff in like ticket.md for another developer. And then I'll fire up another Cloud code and I'll be like, hey, read ticket.md. (t: 1510) Another developer left this note for you. This is what you're going to work on. So I would think about trying to write that state to a file and then just kind of like count on the model's ability to just read files and make sense of them. (t: 1520) It's probably the best you can do today. And maybe we'll figure out clever ways to expose that in the product as something more native. (t: 1530) Cool. All right. And with that said, I have some rare Cloud code stickers (t: 1540) that I found in my backpack. So come find me. I'll be hanging out over there or something. Happy to share them. Thank you. (t: 1550) Bye.

