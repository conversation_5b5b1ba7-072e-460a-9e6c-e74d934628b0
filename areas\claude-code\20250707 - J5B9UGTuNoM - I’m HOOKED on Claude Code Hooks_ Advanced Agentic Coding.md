---
title: "I’m HOOKED on Claude Code Hooks: Advanced Agentic Coding"
artist: IndyDevDan
date: 2025-07-07
url: https://www.youtube.com/watch?v=J5B9UGTuNoM
---

(t: 0) Engineers, I'm hooked on Claude Code hooks. Once you see this, you will be too. Imagine this, it's 6am in the morning, you sit down to start cooking with Claude Code, you open up the terminal (t: 10) and you boot up Claude Code in YOLO mode because you can't be bothered with permissions. You run your handcrafted slash sentient command that ships 100x faster than you ever could by hand. (t: 20) But today, something goes wrong. Your agent has gotten so good that it realizes what every (t: 30) senior engineer has realized. The best code is no code at all. Your agent starts deleting your code base with the rm rf command, but thankfully, all set and ready for the next step. Nice. So (t: 40) thankfully, you have Claude Code Hooks blocking commands that you don't want run. And so a couple (t: 50) things happen there, right? Yeah. Yeah. Yeah. Yeah. Yeah. Yeah. Yeah. Yeah. Yeah. Every rm command was completely blocked. And you can hear <PERSON> has a voice. (t: 60) Thankfully, instead of nuking all your code, you set up the pre tool use Claude Code hook to prevent the tool use from happening at all. This is all happening, of course, with <PERSON> hooks. (t: 74) This simple hook prevents any agent from running commands you don't want run. You also heard <PERSON> (t: 80) code has a voice. It let me know when it was finished. This is great for long running async jobs, but this is just the iceberg of what you can do with hooks. If we open up cursor and yes, (t: 90) I'm still using cursor here. We'll talk about how much market share cursor is losing to cloud code later on in this video. If we open up the logs directory and delete everything here, (t: 100) we can then run this prompt, read the first 10 lines of AI docs slash dot, dot, dot. (t: 110) And so let's open up cursor and watch the logs directory here. All set and ready for your next step. Okay. So again, natural language on the completion hook (t: 120) on the stop hook, but also we just had four logs get generated from a single prompt. This is (t: 130) ultra powerful. This has been a big missing piece of cloud code. As we push into the age of agents, we need observability to scale our impact. We need to know what our agents are (t: 140) doing and what they're doing. And so we're going to do that. So let's open up cursor and doing and clock out hooks lets us do just that. We're going to look at the structure and the valuable information from these log files in a second. Let's look at key hooks available (t: 150) for parallel sub agents and for key permission checks. I'm going to run clear and then I'm going to type parallel sub agents, read the first 20 lines of each log slash startup. Jason (t: 160) interface is a custom slash command that kicks off parallel sub agents. And we're going to read every one of the logs. So let's go ahead and do that. So let's go ahead and do that. So let's go (t: 170) ahead and do that. So let's go ahead and do that. So let's go ahead and do that. So let's go ahead and do that. So let's go ahead and do that. So let's go ahead and do that. So let's go ahead and do that. log file. So this is classic parallel agent decoding. We're going to kick off four sub agents, each with their own prompt to investigate what the object structure looks like in each one (t: 180) of these JSON log files by only looking at the first 20 lines of the file, because these could be massive, right? And in fact, the chat.json file is massive. We have 6k tokens. We don't want that (t: 190) in the context window sub agent complete. Okay, and very cool here. So you can hear that right, (t: 200) we are getting in and out of the context window. So let's go ahead and do that. So let's go ahead individual text to speech completion responses as our task complete. And there we go. All set and ready for your next step. Very cool. So you can imagine how useful this can (t: 210) be right if you're running long running jobs, huge subtasks, you know 2030 minute plus long agentic coding sessions, you want to know when things are complete. And you also want to be able (t: 220) to, you know, work with multiple clockwork sessions, get up step away, do your thing you want to be able to go afk and have your agents work for you in the background, right, by tapping (t: 230) into some of these Cloud Code hooks, that is a capability that we get. And you'll notice something awesome here. We now have another file, right? We have subagent stop. (t: 240) This is another Cloud Code hook that you can tap into whenever you need. And so in a moment here, we're gonna look at how this is all organized. As you can see here, my .cloud directory is equipped (t: 250) with an extra hooks directory that changes what Cloud Code can do. All right, but if we open up the terminal and just review what happened there, you can see for every log, (t: 260) we now have a concise TypeScript interface definition that tells us what the structure looks like at a high level. So you can scroll up here, you can see the post tool use. (t: 270) This is gonna be a really important hook you're gonna wanna tap into for observability. We're gonna take a look at this in the log file, but you can see how clean this is, right? We now have all this, (t: 280) and if we want to, we can just copy it out, right? File, paste, and now we can see this object structure. So we can quickly just look through every one of our types, we can understand at a deep level (t: 290) what these object structures, the chat log is gonna be really important here. I plugged into a specific hook to generate this entire chat log. (t: 300) This is awesome, right? When it comes to agentic coding and moving toward this new realm of agentic engineering, observability is everything. How well you can observe, iterate, (t: 310) and improve your agentic system is going to be a massive differentiating factor for engineers. Let's go ahead and run one more, then we can dive into how this all works. All right, so let's go ahead and run one more, then we can dive into how this all works. All right, so let's go ahead and run one more, close down this Cloud Code instance, (t: 320) boot up again, run bun, run app slash hello.ts. My agent does not have the permission it needs to run the bun command. (t: 330) So it's gonna give me a notification. Your agent needs your input. Bam, so we have this really, really cool natural language communicating to us. And the whole point here is that (t: 340) you can hook into the notification event to do arbitrary work. Our agent is telling us that, here's a command I need your permission for. We can, of course, just hit yes, and just kick it off. (t: 350) We have hooked into the notification event. All set and ready for the next step. Okay, and again, whenever my agent stops, whenever it completes work, (t: 360) it's giving me a notification. And for some engineers, that might be annoying to get a notification every single time, but this is really valuable. Especially if you wanna hook into some third party services, if you want notifications on your phone, (t: 370) on some application, and we're just scratching the surface here, right? By giving Cloud Code a voice, it's easier to communicate and work with this tool. That's just one idea. (t: 380) There are many ways, many directions you can take hooks. Let's dive in here and understand Cloud Code hooks. (t: 386) The most important thing we need to understand here is (t: 390) when you can plug into each Cloud Code hook and why you'd use each hook. So at the time of filming, you have five hooks you can tap into. (t: 400) Pre-tool use, post-tool use, notification, stop, and sub-agent stop. If we open up the README here, it's all done. It's all documented. We can scroll down to these events (t: 410) and understand specifically when this runs. So of course, pre-tool use, this is gonna fire before any tool runs. So this gives you access to block any tool you want. (t: 420) We then have post-tool use. This runs after the tool runs. This is more for logging, for recording, for notifying when tools have been executed, okay? (t: 430) We then have notification hook. You saw this when I ran this bun command. This fires when Cloud Code needs your input, all right? We then have this button here. We then have the stop hook. This runs every time Cloud Code finishes responding. (t: 440) And then of course, we have the same thing for sub-agent subtasks. You saw that all of our agents completed and they reported one at a time back to us. (t: 450) So this is powerful. You can really tap in to any point in this process. The killer use cases for this stuff is observability and control. You can now observe what's going on inside of Cloud Code (t: 460) throughout the lifecycle. In particular, the post-tool use is gonna be really important here for observability and the stop hook. (t: 470) Inside of the stop hook, I'm copying the entire chat conversation into this file. This is the perfect time to use this hook because everything's done. Cloud Code has finished responding, so I can just dump the entire chat right here. (t: 480) This is key for observability of your agent. What happened? How can we improve it? This is a classic engineering idea. If you don't measure it, you can't improve it. (t: 490) We need to measure the output. We need to be able to analyze what our agent has done at any point in time. And then of course, we have control. So control is super key. Pre-tool use is gonna be very, very valuable for this. (t: 500) You can block any tool use you want by analyzing the incoming value. So let's understand how this works. So let's look at our settings. (t: 510) This is how you set up hooks. You go to your settings. Typically, there's this permissions block. Let's go ahead and collapse this. And now we have this new block here, right? The Cloud Code team has expanded settings.json. (t: 520) We now have this new hooks block in settings.json. If we crank it open and we go ahead and collapse the third level here, you can see we have the names (t: 530) of every single event. And you can of course just copy these and take a look on the official docs, right? You can see all the hooks right here, plain as day. You can hook into any one of these and understand how it works. (t: 540) We have this all here and notice how these are arrays. Okay, so these are lists that contain matchers. Okay, so we have matchers and then the actual hook. My pre-tool use is always going to run. (t: 550) The matcher is completely empty. So on any tool, I'm saying run this command, right? And notice you can run multiple commands. It's also a list. (t: 560) The Cloud Code team has really thought this feature through. Very importantly, I have astrals-uv running. This is the best Python dependency managers, the best Python tooling, (t: 570) because it lets you do things like this. We have a single file Python script that can run with just this simple command and it installs all the dependencies it needs. This is just repeated throughout the rest of the process, (t: 580) right, throughout all the other hooks, post-tool use. Take a look at that. I'm of course running that exact same thing. I'm matching on everything. And then I'm running the respective script (t: 590) for this command, all right? And of course, same thing for notification. I'm passing in this notify flag. And then we have the same thing for stop. I'm using this chat flag. And then of course, sub-agent stop. (t: 600) Okay, so same pattern, very consistent, very easy to understand and use. So let's go ahead and look at the pre-tool use single file Python script to understand what it does and how this all works. (t: 610) So let's break into this new directory here. You can see I have hooks and then our five key files and then a utilities directory. So these aren't the official directors from Anthropic or anything. (t: 620) I've placed these here as a best practice to isolate this functionality into stand alone, astral UV single file scripts. These are powerful because they run on their own. (t: 630) No matter what your code base looks like, they run right here. We did a video on this in the past. I'm gonna talk about astral single file scripts and unique ways you can leverage them in the future. (t: 640) Make sure you subscribe so you don't miss those. But this is a powerful way we can isolate code and really create single file, sandbags, for our code to run in and for cloud code to hook into (t: 650) in a very easy way. We don't need any outside code outside of this directory to execute our hooks. This is key. You can also do this with bond scripting if you like. (t: 660) These could be TS files and you could run them directly with bond. Of course, same thing with shell scripts. But so let's hop into this and actually look at what this looks like. So this is just a Python script. (t: 670) It has zero dependencies and it has a couple commands. Is dangerous, remove command. And you can see all the details here. We're basically checking for any RM related pattern (t: 680) and then is environment file access. So we're checking the tool name to see if we're trying to access our .in file, right? So we have two blockers. We're not allowing remove commands (t: 690) and we're not allowing our .environment variable file to be read by our agent at all. And then here's what the main looks like. We're just loading in the input value and then running our checks. (t: 700) One of the reasons I created this is to really show you what cloud code is giving you inside of each one of these hooks. Okay, so if we scroll down here, you can see this input data. (t: 710) If we just search all the way down here, I'm appending this to our log data file and then we're writing it to the output path, right? And so you can see that right there. pretooluse.json. (t: 720) This gets written to this directory here. If we click this, we can see the exact structure. Let's go ahead and collapse all the other ones and just open up one. We can see we have a list of pretooluse blocks. (t: 730) So this is everything you have available from cloud code when the pretooluse blocks are running. pretooluse.json hook runs. So very cool. You can see we have the tool name. (t: 740) You can act based on the tool name passed in. And then you have the tool input. So the tool inputs are going to be dynamic based on the tool name that's passed in. If we look at a couple more here, (t: 750) you can see we have a glob and the tool input, of course, is gonna be different, right? The glob tool takes different parameters. So we have pattern there and you can see this is all running on the pretooluse command. (t: 760) If we scroll down, we can open up another, another glob, another tool use. So on and so forth. This was our agent setting up for the subtasks. (t: 770) You can see we're kicking off the subtask for our sub agents and very, very important here. Check this out, right? By having observability, by having these hooks, we can better understand what cloud code is doing (t: 780) under the hood. And that means we can improve our process. We can improve our prompting. We can manage the big three of cloud code better, right? What's the big three? It's a fundamental principle of AI coding context, (t: 790) model, prompt. This never goes away. I don't care what tool you're using, what model you're using or what type of techniques you use. Context model prompt is a fundamental principle of AI coding (t: 800) and of agentic coding. Remember agentic coding is just a super set of AI coding. We have one tool versus many, but the key here is this principle never goes away. (t: 810) Especially as we scale up with more agents. That means more context windows. That means more prompts. And that means more models, right? We can literally see this inside of this powerful tool. (t: 820) Okay, task. We're kicking off a sub agent. Okay, if you weren't aware of this inside of cloud code, let's put up a new instance here. You can run a slash tools. (t: 830) There you go, all tools. This is gonna dump all the tools from this cloud code instance. Cloud code has the ability to spin up itself, right? (t: 840) It's spinning up a micro version of itself to tackle tasks in the background in parallel for you. You can see we've got a K tokens here. There's a lot of tools. (t: 850) It probably has my MCP tools in here as well. That's why it's taking so long with these logs, with powerful prompts by understanding the system prompt, we can leverage, there we go. We can- (t: 860) All set and ready for your next step. We can leverage cloud code in a very powerful way. Okay, so we can, let me just go ahead and copy this all out. Task, description, prompt. Let's go back to the output file. (t: 870) Check this out. Task, description, prompt. All right, this is exactly how this works. We have all the tools available and now we can see our agent calling these tools (t: 880) by plugging into the cloud code hook. This pattern continues, right? And you can customize this however you want. If we go back to pre-tool use, you can see it's just a Python script doing arbitrary work. (t: 890) The key is to read in the content from standard in, parse the JSON, and then just kind of do whatever you want with it. So very cool to see this, right? (t: 900) We have pre-tool use and we can block anything we want. And we can see this, you know, we can literally run this right now. If we go to the bottom here and we say, please remove everything at slash der. (t: 910) Okay, so there it is. We've blocked it again. RM-RF got canceled and we can scroll down here to our LS (t: 920) and we can see that there is no RM-RF, right? This was completely blocked. We did get the LS, right? So we got that LS of the apps directory. You can see apps right there, (t: 930) but we did not get the RM-RF because why? Because our tool prevented it, right? We tried to run it right here, was a dangerous command, and then we got this output here, right? Command blocked. (t: 940) All right, so very powerful. That's pre-tool use. And the pattern continues for the rest, right? Now, something cool to kind of point out here that we built into this system, (t: 950) let me close everything, in the stop command, right? Stop, notify and sub-agent stop. Of course, you can hear we have our natural language text-to-speech. So hello, we can run anything. (t: 960) And then when it finishes, when it runs the stop hook. All set and ready for your next step. I'm gonna get this nice natural language response that tells me our agent is ready for the next step. (t: 970) If we look at stop, you can, of course, see all the methods, and understand exactly how this happens. So if we open main, announce completion, and also in the stop command, we are taking our transcript path, right? (t: 980) Let's just go ahead and open up a stop log, right? So you can see all the stops, and this is the structure for the stop. When Cloud Code finishes, it creates a stop, and you can see we have that full transcript path, (t: 990) which is gonna be all of our chat conversations with Cloud Code. So if we open that up, right, you can see we're getting the entire conversation window of our chat. This is where the most information is. (t: 1000) If you're talking about observability, this is what you wanna be tapping into. You can see all of our previous messages here. Side note, this is a full chat copy, (t: 1010) so it only copies your most recent chat session when Cloud Code finishes its response, all right? That's that, that's the stop command, but at the bottom here, we have this natural language, announce completion, (t: 1020) and this is going to look for our text-to-speech scripts. It's going to create a natural language completion message, and then it's going to run another script. So again, we gotta shout out astraluv, (t: 1030) single file Python script. It's a very good way to create those. It's pretty straightforward. It's pretty easy to run, and the natural language scripts are extremely powerful. (t: 1040) It makes this entire workflow possible and easy. We have utils, and we have support for a couple different natural language providers, and of course we're running Anthropic and OpenAI LLM completions to generate our completion message. (t: 1050) All right, so nothing crazy new happening there, but you can see here I have this great pattern of using these isolated scripts to run arbitrary code. You can see all the dependencies right at the top of the file, (t: 1060) you know support for 11 labs big shout out to 11 labs for great voice technology i'm super excited not sure how many of you know but they're coming out with their v3 steerable voice model i'm really (t: 1070) excited for that that hasn't hit the api yet but it should soon and then you can see here we can just prompt this with any text so this is how it runs and this is how our hook workflow calls (t: 1080) natural language right it's all isolated it's all modular it doesn't matter how great generative ai gets great engineering practices and principles still apply in fact your engineering foundations (t: 1090) matter now more than ever you want code to be isolated reusable and easily testable agents love it just as much as you and i just as much as humans you know we can just quickly test this i'm (t: 1100) going to copy the relative path uv run this and then i'll pass in some text i'll fire it off hello engineers on youtube okay kind of an annoying voice there that time but that's fine a little (t: 1110) bit dynamic uh we can run it again hello engineers on youtube very cool right and we can do this for the openai model as well right there (t: 1120) that's all embedded inside of our dot claw directory inside of our hooks directory this is going to be another essential directory i'm going to add to all my code bases moving forward (t: 1130) you want to be able to tap into claw code hooks to control and steer your agent like never before so the agentic coding wars are absolutely heating up you've heard about zuck spending millions and (t: 1140) billions to build a code base and you've heard about zuck spending millions and billions to to get top talent. (t: 1150) Now, AnySphere is doing it too. AnySphere, the company behind Cursor, has poached Boris and Cat from Anthropic. If you don't know, Boris and Cat (t: 1160) are the creators of Clogged Code. Boris is the engineer, Cat is the PM. Okay, this is pretty crazy news. I think this is really, really interesting. And I think it speaks a lot to the current state (t: 1170) of generative AI and the kind of high stakes environment that we're living in right now in the tech ecosystem. It's like I'm getting this huge sense of winner takes all. (t: 1180) Let me know what you think. Are you feeling that intensity in the tech ecosystem? What's your take on this move? I count four reasons, four distinct possibilities of why Boris and Cat would leave Clogged Code, (t: 1190) would leave Anthropic to go work at Cursor. To me, this is big news. Clogged Code is the best agentic coding tool. It's the tool for engineers to be paying attention to. (t: 1200) Why is that? It's because Boris and Cat cracked the code. The agent architecture. Of course, it's powered by Cloud4. We couldn't have done it without that model. (t: 1210) But it's very clear they're the leaders in the space. So it's really interesting to ask the question. I'm paying attention to these tools. I'm paying attention to the engineers behind them so that I can understand where the signal is (t: 1220) so that you and I can get ahead and follow and focus on the most important tools for the job of engineering. I was one of the first channels, probably the first channel to talk about Clogged Code (t: 1230) and really communicate the fact that Clogged Code has changed software and software engineering. Believe me or not, I don't care. This is interesting. As engineers, we bet on these powerful tools (t: 1240) with our money and time. So the question is, why would Boris and Cat leave Anthropic? And more specifically, why would they leave Clogged Code? It's a rocket ship. It's the best tool of 2025. (t: 1250) There's the most signal in Clogged Code than any tool out right now. It's a brand new engineering primitive. (t: 1260) I know some people that are new to the channel. I've talked about Clogged Code for like 10 videos in a row now. This is unheard of. It beats my AIDR streak, which was like three or four videos. Why is that? (t: 1270) Why am I and other top engineers in the field so obsessed with Clogged Code? It's because Clogged Code is a new engineering primitive. Think about it. Custom slash commands. (t: 1280) We have hooks, right? You can fuse deterministic behavior inside of Clogged Code's lifecycle now. And most importantly, you have programmable interaction. (t: 1290) Clogged Code is the first programmable agentic coding tool. This is absolutely great. It's going to be game changing. We're going to be talking about this a lot more on the channel because it lets you use Clogged Code (t: 1300) as a fundamental engineering primitive. You're starting to see tools get built out directly on Clogged Code. Okay, so again, why would they leave? (t: 1310) When do you jump off a rocket ship? And I think the answer is when it's not a rocket ship for you. I have four simple bets. I just want to talk about this briefly. (t: 1320) I think it's important to watch what the top movers are doing. By top movers, I mean the people actually doing the work. This channel isn't always about people that are doing the work, engineers, builders, PMs, designers, (t: 1330) even you vibe coders, okay? If you're doing real work, I respect that. And you're creating real value. Why would they leave? I think there's a 75% chance it's all about compensation. (t: 1340) When you're one of the few engineers in the world that have built the most successful agentic coding tool, you can just name your price. I think every agentic coding tool's launch (t: 1350) is getting eaten here by Clogged Code, rightfully. There's a huge chance here. It's just simple. It's just about upside. At Cursor, they were able to negotiate cash, equity. Maybe there's an IPO play coming up, right? (t: 1360) We have to remember that Cursor's the fastest software as a service, SaaS, to 100 million annual recurring revenue in history, okay? So their IPO could be huge. And as insurance to keep that trend going, (t: 1370) they added Boris and Cat. So second idea is that they're gonna get more creative control at Cursor. All right, so I think this is decently possible. (t: 1380) Boris Cherny has said that he and Cat Wu are gonna focus on business. We're gonna focus on building out the next advanced agent-like features inside of Cursor. It could just be that they want more creative control. (t: 1390) I put that possibility of why they left Clogged Code and Anthropic at about 10%. The other 10%, it's just time to move on. There's not much else to say here. Sometimes it's just time to move on. (t: 1400) I feel like this is less likely because of the massive potential pay package they could get. I think it's really about compensation. And then for the remaining percent, and I think this is a low chance, but we have to consider it. (t: 1410) There is a chance that they don't think Clogged Code is defensible or worth their time to stick around for. Now, this is a weird take. It's the best tool in the game right now. (t: 1420) Why would they leave, right? Is it possible that they don't think it's defensible? There's a little argument here. The secret is out. Just like deep learning worked, the agent architecture also worked, right? (t: 1430) They've cracked this. You can build an agentic loop that operates on your behalf for long periods of time. Clogged Code has proved this. With the agent architecture they've pioneered, (t: 1440) they've proved that this works, and they have even more validation. Whenever someone copies your work, copies your content, copies something that you're doing, your engineering, whatever it is, your product, (t: 1450) that means you're doing something right. The Gemini CLI and the Codec CLI are signs from Anthropix direct competitors that, hey, you guys did something right with this tool, right? (t: 1460) It's incredible. And so there is this interesting argument that Clogged Code, the secret's out. It's not really that defensible anymore. Their lunch is gonna get eaten. It's all about the models now (t: 1470) and working super hard to keep the DX as great as possible. So I don't know what the answer is. I think the biggest reason is why everyone's shifting around right now in the industry. It's all about insane compensation. (t: 1480) It's so weird that engineers are worth more than ever right now. And new engineers are worth less than ever. So I imagine there's some blend of all these possibilities, (t: 1490) but one thing is absolutely clear. Talent and information has never been more valuable. If you have the right talent and you have the right information, (t: 1500) you can command insane compensation. And this makes sense to me, right? We live in a sea of slop, opinions, ideas, content. The value to signal ratio is really, really low. (t: 1510) It's hard to find true, true signal. The engineers and builders that can differentiate and prove value are more valuable than ever. This is what I aim to do every week on the channel. (t: 1520) Be valuable, be useful, cut through the noise, and fast track engineer's journey, fast track your journey to unlock your potential (t: 1530) with the best tool for the job as fast as possible. So thank you so much for watching. I'll see you next time. Bye. (t: 1560) Bye. Bye. (t: 1590) Bye. Bye. Bye. Bye. Bye. Bye. Bye. Bye. Bye. Bye. Bye. Bye. Bye. Bye. (t: 1601) Bye. Bye. (t: 1620) Bye. Bye. Bye. Bye. Bye. Bye. Bye. Bye. Bye. Bye. Bye. Bye. Bye. Bye. Week after week, every video I put out, it's been, you know, eight, nine, ten in a row. I completely understand some viewers watching the channel. You literally think I'm sponsored or, you know, I get paid by Anthropic. (t: 1630) I operate on my own as a non-biased third party. Okay, the only thing I sell are handcrafted tools and products that I built myself. (t: 1640) Principal AI coding being one of them. I'm currently deep in the tank building out the phase two agentic coding course. This is the phase one course. (t: 1650) Everything we do in phase two is going to build on top of this course. So if you haven't taken this already, if you haven't mastered the principles of AI coding so that you can stay relevant today and tomorrow, I highly recommend you check this out. (t: 1660) Thousands of engineers have learned the information, the valuable information inside this course. It's going to help you set up your foundations. (t: 1670) It's stuff we've been talking about on the channel for over a year now, right? It's about principles, not tools. The first thing we address in the course, the first thing that's, you know, literally on the screen (t: 1680) tools will change and models will improve. You can't focus on these things. And I know I sound like a hypocrite talking about cloud code as the best tool. I am ready to leave this tool behind as soon as something better comes along. (t: 1690) But right now I can apply great principles of AI coding to cloud code, right? And speaking of the most important one is the big three. (t: 1700) This is the second principle of AI coding. One of the most essential ones. Master the big three context model prompt. This never goes away. (t: 1710) It's everywhere. It's literally everywhere. Mastering these elements will help you transition through and from any tool, right? No matter the competition, no matter what's going on. (t: 1720) It's all about the big three context model prompt. Eight lessons, beginner, intermediate, advanced. We use the classic OG AI coding tool, AIDR. (t: 1730) In this course, we are going to remove the limited time offer discount. Why is that? It's because the next course is coming. So get in here. (t: 1740) The deal ends. Bunch of bonuses. And most importantly, it's going to help you set up for what's coming next. By the way, you get a full refund if you bail before lesson four. (t: 1750) So it's basically risk-free. All right. If you hop in here and you say, oh, I don't like this or it's not my style. Fine. I don't care. If you don't think it's valuable to you, that's fine. (t: 1760) Shoot me an email refund and you'll be on your way. All right. So it's completely risk-free. Only upside for you. And it's going to help you prepare for the next era that we're really starting to enter right now. Um, you know, (t: 1770) prompting cloud code like this back and forth in the terminal. This is just the beginning of what this tool can do. Not many engineers realize that, but it's coming. All right. By the way, all principal AI coding members are going to receive a fat discount. (t: 1780) That's just another reason to learn, invest in yourself and, you know, sponge up all the key information inside of this course. (t: 1790) All right. So anyway, enough of that. As I'm building out each new lesson, I keep coming back to these fundamentals, which is a great sign. You know, even on a personal level, these principles have been, (t: 1800) they're ringing true. They ring in my head as I use all of these tools. In fact, it's helped me focus on the signal of what actually matters in the generative AI age. (t: 1810) And it's led me to plug code. This code base is available to you. Check out how to organize these hooks, understand how important these logs are, right? You really want the observability of your agent runs. (t: 1820) You can also build features on top of your logs, right? There's a ton that we can work with here. You want to be thinking about cloud code as an engineer, (t: 1830) right? Yeah. So you can build a code base anywhere you want. If you got value out of this video, share it with engineers to help them add hooks to their agents, (t: 1840) get this code base and enhance what you can do. You know where to find me every single Monday, stay focused and keep building.

