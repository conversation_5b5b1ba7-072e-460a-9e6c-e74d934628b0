---
title: This One Click INSTANT LLM System IS NUTS 🤯
artist: Income stream surfers
date: 2025-08-26
url: https://www.youtube.com/watch?v=8_DxUVGJ5gQ
---

(t: 0) Hey guys, today I'm going to be talking about how to work with any LLM instantly. Now you can very easily create custom AI workflows, you can use any model and more importantly you can (t: 10) use different models. Two minutes to set up and one API key. Now let me give you an example of why you might want to use this. Let's say you're creating a complex AI application. (t: 20) Now at some point you will have to decide which model to use. So let's say you want intelligence (t: 30) and you want speed slash cheap. So these are the two different types of models. Now intelligence would be Gemini 2.5 or Claude <PERSON> for example or GPT-5. I'll just put 5, you guys know what that (t: 40) is. And then speed and cheap would be something like, I don't know, Gemini 2.5 Flash. So (t: 50) you want to use a model that's cheap and you want to use a model that's fast. So you want to use Flash 2.5, Haiku 3.5, I think it is still 3.5 and then I don't know GPT 4.1 Mini for example or (t: 60) even <PERSON><PERSON>. Now the issue with this as a developer is that you're going to have to set up Gemini (t: 70) with an API key. You're also going to have to set up <PERSON> and you're also going to have to set up GPT which is just a bit of a nightmare to be honest with you. Now another thing is that you (t: 80) might not want to use the set up. So you might not want to use the set up. So you might not want to use the set up. So you might not want to use the set up. So you might not want to use the same provider for intelligence, right, as for example speed. So I love to use Claude Sonic with Flash 2.5 or more (t: 90) frequently 4.1 Nano. Although I think I will move over to Flash 2.5 for a lot of things because it's (t: 100) a really, really good model. Now let's talk about how this is done. Now the really cool thing about this is you literally just have to go to Open Router, right, create an API key, give it 10 (t: 110) bucks or whatever. So now you've got a million context, right, because it's Gemini Flash. And all you do is on the model card, you just go to API. It's literally this easy, right? And then you have (t: 120) this ready to go, right? So this is OpenAI Python. But let's say you wanted to use a curl request (t: 130) instead because curl requests are super, super easy to use. And yeah, you know, you can kind of make a very simple script to run this. (t: 140) So let's just see this in action. I'm not even going to use Claude code for this. I could use Claude code and I wouldn't. But instead of Claude code, I'll just use GPT-5. So I'm going to say write me a series of curl (t: 150) requests. And just another thing quickly, guys, is you could also, for example, add search, right? So I'm also (t: 160) just going to grab this. So we'll just, this is 2.5, right? I'm not, I'll just use one model, but you can very easily, you know, grab another model. In fact, let's just use two models. So I'll use Sonic. (t: 170) What is it? Four, right? Yeah. Sonic four. So I go to API here, grab this, right? Put this into GPT. (t: 180) And then we'll grab the web search stuff. Put that into GPT. (t: 190) Series of curl requests that take the input URL and do research on that URL. (t: 200) Then generate sub, sorry, pillar pages for that URL. That makes sense for a business in that niche. (t: 210) Then generate sub pillar pages and give the sub pillar pages back to the user. Use Flash, Gemini Flash (t: 220) for any web search or scraping and use Sonic for the intelligence. (t: 230) So that's the first step, right? And then we just enter here. And then of course, the other thing we need is a API key. (t: 240) So we just go to keys here. Should already have some credit. So it's a great one. Okay. So this is the process that it gave me. I don't think curl was the best for this. I should just use Python, but I wanted to keep things simple. (t: 250) So I should just be able to copy and paste these commands one by one here. Okay. So let's try this out here. So let's run the export. There we go. And then let's run this. (t: 260) See if that works. Looks like it is working. Okay. Interesting. (t: 270) Oh, damn. There we go. So this is research.json. It created this very, very quickly. Holy crap. That was super fast. Jesus. Let's do this. So generate sub pillars. So feeds research.json. (t: 280) Okay. So we can see that it works. I bet I put this into a Python script because it was just taking ages with this. (t: 290) Okay. So we can see that it works. I bet I put this into a Python script because it was just taking ages with this. And it was just getting confusing, but you can see just how quick this is to set up. It's literally just copy and paste three pages into chat GPT, not even Claude code. (t: 300) And you have something that, you know, works perfectly. So we can see research here. It does the research on the website and then it creates the pillars. (t: 310) And then afterwards it will create the sub pillars as well. So this is actually how I code a lot of my stuff. Another really good thing about this, by the way, guys, is that there's no rate limits. (t: 320) So if you're experiencing problems with rate limits, on Anthropic or chat GPT, this is a also a solution to that problem as well. I would use Python for this guys. Don't bother with curl requests. (t: 330) I was just showing you quickly that you can do it with curl, but it's actually a lot easier with Python. I'll leave the video there, guys. Thank you so much for watching. You'll be watching all the way to the end of the video. (t: 340) You're a legend. I'll see you very, very soon with some more content. Peace out.

