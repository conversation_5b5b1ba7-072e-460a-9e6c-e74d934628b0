---
title: My <PERSON> Code Sub Agents BUILD THEMSELVES
artist: <PERSON>D<PERSON><PERSON>an
date: 2025-07-28
url: https://www.youtube.com/watch?v=7B2HJr0Y68g
---

(t: 0) Welcome back, engineers. IndieDevDan here. Imagine starting your day. You, of course, open up the terminal. You fire up Cloud Code. Then you kick off a single prompt slash cook (t: 10) that does the work it used to take you hours in minutes. You're able to accomplish this with Cloud Code sub-agents. You've created workflows of specialized agents (t: 20) that do one thing and do it extraordinarily well. You can see I have this agent called MetaAgent. My agents are building my agents. (t: 30) Code is a commodity. Your fine-tuned prompts can be valuable. And now your Cloud Code sub-agents can yield extreme value for your engineering (t: 40) if you know how to avoid the big two mistakes engineers are making with sub-agents. In this video, we're gonna break down (t: 50) how to build effective Cloud Code sub-agents. We'll, of course, use this powerful MetaAgent to build new agents. But before we get there, sub-agents have serious trade-offs (t: 60) and pitfalls you should know about so you don't waste your engineering time and tokens. Let's understand how to take our agentic coding (t: 70) to the next level with Cloud Code sub-agents. (t: 80) So first things first, what are Cloud Code sub-agents? I can almost guarantee you sub-agents, don't work like you think they work. (t: 90) Let me explain. Here's what the flow of Cloud Code agents look like end to end. Starts out with your prompt. Your primary agent then prompts your sub-agents. (t: 100) Your sub-agents then do their work autonomously. And then, this is important, they report back to your primary agent and your primary agent reports back to you. (t: 110) The flow of information here is absolutely critical. You prompt your primary agent and then your sub-agent does the work. And then your sub-agent does the work. And then your sub-agent does the work. And then your sub-agent does the work. And then your primary agent prompts individual sub-agents (t: 120) based on your original prompt. Your sub-agents respond not to you, they respond to your primary agent. (t: 130) Okay, many engineers are going to miss this fact. And this changes the way you write your sub-agent prompts. Let's break down exactly what Cloud Code sub-agents look like. (t: 140) So inside this code base, we have a simple hello world agent prompt. You can of course see, this is operating inside of the brand new agents directory. (t: 150) We have a new agent directory to focus on. So we'll get to the important meta agent in a moment. (t: 160) Let's start simple and understand what sub-agent prompts look like. We'll open up a new shell here, fire up Cloud in yellow mode. And you'll notice here in the description, (t: 170) if they say hi, Cloud or hi, CC or hi, Cloud Code, use this agent, hi, CC. And now Cloud Code immediately, and you'll notice here in the description, if they say hi, Cloud or hi, CC or hi, Cloud Code, use this agent, hi, CC or hi, Cloud Code, finds this agent, right? (t: 180) It finds the description for this agent and kicks it off. You have your agent name. This is its unique ID. The description, which is very important. This communicates to your primary agent when it should call this agent. (t: 190) Tools, so you can specify specific tools available. Sub-agent complete. And the color, which you can see right here. You can see we have a nice formatted response there. (t: 200) That natural language text-to-speech, by the way, that's set up with Cloud Code hooks on the stop event. But if we look at this format, there's, there's something really important here. (t: 210) And we're coming up on the first big mistake engineers are making when using Cloud Code sub-agents. If we open up this prompt here, you can see we have a classic markdown format, purpose and report. (t: 220) The first mistake engineers make is not understanding that what you're writing here is the system prompt of your sub-agent. (t: 230) Okay. This is not the prompt for your agent. Okay. It's the system prompt. This is important. It might not seem like an important detail, but it changes the way you write the prompt (t: 240) and it changes what information is available. That's a big mistake. But even bigger is this. Notice how I have this section here. And remember in our diagram here, (t: 250) remember who your sub-agents are responding to. It's not you. It's your primary agent. Okay. This report, this response format is going to be really important. (t: 260) You can see here, I'm explicitly having the sub-agent communicate to the primary agent. I'm saying, Claude, respond to the user with this message. And guess what happened? Look at the response. (t: 270) Hi there. How can I help you? Did you know Nvidia blah, blah, blah, blah, blah, right? We had it research some random tech news, right? This is the sub-agent prompt format. (t: 280) We have variable declaration at the top. The cracked Cloud Code team is going to tweak, improve, add to these as time goes on. They're probably going to add the model here at some point. (t: 290) We have our system prompt, right? And you can see this right in the docs. If you don't believe me, you can always just open up the doc, search system, and you can see what we're actually writing here is the system prompt. (t: 300) To be super clear, this is not the user prompt. Okay. When you're writing, let's open up the prime command. When we have something like this slash prime, this runs right into our primary agent. (t: 310) This is a prompt, right? A user prompt that goes right into the context window of our primary agent. (t: 320) Okay. Our agents directory is very different. This is a system prompt. We're defining the top level functionality. These prompts are going to look similar, but it's important to delineate, right? (t: 330) You can pass variables into this. This is a system prompt. This is not what triggers what's actually done. And so, you know, again, just to bring this up, if you understand this, (t: 340) you are going to perform very well with your sub-agents and you're going to be avoiding the top two mistakes engineers make with Cloud Code sub-agents. (t: 350) You don't prompt your sub-agents. You can write a prompt for your primary agent to prompt your sub-agents, but you're communicating, right? With your primary Cloud Code agent, right? (t: 360) The top level agent. We'll call this the primary agent. It's Cloud Code that prompts your sub-agents, right? It is delegating. You really want to be thinking about your Cloud Code sub-agents as tools of delegation (t: 370) for your primary agent. And this is why the big three is so important. (t: 380) Okay. We've covered this all the time on the channel. This isn't going away anytime soon. In fact, it's only going to become more important as we scale up our agentic code. And we'll be getting more and more into it in the future. (t: 390) So there you go. And I think that's really helpful. And then we're going to move on to the next step, which is to talk about the context model and prompt. So I want you to remember, this is a really important part of coding to multi-agent systems, (t: 400) right? Context, model, prompt, and specifically the flow of the context model and prompt between (t: 410) different agents. Okay. This is super important. Okay. So I think you get the point. I don't need to keep harping on this. It's just really important. Right, so we're just getting started here. (t: 420) You can chain the call and responses and call and responses, right? It's funny that we started out prompt chaining years ago, and now we're still prompt chaining. We're just prompt chaining with bigger compositional units. (t: 430) You and I, the user, prompt the primary agent. They can then run tasks, and then based on the prompt, they come back into the primary agent. (t: 440) And you can have your agent keep cooking, right? It can keep doing important work for you. So you can fire off another set of sub-agents. And so this is the true flow. Your sub-agents are responding to your primary agent. (t: 450) And if you're doing powerful multi-agent orchestration, which you will be pretty soon here if you're watching the IndieDevDan channel, make sure you're subscribed and you comment to stay plugged in (t: 460) the key engineering information like this. You can tell I don't just copy and paste the documentation. You know, everyone's using this. (t: 470) I think, cool, that's good. Use this, but read the documentation, guys. Like, really read through this stuff. It's important. Okay. This is the most important technology of the year, probably of the next few years, right? (t: 480) Agents are how you win as an engineer. Understand what they really do, right? Don't offload all the cognitive work to your agents, right? (t: 490) Understand the most important technology. Okay. And to be super clear, there's nothing more important right now than agents. And the leading agent is Claude Code. (t: 500) If you've been following the channel, you already know this. You've been listening to me glaze the team and glaze the product. You know, really communicate the value of this, right? But it's all for a reason. (t: 510) I focus on the signal that every engineer has a superpower. Every engineer is super, super good. They're cracked at something. One of my key abilities is to focus on the signal and cancel out the noise. (t: 520) Okay. I can focus. I can focus directly on the signal of where the valuable information is. Truly, there's no other way I could show up here for you every week with something valuable if I wasn't constantly just (t: 530) obsessed and passionate about finding the signal. Okay. (t: 540) This, you know, again, this is super powerful. Copy it, throw it into an LLM, but don't offload deep understanding, right? You're basically just vibe coding then. Okay. Deeply understand your tools so that you can do more better, faster, cheaper, so on and so forth. (t: 550) Okay. Anyway, where were we? We were talking about chaining. Yes. So this is super important, right? If you want to scale up, you need to understand the information flow. (t: 560) Okay. We're not just prompting a chat interface anymore. We're not. Just dealing with one context model prompt agent system. (t: 570) We're dealing with multi agent systems. Okay. There's a reason I put up that multi agent observability video. I knew this was coming. It's here. Okay. Multi agent systems are here. (t: 580) The best engineers, the most cracked seniors. We're scaling up hard. Okay. Compute, compute, compute, compute. And you can see it here. This is just a simple flow, right? (t: 590) You prompt your agent. If our sub agents collect results. Okay. Then the next step in that custom slash command. Is going to be great. Collect the results, fire off more sub agents that, you know, create a concrete solution. (t: 600) This is all fantastic. Let's actually use this. I did mention, I'm going to share my meta agent with you. I built a 50, probably a hundred plus agents already, which is kind of crazy to say, but it's all thanks to this meta agent, right? (t: 610) As soon as you get access to a new feature, figure out how you can scale it up. Oftentimes, you know, with gen AI, you build a meta version of that, right? (t: 620) The thing that builds the thing. (t: 624) The thing that builds the thing. You build the thing. But if you build a meta version of that, you can create a meta version of that. (t: 630) And then you can build a meta version of that. And then you can build a meta version of that. All right. So there's a lot of stuff here. We're going to talk about that. But let's get right into it. So let's get right into it. What are we going to do here? Let's just delete a bunch of stuff. Okay. (t: 640) If this causes you pain, you know, deleting causes you pain. You probably aren't deleting enough. So let's get rid of this. Let's get rid of this. And let's get rid of this as well. (t: 650) Let's create new agents with our meta agent. And so to cue this up, you know, a big issue. Using technology to create solutions for problems that don't exist. (t: 660) Noobs and beginners, they start with the technology and work backward. If you want to become a real engineer, if you want to continue being a valuable engineer in the generative AI age, work the other way, work the right way, work (t: 670) the way that product builders think. You have a problem first, next comes a solution, and then the tech comes third. Okay. Problem, solution, tech. (t: 680) Okay. So what do I mean by that? Let me show you a concrete example. Let's use our meta agent to solve a real. Problem. Let's start at the top, right? What's the problem. When I'm agentic coding at scale, I lose track of what some agents have done. (t: 690) Okay. Problem statement solution, add text to speech to my agents. So they notify me when they're done and more importantly, what they've done. (t: 700) Okay. So now we have a problem and a solution. Now let's use the technology. Okay. Only after you have a problem solution, should you move the technology? So technology, we can use cloud code sub-agents. (t: 710) I have a meta agent here that I can use to build up a new. Text to speech agent that communicates what was done. Okay. Fantastic. (t: 720) Order matters, problem solution technology. So inside of dot MCP dot Jason dot sample, we have a setup for the 11 labs MCP server. I have this configured in my dot Jason here. (t: 730) This is getting ignored because of course we have environment variable information here. First, I want to understand what my agent can do. So I'm gonna run all tools, all tools looks like this. (t: 740) It's a simple reusable prompt list, all available tools in your system. Prompt. I want bullet points. I want TypeScript function signature format. This is gonna allow us to understand the tools available to us. (t: 750) And specifically I wanna see the 11 labs tools. Great. So here are all the available tools. I'm just gonna copy these out so I can quickly search. (t: 760) I need two things, text speech, and we probably need some way to play, right? So I'm gonna search text forward looking speech. There it is. Text to speech. (t: 770) So I'm just gonna paste that here. And we also want to find a play. There it is. Play. Audio. Great. Okay. So we have these two methods that we can use and we actually want the entire definition so we can know the exact parameters that we're playing with. (t: 780) Right. Let's paste this all in mode back to text format real quick. (t: 790) Great. Now let's go ahead and just run these, right? So this is in our primary agents context window. I'm just gonna say 11 labs I've completed next we can. (t: 800) And I think we have voice ID here. Let me quickly copy. One of my favorite 11 labs voice IDs. Voice ID just gonna be super clear in the prompt output directory, right? (t: 810) Output. Great. Output directory output to PWD, which is current working directory slash output. I'm gonna have cloud code Opus fire off this tool here. (t: 820) And all I'm doing here is validating the workflow. I'm going to encode into an agent. So text to speech. Great. Save the file. And now we want to run all set and ready for your next step. Great. Now we're gonna run play audio and I should just be able to say play audio because all the context is loaded. (t: 830) Okay. Okay. Our agent is prime. Okay. That was good. I was talking over it. So I'm gonna run it again. Run again. I've completed X, Y, Z next week in ABC. (t: 840) Great. So we can see that we have Texas speech working. We now have full capability to have our agents communicate with us via a sub agent. Okay. So fantastic. (t: 850) So now we can crank open our powerful meta prompt. Okay. So now that we know the workflow and the work that we want done, we can build a new agent that encapsulates this work. (t: 860) So we can open this up. And we can see exactly what our meta agent is. The agent is doing. It has a system prompt that details how to create a new agent, right? (t: 870) It's doing all this work in the isolated context window. And so I'll fast forward through this. I'm gonna type up the prompt right here. So this is what it's gonna look like, right? Build a new sub agent. So I'm asking cloud code to build the agent and you can see here generate a new complete cloud code sub agent configuration file from a user's description. (t: 880) Okay. So use this to create new agents proactively. (t: 890) So we're using the information dense. Keyword encoded by anthropic and that we should probably make this uppercase just to make it more dramatic when a user asks you to create a new sub agent. (t: 900) Okay. So we wanna make sure that we have that language inside of our prompt. Build a new sub agent. Great. We're defining when it's proactively triggered based on our conversation. (t: 910) So far, our agents will have blah, blah, blah, blah, blah. I'm just detailing exactly the flow that we went through. Okay. Get the current working directory, text to speech play. (t: 920) Great. So we didn't copy this. Let's fire this off. And now you're going to see the. Description of our meta agent here. Get activated. This is very important. You need your description to properly set up when your agent should call a specific sub agent. (t: 930) So you can see cloud code is thinking about this. It knows. And now it's gonna use the sub agent. So couple key pieces inside of this prompt. (t: 940) I'm not just having this build on zero information. I'm re pooling the cloud code documentation live. Okay. I want the most recent updated AI documentation. (t: 950) Views of the channel. You know that I like to place this in AI. Docs, but there's a more powerful way to do this where inside of AI docs, you just place a read me. (t: 960) And then on the fly you have, you know, a prompt or now a sub agent pull in live documentation with some type of refresh command. And so you can see we have a brand new agent generated there. (t: 970) Let's go ahead and understand what's happened. Great. So three tool uses our meta agent successfully created now wants to read and verify. So this is great. (t: 980) So our reasoning model is double checking the work. It's read the file. And now it exists all set and ready for the next step. Let's go ahead and check it out. (t: 990) This is a great summary voice proactive output. Auto play looks good. Let's go ahead and look at our new agent. You can see it's in the exact format as we asked. Fantastic. (t: 1000) So we can see here, you know, always review every word must add value. That's great. I like that line. No pleasantries. Yup, exactly. I'm going to add a couple of things, right? So variables, user name, and what else we're going to do here? (t: 1010) I want just one. Sentence. These can be quite long. A concise one sentence looks great. (t: 1020) I want to make a couple of tweaks here specifically to the description. This determines when your primary agent is going to call your sub agent. Cloud code has this IDK, this information that's keyword that they mentioned proactively, right? (t: 1030) They even have a little section for it used proactively or must be used. What we can do here is something a little bit extra, right? (t: 1040) You noticed in my hello agent. Hello world. Agent. I like to have these concrete tags or these concrete triggers for my sub agents. (t: 1050) So if they say X, Y, or Z use this agent. So I'm basically just going to reuse this pattern. Frankly, I should encode this into my meta agent. I'm just going to add this here, right? (t: 1060) If they say TTS, TTS summary, use this agent. I'm review user prompt given to you a concise summary of what it does, by the way. (t: 1070) Um, one of the big reasons I'm using cursor. Still is for writing prompts. It's great for that tab completion. I also want to add one thing here. (t: 1080) So coming full circle here, remember that if you go back to our basic flow here, it's your primary agent that prompts your sub agent. What your primary agent can see and has access to is this description, right? (t: 1090) This is how it knows when to call any given sub agent you have. So you really want to leverage the description and tell your top level agent how to prompt this agent. (t: 1100) When you prompt this agent, describe exactly what you want them to communicate to the user. (t: 1110) And I'm going to add even more detail here. I'm going to say, remember, and again, this is something that I really just need to encode in my meta agent. (t: 1120) Um, remember this agent has no context, any questions, conversations between you and the user and something I like to do to increase prompt adherence. (t: 1130) Um, and topic has. Another. Information dense keyword that you can use. It is this so important colon. I'm going to be absolutely clear with this agent. (t: 1140) Do these following things, right? We want a concise summary of what was done. And also I want to add one more here as practices important. Run only bash PWD and the 11 labs MCP tools. (t: 1150) Don't use any other tools. Base your response. Exactly. Okay. Let's fire up another cloud instance. (t: 1160) We'll go ahead and just use Sonnet for this one. What does this. Code base do use TTS summary after we add details to when this should be called after we add instructions to the system prompt for work completion summary sub agent. (t: 1180) We now have an operational agent that can quickly summarize in natural language for us anywhere, anytime on any piece of work, right? This is the beauty of agents and reasonable prompts. (t: 1190) Now we have this problem solved for good and we can. Reuse it and improve it when we need to code base has been analyzed and now we're getting Texas speech structure, Texas speech response. (t: 1200) There it is. Is input prompt. This code base demonstrates quad code hooks mastery with all six life cycle events implemented for deterministic control featuring security filtering, intelligent TTS feedback, automatic JSON logging and UV single file architecture. (t: 1210) Next step, explore specific hook implementations or test the security filtering functionality. (t: 1220) Nice. Okay. So that looked good or that sounded good. Rather you can see here, there is work to be done here. That was still longer than I wanted it to be. (t: 1230) If we open up logs, thanks to cloud code hooks, we are recording all the conversation that just happened. So we can do something really cool. If we want to, we can dive in and actually see our top level agent prompting our sub agent and our sub agent prompting the response back to our top level agent. (t: 1240) I'll leave that up to you. After you finish watching this video, run this fire off the hello world agent and the meta agent. (t: 1250) So you can really. Understand what the flow of information is between multi-agent systems inside of cloud. (t: 1260) So the benefits are pretty straightforward, right? Cloud code engineers said themselves context preservation. This is both a benefit and an issue. (t: 1270) We'll talk about how this is an issue in a moment, but each sub agent operates in its own context, preventing pollution of the main conversation. So this is powerful. We are booting up fresh agent instances for everyone. (t: 1280) Yeah. So this is a big one of these tasks that we have for our natural language, text to speech for our meta agent. They all have their own isolated context window. (t: 1290) This is very powerful on the channel. We're going to talk more about how you can use this to scale agents across your large complex code bases. You could already do this with sub agents, but now you can do it even better with specialized sub agents. (t: 1300) All right. So you get to save your context window. This is a big idea. That's going to come back up over and over again until we get those. (t: 1310) Two 5 million plus context windows. You know, I can tell you those aren't coming anytime soon. As far as I can see, I would love to be wrong about that. (t: 1320) Specialized agent expertise. We can, of course, fine tune the instructions and the tools. So, you know, you can see this here for our Texas speech agent. You can see, we wrote a nice rich description on not only when to trigger this, but we're giving our top level agent instructions on how prompt this. (t: 1330) Okay. (t: 1340) A lot of engineers are going to miss this. Don't be one of them. You can. I'll just construct how exactly you want the prompt to flow in here in the description. Reusability. This is a classic one. By storing this inside your repository, you can build agents for your code base. (t: 1350) There is a powerful way we're going to discuss on the channel to use a meta agent to build specialized localized agents that excel at operating specific parts of your large code base. (t: 1360) Again, subscribe, comment, all that good stuff. So you don't miss out on future valuable information like this flexible permissions. (t: 1370) You can lock down the tools your agent can call. That's fantastic. Obviously, if you're in Yolo mode, you have to be more explicit about what tools can run, which is why I have this. (t: 1380) I added this important best practice here, but that's that. So these are the kind of four key benefits that Anthropic lists directly here. I think these are all true. There are a couple of hidden benefits as well. (t: 1390) You get focused agents. All right. And you know, the Cloud Code team does mention this, but you can really take this far, right? Just like booting up a fresh agent with a single prompt when you use Cloud Code sub agents. (t: 1400) The. The agent is fresh. It only knows what your primary agent tells it. That means it's less likely to make mistakes given that you design a good system prompt. (t: 1410) Why is that? It's because your agent is focused on one thing, just like a focus engineer. When you're focused on one thing, you perform better full stop. (t: 1420) Another hidden benefit of Cloud Code sub agents is simple multi agent orchestration. And this is something that I'm most excited for with custom slash commands combined with Cloud Code hooks combined with sub agents. (t: 1430) Right. Just kind of stacking up these powerful features. You can build powerful yet simple multi agent systems. So, you know, as a concrete example, we have this in our commands. (t: 1440) We have this classic prime command and we can easily improve this. Right. So now we can have prime TTS. So say I want to kick off prime. (t: 1450) I can say when you finish run the TTS summary agent, the user know you're ready to build. I already have Cloud Code hooks with Texas speech. (t: 1460) I know. We did this in a previous video. I'll link that in the description if you're interested. But we can, of course, run this now in a fresh instance. We can run prime TTS. (t: 1470) And now when this finishes, we're going to run. We're going to chain text speech summary agent at the end. There is. And so here's the summary. It wrote the text speech. (t: 1480) So we have the audio file. Cloud Code hooks mastery project with complete coverage of all six hook lifecycle events. There you go. So it's ready to go. So if you open up that summary there, control E, you can see Cloud Code analyze hooks mastery project. (t: 1490) There you go. So it's ready to go. So it's ready to go. So it's ready to go. So it's ready to go. So it's letting us know that it is complete. We were able to guide our primary agents prompt to our sub agent right here. (t: 1500) When you finish run the TTS summary agent and let the user know you're ready to build. So this is another hidden benefit. We improve our multi agent orchestration. (t: 1510) Okay. And so another pro and con is prompt delegation. So again, this is kind of on the line. You're delegating your prompts to the primary agent oftentimes, just like you saw here and just like you (t: 1520) saw in the. Work completion summary agent right in the description. This means you have to do a little bit more work guiding your primary agent to call your sub agent properly. (t: 1530) This is powerful, though. It's still good. We are offloading work, right? We're encoding powerful engineering practices into our prompts and now into our sub agents. So all in all, it's good. So what are the issues of this? (t: 1540) Right? The opposite of context preservation is that and this was a great example because you kind of saw that you need to be super clear to your sub agents and to your primary agent. (t: 1550) What? Your. Passing into the sub agent because it doesn't have any previous context. By definition, every sub agent having its own context means that there is no context history. (t: 1560) It doesn't have the rich context that your primary agent has. It has only what your primary agent prompts it with. This is the equivalent just to make it ultra clear. (t: 1570) This is like firing up Claude in print mode, you know, whatever you can run Yolo or dangerous, blah, blah, blah. And don't run this, by the way, if you don't know what you're doing, this will run any command. (t: 1580) We're starting to see in the industry Yolo mode coders are getting cooked by this command. So be really careful. But this runs the following, right? (t: 1590) So it's basically saying call work completion summary agent and then you're passing in one prompt. Okay. This is what your primary agent is doing when it's calling the sub agents. (t: 1600) It's literally like it's you calling a one shot prompt to the sub agent. Okay. There's no context. This is a problem, right? (t: 1610) It's a problem. And it's a benefit, right? It's the opposite side of the coin of context preservation. Another big issue. These sub agents are hard to debug. You can see every time we run one of these, right? (t: 1620) If we run high CC, even with this simple prompt, we have no idea what's going on here. We'll get the tool calls, which is super, super nice. But the actual workflows, the prompts, the full parameters for every tool call, we don't get these. (t: 1630) And this is by design. But it also makes these harder to debug and understand what's going on. (t: 1640) Right? So another big issue here is decision overload. As you start scaling up the number of agents you have, it's going to be harder and harder for your agent to know which one to call. (t: 1650) Sub agent complete. Okay. Commands are a bit different. Although you, the engineer, might forget all the commands that you have, right? You just might start forgetting all these commands, right? All the powerful commands we've built. (t: 1660) Agents are a little different because based on the description and based on the number of agents you have, your primary agent might get confused here. Okay. So this is important. You really want to be keeping track of your agents. (t: 1670) Otherwise, it'll come back to bite you and you're going to kick off these agents when you don't want to. As a solution, you really want to be clear about when to call your sub agents with the description variable. (t: 1680) This is the most important variable right next to the name. Two remaining issues here. Dependency coupling is going to be a problem here. So once you start setting up prompts, primary agent reusable prompts or custom slash commands, and once these are calling out your sub agents, which this is how you start scaling up into multi agent systems. (t: 1690) Once you get that going, it's going to be hard. (t: 1700) Okay. So again, back to debugging, it's going to be hard to understand what's going on. You're going to have dependency coupling. Inevitably, you're going to have an agent depend on output from another agent, depend on the format of the specific response from another agent on and on and on. (t: 1710) And then one day you're going to need to change something to improve it or to ship something new, whatever. You're going to change the planner agent right here, and it's going to ruin everything else because one thing changed. (t: 1720) We're already operating in non-deterministic systems. When that one thing changes, it could blow up everything else. Okay. (t: 1730) So there's another. Problem with sub agents. As you start scaling them up, keep your eye on this. Try to keep them separate. Try to keep them in isolated workflows. Don't overload your sub agent chain. (t: 1740) Okay. And then last thing, and this is just kind of a request on my side, even though it is literally counterintuitive to what I was just saying. You cannot call sub agents in your sub agents. (t: 1750) Okay. Probably for all the reasons that I just mentioned, it would be cool if we had kind of like dangerous sub agent. True. (t: 1760) Right. So that inside of this agent or, you know, it's probably like sub sub agent. True. So that inside of this agent, we enable this dangerous, powerful setting of calling sub agents inside of sub agents. (t: 1770) So this is me just being picky. That's not actually a real issue, but it's important to note that, you know, you can't call sub agents in your sub agents. (t: 1780) At least not yet. That's me being hopeful. So there's a very powerful feature. We have quite a few more ideas to explore here with the combination of agents and custom slash commands, aka reusable prompts. (t: 1790) Remember that perspective matters as you start scaling up your multi agent system, right? The flow of the big three context model and prompt matter more than ever and more and more as you scale up the number of agents who have shipping work on your behalf. (t: 1800) There's a reason why it's a principle of AI coding. As usual, link in the description. (t: 1810) If you want to learn the other principles of AI coding that will help you differentiate what you can do with agentic coding. And I'll go ahead and bring back the other prompts that I had here just for fun. (t: 1820) But the meta agent is going to be here available to you. Link in the description. I'm going to add these to the cloud code hooks mastery code base. If you made it to the end, be sure to like subscribe and comment your thoughts no matter what. (t: 1830) Stay focused and keep building.

