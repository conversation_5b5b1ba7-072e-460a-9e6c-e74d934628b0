---
title: 379: <PERSON><PERSON><PERSON><PERSON> — Tiny Experiments
artist: <PERSON><PERSON><PERSON> — The Bootstrapped Founder
date: 2025-03-05
url: https://www.youtube.com/watch?v=6oXhinrC_14
---

- [00:00:00](https://www.youtube.com/watch?v=6oXhinrC_14&t=0) - Hey, it's <PERSON><PERSON><PERSON>, and this is the <PERSON><PERSON><PERSON> Founder. Today I'm talking to <PERSON><PERSON><PERSON><PERSON>, neuroscientist and author of the book Tiny Experiments, where she shines a scientific light on how we set goals and what experiments we should think about and how we can deal with uncertainty in a high-stress world. This episode is sponsored by Paddle.com, my favorite payment provider. I personally use <PERSON>ddle on several of my software projects, and I've been very happy with them over the last couple of years. Since they're a merchant of record, they really help me be confident in charging for all my

- [00:00:30](https://www.youtube.com/watch?v=6oXhinrC_14&t=30) - This episode is sponsored by Paddle.com, my favorite payment provider. I personally use <PERSON>ddle on several of my software projects, and I've been very happy with them over the last couple of years. Since they're a merchant of record, they really help me be confident in charging for all my businesses and doing that internationally. <PERSON><PERSON> takes care of sales tax and expiring credit cards, invoices, and all that, so I can focus on my business instead of chasing money. So go to Paddle.com to check it out. It's one of those important foundational choices that you really want to get right for your business. And now, here's <PERSON>-Laure. <PERSON>-Laure, welcome to the show. I've been looking back at my own creator journey over the last couple of years, and I do wonder, are we hardwired to eventually run out of steam, out of energy?

- [00:01:00](https://www.youtube.com/watch?v=6oXhinrC_14&t=60) - And now, here's Anne-Laure. Anne-Laure, welcome to the show. I've been looking back at my own creator journey over the last couple of years, and I do wonder, are we hardwired to eventually run out of steam, out of energy? Is doing the same thing over and over again with the same joy and intensity even possible? I wonder about this. Is that a thing that our brains do to us? Like, where does this come from? That's actually fascinating. We're hardwired for exploration, actually. As human beings, we like things that are new. We're interested in what's new. We like things that are interesting, that pique our curiosity. And so, if you keep on doing the exact same thing over and over and over again, you are going to run out of steam.

- [00:01:30](https://www.youtube.com/watch?v=6oXhinrC_14&t=90) - As human beings, we like things that are new. We're interested in what's new. We like things that are interesting, that pique our curiosity. And so, if you keep on doing the exact same thing over and over and over again, you are going to run out of steam. You are going to be bored. And I think that's why it's very important to inject a little bit of playfulness, a little bit of experimentation, a little bit of trying to get out of your comfort zone in what you do. And even though it can be tempting once you feel like, oh, I figured out how this thing works. You just keep on doing the same thing over and over again. make space for yourself to try things a little bit differently. And this is how you're going to be able to sustain effort over the long term by making sure that that effort doesn't look exactly

- [00:02:00](https://www.youtube.com/watch?v=6oXhinrC_14&t=120) - And even though it can be tempting once you feel like, oh, I figured out how this thing works. You just keep on doing the same thing over and over again. make space for yourself to try things a little bit differently. And this is how you're going to be able to sustain effort over the long term by making sure that that effort doesn't look exactly the same every single day. That's the thing, right? Like a lot of people, they're looking for stability and knowing what is going to come and it becomes static. And that is where it gets boring, right? Like if there's no experiments, if there's no change, no variance in this, even though it's probably good for if you're a founder, if you're somebody who's trying to do something, get to a certain kind of goal, it is nice to know what the path looks like. But if that path becomes the same thing over and over again, it becomes a problem, right?

- [00:02:30](https://www.youtube.com/watch?v=6oXhinrC_14&t=150) - even though it's probably good for if you're a founder, if you're somebody who's trying to do something, get to a certain kind of goal, it is nice to know what the path looks like. But if that path becomes the same thing over and over again, it becomes a problem, right? Yeah. And that's very interesting that you're talking here about goals and about just having this plan and this vision and applying it because to me, actually, it's not incompatible. Showing up every day and making progress is not incompatible with experimenting. The problem is when we are not doing it, we are not doing it. And that's why I think it's important to have those very, very long-term plans where that gives us the illusion of certainty, the illusion of control where we say, I know what's going to happen. I know what I'm supposed to do. I'm just going to show up and do it. The problem is that there are two things that

- [00:03:00](https://www.youtube.com/watch?v=6oXhinrC_14&t=180) - have those very, very long-term plans where that gives us the illusion of certainty, the illusion of control where we say, I know what's going to happen. I know what I'm supposed to do. I'm just going to show up and do it. The problem is that there are two things that keep on changing all the time. The world keeps on changing. There's new technology, new competitors, new demands from consumers as well. And another thing that keeps on changing is just ourselves. We change too. There is something that, you might find intellectually stimulating today that might not be as interesting tomorrow. Your personal circumstances can change, whether it's with your relationships, your family, your health, right? And so because of that, clinging to that very linear approach to goals

- [00:03:30](https://www.youtube.com/watch?v=6oXhinrC_14&t=210) - you might find intellectually stimulating today that might not be as interesting tomorrow. Your personal circumstances can change, whether it's with your relationships, your family, your health, right? And so because of that, clinging to that very linear approach to goals doesn't really make sense when you use that in just the reality of the world that we're inhabiting and the bodies and brains that we have, right? And you can actually say, this is going to be... My plan, I commit to, but for the next two weeks, for the next two months, for the next three months, for the next six months. And then I'm going to look back on that period of time. I'm going to look at the data I collected, what worked, what didn't. I'm going to evaluate it. And based on

- [00:04:00](https://www.youtube.com/watch?v=6oXhinrC_14&t=240) - My plan, I commit to, but for the next two weeks, for the next two months, for the next three months, for the next six months. And then I'm going to look back on that period of time. I'm going to look at the data I collected, what worked, what didn't. I'm going to evaluate it. And based on that, I can decide what does my next cycle of experimentation going to look like? So you can actually be quite focused and quite productive without having this illusion of a perfect plan that you're going to make. And that's what I'm going to do. And that's what I'm going to do. Do you still have long-term goals or are they just kind of vague themes that you look into? Because if I think about my next couple of weeks of work, I have all these little tiny things that

- [00:04:30](https://www.youtube.com/watch?v=6oXhinrC_14&t=270) - Do you still have long-term goals or are they just kind of vague themes that you look into? Because if I think about my next couple of weeks of work, I have all these little tiny things that are just bouncing around in my mind and in my notion document somewhere. But I do think I have this kind of long-term vision. It's not a clear goal, but it's a vision. How do you keep both things in check at the same time? Yeah, I think that the traditional definition of a goal is that as in a specific destination you want to get to doesn't work. It rarely works. Either you get there and you realize that, oh, actually happiness was not hiding just on the other side of that goal

- [00:05:00](https://www.youtube.com/watch?v=6oXhinrC_14&t=300) - as in a specific destination you want to get to doesn't work. It rarely works. Either you get there and you realize that, oh, actually happiness was not hiding just on the other side of that goal and you're disappointed, or you don't get there and you're obviously also disappointed. So it's better to have a direction, like a vector that's telling you it's pointing in the right direction. Whatever progress I'm making right now, even though I don't know exactly where it's going, even though there's no specific outcome, it feels right. It feels like I'm making progress in the right direction. And I think that's why it's better to have this sense of direction where you're directing your energy at the moment and having space for check-ins with yourself to just

- [00:05:30](https://www.youtube.com/watch?v=6oXhinrC_14&t=330) - even though there's no specific outcome, it feels right. It feels like I'm making progress in the right direction. And I think that's why it's better to have this sense of direction where you're directing your energy at the moment and having space for check-ins with yourself to just ask yourself, am I still going in a direction that feels good? Is the progress I'm making feeling good and feeling right with my values, with my priorities, with my current circumstances? The good thing about not having this end goal, but focusing on this more vector-like approach of a sense of direction is that that also allows you to go places that you could not imagine today with the knowledge that you have.

- [00:06:00](https://www.youtube.com/watch?v=6oXhinrC_14&t=360) - but focusing on this more vector-like approach of a sense of direction is that that also allows you to go places that you could not imagine today with the knowledge that you have. The problem with end goals, this very linear process, is that you can only achieve things that are possible for you to imagine today with your current knowledge, current imagination. But that's not what you really want with your business, with your life in general. You want to be able to look back in five years. And feel like, wow, I had no idea this was possible at all. That's how you want to feel. And that's what you should optimize for. That's kind of how I feel in my last couple of years. And you're probably the same, right?

- [00:06:30](https://www.youtube.com/watch?v=6oXhinrC_14&t=390) - And feel like, wow, I had no idea this was possible at all. That's how you want to feel. And that's what you should optimize for. That's kind of how I feel in my last couple of years. And you're probably the same, right? You've gone into directions that you probably didn't think possible or feasible throughout the years. How does your life look now looking back over the last decade or so? Yeah, it was definitely a series of experiments and what I just described, looking back and feeling like, oh, that's where I am today. I had no idea this was possible at all. That's definitely how I feel. I left my job at Google in 2017. I worked on a startup that didn't work out. We broke up with my co-founder. Then I found myself being completely lost and

- [00:07:00](https://www.youtube.com/watch?v=6oXhinrC_14&t=420) - oh, that's where I am today. I had no idea this was possible at all. That's definitely how I feel. I left my job at Google in 2017. I worked on a startup that didn't work out. We broke up with my co-founder. Then I found myself being completely lost and I decided to go back to the drawing board. And I asked myself, what is something I'm curious about? Even if success was out of the equation, even if I didn't have to worry about money and paying my rent, what is something I am deeply curious about? And for me, it was the brain. I had always been curious about why we think the way we think and feel the way we feel. So I decided to go back to university, to graduate school, to study neuroscience, which people around me in my late twenties were like, what are you doing? You don't become a neuroscientist

- [00:07:30](https://www.youtube.com/watch?v=6oXhinrC_14&t=450) - it was the brain. I had always been curious about why we think the way we think and feel the way we feel. So I decided to go back to university, to graduate school, to study neuroscience, which people around me in my late twenties were like, what are you doing? You don't become a neuroscientist in your thirties. That's something you just don't do. And the great thing was that I didn't do it to become a neuroscientist. I didn't have that end goal. I just wanted to recreate space in my life where I could... It's not just a space. It's a space where I could do anything. I could do anything. I could explore. I could tinker. I could learn new things and where I felt a little bit out of my depth. That's all I was optimizing for. Again, that sense of direction of going in a direction that felt good in the moment. And after I started those studies, I discovered something called the

- [00:08:00](https://www.youtube.com/watch?v=6oXhinrC_14&t=480) - I could explore. I could tinker. I could learn new things and where I felt a little bit out of my depth. That's all I was optimizing for. Again, that sense of direction of going in a direction that felt good in the moment. And after I started those studies, I discovered something called the generation effect, which shows that when you create your own version of something, you're going to both understand it and remember it better. And I felt like that's pretty neat. I want to use this. So I decided to start writing online what I was studying at school, taking those neuroscientific concepts and turning them into practical tools. That was the beginning of my newsletter, which grew pretty quickly. And then I was offered by a publisher to write a book. And what I like about my path right now is that

- [00:08:30](https://www.youtube.com/watch?v=6oXhinrC_14&t=510) - what I was studying at school, taking those neuroscientific concepts and turning them into practical tools. That was the beginning of my newsletter, which grew pretty quickly. And then I was offered by a publisher to write a book. And what I like about my path right now is that I still have no idea where I'm going. I don't know where I'm going. And this is part of what I find exciting. Yeah. Oh, man. And it's exciting to watch you be on that path. I'm a subscriber to the newsletter. I am in your Nestlabs community as well. And I'm a subscriber to your newsletter. And now a reader of your book that you just wrote. We'll get to all of this. I want to talk about every single thing here. But I also want to point out one thing that you just said, like this generation effect, the idea that in doing a thing, you master the thing. This very strongly

- [00:09:00](https://www.youtube.com/watch?v=6oXhinrC_14&t=540) - And now a reader of your book that you just wrote. We'll get to all of this. I want to talk about every single thing here. But I also want to point out one thing that you just said, like this generation effect, the idea that in doing a thing, you master the thing. This very strongly resonates with my own experience writing and just being in front of people, learning in public, building in public, doing a lot of things in front of people. There's something to it that it took me a while to understand that there's this tacit knowledge that you learn, along the way that you would never be able to get through just absorbing information or through just talking to somebody who's an expert either. Like you have to go through this. That is strange because nobody tells you this. There's this internalizing effect that cannot be gained from

- [00:09:30](https://www.youtube.com/watch?v=6oXhinrC_14&t=570) - along the way that you would never be able to get through just absorbing information or through just talking to somebody who's an expert either. Like you have to go through this. That is strange because nobody tells you this. There's this internalizing effect that cannot be gained from external sources. Yeah. And unfortunately, when you think about the way our schools are designed, they're not designed for that kind of learning at all, right? You're just sitting in a classroom, listening to an expert that is giving you that information as if you could just download it into your brain and then know it, right? Yeah. And that doesn't work this way. There's even the thing, and I'm now in a mode where I remember my school days, and I just wonder what was going on there. Like, I really loved writing back in school. I'm German. I love the

- [00:10:00](https://www.youtube.com/watch?v=6oXhinrC_14&t=600) - into your brain and then know it, right? Yeah. And that doesn't work this way. There's even the thing, and I'm now in a mode where I remember my school days, and I just wonder what was going on there. Like, I really loved writing back in school. I'm German. I love the English language. In English class, I always wrote like 10 times as much as I was supposed to. And I got bad grades for this because I didn't hit the milestone, right? I didn't hit it right. Didn't write well. I didn't write well. I didn't write well. I didn't write well. I didn't write well. I didn't write well. I didn't write well. I didn't write well. I didn't write well. I didn't write well. I wrote 10. That's a problem. That's an F. Like, instead of seeing the outburst of creativity and joy that I had in that moment and rewarding this, or at least putting some kind of value behind that, it was punished and kind of curtailed immediately. I could go on about experiences like this,

- [00:10:30](https://www.youtube.com/watch?v=6oXhinrC_14&t=630) - wrote 10. That's a problem. That's an F. Like, instead of seeing the outburst of creativity and joy that I had in that moment and rewarding this, or at least putting some kind of value behind that, it was punished and kind of curtailed immediately. I could go on about experiences like this, but it really frustrates me to see that that's kind of the linear goal mindset, right? The idea that you have these steps and you need to reach them. And once you reach them, you're great, no matter how you reach them or what you do. Yeah. And unfortunately, this is also, why a lot of adults only realize much later in life that they've been on the path that is not their path, that they've been successful based on a definition of success that is not their own definition of success. And the good thing is obviously that at some point they realize this and they can then course correct and start experimenting a little bit more and asking

- [00:11:00](https://www.youtube.com/watch?v=6oXhinrC_14&t=660) - their path, that they've been successful based on a definition of success that is not their own definition of success. And the good thing is obviously that at some point they realize this and they can then course correct and start experimenting a little bit more and asking themselves and developing that self-awareness of what it is I actually want. But it is kind of sad to think about that for many people that only happens much later. Instead of this being something you nurture in children, teaching them how to recognize your own creativity, how to recognize your own curiosity, how to nurture it, how to develop the skills that give you joy. That's not something we teach children. Yeah, unfortunately not. Can you

- [00:11:30](https://www.youtube.com/watch?v=6oXhinrC_14&t=690) - Instead of this being something you nurture in children, teaching them how to recognize your own creativity, how to recognize your own curiosity, how to nurture it, how to develop the skills that give you joy. That's not something we teach children. Yeah, unfortunately not. Can you give me some tangible examples of these linear goals that turn out to be not working for most people? Like what are the dangerous goals that people set for themselves? There are so many goals that people have that I'm not necessarily going to go through goals, but I want to question the scripts that lead you to choose those goals in the first place. And I think this is really helpful because then you can apply it to your own life and see if you've been pursuing these goals and following these scripts. So in the book, I talk about three big cognitive scripts that a lot of

- [00:12:00](https://www.youtube.com/watch?v=6oXhinrC_14&t=720) - scripts that lead you to choose those goals in the first place. And I think this is really helpful because then you can apply it to your own life and see if you've been pursuing these goals and following these scripts. So in the book, I talk about three big cognitive scripts that a lot of us follow without realizing that we're doing them. And this is based on a study from 1979, where they discovered that if you put human beings in similar situations, they don't have the same ability to do the same thing. And so they're not going to be able to behave in similar ways, which is useful in lots of cases. You don't want to overthink how you behave in every single situation, in the grocery store, at the doctor, et cetera. But there are lots of scripts that we follow in more important parts of our lives, the way we choose our careers, the way we connect with other people, the way we perform our work. So the three big scripts are,

- [00:12:30](https://www.youtube.com/watch?v=6oXhinrC_14&t=750) - in every single situation, in the grocery store, at the doctor, et cetera. But there are lots of scripts that we follow in more important parts of our lives, the way we choose our careers, the way we connect with other people, the way we perform our work. So the three big scripts are, first one, the SQL script. This is when you make decisions and choose your goals based on the decisions and goals that you had in the past. You want it to make sense, right? It's a little bit like, you know how you want your CV, your resume to make sense. And so you will choose a job or a career or any decision that you make because you want to have this nice narrative where you can tell people I was doing A and now I'm doing B. It makes sense. But you limit your options,

- [00:13:00](https://www.youtube.com/watch?v=6oXhinrC_14&t=780) - like, you know how you want your CV, your resume to make sense. And so you will choose a job or a career or any decision that you make because you want to have this nice narrative where you can tell people I was doing A and now I'm doing B. It makes sense. But you limit your options, obviously, when you do that. So the second script that we follow is the crowd pleaser script. And that's when you choose your goals based on what is going to happen in the future. So the third script is going to please people around you. Those are the kind of goals that are going to get you a lot of praise from your spouse, your colleagues, and your friends. They'll say, oh, you got the job at this company. That is so amazing. You're so cool. And you choose those goals, not based on what you want, not based on what would make you happy, but based on what is

- [00:13:30](https://www.youtube.com/watch?v=6oXhinrC_14&t=810) - are going to get you a lot of praise from your spouse, your colleagues, and your friends. They'll say, oh, you got the job at this company. That is so amazing. You're so cool. And you choose those goals, not based on what you want, not based on what would make you happy, but based on what is going to make people around you happy. That's the crowd pleaser. And the last script is particularly insidious because that's a script that we all celebrate as a society. I call it the Hollywood script. And that's the script that says that whatever goal you pursue, it needs to be big. It needs to be ambitious. It needs to have a massive impact on the world. Anything less than that, it's failure. And that's the script that might

- [00:14:00](https://www.youtube.com/watch?v=6oXhinrC_14&t=840) - it needs to be big. It needs to be ambitious. It needs to have a massive impact on the world. Anything less than that, it's failure. And that's the script that might make you want to build a startup and raise millions and go for the very big version of a project and maybe dismiss another way of building a business that might actually be more fun and more successful. And that's the script that says that whatever goal you pursue, it needs to be big. You're not going to be the only one. You're going to have to be the only one. And that's the script that says that whatever goal you pursue, it needs to be big. You're not going to be the only one. You're going to have to be the only one. You're going to have to be the only one. It's fulfilling to you, but doesn't have this Hollywood vibe to it. Yeah. Well, you are on the Bootstrap Founder podcast, which is a podcast about bootstrapping your way into like a profitable yet not gigantic company. So that is hopefully the antidote to that particular part. What stands out to me here is that all of these three scripts, and I mean,

- [00:14:30](https://www.youtube.com/watch?v=6oXhinrC_14&t=870) - Yeah. Well, you are on the Bootstrap Founder podcast, which is a podcast about bootstrapping your way into like a profitable yet not gigantic company. So that is hopefully the antidote to that particular part. What stands out to me here is that all of these three scripts, and I mean, by scripts, we kind of just already put this idea of a narrative in there, but they're all about the story that we tell ourselves, a story that others think about us or that we project onto others. And then they have this internal narrative about what their world should look like. It's not about story. So can we think goals without story? Is that possible? To me, a goal is kind of the end of like all these little things that get there. Even a nonlinear goal is something where I still have a theme, a target somewhere out there. It may not be clearly defined,

- [00:15:00](https://www.youtube.com/watch?v=6oXhinrC_14&t=900) - It's not about story. So can we think goals without story? Is that possible? To me, a goal is kind of the end of like all these little things that get there. Even a nonlinear goal is something where I still have a theme, a target somewhere out there. It may not be clearly defined, but it is still many steps away. And all of these steps form a narrative. Can we get rid of narrative in goal forming at all? This is such a great question. Great question. And you're right. I think your instinct of you were kind of implying that that would probably be impossible. And you're right. We are storytelling creatures. We love stories. We need them. This is the way we make sense of the world. This is the way we connect to each other. What I'm recommending here is not to completely let go of stories, is to be intentional and aware

- [00:15:30](https://www.youtube.com/watch?v=6oXhinrC_14&t=930) - would probably be impossible. And you're right. We are storytelling creatures. We love stories. We need them. This is the way we make sense of the world. This is the way we connect to each other. What I'm recommending here is not to completely let go of stories, is to be intentional and aware of the stories that you have in your life and the stories that drive your decisions. And so you can write your own story. You can write your own story. You can write your own story. You can write your own script for the movie of your life without copy pasting a script that someone else has written for you. And so it's really about injecting a bit more intentionality in the stories that we write for ourselves. Yeah. Also a thing that we often do like with manifestations, right? We say good things to ourselves because our brain picks up the good just as much as the bad. So self-talk

- [00:16:00](https://www.youtube.com/watch?v=6oXhinrC_14&t=960) - for you. And so it's really about injecting a bit more intentionality in the stories that we write for ourselves. Yeah. Also a thing that we often do like with manifestations, right? We say good things to ourselves because our brain picks up the good just as much as the bad. So self-talk is also something that I would like to talk to you about. I heard, and I never read about this. It's just people telling me stuff that if you say negative things or if you try to negate negative things, do a double negation. Like I'm not an idiot. If you say something like this, all your brain hears is idiot. Is that correct? What's the basis there? Yeah. I heard that too. I heard that too. I would need to actually read the research to give you a proper evidence-based answer, but at an intuitive level, that does make sense to me

- [00:16:30](https://www.youtube.com/watch?v=6oXhinrC_14&t=990) - something like this, all your brain hears is idiot. Is that correct? What's the basis there? Yeah. I heard that too. I heard that too. I would need to actually read the research to give you a proper evidence-based answer, but at an intuitive level, that does make sense to me in terms of how the brain connects to language. And there's this theory of salience when it comes to words. So you'll connect to the word that has the most salience in the sentence. And that's the only thing you're going to pay attention to. And so based on the other research I've read, I felt like that makes sense to me. Yeah. It makes intuitive sense to me too, because I'm generally a very positive, optimistic person and I don't have negative self-talk,

- [00:17:00](https://www.youtube.com/watch?v=6oXhinrC_14&t=1020) - And that's the only thing you're going to pay attention to. And so based on the other research I've read, I felt like that makes sense to me. Yeah. It makes intuitive sense to me too, because I'm generally a very positive, optimistic person and I don't have negative self-talk, which that might sound boastful. I just don't have it. Like I think I'm great. And other people tell me the same thing, which particularly ever since I started like being a presence in public in front of my audience. I'm like, I'm not going to be a person who's going to be a person who's going to be a person who's going to be a person who's going to be a person who's going to be a person who's entrepreneurial and my software peers, this has been amplified massively and has made my life much better. And now I compare it to people who complain all the time, who are negative, who like try to pull other people down and their life looks to be even more miserable than before. So there is something about this amplification effect too, like once you do it in front of other people.

- [00:17:30](https://www.youtube.com/watch?v=6oXhinrC_14&t=1050) - much better. And now I compare it to people who complain all the time, who are negative, who like try to pull other people down and their life looks to be even more miserable than before. So there is something about this amplification effect too, like once you do it in front of other people. Yeah, absolutely. And this is what makes it challenging for people who are stuck in the cycle of cynicism to change the way they perceive the world and the way they think about themselves is that there is a startup effect here where when you start building this confidence, when you start having this positive self-talk, you are going to put positive energy into the world. You're going to connect with other people. You're going to contribute in ways that are helpful to your community. They're going to mirror that back to you and say, thank you so much. That was so helpful.

- [00:18:00](https://www.youtube.com/watch?v=6oXhinrC_14&t=1080) - having this positive self-talk, you are going to put positive energy into the world. You're going to connect with other people. You're going to contribute in ways that are helpful to your community. They're going to mirror that back to you and say, thank you so much. That was so helpful. You're a great human being that creates confidence and that creates this positive cycle. Whereas if you're stuck in that cynical mindset type of loop, you also bring that negative energy. You probably are not that supportive or helpful to your community. You also get that negative feedback from people around you. And that reinforces in your mind, the idea that you're right, that you're not good enough because that's what other people think as well. So it's very hard

- [00:18:30](https://www.youtube.com/watch?v=6oXhinrC_14&t=1110) - are not that supportive or helpful to your community. You also get that negative feedback from people around you. And that reinforces in your mind, the idea that you're right, that you're not good enough because that's what other people think as well. So it's very hard actually. And I do have empathy for people who are stuck in this negative loop because breaking free from that, taking that first step is actually the hardest. What's the tiniest experiment you could do in that moment? Like what's the littlest thing you can do to step out of this loop? So you could design a very, very tiny experiment. For me, the tiniest experiments, they can take only a few days. You could say, I'm going to do this for three to five days because the only objective here is to prove to yourself that you can do it and you can create positive

- [00:19:00](https://www.youtube.com/watch?v=6oXhinrC_14&t=1140) - So you could design a very, very tiny experiment. For me, the tiniest experiments, they can take only a few days. You could say, I'm going to do this for three to five days because the only objective here is to prove to yourself that you can do it and you can create positive momentum. And so if you're stuck in this cynical loop of saying, I'm not good enough, I'm not, you know, you can just do it. You can do it. You can do it. You can do it. You can do it. You can pick an action that actually, you know, is good, is beneficial, is helpful, and just do it three times, five times. So maybe for the next five days, I'm going to be the one taking the notes in my team meeting. And I'm going to send that to everyone. I'm just going to do that. I'm not going to wait for anyone to ask me to do it. I'm just going to be helpful in this way.

- [00:19:30](https://www.youtube.com/watch?v=6oXhinrC_14&t=1170) - times, five times. So maybe for the next five days, I'm going to be the one taking the notes in my team meeting. And I'm going to send that to everyone. I'm just going to do that. I'm not going to wait for anyone to ask me to do it. I'm just going to be helpful in this way. Maybe for the next five days, I'm going to read some tutorials and then I'm going to post quick recaps on X or Twitter just to be helpful to the community. It always starts with maybe I'll just change that one thing. I'll do that one thing differently. And the great thing about designing experiments is that just like a scientist, you're withholding judgment until the end of the experiment. You're just committing to collecting the data, but you don't analyze the data while

- [00:20:00](https://www.youtube.com/watch?v=6oXhinrC_14&t=1200) - experiments is that just like a scientist, you're withholding judgment until the end of the experiment. You're just committing to collecting the data, but you don't analyze the data while you're running the experiment. You just say, I'm going to commit to do that thing, collect that data for that duration. And at the end, you're going to do that thing. And at the end, you're going to do that thing. And at the end, you're going to do that thing. And at the end, you're going to look back at the end of the week and I can then check in with myself and ask, how did that feel? How did people react? What conversations did I have on the back of this? Is that something I want to keep doing? Do I want to tweak it? Or maybe I didn't like it. I'll do something else. That's interesting. And it makes me think about maybe I've been running my experiments wrong. Because the thing you just said, like removal of bias while you run the experiment, that's something I never consciously did before. And now that you

- [00:20:30](https://www.youtube.com/watch?v=6oXhinrC_14&t=1230) - Is that something I want to keep doing? Do I want to tweak it? Or maybe I didn't like it. I'll do something else. That's interesting. And it makes me think about maybe I've been running my experiments wrong. Because the thing you just said, like removal of bias while you run the experiment, that's something I never consciously did before. And now that you put that into my mind, I think I'm going to do it. When I run a marketing or a sales experiment, a product experiment, I always feel like it has to start working immediately. While I'm doing it, it's like, oh, is this working? Maybe not. Maybe I need to change something. That now feels wrong. Can you kind of elaborate on that a little bit? Yes. So if you think about the scientific method, the way it works is that you need a certain number of trials before you know whether the thing is working or not, right?

- [00:21:00](https://www.youtube.com/watch?v=6oXhinrC_14&t=1260) - to change something. That now feels wrong. Can you kind of elaborate on that a little bit? Yes. So if you think about the scientific method, the way it works is that you need a certain number of trials before you know whether the thing is working or not, right? If you do the thing once, you can't really draw any conclusions. You need to do it several times and you need to collect enough data that you can reliably say there is a correlation between A and B, right? And that's the same with the experiments you run in your business and in your personal life. You decide in advance how many trials and you decide this from a rational place, just based on the experiment. If the experiment is to send a weekly newsletter, for example, you can have a quick look at other newsletters, how long it took them to grow. And you can say,

- [00:21:30](https://www.youtube.com/watch?v=6oXhinrC_14&t=1290) - life. You decide in advance how many trials and you decide this from a rational place, just based on the experiment. If the experiment is to send a weekly newsletter, for example, you can have a quick look at other newsletters, how long it took them to grow. And you can say, actually for this, probably I will need six months. To be able to know if this is working or not. So once you've decided that, you design the experiment. So you pick the action, you pick the duration. Action here is send a weekly newsletter. Duration is six months. And then you do it. That's it. You cannot judge the result until you're done collecting the data at the end of the six months. It's just like a scientist. You wait until you've collected all of the data. How do you stay accountable? Because six months

- [00:22:00](https://www.youtube.com/watch?v=6oXhinrC_14&t=1320) - Duration is six months. And then you do it. That's it. You cannot judge the result until you're done collecting the data at the end of the six months. It's just like a scientist. You wait until you've collected all of the data. How do you stay accountable? Because six months is a long time. Even six days is a long time for a thing that you're not used to. How do you build accountability into this process? It really depends on people, but something that works for most people is creating accountability with someone else. So that could either be finding a little kind of project buddy that is also trying to do the same thing. So in that example, I'm just going to use the same example, but someone else who wants to start a newsletter and you kind of do it together. It could be also learning in public. So kind of sharing whatever you're learning through the

- [00:22:30](https://www.youtube.com/watch?v=6oXhinrC_14&t=1350) - with someone else. So that could either be finding a little kind of project buddy that is also trying to do the same thing. So in that example, I'm just going to use the same example, but someone else who wants to start a newsletter and you kind of do it together. It could be also learning in public. So kind of sharing whatever you're learning through the journey, telling people. I'm on this journey of building a newsletter. That's what I actually did with the Nest Labs newsletter, just learning in public. And I know you're a big advocate for this as well. So learning in public is also a great way to create accountability. That being said, for some people, the project is interesting enough that they don't even necessarily need that kind of external accountability. And I would maybe even use that as a signal when you choose

- [00:23:00](https://www.youtube.com/watch?v=6oXhinrC_14&t=1380) - for some people, the project is interesting enough that they don't even necessarily need that kind of external accountability. And I would maybe even use that as a signal when you choose your experiments. Just if you're hesitating between two or three different experiments and you can't run them all at the same time, using the level of excitement that you have, the level of curiosity as a signal, a factor that you can use to choose which experiment to run in the first place where you might not even need the external accountability. Do you formalize this? Do you have like a document where you say, this is what I want to run. This is how long it takes to take notes along the way? Yes, a hundred percent. But that also can take different forms. So the only

- [00:23:30](https://www.youtube.com/watch?v=6oXhinrC_14&t=1410) - place where you might not even need the external accountability. Do you formalize this? Do you have like a document where you say, this is what I want to run. This is how long it takes to take notes along the way? Yes, a hundred percent. But that also can take different forms. So the only thing to formalize is at the beginning, that's for everybody who wants to run an experiment. Is what is the action? What is the duration? That you decide in advance and you can't touch it until you're done with the experiment with collecting data. Then the actual taking notes and collecting the data that can take a lot of different forms. So personally, I love writing. So usually I just create a little Google doc and I put my notes in there, or you can put that in your note-taking system. I know sometimes that it makes more sense for me to just record little videos.

- [00:24:00](https://www.youtube.com/watch?v=6oXhinrC_14&t=1440) - and collecting the data that can take a lot of different forms. So personally, I love writing. So usually I just create a little Google doc and I put my notes in there, or you can put that in your note-taking system. I know sometimes that it makes more sense for me to just record little videos. So the experiment I'm running is a little bit more complicated. I'm going to show you how to do it. So I'm going to show you how to do it. So I'm going to show you how to do it. So I'm going to show you how to do it. So I'm going to show you how to do it. So I'm going to show you how to do it. So we're running at the moment is to take a 20 minute walk every day for the next 20 days, because I had noticed I was staying in front of my computer a lot and not moving my body for this. I'm recording a little video on my phone every time that I go for my walk. And I just capture a few thoughts. I just film around me a little bit and that will go in my Google photos. And I will see it in one year when it pops again. And that's my way. I don't write anything. I just,

- [00:24:30](https://www.youtube.com/watch?v=6oXhinrC_14&t=1470) - I'm recording a little video on my phone every time that I go for my walk. And I just capture a few thoughts. I just film around me a little bit and that will go in my Google photos. And I will see it in one year when it pops again. And that's my way. I don't write anything. I just, that's my recording for it. And for other people who don't necessarily like writing, or taking videos, again, a little check-in with a friend, that also counts as collecting data. Just saying, oh, this week it's been going like this. And again, I'm not judging whether the experiment is working or not. I'm just paying attention to how it feels, what's working, what's not working. So at the end of the experiment, I was aware enough during the process that I can actually make decisions. Oh, that's interesting. Yeah, I like the video

- [00:25:00](https://www.youtube.com/watch?v=6oXhinrC_14&t=1500) - experiment is working or not. I'm just paying attention to how it feels, what's working, what's not working. So at the end of the experiment, I was aware enough during the process that I can actually make decisions. Oh, that's interesting. Yeah, I like the video idea. That's an accountability system to me. Having to repeat something that is not easily done, but once it's done, it's part of something bigger. That's really cool. I like that. Now, the data scientist in me and the guy who went to university and did quantitative and qualitative studies wonders, what do I measure? What do I track? Because if you have an experiment and you say, I'm going to do this, I'm going to do it for how long, and I'm going to take notes, the question now is, which of those notes are going to be valuable in evaluating the outcome of the experiment? How do I approach this?

- [00:25:30](https://www.youtube.com/watch?v=6oXhinrC_14&t=1530) - studies wonders, what do I measure? What do I track? Because if you have an experiment and you say, I'm going to do this, I'm going to do it for how long, and I'm going to take notes, the question now is, which of those notes are going to be valuable in evaluating the outcome of the experiment? How do I approach this? Maybe as somebody who does not necessarily have this scientific mindset. Yeah, and I love that you mentioned quantitative and qualitative because it is an assumption that is wrong, that a lot of people have, that you have to track quantitative data for this, and that it has to be in a spreadsheet or that it has to use your Apple Watch or anything like that. And quantitative data can be useful, but qualitative data is also incredibly helpful. And so the two types of signals and data that I really recommend tracking,

- [00:26:00](https://www.youtube.com/watch?v=6oXhinrC_14&t=1560) - that it has to be in a spreadsheet or that it has to use your Apple Watch or anything like that. And quantitative data can be useful, but qualitative data is also incredibly helpful. And so the two types of signals and data that I really recommend tracking, are external and internal signals. External signals are anything that are the more like traditional metrics of success based on your experiment. So if you're running, for example, a business experiment, you might want to be tracking whatever open rates, the money you're making, the engagement rates, or whatever kind of metrics that make sense here for your business. But then there are the internal signals. How does that feel to actually do the thing? Because if you are doubling your revenue, you're going to be tracking the internal signals. So if you're

- [00:26:30](https://www.youtube.com/watch?v=6oXhinrC_14&t=1590) - making, the engagement rates, or whatever kind of metrics that make sense here for your business. But then there are the internal signals. How does that feel to actually do the thing? Because if you are doubling your revenue, you're going to be tracking the internal signals. So if you're doubling your revenue through an experiment, but you feel completely burned out and you hate working on this, that's actually important data, important information also to be able to consider. So I would track both external and internal information, again, without judging yet. We're just tracking and paying attention to the signals. And a tool that I recommend using that has been one of the most popular tools that I've shared at Nest Labs is, you'll see, great naming. It's called Plus Minus Next.

- [00:27:00](https://www.youtube.com/watch?v=6oXhinrC_14&t=1620) - and paying attention to the signals. And a tool that I recommend using that has been one of the most popular tools that I've shared at Nest Labs is, you'll see, great naming. It's called Plus Minus Next. And it really, really describes what it is. It's three columns. The first column, plus everything that worked, that feels good. Minus everything that didn't really work, didn't go as expected, doesn't feel so good. And next, what you might want to tweak in the future, what you might want to experiment with next, whether you feel like, I think I might have to stop this when I'm done, this is not really working, or maybe I'll keep going, but I will hire another person to help me or any changes that you want to make.

- [00:27:30](https://www.youtube.com/watch?v=6oXhinrC_14&t=1650) - to experiment with next, whether you feel like, I think I might have to stop this when I'm done, this is not really working, or maybe I'll keep going, but I will hire another person to help me or any changes that you want to make. When you do that, and if you collect that data regularly, as part maybe of your weekly review, you just capture the good, the bad, and potential avenues for exploration, you will have the data that you need in the end to make a decision. Wow, that sounds great. That's a wonderful framework. I love this. It has all the things you need to critique yourself, to kind of feel the joy of getting things right, and also get the learnings from getting things wrong. Perfect, both either way. But then they're taking the next step, and they're taking the next step, and they're taking

- [00:28:00](https://www.youtube.com/watch?v=6oXhinrC_14&t=1680) - I love this. It has all the things you need to critique yourself, to kind of feel the joy of getting things right, and also get the learnings from getting things wrong. Perfect, both either way. But then they're taking the next step, and they're taking the next step, and they're taking the next step, or preparing for the next step. That is a framework I will now implement in my own thinking. I really, really like this. Uncertainty is such a weird thing that we often block our own efforts to enjoy it, to embrace it, by just being afraid of not knowing what's next, not knowing what's coming. How do we get better? How do I get better at this? How do I become friends with uncertainty? What's the path for me there? Yeah, I think the first step is to understand why we're in this situation. And I think that's a really good point. I think that's

- [00:28:30](https://www.youtube.com/watch?v=6oXhinrC_14&t=1710) - not knowing what's coming. How do we get better? How do I get better at this? How do I become friends with uncertainty? What's the path for me there? Yeah, I think the first step is to understand why we're in this situation. And I think that's a really good point. I think that's where we fear uncertainty so much in the first place. And so you can stop kind of blaming yourself when that happens. From an evolutionary perspective, it makes sense for our brains to really dislike uncertainty. Because in the jungle, the more information you have, the more likely you are to survive, right? In today's world, you don't want to just survive, right? You actually want to thrive. You want to grow. You want to learn. You want to explore. Trying to resolve uncertainty too quickly is going to give you...

- [00:29:00](https://www.youtube.com/watch?v=6oXhinrC_14&t=1740) - are to survive, right? In today's world, you don't want to just survive, right? You actually want to thrive. You want to grow. You want to learn. You want to explore. Trying to resolve uncertainty too quickly is going to give you... A sense of safety, sure. And that's what we're really craving. But it's also going to close many doors to opportunities, to things that might have been interesting, but that don't come with that sense of certainty and safety. So I think the first step is just understanding what's going on in your brain and understanding that when you really seek certainty, it's because you want to feel safe. But if you're someone who's kind of ambitious and wants to explore and create, you might want to...

- [00:29:30](https://www.youtube.com/watch?v=6oXhinrC_14&t=1770) - on in your brain and understanding that when you really seek certainty, it's because you want to feel safe. But if you're someone who's kind of ambitious and wants to explore and create, you might want to... Leave a little bit of room for that uncertainty in order to grow. And then you can actually play with that uncertainty. And that's what scientists do. When you design an experiment, what you do is that instead of being in this kind of analysis paralysis and overthinking where you're panicking because this looks quite scary, you don't know where you're going, you can take the uncertainty and say, hello, old friend. Hi, uncertainty. Let's learn together. What are some interesting questions that you can ask yourself? And then you can actually play with that uncertainty. And that's

- [00:30:00](https://www.youtube.com/watch?v=6oXhinrC_14&t=1800) - because this looks quite scary, you don't know where you're going, you can take the uncertainty and say, hello, old friend. Hi, uncertainty. Let's learn together. What are some interesting questions that you can ask yourself? And then you can actually play with that uncertainty. And that's what I'm going to do. So I'm going to start with you, and then I'm going to ask you questions. What are some doubts that I could turn into a little project where I could try it? And I love what you said earlier about learning from what goes wrong and the mistakes also. It's really creating this kind of movement where you make progress. You don't know exactly what's going to happen. Again, you don't have this linear sense of progression, but you're not stuck anymore. You're now actively collecting data and learning from that uncertainty. Yeah.

- [00:30:30](https://www.youtube.com/watch?v=6oXhinrC_14&t=1830) - creating this kind of movement where you make progress. You don't know exactly what's going to happen. Again, you don't have this linear sense of progression, but you're not stuck anymore. You're now actively collecting data and learning from that uncertainty. Yeah. That is the path, right? The path is taking all the data you can get and turning it into manageable insights, no matter where it goes. That's the idea of an experiment. Here's something that is certain. You wrote a book on this issue that's going to help a lot of people figuring out how to become better in tiny steps, tiny increments in their lives, tiny experiments. I'm really, really looking forward to seeing people embracing the concepts. That is, to me, something that is desperately needed for many people who are stuck. So if people want to,

- [00:31:00](https://www.youtube.com/watch?v=6oXhinrC_14&t=1860) - I'm really, really looking forward to seeing people embracing the concepts. That is, to me, something that is desperately needed for many people who are stuck. So if people want to, find the book, learn more about you, join your newsletter, join your communities, all of these things, where should they go? Yes. So you can go to nestlabs.com slash newsletter for my newsletter, which I send every Thursday and where I talk about a lot of the topics that we discussed together today. Or you can go to nestlabs.com slash book if you want to order your copy of Tiny Experiments. I also highly recommend just to look it up on anywhere books are sold, including your local bookshop if you want to support them. So you can find it anywhere books

- [00:31:30](https://www.youtube.com/watch?v=6oXhinrC_14&t=1890) - Or you can go to nestlabs.com slash book if you want to order your copy of Tiny Experiments. I also highly recommend just to look it up on anywhere books are sold, including your local bookshop if you want to support them. So you can find it anywhere books are sold. That's wonderful. Well, thank you so much for sharing a tiny glimpse of this massive problem space with me today. I learned so many things already. It's crazy, like so many actionable things that I can immediately use. That was a wonderful exchange. Thank you so much, Anne-Laure. Thank you so much for having me. And that's it for today. Thank you for listening to The Bootstrap Founder. You can find me on Twitter at avidkal, A-R-V-I-D-K-A-H-L. If you want to support me and the show, please share podscan.fm with your professional peers and those who you

- [00:32:00](https://www.youtube.com/watch?v=6oXhinrC_14&t=1920) - And that's it for today. Thank you for listening to The Bootstrap Founder. You can find me on Twitter at avidkal, A-R-V-I-D-K-A-H-L. If you want to support me and the show, please share podscan.fm with your professional peers and those who you think are going to be interested in the podcast. will benefit from tracking mentions of their brands and businesses, and maybe even names on podcasts out there. Podscan is a near real-time podcast database with a stellar API. So feel free to share the word with those who you think need to stay on top of the podcast ecosystem. Shout out to my sound engineer, Danielle Simpson, who edited and mixed this episode. Thank you so much.

