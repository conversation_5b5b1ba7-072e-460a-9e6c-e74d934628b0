---
title: "ULTIMATE Claude Code: UPGRADE Your CLAUDE CODE NOW! with these 15 TOOLS!"
artist: AICodeKing
date: 2025-07-14
url: https://www.youtube.com/watch?v=imz5WWVpkRc
---

(t: 0) Hi, welcome to another video. So, I was thinking of telling you guys how you can increase your (t: 10) Clawed Code setup by quite a big margin, rather than just using plain Clawed Code. And don't worry, I won't be talking about things like context engineering and all that. This will (t: 20) mainly be about the definitive tools that enhance the experience of using Clawed Code in multiple ways. Most of the things I'm going to talk about have already been covered in (t: 30) detail in my previous videos. So, you can check those respective videos out if you want (t: 40) to know more about them, as well as setup details and everything. Also, these are the tools that I use almost regularly with Clawed Code. (t: 52) Let's start talking about them one by one. Let's begin with some MCP servers that I have configured with Clawed Code. The first one (t: 60) is Context 7. This is one of the very few MCP servers that I actually use. Most of you (t: 70) would know about it, but for those who don't, it can fetch documentation and example snippets for Clawed Code for libraries or things that it isn't aware of. (t: 80) For example, if there's a new library that you want to use, it can fetch the documentation for implementation, (t: 90) and Clawed Code can use that. It's really good, and all you have to do to use it is just paste the MCP server string here in the config file. You should be good to go, and (t: 100) it should work well. This is the one that I use almost regularly. Another MCP that I (t: 110) use is the Fetch MCP tool. This is by Anthropic themselves, and works way better than most web scraping MCPs. It (t: 120) allows Clawed Code to fetch and scrape pages quite well, and it is something that I have. I only have these two MCPs set up with it, and nothing else. I believe that the less (t: 130) stuff and overhead you have with your coder, the better it works and adheres to prompts in all kinds of scenarios. (t: 140) Anyway, now let's talk about the MCP server. The first MCP server is the one that I use most regularly. The second MCP server is the one that I use most regularly. The third MCP server is the one that I use most regularly. The fourth MCP server is the one that I use most regularly. The fifth MCP server is the one that I use most regularly. The fourth MCP server is the one that I use most regularly. The sixth MCP server is the one that I use most regularly. The sixth MCP server is the one that I use most regularly. But, before we do that, let me tell you about Ninja Tools. (t: 150) Ninja Tools is an AI platform that combines all the best AI models and experiences at one place. It allows you to save over $600 per year compared to having separate subscriptions. (t: 160) You get access to Clawed, 3.7 Sonnet, GPT-4-0, Gemini and a ton of other models in one subscription. You even get some more cool options like AI video generation, image generation, and (t: 170) image rendering. music generation, and document chats. You can also use their Playground to compare multiple AI responses at once. The best part is that it just starts from $11 per month that gives you (t: 180) more than 1,000 chat messages, 30 AI image generation, and 5 music generation, while there (t: 190) is also some even more advanced plans if you need them. Also, make sure to use my coupon code AI Code King 20 to get an additional 20% off. Make sure to check Ninja Tools out and save some money (t: 200) on your subscription while you're at it. Now, back to the video. So, one of the best ones is Claudia. Claudia is a desktop graphical interface as well, but it's not just that because it has (t: 210) some cool features too. For example, there's the Agents option that allows you to give it a system (t: 220) prompt and make custom agents. You can make an agent with a system prompt for testing, or one to make apps in a specific design style, (t: 230) and then you can give it a system prompt for testing. And it will just run in the background and get stuff done. This can also be a simple graphical interface if you want, which is awesome. However, (t: 240) there's also one more thing that is a graphical interface, but I use it way more than Claudia, and that is Cloud Code UI. It's different from Claudia (t: 250) in the sense that it's not desktop-based, but instead, it's web-based. (t: 260) You can use it to create a web-based desktop interface, or you can use it to create a web-based desktop interface. You can run this, and it allows you to use Cloud Code from a web UI, letting you put it on a server, or share it locally and use it on your phone. (t: 270) It allows you to do long-running tasks and then check back on your phone when you are away. You can host it on a server and access it easily and do multiple things like that. (t: 280) I have covered it in depth in my other videos, so you can check that out as well. (t: 290) Another thing that is quite cool is Vibikit. This is also an open-source library that allows you to run Cloud Code or Gemini CLI or similar agents in sandboxes. This lets you script some (t: 300) apps that sandbox a repo and then do edits and pushback, which is quite awesome as well. (t: 310) It also has two example repos. One is a Codex-like clone, and one is a V0-like clone. (t: 320) Both of them are good, but they are not the same. They are the same, but they are different. They are good in plain sight, but I am unable to run either of them correctly. So, you can check this out as well. It is quite good. (t: 330) Anyway, there's also the Cloud Code GitHub integration. You can get it set up, and it allows you to assign issues to Cloud Code via GitHub asynchronously. It works via (t: 340) the GitHub Actions VMs and is quite similar to assign to Codex or Jules things, and I really use it a lot. (t: 350) Secondly, there's also another thing that I have been using a lot, and this one is called Cloud Code Router. Cloud Code Router basically allows you to use different models with Cloud (t: 360) Code. It lets you use models from any provider. For example, I've been using Cloud Code with (t: 370) Kimmy K2 a lot with this, and it allows you to save a ton of costs while having good capabilities as well, which is quite awesome if you ask me. (t: 380) You can configure it with Kimmy or maybe Gemini as well, and it works quite well without any kind of issues, which is also awesome. Another thing that exists, but I don't use it, is SuperCloud. (t: 390) It aims to add a ton of slash commands as well as a lot of custom instructions. (t: 400) It gives you some useful commands and system instructions, but it just adds a ton of overhead for me rather than anything. (t: 410) It also allows you to save a ton of money on your cloud code. So, I have stopped using that, and I wouldn't recommend it as much either. (t: 420) Some more stuff is CC Usage. CC Usage allows you to monitor your costs that you are incurring with Cloud Code. (t: 430) Or if you are using a Cloud Code Max subscription, then you can use this to see how much you are saving with the subscription, as it shows you the token-based pricing based on the model (t: 440) that you are using. So, this is also cool. Another one is CC Undo. This is also a good one that allows you to undo or redo cloud code changes easily. (t: 450) It provides granular undo and redo functionality. It reads directly from cloud code session files to track file operations, and allows (t: 460) you to selectively revert or restore changes with full preview and cascading safety. (t: 470) So, there's that. These are the tools that are actually useful and that I use. It's not about crazy prompts or anything. (t: 480) I try to keep most of the stuff super simple without messing around with custom prompts or commands or context engineering and whatnot. (t: 490) I think that the base cloud code is good, but you can do some very little things that make your daily use cases more useful. (t: 500) Like some good MCL. Or some good UIs. And some good integrations in your daily workflow, like GitHub issues and stuff as well. (t: 510) That is mainly the stuff you can use to transform your cloud code experience by a lot. Overall, it's pretty cool. (t: 520) Anyway, share your thoughts below and subscribe to the channel. You can also donate via Superthanks option or join the channel as well and get some perks. I'll see you in the next video. (t: 530) Bye!

