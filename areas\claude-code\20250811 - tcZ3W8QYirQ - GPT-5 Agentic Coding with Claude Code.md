---
title: GPT-5 Agentic Coding with <PERSON>
artist: IndyDevDan
date: 2025-08-11
url: https://www.youtube.com/watch?v=tcZ3W8QYirQ
---

(t: 0) It's incredible what you can do with a single prompt. We're running GPT-5 Mini Nano right next to Opus, Opus 4.1, Sonnet, Haiku, and the new GPT (t: 10) OSS 20 billion and 120 billion that are running directly on my M4 Max MacBook Pro. (t: 20) So you can hear the agents are completing their work. (t: 30) In parallel, we have natural language responses coming back to us. And at the end here, we're going to get a concrete comparison of how these models performed (t: 40) across the most important three dimensions, performance, speed, and cost. We have a brand new agentic model lineup that we need to break down in this video. (t: 50) We're going to look at a concrete way of how we can flatten the playing field to really understand how these models perform. (t: 60) Side by side, all of our Cloud Code sub-agents running in their own respective nano agents have finished their work. All set and ready for the next step, Dan. We have concrete responses and concrete grades for every single model. (t: 70) So we are using Cloud Code running Opus 4.1 in the LLM as a judge pattern to determine (t: 80) what models are giving us the best results. And you can see something really, really awesome here. Total cost on GPT OSS? Zero. (t: 90) Sense. In this video, we dive into fundamental agentic coding and attempt to answer these questions. Can GPT-5 compete with Opus 4.1? (t: 100) Has useful on-device local LLM performance been achieved? And what's the best way to organize all the compute available to you? (t: 110) If these questions interest you, stick around and let's see how our agents perform on fundamental agentic coding tasks. (t: 120) So as you've seen already, every single tech YouTuber, content creator, they've turned the camera on, recorded the screen, and literally just spat back out the benchmarks of all these brand new models (t: 130) back out to you, just regurgitated exactly what the post tells you itself. Okay. (t: 140) If you've been with the channel for any amount of time, you know that we don't do that here. We dive deeper. We actually use this technology and we develop a deep understanding. So there's a lot of things that we can do to help you. And we're going to talk about that in a little bit. So let's get started. So let's get started. So let's get started. So let's get started. So let's get started. So let's get started. (t: 150) So let's get started. (t: 170) So let's get started. agents. First, let's understand how you and I, engineers with our boots on the ground, (t: 180) can have a better, deeper understanding of these models' agentic performance. It's not about a (t: 190) single prompt call anymore. It's about how well your agent chains together multiple tools to accomplish real engineering results on your behalf. What just happened with this prompt? (t: 200) You can see here we have rankings. Surprisingly, we have Claude 3 Haiku outperforming all of our other models. If you look at this, it looks backward. We would expect these models to be on (t: 210) top and these other models to be on the bottom. What's going on? So we're evaluating all of our models against each other in a fair playing field where we care about performance, speed, and cost (t: 220) as a collective. These agents are operating in a nano agent, a new MCP server, to create a fair (t: 230) playing field where every one of these models is scaffolded with the same context and prompt right to of the big three. And then we get to see how they truly perform, right? If we put these (t: 240) models inside Claude code, I can guarantee you Opus and Sonnet will outperform. But you can see (t: 250) here for this extremely simple task, what's the capital? We can see very different results out of the box. Okay. And of course, we're going to scale this up. Let's go ahead and throw a harder, (t: 260) more agentic problem at these models. I'll run that hop. Hop is higher order prompt. We'll break it down in just a second. And then we have this prompt that we're passing into this prompt basic (t: 270) read test. Let's fire this off. We're running a multi-model evaluation system inside of Claude (t: 280) code. So we prompt a higher order prompt. We pass in a lower order prompt. We then have our primary agent kick off a slew of respective models running against the nano agent MCP server with a few very (t: 290) specific tools. You can think of this server as like a micro NICLI, a micro codec CLI, a micro Claude code server that our agents can run against agent (t: 300) complete in a fair playing field. You can see our agents are starting to respond here. And then they (t: 310) return the results. And of course, they report back to the primary agent and then our primary agent reports back to us. So this is the multi-model evaluation workflow, higher order prompt, (t: 320) lower order prompt. We have models that call our nano agent MCP server. And the whole point here is to create a true, even platform. And then we have a multi-model evaluation system that we can run over to default. Making sure that we've just executed this data allocation framework that that (t: 330) the main set of parameters that we have, all were pipe (t: 340) APIs. fullest Enemy. And then we have a query minimizer, which is in kupu and in that query (t: 350) morphers, you can see that I'm historically the most potent more powerful model, so that the model is able to Rosie scale the system up a lot. We'll hang out there at the Gottlieb time, however. And I'll send you, and you'll be able to see this as well. But let's I'll make sure that this is a live view. So you can see all of the data share is within the control that K Gmail and I drop in as well. It grows out at the email address of an endpoint that we set up. But at this point, I don't have any classes. So all the data set up to the end that I show you here and it looks okay it's all done. these brand new GPT open source models, OpenAI, absolutely cooked on these models. Again, (t: 360) these are running right on my machine right now. Let's open up the Scobase and understand this (t: 370) setup. As usual, we have our primary agentic directories. We have our plans. We have our (t: 380) application-specific nano agent here. We have our app docs, AI docs, and most importantly, our commands and our agents. If we open up commands, you can see we have this directory (t: 390) perf. Inside of perf, you can see we have a hop, a higher order prompt, and we have lops, lower order prompts. This is a powerful prompt orchestration, a prompt engineering, (t: 400) or context engineering, whatever you want to call it. I don't care. This is a powerful prompt orchestration technique you can use to reuse top-level prompts and pass in prompts as a lower (t: 410) level. We've covered this on the show. We've covered this on the show. We've covered this on the channel. Subscribe, like, do all that good stuff so you don't miss out on these advanced (t: 420) prompt engineering techniques. You can see here we have a simple grading system, S through F, where S is the best and F is the worst. We have a classic prompt format. We can collapse everything (t: 430) to quickly understand it, use the nano agent MCP server, execute, and then report the results in the response format. You can see the response format is a simple grading scheme. We're using (t: 440) CLAW code OPUS4. We're using CLAW code OPUS4.1 as an LLM, as a judge, to manage all this for us. And then inside the (t: 450) evaluation details, we're just passing in the lops, the lower order prompts, right? The prompts that contain the detail that we want to swap in and out as we evaluate different agentic behavior, (t: 460) all right? If we open up the terminal again, we can see how we're doing here. Looks like we are waiting on GPT-OSS 20 billion, and everyone else has completed. Not a ton of surprises there, (t: 470) right? On-device does take some time to run. There we go. We just got that complete. And now CLAW code is going to formulate these results into a concrete response for us. It's (t: 480) going to do the evaluation. So you can see those tokens streaming in there. Hopefully I still have enough OPUS for a great evaluation here. Let's see how that goes. But so we have a higher order (t: 490) prompt here. And then, you know, here's our eval one that we ran. This is our dummy test. So you can see the structure here, right? We're firing off CLAW code sub-agents, and we're having our (t: 500) sub-agents then fire off our nano agent server. So if we open up GPT-5, we're going to see that we're firing off our nano agent server. So if we open up GPT-5, we're going to see that we're firing off GPT-5, we're firing off GPT-5, we're firing off GPT-5, we're firing off GPT-5. So we have nano, right? So this is a CLAW code sub-agent. And all it does is it takes whatever prompt was (t: 510) passed in. It has access to a single tool, right? Our MCP nano prompt nano agent tool. And then we (t: 520) just pass in whatever our parent gives us, right? Whatever the primary agent gives us. So simple enough. You can imagine that we just continue doing this along every other model we want to (t: 530) run. So you can see here, here's GPT-5 mini. Right? But we can easily just swap this out, right? You can see here, there's nano, (t: 540) here's mini, here's five. And then we repeat the same thing for the cracked OpenAI local models, which are absolutely mind-blowing. We have 20 billion and 120 billion running right on my M4 (t: 550) Max MacBook Pro. This is a 128 gigabyte unified memory machine. This thing is absolutely cracked. (t: 560) These models run on the device, and they are doing agent decoding work. As you'll see in these results. So we can hop back to the results. Pretty excited to share this with you here. But you can see how these prompts are set up, right? These are our agents and our lower order prompts just detail (t: 570) the exact benchmark that we want run, right? So on our dummy test, the prompt is what's the capital (t: 580) United States respond in all your JSON format structure so we can get all the auxiliary metadata coming out of the nano agent MCP server. All right, that's how this is set up. We have another (t: 590) interesting result here. Okay, you know, we have the raw outputs. Here's all of our agents that executed. Right we had nine nano agents firing off and then we have the respective responses. You can see here (t: 600) we're looking for first ten lines and last ten lines and as specific prompt format, we can take a look at the result and we just quickly show you exactly what that prompt looked like. (t: 610) So this is the lower order prompt basic read. So if we look at this, we have instructions and then we have variables and the prompt. Here's the most (t: 620) important. So we're saying read the readme file provide exactly the first ten lines and the last ten lines in the favor Vitamin E 21 as the neutral graphic in this solution. That's what you're saying. Just look at the page given for here description. the file. So this is an agentic task. This is a little more advanced. We're just stepping up the (t: 630) difficulty scale just a little bit from just asking a simple question. We just want it in this exact response format, right? So we're testing for instruction following. We're testing for tool use, (t: 640) right? In a second here, I'll show you the exact tools that our nano agent MCP server can call. Then we fire off all of our agents and we have an expected output, right? So we're just sticking to (t: 650) great prompt patterns. We're writing extraordinarily clearly to our agent, both our primary agent and our sub agents. And we're, you know, being really clear about the flow of communication between our (t: 660) agents, right? We need to make sure that we're orchestrating the communication extraordinarily well. So that's what we're doing here. But the key here is, right, here's the agentic prompt (t: 670) that's running. Read the readme, give me the first 10 lines and the last 10 lines, okay? So this is what we're evaluating our models against. So now we can say, (t: 680) you know, for this fundamental agentic coding task, how did our models perform? Okay. And we are evaluating on performance. So did it do the job speed and cost? Okay. And so, you know, obviously (t: 690) if the model can't do the job, it doesn't matter if it's fast or how much it costs or how cheap it is, right? All right. So you can see kind of some rough grades here, right? If you look at the (t: 700) overall breakdown of this task, we have some rough grades, okay? It's not all roses when you really (t: 710) flatten the table. So we're going to go ahead and do a little bit of a test here. So we're going to go ahead and do a little bit of a test here. So we're going to go ahead and do a little bit of a test here. So we're going to the playing field, right? Take a look at Opus, for instance, right? We all know that Opus costs, but we don't really realize how much this model costs. It's extraordinarily expensive, okay? And you can see here for (t: 720) some reason, GPT-5 had to churn and churn and churn its output tokens to figure out how to get this (t: 730) response properly, okay? So terrible cost there. I'm actually surprised this chewed up that many tokens. I've seen this run much better, but I have seen some weirdness with GPT-5. So I'm going to go ahead and do a little bit of a test here. (t: 740) GPT-5 through the API, it's taking a crap ton of time. Maybe it's just me. Looks like that model is getting slammed. But then we can see something else really interesting here, right? There are some good, fast options. (t: 750) And, you know, once again, we can see the absolutely cracked benchmark ghosting model, Cloud4 Sonnet, you know, performing really well. Let's look at that Cloud4 Sonnet grade here. (t: 760) I've actually got a D. Wow, why did it get a D? Performance does not look good, right? So if we look for that Cloud4 response, yeah, so you can see what's happening here, right? It's not giving (t: 770) us that exact response format. Look, it's got this preamble, right? Now I'll extract the first blah, blah, blah, blah, blah, right? That's not what we asked for. So we have to dock points. (t: 780) On the other hand, though, we have some really great nano and mini GPT-5 agents performing really well. Okay, so why are these performing well when we combine an overall grade, right? (t: 790) It's because, once again, we're not just looking at performance. GPT-5 nano is giving us that exact response we were looking for, the first 10 lines and the last 10. And then we have, you know, (t: 800) we have last 10 right here. So very clear, very clean. And we're going to, of course, open up the readme and see that exactly, right? So here's the readme. Here's the first 10, right? And you can (t: 810) see the last thing there is clone the repository on that first 10 lines. So that looks great. And then the last 10 right at the bottom here, we have license MIT. Scroll to the bottom. Look at (t: 820) the 600 lines. The bottom is, of course, license MIT. So that's great. The results are not exactly what you would expect because we're accounting for speed, cost, and performance. (t: 830) So you can see here, GPT-5 did win on performance. But when you put it all together, right, when you put it together with the speed and cost of GPT-5, the grade gets dragged down. So very (t: 840) interesting. Let me show you another interesting example where we can push these models to do more. Hop a file operations test. So not only are our models going to read, they're going to write (t: 850) files here. So let's fire this off and let's understand what this prompt does. So file operations. This prompt is a little more involved. Here's the prompt that we're (t: 860) passing through the file. And then we're going to go ahead and run the test. So we're going to do this. I'm just going to take some valid details of every file. I'm actually passing in to every nano agent. Complete the following tasks. Read dot Cloud Settings dot JSON. Extract all the unique (t: 870) hook names and then create a JSON file with this structure. It needs to be precise about the JSON structure. Create another file with the content model name was here. Successfully completed (t: 880) operations test. And then you list the current directory to show the files created. This is a really interesting one, right? Again, we're just slowly pushing up the difficulty level for (t: 890) fair playing field. Okay. I think this is really, really important for truly understanding agentic capability. But if we scroll down to the bottom here, you can see our parallel sub-agents (t: 900) are getting to work here. There's a, there's a cloud version. There's a haiku version. There's GPT-5 mini. There's our OSS, right? Open source 20 billion running right on my device, right? (t: 910) We can click into this, check this out. It has read the file and it outputted this like check. This is so incredible. I got to say out of all the models that were released, you know, (t: 920) Opus 4.1, GPT-5, so far I'm most impressed with the open source models from OpenAI. This is (t: 930) viable agentic coding happening on device right now. Okay. So this is very, very, very crazy. Okay. Very interesting. And let's close this, open up the terminal and see our agents working. (t: 940) You can see most of them have already finished. Our OSS 20 billion is still, uh, formulating its output response there, but you know, you can see just one tool use in all (t: 950) of these. There's our OSS responding. Sub-agent complete. Okay, fantastic. So we have all of our sub-agents complete. Now our LLM as a judge, cloud code on Opus 4.1 is going to put all the results (t: 960) together. And based on the higher order prompt, it's going to put all the results together in a (t: 970) concrete response format. It's going to evaluate them, right? You can see this exact rubric, right? You can see our performance and our agent responses. So this is, all happening thanks to our top level cloud code, right? The agent architecture, (t: 980) plus the ability to call the right tool, plus a powerful cracked model. All set and ready for the next step, Dan. All right. So let's see what we have here. (t: 990) Surprising results, right? Take a look at the results. Not what you would expect, right? These five nanos and five minis seem to be very good, very fast, accurate instruction following agents. (t: 1000) We even got OSS 20 billion operating very, very quickly. Okay. And so, you know, it's so interesting to see these results, right? Let's go ahead and take a look at the actual (t: 1010) prompt we're looking for, and then we can, you know, double check some of this work, right? Let's look at this prompt in detail. You can see all these files, right? These are agentic tasks. (t: 1020) Calling tools, right? Calling a list of tools to accomplish the result. Let's look at the file operations. Okay. So read this file. We created summary model name dot JSON, (t: 1030) and then we created a model was here, and then we listed all the results inside of the agent. Okay. So let's take one agent, right? Let's take our top and our worst performer, right? (t: 1040) So GPT-5 nano, how did this perform, right? So let's look for this. We should have this here now. So we have the model name, GPT-5 nano. (t: 1050) Here are all the cloud code hook names, pre, post, notification, stop, sub, pre, blah, blah, blah, right? So perfect format there. What else did we ask for? Model signature name. (t: 1060) Let's open this up. And again, let's look for GPT-nano. And you can see GPT-5 nano was here, successfully completed file operations, okay? Exactly what we were looking for. (t: 1070) And then list directory. So very precise, right? You know, it read a file, it outputted a JSON structure, and then it wrote two files, right? It wrote these two files. What happened to our best model, right? (t: 1080) Which is, by the way, costing me quite a bit, right? Every time I run this. What happened here? Let's take a look, right? So let's start with summary opus. (t: 1090) You can see here, results look great, right? Let's look for signature opus. And once again, it accomplished the task perfectly, okay? So if we scroll here, right, our opus4.ns, right? (t: 1100) So it performed the task perfectly. But of course, we can see speed and cost brought the grade down quite a bit. And I was actually supposed to look for it. (t: 1110) Let's actually look for opus4.1. So what happened with opus4.1? 4.1, opus1 does not have an output file. Okay, so that does it. (t: 1120) For some reason, opus4.1 just does not have an output file. Maybe this was my fault. So maybe I... Let me look for 4.1 agent. Wow, no, it just doesn't have an output file. (t: 1130) Interesting. So yeah, that explains why that does not work and why it got such a low grade. But you know, the great part about this architecture here is maybe this is a perfect thing to kind of showcase. (t: 1140) We can, right here in the terminal, fire up a Claude instance. These are all individual sub-agents, right? It's really important to atomize your units of compute so that you (t: 1150) can test them and scale them up. (t: 1153) So let's go ahead and run this. So I'm going to run this. I have a base level MCP server that's getting called out of a (t: 1160) Claude code sub-agent, right? I'm explicitly saying inside these prompts, use this MCP server. I'm passing the parameters. There is zero confusion about what's going on here, right? (t: 1170) So this agent will run this and then it's going to report the results as is, right? So we can go ahead and just run this against our 4.1. There it is. So there's our nano agent. (t: 1180) And now we pass in the prompt to execute. So, you know, again, I'm just going to be really blunt and pass in. The exact prompt we were looking for this prompt here. And while we're doing this right in the background, we can just keep churning away. (t: 1190) So let's go to the next task. So hop, and then we can pass in our next evaluation. So there's that passing that lower order prompt into the higher order prompt. (t: 1200) This is a great way to increase your velocity. Let's just copy this prompt as is, and let's prompt our agent. Okay. So all this is going to get passed directly in. (t: 1210) What you'll see here is a Claude code sub-agent spin up this exact you know, series of commands that we want our nano agent to run. And all we're doing here, you can see there's that agentic prompt. (t: 1220) There's our tool call nano agent, prompt, nano agent. And the signature for this is quite simple. We can just search for this death, nano agent, and in our nano agent Python file, you can see exactly what this looks like. (t: 1230) We have a rich description, read a rich function comment for any callers of this tool, right? Because this is a, um, MCP server tool that's getting called. (t: 1240) And. Sub-agent complete. And this just passes off the work to a concrete lower level agent. All set and ready for your next steps, Dan. (t: 1250) You can see here, we now have that output and we now have the correct responses. If we open this up, there we go. You can see Opus 4.1 performing the results as we wanted. (t: 1260) Maybe got overwritten. Who knows what happened? Sub-agent complete. But the important piece here is that you want that granularity of your agents. At any point in time now I can just, you know, spin up a on device. (t: 1270) Sub-agent complete. GPT OSS 20 billion parameter. And you know, just for fun, let's do that. Right? So if I hit up here and I switch out the agent, uh, let's do the one 20, right? (t: 1280) So we'll have the one 20 run this exact same prompt again. And you know, it did complete its work. So I'm just going to delete it here so we can rerun this. (t: 1290) Okay. I'm going to fire that off. It's going to have the nano agent perform these tasks, right? So this is ultra powerful. We're composing powerful units of compute, right? (t: 1300) We're using the great cloud code agent architecture. Right? Right? So we're going to use this as a browser with powerful capabilities to, to call tools in parallel, to call sub agents and call our specific sub agents, right? (t: 1310) We can open up our nano agent GPT OSS 120 billion. And, uh, you know, you can see exactly what's happening here, right? Very, very powerful. This is a gentic coding running on my device. (t: 1320) This is mind blowing. Okay. You know, the performance probably has some work to do, but if we attempt to, okay, yeah, but we got to fix the scrolling issues here, guys. (t: 1330) Um, we can't scroll at all. I know I'm not the only one that experiences issues like this, but agent complete anyway, uh, we'll let this complete GPT OSS models for these again, simple. (t: 1340) I realize these are simple agentic coding tasks, but that doesn't change the fact that we have proof here that these GPT OSS models running on your device can do easy to simple to maybe even some moderate difficulty agentic work for you. (t: 1350) Okay. (t: 1360) And this is running on a blank. Instance, right? A blank equal playing field, a gentic system. And maybe that's a good place to go. Now, how am I actually running this? What does that MCP server look like? Exactly. (t: 1370) I only have a single tool call. So what's going on underneath the hood. You can hear my device. My Mac is working now to accomplish this task. And you know, by the way, I am running, you know, uh, w how many models are we running at the same time here? (t: 1380) Right. For a moment there, we were running like 10 models at the same time. Some hitting cloud, three of them on device. You can see the time consumption there on GPT OSS. (t: 1390) 20. 20 billion definitely consume some time. Beautiful. Look at this one 20 completed this task for us. We should have those files back now. There it is. One 20 ready for the next challenge. (t: 1400) Beautiful. Right. One 20 B completed that task here. It is right. All of the hooks once again, recreated there's the signature, right? Successfully completed file operations, right? (t: 1410) This is agentic coding. Our agents are operating our device. They're doing engineering work. And for the first time we have an on device model that is completing work. (t: 1420) On our behalf, this model, when this ran aside from the cloud code pieces of it, right? All that agentic work happened on my device, which is insanely incredible, right? (t: 1430) I'm running on a llama by the way. Um, let me break down exactly how this works, right? What is this nano agent? (t: 1440) So the nano agent is quite simple. If we open up main here and the nano agent, I have a simple MCP server. Okay. And it has a single tool execute an autonomous agent with natural. (t: 1450) Language task. So this is just like a clog codes task tool, right? When it spins up generic agents or specific sub agents, it's kind of the same deal, except here, we're just passing in a prompt. (t: 1460) And then our prompt gets interpreted by our nano agent and it runs a specific tool. So you can see here, we just have that one tool prompt, nano agent, and prompt nano agent takes an agentic prompt, a model and a model provider. (t: 1470) That's it. Okay. The agentic prompt is where all the magic happens inside the nano agent. We are actually running another agent, right? (t: 1480) If you. Remember the flow here, this is what's happening. Our user calling a prompt. Our primary agent is kicking off all of these different models and mobile providers against our nano agent MCP server. (t: 1490) And then, you know, if we add some space here, we would see our nano agent itself consists of a few tools. So let's go ahead and understand the tools are nano agent has. (t: 1500) Okay. So execute nano agent. Um, at some point here, we're going to see our, a tool selection right here. And what do we have here? Right? Get nano agent tools. Check this out. (t: 1510) This is all it has. Right? Very simple, very concise, right? A subset of, you know, modern agentic coding tools, tools. Um, but you can see, you know, just read, right? (t: 1520) List, get file, edit file, just as you would imagine. Right. And you know, you can just see all those tools here. So how does this all run? We are running on the open AI agent SDK. (t: 1530) So if I look at create agent here, we should get at some point agent, which is coming right out of the open AI agents SDK. We can look at the PI project, right? (t: 1540) And this is it, right? So this is our. Eight scaffolding. They have some great tooling here. I have to recommend if you want to experiment, build out with some basic agents to really understand how the most important architecture of the year and probably of the next few years is built and how to use it and how to build, you know, your own agents. (t: 1550) This is a great way to get started quickly. The open AI agents SDK is fantastic. (t: 1560) This allows us to create a very fair and balanced way to compare models, right? Because in our provider config, uh, we can specify, you know, is this an anthropic model? This is an old llama model, right? (t: 1570) And then we can just. Update the end point. So great way to test on a fair playing field. These all call their respective tools and get work done on our behalf. So that's basically the nano agent, right? (t: 1580) It's a generic prompt that calls one of several tools and these tools just do some operation, right? And the incredible part here is that, uh, I think the, the playing field for agents has really opened up this week, literally just this week. (t: 1590) We have a ton of new options for agentic coding and to get work done. (t: 1600) Both. On and off device to be ultra clear here. It isn't just the model that matters, right? There's a lot of work that goes into something like cloud code to make it the best, most efficient, most scalable, most consistent. (t: 1610) Consistency is really important. The most consistent agent. So I think that, you know, all of these models, right? You still want a top level state of the art agent. (t: 1620) It, that is clearly cloud code. I'm by no means substituting cloud code. Um, cause there is no substitute. What we are doing here is understanding. (t: 1630) A genetic model capability and taking some time to understand when and how we can start delegating some of our work from cloud code to alternatives that are more specialized for certain tasks, right? (t: 1640) Of course, there are three things that we focus on performance, speed and cost. You now have more options than ever to really decide what you want to trade off. (t: 1650) Okay. A lot of the times when you're really working and churning, the only thing that matters is performance. Okay. Now you can do some really powerful things with this. (t: 1660) New. Opus 4.1 GPT-5 is, I think, comparable. I'm running into issues with this model at scale. I'm more pressed and surprised with the mini nano and the OSS models than I am with GPT-5. (t: 1670) Not to say that GPT-5 isn't a great model. Um, I just need to spend some more time with it. I need to understand it at a deeper level. (t: 1680) You can see here in this agentic prompt, um, GPT-5 actually performed pretty well, right? So once again, very interestingly, we have nano and mini in first place when we combine. (t: 1690) A conglomerate response. We're looking for the best overall grade across performance, speed and cost. Right. But as you can see here, right, the true colors are starting to show a little bit, right? (t: 1700) Opus 4.1, Opus 4, Sonnet 4 S tier performance. And across the, the, the tests that I have done, I, you know, just got some time to set up this nano agent, you know, with the kind of few tests that I've had time to dig into to understand these models at a deeper fundamental unbiased level. (t: 1710) Pretty clear and pretty consistent that these are still your tests. (t: 1720) Still your top models, but we have new viable agentic coding models. Really happy to see this. Let's open up LOP4, lower order prompt eval 4. What is Cloud Code? (t: 1730) The primary agent asking our sub agent to accomplish with the nano agent MCP server, which itself is a little micro agent. Okay. It's asking this perform the following code engineering tasks. (t: 1740) Read the file, right? So we're reading our constants file, right? Right here. Okay. Bunch of constants for this code base. Analyze the code structure. (t: 1750) And then we're going to do this. Create a new Python file called analysis model name, replace model name with your actual model name that contains doc string analysis. And then a function get constants report. (t: 1760) And then a comment at the bottom of the file. So, so again, here, we're just like testing the agentic behavior of these models with these kind of intricate step-by-step again, agentic prompts, right? (t: 1770) We're out of the world of just chatting. We're out of the world of prompt chaining in specific workflows. We want arbitrary long sequences. (t: 1780) I'm sure. So we can go back to this. If we go back and see the parameters of engineering tasks completed by our models. Okay. A single prompt eval is not enough anymore. (t: 1790) It's about the string of tasks. Your system can accomplish. We've scaled far beyond the prompt, far beyond the prompt chain. We're now at this agent architecture that interact with this environment over and over and over. (t: 1800) Okay. And then we have a create another file here, enhance constants, add one. Useful constant. And you can see exactly what it's saying to add. (t: 1810) plus your addition, all right? Return a summary, right? So let's look at one of our models performance here. Let's look at a perfect example, right? So we have some S tiers here. Let's go ahead and take a look at Sonnet 4. (t: 1820) If we just look for all these files, so we should see analysis, Sonnet 4. There it is, so analysis model name. And check this out, it's a Python file with this analysis at the top. (t: 1830) And then we have constants report. So check out this great report, right? As a function in this file. And then at the bottom, we said analysis completed by, okay? (t: 1840) So it's following instructions extraordinarily well. And then we have one more file here, right? There it is, enhanced constants cloud four. And check this out. So it has the exact format as our constants go side by side. (t: 1850) If we save both of these to get the formatting the same, they have the exact format. Five mini, five mini. It's literally just replacing the constants, right? (t: 1860) So it wrote this new file. And if we scroll to the bottom here, we will have the enhanced signature as specified by the prompt, enhanced by XYZ, okay? We're not surprised at all, right? (t: 1870) We know that's on it. We know that the four series is absolutely cracked, but we can also see, you know, we have a bunch of other models here performing fairly well. A tier performance, OSS 20B. (t: 1880) Let's see how it did. I think you get the point. I'm just like really curious about this too. This is the first time I'm running these tests. We were looking at these live together here. So, you know, let me look at that first file analysis. (t: 1890) 120B, okay? So there's an analysis. There's a report and there it is. Analysis completed by OSS 120B, fantastic. And then we have that enhanced constants. (t: 1900) Okay, go ahead and open this up. Enhanced constants. You know, let's look at this side by side and you can see GPT OSS 120B. It looks like it's placed the file (t: 1910) as is side by side, like perfectly, right? How many lines do we have here? We have 114 in the original and OSS added that model signature as prescribed. (t: 1920) This looks incredible. It comps the task. The performance is there. Of course, on the OSS running on device, it did take quite a bit more time than these other models, (t: 1930) but again, look at the total cost. It's running on my device. This is just so incredible, right? I know I'm glazing hard on these models, but I think this is a big, big deal right now. (t: 1940) And I don't think many engineers understand this because, you know, what do most of us do? We just, you know, look at the blog posts. We just scroll, scroll, scroll. (t: 1950) We look at the benchmarks and then we complain about how it's only 2% or 3% higher and you don't really understand what it means, okay? Like most engineers don't actually understand (t: 1960) what, you know, what it means. Okay. You know, a 2% increase in agentic coding really looks and feels like, okay, over the previous generation, right? It's actually massive, okay? (t: 1970) And with every bump up on all these different benchmarks, you get something incredible that you can't see by just looking at the benchmark, right? You need to feel these tools. You need to run some type of, you know, (t: 1980) specialized workflow. You just spend time. You don't even have to run evals, right? If you really want to understand a model, definitely run evals, but you don't even need to go that far. You just need to spend time. (t: 1990) Running these models against real problems. Okay. Don't trust any individual benchmark. It's their job to tell you that they're doing a great job. (t: 2000) Okay. And I love every one of these companies as much as the next. I'm a big fan of every single big AI, you know, gen AI company. But at the end of the day, you need to crack open the hood of all these models (t: 2010) and say, where is the true value? Because I can guarantee you, right? There are some cheap, fast, high-performing models that you can take and use. (t: 2020) And if you can't even tap into now, you just need to actually know that you can, right? You need to know that it's even possible. This has been a breakthrough week. (t: 2030) It's very clear to me that there is more compute than ever to tap into. I'm able to understand and move and work through these innovations (t: 2040) because I understand the industry at a fundamental level, right? Everything we do is based off just one concept. The big three. Context, (t: 2050) model, and prompt. Everything is based off these. If you understand these, you can build evals, you can build benchmarks, you can build agents because they're all just scaffolded on top of these, right? (t: 2060) So if you're interested, you know, check out Principled AI Coding. This is my handcrafted course that I built to help you understand how to excel (t: 2070) and how to stay relevant with today's tools and tomorrows. Thousands of engineers have taken this. They've gotten serious value out of this. They've learned how to approach the industry. (t: 2080) There's going to be a big bonus for anyone that takes Principled AI Coding. You'll get a discount on the next upcoming agentic coding course. Okay. You know, this course has been out for, you know, about half a year now. (t: 2090) All the ideas are still relevant. You can use cloud code or whatever agentic coding tool you want inside this course if you're interested, but you want to be prepared for what's coming next. (t: 2100) Okay. And what's coming next is serious agentic coding. It's beyond that. It's agentic engineering, right? You see this here, right? (t: 2110) I have local models performing agentic coding tasks on our behalf, right? They're accomplishing things that previously only cloud code could do. (t: 2120) Okay. Things are changing. The landscape is shifting. What doesn't change is fundamental principles of AI coding. Okay. Because agentic coding is just a superset of AI coding. (t: 2130) Okay. I've said this a hundred times. I'm going to keep saying it so that everyone, right? If you made it to this video, if you made it to the end, you're one of the lucky few that has an opportunity to really, (t: 2140) to really excel and push yourself forward, right? Invest in yourself to understand this concept. We talk about key ideas like closing the loop, you know, programmatic AI and agentic coding. (t: 2150) And this is all preamble. It's all set up. It's all the system prompt for the phase two agentic coding course. I'm going to be releasing in September. I'm really excited. (t: 2160) I work on this thing 24 seven when I'm not, you know, eating, sleeping and resting. Okay. So we have more compute than ever. It's not about the prompt anymore. It's not about the individual. (t: 2170) Okay. Okay. It's not about the individual model anymore. It's about what the model can do in long chains of tool calls, right? The true value proposition of models is being exposed. (t: 2180) It's real work end to end. And the thing to keep an eye on is, do you know how to trade off performance costs and speed when the time is right? (t: 2190) Because I can guarantee you, you don't always need it. Opus four. Okay. You might be able to settle with GPT five, which is much cheaper by the way, than Opus four. (t: 2200) Right. Or you might be able to go further, right? You can just use five mini. Maybe you need to scale up to sound at four for that task. Fine. But maybe, you know, you can build your own specialized small agent, right? (t: 2210) You can build off from the nano agent code base that is going to be available to you. Link in the description. I'm going to clean this up and make sure it's available for you to you so that you can, you know, (t: 2220) understand agentic coding at a fundamental level, but you know, maybe you can go even further beyond and use a small agent. (t: 2230) On device model. These are only going to get better. So you want to have the infrastructure in place and the tooling in place to understand the capabilities. So when it's ready, you can hop on it. (t: 2240) I can tell you right now, I'm going to be investing more into the, you know, these models across the board. So I know what tasks can be accomplished by what model so I can make the right trade off. (t: 2250) Okay. Engineering is all about trade offs, performance, speed cost at different times of the day, based on the task you're working on. (t: 2260) Different things matter. Okay. So super long one. Thanks for sticking with me here. You know where to find me every single Monday. Stay focused and keep building.

