---
title: This will 10x your Claude Code setup (TaskMaster AI + Claude Code guide)
artist: "Morning Maker Show with <PERSON> and <PERSON> "
date: 2025-07-14
url: https://www.youtube.com/watch?v=FVedw8QGqq4
---

(t: 0) There's no better way to reduce AI coding errors than to set up a task management system in your project. I've used Taskmaster AI to ship multiple apps to production. This is the absolute best (t: 10) hack that eliminates most errors that AI does while coding. The best part of it is that it's going to help AI manage complexity as your app grows. It's so good that most people hide the (t: 20) secret very well. They never share their complete prompts and workflows or hide them behind a paywall. Today I will share everything with you, so please check out the description to get all the (t: 30) prompts and detailed instructions. Not only did I set up Taskmaster AI with Cloud Code, but I ended up coding an entire app and shipping it to production. Here's my reaction to the final result. (t: 40) Wow, what the f***? What? This is so cool. Look at the about page y'all. (t: 50) Oh my gosh. The copy is also completely cracked. Look at this. It's so cool. It's so cool. It's so awesome. Man, I just want to use this because it looks cool. So now I'm going to put (t: 60) something in the subject line. Gonna resize the text box here and just drop in a full-blown HTML email and then ask it to analyze and boom. Good stuff. All right. I'll show you more at the end (t: 70) of the video, but for now, let's get you set up. We are going to go through a few steps. Step one, generate a PRD file for Taskmaster to initialize. Step two, set up the Taskmaster (t: 80) MCP in Cloud Code. Step three, parse the PRD. And step four, finally, I'll show you the build (t: 90) loop with Taskmaster AI so you can build anything with it too. Feel free to follow along so we can do the setup together. This is how you set up Cloud Taskmaster AI in Cloud Code. The first thing you (t: 100) got to do is create the .taskmasterconfig.json file. And in this file, you should paste the following. I'm linking all the config in the description, so please check it out. (t: 110) Hold up. So how much will this cost? If you do this, you're going to get a $1.5 million in cash. This is the request made from Taskmaster AI. It will go through your Cloud Code subscription. (t: 120) Effectively, you get this for free. So don't worry about it. Once you put this config in, you open up the terminal. The first thing you need to do is install Taskmaster AI if you haven't done (t: 130) that already. The second thing you need to do is run this command, set the model to Sonnet or Opus, your choice, and pass in this dash dash cloud code option. Okay. Then it's going to say it's (t: 140) successfully set. So you can see that it's working. And then you can see that it's working. So you can see that it's working. And now we need to create the task. So for the purpose of this, I will create something that I called Cloud Code Spam Checker Application. And I'll be using two (t: 150) prompts to create this PRD file. You're going to have to spend a good 10, 15 minutes and think (t: 160) hard about what you're building and just write down some of the user interface tech and also just any feature that comes into your head. Write it down, try to describe it. So what I wrote is a (t: 170) little bit about the app, what it does, the target audience. And then I noted that there is already (t: 180) a code base. I said this time and again, when you start coding with AI, bootstrap the code base (t: 190) yourself, use your favorite CLI, your favorite boilerplate, or use Page AI, whatever you use, get it set up before you start doing any features because you're just wasting time and tokens on (t: 200) setting up frameworks. So that's the first thing you need to do. And then you can go ahead and do some other stuff. You can do some other things like setting up frameworks, installing libraries, stuff like that, and you're going to get a worse foundation anyway. All right. (t: 210) Then I mentioned some of the libraries that I know I want to use. This is probably different for you. So you might want to do a little bit of Googling on what open source libraries are available for (t: 220) the app that you're building. And then I went into how the user interface should work. I even went into details on how the layout should be, stuff like that. And then I made sure to highlight some (t: 230) of the advanced features that I had already set up. So I'm going to go ahead and do some of that. So the output for that is a structured markdown with some checklists of what needs to be done. (t: 240) I will take this entire thing and then move along to the actual PRD prompt. This is the document (t: 250) that Taskmaster AI will use to generate all of the dependencies and analyze the complexity, make subtasks and so on and so forth. Just a heads up, I'm running this on console.anthropic.com. (t: 260) So I'm going to go ahead and do some of the advanced features. So the output for that is a structured markdown with some checklists of what needs to be done. I'm running this on console.anthropic.com. But you can just run it on cloud.ai or even chat GPT. It doesn't really matter. (t: 270) I'm just more comfortable with the console here. After I run this, I get another markdown. And first we have a description, we have some personas (t: 280) and then we have some user interface components. And after this description, we get into the actual user stories. As you can see, all the user stories are broken down with a lot of requirements in here. (t: 290) So I'm going to just run this and see how it works. So I'm going to run this and see how it works. So I'm going to run this and see how it works. So I'm going to run this and see how it works. Now, if you don't want any of these, I suggest that you just review the PRD (t: 300) and make sure that you only include the stuff that you want, because this will include everything you need for a production application. But maybe what you're trying to build is a tool (t: 310) that you want to use yourself, or you might not care about performance that much. For example, I will create a new text file under scripts, PRD.txt, and then paste the entire output of the (t: 320) PRD prompt. And then I'm going to run this and see how it works. So I'm going to run this and see how it works. So I'm going to run this and see how it works. So I'm going to run this and see how it works. So to set up Taskmaster AI with cloud code, we first need to add the Taskmaster AI MCP. (t: 330) This will generate an MCP.json that looks like this. Alternatively, instead of the command, you could just create the MCP.json yourself and paste these contents into (t: 340) it. Now back to cloud. So cloud code is asking for permission to use this MCP server, and highlights that all tool calls require approval. And I'll just go ahead and hit that approve button. And (t: 350) now I'm going to prompt my data with there. And if you don't have a tool, you can start to update your data by your own. And if you don't have an EPUB server, you don't have an EPUB server. So prompt cloud code the following I've initialized a new project with Taskmaster AI I have a PRD (t: 360) at scriptsprd.txt can you parse it and set up the initial tasks then it's going to ask me for approval to use Taskmaster AI and I will say yes and in this specific case I will hate to always (t: 370) have to approve running these tasks reading tasks and so on so I'll just say yes and don't ask again for Taskmaster AI and remember Taskmaster AI is just a layer on top of AI agents so I will (t: 380) definitely recommend creating a cloud.md file and setting up rules for this project too and we're good to go is what I would say if it actually worked but currently Taskmaster AI seems to have (t: 390) an issue with the generated JSON it threw a bunch of errors to be more precise it turned out to be (t: 400) more tricky than I anticipated and I ended up spending an entire day finding a workaround for this which I'll share with you in a moment I don't like being tricked and this is exactly why I use (t: 410) Zavala to deploy my app this is one of the few trickery free app hosting companies that left out there they don't charge you for compute hours or nonsense like that they only charge you for what you use and you can deploy (t: 420) anything there they have storage managed databases and application hosting for everything you can come up with Python, PHP, Next.js, Nuxt, Laravel anything you'll throw at it they can host it and (t: 430) the best part get $50 of free credit by using the link in the description okay now back to our (t: 440) workaround we generate the tasks and cursor and then we move to cloud code and continue to work with the tasks later on so now we're just going to do a simple cursor prompt I've initialized a new project with (t: 450) Taskmaster AI I have a PRD at scripts slash prd.txt can you parse it and set up initial tasks now cursor is going to directly call the Taskmaster MCP hopefully generate the tasks and then we can (t: 460) move back to cloud code again I now realize that for the same reason you watch this video you might (t: 470) not know how to set up a taskmaster in a single task and that's because we don't have a taskmaster in a single task. So we're going to create a taskmaster in a single task and we're going to create a taskmaster in a single task. set up taskmaster and cursor either what I meant by that is that we also need to set up taskmaster as an MCP in cursor you can see how that looks on my screen here but if you want step by step guidance on how to do it please check out the video here where I go into a lot more detail. (t: 480) Now we're going to switch over to cloud code and just do the implementation there going forward so I'm going to hit command escape and then prompt cloud code can you analyze the complexity of our tasks to help me understand which ones need to be broken down for me. (t: 500) further Claude will then call the analyze project complexity from taskmaster II and I'm just going to prove and here we are with it created the complexity report and it recommends a number of tasks to be broken down into a few subtasks. (t: 510) In my experience creating the sub tasks greatly reduces the error rate of the application now usually I would tell it to do it step by step. (t: 520) But Claude code is supposed to have some agents so I'm just gonna tell it to use some agents and hopefully it will be able to break down these tasks in parallel and. (t: 530) And to be able to break down these tasks in parallel. tasks in parallel. So now I'm gonna prompt it, can you help me break down all of the high complexity tasks? Use sub-agents to perform the work in parallel. So there's a lot going on but I see it actually broken down into four (t: 540) separate sub-agents and each of them now calls the Taskmaster MCP to help break (t: 550) down the task. This is actually pretty cool as you can see some of the sub agents already done with the task so I think now is going to put together the result of this and hopefully give me a summary. Yeah there it is it's (t: 560) successfully broken down the task. So now we're basically in a position where we have the tasks, we have the complexity and then we have the dependencies (t: 570) between the tasks. I wanted to check if it lied but we can see task number three is actually containing a number of sub tasks in here so it didn't lie to me it (t: 580) actually did the work which is pretty cool. So now we're gonna get into the prompt loop and this is similar to using Taskmaster AI in any other AI agentic system. So now we're gonna get into the prompt loop and this is similar to using Taskmaster AI in any other AI agentic system. But I'm gonna start with the prompt loop and this is similar to using Taskmaster AI in any other AI agentic system. We're going to start by prompting it to show tasks and then I'm going to prompt (t: 590) it what's the task I should work on please consider dependencies and priorities. Alright so it came back with this next task implement core data types (t: 600) and schemas is looking pretty good it seems to have a fairly good understanding of what this task is and it's asking me if I'm ready to start. I'm definitely ready to start so what I'm going to prompt it now is implement task (t: 610) two and all of its sub tasks and boom done the task is complete. It's happy with the result now I'm just going to try to run this application. (t: 620) Seems like everything's fine and if I pop it open in the browser it all opens up correctly no problem at all. And that's it you can now keep going and (t: 630) pick up the next task. You have the recipe to build any app one task at a time. Remember to commit your changes after each task though because you don't have checkpoints like you did in cursor. So (t: 640) did I actually manage to build something with it? Why yes. Yes I did I managed to build an entire app from idea to production and here's a (t: 650) full demo of it. This is the brand new spamoose the terminal spam scanner 2.0 ah man does this put a smile on my face as you can see this is a complete blast (t: 660) from the past it has 90s vibes the copy is also really spot-on. Wow what the f**k. (t: 670) What this is so cool look at the about page y'all. Oh my gosh look at this it's so awesome man I just want to use this because it (t: 680) looks cool. So on the left side you can see we have our email input we can just (t: 690) press analyze email it's gonna call the API we're gonna get a bunch of stuff back we're gonna get a full report we're gonna get some details and we're gonna (t: 700) get tips on how to improve the email. Let me just put a more meaty email in here. and try to analyze it so I'm just gonna hit new email and you can see the layout (t: 710) is customizable so I could just drag this to the left side and resize it so I have more space for the analysis pretty cool don't hate it so now I'm going to (t: 720) put something in the subject line gonna resize the text box here and just drop in a full-blown HTML email and then ask it to analyze and boom super high risk (t: 730) of spam and look at how many improvement tips we got for this one. As you can see on the left this is actually an HTML email so it's actually (t: 740) pretty hard to judge by the email body what's inside and this is where the preview pane comes in super handy and look at this we can switch to tablet or (t: 750) mobile view to see roughly how the email will look when it's gonna hit people's inboxes we can even zoom in and out and have a slider to do that as well. Hey I went completely overboard on this preview thing but so if we look at the (t: 760) report here we see monetary amounts discounts long your all's excessive capitalization and suspicious formatting that were detected (t: 770) so a bunch of things I can improve to make this better the obvious improvement here is to also add some sort of AI detection on top of it but that's (t: 780) content for another video for now let me just show you the rest of the user interface so we have a history tab here let's get stored to local storage and it allows me to easily go back and forth between the emails that I've tested (t: 790) before and see the score that I previously got maybe see the tips that I got to the right side. we already saw analysis and details and tips we also have a couple toggles to the (t: 800) right side here one of them is to make the analysis and preview full screen and the other one is to reset the layout to the initial value and you can see here (t: 810) at any time we can resize the panel so we either have more space to write the email or more space to see the preview and analysis on the right depending on the task that we're currently performing. (t: 820) So here we are Cloud Code and I have built an entire application together starting from just one idea now every Every person in the world can go to spamoose.com, open up the app and enjoy it in its full glory. (t: 830) If you liked this video, remember to like, comment and subscribe. And for now, this is all I got. Thank you for watching.

