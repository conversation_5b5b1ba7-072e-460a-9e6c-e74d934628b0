---
title: "Claude Code + Obsidian: Auto-Organising Resources for A-Level Music Tech"
artist: <PERSON>
date: 2025-08-03
url: https://www.youtube.com/watch?v=ZCYzn8nO1rk
---

(t: 0) Over the last week I've been messing around with cloud code a lot and one of the things I'm getting into is how much it can change and organize how I am using my resources on my computer. (t: 10) So this is just the way that I'm working at the minute which is this. I've got cloud code set up on two different windows. (t: 20) This cloud code here is connected to my notes folder. So this is like a daily reflection. This is like things that I'm working on. It's certain things that I want cloud to know about me. It's certain things that I've done each day that I want to remember that if I go back into it's just like this diary. (t: 40) And this one is connected to my obsidian vault folder. So this is really the one where I'm using this for teaching that I'm using for learning. (t: 50) And this one is for files or for books or whatever it is. So this was amazing. I didn't think that it would do this but it was quite shocking. So this is what happened in my finder in my obsidian vault folder. (t: 60) I've got this book buddy and book buddy is an app that I've got on my phone which basically has all my books in it. So I asked the book buddy app to export out as a CSV all my books. As you can see what it's done is it's got all these. (t: 70) It's all the information that I've got in these books. So I've got all these. (t: 80) And what I wanted to do basically was just to see if cloud code would be able to read this CSV file and extract out all of the relevant music technology books for me and maybe go a bit further and maybe link these into the areas that I've got. (t: 90) So that's what the whole point of this video is today. So again, this is my cloud code connected to my notes or my daily reflections and that this is the actual. (t: 100) Cloud looking into this folder here. (t: 110) Okay, so in the first instance, I just spoke to Claude. So there was no prompting really. And basically what I said to it was I've got the CSV file and I want you to go through it and maybe extract out some parts for chapters or whatever it is. (t: 120) I just want to see if you could do this. So not surprisingly, Claude was able to do this. (t: 130) It was able to search through and pick apart all these individual books. As you can see here, it's got books relevant to music technology. It's got books relevant to music history, books relevant to education. (t: 140) So this is quite cool, I think. What Claude did, which was amazing, was that he created this step by step plan. (t: 150) In the first step, it's going to create this database of technology books. That's great. Second step is going to take those books and make more of a topic specific book. (t: 160) And then the next step was cross referencing the system, updating the MD file that I've got in there. And then add book chapters for reference. (t: 170) And then the last part of this was the student resource integration. So this is what it came out with. This is my Obsidian Vault that takes my diaries or my ideas or just things that I wanted to mess around with in Claude. (t: 180) And again, this is connected to this window here. So I can talk back and forth with it. I can have Claude create folders or files. (t: 190) So let's just take 1.12. This is one of the areas that I teach. And the topics are there. And the topics that I teach. So what this has done, essentially has done, is it's gone into the correct folder, which is the resources here. (t: 200) And what it's done under that resource is it has now created this new folder called reference books. (t: 210) And in the reference books, it's now created a 1.12 book recommendation. So what it's done is that, as you can see, it's tagged everything. (t: 220) It's got these core textbooks that are relevant to the actual topic. It's gone through each one of these. And it's mentioned about the author, key chapters. (t: 230) It's quite staggering what it's done. And then it's gone into genres. It's gone into direct mapping. (t: 240) So yeah, I mean, this is just incredible what it's done here is that it's actually told me that I can now use this for the Haas effect, slap back in here. (t: 250) And then, yeah, it links it into current material that I've done. Now, this is very important because this is just nuts. So this here, this task to this is an assignment that I gave to my class. (t: 260) So what Claude has done is that it's referenced a an assignment that I gave a task that I gave to my students. (t: 270) And it's recognized. I used this this kind of echo and it's the link to it into all of these, which is just astonishing. (t: 280) And again, as you can see, it's just linked everything into current materials that that I've done. So it's not just a (t: 310) I'm doing for certain tasks. I have gone back and check this. I just can't can't get over this. Key concepts. Yeah. (t: 320) So it's broken this down. Practical exercises. Oh, oh, my God. Look at this. Okay. So I didn't realize I was doing this. Okay. So past paper links. (t: 330) No, surely that hasn't done this. Okay. So past paper links here. So what I did is in. Let me find this in notes and assessment tools. (t: 340) I've got a past paper question. And I'm what I basically did often do another diary on this. But I basically just gave Claude past papers and said extract all all the questions from this paper are relevant to this topic. (t: 350) And it has done that. But what it's also done is it's it's referenced the book that I'll be using or that I should use for this paper. (t: 360) So let's just see if this works. I don't even know if this would work or not. (t: 370) So this is 2024. Question A. So this is the question. A five F. So here. Yeah, yeah, it's it's it's correct. It's a bit of formatting issue there. I'll just fix that. That is just insane. (t: 380) That is absolutely just nuts. So this is something that I had Claude do in another session, which is about differentiation. And I think what it's done, which again, I think is accurate for like foundation students is recommended. These two books here. And then for more advanced students, I recommend the book. So I recommend the book. This is it's a great book. It's a great book. I think it's a great book. (t: 390) I think it's a great book. I think it's a great book. I think it's a great book. I think it's a great book. Think what it's done. Which again, I think is accurate for like foundation students is recommended. (t: 400) These two books here. And then for more advanced students, it's it's an this which is accurate. This book in particular is quite difficult, and it would only be for you know, students (t: 410) of a or a star level. It's it's given me extension work. Yeah. Mark exams your practical assessments. (t: 420) I don't know how I feel about these quotes and I don't know where Claude got this from. I might. even ask it where did i actually get this from and then yeah this related files just absolutely (t: 430) insane so um i wanted to see about how it would do this this differently with another area because i thought well maybe it's just you know somewhat like guessing and maybe that all this stuff is the (t: 440) exact thing and it just copy and pasted it from um from one section into the next so this is section 1.12 here uh and now i'm going to go into another area which is 1.3 for synthesis so again um be (t: 450) aware that this is in the exact same area so technical resources and um technical resources (t: 460) so again what it's done is that it's recognized it's going to put it into the exact same area so the teaching resources goes on on here uh reference books goes in there and again i've (t: 470) got a lot of stuff in here that um that uh i've taken off the internet or um i've got from myself so here's the book recommendation and i just wanted to see if the books are (t: 480) different from um synthesis to it is to uh 1.12 so core textbooks let's just see core textbooks yeah (t: 490) so so it's different so this big studio one is different from this electronic one and it's the same thing here key chapters let me just see about that so electronic music here (t: 500) and this goes here so key chapters it's actually that is nuts it's actually went into it and then broke this down to where i can figure out where (t: 510) in the um in the book i want to it even references the student level here so basically what this has (t: 520) done is it's given me a different um set of books for this topic uh 1.3 than it has for 1.12 (t: 530) so this is my diary in here and this is my um my obsidian vault which has all the the information of the book and i'm going to go ahead and open it up and see if i can find it so i'm going to open it up and see if i can find it so i'm going to open it up and see if i can find it so i'm going to just fast forward through about uh what we just did so why don't we just do this i'm going to ask (t: 540) claude if it can recommend on the internet based on my books you know like 10 other books that i could recommend based around my csv file that you've analyzed for music technology books could (t: 550) you recommend 10 music technology books and put this into a table please all right let's see what (t: 560) it does so this is taking uh quite some time for it to do this search and to get these books so i'm go back into my my notes here and again this cloud notes is connected to this so (t: 570) I just wanted to give you an example about how I'm using this so you can see that I've got different sessions in here each one of these sessions basically is (t: 580) me going into it and just doing something that I was doing on cloud code or another AI so I think what I'm gonna do now is let me just give you an example of this I'm gonna ask Claude to put in another session and I'll just (t: 590) give it a really quick explanation about what we did okay so in my cloud code notes can you add another session in there and with this time basically (t: 600) what I've done is I've gone through a web search using cloud code and I asked it to create a list of books that I don't have in my CSV file and to search (t: 610) the internet for new books for me okay so now that I've given it this this prompt and I hit OK now watch this what it should do now (t: 620) within this yes as you can see it's created this now new session for me and it's basically taken that very very long prompt here me just talking out loud about X Y & Z and (t: 630) it's put into more of a of a condensed way that I can just really quickly read this so with that let me go back to the web search that it was doing so this was (t: 640) the the books that are essential and why that they're essential so there's a study guide yeah so it it's done it that's quite amazing okay I think that's (t: 650) all for today thank you

