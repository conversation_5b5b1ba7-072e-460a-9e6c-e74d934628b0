---
title: Build Sites That Sell 99% of Products with <PERSON>
artist: AI LABS
date: 2025-08-18
url: https://www.youtube.com/watch?v=VtmBevBcDzI
---

(t: 0) Before you even think about launching your product, there's one thing that will make or break your success. And most people completely mess this up. They rush to build their product first, then throw together some generic landing page at the last minute. (t: 10) But here's the reality. Your product gets judged by your landing page quality, and if that looks amateur, your product will too. Now with AI, everyone thinks making landing pages is easy. (t: 20) You just go to Cursor or Cloud, type, make me a landing page. You get something with gradients and cards. But it looks exactly like every other AI-generated site out there. (t: 30) Boring, generic, and definitely not premium. Well, today, I'm going to show you how to fix that. So, I was scrolling through X and stumbled upon this post by Legion's dev. (t: 40) They had shared a public template for designing landing pages, and I thought it looked incredibly professional and amazing. There were so many small details that made it appear completely human-designed rather than AI-generated. (t: 50) I went ahead and opened up the chat in V0, since that's where it had been designed. You can see the whole conversation right here. Upon opening it and testing it out, I noticed subtle details that made the website even more responsive. (t: 60) So what I did was read through his prompts carefully. I wanted to create a template so that anyone could easily achieve amazing designs like these, regardless of which AI agent they were using. (t: 70) First, I tried giving the same prompts he used to Cloud Code. After the first and second attempts, Cloud Code didn't generate anything close to what I wanted. (t: 80) For example, it needed to create this specific design component. But Cloud Code just produced some kind of basic dot structure instead. (t: 90) I even tried providing the description of what V0 had actually implemented, but it still didn't produce the desired result. It just added a gradient to the previous result, and it still didn't look that great. (t: 100) So here's what I did. I copied the final result from the site and broke it down completely. I took the end result, the actual code, along with some of the original prompts he used. (t: 110) Now I'm going to share the best tips and insights for creating amazing websites like these, using a detailed breakdown, of the website that was built on V0. Now a quick break to tell you about today's sponsor, ChatLLM. (t: 120) So many AI tools out there, but buying them all? Expensive and messy. Now you don't have to. ChatLLM Teams gives you every top AI model in one place. (t: 130) ChatGPT-03 Pro, GPT-4.1, CloudSonnet 4, Lama 3, Gemini 2.5 Pro, Abacus Smaug, DeepSeq, Grok 4, and RootLLM auto-picks the best one based on your prompt. (t: 140) Want visuals? It generates images using Flux, Ideagram, DALL-E, and videos using Kling, Runway, Hylo, and more. (t: 150) Instantly and effortlessly. Your content feels robotic? Humanize it and pass AI detectors with ease. Need reports or slides? It builds full docs and presentations with charts and deep research. (t: 160) Coding? Use the built-in CodeLLM editor or launch DeepAgent to build full apps and agents. All this for just $10 a month. Visit chatllm.abacus.ai or click the link in the description now. (t: 170) Okay. So, one thing you'll notice that makes this website look so good is the interactive background it has. (t: 180) I've seen AI generate backgrounds before, even gradients, but I haven't seen it make them this impressive. Looking into the code and his prompts, I found that he was using Paper Shaders, a library for creating these amazing-looking backgrounds for websites. (t: 190) At first, I tried asking Claude Code to use this library and generate good backgrounds, but it failed. For some reason, it just didn't understand what actually looked better, unlike this example. (t: 200) Now, I'm going to show you how to use the same prompt to create these amazing backgrounds for your own website. As you can see from his prompt. But when I gave the same prompt to Claude Code, it really messed things up. (t: 210) So here's what I did, and here's how you can generate these amazing backgrounds for your own website. First, you need to explicitly tell Claude Code that you're using Paper Shaders and specify the design clearly. (t: 220) The next step is to use this already prepared prompt. These details are actually what I extracted from his website. I went back to GitHub, used Git ingest, and it basically transformed the repo into AI readable text. (t: 230) Since it's not a big repo, I then pasted it into Claude Code and had a focused conversation with it. I gave it the exact design you see here, using this specific prompt, and it produced other amazing backgrounds with the same library. (t: 240) Don't worry about the prompts and resources. You'll find all of them in the resources section of this video. (t: 250) Essentially, in the context window of this chat, only the background details are included. That's more effective than just handing the entire codebase to Claude Code. (t: 260) This is why I prefer this separation approach. I even have a lot of code to write. I even had Claude Code implement a dropdown with different styles so I could show you the variety of backgrounds it came up with. For example, here's this water droplet moving across the screen. (t: 270) Then there are these small cell animations. They're moving really slowly, but they're still animating beautifully. There are different types of animations, and you can generate many variations with Claude Code. (t: 280) You can change the colors, ask it for different options, and experiment freely. The main thing I discovered was that after giving it this prompt, it unlocked some kind of improvement. (t: 290) Before this, most of the designs it generated had no animation whatsoever. But afterwards, the designs looked incredible. Just look at this background right here. (t: 300) It looks absolutely amazing. This is what actually makes your landing pages beautiful. Amazing backgrounds, along with the other elements we'll discuss next. (t: 310) Now, next up on the website, you'll see down here this small rotating circle, which displays V0 is amazing while it animates. Over here, you'll notice a small pop-up or merge animation as well. (t: 320) I also noticed that along the way, the author tried to implement even more animations. And I know that many of you, when implementing animations, just tell AI to go ahead and add them. (t: 330) But that's actually not the correct approach. Using specific libraries for animations is a much better practice, because the AI doesn't have to create everything from scratch. Instead, it leverages a library that's already built to handle animations professionally. (t: 340) One of the best libraries for this, something AI models are actually very skilled with, is Framer Motion. Basically, you just import it, (t: 350) and it offers pre-made, amazing-looking animations for all kinds of elements, including buttons, components, and more. So here's what you should do. Whenever you're implementing an animation, make sure to use this approach. (t: 360) For example, if you wanted to implement this rotating circle animation right here, you would ask the AI to create a rotating circle with text. (t: 370) But along with that request, you'd also provide this prompt, telling it to use Framer Motion for React, which is the React version of the Framer Motion library. When you do this, the results are significantly better. (t: 380) Even in my own testing, I've seen countless examples where simply using this library makes animations smoother and more stable. Because these animations are pre-configured within a library, rather than being custom code, (t: 390) there's a much lower chance of things breaking. For instance, if I change the background of this website, the moving element animations wouldn't randomly break, (t: 400) because they're built with Framer Motion's reliable foundation. So definitely try to use this library as often as you can, whenever you're implementing animations. So, on the topic of this particular video, (t: 410) I'd like to talk about how to use this particular animation. It's actually really amazing to see these little quality animations, because they make the whole website experience so much better. On that note, I'll leave a list of additional animations in the description. (t: 420) Basically, if you were to implement this effect, it wouldn't just be a simple mouse cursor or a small circle popping out from the side. (t: 430) What actually happened here was a gooey morphing effect. That means these are two separate elements that have been morphed together using this gooey animation technique. So here's how you do it. (t: 440) Have the AI create two separate elements. For example, tell it to make a login button and then a small arrow circle. After that, give it a prompt to apply a gooey morphing animation between them. (t: 450) And again, specify that it should use Framer Motion for this effect. This is how you'll achieve these small but incredibly impactful animations. Also, I went ahead and found some other examples as well and tested them thoroughly. (t: 460) You'll be able to access all of those in the resources section too. So next up, another small detail that really makes the website look impressive. (t: 470) Is the different font choices the author used. They didn't just stick to the normal default font that AI usually generates, which wouldn't have helped the overall look of the site. (t: 480) By implementing a different font, especially for a single word, it adds a tremendous amount of character to the landing page. The go-to place for getting fonts is Google Fonts. (t: 490) You can go there and implementing the fonts you find is really straightforward. For example, I saw this font that I liked and to implement it, I simply copied the code and told Claude Code to add it. (t: 500) You can see that I asked it to apply the font specifically to the word beautiful and I provided the link I copied. It went ahead and implemented it perfectly. Now, even though the font I tried isn't necessarily better than the one that was already there, (t: 510) it demonstrates how easily you can customize it. So definitely try out different fonts on your websites. Just like they did here with specific words, (t: 520) it makes your site look much more polished and gives it significantly more character. Now, when you actually look at the site, in my opinion, it looks absolutely amazing. (t: 530) Along with the gradient and the fonts, another crucial factor is the layout they chose. For example, when they implemented the wording, you can see that instead of putting it in the center, (t: 540) they purposefully placed it in the bottom left corner. That made it look significantly better. For this, you basically need to know some website layouts you can use to improve your designs. You don't have to reinvent the wheel. (t: 550) You can simply take inspiration from websites that are already out there. For that, I found a website gallery called Landbook, which I've been using recently for a few days. It's a great place to find inspiration for design. (t: 560) You can find different layouts, structures, and mapping styles for websites, and they provide excellent ideas. Just look at this one right here. In my opinion, it looks incredible, and it's essentially just a layout. (t: 570) For example, if you were making a landing page for an agricultural product, this could easily work. All you'd need to do is give a screenshot of this to Claude Code (t: 580) and tell it to use agricultural images along with your logo. But if I had just gone to Claude Code saying, Hey, I want to make a landing page, even if I added fonts and backgrounds, without proper structure or a unique layout, (t: 590) the results wouldn't look impressive. Just look at this site right here. It has so many of the same qualities as the one the original author made. Short chunks of text with different fonts, animated backgrounds, and a distinctive layout. (t: 600) All of that combined makes your landing pages look truly professional. And honestly, all you have to do is take a screenshot and give it to Claude Code. (t: 610) That's it. They even provide a color palette, which is also fantastic, so make sure to use that as well. While I was looking at the chat from the author, I noticed how he implemented the V0 brand logo. (t: 620) He didn't just ask to implement the logo or provide an image. Instead, he supplied the SVG code for it. This is much better compared to the alternatives. (t: 630) The reason is that SVGs are represented in code since these logos are built from different shapes. This makes it much easier for the model to understand them, compared to providing images and asking it to add those images. (t: 640) This also gives you extra flexibility. Adding icons and images as SVGs gives you access to additional features. You can animate these SVG icons using Framer Motion and create really amazing animated drawn icons. (t: 650) SVGs are easy to find and there are plenty of resources available. I'll list several of these resources. (t: 660) Take a look at this site where you can find a lot of SVG logos for different brands. If I want the V0 logo, I search for it, and there it is. Clicking on it copies the SVG to my clipboard. (t: 670) When I go back to my terminal and paste it, I get the SVG code. From there, I can animate it or use it directly in my app. Besides icons, there are also many sites with SVG illustrations that look really amazing. (t: 680) They're important for certain sites, and while they weren't necessary in our case, they're still an amazing resource when building professional landing pages. Simply download these SVGs and provide them to Cloud Code, (t: 690) and implementation becomes easy. Since SVGs are made of shapes, they're easily edited. Colors can be changed to match your brand, making them a really great resource. (t: 700) That brings us to the end of this video. If you'd like to support the channel and help us keep making videos like this, you can do so by using the Super Thanks button below. As always, thank you for watching, and I'll see you in the next one.

