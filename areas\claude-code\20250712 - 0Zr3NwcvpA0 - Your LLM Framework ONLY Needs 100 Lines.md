---
title: Your LLM Framework ONLY Needs 100 Lines
artist: <PERSON>
date: 2025-07-12
url: https://www.youtube.com/watch?v=0Zr3NwcvpA0
---

(t: 0) you have been lied to. You've been sold a story that to build with AI, you need to read documentation that looks like a textbook. You need to understand these spiderweb diagrams (t: 10) that promise simplicity but deliver chaos. And you absolutely need to fight your way through dependency hell. (t: 20) It's enough to make you ask the question, does it really need to be this complicated? The answer is no. (t: 30) And frankly, you've been lied to. You've been sold a story that building powerful AI requires these massive, convoluted systems. (t: 40) So, what happens if we just delete everything non-essential? What if we get back to what is truly minimal (t: 50) and truly viable? Here's the truth. Your LLM framework, only needs 100 lines. (t: 60) The answer is Pocket Flow. The minimalist framework built on just those 100 lines. And I'm not kidding. Check this out. (t: 70) This is the code. The entire framework. You've got the node, the atom of our system, batch processing for when you're dealing with mountains of data, (t: 80) the flow, which is the master recipe, and async parallel execution for when you need ludicrous speed. It's all right there. No smoke and mirrors. (t: 90) And it turns out, thousands of developers are tired of the bloat too. As this chart of its GitHub star history shows. This minimalist approach is a rapidly growing open source movement. (t: 100) The reason for this growth is a core philosophy. This comparison table says it all. Zero dependencies and a hundred lines of code. (t: 110) Versus hundreds of megabytes and massive tangled networks. This is the reason for this growth. And it's not just about building massive tangled code bases. It's about moving past the unnecessary complexity (t: 120) to master the fundamentals. (t: 123) And here's the secret the big frameworks don't want you to know. At their core, all LLM applications are just simple graphs. (t: 130) With the core abstractions shown in this first diagram. You can build every powerful pattern you've heard about, shown in this second diagram. (t: 141) We're talking sophisticated agents, high performance RAG, and ridiculously fast parallel workflows. (t: 150) So get ready. In this video, we are going to tear down the black boxes. This is not just another tutorial. This is a master class in first principles. (t: 160) You will learn how agents actually work. You will learn how RAG is really built. You will learn from the ground up. (t: 170) And the best part? If you know a little bit of Python, you're ready. Because we're building our understanding of everything. On top of just these 100 lines. (t: 182) Alright, we've seen the 100 lines. It looks deceptively simple. But how does it actually work? (t: 190) Let's crack it open. The entire framework boils down to just three fundamental ideas. The node, the shared store, and the flow. (t: 200) Master these three, and you've basically unlocked the whole system. Let's start with the absolute heart of it all. The node. (t: 210) A node is the smallest, most basic unit of work you can have. Think of it as a single hyper-focused worker at a specific station on an assembly line. (t: 220) This worker isn't a multitasker. They do one thing, and they do it well. And they follow a strict three-step routine for every task they're given. (t: 230) Prep, exec, and post. First, there's prep, which is short for pre-process. This is the get your ingredients phase. (t: 240) The worker looks at this big shared space, which we'll talk about next, and picks out only the specific data it needs for its job. (t: 250) It gathers the raw materials and gets them ready. Next up, exec, for execute. This is the do the cooking phase. And this is super important. (t: 260) Notice that the exec function only receives the ingredients that are needed. The prep handed it. It cannot see the entire shared space. It's working in a clean, isolated bubble. (t: 270) This is a deliberate design choice. It forces a clean separation between your data fetching logic and your core computation logic, which makes your code incredibly clean and easy to test. (t: 280) This is where you do your math, call your API, or prompt your LLM. Finally, there's post. (t: 290) This is the clean up and serve the dish phase. After exec is done, the post function takes that result and writes it back to the shared space for the next worker down the line. (t: 300) But it does one more crucial thing. It returns a simple string called an action. This is like a little status update. A note that says, I'm done, everything was normal, (t: 310) or something weird happened, take the error path. This little action string is what will guide our entire workflow later. Let's visualize this whole process. (t: 320) The node's prep method takes data from the shared store. The isolated exec step does its work. (t: 330) Then the post step writes data back to the shared store and outputs an action to tell the system where to go next. (t: 340) Okay, prep, exec, post. Simple enough. But what happens when things go wrong? What if your worker drops the eggs? (t: 350) What if the API you're calling is down? (t: 355) This is where Pocket Flow gives our basic worker some superpowers. (t: 360) We used the node class, which has built-in retry logic. When you create a node, you can simply tell it, hey, if you fail, I want you to try again. (t: 370) You can set max retries to define how many times it should try and wait to tell it how many seconds to pause between attempts. (t: 380) Behind the scenes, Pocket Flow wraps your exec code in a safety harness. It's basically a for loop that runs up to max retries times. Inside the loop, it tries to run your code. (t: 390) If an error happens, it doesn't crash. It just says, oops, let's wait a few seconds and try that again. But what if it fails every single time? (t: 400) Even after all the retries, it just can't succeed. That's where exec fallback comes in. It's your plan B. (t: 410) Instead of letting the whole assembly line grind to a halt with a nasty error, the node will call this special function, giving you a chance to handle the failure gracefully. You can log the error, return a default value, (t: 420) or just provide a user-friendly message. Let's make this crystal clear with our summarize file example. First, the prep step. (t: 430) Look how simple it is. Its only job is to go to the shared dictionary and get the value associated with the key file content. (t: 440) That's it. It hands this content off. Next, the exec step. It takes that file content and only that content and sends it to an LLM to be summarized. (t: 452) This is the part that might fail if the LLM's API is down or overloaded. (t: 460) If that LLM call fails after all our retries, the exec fallback method kicks in. It prints a helpful error message to the console for us developers, and then returns a friendly, safe message like, (t: 470) Sorry, summarizing the file failed. Reassuringly, our application doesn't crash. And finally, if everything works perfectly, (t: 480) the post step takes the successful summary it received from exec and neatly places it back into our shared dictionary under the key summary for other nodes to use. (t: 490) And when we create this node, we can easily tell it to be resilient. We just pass in the arguments max-resist, and then wait for the next retries to be done. (t: 500) Now, our summarizer will try up to three times, waiting five seconds between each attempt before giving up. (t: 510) So that's the node. It's not just a worker, it's a reliable worker. It preps its data, executes its task in isolation with built-in retries, and then posts its results. (t: 520) It's the simple, powerful building block for everything we're about to create. But how do these different workers, these different nodes, and these different nodes work together? Well, the two nodes communicate. (t: 530) They don't talk to each other directly. Instead, they all use that shared assembly line I mentioned. This is our second core idea. The shared store. (t: 540) And here's the best part. The shared store is not some complicated database or message queue. It's just a Python dictionary. Seriously, that's it. It's a single global dictionary that every node in our system can access. (t: 550) Think of it like a central whiteboard, or a kitchen countertop. One node can write a result onto the whiteboard, like, result, 42. (t: 560) Then, the next node in the line can walk up to that same whiteboard, read the number 42, and use it for its own task. The prep function reads from this dictionary. (t: 570) The post function writes back to this dictionary. It's the single source of truth for our entire workflow. And its beautiful simplicity is what makes the whole system so easy to debug. (t: 580) You can literally just print the dictionary at any time to see the exact state of your application. Okay, so we have our workers, nodes, and our shared countertop, the shared store. (t: 590) The final piece of the puzzle is the flow. If the nodes are the workers, the flow is the master recipe or the factory foreman. (t: 600) It tells each worker what to do and where to go next. When we connect nodes, we're defining the flow of work. The simplest connection is with a double arrow. Node A, Node B. (t: 610) This just means, after Node A is done, go to Node B. This is the default path. But what if we need to make a decision? What if the process needs to branch? (t: 620) This is where those action strings from the post method come into play. We can create name transitions. For example, reviewNodeApproved . (t: 630) This means, if the reviewNode's post method returns a string approved, then the next step is the payment node. Under the hood, it's beautifully simple. Each node has its own tiny address book, called self.successors. (t: 640) When you write Node A, Node B, you're just adding an entry to Node A's address book that says, for the default action, the address is Node B. (t: 650) The flow orchestrator simply runs a node, gets the action string back from its post method, and looks up that action in the current node's address book (t: 660) to find out where to go next. And now for a really cool meta idea. Here's the secret. A flow is also a node. (t: 670) Think about that. A whole sequence of nodes, a whole sub-assembly line, can be bundled up and treated as a single workstation in a larger assembly line. This means you can connect flows to other nodes, (t: 680) or even connect flows to other flows. This is called nesting, and it's how you build incredibly complex and sophisticated applications from small, reusable parts, (t: 690) without ever losing that core simplicity. Alright, we've covered the core mechanics, nodes, the shared store, and the flow. (t: 700) But let's address the elephant in the room. This is supposed to be an LLM framework. So where's the LLM? If you look at the 100 lines of Pocket Flow, (t: 710) you won't find a single mention of OpenAI, Anthropic, or Google. And that is a very deliberate design choice. (t: 720) The core principle here is no vendor lock-in. Many frameworks come with hundreds of built-in wrappers for every API under the sun. (t: 730) At first, this seems helpful. But APIs change, new models are released, and suddenly the framework's wrapper is out of date, (t: 740) or it doesn't support the one specific parameter you need. You end up fighting the framework instead of building your application. (t: 750) So how does an LLM framework not include an LLM? Turns out, you can just write the function yourself. (t: 760) Here it is. This is a perfectly good utility function to call OpenAI's API. It's about 10 lines of code. It takes a list of messages in, (t: 770) and it gets a string out. It's truly minimal. If you want to switch to a different model, you just write a different 10-line function. (t: 780) You are always in complete control. With this simple call underscore LLM function in our toolbox, we can now build one of the most common LLM applications, (t: 790) a chatbot. And you're about to see just how simple it really is with the Pocket Flow architecture. (t: 800) A chatbot is not some complex multi-part system. It's just one node, talking to itself over and over again. Here's the entire logic visualized. (t: 810) It's a single chat node that, after it runs, has an action that just points right back to itself, creating an infinite loop. (t: 820) The conversation only happens when the user decides to exit. (t: 824) Let's build this chat node. First, the prep step. (t: 830) This is where we handle the user side of the conversation. The first time the node runs, it checks if a message's history exists in our shared store. (t: 840) If not, it creates an empty list and prints a welcome message. Then it prompts the user for input. It adds the user's message to the history and returns the entire list of messages, (t: 850) ready for use. This is the first step for the LLM. Next, the exec step. This is the simplest part. It just takes the list of messages prepared in the previous step (t: 860) and passes it directly to our call LLM function. The LLM's response is then returned. Finally, the post step. It prints the assistant's message to the screen for the user to see. (t: 870) Then, crucially, it appends the assistant's response back into our message's history in the shared store. This ensures that the next time the loop runs, (t: 880) the LLM will have the full context of the conversation so far. And to keep the conversation going, the post method returns the action string continue. (t: 890) Now, we just need to create the flow. We instantiate our chat node, and then, we define its transition. We tell it that for the action continue, (t: 900) it should go to... itself. chat node minus continue, chat undersnode. This creates the self loop. (t: 910) And that's it. We create an empty shared dictionary and run the flow. (t: 916) So, what does it actually look like (t: 920) when our chat node takes a turn in a conversation? Let's peek under the hood and follow the journey of a single message. Right now, our chatbot is waiting, and as you can see, our shared store, (t: 930) the chatbot's memory, is completely empty. The prep method springs into action. Its job is simple. Listen. (t: 940) It waits for your input. Let's say you type, Hello, who are you? Prep grabs that text, wraps it up neatly in a little dictionary to label it as the user's message, (t: 950) and then, this is the key, it adds it to the messages list inside our shared store. Just like that, our conversation's memory is no longer empty, (t: 960) and now contains our opening line. Now, it's time for the exec step, the brain of the operation. It takes that messages list that prep prepared (t: 970) and sends it off to the LLM. The LLM does its thing and thinks up a response. Maybe something like, (t: 980) I'm a helpful AI assistant. Now, remember the rule about exec? It works in a bubble. It doesn't touch the shared store directly. (t: 990) Its only job is to do the thinking and hand back the raw result. As you can see, the shared store remains completely unchanged during this step. (t: 1000) Finally, the POST method gets to work. It's the town crier and the librarian all in one. First, it takes the assistant's response that exec just generated (t: 1010) and prints it to the screen so we can see it. Then, its most critical job. It takes that same response, wraps it up as an assistant message, (t: 1020) and appends it to our messages history in the shared store. This is how the chatbot remembers what was just said, ensuring the context is saved for the next turn. (t: 1030) So what have we just seen? A simple prep, exec, post cycle, all interacting with a single dictionary. That's it. That's the fundamental pattern behind every chatbot you've ever used. (t: 1040) By building it from scratch with these basic blocks, you can see there's no magic here. Just a clean, simple, and incredibly powerful design. (t: 1051) So, we've built a chatbot. It's cool, it's conversational. But what if you don't want a conversation? (t: 1060) What if you need clean, predictable, machine-readable data? This is one of the biggest challenges when working with LLMs. You ask it to extract a name and an email from a block of text, (t: 1070) and you hope for something clean like name jane doe, email jane at example dot com. But instead, you get... (t: 1080) a rambling paragraph. Certainly, it's not a good idea. The name mentioned in the document is jane doe, and you can find her email at jane at example dot com. That's great for a human, (t: 1090) but for your program, it's a nightmare. Your code now has to play a guessing game, trying to parse that sentence to find the data it needs. If the LLM changes its phrasing even slightly next time, (t: 1100) your code breaks. This is brittle, unreliable, and frustrating. We need a better way. We need structured output. (t: 1110) And here's another secret. You don't always need complex, model-specific tools or libraries to achieve this. The most powerful and universal method is often the simplest. (t: 1120) Just ask the AI nicely, but very specifically, for the format you want. We're going to use this principle to build a node that can parse a messy, unstructured resume (t: 1130) into clean, structured data. But first, a crucial tip that will save you countless headaches. Tip number one. Speak YAML, not JSON. (t: 1140) You've probably heard of JSON. It's the standard for APIs everywhere. But for getting structured data from an LLM, JSON can be surprisingly fragile. Why? (t: 1150) Because of quotes and newlines. JSON is very strict. If your text contains a double quote, it must be escaped with a backslash. If it contains a newline, (t: 1160) it must be represented as slash n. LLMs are notoriously bad at this. They're trained on human language, not strict programming syntax. They often forget to escape a quote (t: 1170) or mess up the newline character, which makes the entire output invalid. This is where YAML shines. YAML is a data format that's much more human-readable, (t: 1180) and crucially, much more forgiving with strings. Look at this example. To represent a multi-line dialogue in JSON, the AI has to get every single (t: 1190) glashquote, axuot, and, blashclot, n exactly right. It's a minefield. Now look at the YAML version. It's clean. It's natural. (t: 1200) The quotes don't need escaping, and the newlines are preserved automatically using the pipe, lashclot, character. For an LLM that thinks in language, generating this is far more reliable. (t: 1210) So, our first rule of thumb is, when you ask for structured data, ask for it in YAML. Alright, let's put this into practice. We're going to build a resume parser node. (t: 1220) Its job is to take a raw block of text, a resume, and extract key information. The person's name, their email, and a list of their work experience. (t: 1230) Here's the heart of the node, the prompt inside its exec method. This is where we tell the AI exactly what we want. First, we give it the raw resume text. (t: 1240) Then, we give it crystal clear instructions for the output. We explicitly say, output only the requested information in YAML format. (t: 1250) And this is the most important part, we give it a perfect example of what the final YAML should look like. We show it the keys we want, name, email, and experience, (t: 1260) and the structure we expect for each. This is called few-shot prompting, and it's one of the most effective ways to guide an LLM. (t: 1270) When the LLM responds, we don't just trust it blindly. The exec method then tries to parse the YAML string. If the LLM made a mistake, and the YAML is invalid, (t: 1280) the YAML safe load function will raise an error. Remember our node's built-in retry logic? This error would automatically trigger a retry, giving the LLM another chance to get it right. (t: 1290) And finally, if the parsing is successful, the post step takes the clean, structured Python dictionary and saves it to our shared store. (t: 1300) So, what's the result of all this? (t: 1305) We turn unstructured chaos (t: 1310) into clean, predictable data. (t: 1312) On the left is the kind of messy, typo-ridden text you get in the real world. And on the right is the clean, structured dictionary (t: 1320) our node produces. (t: 1322) Now, instead of a messy block of text, our application has clean, predictable data it can reliably use for anything. (t: 1330) Populating a database, displaying a profile, or sending an automated email. (t: 1340) Okay, so our resume parser node is great. It can take one resume and turn it into clean, structured data. But what if you don't have one resume? (t: 1350) What if you have a thousand? Or ten thousand? Are you going to run your flow a thousand times? You could, but that's clumsy. (t: 1360) We need a way to process a whole batch of items within a single, elegant workflow. This is where the batch node comes in. To understand the batch node, let's first quickly revisit our standard node. (t: 1370) Remember its exec method? It's designed to process just one thing at a time. It takes a single pre-processing result and returns a single execution result. (t: 1380) Simple. Now, let's look at the batch node. It's a special type of node, but it thinks about work differently. It's built for bulk operations. (t: 1390) Here's the difference. For a batch node, the prep method doesn't just return one item to work on. It returns an iterable, usually a list of all the items you want to process. (t: 1400) So if you have a thousand items, your prep method will return a list of a thousand resume texts. Then, the exec method's job changes. It's now called once for each item in that list. (t: 1410) It processes one item, finishes, then processes the next and the next all the way down the line. (t: 1420) Finally, the post method is a bit different, too. It waits until all the items in the batch have been processed. Then, it receives a single list of all the items that it wants to process. (t: 1430) It's a bit different from the post method. It receives a single list containing all of the individual results from each exec call. (t: 1437) This is perfect for when you need to aggregate (t: 1440) or save all the results at once. So how is this magic implemented in our 100-line framework? You're going to laugh. Here it is. (t: 1450) The batchnode's underscore exec method is just a single line of code. It's a simple for loop. It takes a list of items from prep, and for each item, it just calls the standard (t: 1460) one-at-a-time underscore exec method. We already know from the parent node class. It collects the results in a list and returns it. That's it. No complex schedulers, (t: 1470) no hidden machinery. Just a for loop. It's beautifully, dumbly, simple. Let's adapt our resume parser to use this. (t: 1480) We'll create a batch resume parser node. The prep method now, instead of loading one resume, loads a whole folder of them and returns a list of their contents. (t: 1490) And the exec method? Here's the best part. It doesn't change at all. The exact same logic that we used to parse one resume (t: 1500) now works on a single item from the batch. The batch node handles the looping for us automatically. And finally, the post method. It receives a list of all the parsed resume dictionaries. (t: 1510) It can then, for example, save all of them to a single JSON file or database. And now, we can start to work on the batch. Let's start with the batch. (t: 1520) Let's walk through it. We have a folder with three resumes. Resume1Text, Resume2Text, and Resume3Text. Our batch resume parser node's prep method runs, (t: 1530) reads all three files, and returns a list containing three long strings of text. Then, the underscore exec loop kicks in. (t: 1540) First, it calls exec with the content of Resume1Text. And then, it calls the file with the content of Resume1Text. And then, it calls the file with the content of Resume2Text. (t: 1550) It calls the LLM, gets structured data back, then it calls exec with Resume2Text. Another LLM call. (t: 1560) Finally, it calls exec with Resume3Text. A third LLM call. Once all three are done, the post method receives a list containing the three structured resume dictionaries ready to be saved. (t: 1570) It works perfectly. It's organized. But you might be thinking, (t: 1580) wait a minute. This is still processing them one by one. It's a sequential for loop. How is this any faster? And you are absolutely right. (t: 1590) It isn't. The batch node is fantastic for organizing your code and handling bulk data cleanly, but it doesn't give you a speed boost. Each LLM call still happens sequentially. (t: 1600) We're still waiting for the first resume to finish before we even start on the second. So, how do we fix that? How do we go from processing one by one, (t: 1610) to processing them all at once in parallel? That is where we introduce Async and the real star of the show. (t: 1620) We need to understand the true villain here. And it isn't the work itself. It's the waiting. (t: 1630) When you make an API call to an LLM, your program just sits there, twiddling its thumbs, waiting for the answer. (t: 1640) For a slow operation, like an LLM call, that could be 5, 10, even 20 seconds of pure wasted time per resume. (t: 1651) So how do we stop waiting? The answer is to think like a smart chef. (t: 1660) A slow chef makes eggs, waits, serves them, and then starts the toast. A smart chef starts the eggs, (t: 1670) and while they cook, starts the toast. They use the waiting time for one task to make progress on another. (t: 1680) This is the core idea of asynchronous programming. In Python, we have two magic words to become a smart chef. Async and await. (t: 1692) Let's look at a super simple Python example, totally unrelated to LLMs. (t: 1700) Imagine we have two tasks. Making coffee, which takes 3 seconds, and making toast, which takes 2 seconds. (t: 1710) We mark both functions with asyncdef. Inside each, instead of just waiting, we await a sleep command. (t: 1720) This await is the key. It tells Python, I'm pausing this task. Feel free to go work on something else. In our main function, (t: 1730) we use asyncio.gather to tell Python to run both of these smart chef tasks concurrently. What happens when we run this? (t: 1740) Python starts making coffee, hits the 3 second await, and pauses the coffee task. It immediately switches to making toast, (t: 1750) hits the 2 second await, and pauses the toast task. After 2 seconds, the toast is ready. After 3 total seconds, (t: 1760) the coffee is ready. The total time taken isn't 3 plus 2, which is 5 seconds. The total time is just 3 seconds, (t: 1770) the time of the longest task. (t: 1774) We did the toast work during the coffee's waiting time. (t: 1780) That's the principle. Now let's apply it to our LLM calls. Here is our original synchronous call LLM function. (t: 1790) It works, but it blocks. It waits. To make it asynchronous, we just need to make 2 small changes. (t: 1800) First, we use an async-compatible library like async-openai. Second, we use our magic words. We label the function with asyncdef, (t: 1810) and we await the API call itself. That's it. This function now behaves like our Smart Chef. When it's called, it will send the request to the LLM, (t: 1820) and then immediately yield control, allowing our program to do other things. So how do we use this new speedy function to process our batch of resumes? (t: 1830) This is where Pocket Claw's elegance shines. We take the batch resume parser node we already built and we just make a few tiny upgrades. First, we change its parent class from batch node to acnode. (t: 1840) We then change the batch resume parser node to batch. We then change the batch node to async-parallel-batch-node. Next, we add the async keyword to our prep, exec, and post methods, (t: 1850) renaming them to prepasync, execasync, and postasync. And finally, the most important change. Inside our execasync method, (t: 1860) we swap out our old calllm with our new speedy calllm async, making sure to await it. And that's it. Seriously. (t: 1870) We've changed maybe 5 lines of code. Is it really that simple? Yes! Just by changing the parent class and making our functions async, everything is now parallel. (t: 1880) Want to know how the framework does this? It's almost unbelievable. Let's look at the source code. This was the underscore exec method for our old batch node. (t: 1890) As we saw, it's just a simple sequential for loop. Now, here is the underscore exec method for our new async-parallel-batch node. It looks almost the same, but instead of a for loop, (t: 1900) it wraps all the tasks in a list and passes them to asyncio.gather. It's a direct swap. A simple loop becomes a concurrent gather. (t: 1910) That's the entire implementation. One line of code is the difference between a slow sequential process and a massively parallel one. It's not hidden behind layers of abstraction. (t: 1920) It's right there. And it's stupidly simple. So, what's the payoff? Let's go back to our example. 10 resumes, 10 seconds each. The old batch node with its for loop took 100 seconds. (t: 1930) Our new async-parallel-batch node using gather fires off all 10 requests at once. The total time becomes the time of the longest single task. (t: 1940) It drops from 100 seconds to just over 10. It's a monumental speedup. Now, a quick reality check. Remember API rate limits? (t: 1950) Don't hit an API with a thousand requests at once unless you've checked their documentation. And this pattern works best for independent tasks. But for bulk processing jobs like this, (t: 1960) this simple upgrade from a for loop to a gather, managed cleanly by the async-parallel-batch node, is a total game changer. It takes your application from a crawl to a sprint. (t: 1970) So far, we've seen a single node do a task. We've seen a chatbot loop on itself. But what happens when the real world gets complex? (t: 1980) Have you ever gone to an LLM with a big ambitious idea? You give it this massive prompt. Write me a comprehensive, well-structured (t: 1990) and polished blog post about the importance of AI safety. And what do you get back? A rambling wall of text. The structure is OK. (t: 2000) The content is generic. The tone is all over the place. It's a C-plus effort, at best. Why? Because we're asking one worker (t: 2010) to be a genius outliner, a creative drafter, and a meticulous proofreader all at the same time. The LLM gets overwhelmed. The solution isn't a better prompt. (t: 2020) It's a better system. It's task decomposition. We break the big task down into a series of smaller, focused steps. We build a workflow. (t: 2030) We're going to build an article-writing workflow. Think of it as a three-station assembly line for ideas. Station 1. The outliner. (t: 2040) Its only job is to take the topic and create a blueprint, a structured list of section titles. Station 2. The batch drafter. This is where it gets tricky. This is where it gets interesting. (t: 2050) It receives that list of sections and drafts all of them simultaneously. Station 3. The combiner. This final worker takes all the drafted sections (t: 2060) and assembles them into one polished, coherent article. This workflow combines chaining and parallel processing. (t: 2070) It's a focus chain, where the middle step is a parallel explosion of creativity. Let's build it. Node by node. First up, the generate outline node. (t: 2080) Its job? Grab the topic. Its prompt? Laser focused. List the section titles. Its final move? Split the LLM's response into a clean list of titles for the next step. (t: 2090) Next, the batch draft sections node. And this is where the real power kicks in. This node is built for parallel work. The framework takes that list of titles (t: 2100) and calls the LLM for every single one. In parallel. Each call is totally focused on writing just one section. Finally, the combine and refine node. (t: 2110) It gathers all the drafted sections from the previous step, joins them together, and asks the LLM one last time to polish everything into a cohesive, final article. (t: 2120) We wire them up in a simple, powerful chain. From the outliner, to the batch drafter, to the final combiner. We kick things off with just the topic in our shared memory. (t: 2130) Topic AI safety. Then we run the flow. (t: 2134) Let's follow the data. Watch how our shared memory evolves as the flow runs. (t: 2140) At the start, it's simple. Just the topic. Then, the generate outline node runs. After step one, the blueprint appears. Our memory now holds a list of section titles. (t: 2150) The flow moves to the batch draft sections node. The magic happens. It takes that list and drafts all three sections at once. After step two, (t: 2160) the memory is filled with all the drafted content. Finally, the combine and refine node assembles everything. At the very end, our shared memory holds the finished masterpiece. (t: 2170) The final, polished article. Look at the result. Instead of that one overloaded prompt, our workflow, combining focus steps and parallel power, (t: 2180) has created something infinitely better. This is the power of a workflow. It's the difference between one worker juggling everything (t: 2190) and a team of specialists, each mastering their part, all working in concert. There's so many savers. So, we've built a linear workflow. (t: 2200) It follows a predictable path from A to B to C. (t: 2210) It's powerful. It's efficient. But, it's rigid. It's a train on a track. (t: 2220) It cannot adapt. It cannot change its mind. The path is fixed. But what if we could smash that path to pieces? (t: 2230) What if our workflow could stop in its tracks, look around and say, You know what? (t: 2240) I need more data. What if it could rewrite its own plan on the fly? What if we could build something that doesn't just follow orders, but actually thinks? (t: 2250) This brings us to the most hyped, most mythologized concept in AI today. (t: 2260) The agent. You hear the term agentic AI everywhere. (t: 2270) It sounds like magic. It sounds like a black box of artificial consciousness. But I'm going to show you the truth. An agent is not a new kind of technology. (t: 2280) It's a design pattern. And in Pocket Flow, an agent is just a flow. (t: 2290) With a loop and a branch. That's it. That's the entire secret. Look at this diagram. This is the blueprint for almost every agent you've ever seen. (t: 2300) There's a central thinking node, the decide node. Its only job is to ask a simple question. Based on my goal and what I know, what is my next move? (t: 2310) The LLM's answer isn't a long paragraph. It's a single command. (t: 2320) An action. Like search, answer, or finish. Let's build the brain of our agent. The decide action node. (t: 2330) Its prep step scans the battlefield, gathering all available intelligence, the original mission objective and any new intel from the field. Its exec step is where the thinking happens. (t: 2340) It crafts a strategic prompt for the LLM, giving it the full context and a crystal clear list of available tools. (t: 2350) Given the mission, what is your command? Search or answer? The LLM's choice is returned in clean tactical yaml. The post step is the commander. (t: 2360) It parses the LLM's decision. If the command is search, it preps the search term for the next specialist. Then, it shouts the order. Search or answer? (t: 2370) And directs the flow down the chosen path. Next, our first tool, the search web node. This node is a specialist. (t: 2380) It's dead simple. It doesn't think, it acts. Its prep step grabs its orders, the search term. Its exec step launches the web search. (t: 2390) Its post step reports back, adding the new intel to our shared memory and returns one simple command. Decide. The flow snaps back to mission control. (t: 2400) Finally, the direct answer node. This is our end game. Its prep step gathers every piece of research we've collected. (t: 2410) Its exec step feeds it all to the LLM with one final command. Synthesize the final answer. And its post step delivers the payload, the answer to the user, and gracefully ends the (t: 2420) mission. We wire it all up like a command center. The decide action node is the hub. With paths branching out to its tools. (t: 2430) The search web node has one path, straight back to the hub. Let's trace a live run. The user asks, who won the 2024 physics Nobel prize? (t: 2440) Round one, the first move. The decide action node awakens. It sees the mission objective, but has zero intel. (t: 2450) It asks the LLM, what is your command? The LLM responds. Action, search. Search term, 2024. (t: 2460) Physics Nobel prize winner. The commander shouts, search. The flow rockets down the search branch. (t: 2470) Round two, intel gathering. The search web node executes its order. It finds an article and adds the summary to our shared intel. (t: 2480) It reports back, decide. The flow loops back to the brain. Round three. The final decision. (t: 2490) The decide action node runs again. This time it sees the new search results. It asks the LLM, new intel acquired. What is your next command? (t: 2500) The LLM sees the mission can be completed. It responds. Action, answer. The commander shouts, answer. (t: 2510) The flow moves to the exit path. Final step, mission complete. The direct answer node takes all the research. And generates the final, well sourced answer. (t: 2520) It delivers the answer. The mission is over. The flow completes. And that's it. We've just demystified the agent. (t: 2530) It's not magic. It's not a sentient being. It's a graph. A simple, elegant, and powerful graph that you now know how to build from scratch. (t: 2540) And there it is. The curtain falls on the magic show. We have a new project. We have forged an entire LLM ecosystem. (t: 2550) From simple logic, to chatbots, to parallel batch processing, to full blown agents. And we built it all on a foundation of just 100 lines of code. (t: 2560) We have torn open the black boxes. There are no mysterious, all powerful classes doing the work for you. There is only the node. (t: 2570) The shared store. And the flow. (t: 2573) So, are you ready to build? (t: 2580) The Pocket Flow GitHub repo has everything we covered today and way, way more. You'll find cookbooks for forging streaming applications. (t: 2590) For giving your chatbots a voice. For teaching them the language of databases. And even full stack web apps. All built on this same simple foundation. (t: 2600) And if you think this is just a toy. Let me show you what this toy can do. This is a nice little toy. It's a nice little toy. Another Pocket Flow project. The codebase knowledge builder. (t: 2610) You give it a GitHub link and it builds a comprehensive tutorial explaining the ENTIRE CODEBASE. It's just a bigger graph. A more complex workflow. (t: 2620) Built with the same simple nodes. And here's the ultimate testimony to the framework's power. This project, which is just an example of what you can build WITH Pocket Flow, became (t: 2630) more popular than the framework itself. It has over 10 000 GitHub stars. The thing we're going to talk about today is that the framework's power is 50% more than the software. So we're going to take a look at it. It's designed to adapt to the big game. The things you build with the tool are more interesting than the tool itself, which is (t: 2640) exactly how it should be. But there is one final secret. (t: 2650) This video. (t: 2660) The script, the structure, the very breakdown of the knowledge you just gained was also generated by a Pocket Flow agent.

