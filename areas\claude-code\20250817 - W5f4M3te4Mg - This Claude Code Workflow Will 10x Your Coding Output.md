---
title: This Claude Code Workflow Will 10x Your Coding Output
artist: <PERSON>
date: 2025-08-17
url: https://www.youtube.com/watch?v=W5f4M3te4Mg
---

(t: 0) Clawcode has been my go-to coding agents for the last couple months, and with the high interest of Clawcode over time, in this video specifically, I'm going to show you tips and tricks on how you can be able to 20x the results of using Clawcode. Now, for this video specifically, it's all about (t: 10) how you can be able to improve your productivity and results with Clawcode, but if you haven't get started with Clawcode or no idea how you can be able to start, you can check out this video (t: 20) that I made called Clawcode from Zero to Pro, the Ultimate 2025 Guide, where I show you how you can be able to set this up and how you can be able to get onboarded with Clawcode completely from (t: 30) scratch. So if you're interested, check that video out, which I'll link it in the description below. But for this video specifically, I'm going to first thing first show you how you can be able to install this and be able to initialize our projects using Clawcode first, and then we're (t: 40) going to take a look at different types of development modes, for example, using like the Kero Spectrum in developments or the planning mode or also the YOLO mode here to perform different (t: 50) types of development tasks. And then we're going to take a look at some tips and tricks on using the core features on how we can be able to create our tasks and also how we can be able to use the hooks feature and also the commands here to better improve our productivity. (t: 60) Then we're also going to take a look at some advanced tools like SuperCloud and also subagents here to create a specialized AI agents here to perform different types of tasks in our development workflows. Then lastly, we're also going to take a look at the session management, how we can be (t: 70) able to manage our contacts, how we can be able to resume different types of conversations and export the conversations to different subagents or different large language models to carry on with (t: 80) our developments. So pretty much that's what we're going to cover in this video. If you're interested, let's get into it. All right. So to get started, first thing first, we're going to install Cloud Code extension onto our coding IDE. (t: 90) So here in our VS Code, I searched for Cloud Code. Here you can see I have Cloud Code extension installed. But once we have this installed, you can click on the Cloud Code icon and we can run (t: 100) Cloud Code inside of our IDE. So inside of our Cloud Code session, we can start to initialize our project whenever we have a brand new project here. So here it's going to initialize a Cloud MD file with the code-based documentation for this application. So let me show you what this (t: 110) application does. So here, if I were to open my terminal, start the application, you can see that currently it starts on 3001. Simply, it's a simple calling application that (t: 120) we can interact with the large language model. So here you can see the AI assistant started asking me some questions and I say, hi there, how's it going? And is able to capture my emotions, (t: 130) send it to a large language model to generate the response. So back to the code here, you can see that if I were to initialize our project, right, so this is what the application does, is able to understand what this project does and list out the do's on how to understand (t: 140) that project. So explore the project structures, the package.json file, and understand what the project does. So let's start with the project. So let's start with the project. So explore the project structures, the package.json file, and understand what the project does. So let's start with the project. So here you can see that the AI assistant started asking me some questions and I say, hi there, how's it going? And is able to capture my emotions, send it to a large language model, list out the problems, and create a comprehensive cloud.md file. So here you can see this is the cloud.md file, (t: 150) and it gives you the project overview and everything you need to know about this project. So things like the development commands and everything for the architecture and so on. (t: 160) So whenever we have conversation with cloud code, is able to reference through this cloud.md file to understand the project structure before it starts to apply or make any changes. So here, (t: 170) once we have our cloud.md file, now it's time for us to talk about how we can be able to have cloud code follow the spec driven development, where it's going to plan things before it starts (t: 180) to execute the task. So here I add additional prompts for the cloud.md file to plan and review every time before it starts to work. So here you can see before it starts to begin the work, (t: 190) it's going to write a detailed implementation plan inside of a .md file. And this plan should be very clear and detailed breakdown of the implementation steps. Now the goal here is that it should give you (t: 200) a reason behind the approach and also a list of tasks and focus on the MVP here, avoid overplanning, and making sure that we do follow the plan and review before it starts to implement. (t: 210) And for implementation here, we always want to make sure to attach a detailed description of the changes that we made for the plan, and also making sure that we keep track of the progress and the steps that is being made so that the other engineers can be able to pick things up (t: 220) based on the plans that we have. So pretty much that's the prompt that we want to add. And it's very similar to the Kero spec driven development where we have the requirements design and also (t: 230) the task lists. So basically once we have this additional prompt added, pretty much we can have a lot of code to follow this approach, to follow this spectrum development here. All right, so once (t: 240) we have the prompt added, then instead of a clock, let's say if we want to build a beautiful UI UX for our current AI calling for an application, help me to break down into key components and put them (t: 250) together in the end. And what I usually do is whenever I try to implement a new feature, for example, like this, I will always try to do the plan mode. So here I can be able to do shift tab. And here, if I were to shift tab again, you can see that here I can be able to switch to the plan (t: 260) mode and it's going to start planning before it starts. Execute the tasks. So here I'm just going to send this request and here, let's take a look (t: 270) at what it does here. All right. So first thing first here, you can see start to read through all the components that we have inside of our current projects. And here you can see eventually is able to use a tool called web research here. And by default for every ClockO session, you can see (t: 280) that these are all the tools that we can be able to use things like the web research, the web fetch to fetch content from a URL, right? So it's able to call those tools to perform some actions. You (t: 292) can also use it to find the trends for the UX and UI design, and also for related UI for the AI (t: 300) conversation interface and is able to find the dribble.com voice calling UI. And here you can see it's fetching that requirements and come up with a enhancement plan for the UI design. (t: 310) So once we look through all the plan here, once everything looks good, then we can be able to start to auto accept this edits and we can be able to start to look at the next step. (t: 320) All right. So now you can see that currently still building, but you can see that it creates a task, the MD file, which currently you can see that it's a build. So now you can see that it creates a task, the MD file, which currently you can see that it's a build. So now you can see that it creates a task, the MD file, which currently you can see that it's a build. So now you can see that it creates a task, the MD file, which currently you can see that it's a build. UI UX enhancement task. And it shows you exactly what are the things that are completed for (t: 330) implementation. So foundational components are completed. The core interface enhancement are completed. And also the advanced features is currently in process. So things like the (t: 340) visualizer, the voice pause components, things like that, which currently you can see it is still in progress, which we will view this whole thing once everything's fully finished by clock code. (t: 350) And of course, if you're tired of having to say yes every time when clock code is doing this, I can we can also be able to run this in a YOLO mode. For example, if I were to do cloud dangerously (t: 360) skip missions, I can be able to run this in YOLO mode. Since we already have the enhancement MD file, we're just going to say continue where we left off for the enhancement plan and please (t: 370) complete it. So now you can see it starts to look through where it left off and it's going to complete those tasks one by one here without having to ask permissions every single time. (t: 380) So now if we were to look at the enhancement doc, you can see that every thing is completed for phase four, phase three, everything. Okay, so once we have everything that (t: 390) is completed, then what we can do is we can start our application again. And let's take a look at what the result looks like. So here I'm just going to do npm run dev. And here you can see this will (t: 400) the result look like for the application, which overall the result looks much more better compared to the last one, which here you can see we have some cool animations, even for the message sent (t: 410) from the assistant. So you can see that we have those animations whenever those messages are sent. Alright, so the next thing that I want to talk about is hooks. So hooks, you can think of it like programmatically run things whenever clocko perform some actions. So here you can see there's (t: 420) a couple of things that we can do. So basically this is structure inside of hooks, which we can to be defined inside of our settings dot local dot JSON file, which is located here. So here you can (t: 430) see I have created a hooks object. So whenever clocko stops, right, maybe it could be finishing responding things, whenever you finish is going to trigger this hook, which here in this hook is (t: 440) going to trigger this command for playing a system sound instead of our local system sound. So this is going to trigger this command for playing a system sound instead of our local system sound. So here you can see that we have our local machine. So here let me give you an example. So if I were to start clocko here, and I'm just going to say hi, you can see that it starts to play a system (t: 450) sound after it's finished responding. But most interesting part about hooks is that the customization here can be endless. So let's say if you want to run a hook scripts for a specific (t: 460) project, for example, so whenever there is a write or edits, when it matched this pattern is going to trigger this hook to for example, check for the styling to make sure that everything's working. (t: 470) In terms of hook events, we have pre tool use, which means before we use a tool, things like these, we can be able to use them. So let's say we have a hook script, and we want to run a hook with this, we can be able to watch for or things like after a tool is being used, we can also be able to trigger that as well. And it can also listen for notifications, user prompts and myths. (t: 480) So whenever users submit a prompt before a clock process, it is going to trigger that event. So you can see that there's so many things that we can watch for or listens to (t: 490) using the hook function from orthopic clock code. Now the next thing that I want to talk about is slash commands. So these commands are built in commands that we can use inside of a clock code. (t: 500) So for example, things like we can be able to clear the conversation histories. We can also use the the cost here to check the token usage and also the init functions that we used earlier in the video to initialize our projects or view the pull request comments and request for pull request (t: 510) reviews things like that we can be able to use inside of our development workflows so we can also be able to create our own commands here so let's say inside of our projects i start a new (t: 520) cloud code session and here inside of our dot cloud folder i create a folder called commands and here inside of this i create a dot md file called uppercase dot md file and inside of this (t: 530) dot md file i will basically instruct it to please respond in uppercase so every time when i (t: 540) you know use this command for example so inside of cloud code let's say if i were to use uppercase command and please respond in uppercase format so here let's say if i were to say hi there for (t: 550) example the response is generated in the uppercase format and here is going to be very useful because whenever we have prompts that we want to reuse we can actually do this by creating a custom (t: 560) commands here to reuse those prompts every time for our development right so maybe it could be like a qa for adding a testing or maybe it could be things like creating a block of code or components we can be able to save those prompts into commands and reuse those commands every single (t: 570) time for development here and what's really cool is that there's also a framework that extends from cloud code with specialized commands and personas for different types of development works during (t: 580) your development process and you can see that if we were to scroll down there are some key features that commands that we can use for different type of tasks for development and you can also switch (t: 590) to different personas for different types of tasks for different types of tasks for different types of specialists when it tackles different types of problems for example if you encounter a architect problem where you're doing the system design part you can switch to the architect personas or if (t: 600) you're doing the front end side of things you can switch to front end or if you're doing backhand you can switch to the back-end personas for different specialists to work on different type of specialized (t: 610) tasks and if you're curious on how to use this then you can check out this video that i made called super cloud where you can be able to upgrade your cloud code workflow with super cloud method so if you're interested check that video out which i'll link it in the description below all right so (t: 620) let's go ahead and start this project so what you can see here is that i have a lot of plugins that are very useful but not being mentioned by a lot of people so one of them is resume function where (t: 630) we can be able to resume a conversation so right now you can see that i start a new conversation with cloud but let's say you want to resume the previous conversation or the past conversation that you had with cloud code you can use this and be able to view all the past conversations (t: 640) you had and let's say if i want to go to the conversation that i had 16 hours ago i can be able to go here and here i can be able to continue with that conversation based on what i had before (t: 650) i can download this program so i'm going to be able to export this part so shoot a call for a my slash we can'd be able to export the current conversation to a file or clipboard and be able to pass the conversation memory to another large language model for example i can be able to do the (t: 660) export here and here i can be able to save this to a file or i can be able to copy this clipboard so i can be able to save this to a file here and here you can see it's going to give you a name (t: 670) and if i were to enter this it's going to create that file and here you can see this is the file that it creates but obviously this process for exporting the conversation and pass it to another (t: 680) coding agent here is going to be very complex and that's why i want to introduce to the sponsor of They have built a central memory layer for modern dev teams using coding agents. Now, since you have been in this situation, you're coding with an AI IDE like cursor or (t: 690) a clock code, and you have spent all your time carefully describing your project context. But the next day when you start a new session, all of the knowledge is gone and you have to (t: 700) waste time explaining everything from scratch. Or maybe you're working with your team, but all the valuable lessons from past interactions, the best practices, the bug fixes are all siloed. (t: 710) They aren't shared across the team, so your colleagues' agents keeps making the same mistake over and over again. And you know that basic rule files like claw.md file just aren't enough for the massive code base. Or maybe you start in cursor, switch over to Gemini CLI or any other agents, (t: 720) and none of that context carry over. But ByteRover here solves all that. What if your AI agents can (t: 730) actually remember all that context permanently? With ByteRover, your project knowledge is saved. Everything from high-level programming concepts to specific business logic, (t: 740) and even more so, the most important part of your project. You can save all that context. You can save all that context. You can save all that context. You can save all that context. You can save all that context. You can save all that context. You can save all that context. Past interactions, bug fixes, even the model's own reasoning steps. This gives your agents maximum context, enabling smarter, more accurate code as your project grows. You can think of it (t: 750) as a unified memory layer shared across all your favorite coding IDEs like cursor, claw code, VS code, and more. So it scales right alongside your code base. (t: 760) For all my fellow open source fans, ByteRover just launched Cypher, an open source memory layer that you can plug directly into your IDE with zero configuration (t: 770) and no code is needed to run your code. It's a complete and easy way to get started. So check out the link in the description to try it out. Alright, so the other one I want to talk about is how we can be able to use the Bash mode. So here, (t: 780) we also have the Bash mode where we can be able to run any commands inside of our terminal. So if we want to run commands inside of our terminal, we have to cancel this, right? But instead of (t: 790) canceling this, we can actually be able to do this like this. For example, if I wanted to do npm run install, I can be able to npm install without having to stop the (t: 800) claw code session using the Bash mode right here, right? So if I do the exclamation mark, you can see that the Bash mode is on. And I can be able to run any commands inside of my terminal using this. And what's really cool about this is that claw code is able to save that memory (t: 810) inside of this conversation. And it was exactly what you did for this project so that it has context on what are the things that are going to do next. Alright, so this one we're going to talk (t: 820) about is the subagents in claw code where you can have or create a specialized subagents in claw code that can be able to perform a specific task in the entire development workflow. So you can do this on your own, but you can also do this on your own. So the first thing that I want to talk about is the subagents in claw code. So here, I'm going to talk about the subagents in the Cloud Code. So if you're (t: 830) going to do this, you're going to have to create a subagent in the Cloud Code. So for example, recently, I have made a video called subagents here, which you can check this out, where simply you can see here that I have created multiple subagents in claw code. And here you can see I have multiple subagents all running in parallel, where you can see here we have our front end (t: 840) agents, back end agent, and also a database agents, all running in parallel to develop a full stack projects using the subagent feature in claw code. And pretty much the way how subagent works is that (t: 850) we first have our user here interacting with claw code. And here, let's say the claw code here give instructions, it's going to look at this task and be able to do that. And then it's going to (t: 860) delegate the work to a subagent here by passing a task to the subagents. And the subagent here will basically using its tools like MCB tools or specific tasks or a specific system prompts that it's going to follow to basically complete a task. And each subagent here has its own context window. (t: 870) So it's not going to use up any main context window from the main claw code session. And the other benefit is that each subagent here is fine tuned with specific domains. And all the subagents (t: 880) here have the options to work across different projects and shared across the team with consistent workflows. And lastly, like I mentioned, each subagent here has a different way of working (t: 890) can access to different tools like MCB servers that it can call to perform any tasks. So pretty much that's how the subagent works in claw code. And if you're interested to learn more about subagents, you can check out this video that I made called the claw code subagents building a (t: 900) full stack applications with your own AI teams. In that video specifically, I show you how you can be able to create your own subagents and be able to delegate different types of tasks to different (t: 910) subagents. All right, so pretty much that's it for this video. So if you do find value in this video, please make sure to like this video, consider subscribe for more content like this. But with that being said, I'll see you in the next video. Bye. (t: 920) I will see you in the next video.

