---
title: This MCP Just Changed Claude Code FOREVER
artist: Income stream surfers
date: 2025-07-29
url: https://www.youtube.com/watch?v=a8s5nOD0TYo
---

(t: 0) Okay guys, I'm making this video because I really really feel that as many people need to know about this as possible. Now the previous way that we were doing AI coding right was we have the code, (t: 10) we have the localhost dev, right, we have the browser, and effectively we also we then have (t: 20) cloud code right which is kind of connected to all of these different things. Now the current problem with this setup is that the link between for example the code right and then so that the code (t: 30) and then the server and then the browser these links have to be guessed by cloud code right (t: 40) so it reads the code and it infers from the code what it believes to be true right but what if the (t: 50) assumption that it makes about the code is actually incorrect in let's say when it processes in the browser right how the does cloud code know that you know (t: 60) this there is this disconnect well it was previously impossible however the new kind of thing is context engineering but I think what I'm going to talk about today is even better than (t: 70) context engineering and it's very very simple as well to be honest with you it could it could very (t: 80) easily fit into the context engineering template right I just think this is even more important which is giving the context engineering template a little bit more context and then the context engineering template is even more important which is giving the context engineering template a little bit more context of the server and the browser to cloud itself but in a different way to what maybe people (t: 90) are used to so obviously most people know that you can run docker right for the server logs if you're (t: 100) not coding inside docker you're making a mistake okay you can also just use visual studio code to run it right but the fact that um cloud code can act access docker and do all the docker commands (t: 110) and restart docker and you don't even have to worry about the docker command you can just run it right but the fact that um cloud code can act access docker and you don't even have to worry about like running npm run dev or running scripts or anything right it will just run itself (t: 120) this part is already pretty secure right generally speaking most people can easily feed the server logs from localhost or docker or you know whatever to cloud code right (t: 130) the problem that most people have is actually in the browser but not just in the browser (t: 140) but like how the browser processes code right so again I'm going to talk about that in a little bit but like how the browser processes code right so again I'm going to talk about that in a little bit I'm not a dev right I don't want people commenting saying um you don't know what you're talking (t: 150) about why you're pretending to be a developer I'm not a dev but like I can understand these concepts and I'm just going to give my interpretation of why this happens right so a browser obviously (t: 160) processes a piece of code in a very specific way like it will go a b c right but if there's a problem with b then there might be a problem with c right so these two sections here could (t: 170) either be a problem with b or a problem with c right so these two sections here could either be processing in a different way or whatever on a browser but the code itself looks solid (t: 180) so when cloud code reads the code it says oh but the code looks right okay well I fixed the problem because the code looks right that's not enough right you need the (t: 190) context of how the browser will process this and not just the look the css right but also the (t: 200) javascript right so I'm going to show you an example right here right now so again it's grove this is just what I'm working on right now so I'm trying to change these this live activity to be a bit more (t: 210) descriptive right the first time I tried this with cloud code it said okay I'm finished and then you know I said well it hasn't finished yet so what I did was I jumped back on (t: 220) to cloud code and now as you can see here what it's actually doing is it's using the playwright mcp now this is super super simple to install okay it's really really (t: 230) easy. But it's not actually taking screenshots. Like it will take screenshots. You can see this (t: 240) is a screenshot, for example. But the main thing that it's doing is it's executing the JavaScript and it's reading what comes out. This is the main difference here. So instead of just making an (t: 250) assumption, oh, the code looks good, what it's actually doing is it's getting the real context from the browser by executing what, you know, executing the page as a user would. So in terms (t: 260) of unit testing, Playwright is just going to be absolutely next level. This right here is the (t: 270) main mistake that people make in coding, right? It's not giving context, but not context in the (t: 280) sense of documentation, context in the sense of your project. So what it just did there, this is a fantastic example of what I'm talking about and why context is so damn important. You can see (t: 290) here I executed an SQL on the production database, right? Which showed that there was no link building stuff. But then I stopped it and I said, no, that's the wrong database. Use the dev (t: 300) database. And you see there are 11 link building activities in the database. So it can confirm that the changes that it's just made should actually properly do what I've asked it to do. (t: 310) And then the final stage of this, hopefully, I believe, once Docker is running, what's it going to do? It's going to process the JavaScript and see if the (t: 320) change that I've asked it to make is actually implemented. This is the key difference. This is why I absolutely love the Playwright MCP, right? So it's going to go to link building here. And (t: 330) then there we go. It says link building opportunity found, whereas previously what it was saying was (t: 340) everything else except link building, right? Now I'll probably change this to be a bit more descriptive because it's just saying link building opportunity found but this context context goes so (t: 350) much further beyond just having like a dot md file with all of the information in it this is a common (t: 360) misconception this is an important part of context right having like um scraped documentation for example of what you're using and then a good md file of what you're building etc etc but then (t: 370) there's the whole other part of this which is like how how does this code contextually render for a user right why is it wrong so i found this system to have 100x my coding abilities that's (t: 390) why i'm going to keep making videos until basically one goes viral enough for me to be satisfied because this shit is next level i don't worry anymore about like database and like um (t: 400) is the code actually correct on the browser like how does it render what is the user experience i don't have to worry about this anymore because it can render its own javascript and understand (t: 410) what's here so it knows that it's fixed the problem this time because it doesn't say (t: 420) basically what i was fixing uh previously if i can't show you anymore because i've already fixed the problem but uh link building was showing non-link building activities right so i changed (t: 430) that very quickly very easily and then basically i said i didn't even have to say you know the the end to end test (t: 440) needs to have this at the end of it it just knows that as long as this is showing link building opportunity found instead of just what it was showing before then it's actually fixed the (t: 450) problem so it's like the context of the problem being fixed instead of the context of like building the project okay guys i'll leave the video there i really hope this was helpful i'm absolutely (t: 460) obsessed with playwrights mcp super base mcp i use daily constantly um and there aren't many others that i use to be honest (t: 470) with you so thank you so much for watching guys if you're watching all the way to the end of the video you're absolutely legend and i will see you very very soon with some more content peace out

