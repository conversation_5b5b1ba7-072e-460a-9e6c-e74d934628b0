---
title: Make Your Claude Code 10x Smarter (My PRD.md Tricks)
artist: Solo Swift Crafter
date: 2025-08-13
url: https://www.youtube.com/watch?v=tLXKKQTvC44
---

(t: 0) So yeah, if you actually want a supercharged cursor or clawed code, you know, like really have your AI tools get your app and not just make stuff up. (t: 10) You need a solid PRD, not some fake corporate doc, and definitely not just for show. You need a living, breathing .md file right in your repo that your AI can actually read. (t: 20) Or maybe prd.md if you want to be fancy. If you're a cursor person, yeah, you can just add your PRD as a rule. (t: 30) And boom, your agent's onboarded. No drama. And, you know, I'm not talking about that move where you just open up Clawed and type (t: 40) like, write me a PRD for my NFC app, hit enter and copy paste whatever comes out. It never works. (t: 50) The only way this actually pays off is if you treat it as a real conversation. Me and Clawed, back and forth. Figuring out what actually matters. (t: 60) What's in, what's out, where the edges are. What simple, lovable, complete really means for this release. It's almost like therapy. Except the AI is your product coach. (t: 70) And yeah, it's actually taking notes. So today, I want to just pull up my real PRD, like right here on screen. This is for my current project, Smart NFC. (t: 80) I'll just walk you through how I built this doc, why I bother, and exactly how I keep it useful for clients. I'm Clawed for Cursor. And honestly, for future me, let's just get into it. (t: 90) Just real quick. I'm Daniel. I've been in the iOS game for almost eight years, freelancing and shipping apps for other (t: 100) people. But since dub dub 25, I've gone full solo dev, started building in public. And yeah, kind of figuring out this personal brand thing as I go. (t: 110) More on that for another video. So here's the trick nobody really tells you, right? If you just... Dump your app idea into Clawed and say, hey, give me a PRD. (t: 120) It's just going to spit out like a generic checklist. And honestly, half the time, it gets the most important stuff completely wrong. (t: 130) Yeah, I've tried it. Doesn't work. So now I start every project exactly the same way. I just open up Clawed and, you know, I just talk to it. (t: 140) I'll literally type, hey, I want to build an NFC tool for iOS that actually feels... Well, modern. (t: 150) I want it to be SLC. Simple, lovable, complete. Not just some clunky MVP. What do you need to know to help me write a real spec? (t: 160) And then... And this is the key. I just let the AI grill me. Like, it'll throw questions at me. What's the actual problem I'm solving? (t: 170) Who are my users, really? Which features actually matter? And what can wait until vNext? And, you know... What is done? Even mean? For me? Not for some imaginary product manager, but for me. (t: 180) So as we go back and forth, I just start dropping those answers right into my PRD.MD. (t: 190) The whole point isn't to build a feature list. It's to capture the way I'm actually thinking about the app. And yeah, let me show you how that plays out in the doc, because honestly, that's where the magic happens. (t: 200) So yeah, here's something I think about way too much. We're basically in this wild AI era now, right? Every weekend, there's a new MVP app popping up. (t: 210) Half the time, it's just someone who pasted their idea into Clod or GPT and had it build the whole thing in like a day. (t: 220) And, you know, honestly, that MVP era, it's kind of done. Shipping something that just barely works, it just doesn't impress anyone anymore. (t: 230) The bar is so much higher now. People actually want apps that feel polished, that feel finished. So that's why... Now I stick this at the top of every PRD I write. (t: 240) This PRD defines an SLC, simple, lovable, complete, v25.0.1, not an MVP. (t: 250) The release has to feel complete, polished and delightful, even if the scope is tight. And just a little Daniel Quirk. And I always start my app version numbers by the year I'm pushing to the app store. (t: 260) So like every release and every update this year is 25.00, 25.1.0 and so on. (t: 270) Honestly, I've been doing that forever, even before the AI tool boom. It just helped me keep my sanity. (t: 280) But now with all these AI agents in the mix, it makes life so much easier. They stop bugging me about what's v1 and just follow whatever I put in the PRD. (t: 290) No back and forth, no weird AI hallucinations, no roadmap drama. So, yeah, for me, the SLC bar and this versioning thing, that's how I set the tone. (t: 300) Not just for me, but for every session I have with Claude or Cursor. (t: 310) I want every release, even the very first one, to feel done. Not almost there. Not yeah, it's a AI generated demo, like actually something I'm proud to put on my (t: 320) home screen and, you know, when Claude or Cursor sees that line at the top, the suggestions I get just match my standards way better. (t: 330) So, yeah, if you want to actually level up and rise above this flood of AI MVPs, set your own bar, put it right at the top of your dock and let your tools come up to your standard. (t: 340) So, yeah, let's get a little more specific here. Whenever I'm filling out a PRD, I always start with a straight up overview. (t: 350) Like, what does this app actually do for SmartNFC? The whole idea is, intuitive, modern NFC tag workflows. (t: 360) You can scan, you can compose records, you can write them all with a clean, swift UI interface. I want that bottom tab bar, the haptics, dark mode, accessibility, (t: 370) the stuff that actually makes it feel like a modern iOS app. And yeah, this isn't just some random checklist for features. (t: 380) This is honestly what I want Claude or, you know, future me. I want to prioritize. I literally type out the problem. (t: 390) iOS NFC tools kind of suck right now. They're clunky, they're dated, and the stuff you actually want is buried like five taps deep. (t: 400) What do users want? They just want something fast, clean, that feeling where it just works. So my motivation is pretty simple. (t: 410) I just want to give indie users, people like me, maybe like you, a workflow that actually feels modern, not just functional, actually like delightful. (t: 420) And then I write out my target users, not some vague for everyone thing. Now, I'm specific iPhone folks who mess (t: 430) with automation, creators, embedding links, power users, building crazy shortcuts, maybe a few educators or hobbyists who just want to play with tags. (t: 440) And, you know, the reason I actually write all this down isn't just so it looks good for the minute I paste this whole chunk into Claude. (t: 450) It just starts making suggestions, flows, edge cases, the weird little gotchas that actually fit my real audience, not just stuff for enterprise users or, (t: 460) I don't know, schools in Germany that happened once, by the way. No idea why. (t: 470) So, yeah, be specific up front, because the more real you get, the better your AI will help. And honestly, the less you have to untangle later. (t: 480) Yeah. So here's where most agents totally go off the rails, right? If you don't spell out your pricing, your feature tiers, your boundaries, (t: 490) they just start inventing stuff out of thin air. Like suddenly you've got subscription screens, five different pro unlocks or some paywall that you never even wanted. (t: 500) It's wild. So now I always lock down my free and pro model right in the PRD, like super clear, free, three scans, three writes per device. (t: 510) That's it. Pro. Fourteen dollars and ninety nine cents one time. And yeah, I even get nitty gritty with the limit tracking logic. (t: 520) Those counters stored in key chain, they don't reset if you reinstall. And the UI always shows you a little pill like, hey, two out of three writes used. (t: 530) So you never get surprised by a paywall. And honestly, I'm a little obsessive about making my out of scope (t: 540) list explicit, like no iCloud sync and V25, no iPad data parity, no CSV export. (t: 550) If it's not shipping, I write it down. And if I ever change my mind, it goes in the dock. And yeah, I update Claude MD. So the AI is never guessing. (t: 560) So why do I get this specific? And because every single time I start a new Claude session, I have to look for apps that look slightly different and that don't match (t: 570) them with everything I said, maybe even convert them into a browser (t: 580) or fire up cursor. I want my agent to know exactly what's shipping and what's just an idea for later. That means less back and forth, fewer (t: 590) hallucinated features and honestly, weeds. I do the opposite. I get really granular with it, like every tab, every main flow, every (t: 600) weird error state. So for the scan tab, it's not just scan NFC. Now it's big scan a tag button, (t: 610) then after you scan, show a tag status card, then a record card. And yeah, dark mode by default, got to have it. Write tab, I literally write out, add record sheet, live byte estimate, (t: 620) capacity check, undo after write, the whole deal. History, it's got to be segmented, all scanned, created, and every row lets you tap in for details, reuse, or duplicate. (t: 630) And then error states. I love writing these out, not just for my own sanity, (t: 640) but so I can literally throw them at Claude and say, hey, here are all my friendly error messages. Can you check if my current flows actually match the PRD? Or like, (t: 650) add capacity validation warnings and retry tips to the write flow. And the thing is, (t: 660) the AI gets it because it's all right there, super explicit. So yeah, the more detailed you get about your UX and your edge cases, the more your AI tools can actually help. And the less you're left (t: 670) cleaning up weird bugs or mismatched screens down the road. Trust me, it's worth the extra lines in (t: 680) your doc. Yeah. So here's where it's going. I'm going to go ahead and show you how to do that. So here's where it gets real. Honestly, my PRD, it's never actually finished. I'm always tweaking it. Like, every time I spot something that feels off, maybe a user hits some weird paywall bug, (t: 690) or I realized the limit tracking isn't quite clear enough. I just go update that section in the doc. (t: 700) And then, you know, I sync that line right over to Claude.md or my cursor rule. So the AI is never stuck in the past. And yeah, it's not just a before launch thing. It's not just a before launch thing. (t: 710) Every single launch, every big change, I open up the PRD, add what's new, and just let the AI see (t: 720) those updates next time I kick off a session. Whenever I add a new feature, it's literally a conversation with Claude. Hey, here's what changed in V25.1. Update the test plan, rewrite the error (t: 730) messages, double check the settings UI. Did I miss anything? And I let the AI see those updates. (t: 740) And then, I go ahead and do the same thing. And then, I go ahead and do the same thing. And then, I go back and fix the factory fault status. And once I fix the problem, does this AI help me? (t: 750) Because the PRD is honestly the only artifact that never lies. If you keep it fresh, everything else (t: 760) in your workflow just works better. So treat your PRD like a living doc, not a checkbox. It saves you from so many headaches down the road. So let's actually get meta for a sec and show how this PRD (t: 770) boilerplate. What I'll do is open up Claude, grab just the right tab UX flow straight out of my PRD and paste it in. Then I just prompt based on this PRD section, generate a Swift UI view for the (t: 780) right screen, gate the pro features, add live byte estimates and show friendly error messages for (t: 790) capacity checks. Use the same UX flow as described. And yeah, Claude comes back with code that actually (t: 800) fits, not just some random copy paste component. It's legit close to what I'd want. Same deal if I need a test plan. I just say from the test plan in the PRD, generate store kit test cases for free (t: 810) pro purchase, refund and offline flows. Or, you know, sometimes I just want to double check my (t: 820) error handling. I'll ask, scan my latest commit. Does the error handling in right tag view match the friendly (t: 830) fixes in the PRD? And honestly, cursor works the exact same way. You just add your PRD as a rule, reference it in your commits, drop it in your prompts. And every agent now actually knows what (t: 840) you care about. It's wild. So yeah, the PRD is seriously the backbone here. It powers every (t: 850) single AI session. It saves me hours. I mean, start your PR on a PRD the next minute and collect them because we have them to help you (t: 860) build intensity practice, every week, make sure that the message is д gum relief, if you're gonna use it on a PRD and actually deal with the messages and ... And yeah, thank you so much for, for all of that skills. (t: 870) Yeah. And yeah, thanks to everyone. I've got a oportun, a great show next week, environmental' are Friday or I have kind of a talk right after this very, very Einsp fuse . (t: 880) Like a few minutes healthy. Yeah. Nice. Thanks man. Okay. Thanks so much for that. Thank you. See ya next time. Great night. PRD horror stories honestly I'd love to hear about them in the comments or if you're wrestling with (t: 890) this whole AI as a teammate thing and you have questions just ask I really do try to answer every single one if this was helpful feel free to like subscribe or share it with another indie dev (t: 900) who's navigating the AI era and wants their workflow to feel a little more intentional (t: 910) so yeah until next time keep crafting keep learning and don't let just ship it energy keep you from building something you're actually proud of peace

