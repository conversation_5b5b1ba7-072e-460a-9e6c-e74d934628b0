---
title: This Week in Claude Code News
artist: <PERSON>
date: 2025-08-23
url: https://www.youtube.com/watch?v=LYRiAJlej9w
---

(t: 0) Okay so I'll be going over the cloud code updates of last week. Firstly you want to make sure you're on the right version so you need to be on 0.88 or greater. So you can write cloud dash dash version and you can see it over here I'm on 0.89 and you can write cloud updates if you're not on the same (t: 10) version as me. I'm not sure what they changed between 0.88 and 0.89 because they haven't added (t: 20) it to changelog but we'll see. The main thing they did in this update is they fixed some error tracking usage tracking in slash cost so if you're using the API then you can write slash cost and (t: 30) then you can see how much cost you've taken up or used so far. I'm using the cloud max plan so I don't have to worry about that but I can see my own cost here using the cc usage status line which (t: 40) if you don't have installed you can download by writing cc usage status line and then finding on github it should be linked down below as well and it should look something like this. Something else (t: 50) you can do is set environment variables for these two over here so if you write slash model for example. Then you should see it says default for up to 50% of usage depending on your plan or opus or sonnet (t: 60) or opus plan mode which will use opus in that particular plan mode. If for some reason you want to switch out the model for an older model or a model from a different provider if you change your base url as well then you can do that over here. So I can go back to cloud opus (t: 70) 4.0 for the opus model and then go back to sonnet 3.7 for example for the sonnet model over here (t: 80) and then I can press enter and then I can run cloud and now I'm using some of the older models. And then I can go back to the old model and then I can run the cloud and now I'm using some of the older models. And you can see that over here on the status line because it now says opus 4 instead of opus 4.1 which I said earlier. I think this is probably more useful if you're using models from a (t: 90) different provider but in that case you would have to change your base url as well. I'm not sure what they add in 8.7 over here because there's a bit of a gap so maybe it's nothing (t: 100) important. The next thing you can do is you can write slash context so you can actually see the context that's being used so far. I have a cloud session that just ran over here and I can write slash context over here and then I should be able to see it's used this amount of messages in the (t: 110) message context, this amount of memory files, this amount of system tools, and the system prompt is about 3,000 tokens and this is how much free space I have left. So that can be useful if (t: 120) you're like debugging context related issues. It can especially be useful in case like you've filled your entire memory or put different MCP tools or something and you can know the effects of that (t: 130) because some people like to do that for some reason. They have like 50,000 tokens of MCP tools for example. But yeah I think this design over here is pretty cool. In 8.5 here they included (t: 140) session cost info in the status line. So I think this is pretty cool. I think this is pretty cool. I usually prefer to use cc usage's status line but if you prefer making your own status line then you can do status line and then you can say include how much I've used this session for (t: 150) example the cost information and then it will make your custom status line based on that. They also fixed some bugs over here. With the at mention they now include the dot cloud folder in (t: 160) your home directory. So if you write dot cloud over here at dot cloud then you can see user slash array dot cloud and the other dot cloud is the one in the project folder. So you can see the (t: 170) data that I added before and so forth. They made it so that rip grep is used by default instead of grep. So rip grep is a faster version of grep which basically means that if you have a really large (t: 180) codebase then you should notice some performance improvements when cloud is searching for the codebase to find relevant things. And you can opt out of that behavior by using this command before (t: 190) running cloud code or putting that in your environment variables. They also made it so that at mention supports files with spaces in the path and I don't think that should matter for a (t: 200) lot of programmers because programmers usually don't have spaces in the path. For other people it may matter more if you're using cloud code for something else. So you can write at and then you can write speech mark over here and then you can write something with a space (t: 210) in the name. So you can write test1 and you can see I have test12 which is a folder and test123 which is a file. Both have spaces in them. They also added a new shimmering spinner and that (t: 220) basically means the star over here you can see it's moving left and right and getting bigger and smaller. I don't know if you remember but the previous star basically only got bigger and (t: 230) smaller rather than moving left and right as well. And that's basically everything that I've done. Also cloud for sony did introduce a 1 million token context window so some people are wondering (t: 240) when it's going to be available in cloud code and it seems like it's rolling out slowly. So for example this user now sees it in their slash model so if they run slash model they can see it says default sony with 1 million token context and they have a video proof of it actually like appearing (t: 250) when they run slash model over here. I still don't see the option but I don't think this will be a (t: 260) big issue for many people. It's still about ensuring your context window does not get polluted even though you can have a lot of people using it. So I'm going to go ahead and run slash model longer chats. If you're working on many different features within the same chat for example then you can have many different distractors in your context window that can reduce the model's (t: 270) performance. And if you are interested in learning more about what I just said then you can watch my previous video about clouds in your context window and that should be linked down below. (t: 280) But if you are really keen to try the new context window then cursor does support it with cloud for sony and you can also use a cursor cli if you want a more cli experience for it. Another thing related to cloud code is that deepseek drops another model deepseek v 3.1 and they say it's (t: 290) better at multi-step aging. So if you're looking for a more advanced model you can go ahead and and they actually made a anthropic api endpoint which means that you can now use deepseek (t: 300) in cloud code and benefit from all the features that cloud code has available but using deepseek. And I go through how to do this in my previous video it should be linked (t: 310) above somewhere right now and also down below in the description. Another relevant thing for cloud code is that salesforce ai research came out with a new paper two days ago introducing mcp universe which is a brand new benchmark to evaluate lms and realistic and hard tasks (t: 320) free interactions with real world mcp. And they said that it's going to be better at coding than using mcp servers and you can scroll down and see the results and read through the paper as well but basically gpt 5 comes out on top across all these different categories and the scores are (t: 330) pretty low because these are pretty hard tasks and of course they don't have coding as a benchmark over here but you can see that cloudforce sonic performs pretty badly when it comes to web (t: 340) searching and i found that to be the case myself when i get cloud code to do any web searching through the web tool that it has available or to use a twively mcp server to find information online (t: 350) then it performs pretty badly compared to when i use gpt 5 in chat gpt. So often what i do is i use chat gpt with thinking mode on and also the high setting and also like web search enabled to find information online or documentation or find an implementation for (t: 360) something and then i pass that information back to cloud code because gpt 5 is just better at web searching when you have the right settings and you can use something like ref tools which (t: 370) provides context it's like context 7 as well and that is an mcp server and it can find good documentation for you but if you're looking at more like nuanced things or things that may not (t: 380) be in the documentation when it comes to like finding solutions to bugs that you're facing then i think using gpt 5 will be a better approach than using the default search capabilities of cloud code a trend that has been happening in the last (t: 390) few weeks is that people have been running cloud code in a vps and having it go crazy and skipping permissions and basically just vibing with it so you can do this yourself in a vps like hudson so (t: 400) if you sign up to hudson you can get a vps for four dollars a month not including tax and you (t: 410) can use my referral code in the description down below to get 20 in cloud credits that should be free so it's basically free if you sign up using that code and basically you can make your own vps over here you can choose a region you can set your ssh key and then once it's set up you can go inside (t: 420) your vps install npm install cloud code and then after ssh into your server installing nodes.js installing npm and then installing cloud code as well you can run this command over here and then (t: 430) press enter and then you can just set up called code again and because they can't really open the (t: 440) link in the browser you have to click on this url and then copy the code and then paste it back in over here and then when it says cloud code is running in bypass permissions mode you can press the yes I accept over here then you can say something like install docker and docker compose (t: 450) and set up a like a server for me with in xjs video calling chat application attached to it and then you can see how it performs on this like bigger task and I think this is more for (t: 460) like playing around and having fun with it and then basically seeing what the capabilities of different models are because you can also swap out the anthropic models with models from other (t: 470) providers and then you can have like two different vps's and you can see how they just key on these longer Horizon tasks which are more difficult as well and you can get a feeling for like the raw behavior of it when you're not having to approve it every single time when it (t: 480) wants to do something there have also been people have been connecting cloud code which is running in a vps or on their local laptop at home to their phones as well so they can use cloud code on the (t: 490) go and then like keep track of anything that's happening I may make a video about that as well and yeah it seems that cloud code is underway and installed docker docker compose it's now (t: 500) setting up a front end as well for the application but yeah that's basically it for the video I do go through the changelog every single week and talk about any new updates and also news surrounding the cloud code community as well so if you do enjoy that kind of stuff and you do want to see (t: 510) more of it then do subscribe for that because I will continue to do this until cloud code is no longer relevant or it's replaced by something better or just anything else like that

