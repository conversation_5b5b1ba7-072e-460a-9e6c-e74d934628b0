---
title: Mastering <PERSON> in 30 minutes
artist: Anthropic
date: 2025-05-22
url: https://www.youtube.com/watch?v=6eBSHbLKuN0
---

(t: 0) Hello. Hey, everyone. I'm <PERSON>. I'm a member of technical staff here at Anthropic, and (t: 20) I created Quad Code. And here to talk to you a little bit about some practical tips and tricks for using Quad Code. It's going to be very practical. I'm not going to go too (t: 30) much into the history or the theory or anything like this. And, yeah, before we start, actually, can we get a quick show of hands who has used Quad Code before? Yeah. All right. That's (t: 40) what we like to see. For everyone that didn't raise your hand, I know you're not supposed to do this while people are talking, but if you can open your laptop and type this... (t: 50) And this will help you install Quad Code, just so you can follow along for the rest (t: 60) of the talk. All you need is Node.js if you have it. It should work. You don't have to (t: 70) follow along, but if you don't have it yet, this is your chance to install it so you can (t: 80) follow along. So what is Quad Code? What is Quad Code? Quad Code is a code that's a source of information that can be used to test what you can do in your documentation. And so what it can do is you can do a lot of things. You can do a lot of things in your documentation. is a new kind of AI assistant. (t: 90) And there's been different generations of AI assistants for coding. Most of them have been about completing a line at a time, completing a few lines of code at a time. (t: 100) Quadcode is not for that. It's fully agentic. So it's meant for building features, for writing entire functions, entire files, fixing entire bugs at the same time. (t: 110) And what's kind of cool about Quadcode is it works with all of your tools. And you don't have to change out your workflow. You don't have to swap everything to start using it. (t: 120) So whatever IDE you use, if you use VS Code, or if you use Xcode, or if you use JetBrains IDEs, there's some people at Anthropic that you can't pry them (t: 130) from their cold, dead hands. But they use Quadcode, because Quadcode works with every single IDE, every terminal out there. It'll work locally over remote SSH, over Tmux. (t: 140) Whatever environment you're in, you can run it. (t: 143) Its general purpose. And this is something where, if you haven't used these kind of free form coding assistants (t: 150) in the past, it can be kind of hard to figure out how to get started. Because you open it up, and you just see a prompt bar. And you might wonder, what do I do with this? What do I type in? (t: 160) It's a power tool. So you can use it for a lot of things. But also, because it can do so much, we don't try to guide you towards a particular workflow. Because really, you should be able to use it (t: 170) however you want as an engineer. (t: 171) As you open up Quadcode, for example, you can see that it's a little bit more complicated. (t: 180) So you can do a lot of things. But it's not just for the first time. There's a few things that we recommend doing to get your environment set up. And these are pretty straightforward. So run terminal setup. This will give you Shift Enter for new lines, (t: 190) so you don't have to do backslashes to enter new lines. It makes it a little bit nicer to use. Do slash theme to set light mode, or dark mode, or daltonized themes. (t: 200) You can do slash install GitHub app. So today, we announced a GitHub app, where you can add, mention, or pull request. So to install it, just run this command in your terminal. (t: 210) You can customize the set of allowed tools that you can use, so you're not prompted for it every time. This is pretty convenient. For stuff that I'm prompted about a bunch, (t: 220) I'll definitely customize it in this way, so I don't have to accept it every time. And something that I actually do is, for a lot of my prompts, I won't hand type them into Quadcode. If you're on Mac OS, you can go into your system settings (t: 230) under Accessibility as Dictation, and you can enable it. And so something I do is you just hit the Dictation key twice, and you can just speak your prompt. (t: 240) And it helps a lot to have specific prompts. So this is actually pretty awesome. You can just talk to Quadcode, like you would another engineer, and you don't have to type a lot of code. (t: 250) So when you're starting out with Quadcode, it's so freeform, and it can do everything. What do you start with? The thing I recommend above everything else (t: 260) is starting with code-based Q&A. So just asking questions to your code base. This is something that we teach new hires at Anthropic. So on the first day in technical onboarding, (t: 270) you learn about Quadcode. You download it. You get it set up. And then you immediately start asking questions about the code base. And in the past, when you were doing technical onboarding, (t: 280) it's something that taxes the team a lot. You have to ask other engineers on the team questions. You have to look around the code, and this takes a while. You have to figure out how to use the tools. This takes a long time. (t: 290) With Quadcode, you can just ask Quadcode, and it'll explore the code base. It'll answer these kind of questions. And so at Anthropic, onboarding used to take about two or three weeks for technical hires. (t: 300) It's now about two or three days. (t: 302) What's also kind of cool about Q&A is we don't do any sort of indexing. So there's no remote database with your code. (t: 310) We don't upload it anywhere. Your code stays local. We do not train generative models on the code. So it's there. You control it. There's no indices or anything like this. (t: 320) And what that means is also there's no setup. So you start Cloud. You download it. You start it. There's no indexing. You don't have to wait. You can just use it right away. . (t: 330) This is a technical talk, so I'm going to show some very specific prompts and very specific code samples that you can use and hopefully improve and up-level your Quadcode experience. So some kind of questions that you can ask is, (t: 340) how is this particular piece of code used, or how do I instantiate this thing? In the Quadcode, it won't just do like a text search and try to answer this. It'll often go a level deeper, and it'll (t: 350) try to find examples of how is this class instantiated? How is it used? And it'll give you a much deeper answer, so something that you would get out of a wiki or documentation instead (t: 360) of just like Command-F. Something that I do a lot also is ask it about Git history. So for example, why does this function have 15 arguments? (t: 370) And why are the arguments named this weird way? And this is something I bet in all of our codebases you have some function like this or some class like this. And Cloud Code can look through Git history. (t: 380) And it'll look to figure out how did these arguments get introduced, and who introduced them, and what was the situation? What are the issues that those commits linked to? And it'll look through all this and summarize it. (t: 390) And you don't have to tell it that in all this detail. You just ask it. So just say, look through Git history. And it'll know to do this. The reason it knows, by the way, is not because we prompted it to. (t: 400) There's nothing in the system prompt about looking through Git history. It knows it because the model is awesome. And if you tell it to use Git, it'll know how to use Git. So we're lucky to be building on such a good model. (t: 410) I often ask about GitHub issues. So It can use WebFetch. And it can fetch issues and look up context on issues too. (t: 420) And this is pretty awesome. And this is something that I do every single Monday in our weekly stand up is I ask, what did I ship this week? And Cloud Code looks at the log. (t: 430) It knows my username. And it'll just give me a nice readout of everything I shipped. And I'll just copy and paste that into a doc somewhere. (t: 440) So yeah, that's tip number one. For people that have not used Cloud Code before, if you're just showing it to someone for the first time, onboarding your team, the thing we definitely recommend is start with code-based Q&A. Don't (t: 450) start by using fancy tools. Don't start by editing code. Just start by asking questions about the code base. And that'll teach people how to prompt. And it'll start teaching them this boundary of, what can Cloud Code do? (t: 460) What is it capable of? Versus, what do you need to hold its hand with a little bit more? What can be one-shotted? What can be two-shotted, three-shotted? What do you need to use interactive mode for in a REPL? (t: 470) Once you're pretty comfortable with Q&A, you can dive into editing code. This is the next thing. And the cool thing about any sort of agentic, (t: 480) like using an LM in an agentic way, is you give it tools. And it's just magical. It figures out how to use the tools. And with Cloud Code, we give it a pretty small set of tools. (t: 490) It's not a lot. And so it has a tool to edit files. It has a tool to run bash commands. It has a tool to search files. And it'll string these together to explore the code, (t: 500) brainstorm, and then finally make edits. And you don't have to prompt it specifically. To use this tool, and this tool, and this tool. You just say, do this thing. And it'll figure out how to do it. It'll string it together in the right way that (t: 510) makes sense for Cloud Code. (t: 512) There's a lot of ways to use this. Something I like to do sometimes is, before having (t: 520) Cloud jump in to write code, I'll ask it to brainstorm a little bit or make a plan. This is something we highly recommend. And something I see sometimes is people, they take Cloud Code, (t: 530) and they ask it, hey, implement this enormous 3,000 lines. And it's a really fun feature. And sometimes it gets this right on the first shot. (t: 540) But sometimes what happens is the thing that it builds is not at all the thing that you wanted. And the easiest way to get the result you want is ask it to think first. So brainstorm ideas. (t: 550) Make a plan. Run it by me. Ask for approval before you write code. And you don't have to use plan mode. You don't have to use any special tools to do this. All you have to do is ask Cloud. And it'll know to do this. (t: 560) So just say, before you write code, make a plan. That's it. (t: 563) Also, I want to think with this one, this commit push PR. This is a really common incantation that I use. There's nothing special about it. (t: 570) But Cloud is kind of smart enough to interpret this. So it'll make a commit. It'll push it to the branch, make a branch, and then make a pull request from your GitHub. You don't have to explain anything. It'll look through the code. It'll look through the history. (t: 580) It'll look through the Git log by itself to figure out the commit format and all the stuff. And it'll make the commit and push it the right way. Again, we're not system prompting it to do this. (t: 590) It just knows how to do this. The model is good. (t: 592) So as you get a little bit more advanced, you're going to want to start to plug in your team's tools. (t: 600) And this is where Cloud Code starts to really shine. And there's generally two kinds of tools. So one is bash tools. And an example of this, I just made up this like barley CLI. This isn't a real thing. (t: 610) But you can say, use this CLI to do something. And you can tell Cloud Code about this. And you can tell it to use, for example, like dash dash help to figure out how to use it. And this is efficient. (t: 620) If you find yourself using it a lot, you can also dump this into your Cloud MD, which we'll talk about in a bit. So Cloud can remember this across sessions. But this is a common pattern we follow at Anthropic. And we see external customers use too. (t: 630) And same thing with MCP. Cloud Code can use bash tools. It can use MCP tools. So just tell it about the tools. And you can add the MCP tool. (t: 640) And you can tell it how to use it. And it'll just start using it. And this is extremely powerful. Because when you start to use code on a new code base, you can just give it all of your tools, (t: 650) all the tools your team already uses for this code base. And Cloud Code can use it on your behalf. (t: 660) There's a few common workflows. And this is the one that I talked about already. So kind of do a little bit of exploration, do a little bit of planning, and ask me for confirmation (t: 670) before you start to write code. These other two on the right are extremely powerful. When Cloud has some way to check its work, so for example, (t: 680) by writing unit tests or screenshotting in Puppeteer or screenshotting the iOS simulator, then it can iterate. And this is incredible. Because if you give it, for example, a mock, (t: 690) and you say, build this web UI, it'll get it pretty good. But if you had to iterate two or three times, often it gets it almost perfect. So the trick is give it some sort of tool (t: 700) that it can use for feedback to check its work. And then based on that, it will iterate by itself. And you're going to get a much better result. So whatever your domain is, if it's unit tests or integration tests or screenshots for apps or web or anything, (t: 710) just give it a way to see its result. And it'll iterate and get better. (t: 720) So these are the next steps. Teach Cloud how to use your tools and figure out the right workflow. If you want Cloud to jump in a code, if you want it to brainstorm a little bit, make a plan, if you want it to iterate, kind of have some sense of that (t: 730) so you know how to prompt Cloud to do what you want. (t: 733) As you go deeper, beyond tools, you want to start to give Cloud more context. (t: 740) And the more context, the smarter the decisions will be. So that's the first step. So that's what we're going to do next. And you're going to have to figure out what the systems will be. Because as an engineer working in a code base, you have a ton of context in your head (t: 750) about your systems and all the history and everything else. So there's different ways to give this to Cloud. And as you give Cloud more context, it'll do better. There's different ways to do this. (t: 760) The simplest one is what we call Cloud MD. And Cloud.md is the special file name. The simplest place to put it is in the project root. (t: 770) So the same directory you start Cloud in, put a Cloud MD start of every session. And essentially, the first user turn will include the CloudMD. You can also have a local CloudMD. (t: 780) And this one, you don't usually check into source control. So CloudMD, you should check into source control, share with your team so that you can write it once and share it with your team. (t: 790) This one, you don't check in. It's just for you. The kinds of things you put in CloudMD, it's like common bash commands, common MCP tools, (t: 800) architectural decisions, important files, anything that you would typically need to know in order to work in this codebase. Try to keep it pretty short, because if it gets too long, it's just going to use up a bunch of context. (t: 810) And it's usually not that useful. So just try to keep it as short as you can. And for example, in our codebase, we have common bash commands. We have a style guide. (t: 820) We have a few core files, things like that. All the other CloudMDs, you can put them in other nested child directories. And Cloud will pull them in on demand. (t: 830) So these are the CloudMDs that will get pulled in automatically. But then also, you can put in put CloudMDs in nested directories. And those will get automatically pulled when Cloud works in those directories. (t: 840) And of course, if you're a company, maybe you want a CloudMD that's shared across all the different codebases, and you want to manage it on behalf of your users, (t: 850) and you can put it in your enterprise route. And that'll get pulled in automatically. (t: 853) There's a ton of ways to pull in context. I actually had a lot of trouble putting this slide together, (t: 860) just to communicate the breadth of ways you can do this. But CloudMD is pulled in automatically. You can also use slash commands. So this is .clod slash commands. And this can be in your home directory, (t: 870) or it can be checked into your project. And this is for slash commands. And over here, we have a few examples of the slash commands (t: 880) that we have in Cloud Code itself. And so for example, if you're in the Cloud Code repo, and you see issues getting labeled, that's actually this workflow running here. It's labeled GitHub issues. (t: 890) And we have a GitHub repo. And this is GitHub action running, the same one we talked about this morning, where Cloud Code will run this command. And it's just a slash command. It'll run, and it'll label the issues so humans don't have to. (t: 900) It just saves us a bunch of time. And of course, you can add mentioned files to pull them into context. And like I said before, QuadMDs in a nested directory (t: 910) get pulled in when Quad works in that directory. (t: 913) So give Cloud more context. (t: 920) And it's definitely worth taking the time to tune context. You can run it through a prompt improver. Consider who the context is for. If you want to pull it in every time, if you want to pull it in on demand, (t: 930) if you want to share it with a team, if it's a personal preference, definitely take the time to tune it. This will improve performance dramatically if you do it right. (t: 940) As you get more advanced, you're going to want to think about this a little bit more, this kind of hierarchy of different ways to pull in everything. So not just QuadMD, but also config, (t: 950) and . . . So projects are specific to your Git repo. And this you can check in, or you can make it just for you. You can also have global configs that (t: 960) are across all your projects. Or you can have enterprise policies. And this is essentially a global config that you roll out for all of your employees, everyone on your team automatically. (t: 970) And this slide is pretty information dense. But the point is, this applies to a lot of stuff. So you can do this for slash commands. You can do it for permissions. So for example, if you have a command that says, I'm going to do this for this. (t: 980) And then you can do it for permissions. So for example, if you have a bash command that you would run for all your employees, like all your employees use this test command, for example. You can actually just check it into this enterprise policies (t: 990) file. And then any employee, when they run this command, it will be auto approved, which is pretty convenient. And you can also use this to block commands. So for example, let's say there's (t: 1000) a URL that should never be fetched. Just add it to this config. And that'll make it so an employee cannot override it. And that URL can never be fetched. So pretty convenient both to unblock people (t: 1010) and also just to keep your code base safe. And then same thing for MCP servers. Have an MCP JSON file. Check it into the code base. That way, any time someone runs quad code in your code base, (t: 1020) they'll be prompted to install the MCP servers and share it with the team. (t: 1025) If you're not sure which of these to use, (t: 1030) this is kind of an insane matrix, because we support a lot of stuff. And engineer workflows are very flexible. And every company is different. So we kind of want to support everything. So if you're not sure how to get started, (t: 1040) I would recommend start with the code base. And then if you're not sure how to get started, I would recommend start with the code base. So if you're not sure how to get started, I would recommend start with the code base. I would recommend start with shared project context. You write this once, and then you share it with everyone on the team. And you get this kind of network effect where someone does a little bit of work, and everyone on the team benefits. (t: 1051) There's a lot of tools built into quad to manage this. So as an example, if you run slash memory, (t: 1060) you can see all the different memory files that are getting pulled in. So maybe I have an enterprise policy. I have my user memory. I have project quad MD. And then maybe there's a nested quad MD that's only pulled in (t: 1070) for a certain time. But I can see that it's being pulled in for certain directories. And then similarly, when you do slash memory, you can edit particular memory files. When you type pound sign to remember something, you can pick which memory you want it to go to. (t: 1081) So yeah, that's the next step. Take the time to configure quad MD, MCP servers, all the stuff (t: 1090) that your team uses so that you can use it once, configure it once, and then share it with everyone. (t: 1096) An example of this is. (t: 1100) In our apps repo for Anthropic, this is the repo that we have all of our web and apps code in. There's a Puppeteer MCP server, and we share this with the team. (t: 1110) And there's an MCP JSON checked in. So any engineer working that repo can use Puppeteer in order to pilot end-to-end tests and to screenshot automatically and iterate so that every engineer doesn't have (t: 1120) to install it themselves. (t: 1121) This is a talk about pro tips. I just want to take a quick interlude to talk about some common key bindings that I've talked about. Some common key bindings that people may not know. (t: 1130) It's very hard to build for terminal. It's also very fun. It feels like rediscovering this new design language. But something about terminals, it's extremely minimal. (t: 1140) And so sometimes it's hard to discover these key bindings. And here's just a quick reference sheet. So any time, you can hit Shift-Tab to accept edits. (t: 1150) And this switches you into auto accept edits mode. So bash commands still need approval, but edits are auto accepted. And you can always ask Quad to undo them later. For example, I'll do this. If I know Quad's on the right track, (t: 1160) or if it's writing unit tests and iterating on tests, I'll usually just switch into auto accept mode. So I don't have to OK every single edit. Any time you want Quad to remember something, (t: 1170) so for example, if it's not using a tool correctly and you want it to use it correctly from then on, just type the pound sign and then tell it what to remember. And it'll remember it. It'll incorporate it into QuadMD automatically. (t: 1180) If you ever want to drop down to bash mode, so just run a bash command, you can hit the exclamation mark and type in your command. That'll run locally. But that also goes in. It goes into the context window. (t: 1190) So Quad will see it on the next turn. And this is pretty good for long running commands if you know exactly what you want to do or any command that you want to get into context. And Quad will see the command and the output. (t: 1200) You can add mention files and folders. Any time you can hit escape to stop what Quad is doing. No matter what Quad is doing, you can always safely hit escape. (t: 1210) It's not going to corrupt the session. It's not going to mess anything up. So maybe Quad is doing a file edit. I'll hit escape. I'll tell it what to do differently. Or maybe it's suggesting that I'm suggesting a 20-line edit. (t: 1220) And I'm like, actually, 19 of these lines look perfect. But one line you should change. I'll hit escape. I'll tell it that. And then I'll tell it to redo that edit. You can hit escape twice to jump back in history. (t: 1230) And then after you're done with the session, you can start Quad with a resume to resume that session, if you want, or dash dash continue. (t: 1240) And then any time if you want to see more output, hit Control-R. And that'll show you the entire output, the same thing that Quad sees in its context window. The next thing I want to talk about is the quad code SDK. (t: 1250) So we talked about this at the top. Right after this, Sid is doing a session, (t: 1260) I think, just across the hallway. And he's going to go super deep on the SDK. If you hadn't played around with this, if you use the dash p flag in Quad, this is what the SDK is. And we've been landing a bunch of features (t: 1270) over the last few weeks to make it even better. So yeah, you can build on top of this. You can do cool stuff. This is exactly the thing that Quad, the code uses. It's exactly the same SDK. (t: 1280) And so for example, something you can do is quad dash p. So this is the CLI SDK. You can pass a prompt. You can pass some allowed tools, which could include (t: 1290) specific bash commands. And you can tell it which format you want. So you might want JSON, or you might want streaming JSON, if you want to process this somehow. (t: 1300) So this is awesome for building on. We use this in CI all the time. We use this for incident response. We use this in all sorts of pipelines. So really convenient. Just think of it as like a Unix utility. (t: 1310) You give it a prompt. It gives you JSON. You can use this in any way. You can pipe into it. You can pipe out of it. (t: 1320) The piping is also pretty cool. So you can use, for example, git status and pipe this in, and use jq to select the result. The combinations are endless. (t: 1330) And it's sort of this new idea. It's like a super intelligent Unix utility. And I think we barely scratched the surface of how to use this. We're just figuring this out. But you can read from a GCP bucket. (t: 1340) Read a giant log and pipe it in and tell Claude to figure out what's interesting about this log. You can fetch data from the Sentry CLI. You can also pipe it in and have Claude do something with it. (t: 1352) The final thing, and this is probably the most advanced use (t: 1360) cases we see, I'm sort of a Claude normie. So I'll have usually one Claude running at a time. And maybe I'll have a few terminal tabs for a few different repos running at a time. When I look at power users in and out of Anthropic, (t: 1370) almost always they're going to have SSH sessions. They'll have tmux tunnels into their Claude sessions. They're going to have a bunch of checkouts of the same repo (t: 1380) so that they can run a bunch of Claudes in parallel in that repo. Or they're using git work trees to have some kind of isolation as they do this. And we're actively working on making this easier to use. (t: 1390) But for now, these are some ideas for how to do more work in parallel with Claude. You can run as many sets of clades as you want. And there's a lot that you can get done in parallel. (t: 1401) So yeah, that's it. I wanted to also leave some time for Q&A. So I think this is the last slide that I have. (t: 1410) And yeah, if folks have questions, there's mics on both sides. And yeah, we'd love to answer any questions. (t: 1420) And then we'll just wrap up by phone call (t: 1430) and ask a few more questions. But for now I'm happy to wrap this up. (t: 1440) Thank you so much for. Can't wait to talk with you later. I will. Have a great evening. (t: 1450) Again, a sending thank you to those guests over here for coming. Have a great evening. Bye bye, everybody. Bye bye. Bye bye. especially tricky is the things that we do to make bash commands safe. (t: 1460) Bash is inherently pretty dangerous and it can change system state in unexpected ways. But at the same time, if you have to manually approve (t: 1470) every single bash command, it's super annoying as an engineer and you can't really be productive because you're just constantly approving every command. And just kind of navigating how to do this safely in a way that scales across the different kinds (t: 1480) of code bases people have, because not everyone runs their code in a Docker container, was pretty tricky. And essentially the thing we landed on is there's some commands that are read only, (t: 1490) there's some static analysis that we do in order to figure out which commands can be combined in safe ways. And then we have this pretty complex tiered permission system so that you can allow list (t: 1500) and block list commands at different levels. (t: 1502) Hi Boris. You mentioned giving an image to Cloud Code, which made me wonder if there's some sort of (t: 1510) multi-modal functionality that I'm not aware of. Is that, are you just pointing it at an image on the file system? Or something? BORIS SMUS- Yeah, so Cloud Code is fully multi-modal. It has been from the start. (t: 1520) It's in a terminal, so it's a little hard to discover. But yeah, you can take an image and just drag and drop it in. That'll work. You can give it a file path. That'll work. (t: 1530) You can copy and paste the image in, and that works too. So I'll use this pretty often for if I have like a mock of something. I'll just drag and drop in the mock. I'll tell it to implement it. (t: 1540) I'll give it up a tier server so it can iterate against it. And yeah, it's just fully automated. Hey, why did you build a CLI tool instead of an IDE? (t: 1550) BORIS SMUS- Yeah, it's a good question. I think there's probably two reasons. One is we started this at Anthropic. (t: 1560) And at Anthropic, people use a broad range of IDEs. And some people use VS Code. Other people use Zed or Xcode or Vim or Emacs. And it was just hard to build something (t: 1570) that works for everyone. And so terminal is just the common denominator. The second thing is, at Anthropic, we see up close how fast (t: 1580) the model is getting better. And so I think there's a good chance that by the end of the year, people aren't using IDEs anymore. And so we want to get ready for this future. And we want to avoid over-investing in UI (t: 1590) and other layers on top, given that the way the models are progressing, it may not be useful work pretty soon. (t: 1600) How much of you, I don't know if this is on. How much of you used Cloud Code for machine learning, modeling, and almost that AutoML experience? (t: 1610) I was curious what the experience has been so far with that. Yeah, I think the question was, how much are we using Cloud Code for machine learning and modeling? (t: 1620) We actually use it for this a bunch. So both engineers and researchers at Anthropic use Cloud Code every day. I think about 80% of people at Anthropic that are technical (t: 1630) use Cloud Code every day. And hopefully, you can see that in the product and the amount of love and dogfooding we've put into it. But this includes researchers who use tools like the notebook tool to edit and run notebooks. (t: 1640) OK, very cool. Thank you. (t: 1645) All right, I think that's it. Thanks. (t: 1660) Thanks. Thanks.

