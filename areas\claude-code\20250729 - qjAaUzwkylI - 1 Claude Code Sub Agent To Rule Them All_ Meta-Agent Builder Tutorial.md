---
title: "1 <PERSON> Code Sub Agent To Rule Them All: Meta-Agent Builder Tutorial"
artist: AI Business Lab
date: 2025-07-29
url: https://www.youtube.com/watch?v=qjAaUzwkylI
---

(t: 0) Hey guys, what's up? It's <PERSON>. Thank you again for joining me this morning and thank you for clicking on this. Now what I'm going to do is talk really quickly about what the big trend is right now and that is agents and sub-agents. And what's happening is people have figured out that (t: 10) <PERSON> can actually spin up these smaller sub-agents that it can kind of handle. So you (t: 20) have a big agent which is <PERSON> and then <PERSON> can spin up sub-agents which are like, instead of being a hammer, they're more like a scalpel. They can be very specific in what they do and how they (t: 30) handle things. And because of that, they don't need to use a ton of context. So I've been using this besides <PERSON> which is also a context, you know, king. Context king. I'll just make that up. You (t: 40) were here for it. You saw it happen. Now <PERSON> is a context king and these other, you know, microagents (t: 50) are also context kings. So I'm going to show you what I found in terms of how to make these happen for the big trend. So I'm going to show you how to make these happen for the big trend. For you. Now <PERSON> knows and will understand what these are, especially if you give it the (t: 60) documentation. I'll put a link below for what the documentation looks like when it's just the blog post that <PERSON>thropic put up about sub-agents. And what people are using now is something I found (t: 70) on another video by a guy named, damn it, I forgot his name. Someone codes. Anyways, (t: 80) really good guy. I'll put a link to the video in the description. As well because I always give my shout outs. But yeah, so what he did was use this prompt as a meta (t: 90) agent, not Facebook meta. We're talking about meta in terms of like all encompassing, you know, (t: 100) big picture meta, right? The meta. Now what you do here is you use this and this big picture meta will help Claude create smaller agents for you which have are very task specific, right? So I'll (t: 110) show you a quick thing that I've done. I've done a lot of work on this. I've done a lot of work on this. I'll show you a quick thing that I created just now. But I'll walk you through some of these. So all (t: 120) you have to do is you got to go in to your Claude. I'll do a quick thing here. So you go into your Claude and I had to do a thing, but all you have to do is click slash agents and you get a whole (t: 130) thing here. And here's all the ones that I've made so far. And then there's here to create a new agent, create a personal one, and you can generate with Claude and you type in whatever you want it to do. (t: 140) Really that simple. So with this new prompt I'm going to show you what it is. So the name meta agent generates a new (t: 150) complete Claude code sub-agent configuration file from the user's description. Use this to create new agents. Use this proactively when the user asks you to create a new sub-agent. Let's color (t: 160) what it does, its purpose, its instructions, and all that. So what I did over here was I create a sub agent (t: 170) where I just wanted it to like find competitors, right? So I'll show you what it made. (t: 180) So I made this one here, which is search engine competitor analysis, okay? (t: 190) And use this agent we need to analyze search engine results to understand which websites, pages, and copy are ranking well for specific search queries. Super valuable, right? (t: 200) And I'll show you a little bit about what it created for me. It gave me a summary over here. I'll share this with you. (t: 210) So it did found market leaders, winning content strategies, key market segments, content gaps to exploit, and SEO opportunities. And that was just with one agent. (t: 220) Now I can make another agent and say, take these, research them, turn them into a blog post, turn them into a website, optimize my website with some of these features. (t: 230) That's what you get to do after this. Anyways, this is just a quick thing. Try it out? Give it a go. Hit the like, hit the subscribe. (t: 240) If you found any value out of this, there's going to be a Google Drive link for free where you can grab this meta agent builder. (t: 250) And yeah, give it a go and try it out. It saves credits, it'll save you time. And it actually helped me with my SaaS like a lot. I was able to get over some hurdles and whatnot, again. (t: 260) Again. So if you have any another students who are using this for, you know, things, again because i did it with serena before and uh did it again it got me through some new hurdles uh so i love it i hope you guys love it and have an excellent day thank you so much for (t: 270) clicking on this link you're an absolute legend if you watch the end i love you guys ciao

