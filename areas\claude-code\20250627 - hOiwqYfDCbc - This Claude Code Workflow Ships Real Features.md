---
title: This Claude Code Workflow Ships Real Features
artist: <PERSON>
date: 2025-06-27
url: https://www.youtube.com/watch?v=hOiwqYfDCbc
---

(t: 0) Today, you and I are going to build a real feature using cloud code instead of all these proof of concepts and fake demos that you see all around YouTube. I'm going to add a new feature to my AI engineering tutor app. And this feature is going to be about the conversation history. Because (t: 10) if you type a question right now, like what is AI engineering? It's saved in a database for 30 days for abuse purposes, but otherwise it's automatically deleted. But some people like to actually have a (t: 20) chat history like you have in chat GPT here on the left. But right now I have not implemented this feature properly. So let's go ahead and see how we can implement it together with cloud code. (t: 30) So this is the repository and we've got the front end and backend application of the repository running already. I'm going to go and open up a new terminal here, because then we're going to start (t: 40) our cloud code session. And I can do that by just typing cloud. There we go. Now this is actually a completely new cloud session. I don't have a cloud markdown file or any special instructions. (t: 50) What I suggest you to do always with the iCoding is to not just jump into the ask of creating this conversation history feature, but first letting the AI agent actually (t: 60) get a better sense of how your repository works. So what I'm going to do is I'm going to ask it to create a cloud MD file to describe the structure of the front end and backend of this project, (t: 70) specifically detailing how conversations are not stored at the moment. The end goal, (t: 80) is going to be that we will implement a new feature to show and save conversations permanently. (t: 100) But for now, you learning the repository is the most important focus. And by doing this, (t: 110) you're really treating cloud as, for example, a junior engineer, because a junior engineer, when they join a new team, really just needs to learn how to do it. And so what I'm going to do is do a follow up. When they join them, at any time I think they have to tag in the (t: 140) required. In a moment, I'm actually going toisko gj. In another time I'm going to ask you, one thing, if you're going to rotate this, ingesting my YouTube videos so that the AI system that this application uses is actually using all (t: 150) of my content to answer questions properly. What you can see now is that cloud is really investigating all these different files in order to find all the references to how conversations (t: 160) are actually saved at the moment. And you can see that it's starting to create that cloud.markdown file with the detailed project structure documentation. Oh, so it actually happened to be (t: 170) done right there. And you can see that it described the architecture with our fast API backend, the key components, authentication, API endpoints. This is important because you can indeed see that (t: 180) we already have an endpoint to retrieve a conversation document. So we have sort of a (t: 190) partial implementation already, and it knows how the frontend is built with React plus Vite, which is very good. And then it's got a nice overview of all of these different services that go along with it, as well as the data structure for the conversation history, (t: 200) it's very impressive that it was able to capture all of this in just one minute of time. So that's very good. And you can indeed see what the conversation flow looks like in this application (t: 210) again. So that is very cool. Now in a terminal, it asks me, do you want to make this edit to cloud markdown? Yes. And I think we're just going to say yes, and don't ask again. So we can make those (t: 220) changes and not have to ask every single time. All right, so it's updating its seduce, and it should tell me that it's ready for new inputs. Yep, there we go. So what I wanted to do now is (t: 230) create a new requirements pack for this new conversation history feature. And I'm going to kind of describe what I described to you to cloud so that it's aware of how it's currently being (t: 240) implemented, and how we want the final implementation to look like. Create a product requirements markdown for the new conversation history feature. Right now, convos are saved (t: 260) for 30 days for abuse detection. But not visible in the user's UI. By toggling the existing but disabled history button, users (t: 270) should be able to save their convos permanently and retrieve them like in other AI UIs. Note that (t: 290) we need to be justifying the user's UI. So we're going to create a new configuration for this new scraper, and we've got that draft one just living DOMTfend. It's actually one of the ones I'm reviewing so far, but even her Sherlock test was fine. And it's also held maintained with (t: 300) anoc Snapdragon Neo out. So I can see this cross-example should be institutions that will only work with real users, since it's already been, but other supports, the less public sources, socioeconomic assets that could work with aおいce, so we could definitely be getting an archive of neues from new компonents that has been installed in her computer. And then we can create a new font 너amani nir. which has the same image, but has been changed to a yellow inkاء with a turquoise to give it a darker green from the human (t: 310) Chelsea. So if, for example, you can see there is a new character in theищes, there will Layne agree with for now work out the requirements doc and don't code yet so here too i'm not asking you (t: 320) to code yet so even though this tool is called cloud code i'm not asking you to code yet i first wanted to create a proper requirements document so that from there it can actually keep track of (t: 330) what it has it has not implemented and even if we quit the session and come back tomorrow we can actually just continue from where we left off so that's why you want to have those product (t: 340) requirement documents so i'm going to go ahead and make some tea and then i'll be right back with you when it's done so actually claude is still thinking about this but i really wanted to share some (t: 350) progress already with you because some of what i'm seeing here is super impressive and shows you why so many people love cloud code so first of all if we have a look at what it's already doing it's (t: 360) researching the entire conversation history ui components which already exist together with how the conversations are being stored but what i really like is that regarding this gdpr compliance requirement it actually does a web (t: 370) search to figure out if there are some existing examples for this now things like gdpr and being compliant with other things like iso are really aspects that you need to verify as a human you (t: 380) can't rely on ai tools to do everything for you but by doing a web search here it's going to at least get 90 of the way there and i think that is already really great and saves me a lot of headache (t: 390) so i will have to verify it but it's a really great start oh and look at that we actually have a very nice product requirements document and in this product requirements document (t: 400) there are a lot of aspects of the solution like the success metrics which i like to see apart from just the functional requirements like how you can see here that one of the goals is to have 30 of (t: 410) active users enable the conversation history feature within 30 days but right now we're really just concerned with making this work from a functional perspective so let's have a look at (t: 420) the problem statement the current state is that conversations are deleted after 30 days that's correct users lose conversation context when starting a new session that's correct you don't have any (t: 430) conversation management in ui all of that is completely correct and it got that from reading all of the code and a little bit of my initial input of course and of course here you can see (t: 440) the user pain points like how there's context loss there's no reference system they cannot go back to previous conversations etc what i really like about this is that everything is segmented very (t: 450) well we have core functionalities like the conversation history toggle the privacy consent flow etc i mean this is exactly what you want to see in a prd and to be quite honest this is a better product (t: 460) requirements document than i see in a lot of professional use cases at the moment so sure ai (t: 470) is not perfect but i'm just telling you that this is better than an average product requirements document is it perfect no but this is really extensive and it goes on for a very long time (t: 480) and it doesn't just talk about the functionality it talks about the success metrics it talks about the potential data structures that it needs to implement and it talks about the functionality it talks about the potential data structures that it needs to implement and it talks about the potential data structures that it needs to implement and it even has a little consent model (t: 490) design here i mean that is very cool right so let's go ahead and actually just implement an mvp of this feature because the consent model is something that i'm going to work on for sure it's just that right now i want to take things step by step and i just want the ability for (t: 500) users to select their previous conversations simply by id it doesn't need to have a fancy name (t: 510) in the left side of the application and then of course the saved conversation should persist for more than 30 days so i'm going to go ahead and implement this feature and i'm going to go ahead and visit the server interface so let's go ahead and ask claw to implement that i want us to first (t: 520) implement an mvp of this prd so that's minimum viable product right that's the smallest unit of (t: 530) successful work that you can create to implement a feature like this and then we're going to say the idea being that the user can select their conversations via a list of conversation (t: 540) ids on the left this of course implies that convos should be persisted in the database over 30 days. (t: 550) Right now, the toggle can just be enabled without the consent form. (t: 560) We will implement this later. And the great part is that you can even ask Claude to then update the PRD (t: 570) and make sure that it ticks off the boxes of the features that it has or has not implemented. And of course, you can also move this PRD to your project management tool of choice (t: 580) like GitHub or monday.com, whatever you're using. You can definitely export these PRDs to a system like that as well. All right, so it has a couple of to-dos it's going to do. (t: 590) It's going to remove the time to live from Conversations container to enable permanent storage. And this is a setting on the Cosmos DB, which is a NoSQL database, (t: 600) where the container will automatically remove data that has not been touched for 30 days. So that is exactly what we're going to do. And that is why we need to remove it, because if you have a conversation stored right now, (t: 610) you want to make sure that it's stored indefinitely unless a user deletes it, right? And then, of course, we need a new API endpoint to list all the conversations. We need to enable the conversation history toggle in the sidebar. (t: 620) We need to create the conversation list UI component, add a conversation selection and switching functionality. And then, of course, we need to test end-to-end conversation persistence (t: 630) and retrieval. So all of those singular steps sound great to me. And you can see that it's actually starting to create some code changes. Now, this is a terminal-based coding app, (t: 640) so you can check out the diffs like so. But what I like to do is just check out what's going on in Visual Studio Code. So I can actually go to my branches here and see that in Cosmos DB PY, (t: 650) it's changing a couple of things. It removes that default TTL. And then if we scroll down here, you can see that, for example, (t: 660) it's creating a new endpoint or rather function to be able to retrieve all of the conversations for a given user. So let's let it cook, and I'll be right back with you once it's done. (t: 670) All right, so what you see now is that it actually has ticked off most of its to-dos, but we do need to test the end-to-end conversation persistence and retrieval. (t: 680) So it seems like it wants to actually run npm run build just to see if there are any TypeScript errors. So I'm actually going to say yes to that. That's totally fine. (t: 690) I don't expect there to be a lot of errors, but you never know. And you can actually see here that the error is not there. There is an error when it's trying to build the full project. So that's totally fine. It's going to go back into the code and try and fix that up. (t: 700) And then I'm expecting that it's going to just run everything again once it's done with fixing this. Now it's going to build again. Yep, there we go. (t: 710) It's going to build again, and hopefully we get no errors now. No, we do still get an error. Response data is possibly undefined. And this is exactly what the actual AI coding process looks like sometimes. (t: 720) It's not going to get it right in one go, but this is running completely in the background. It's not actually interrupting my regular development flow. So I could be doing something else entirely in the background and working basically in parallel to cloud code. (t: 730) So this is totally fine. Sometimes it just needs a couple of iterations to fix stuff. And now you can indeed see, boom, it's actually able to build the entire project. (t: 740) We don't want to build the project in development, but it is good to know that there are no build errors. So now what it's going to do is it's going to test the backend. (t: 750) I think that's a good idea, but you know what? We are here anyway. So let's go and just test it ourselves. I'm going to go ahead and say no. And now let's just go ahead and reload our frontend (t: 760) and check out what actually happened in practice. So if we check out the other terminals, we have the frontend application here and the backend here. So technically, you know, this all hot reloads on changes, (t: 770) but just to make sure that nothing strange has happened, I'm just going to reload both the frontend and the backend just to make sure, right? And then what we're going to do is we're going to go back to my browser (t: 780) and then check if the feature was actually implemented properly. So I'm going to go to my browser and then I'm going to go back to the frontend application and it's going to give itself a full refresh. (t: 790) And now you can see that this conversation history button has a toggle. At this point, if I ask a question now, it should be saving that conversation. So I'm going to go ahead and paste the same question here. (t: 800) And then once it's done with answering this, I do expect that it's going to show on the left here in the sidebar. So let's let it ramble on about AI engineering. (t: 810) And then there we go. So what I'm going to do now is I'm going to go back to my browser. I'm going to give this page a full refresh. And then what we're going to see is we are... And then unfortunately, we see that it doesn't work. (t: 820) So what we need to do is we actually need to go back into cloud and report that the feature doesn't work. But we need to explain why it doesn't work. So what do we do in a situation like this where the implemented feature doesn't work? (t: 830) You do not want to go back to cloud and just type, uh-oh, the feature doesn't work. Why don't you just do a little bit of investigation work yourself (t: 840) so you can give the AI model the right context so it can actually fix the feature? What do I mean by that? Well, in this case, we can actually have a look at our network tab in our browser to get a better idea of if the conversation is being stored at all. (t: 850) What I mean by that is that I can ask the question, what is AI engineering again? And you can see that it creates this request for the message response. (t: 860) And that is indeed being streamed in at the moment. Now I'm just curious to see what happens once the message is done generating. So the message finishes, (t: 870) and there's no extra request. So at the very least, we know now that as far as the initial implementation goes, the frontend is not responsible for saving the conversation. (t: 880) I suppose then in the message backend API, the conversation should have been stored to the database, but that is not happening. So now that we know that context, we can actually formulate the problem (t: 890) in much more detail to cloud, which will improve the chance that it can actually fix the feature. So I'm going to go back to Visual Studio Code, and then I'm going to type the following. (t: 900) manually It seems the toggle works quite well, and the convo is not saved if the toggle is off. (t: 910) However, if the toggle is on, the conversation is also not saved. I see the frontend does not make a new call to the backend (t: 920) after a message is done, which makes sense, but then, (t: 930) then I assume the message backend does not properly save the conversation or something else is amiss. (t: 940) So I don't know exactly what's going wrong. I'm not going to investigate that completely because if I did that, I might as well code the feature myself. (t: 950) But I do take the effort to investigate a little bit and to test a little bit further than just seeing that the UI doesn't work. Because if you can give your AI models just a little push towards the right direction, they will figure things out much faster and much more accurately. (t: 960) Just like how a junior engineer would operate. So that's why you should always give these AI models the right context. (t: 970) But the proof is in the pudding. So let's go ahead and let it search for anything that it wants and re-implement the solution and see if then it all works. (t: 980) Looking at our cloud session, it seems to do a couple of things differently now. For example, in the request for the message, it actually seems that we now need to pass a save conversation flag, which is probably going to be just a false or true. (t: 990) And whether it's false or true depends on what the front-end toggle looks like. So if you've enabled a conversation history, save conversation will of course be true. (t: 1000) If you've not enabled the conversation history toggle, then the save conversation value is going to be false. So that makes a lot of sense because based on that flag, the backend will know whether or not the conversation actually needs to be stored for a longer period of time. (t: 1010) So that makes sense to me. All right. What we're going to do now is just test this implementation again. So I'm going to fully refresh the page. (t: 1020) And then once again, we're going to ask the question, what is AI engineering? Now, if you check out that message request again, and I check the headers or actually rather the payload, you can see here that in the payload, save conversation is set to true. (t: 1030) So that's actually a good sign. Now let's let it just generate the entire response. And then we're going to, of course, refresh the page once again. (t: 1040) So I'm going to refresh the page and it still doesn't work. So now. Now I'm actually going to go into the database and see if the conversation itself was stored, because that will give us a good sign and we can pass it onto the AI model so that it understands that the data itself might have been stored, but it's not being retrieved properly. (t: 1050) So in the database, I'm going to go ahead and refresh my conversations. (t: 1060) And you should indeed be able to see all of these conversations that I've recently created. So it does seem like the conversations are being saved, but they're not being retrieved properly. (t: 1070) And I'm actually going to take this and copy the entire object. Because it will give cloud a good example of how the conversation has been stored. (t: 1080) So I'm going to let it know that the conversations seem to save in the DB correctly, but they are not fetched properly. (t: 1090) Here is an example of a stored conversation. (t: 1100) And then it's going to paste all of that text in. There we go. So this is another great. Recommendation. If you are working on a feature where there's some database interactivity, you can just go into the database and check whether or not things have been saved correctly. (t: 1110) And if they have been saved, you can just like cloud. No, technically you could let cloud connect to your development database directly, but it is very convenient sometimes to help it along yourself and just check out the data and make sure that things are, or are not saved. (t: 1120) All right. It seems to be done. Here's hoping you did well. (t: 1130) So we're going to go ahead and check out the website again. And it looks like it actually auto refilled. So let's go ahead and refresh this. So this is great. You can actually see these conversations in here. (t: 1140) That's a really good sign, but we have to test a little bit further than that. Right? Let's go ahead and disable the conversation history. And now what I'm going to do is time the following. I do not want you to save this. (t: 1150) It's not like the AI model is actually going to listen to this specific request, but by typing something else, we can actually recognize whether or not this conversation will be saved. (t: 1160) So let's see if. When we refresh the page. The conversation will show up or not. So I'm going to go ahead and refresh the page and then let's go ahead and enable conversation history. (t: 1170) And it's not in there. Now let's go ahead and do one more test. Please save this chat. And then we should see this show up on the left side here. (t: 1180) Once the message has been done. All right. So there we go. Yep. There we go. Please save this chat. But funnily enough, there is a little bit of a UI improvement that we need to make that I just realized. (t: 1190) So. I was going to say, I'm going to try this again. And then I'm going to try this again. So here's our chat. So we have a little bit of a UI improvement. And then we have a little bit of a chat. And then we have a little bit of an AI response. (t: 1200) So we can see here. There's only four items that can be shown at once and have the scroll. That's actually not very obvious that there even is a scroll bar. So that's something I'm going to pick up in a second iteration, but at least the interactivity here does work. (t: 1210) Now, of course, the AI response here is that it doesn't have the ability to save the chat conversation that functionality would need to be handled by the platform you're using to access me. But that's exactly what we're doing here. (t: 1220) So. Of course, the. AI could change. I could improve the system prompt and just tell it that there is an explicit toggle that the user can enable or disable. So clearly there's still a lot of work to do, and this implementation is also missing the dialogue form, but it's starting to really come together. (t: 1230) Now I have one more surprise for you because you got so far into the video and that is me sharing a secret with you because not that many people actually care about investing in their AI coding skills or getting ahead in their careers. (t: 1240) But you clearly do because you watch this video all the way until the end. (t: 1250) And I just wanted to. Share with you that AI can be such a powerful tool to actually accelerate your career. I grew to senior much faster thanks to AI coding, and you can be part of that too. (t: 1260) If you check out the link in the description below, you can join my AI native engineering community where you can learn way more AI coding techniques, as well as how to build full real production AI systems like the one that I demoed in the video today. (t: 1270) I really hope to see you there because you've got the right potential. Bye bye.

