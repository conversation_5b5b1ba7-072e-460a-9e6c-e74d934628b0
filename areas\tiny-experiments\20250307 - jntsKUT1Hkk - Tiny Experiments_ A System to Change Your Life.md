---
title: Tiny Experiments: A System to Change Your Life
artist: Bullet Journal
date: 2025-03-07
url: https://www.youtube.com/watch?v=jntsKUT1Hkk
---

- [00:00:00](https://www.youtube.com/watch?v=jntsKUT1Hkk&t=0) - You can actually run experiments on the side of doing other things. Your tiny experiments don't necessarily have to completely replace whatever other projects you're working on. <PERSON><PERSON><PERSON><PERSON> is an entrepreneur, the author of Nest Labs, an ex-Google employee, and now the author of a new book, Tiny Experiments. I'm a neuroscientist. I am obsessed with curiosity. At Google, we were encouraged to be curious within that limited playground. In this video, we sit down and talk about how to run experiments, to improve your life instead of setting goals. Up to the moment where my startup failed, I hadn't really considered life as a giant experiment.

- [00:00:30](https://www.youtube.com/watch?v=jntsKUT1Hkk&t=30) - At Google, we were encouraged to be curious within that limited playground. In this video, we sit down and talk about how to run experiments, to improve your life instead of setting goals. Up to the moment where my startup failed, I hadn't really considered life as a giant experiment. That was the moment where something really clicked for me. You're literally taking the uncertainty and you're turning it into something generative and action-driven. Again, these aren't goals. They're ways to transform your life iteratively, one experiment at a time. The way to do this is to fill a very simple template that I call... I am... Anne-Laure, thank you so much for joining me today. Thank you for having me. I'm excited. For those of you people who may not know who you are, what is one thing they should know about you

- [00:01:00](https://www.youtube.com/watch?v=jntsKUT1Hkk&t=60) - The way to do this is to fill a very simple template that I call... I am... Anne-Laure, thank you so much for joining me today. Thank you for having me. I'm excited. For those of you people who may not know who you are, what is one thing they should know about you that'll provide some context here with what we're about to talk about? I am obsessed with curiosity. I'm a neuroscientist. I study neuroscience at the ADHD Research Lab at King's College London, and I write a newsletter every week that is all about systematic curiosity and mindful productivity. I really see my life in two different chapters. There was a first chapter that was very linear where I was very focused on success.

- [00:01:30](https://www.youtube.com/watch?v=jntsKUT1Hkk&t=90) - and I write a newsletter every week that is all about systematic curiosity and mindful productivity. I really see my life in two different chapters. There was a first chapter that was very linear where I was very focused on success. I used to work at Google. I worked on a couple of startups. The traditional definition of success, getting a good job, getting promoted, working on cool projects, making money, all of that. And it's only when my startup failed that I went back to university to study neuroscience and started that second chapter of my life where I'm a lot more experimental and trying new things. What is your first chapter? look like to run a tiny experiment in your life? So if you want to design a tiny experiment, it all starts with observation. And I know for a lot of us, and especially in the productivity

- [00:02:00](https://www.youtube.com/watch?v=jntsKUT1Hkk&t=120) - and started that second chapter of my life where I'm a lot more experimental and trying new things. What is your first chapter? look like to run a tiny experiment in your life? So if you want to design a tiny experiment, it all starts with observation. And I know for a lot of us, and especially in the productivity space, we like doing things. We like building things. We like the action of exploring, discovering. So I know it can create a little bit of resistance to just say, hey, for now, actually, we're just going to observe. And you take little notes, no judgment, just observing. Based on that, you start formulating a hypothesis. So you look at the way things are and you start

- [00:02:30](https://www.youtube.com/watch?v=jntsKUT1Hkk&t=150) - actually, we're just going to observe. And you take little notes, no judgment, just observing. Based on that, you start formulating a hypothesis. So you look at the way things are and you start asking what might be, what might be different. Maybe I would actually enjoy doing this thing differently. Maybe I would enjoy doing more of that work. Just using your instinct to see if something is really piquing your curiosity, you can start designing your first tiny experiment. The way to do this is to fill a very simple template that I call a pact, which I define as a commitment to the goal. And I call it a pact. And I call it a pact. And I call it a pact. I will insert an action or insert a duration. So for example, I will write a weekly newsletter

- [00:03:00](https://www.youtube.com/watch?v=jntsKUT1Hkk&t=180) - The way to do this is to fill a very simple template that I call a pact, which I define as a commitment to the goal. And I call it a pact. And I call it a pact. And I call it a pact. I will insert an action or insert a duration. So for example, I will write a weekly newsletter every week for the next six weeks. I will go on a daily walk every day for the next two weeks and listen to a new podcast. I will read one research paper every Monday for the next five weeks and I will take notes and then share it in a sub stack. You do it. You don't, you withhold judgment. You don't try to analyze it yet. You just conduct the experiment. You do it. You don't, you withhold judgment. You don't try to analyze it yet. You do it. You don't try to analyze it yet. You do it. You wait until you're done and you've gone through all of these repetitions to look back and see how that actually felt. For many people, myself included, when I think about actual experiments,

- [00:03:30](https://www.youtube.com/watch?v=jntsKUT1Hkk&t=210) - will take notes and then share it in a sub stack. You do it. You don't, you withhold judgment. You don't try to analyze it yet. You just conduct the experiment. You do it. You don't, you withhold judgment. You don't try to analyze it yet. You do it. You don't try to analyze it yet. You do it. You wait until you're done and you've gone through all of these repetitions to look back and see how that actually felt. For many people, myself included, when I think about actual experiments, it kind of seems to be the domain of academics and it might make it feel like whatever experiment I'm running, I'm doing wrong. So I'm curious, what are the telltale signs of running a, for lack of a better, good experiment in your life? Number one sign that you're running a good experiment might not be what you would expect. So I'm going to start with the number one sign but it's whether you're excited about it. Are you actually excited to explore this question,

- [00:04:00](https://www.youtube.com/watch?v=jntsKUT1Hkk&t=240) - for lack of a better, good experiment in your life? Number one sign that you're running a good experiment might not be what you would expect. So I'm going to start with the number one sign but it's whether you're excited about it. Are you actually excited to explore this question, to try new things in that area of your life? If not, that's already a good sign that maybe that's not worth exploring. So something you are actually curious about, your level of curiosity is a good factor to look at. The second one is whether it's doable. Can you actually complete the experiment? If you went for something really ambitious and you never finished collecting the data, then that's not a good experiment. That's why it's always better to try to go for something a little bit smaller and shorter if you're in doubt. If you want to experiment with meditation,

- [00:04:30](https://www.youtube.com/watch?v=jntsKUT1Hkk&t=270) - If you went for something really ambitious and you never finished collecting the data, then that's not a good experiment. That's why it's always better to try to go for something a little bit smaller and shorter if you're in doubt. If you want to experiment with meditation, for example, maybe try it for 10 days first. Don't go to a Vipassana retreat straight away. Try to do something a little bit more manageable first. Same for the newsletter. Again, with my first one, I went really extreme. I said 100 days, but since then I have refined my method a lot and I have realized that it's a lot easier to stick to something that is smaller. So don't necessarily go for the 100 articles in 100 days. Just say, what would it look like? Maybe six newsletters

- [00:05:00](https://www.youtube.com/watch?v=jntsKUT1Hkk&t=300) - first one, I went really extreme. I said 100 days, but since then I have refined my method a lot and I have realized that it's a lot easier to stick to something that is smaller. So don't necessarily go for the 100 articles in 100 days. Just say, what would it look like? Maybe six newsletters over the next six weeks. And number three, something that is out of your comfort zone, something where you will learn something new and not something that is optimized for success. If you're picking an experiment because it feels really comfortable and you feel like, if I do this, I know I'll get this result and this is great. This is more of a regular project. It's not really an experiment. An experiment needs to start from a place of uncertainty. You don't already have the answer. You have a hypothesis. You might have an inkling of an answer. You might feel like

- [00:05:30](https://www.youtube.com/watch?v=jntsKUT1Hkk&t=330) - if I do this, I know I'll get this result and this is great. This is more of a regular project. It's not really an experiment. An experiment needs to start from a place of uncertainty. You don't already have the answer. You have a hypothesis. You might have an inkling of an answer. You might feel like maybe I'll enjoy this. Maybe this will help. Maybe this will make me more productive, but I'm not a hundred percent sure. That's why I'm running the experiment. So those would be the three factors I would look at in terms of designing a good experiment. One thing that I love so much about this model that you create is its form factor, for lack of a better, it's in the title, tiny experiments. This idea that even if you have a full-time job or you have busy family life or something like that, you can run these experiments.

- [00:06:00](https://www.youtube.com/watch?v=jntsKUT1Hkk&t=360) - is its form factor, for lack of a better, it's in the title, tiny experiments. This idea that even if you have a full-time job or you have busy family life or something like that, you can run these experiments. I believe that you can actually run experiments on the side of doing other things and that your tiny experiments don't necessarily have to completely replace whatever other projects you're working on. They can be just a way to maybe discover another area of your life where you want to explore, where you want to try new things. And in fact, I know that for some people, they do like to do this. And I think that's a really good idea. I think that's a really good idea. Stick to something fairly stable in one area of their life and experiment a lot in another. So you might say, you know what, actually I have this job and I could do it with my eyes closed.

- [00:06:30](https://www.youtube.com/watch?v=jntsKUT1Hkk&t=390) - you want to explore, where you want to try new things. And in fact, I know that for some people, they do like to do this. And I think that's a really good idea. I think that's a really good idea. Stick to something fairly stable in one area of their life and experiment a lot in another. So you might say, you know what, actually I have this job and I could do it with my eyes closed. I know exactly what I'm doing. I've been working in this role for quite a few years. I'm not going to experiment there. I'm very happy with the way it works, but I'm going to experiment a lot with my health. I want to figure out what works, what doesn't for me. I'm going to experiment with different gym routines. I'm going to try different diets. I'm going to try different foods. I'm going to try different foods. I'm going to experiment with the time at which I go to bed, for example. I'm going to keep track and see how that impacts my creativity and my productivity. One thing that I've always found interesting

- [00:07:00](https://www.youtube.com/watch?v=jntsKUT1Hkk&t=420) - what doesn't for me. I'm going to experiment with different gym routines. I'm going to try different diets. I'm going to try different foods. I'm going to try different foods. I'm going to experiment with the time at which I go to bed, for example. I'm going to keep track and see how that impacts my creativity and my productivity. One thing that I've always found interesting about the scientific method is that most people, when they're conducting experiments, are trying to find ways to prove themselves wrong. And that's not at all the case, really, when we think about personal productivity. A lot of times we try things out and we are so scared of failure. And we're not going to be able to do it. And we're not going to be able to do it. And it's like the worst possible thing to prove ourselves wrong. And I'm kind of curious how this very well-tested approach to developing the best knowledge we have somehow feels so hard when

- [00:07:30](https://www.youtube.com/watch?v=jntsKUT1Hkk&t=450) - when we think about personal productivity. A lot of times we try things out and we are so scared of failure. And we're not going to be able to do it. And we're not going to be able to do it. And it's like the worst possible thing to prove ourselves wrong. And I'm kind of curious how this very well-tested approach to developing the best knowledge we have somehow feels so hard when we bring it into our own lives. Yeah, this is actually a question that's very close to my heart, which is how can we reinvent our relationship to failure? And I do think that having an experimental mindset can help a lot. I have an entire chapter in the book where I talk about my problem with the traditional definition of goals. And these are great if you want to make sure that you achieve a very narrow definition of success.

- [00:08:00](https://www.youtube.com/watch?v=jntsKUT1Hkk&t=480) - And I do think that having an experimental mindset can help a lot. I have an entire chapter in the book where I talk about my problem with the traditional definition of goals. And these are great if you want to make sure that you achieve a very narrow definition of success. So it's a much more experimental approach versus that linear, smart goal type of approach that a lot of us are using in our productivity systems. The only goal a scientist has is to learn something new. If by the end of the experiment, you collect the data, you collect the data, you collect the data, you collect the data, you collect the data, and you learn something new, whether it's something new about the world, about your work, or about yourself, then that's success. And I know for a lot of people, it feels less satisfying

- [00:08:30](https://www.youtube.com/watch?v=jntsKUT1Hkk&t=510) - The only goal a scientist has is to learn something new. If by the end of the experiment, you collect the data, you collect the data, you collect the data, you collect the data, you collect the data, and you learn something new, whether it's something new about the world, about your work, or about yourself, then that's success. And I know for a lot of people, it feels less satisfying because there's less of the sense of an end goal and a destination and a milestone that you get to. But this is how you can ensure that you keep on growing, even though you don't really know where you're going. And because of that, the direction you might take might be very different from what you could have imagined in the first place. And so I think it's really important that you and that's completely okay. I'd love to hear a little bit more about tiny experiments that aren't about getting more done

- [00:09:00](https://www.youtube.com/watch?v=jntsKUT1Hkk&t=540) - you're going. And because of that, the direction you might take might be very different from what you could have imagined in the first place. And so I think it's really important that you and that's completely okay. I'd love to hear a little bit more about tiny experiments that aren't about getting more done or, you know, getting more followers or more money or more ads or something like that, but just a little bit more of a emotional and highly personal application. Yeah, absolutely. You don't have to only run experiments for your work or your productivity. They can actually be very helpful for, I want to say, inner curiosity, this self-curiosity that you have. And I think that's a really good point. And I think that's a really good point. Yeah, absolutely. You don't have to only run experiments for your work or your productivity. You don't have to only run experiments for your work or your productivity. You can do it for you turn your attention inward and you try to figure out what is going on here. What's interesting? What could change? What do I want to try? You could experiment with meditation, with journaling,

- [00:09:30](https://www.youtube.com/watch?v=jntsKUT1Hkk&t=570) - They can actually be very helpful for, I want to say, inner curiosity, this self-curiosity that you have. And I think that's a really good point. And I think that's a really good point. Yeah, absolutely. You don't have to only run experiments for your work or your productivity. You don't have to only run experiments for your work or your productivity. You can do it for you turn your attention inward and you try to figure out what is going on here. What's interesting? What could change? What do I want to try? You could experiment with meditation, with journaling, with sitting with yourself and observing your own thoughts. The other great thing about experiments is that you're not committing to it for the rest of your life, like a habit where you say to your partner, for example, from now on and for the rest of our lives, we have to do this date night every Friday. You could say, let's just do that for a month or two. And see if we like it or not. And then we can tweak it together. Same with your work. You can say, I'm actually going to try this system for a few weeks and I'm going to see if it works for me

- [00:10:00](https://www.youtube.com/watch?v=jntsKUT1Hkk&t=600) - date night every Friday. You could say, let's just do that for a month or two. And see if we like it or not. And then we can tweak it together. Same with your work. You can say, I'm actually going to try this system for a few weeks and I'm going to see if it works for me or not. That's the difference between an experiment and a habit is that with the habit, you already have the answer in terms of whether this is good for you or not. You're already convinced that if you stick to this habit forever, this is going to be good for you. Whereas with an experiment, you feel like maybe that would be good for you, but you don't assume that because it works for other people, it's going to work for you as well. So you're going to test it and you're going to collect your own data. And based on that data,

- [00:10:30](https://www.youtube.com/watch?v=jntsKUT1Hkk&t=630) - you feel like maybe that would be good for you, but you don't assume that because it works for other people, it's going to work for you as well. So you're going to test it and you're going to collect your own data. And based on that data, you're going to decide whether you want to keep going, tweak it or completely abandon it. What time of the experiments are you running in your life right now? If you have any time for any time experiments. Oh, that's funny because the experiment I'm running at the moment is based on the fact that I have almost zero time. So I'm running an experiment where I've committed my practice to walk for 20 minutes every day for the next 20 days, whatever happens. I'm in this promotional phase. I'm recording quite a few podcasts. I'm in

- [00:11:00](https://www.youtube.com/watch?v=jntsKUT1Hkk&t=660) - running at the moment is based on the fact that I have almost zero time. So I'm running an experiment where I've committed my practice to walk for 20 minutes every day for the next 20 days, whatever happens. I'm in this promotional phase. I'm recording quite a few podcasts. I'm in meetings. I have a lot of copy to write and I could spend my entire day in front of my computer and then be so tired that I just lay down on my bed and maybe watch something on Netflix and rinse and repeat. And that's when I decided, okay, I need to find an experiment that's going to help me feel better. And I'm not quite sure if I'm going to be able to do that. I'm going to have to quite sure. Again, I don't have the answer. Is it working or not? I don't know. But out of everything I came up with that felt like the easiest experiment to implement right now with

- [00:11:30](https://www.youtube.com/watch?v=jntsKUT1Hkk&t=690) - help me feel better. And I'm not quite sure if I'm going to be able to do that. I'm going to have to quite sure. Again, I don't have the answer. Is it working or not? I don't know. But out of everything I came up with that felt like the easiest experiment to implement right now with the mental space that I have, which is almost zero and the energy that I have, which is almost zero also. So that's why I designed this experiment in this way, just 20 minutes, which is very, very short and just for the next 20 days, which is almost going to bring me to the date of the book launch. We'll see if that works. I might keep going actually. People may be hearing about this for the first time and they may have like no idea where to even begin constructing a tiny experiment. And given that you're in the, I have zero time for anything

- [00:12:00](https://www.youtube.com/watch?v=jntsKUT1Hkk&t=720) - date of the book launch. We'll see if that works. I might keep going actually. People may be hearing about this for the first time and they may have like no idea where to even begin constructing a tiny experiment. And given that you're in the, I have zero time for anything space in your life. Are there a couple of questions maybe or prompts that would help people kind of even think about creating a tiny experiment? Something that is very fun to do actually, and you can do very quickly is just open your calendar and look at the way you spent your last week. That's it. And that's it. And that's it. And that's it. And that's it. And that's it. And that's it. Open a notebook or take some notes, but look at the meetings and ask yourself whether some meetings gave you energy, whether others drain your energy, what felt good. Looking at your

- [00:12:30](https://www.youtube.com/watch?v=jntsKUT1Hkk&t=750) - can do very quickly is just open your calendar and look at the way you spent your last week. That's it. And that's it. And that's it. And that's it. And that's it. And that's it. And that's it. Open a notebook or take some notes, but look at the meetings and ask yourself whether some meetings gave you energy, whether others drain your energy, what felt good. Looking at your calendar is a way to prompt those thoughts. Ask yourself, is there anything I can change? That's it. Is there one thing I can change? It can be very, very small. And then try it next week. Just say for five days, I'm going to do this thing differently. And that's it. You have your first tiny experiment. To start designing your own tiny experiments, pick up your own copy of tiny experiments. We will put all that information down below as well as different places where you can find Anne-Laure's incredible work. Anne-Laure, thank you so much for writing this book

- [00:13:00](https://www.youtube.com/watch?v=jntsKUT1Hkk&t=780) - first tiny experiment. To start designing your own tiny experiments, pick up your own copy of tiny experiments. We will put all that information down below as well as different places where you can find Anne-Laure's incredible work. Anne-Laure, thank you so much for writing this book and spending time with us. Where can people learn more about you? Thank you so much for having me. You can go to nestlabs.com where you can subscribe to my weekly newsletter, which I send every Thursday. And if you want to get a copy of the book, you can just look up tiny experiments anywhere books are sold. Thank you.

- [00:13:30](https://www.youtube.com/watch?v=jntsKUT1Hkk&t=810) - Thank you.

