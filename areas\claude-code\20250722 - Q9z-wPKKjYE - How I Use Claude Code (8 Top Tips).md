---
title: How I Use Claude Code (8 Top Tips)
artist: Income stream surfers
date: 2025-07-22
url: https://www.youtube.com/watch?v=Q9z-wPKKjYE
---

(t: 0) Okay guys, so I'm going to give you my top tips for Claude Code. This is after about two to three months of heavy, heavy usage. Okay, so you can see just this month, for example, we've used $1,000 worth of tokens. (t: 10) I haven't even been coding that much this month. Last month was much, much more. So I want to give you guys my top tips. (t: 20) By the way, to do this, you do npx cc usage at latest. And these are my top tips. So Claude Code is a terminal based directory specific AI coding tool. (t: 30) This means it works within its current directory. What does that mean exactly? If I exit out of WSL, because it's now available on Windows, let's write clear. You really need to get used to working within the terminal if you want to be able to use (t: 40) Claude Code. So right now, if I write <PERSON>, I am coding inside the income folder, right? Income stream surface, obviously, that's what that folder is. (t: 50) And you can see there are five MCP servers which failed to connect. And there's a lot of problems here, like found invalid settings files, blah, blah, blah. (t: 60) The reason is, is because there's a lot of weird settings files in this folder. So if you want to code with <PERSON> <PERSON>, the first thing you need to do is make a new folder, right? So mkdir is make new directory or whatever. (t: 70) And then you cd inside here, right? And then if I write Claude, you'll see that there are no more issues or anything like that. (t: 80) Everything is working perfectly because it's a fresh coding instance. You need to know that the context is... Is project wide. So if you're coding in this folder and then you change folder, right? (t: 90) If I open this now, let's say I want to open another Claude code. If I do not cd into make a new folder first, right? Cd is change directory. Then I am coding inside a whole different instance, right? (t: 100) So if I write Claude again here, you'll see, like I said before, there are problems with this folder because it just has so many weird settings in it from all the amounts of times (t: 110) I've been experimenting with Claude and Claude desktop. Right? And Cline and MCPs and everything. Understanding the terminal and understanding directories is absolutely essential to working (t: 120) with Claude code. If you, for example, git clone something, right? Let's just git clone this, right? (t: 130) If I don't cd first into this folder, then I'm working in a different environment. Again, and I'll show you this in action. (t: 140) And this is super important to understand. If I write Claude here, you'll see... That if I write slash memory, there should be nothing in the memory because I haven't set anything in the project memory, right? (t: 150) But because I've been coding with Claude code, right? If I instead cd into Shopify AI overload and then write Claude, right? (t: 160) And then I write slash memory, you'll see on GitHub there is a dot Claude file, right? So if I write slash memory, you'll see that my project memory has been brought over. (t: 170) So this is how you get continuity between coding sessions. You actually commit and push your dot Claude folder to your GitHub repo. (t: 180) It follows that repo around. So even if I sent it off to another dev and they pulled this, they would understand what's (t: 190) going on or Claude code would understand what's going on because this folder, this memory has been passed over. So that's one thing that's really, really key to note. (t: 200) Now. Another important tip I have is put behaviors or workflows in memory and put specifics as prompts. This allows you to make universal workflows where you can make the same thing over and (t: 210) over. So let me just talk a little bit about what that looks like. You put inside slash memory inside this file here, you put behavior, right? (t: 220) How you want Claude to behave. Now I have a really good example of this. I've been talking about this the last few videos that I've made. (t: 230) So in this video here. I've talked about it quite a bit right? Bolt plus level or try this MCP stack instead. Basically I talked about how you can make a generator of a Next.js service websites. (t: 240) So it's a use case, right? You could do WordPress, you could do WordPress directory website, you could do a Next.js (t: 250) directory website. You could make a very, very specific prompt and then you put the specifics in the conversation with Claude code. Right? So what that looks like. (t: 260) Okay. video that I made right this prompt here is a very specific workflow that I've created and is available for free in that in that on that video in the description there is a link to a (t: 270) google document which contains this workflow basically this is the behavior that I want it to do and how I want to do it and then in xml tags here there are the specifics of the specific (t: 280) website that I wanted to build so services languages etc so what does that look like (t: 290) so service information service information plumber in new york services just think of them languages english and spanish locations just think of them and then contact details just use (t: 300) placeholders for now and then if you look in the specifics of this behavioral prompt which is inside the memory of claude you'll see that it says use digital ocean etc to launch the website (t: 310) blah blah blah so if I actually go on digital ocean it used github for me I didn't have to do anything right it literally just made the website (t: 320) and then I said okay now push the website to main right or put it on digital ocean it used the digital ocean mcp and it got this website live in under 30 minutes right because it was a very (t: 330) specific workflow which I know worked in a very specific way and I've detailed it in the claude.md file and then I feed the specifics like all I do is feed it this right and it will go away (t: 340) and it will make the entire website I'm not going to do that now but I'll just show you what that (t: 350) looks like so I just send that here and then it will literally create the folder it will create the entire next.js website according to the behavior prompt and then when I say push it to (t: 360) github and put on digital ocean it will also do that for me okay but one thing that's quite annoying is having to press enter constantly right if I just ran that prompt now I would (t: 370) have to sit and press enter the way to get around that is claude dangerously skip permissions I do not recommend doing claude dangerously skip permissions if you're working on something (t: 380) complicated if you're doing a basic programming task or if you're creating a project from nothing use it if you're editing a current live project do not use it instead if you're doing something (t: 390) which requires a large amount of intelligence or thought or you're working on on an important project you need to sit and watch claude code okay it will do things that you do not want it to do (t: 400) it will mess things up it will get things wrong (t: 410) especially if you're doing a basic programming task or if you're creating a project from nothing especially since it started getting less and less intelligent and you really really have to watch over it I do not recommend using claude dangerously skip permissions on already existing projects (t: 420) because it will mess things up it will hard code things right so you have to watch claude code when making complicated third-party api codes as well this is because if something fails once or twice (t: 430) or three times it will try to hard code a test so you will think you've built an entire project when really it's just a series of things that you're doing and you're not doing anything (t: 440) you're not making any sense out of it and you're not making any sense out of it and you're not making any sense out of it of connected console log statements which pass the test so be extremely wary of that fact now number five use opus as much as possible especially with larger project projects it's often not worth having (t: 450) sonnet do a load of coding for you on a large complicated project as it'll just basically make shit up something else that's important to note here is once you start to get to the point where (t: 460) a project looks something like this obviously there's a lot of bloat here that needs to be removed but once you've got a huge project like this you're going to have to make sure that you're (t: 470) can use what's called FileShPO acordo to getakat you're going to getą (t: 490) bibi hosting bar (t: 500) kod have to necessarily understand what's going on. Now I one prompted this yesterday. Let me just give you a quick example of something. Okay. I didn't even know that this was possible until yesterday. All I said to Claude code was obviously I have Docker desktop installed, but I said, (t: 510) set me up a WordPress environment so that I can dev locally. Right. And that's exactly what it did. (t: 520) So what Docker allows you to do is it allows you to run things like WordPress locally on your machine without any local WordPress or local WP or whatever, no other services, just literally (t: 530) just Docker. Also the really cool thing about Docker is digital ocean can use a Docker file to (t: 540) create the project, right? So if you can make something run locally inside Docker, it should, (t: 550) theoretically speaking, run inside digital ocean in exactly the same way, because the, the setup file, the, the, the, the, the, the, the, the, the, the, the, the, the, the, the, the, the, the, the, the, the, the, the, the, the, the, the, the, the, the, the, the, the, the, the, the, the, the, the, the, the, is in Docker and both Docker and digital ocean will use the same set up file, right? So you're (t: 560) not changing files. You don't have to worry about this, that, and the other, and, oh, I was coding (t: 570) on windows. And now this is on Linux. What do I do? Everything just slots nicely together. You push it to GitHub. You have a Docker file. It is pushed to, uh, to digital ocean with the Docker (t: 580) file. And then when you push to GitHub, any changes that you make, it will automatically trigger a rebuild inside Docker, sorry, inside digital ocean. And that entire flow (t: 590) is really, really essential to making like a more complicated project. Now, the other thing is (t: 600) databases. I would recommend using something like my SQL, uh, or use SQL light connected to super base, right? So SQL light, you can use while you're devving, right? As long as you tell (t: 610) Claude code from the beginning, I do eventually want to use super base, or you can just, you can start using super base from the very beginning. The super base MCP works extremely well. (t: 620) It's just, it's nice to have SQL light so that you could have like coding, uh, local code that (t: 630) any AI can read to understand how your database works. If everything sits on super base, that's a little bit more difficult. Now, number seven is think about your tech stack beforehand. If you're (t: 640) not technical, I would recommend either a sticking with something like HTML, CSS, JavaScript, and flask flask is just. A way to have a web server on Python. Uh, you could also use a fast API. You could use PHP. You could use (t: 650) next to yes, there's a million different options, right? But if you, if you're not technical, just stick to something very, very simple or ask an AI to give you a tech stack and then give that to (t: 660) Claude code and struggle through potentially a few issues with that. Now, the final thing is if (t: 670) you're making a complicated project, right? I recommend using context engineering as a base. The reason is, is this. I'm going to take this video here for example, it's a very, very good way to make a very, very (t: 680) complicated project. When I made SEO Grove, I kind of wish I had context engineering from the beginning because I could have given it a very, very good solid base. Whereas, you know, it's (t: 690) working now. SEO Grove is pretty much ready to launch. I'm super, super excited to actually launch this project, but it is a little bit bloated and there are still a couple of issues that we (t: 700) need to iron out just because of, you know, not having the tech stack. So that's, that's, that's the, that's the key. of not having the tech stack fully thought out and not using context engineering from the very (t: 710) beginning. I would not recommend context engineering for something like a service website or a WordPress website. It's overkill. Cloud Code already understands Next.js at a decent level (t: 720) to make a Next.js site or a WordPress site or even HTML, CSS, and JavaScript service website. (t: 730) If you're making just a simple HTML, CSS, and JavaScript website as well, you might not even need context engineering. You only really need context engineering if you're making a very (t: 740) complicated SaaS project or application or whatever it might be. I'll leave the video there, guys. Thank you so much for watching. If you're watching all the way to the end of the video, as usual, you're an absolute legend, and I will see you very, very soon with some more content. Peace out.

